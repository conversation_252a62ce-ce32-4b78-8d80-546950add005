{"version": 3, "file": "src_app_reports_reports_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;AAC4C;AAQd;AAGkD;;;;AAM1E,MAAOK,mBAAoB,SAAQJ,+DAAa;EAapDK,YAAoBC,KAAuC,EAAYC,cAA8B;IACnG,KAAK,EAAE;IADW,KAAAD,KAAK,GAALA,KAAK;IAA8C,KAAAC,cAAc,GAAdA,cAAc;IAVrF,KAAAC,eAAe,GAAY,KAAK;IAQhC,KAAAC,UAAU,GAAsB,IAAIR,mEAAiB,EAAE;EAIvD;EAEAS,QAAQA,CAAA,GAAU;EAElBC,cAAcA,CAAA;IACZ,IAAIC,kBAAkB,GAAG,IAAI,CAACL,cAAc,CAACM,UAAU,EAAE;IACzD,IAAI,CAACJ,UAAU,CAACK,MAAM,GAAGF,kBAAkB,CAACE,MAAM;IAClD,IAAI,CAACL,UAAU,CAACM,KAAK,GAAGH,kBAAkB,CAACG,KAAK;IAChD,IAAI,CAACN,UAAU,CAACO,KAAK,GAAGJ,kBAAkB,CAACI,KAAK;EAClD;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACZ,KAAK,CAClCa,IAAI,CAACpB,mDAAM,CAACI,0FAAoB,CAAC,CAAC,CAClCiB,SAAS,CAAEC,KAAmB,IAAI;MACjC,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClB,IAAI,CAACC,kBAAkB,GAAGF,KAAK,CAACC,QAAQ,CAACE,SAAS;QAClD,IAAI,CAACC,YAAY,GAAGJ,KAAK,CAACC,QAAQ,CAACI,WAAW;QAC9C,IAAI,CAAClB,eAAe,GAAG,IAAI,CAACiB,YAAY,KAAKvB,kEAAgB,CAACc,KAAK;QACnE,IAAI,CAACW,cAAc,GAAGN,KAAK,CAACM,cAAc;;IAE9C,CAAC,CAAC;EACN;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACV,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACW,WAAW,EAAE;;EAE1C;;;uBA3CWzB,mBAAmB,EAAA0B,+DAAA,CAAAE,8CAAA,GAAAF,+DAAA,CAAAI,kEAAA;IAAA;EAAA;;;YAAnB9B,mBAAmB;MAAAgC,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAAC,aAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClB8B;AACc;AACtB;AACJ;AACE;AACkB;AACA;AACE;AACN;AACkB;AACJ;AACJ;AACN;AAC+B;AACzB;AACxB;AACqD;AACA;AACF;AAC3C;;;;;;;;;;;;;;;;;AClB7B;;AAMzB,MAAOE,sBAAsB;EAUjC1C,YAAA;IATA,KAAA2C,uBAAuB,GAAW,CAAC;IACnC,KAAAC,iBAAiB,GAAW,CAAC;IAErB,KAAAC,eAAe,GAAG,IAAIJ,yCAAO,EAAQ;IACtC,KAAAK,kBAAkB,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;IAEvD,KAAAC,iBAAiB,GAAG,IAAIP,yCAAO,EAAU;IAC1C,KAAAQ,uBAAuB,GAAG,IAAI,CAACD,iBAAiB,CAACD,YAAY,EAAE;EAEvD;EAEfG,YAAYA,CAAA;IACV;IACA,IAAI,IAAI,CAACP,uBAAuB,KAAK,IAAI,CAACC,iBAAiB,EAAE;MAC3D,IAAI,CAACC,eAAe,CAACM,IAAI,EAAE;MAC3B,IAAI,CAACR,uBAAuB,GAAG,CAAC;;EAEpC;EAEA;;;EAGAS,2BAA2BA,CAAA;IACzB,IAAI,CAACT,uBAAuB,EAAE;IAC9B,IAAI,CAACO,YAAY,EAAE;EACrB;EAEAG,wBAAwBA,CAACC,YAAqB,EAAEC,YAAwB;IACtE,IAAID,YAAY,EAAE;MAChB,IAAI,CAACV,iBAAiB,IAAIW,YAAY,CAACC,MAAM;;EAEjD;EAEAC,2BAA2BA,CAAA;IACzB,IAAI,CAACT,iBAAiB,CAACG,IAAI,CAAC,IAAI,CAAC;EACnC;EAEAO,gBAAgBA,CAAA;IACd,IAAI,CAACf,uBAAuB,GAAG,CAAC;IAChC,IAAI,CAACC,iBAAiB,GAAG,CAAC;EAC5B;;;uBAzCWF,sBAAsB;IAAA;EAAA;;;aAAtBA,sBAAsB;MAAAiB,OAAA,EAAtBjB,sBAAsB,CAAAkB,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;ACJyC;AAG7D;AAC2F;;;;;;;;;ICL3FpC,4DAAA,aAAkE;IAChEA,uDAAA,qBAA2C;IAC7CA,0DAAA,EAAM;;;IADSA,uDAAA,GAAe;IAAfA,wDAAA,gBAAe;;;;;IAK1BA,uDAAA,aAA2C;;;;;IAC3CA,4DAAA,aAAuC;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,gEAAA,MAAA6C,WAAA,CAAAC,QAAA,MAAwB;;;;;IAI/D9C,4DAAA,aAAsC;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;;;;;IACnDA,4DAAA,aAAuC;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAK;;;;IAA1BA,uDAAA,GAAqB;IAArBA,+DAAA,CAAAgD,WAAA,CAAAC,OAAA,CAAqB;;;;;IAI5DjD,4DAAA,aAAsC;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAK;;;;;IAClDA,4DAAA,aAAuC;IAC+CA,oDAAA,GAElF;IAAAA,0DAAA,EAAI;;;;IAFHA,uDAAA,GAA2D;IAA3DA,oEAAA,0CAAAmD,WAAA,CAAAC,SAAA,KAA2D;IAAsBpD,uDAAA,GAElF;IAFkFA,+DAAA,CAAAmD,WAAA,CAAAE,WAAA,CAElF;;;;;IAKJrD,4DAAA,aAAsC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;;;;;IAChDA,4DAAA,aAAuC;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAK;;;;IAA9BA,uDAAA,GAAyB;IAAzBA,+DAAA,CAAAsD,WAAA,CAAAC,WAAA,CAAyB;;;;;IAGlEvD,uDAAA,aAA4D;;;;;IAC5DA,uDAAA,aAAiE;;;;;IA1BnEA,4DAAA,eAA4D;IAC1DA,qEAAA,MAAsC;IACpCA,wDAAA,IAAA0D,mDAAA,gBAA2C;IAC3C1D,wDAAA,IAAA2D,mDAAA,gBAAoE;IACtE3D,mEAAA,EAAe;IAEfA,qEAAA,MAAqC;IACnCA,wDAAA,IAAA6D,mDAAA,gBAAmD;IACnD7D,wDAAA,IAAA8D,mDAAA,gBAAiE;IACnE9D,mEAAA,EAAe;IAEfA,qEAAA,MAAqC;IACnCA,wDAAA,IAAA+D,mDAAA,gBAAkD;IAClD/D,wDAAA,IAAAgE,mDAAA,gBAIK;IACPhE,mEAAA,EAAe;IAEfA,qEAAA,QAAyC;IACvCA,wDAAA,KAAAiE,oDAAA,gBAAgD;IAChDjE,wDAAA,KAAAkE,oDAAA,gBAAqE;IACvElE,mEAAA,EAAe;IAEfA,wDAAA,KAAAmE,oDAAA,iBAA4D;IAC5DnE,wDAAA,KAAAoE,oDAAA,iBAAiE;IACnEpE,0DAAA,EAAQ;;;;IA3B0BA,wDAAA,eAAAqE,MAAA,CAAAC,UAAA,CAAyB;IAyBrCtE,uDAAA,IAAiC;IAAjCA,wDAAA,oBAAAqE,MAAA,CAAAE,gBAAA,CAAiC;IACpBvE,uDAAA,GAAyB;IAAzBA,wDAAA,qBAAAqE,MAAA,CAAAE,gBAAA,CAAyB;;;ADpB5D,MAAMC,QAAQ,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC;AAO5D,MAAOC,6BAA8B,SAAQvG,+DAAa;EAe9DK,YAAoBmG,eAAgC;IAClD,KAAK,EAAE;IADW,KAAAA,eAAe,GAAfA,eAAe;IARnC,KAAAH,gBAAgB,GAAGC,QAAQ;IAC3B,KAAAF,UAAU,GAAG,IAAIjC,uEAAkB,EAAyB;IAC5D,KAAAsC,OAAO,GAAY,IAAI;EAQvB;EAEA/F,QAAQA,CAAA;IACN,IAAI,CAACgG,gBAAgB,GAAG,IAAI,CAACC,YAAY,CAACvF,SAAS,CAAEwF,WAAmB,IAAI;MAC1E;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACC,QAAQ,EAAE,GAAG,IAAI,CAACC,QAAQ;MACzD,IAAIJ,WAAW,KAAKC,QAAQ,EAAE;QAC5B,IAAI,CAACI,eAAe,EAAE;;IAE1B,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC7E,WAAW,EAAE;;EAEvC;EAEA;;;EAGAoF,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACR,OAAO,IAAI,IAAI,CAACL,UAAU,CAACe,IAAI,CAACtD,MAAM,GAAG,CAAC,EAAE;MACpD;;IAEF,IAAI,CAACuD,cAAc,GAAG,IAAI,CAACC,UAAU,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,IAAI,CAACb,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACc,aAAa,CAACC,UAAU,GAAG,IAAI,CAACV,UAAU;IAC/C,IAAI,CAACS,aAAa,CAACE,QAAQ,GAAG,IAAI,CAACC,QAAQ;IAE3C,IAAI,CAAClB,eAAe,CAACmB,uBAAuB,CAAC,IAAI,CAACJ,aAAa,CAAC,CAACnG,SAAS,CAAC;MACzEoC,IAAI,EAAGoE,GAA4B,IAAI;QACrC,IAAI,CAACnB,OAAO,GAAG,KAAK;QAEpB,IAAIoB,cAAc,GAAG,EAAE;QACvBD,GAAG,CAACE,OAAO,CAACC,EAAE,IAAG;UACf,IAAI,IAAI,CAACC,gCAAgC,CAACD,EAAE,CAAC,EAAE;YAC7CF,cAAc,CAACI,IAAI,CAACF,EAAE,CAAC;;QAE3B,CAAC,CAAC;QACF,IAAI,CAAC3B,UAAU,CAACe,IAAI,GAAGU,cAAc;MACvC,CAAC;MACDK,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACzB,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC0B,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;;;;EAIAF,gCAAgCA,CAACI,YAAmC;IAClE,IAAIR,GAAG,GAAG,KAAK;IACf,IAAI,CAACR,cAAc,CAACU,OAAO,CAACO,EAAE,IAAG;MAC/B,IAAIA,EAAE,KAAKD,YAAY,CAAClD,SAAS,CAAC6B,QAAQ,EAAE,EAAE;QAC5Ca,GAAG,GAAG,IAAI;QACV;;IAEJ,CAAC,CAAC;IACF,OAAOA,GAAG;EACZ;;;uBAhFWrB,6BAA6B,EAAAzE,+DAAA,CAAAE,mEAAA;IAAA;EAAA;;;YAA7BuE,6BAA6B;MAAAnE,SAAA;MAAAmG,MAAA;QAAAzB,UAAA;QAAAS,aAAA;QAAAG,QAAA;QAAAL,UAAA;QAAAL,QAAA;QAAAL,YAAA;MAAA;MAAAtE,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAgG,uCAAA9F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1Cb,wDAAA,IAAA4G,4CAAA,iBAEM;UAEN5G,wDAAA,IAAA6G,8CAAA,oBA2BQ;;;UA/BF7G,wDAAA,SAAAc,GAAA,CAAA6D,OAAA,CAAa;UAIX3E,uDAAA,GAAc;UAAdA,wDAAA,UAAAc,GAAA,CAAA6D,OAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFtB;AAC4C;AAEY;AAEyB;;;;;;;;ICAvE3E,4DAAA,aAA0E;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAK;;;;;;;;IAG3FA,4DAAA,YAIC;IACCA,oDAAA,0BACF;IAAAA,0DAAA,EAAK;;;IAJHA,wDAAA,eAAAA,6DAAA,IAAA+G,GAAA,EAA4B;;;;;;;;IAK9B/G,4DAAA,YAIC;IACCA,oDAAA,wBACF;IAAAA,0DAAA,EAAK;;;IAJHA,wDAAA,eAAAA,6DAAA,IAAAgH,GAAA,EAAmC;;;;;IAKrChH,4DAAA,aAA0E;IACxEA,oDAAA,qBACF;IAAAA,0DAAA,EAAK;;;;;;;;;;;IAtBTA,qEAAA,GAAyC;IACvCA,4DAAA,YAAsB;IACmCA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAK;IAC7EA,wDAAA,IAAAiH,+CAAA,gBAA2F;IAC3FjH,4DAAA,YAA8D;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAK;IACvFA,4DAAA,YAAkD;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAK;IACpEA,wDAAA,IAAAkH,+CAAA,gBAMK;IACLlH,wDAAA,KAAAmH,gDAAA,gBAMK;IACLnH,wDAAA,KAAAoH,gDAAA,gBAEK;IACPpH,0DAAA,EAAK;IACPA,mEAAA,EAAe;;;;IAtBPA,uDAAA,GAAwB;IAAxBA,wDAAA,eAAAA,6DAAA,IAAAqH,GAAA,EAAwB;IACvBrH,uDAAA,GAAsB;IAAtBA,wDAAA,UAAAsH,MAAA,CAAA5I,eAAA,CAAsB;IACvBsB,uDAAA,GAA+B;IAA/BA,wDAAA,eAAAA,6DAAA,IAAAuH,GAAA,EAA+B;IAGhCvH,uDAAA,GAA4D;IAA5DA,wDAAA,SAAAsH,MAAA,CAAAE,kCAAA,KAAAF,MAAA,CAAA5I,eAAA,CAA4D;IAO5DsB,uDAAA,GAA4D;IAA5DA,wDAAA,SAAAsH,MAAA,CAAAE,kCAAA,KAAAF,MAAA,CAAA5I,eAAA,CAA4D;IAM1DsB,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAsH,MAAA,CAAAG,gBAAA,CAAsB;;;;;IAW3BzH,4DAAA,aAAiF;IAC/EA,oDAAA,qBACF;IAAAA,0DAAA,EAAK;;;;;;;;;;;IANTA,qEAAA,GAAwC;IACtCA,4DAAA,YAAsB;IAC0CA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAK;IACpFA,4DAAA,aAAwD;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAK;IACzEA,wDAAA,IAAA0H,+CAAA,iBAEK;IACL1H,4DAAA,YAA8D;IAAAA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAK;IAEvFA,mEAAA,EAAe;;;;IAPPA,uDAAA,GAA+B;IAA/BA,wDAAA,eAAAA,6DAAA,IAAA2H,GAAA,EAA+B;IAE9B3H,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAqE,MAAA,CAAAoD,gBAAA,CAAsB;IAGvBzH,uDAAA,GAA+B;IAA/BA,wDAAA,eAAAA,6DAAA,IAAA4H,GAAA,EAA+B;;;ADtBvC,MAAOC,kBAAkB;EAQ7BtJ,YACYuJ,kBAAsC,EACxCtJ,KAAuC;IADrC,KAAAsJ,kBAAkB,GAAlBA,kBAAkB;IACpB,KAAAtJ,KAAK,GAALA,KAAK;IARf,KAAAiJ,gBAAgB,GAAY,KAAK;IACjC,KAAAM,iBAAiB,GAAY,KAAK;IAClC,KAAArJ,eAAe,GAAY,KAAK;IAChC,KAAA8I,kCAAkC,GAAY,KAAK;EAMhD;EAEH5I,QAAQA,CAAA;IACN,IAAI,CAACoJ,YAAY,GAAG,IAAI,CAACxJ,KAAK,CAACa,IAAI,CAACpB,mDAAM,CAACI,mFAAoB,CAAC,CAAC,CAACiB,SAAS,CAAEC,KAAmB,IAAI;MAClG,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClB,IAAI,CAACiI,gBAAgB,GAAGlI,KAAK,CAACC,QAAQ,CAACyI,sBAAsB;QAC7D,IAAI,CAACT,kCAAkC,GAAGjI,KAAK,CAACC,QAAQ,CAACgI,kCAAkC;QAC3F,IAAI,CAACO,iBAAiB,GAAGxI,KAAK,CAACC,QAAQ,CAACI,WAAW,KAAKxB,kEAAgB,CAAC8J,OAAO;QAChF,IAAI,CAACxJ,eAAe,GAAGa,KAAK,CAACC,QAAQ,CAACI,WAAW,KAAKxB,kEAAgB,CAACc,KAAK;OAC7E,MAAM;QACL,IAAI,CAACuI,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACD,kCAAkC,GAAG,KAAK;;IAEnD,CAAC,CAAC;EACJ;EAEAW,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACJ,iBAAiB,EAAE;MAC1B,OAAO,cAAc;KACtB,MAAM;MACL,OAAO,OAAO;;EAElB;EAEA3C,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC4C,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACjI,WAAW,EAAE;;IAGjC,IAAI,IAAI,CAACqI,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACrI,WAAW,EAAE;;EAE9C;;;uBA3CW8H,kBAAkB,EAAA7H,+DAAA,CAAAE,sEAAA,GAAAF,+DAAA,CAAAI,8CAAA;IAAA;EAAA;;;YAAlByH,kBAAkB;MAAAvH,SAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA2H,4BAAAzH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB/Bb,4DAAA,aAA6B;UAIvBA,wDAAA,IAAAuI,0CAAA,2BAwBe;UAGfvI,wDAAA,IAAAwI,0CAAA,0BASe;UACjBxI,0DAAA,EAAM;UAIVA,uDAAA,oBAA+B;;;UAzCVA,uDAAA,GAAwB;UAAxBA,wDAAA,UAAAc,GAAA,CAAAiH,iBAAA,CAAwB;UA2BxB/H,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAAiH,iBAAA,CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IE3BxC/H,uDAAA,iCAI2B;;;;IAFzBA,wDAAA,iBAAAyI,SAAA,CAAAC,kBAAA,CAA0C,aAAAD,SAAA,CAAAE,QAAA;;;;;IAK1C3I,uDAAA,gCAA4F;;;;IAApEA,wDAAA,iBAAAyI,SAAA,CAAAC,kBAAA,CAA0C;;;;;IAIpE1I,uDAAA,aAAqE;;;;;IAdvEA,4DAAA,aAAqF;IACtDA,oDAAA,GAAuB;IAAAA,0DAAA,EAAI;IAExDA,wDAAA,IAAA4I,4EAAA,qCAI2B;IAE3B5I,wDAAA,IAAA6I,gEAAA,gCAAA7I,oEAAA,CAEc;IAGdA,wDAAA,IAAA+I,wDAAA,iBAAqE;IACvE/I,0DAAA,EAAM;;;;;;;IAdyBA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAyI,SAAA,CAAAO,UAAA,CAAuB;IAGjDhJ,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAqE,MAAA,CAAA4E,eAAA,CAAuB,aAAAC,GAAA;IAUpBlJ,uDAAA,GAAqB;IAArBA,wDAAA,UAAAqE,MAAA,CAAA8E,WAAA,CAAAC,IAAA,EAAqB;;;;;IAf/BpJ,4DAAA,UAAwB;IACtBA,wDAAA,IAAAqJ,kDAAA,iBAeM;IACRrJ,0DAAA,EAAM;;;;IAhBoBA,uDAAA,GAAe;IAAfA,wDAAA,YAAAsH,MAAA,CAAAgC,UAAA,CAAe;;;ADQnC,MAAOC,6BAA6B;EAKxChL,YAAA,GAAe;EAEfK,QAAQA,CAAA,GAAU;EAElBuK,WAAWA,CAACK,KAAa;IACvB,OAAOA,KAAK,KAAK,IAAI,CAACF,UAAU,CAACvH,MAAM,GAAG,CAAC;EAC7C;;;uBAXWwH,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAAjJ,SAAA;MAAAmG,MAAA;QAAA6C,UAAA;QAAAG,SAAA;QAAAR,eAAA;MAAA;MAAAxI,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA+I,uCAAA7I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT1Cb,wDAAA,IAAA2J,4CAAA,iBAiBM;;;UAjBA3J,wDAAA,SAAAc,GAAA,CAAAwI,UAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCqF;;;;;;;;ICGrGtJ,4DAAA,SAAoC;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;;;;IAArBA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA6J,QAAA,CAAAC,IAAA,CAAgB;;;;;IAOpD9J,4DAAA,SAAoC;IAClCA,oDAAA,GACF;;IAAAA,0DAAA,EAAK;;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,OAAAgK,QAAA,CAAAC,OAAA,EAAAC,QAAA,kBACF;;;;;IAJFlK,4DAAA,SAAqC;IACXA,oDAAA,GAA2B;IAAAA,0DAAA,EAAK;IACxDA,wDAAA,IAAAmK,wDAAA,gBAEK;IACLnK,4DAAA,SAAI;IAAAA,oDAAA,GAA8B;;IAAAA,0DAAA,EAAK;;;;;IAJfA,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAkK,QAAA,IAAAE,YAAA,CAA2B;IAC7BpK,uDAAA,GAAY;IAAZA,wDAAA,YAAAqK,MAAA,CAAAZ,SAAA,CAAY;IAG9BzJ,uDAAA,GAA8B;IAA9BA,+DAAA,CAAAA,yDAAA,OAAAkK,QAAA,EAA8B;;;;;IAdxClK,4DAAA,YAAuC;IAGTA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;IACrDA,wDAAA,IAAAuK,mDAAA,gBAAyD;IACzDvK,4DAAA,SAAI;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;IAGlBA,4DAAA,YAAO;IACLA,wDAAA,IAAAwK,mDAAA,gBAMK;IACPxK,0DAAA,EAAQ;;;;;IAboBA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAyK,QAAA,CAAAC,YAAA,CAAwB;IAC1B1K,uDAAA,GAAY;IAAZA,wDAAA,YAAAsH,MAAA,CAAAmC,SAAA,CAAY;IAKdzJ,uDAAA,GAAa;IAAbA,wDAAA,YAAAyK,QAAA,CAAAE,IAAA,CAAa;;;ADEjC,MAAOC,6BAA8B,SAAQhB,4EAA0B;EAM3ErL,YAAoBsM,cAAoC;IACtD,KAAK,EAAE;IADW,KAAAA,cAAc,GAAdA,cAAc;IAFlC,KAAAC,SAAS,GAAuB,EAAE;EAIlC;EAEAlM,QAAQA,CAAA;IACN,IAAI,CAACmM,QAAQ,EAAE;IACf,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,WAAW,CAAC,IAAI,CAACC,YAAY,EAAE,YAAY,CAAC;EACpE;EAEAF,QAAQA,CAAA;IACN,IAAI,CAACF,cAAc,CAACK,qBAAqB,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC7L,SAAS,CAAC;MACvEoC,IAAI,EAAG0J,QAAqB,IAAI;QAC9B,IAAI,CAAC3B,SAAS,GAAG2B,QAAQ,CAACC,OAAO;MACnC,CAAC;MACDjF,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;;;uBAxBWwE,6BAA6B,EAAA5K,+DAAA,CAAAE,wEAAA;IAAA;EAAA;;;YAA7B0K,6BAA6B;MAAAtK,SAAA;MAAAmG,MAAA;QAAAwE,YAAA;QAAAE,QAAA;MAAA;MAAA5K,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA4K,uCAAA1K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX1Cb,wDAAA,IAAAwL,8CAAA,oBAiBQ;;;UAjBiBxL,wDAAA,YAAAc,GAAA,CAAAgK,SAAA,CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;ACGrC;AACkE;;;;;;;ICA5D9K,4DAAA,SAA8C;IAAAA,oDAAA,GAAa;IAAAA,0DAAA,EAAK;;;;IAAlBA,uDAAA,GAAa;IAAbA,+DAAA,CAAAyL,KAAA,CAAAC,IAAA,CAAa;;;;;IAO3D1L,4DAAA,SAA8C;IAC5CA,oDAAA,GACF;;IAAAA,0DAAA,EAAK;;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,OAAA2L,KAAA,CAAApF,EAAA,EAAAqF,QAAA,qBACF;;;;;IALF5L,4DAAA,SAAqC;IACXA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;IAErDA,wDAAA,IAAA6L,uDAAA,gBAEK;IACP7L,0DAAA,EAAK;;;;;IALqBA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA4L,QAAA,IAAAE,SAAA,CAAwB;IAE7B9L,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAyK,QAAA,CAAAsB,gBAAA,CAAyB;;;;;IAM5C/L,4DAAA,SAAiD;IAC/CA,oDAAA,GACF;;IAAAA,0DAAA,EAAK;;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,OAAAiM,SAAA,CAAA1F,EAAA,EAAAkE,QAAA,CAAAE,IAAA,OACF;;;;;IAnBN3K,4DAAA,YAAuC;IAGTA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;IACrDA,wDAAA,IAAAkM,kDAAA,gBAAgE;IAClElM,0DAAA,EAAK;IAEPA,4DAAA,YAAO;IACLA,wDAAA,IAAAmM,kDAAA,gBAMK;IACLnM,4DAAA,SAAI;IACsBA,oDAAA,aAAK;IAAAA,0DAAA,EAAK;IAClCA,wDAAA,KAAAoM,mDAAA,gBAEK;IACPpM,0DAAA,EAAK;;;;IAjBqBA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAyK,QAAA,CAAAC,YAAA,CAAwB;IAC7B1K,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAyK,QAAA,CAAAsB,gBAAA,CAAyB;IAIxB/L,uDAAA,GAAa;IAAbA,wDAAA,YAAAyK,QAAA,CAAAE,IAAA,CAAa;IASX3K,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAyK,QAAA,CAAAsB,gBAAA,CAAyB;;;ADD/C,MAAOM,4BAA6B,SAAQzC,4EAA0B;EAK1ErL,YAAA;IACE,KAAK,EAAE;EACT;EAEAK,QAAQA,CAAA;IACN,IAAI,CAACkM,SAAS,GAAG,IAAI,CAACE,WAAW,CAAC,IAAI,CAACC,YAAY,EAAE,SAAS,CAAC;IAC/D,IAAI,CAACqB,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACrB,YAAY,CAACjF,OAAO,CAAC,CAACuG,CAAC,EAAEC,CAAC,KAAI;MACjC,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;QACnB;;MAEF,MAAM2B,MAAM,GAAG,EAAE;MACjB,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAE;MACrB,KAAK,MAAMC,IAAI,IAAIL,CAAC,CAACM,YAAY,EAAE;QACjC,IAAI,CAACH,GAAG,CAACI,GAAG,CAACF,IAAI,CAAClH,UAAU,CAAC,EAAE;UAC7BgH,GAAG,CAACK,GAAG,CAACH,IAAI,CAAClH,UAAU,EAAE,IAAI,CAAC;UAC9B+G,MAAM,CAACtG,IAAI,CAAC;YACVI,EAAE,EAAEqG,IAAI,CAAClH,UAAU;YACnBgG,IAAI,EAAEkB,IAAI,CAACxC;WACZ,CAAC;;;MAIN,IAAI,CAACU,SAAS,CAAC0B,CAAC,CAAC,CAACT,gBAAgB,GAAGU,MAAM;IAC7C,CAAC,CAAC;EACJ;;;uBAjCWJ,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAA/L,SAAA;MAAAmG,MAAA;QAAAwE,YAAA;MAAA;MAAA1K,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAqM,sCAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBzCb,wDAAA,IAAAiN,6CAAA,oBAsBQ;;;UAtBiBjN,wDAAA,YAAAc,GAAA,CAAAgK,SAAA,CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCmB;AAE5B;AAKkD;AAClD;;;;;;;;;;;;;ICexB9K,4DAAA,cAA8E;IAS1EA,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;IAJPA,uDAAA,GAAuB;IAAvBA,wDAAA,wBAAuB;;;;;IAiBzBA,4DAAA,UAAgC;IAC9BA,uDAAA,kCAG2B;IAC7BA,0DAAA,EAAM;;;;IAHFA,uDAAA,GAAyB;IAAzBA,wDAAA,eAAAqE,MAAA,CAAAiF,UAAA,CAAyB,oBAAAjF,MAAA,CAAA4E,eAAA;;;ADjC7B,MAAOqE,2BAA4B,SAAQhP,mFAAmB;EASlEC,YACEC,KAAuC,EAC/B+O,cAA8B,EAC9B7I,eAAgC,EAC9BjG,cAA8B;IAExC,KAAK,CAACD,KAAK,EAAEC,cAAc,CAAC;IAJpB,KAAA8O,cAAc,GAAdA,cAAc;IACd,KAAA7I,eAAe,GAAfA,eAAe;IACb,KAAAjG,cAAc,GAAdA,cAAc;IAV1B,KAAAwK,eAAe,GAAY,IAAI;IAE/B,KAAAuE,UAAU,GAAW,oBAAoB;EAWzC;EAEA5O,QAAQA,CAAA;IACN,IAAI,CAACO,eAAe,EAAE;IAEtB,IAAI,CAACsO,UAAU,EAAE;EACnB;EAEArI,WAAWA,CAAA;IACT,IAAI,CAACtF,mBAAmB,EAAE;EAC5B;EAEA4N,oBAAoBA,CAACC,OAAsB;IACzC,IAAI,CAAClI,aAAa,GAAGkI,OAAO;IAC5B,IAAI,CAACC,YAAY,GAAGR,6CAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IACjDH,OAAO,CAAChI,QAAQ,GAAG,IAAI,CAAC6H,UAAU;IAClC,IAAI,CAACO,mBAAmB,GAAGJ,OAAO,CAACI,mBAAmB;IAEtD,IAAI,CAACC,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACvI,aAAa,EAAE;MACvB;;IAEF,IAAI,CAACwI,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB,IAAI,CAACV,cAAc,CAACW,aAAa,EAAE;IAEnC,IAAI,CAACxJ,eAAe,CAACyJ,kBAAkB,CAAC,IAAI,CAAC1I,aAAa,CAAC,CAACnG,SAAS,CAAC;MACpEoC,IAAI,EAAG0J,QAA8B,IAAI;QACvC,IAAI,CAAC9B,UAAU,GAAG8B,QAAQ;QAC1B,IAAI,CAACgD,wBAAwB,EAAE;QAC/B,IAAI,CAACb,cAAc,CAACc,YAAY,EAAE;MACpC,CAAC;MACDjI,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAkI,oBAAoBA,CAACC,IAAuB;IAC1C,IAAI,CAACf,UAAU,GAAG,EAAE;IACpB,IAAIgB,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,IAAI,CAAC;IAC5BC,IAAI,CAACxI,OAAO,CAAC0I,CAAC,IAAG;MACf,IAAIH,IAAI,CAACG,CAAC,CAAC,EAAE;QACX,IAAI,CAAClB,UAAU,IAAIkB,CAAC,GAAG,GAAG;;IAE9B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACjJ,aAAa,EAAE;MACvB;;IAGF,IAAI,CAACA,aAAa,CAACE,QAAQ,GAAG,IAAI,CAAC6H,UAAU;IAC7C,IAAI,CAACE,oBAAoB,CAAC,IAAI,CAACjI,aAAa,CAAC;EAC/C;EAEAkJ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrF,UAAU,EAAEvH,MAAM,GAAG,CAAC;EACpC;EAEA0L,UAAUA,CAAA;IACR,IAAI,CAACmB,cAAc,GAAG,IAAIzB,qDAAS,CAAC;MAClC0B,IAAI,EAAE,IAAI3B,uDAAW,CAAC,OAAO;KAC9B,CAAC;IAEF,IAAI,CAAC2B,IAAI,CAACC,YAAY,CAACxP,SAAS,CAACyP,GAAG,IAAG;MACrC,IAAI,CAAC9F,eAAe,GAAG8F,GAAG,IAAI,OAAO;IACvC,CAAC,CAAC;EACJ;EAEA,IAAIF,IAAIA,CAAA;IACN,OAAO,IAAI,CAACD,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC;EACxC;EAEAC,kBAAkBA,CAACC,IAAgB;IACjC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,EAAE;;IAEX,OAAOA,IAAI,CAACC,MAAM,CAAElJ,EAAY,IAAI;MAClC,OAAO,IAAI,CAACmJ,qBAAqB,CAACC,QAAQ,CAACpJ,EAAE,CAACqJ,OAAO,CAAC;IACxD,CAAC,CAAC;EACJ;EAEAC,0BAA0BA,CAACC,UAAoB;IAC7C,IAAI,CAACC,oBAAoB,GAAGD,UAAU;IAEtC,IAAI,IAAI,CAAClG,UAAU,EAAE;MACnB,IAAI,CAAC8E,wBAAwB,EAAE;;EAEnC;EAEAA,wBAAwBA,CAAA;IACtB,IAAI,CAACgB,qBAAqB,GAAG,EAAE;IAE/B,IAAI,CAAC9F,UAAU,CAACtD,OAAO,CAAC0J,GAAG,IAAG;MAC5BA,GAAG,CAAChH,kBAAkB,GAAGgH,GAAG,CAACC,YAAY,CAACR,MAAM,CAAC5C,CAAC,IAAI,IAAI,CAACkD,oBAAoB,CAACJ,QAAQ,CAAC9C,CAAC,CAACqD,UAAU,CAAC,CAAC;IACzG,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACvG,UAAU,CAACtD,OAAO,CAAC0J,GAAG,IAAG;MAC5BA,GAAG,CAAChH,kBAAkB,CAAC1C,OAAO,CAAC8J,KAAK,IAAG;QACrC,IAAIC,GAAG,GAAGD,KAAK,CAACjD,YAAY,CAACmD,MAAM,CAAC,UAAUC,WAAW,EAAEC,IAAI;UAC7D,OAAOD,WAAW,GAAGC,IAAI,CAACpN,QAAQ;QACpC,CAAC,EAAE,CAAC,CAAC;QACL,IAAI,CAAC+M,SAAS,IAAIE,GAAG;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACzG,UAAU,GAAG+D,6CAAW,CAAC,IAAI,CAAC/D,UAAU,CAAC;EAChD;;;uBAnIWgE,2BAA2B,EAAAtN,+DAAA,CAAAE,8CAAA,GAAAF,+DAAA,CAAAI,kEAAA,GAAAJ,+DAAA,CAAAI,mEAAA,GAAAJ,+DAAA,CAAAI,kEAAA;IAAA;EAAA;;;YAA3BkN,2BAA2B;MAAAhN,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA0P,qCAAAxP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBxCb,4DAAA,aAA6B;UAIzBA,wDAAA,uBAAAuQ,kFAAAC,MAAA;YAAA,OAAa1P,GAAA,CAAA4M,oBAAA,CAAA8C,MAAA,CAA4B;UAAA,EAAC,4BAAAC,uFAAAD,MAAA;YAAA,OACxB1P,GAAA,CAAAyO,0BAAA,CAAAiB,MAAA,CAAkC;UAAA,EADV,4BAAAE,uFAAAF,MAAA;YAAA,OAExB1P,GAAA,CAAAwN,oBAAA,CAAAkC,MAAA,CAA4B;UAAA,EAFJ;UAI3CxQ,0DAAA,EAA0B;UAG3BA,uDAAA,aAAkD;UAElDA,4DAAA,aAAiB;UAGiCA,oDAAA,eAAQ;UAAAA,0DAAA,EAAQ;UAC5DA,4DAAA,yBAAqE;UACTA,oDAAA,aAAK;UAAAA,0DAAA,EAAmB;UAClFA,4DAAA,2BAAyD;UAAAA,oDAAA,YAAI;UAAAA,0DAAA,EAAmB;UAKtFA,wDAAA,KAAA2Q,2CAAA,kBAWM;UACR3Q,0DAAA,EAAM;UAENA,4DAAA,eAAuB;UAEnBA,uDAAA,0BAKkB;UAElBA,wDAAA,KAAA4Q,2CAAA,kBAKM;UACR5Q,0DAAA,EAAM;;;UAnDNA,uDAAA,GAAiC;UAAjCA,wDAAA,mBAAAc,GAAA,CAAAjB,cAAA,CAAiC,iBAAAiB,GAAA,CAAAnB,YAAA;UAazBK,uDAAA,GAA4B;UAA5BA,wDAAA,cAAAc,GAAA,CAAA8N,cAAA,CAA4B;UAS9B5O,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAA6N,eAAA,GAAuB;UAkBzB3O,uDAAA,GAA6B;UAA7BA,wDAAA,iBAAAc,GAAA,CAAA8M,YAAA,CAA6B,eAAA9M,GAAA,CAAAwI,UAAA,kBAAAxI,GAAA,CAAAwI,UAAA,CAAAvH,MAAA,iBAAAjB,GAAA,CAAA+O,SAAA;UAKzB7P,uDAAA,GAAwB;UAAxBA,wDAAA,SAAAc,GAAA,CAAAwI,UAAA,kBAAAxI,GAAA,CAAAwI,UAAA,CAAAvH,MAAA,CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9CQ;AACX;AAEjC;AAO+B;AAK/B;AAC4C;AAGqC;;;;;;;;;;;;;ICP7E/B,4DAAA,cAA8C;IACxCA,oDAAA,cAAO;IAAAA,0DAAA,EAAK;IAChBA,uDAAA,oCAAkG;IACpGA,0DAAA,EAAM;;;IAD8CA,uDAAA,GAAmB;IAAnBA,wDAAA,oBAAmB;;;;;IASjEA,4DAAA,YAAqD;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAI;;;;IAAhCA,uDAAA,GAA4B;IAA5BA,gEAAA,KAAAgR,MAAA,CAAAC,eAAA,YAA4B;;;;;IADnFjR,4DAAA,cAAqE;IACnEA,wDAAA,IAAAkR,+CAAA,gBAAqF;IACvFlR,0DAAA,EAAM;;;;IADAA,uDAAA,GAAqB;IAArBA,wDAAA,SAAAqK,MAAA,CAAA4G,eAAA,CAAqB;;;;;;IAE3BjR,4DAAA,cAA4D;IACNA,wDAAA,mBAAAmR,oEAAA;MAAAnR,2DAAA,CAAAqR,GAAA;MAAA,MAAAC,MAAA,GAAAtR,2DAAA;MAAA,OAASA,yDAAA,CAAAsR,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACzR,oDAAA,UAAG;IAAAA,0DAAA,EAAS;;;;;;IAN5FA,4DAAA,cAA+F;IAE3FA,wDAAA,IAAA0R,2CAAA,kBAEM;IACN1R,wDAAA,IAAA2R,2CAAA,kBAEM;IACN3R,4DAAA,cAAsC;IAElCA,wDAAA,mBAAA4R,8DAAA;MAAA5R,2DAAA,CAAA6R,GAAA;MAAA,MAAAC,MAAA,GAAA9R,2DAAA;MAAA,OAASA,yDAAA,CAAA8R,MAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAO5B/R,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;;IAhBLA,uDAAA,GAA4B;IAA5BA,wDAAA,SAAAqE,MAAA,CAAA2N,sBAAA,CAA4B;IAG5BhS,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAqE,MAAA,CAAA4N,aAAA,CAAmB;IASrBjS,uDAAA,GAAuB;IAAvBA,wDAAA,wBAAuB;;;;;IAsBzBA,uDAAA,cAAuF;;;;;IAEvFA,4DAAA,cAAsE;IACpEA,uDAAA,yBAKkB;IACpBA,0DAAA,EAAM;;;;;IALFA,uDAAA,GAAuB;IAAvBA,wDAAA,UAAAkS,UAAA,CAAAC,MAAA,CAAuB,kBAAAC,OAAA,CAAArK,iBAAA,IAAAqK,OAAA,CAAAxE,YAAA,cAAAwE,OAAA,CAAAC,QAAA,gBAAAD,OAAA,CAAAE,mBAAA,CAAAJ,UAAA,CAAAC,MAAA;;;;;IAUzBnS,4DAAA,eAAwD;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;;;;IAA5BA,uDAAA,GAAqB;IAArBA,gEAAA,MAAAkS,UAAA,CAAAC,MAAA,MAAqB;;;;;IAK3EnS,4DAAA,aAA6C;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;;;;;IAG1DA,4DAAA,aAAiD;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAE5DA,4DAAA,aAA8C;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;;;;;IAG3DA,4DAAA,SAA0B;IACxBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAuS,SAAA,CAAAC,OAAA,MACF;;;;;IAGAxS,4DAAA,SAA8B;IAC5BA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAuS,SAAA,CAAAE,aAAA,MACF;;;;;IAKMzS,4DAAA,WAAmC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAO;;;;IAAnCA,uDAAA,GAA4B;IAA5BA,gEAAA,MAAA0S,QAAA,CAAAC,eAAA,MAA4B;;;;;IAFjE3S,4DAAA,SAAqC;IACnCA,oDAAA,GACA;IAAAA,wDAAA,IAAA4S,yDAAA,mBAAsE;IACxE5S,0DAAA,EAAK;;;;IAFHA,uDAAA,GACA;IADAA,gEAAA,MAAA0S,QAAA,CAAAI,QAAA,SAAAJ,QAAA,CAAAhH,IAAA,MACA;IAAO1L,uDAAA,GAA0B;IAA1BA,wDAAA,SAAA0S,QAAA,CAAAC,eAAA,CAA0B;;;;;IAIvC3S,4DAAA,SAA2B;IACzBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAuS,SAAA,CAAAQ,SAAA,MACF;;;;;IAnBF/S,4DAAA,aAAuD;IACrDA,wDAAA,IAAAgT,iDAAA,iBAEK;IACLhT,4DAAA,SAAI;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,SAAI;IAAAA,oDAAA,GAAuC;;IAAAA,0DAAA,EAAK;IAChDA,wDAAA,IAAAiT,iDAAA,iBAEK;IACLjT,4DAAA,SAAI;IAEAA,wDAAA,KAAAkT,kDAAA,iBAGK;IACPlT,0DAAA,EAAK;IAEPA,wDAAA,KAAAmT,kDAAA,iBAEK;IACPnT,0DAAA,EAAK;;;;;IAnBEA,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAoT,OAAA,CAAAnB,aAAA,CAAmB;IAGpBjS,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAuS,SAAA,CAAAc,WAAA,CAAuB;IACvBrT,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAA,yDAAA,OAAAuS,SAAA,CAAA3M,QAAA,EAAuC;IACtC5F,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAoT,OAAA,CAAArL,iBAAA,CAAuB;IAKH/H,uDAAA,GAAc;IAAdA,wDAAA,YAAAuS,SAAA,CAAAe,KAAA,CAAc;IAMlCtT,uDAAA,GAAoB;IAApBA,wDAAA,UAAAoT,OAAA,CAAAnB,aAAA,CAAoB;;;;;IA9C/BjS,4DAAA,UAA4C;IAG1CA,wDAAA,IAAAuT,4CAAA,kBAAuF;IAEvFvT,wDAAA,IAAAwT,4CAAA,kBAOM;IAGNxT,4DAAA,YAA4B;IAC1BA,oDAAA,GACA;IAAAA,wDAAA,IAAAyT,6CAAA,mBAAoF;IACtFzT,0DAAA,EAAI;IAEJA,4DAAA,gBAAsB;IAElBA,wDAAA,IAAA0T,2CAAA,iBAA0D;IAC1D1T,4DAAA,aAAuB;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAK;IACpCA,4DAAA,cAAuB;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAK;IAChCA,wDAAA,KAAA2T,4CAAA,iBAA4D;IAC5D3T,4DAAA,cAAuB;IAAAA,oDAAA,qBAAa;IAAAA,0DAAA,EAAK;IACzCA,wDAAA,KAAA4T,4CAAA,iBAA2D;IAC7D5T,0DAAA,EAAK;IACLA,wDAAA,KAAA6T,4CAAA,kBAoBK;IACP7T,0DAAA,EAAQ;;;;;IA/CFA,uDAAA,GAAuC;IAAvCA,wDAAA,SAAA8T,MAAA,CAAAC,kBAAA,CAAA7B,UAAA,CAAAC,MAAA,EAAuC;IAEvCnS,uDAAA,GAAoC;IAApCA,wDAAA,SAAA8T,MAAA,CAAAE,eAAA,CAAA9B,UAAA,CAAAC,MAAA,EAAoC;IAWxCnS,uDAAA,GACA;IADAA,gEAAA,MAAAkS,UAAA,CAAA+B,KAAA,MACA;IAAOjU,uDAAA,GAA4B;IAA5BA,wDAAA,SAAA8T,MAAA,CAAA9B,sBAAA,CAA4B;IAK5BhS,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA8T,MAAA,CAAA7B,aAAA,CAAmB;IAGnBjS,uDAAA,GAAuB;IAAvBA,wDAAA,SAAA8T,MAAA,CAAA/L,iBAAA,CAAuB;IAEvB/H,uDAAA,GAAoB;IAApBA,wDAAA,UAAA8T,MAAA,CAAA7B,aAAA,CAAoB;IAELjS,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAkS,UAAA,CAAAgC,MAAA,CAAgB;;;ADrD5C,MAAOC,sBAAuB,SAAQjW,wDAAa;EAevDK,YACUgP,cAA8B,EAC9B7I,eAAgC,EAChC0P,EAAqB,EACrBC,KAAqB,EACrB7V,KAAuC;IAE/C,KAAK,EAAE;IANC,KAAA+O,cAAc,GAAdA,cAAc;IACd,KAAA7I,eAAe,GAAfA,eAAe;IACf,KAAA0P,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAA7V,KAAK,GAALA,KAAK;IAnBf,KAAA8V,eAAe,GAAkB,EAAE;IAGnC,KAAAC,eAAe,GAA+B,EAAE;IAGhD,KAAAxM,iBAAiB,GAAY,KAAK;IAClC,KAAAkK,aAAa,GAAY,KAAK;IAC9B,KAAAI,QAAQ,GAAY,IAAI;EAcxB;EAEAzT,QAAQA,CAAA;IACN,IAAI,CAACgH,QAAQ,GAAG,IAAI,CAACyO,KAAK,CAACG,QAAQ,CAACnP,IAAI,CAAC,YAAY,CAAC;IAEtD,IAAI,CAAC0C,iBAAiB,GAAG,IAAI,CAACnC,QAAQ,IAAIkL,uDAAY,CAAC5I,OAAO;IAC9D,IAAI,CAAC+J,aAAa,GAAG,IAAI,CAACrM,QAAQ,IAAIkL,uDAAY,CAAC5R,KAAK;IAExD,IAAI,CAAC8I,YAAY,GAAG,IAAI,CAACxJ,KAAK,CAACa,IAAI,CAACpB,oDAAM,CAACI,mFAAoB,CAAC,CAAC,CAACiB,SAAS,CAAEC,KAAmB,IAAI;MAClG,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClB,IAAI,CAACwS,sBAAsB,GAAGzS,KAAK,CAACC,QAAQ,CAACiV,OAAO,IAAIlV,KAAK,CAACC,QAAQ,CAACiV,OAAO,CAAC1S,MAAM,GAAG,CAAC;;IAE7F,CAAC,CAAC;EACJ;EAEAqD,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC4C,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACjI,WAAW,EAAE;;EAEnC;EAEA2U,SAASA,CAAC/G,OAAsB;IAC9B,IAAI,CAACJ,cAAc,CAACW,aAAa,EAAE;IACnC,IAAI,CAACN,YAAY,GAAGR,mCAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IACjDH,OAAO,CAAChI,QAAQ,GAAG,IAAI;IACvBgI,OAAO,CAACgH,QAAQ,GAAGhH,OAAO,CAACgH,QAAQ;IAEnC,IAAI,IAAI,CAAC/O,QAAQ,IAAIkL,uDAAY,CAAC5I,OAAO,IAAI,IAAI,CAACtC,QAAQ,IAAIkL,uDAAY,CAAC5R,KAAK,EAAE;MAChFyO,OAAO,CAAChI,QAAQ,GAAG,IAAI,CAACC,QAAQ;;IAGlC,IAAI,CAAClB,eAAe,CAACkQ,+BAA+B,CAACjH,OAAO,CAAC,CAACrO,SAAS,CAAC;MACtEoC,IAAI,EAAG0J,QAAuB,IAAI;QAChC,IAAI,CAAC2G,gBAAgB,EAAE;QACvB,IAAI,CAACwC,eAAe,GAAG,EAAE;QAEzB,IAAI,CAACD,eAAe,GAAGlJ,QAAQ;QAC/B,IAAI,CAAC6F,eAAe,GAAG,CAAC;QACxB,IAAI,CAACoB,QAAQ,GAAG,IAAI,CAACiC,eAAe,IAAI,IAAI,IAAI,IAAI,CAACA,eAAe,CAACvS,MAAM,IAAI,CAAC;QAEhF,IAAI,CAAC,IAAI,CAACsQ,QAAQ,EAAE;UAClB,IAAI,CAACiC,eAAe,CAACtO,OAAO,CAAC6O,MAAM,IAAG;YACpC,IAAI,CAAC5D,eAAe,IAAI4D,MAAM,CAACX,MAAM,CAACnS,MAAM;YAE5C,IAAG,IAAI,CAACwS,eAAe,CAACM,MAAM,CAAC1C,MAAM,CAAC,IAAI,CAAC,EAAC;cAC1C,IAAI,CAACoC,eAAe,CAACM,MAAM,CAAC1C,MAAM,CAAC,IAAI0C,MAAM,CAACX,MAAM,CAACnS,MAAM;aAC5D,MAAI;cACH,IAAI,CAACwS,eAAe,CAACM,MAAM,CAAC1C,MAAM,CAAC,GAAG0C,MAAM,CAACX,MAAM,CAACnS,MAAM;;UAE9D,CAAC,CAAC;;QAEJ,IAAI,CAACwL,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAC+F,EAAE,CAACU,YAAY,EAAE;MACxB,CAAC;MACD1O,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAqL,SAASA,CAAA;IACP,MAAMsD,SAAS,GAAuB,EAAE;IAExC,IAAI,CAACT,eAAe,CAACtO,OAAO,CAACgP,MAAM,IAAG;MACpC;MACAA,MAAM,CAACd,MAAM,CAAClO,OAAO,CAACiP,KAAK,IAAG;QAC5B,IAAIrI,IAAI,GAAG,IAAImE,2DAAgB,EAAE;QACjCnE,IAAI,CAACsI,KAAK,GAAGF,MAAM,CAACf,KAAK;QACzBrH,IAAI,CAACuI,QAAQ,GAAGF,KAAK,CAAC5B,WAAW;QACjCzG,IAAI,CAACjH,QAAQ,GAAGsP,KAAK,CAACrP,QAAQ;QAC9BgH,IAAI,CAAC3J,OAAO,GAAGgS,KAAK,CAACzC,OAAO;QAC5B5F,IAAI,CAACwI,YAAY,GAAG,EAAE;QACtB;QACAH,KAAK,CAAC3B,KAAK,CAAC5G,GAAG,CAAC2I,QAAQ,IAAG;UACzBzI,IAAI,CAACwI,YAAY,IAAIC,QAAQ,CAACvC,QAAQ,GAAG,GAAG,GAAGuC,QAAQ,CAAC3J,IAAI;UAE5D;UACA,IAAI2J,QAAQ,CAAC1C,eAAe,IAAI,IAAI,IAAI0C,QAAQ,CAAC1C,eAAe,IAAI,EAAE,EAAE;YACtE/F,IAAI,CAACwI,YAAY,IAAI,IAAI,GAAG,GAAG,GAAGC,QAAQ,CAAC1C,eAAe,GAAG,GAAG;;QAEpE,CAAC,CAAC;QACFoC,SAAS,CAAC5O,IAAI,CAACyG,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM0I,OAAO,GAAG;MACdC,cAAc,EAAE,GAAG;MACnBC,YAAY,EAAE,GAAG;MACjBC,gBAAgB,EAAE,GAAG;MACrBC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE,IAAI;MACZC,gBAAgB,EAAE;MAClB;KACD;;IAED,MAAMC,WAAW,GAAG,IAAInF,sDAAW,CAACyE,OAAO,CAAC;IAE5CU,WAAW,CAACC,WAAW,CAAClB,SAAS,CAAC;EACpC;EAEAhB,kBAAkBA,CAAC5B,MAAc;IAC/B,IAAG,IAAI,CAAC+D,2BAA2B,IAAI,IAAI,EAAC;MAC1C,IAAI,CAACA,2BAA2B,GAAG/D,MAAM;MACzC,OAAO,KAAK;KACb,MAAK,IAAG,IAAI,CAAC+D,2BAA2B,IAAI/D,MAAM,EAAC;MAClD,OAAO,KAAK;KACb,MAAI;MACH,IAAI,CAAC+D,2BAA2B,GAAG/D,MAAM;MACzC,OAAO,IAAI;;EAEf;EAEA6B,eAAeA,CAAC7B,MAAc;IAC5B,IAAG,CAAC,IAAI,CAACH,sBAAsB,EAAC;MAC9B,OAAO,KAAK;;IAGd,IAAG,IAAI,CAACmE,qBAAqB,IAAI,IAAI,EAAC;MACpC,IAAI,CAACA,qBAAqB,GAAGhE,MAAM;MACnC,OAAO,IAAI;KACZ,MAAK,IAAG,IAAI,CAACgE,qBAAqB,IAAIhE,MAAM,EAAC;MAC5C,OAAO,KAAK;KACb,MAAI;MACH,IAAI,CAACgE,qBAAqB,GAAGhE,MAAM;MACnC,OAAO,IAAI;;EAEf;EAEAJ,gBAAgBA,CAAA;IACd,IAAI,CAACmE,2BAA2B,GAAG,IAAI;EACzC;EAEA5D,mBAAmBA,CAACH,MAAc;IAChC,OAAO,IAAI,CAACoC,eAAe,CAACpC,MAAM,CAAC;EACrC;;;uBAjKWgC,sBAAsB,EAAAnU,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA,GAAAF,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAI,4DAAA,GAAAJ,+DAAA,CAAAsW,+CAAA;IAAA;EAAA;;;YAAtBnC,sBAAsB;MAAA7T,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA4V,gCAAA1V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BnCb,4DAAA,aAA6B;UAEbA,oDAAA,0DAAmD;UAAAA,0DAAA,EAAI;UAGrEA,4DAAA,aAA6B;UAEzBA,wDAAA,qBAAAwW,+DAAAhG,MAAA;YAAA,OAAW1P,GAAA,CAAA4T,SAAA,CAAAlE,MAAA,CAAiB;UAAA,EAAC;UAG9BxQ,0DAAA,EAAc;UAGfA,wDAAA,IAAAyW,qCAAA,iBAGM;UAGNzW,uDAAA,aAAkD;UAElDA,wDAAA,IAAA0W,qCAAA,iBAqBM;UAEN1W,4DAAA,aAAuB;UAGnBA,uDAAA,yBAKkB;UAElBA,wDAAA,KAAA2W,sCAAA,mBAmDM;UACR3W,0DAAA,EAAM;;;UAlGNA,uDAAA,GAAoB;UAApBA,wDAAA,qBAAoB,mBAAAc,GAAA,CAAAiH,iBAAA;UAKD/H,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAAiH,iBAAA,CAAuB;UAQtC/H,uDAAA,GAAmD;UAAnDA,wDAAA,SAAAc,GAAA,CAAAwT,eAAA,IAAAxT,GAAA,CAAAwT,eAAA,CAAAvS,MAAA,KAAmD;UA4BrD/B,uDAAA,GAA8E;UAA9EA,wDAAA,kBAAAc,GAAA,CAAAiH,iBAAA,KAAAjH,GAAA,CAAAkR,sBAAA,IAAAlR,GAAA,CAAA8M,YAAA,CAA8E,aAAA9M,GAAA,CAAAuR,QAAA,iBAAAvR,GAAA,CAAAkR,sBAAA,GAAAlR,GAAA,CAAAmQ,eAAA;UAKtDjR,uDAAA,GAAkB;UAAlBA,wDAAA,YAAAc,GAAA,CAAAwT,eAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtD6B;AACvB;AACgD;;;;;;;;;;;ICUtGtU,4DAAA,aAAqC;IAGjCA,wDAAA,6BAAA6W,kGAAArG,MAAA;MAAAxQ,2DAAA,CAAA8W,GAAA;MAAA,MAAAhD,MAAA,GAAA9T,2DAAA;MAAA,OAAAA,yDAAA,CAAA8T,MAAA,CAAAiD,QAAA,GAAAvG,MAAA;IAAA,EAAwB;IACzBxQ,0DAAA,EAA4B;;;;IAF3BA,uDAAA,GAA6B;IAA7BA,wDAAA,iBAAAsH,MAAA,CAAA3H,YAAA,CAA6B,cAAA2H,MAAA,CAAAyP,QAAA;;;;;;IAOnC/W,4DAAA,aAA6C;IAIvCA,wDAAA,gCAAAgX,yGAAAxG,MAAA;MAAAxQ,2DAAA,CAAAkJ,GAAA;MAAA,MAAA+N,MAAA,GAAAjX,2DAAA;MAAA,OAAsBA,yDAAA,CAAAiX,MAAA,CAAAC,sBAAA,CAAA1G,MAAA,CAA8B;IAAA,EAAC;IAEtDxQ,0DAAA,EAAgC;;;;IAH/BA,uDAAA,GAA6B;IAA7BA,wDAAA,eAAAqE,MAAA,CAAAxE,cAAA,CAA6B;;;ADd7B,MAAOsX,6BAA6B;EAWxC5Y,YAAoBE,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAVxB,KAAA2Y,SAAS,GAAgC,IAAIR,uDAAY,EAAE;IAC3D,KAAAS,cAAc,GAAoC,IAAIT,uDAAY,EAAE;IACpE,KAAAU,cAAc,GAA2B,IAAIV,uDAAY,EAAE;IAG5D,KAAAW,eAAe,GAAY,IAAI;IAExC,KAAAC,QAAQ,GAAsB,IAAIrZ,mEAAiB,EAAE;EAGA;EAErDS,QAAQA,CAAA;IACN,IAAI,CAAC6Y,mBAAmB,EAAE;EAC5B;EAEAC,cAAcA,CAACjS,aAA4B;IACzC,IAAI,CAAC2R,SAAS,CAACO,IAAI,CAAClS,aAAa,CAAC;EACpC;EAEAyR,sBAAsBA,CAAC1H,UAAoB;IACzC,IAAI,CAAC8H,cAAc,CAACK,IAAI,CAACnI,UAAU,CAAC;EACtC;EAEAiI,mBAAmBA,CAAA;IACjB,IAAI,CAACG,cAAc,GAAG,IAAI,CAACnZ,cAAc,CAACM,UAAU,EAAE;IAEtD,IAAI,CAACgY,QAAQ,GAAG,IAAI5J,qDAAS,CAAC;MAC5B0K,MAAM,EAAE,IAAI3K,uDAAW,CAAC,IAAI,CAAC0K,cAAc,CAAC5Y,MAAM,CAAC;MACnD8Y,KAAK,EAAE,IAAI5K,uDAAW,CAAC,IAAI,CAAC0K,cAAc,CAAC3Y,KAAK,CAAC;MACjD8Y,KAAK,EAAE,IAAI7K,uDAAW,CAAC,IAAI,CAAC0K,cAAc,CAAC1Y,KAAK;KACjD,CAAC;IAEF,IAAI,CAAC6X,QAAQ,CAACjI,YAAY,CAACxP,SAAS,CAACwG,GAAG,IAAG;MACzC,IAAI,CAAC8R,cAAc,CAAC5Y,MAAM,GAAGgZ,OAAO,CAAClS,GAAG,CAAC+R,MAAM,CAAC;MAChD,IAAI,CAACD,cAAc,CAAC3Y,KAAK,GAAG+Y,OAAO,CAAClS,GAAG,CAACgS,KAAK,CAAC;MAC9C,IAAI,CAACF,cAAc,CAAC1Y,KAAK,GAAG8Y,OAAO,CAAClS,GAAG,CAACiS,KAAK,CAAC;MAC9C,IAAI,CAACP,QAAQ,CAACxY,MAAM,GAAGgZ,OAAO,CAAClS,GAAG,CAAC+R,MAAM,CAAC;MAC1C,IAAI,CAACL,QAAQ,CAACvY,KAAK,GAAG+Y,OAAO,CAAClS,GAAG,CAACgS,KAAK,CAAC;MACxC,IAAI,CAACN,QAAQ,CAACtY,KAAK,GAAG8Y,OAAO,CAAClS,GAAG,CAACiS,KAAK,CAAC;MAExC,IAAI,CAACtZ,cAAc,CAACwZ,UAAU,CAAC,IAAI,CAACL,cAAc,CAAC;MACnD,IAAI,CAACP,cAAc,CAACM,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC;IACzC,CAAC,CAAC;EACJ;;;uBA7CWL,6BAA6B,EAAAnX,+DAAA,CAAAE,kEAAA;IAAA;EAAA;;;YAA7BiX,6BAA6B;MAAA7W,SAAA;MAAAmG,MAAA;QAAA5G,cAAA;QAAAF,YAAA;QAAA4X,eAAA;MAAA;MAAAW,OAAA;QAAAd,SAAA;QAAAC,cAAA;QAAAC,cAAA;MAAA;MAAA7W,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAwX,uCAAAtX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV1Cb,4DAAA,aAAsB;UAKhBA,wDAAA,qBAAAoY,sEAAA5H,MAAA;YAAA,OAAW1P,GAAA,CAAA4W,cAAA,CAAAlH,MAAA,CAAsB;UAAA,EAAC;UACnCxQ,0DAAA,EAAc;UAKnBA,4DAAA,aAAsB;UACpBA,wDAAA,IAAAqY,4CAAA,iBAKM;UACRrY,0DAAA,EAAM;UAGNA,wDAAA,IAAAsY,4CAAA,iBAQM;;;UA1BAtY,uDAAA,GAA+B;UAA/BA,wDAAA,gBAAAc,GAAA,CAAAyW,eAAA,CAA+B;UASdvX,uDAAA,GAAc;UAAdA,wDAAA,SAAAc,GAAA,CAAAiW,QAAA,CAAc;UAS/B/W,uDAAA,GAAoB;UAApBA,wDAAA,SAAAc,GAAA,CAAAjB,cAAA,CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACZH;AAC6C;AACnC;AAEjC;AAO8B;AAK9B;AAC4C;AAGoC;AACD;;;;;;;;;;;;;IChB/DG,4DAAA,qBAA4E;IAAAA,oDAAA,GAE1E;IAAAA,0DAAA,EAAa;;;;IAFqCA,wDAAA,UAAAgK,QAAA,CAAAC,OAAA,CAAuB;IAACjK,uDAAA,GAE1E;IAF0EA,+DAAA,CAAAgK,QAAA,CAAAF,IAAA,CAE1E;;;;;;IANR9J,qEAAA,GAA8D;IAC5DA,4DAAA,yBAAqC;IACxBA,oDAAA,cAAO;IAAAA,0DAAA,EAAY;IAC9BA,4DAAA,qBAA6C;IAC3CA,wDAAA,IAAA2Y,qEAAA,yBAEe;IACjB3Y,0DAAA,EAAa;IAEfA,4DAAA,cAA2B;IACtBA,wDAAA,mBAAA4Y,4EAAA;MAAA5Y,2DAAA,CAAA6R,GAAA;MAAA,MAAAC,MAAA,GAAA9R,2DAAA;MAAA,OAASA,yDAAA,CAAA8R,MAAA,CAAA+G,gBAAA,EAAkB;IAAA,EAAC;IAAC7Y,oDAAA,0BAAmB;IAAAA,0DAAA,EAAI;IAE3DA,mEAAA,EAAe;;;;IATCA,uDAAA,GAAuB;IAAvBA,wDAAA,gBAAAgR,MAAA,CAAA8H,OAAA,CAAuB;IACH9Y,uDAAA,GAAoB;IAApBA,wDAAA,YAAAgR,MAAA,CAAAvH,SAAA,CAAA4B,OAAA,CAAoB;;;;;IAL1DrL,4DAAA,aAAmD;IACjDA,wDAAA,IAAA+Y,wDAAA,2BAYe;IACjB/Y,0DAAA,EAAM;;;;IAbWA,uDAAA,GAA6C;IAA7CA,wDAAA,SAAAqE,MAAA,CAAAoF,SAAA,IAAApF,MAAA,CAAA2U,eAAA,CAAAjX,MAAA,KAA6C;;;;;IAc9D/B,4DAAA,aAAqD;IACnDA,uDAAA,qBAA8E;IAChFA,0DAAA,EAAM;;;;;IAGJA,4DAAA,gBAAmC;IAAAA,oDAAA,iDAA0C;IAAAA,0DAAA,EAAY;;;;;IAF3FA,4DAAA,aAAmD;IACjDA,uDAAA,qBAA8F;IAC9FA,wDAAA,IAAAiZ,qDAAA,wBAAyF;IAC3FjZ,0DAAA,EAAM;;;;IAFyDA,uDAAA,GAAmB;IAAnBA,wDAAA,YAAAqK,MAAA,CAAA6O,OAAA,CAAmB;IACpElZ,uDAAA,GAAqB;IAArBA,wDAAA,SAAAqK,MAAA,CAAA8O,OAAA,CAAAC,OAAA,CAAqB;;;;;;IAMrCpZ,4DAAA,iBAKC;IADCA,wDAAA,mBAAAqZ,sEAAA;MAAArZ,2DAAA,CAAAsZ,IAAA;MAAA,MAAAC,OAAA,GAAAvZ,2DAAA;MAAA,OAASA,yDAAA,CAAAuZ,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtBxZ,oDAAA,aACF;IAAAA,0DAAA,EAAS;;;;;;IA1CfA,4DAAA,cAAoE;IAMxDA,wDAAA,mCAAAyZ,8FAAAjJ,MAAA;MAAAxQ,2DAAA,CAAA0Z,IAAA;MAAA,MAAAC,OAAA,GAAA3Z,2DAAA;MAAA,OAAyBA,yDAAA,CAAA2Z,OAAA,CAAAC,qBAAA,CAAApJ,MAAA,CAA6B;IAAA,EAAC;IACxDxQ,0DAAA,EAA2B;IAE9BA,wDAAA,IAAA6Z,yCAAA,iBAcM;IACN7Z,wDAAA,IAAA8Z,yCAAA,iBAEM;IACN9Z,wDAAA,IAAA+Z,yCAAA,iBAGM;IACR/Z,0DAAA,EAAM;IAGRA,4DAAA,aAA+B;IAC7BA,wDAAA,KAAAga,6CAAA,oBAOS;IACXha,0DAAA,EAAM;;;;IA3CSA,wDAAA,cAAAsH,MAAA,CAAA2S,IAAA,CAAkB;IASvBja,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAsH,MAAA,CAAA4S,WAAA,CAAiB;IAejBla,uDAAA,GAAmB;IAAnBA,wDAAA,SAAAsH,MAAA,CAAA6S,aAAA,CAAmB;IAGnBna,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAsH,MAAA,CAAA8S,WAAA,CAAiB;IAStBpa,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAsH,MAAA,CAAA0R,eAAA,CAAAjX,MAAA,MAAiC;;;ADEpC,MAAOsY,mBAAoB,SAAQnc,+DAAa;EAuBpDK,YACU+b,YAAkC,EAClC/M,cAA8B,EAC9B6G,EAAqB,EACrB5V,KAAuC,EACvC+b,kBAAsC;IAE9C,KAAK,EAAE;IANC,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAA/M,cAAc,GAAdA,cAAc;IACd,KAAA6G,EAAE,GAAFA,EAAE;IACF,KAAA5V,KAAK,GAALA,KAAK;IACL,KAAA+b,kBAAkB,GAAlBA,kBAAkB;IA3BnB,KAAAH,WAAW,GAAY,KAAK;IAC5B,KAAAD,aAAa,GAAY,IAAI;IAC7B,KAAAD,WAAW,GAAY,KAAK;IAC3B,KAAAvM,OAAO,GAAG,IAAIiJ,uDAAY,EAAiB;IAO7C,KAAA4D,mBAAmB,GAAa,EAAE;IAK1C,KAAAC,qBAAqB,GAAa,CAChChC,mEAAiB,CAACiC,UAAU,EAC5BjC,mEAAiB,CAACkC,KAAK,EACvBlC,mEAAiB,CAACmC,SAAS,EAC3BnC,mEAAiB,CAACoC,GAAG,CACtB;EAUD;EAEAjc,QAAQA,CAAA;IACN,IAAI,CAACkc,WAAW,EAAE;IAElB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACR,kBAAkB,CAACS,yBAAyB,CAAC1b,SAAS,CAAEwG,GAAa,IAAI;MACpG,IAAI,CAAC2U,qBAAqB,GAAG3U,GAAG;MAChC,IAAI,CAAC0T,UAAU,EAAE;IACnB,CAAC,CAAC;IAEF,IAAI,CAACxR,YAAY,GAAG,IAAI,CAACxJ,KAAK,CAACa,IAAI,CAACpB,mDAAM,CAACI,0FAAoB,CAAC,CAAC,CAACiB,SAAS,CAAEC,KAAmB,IAAI;MAClG,IAAI,CAAC0b,yBAAyB,CAAC1b,KAAK,CAAC;IACvC,CAAC,CAAC;EACJ;EAEA6F,WAAWA,CAAA;IACT,IAAI,CAAC4C,YAAY,EAAEjI,WAAW,EAAE;EAClC;EAEAkb,yBAAyBA,CAAC1b,KAAmB;IAC3C,MAAM2b,kBAAkB,GAAG3b,KAAK,EAAEC,QAAQ,EAAEiV,OAAO,EAAE1S,MAAM,GAAG,CAAC;IAC/D,IAAImZ,kBAAkB,EAAE;MACtB,IAAI,CAACnT,iBAAiB,GAAGiQ,OAAO,CAACzY,KAAK,CAACC,QAAQ,CAACI,WAAW,KAAKxB,kEAAgB,CAAC8J,OAAO,CAAC;MACzF,IAAI,CAACzI,kBAAkB,GAAGF,KAAK,CAACC,QAAQ,CAACE,SAAS;MAClD,IAAI,CAACyb,kBAAkB,GAAG5b,KAAK,CAACC,QAAQ,CAACiV,OAAO,CAAC1S,MAAM,GAAG,CAAC;;EAE/D;EAEA,IAAI+W,OAAOA,CAAA;IACT,OAAO,IAAI,CAACmB,IAAI,CAACjL,GAAG,CAAC,SAAS,CAAC;EACjC;EAEA,IAAIoM,SAASA,CAAA;IACX,OAAO,IAAI,CAACnB,IAAI,CAACjL,GAAG,CAAC,WAAW,CAAC;EACnC;EAEA,IAAImK,OAAOA,CAAA;IACT,OAAO,IAAI,CAACc,IAAI,CAACjL,GAAG,CAAC,SAAS,CAAC;EACjC;EAEA;EACQ8L,WAAWA,CAAA;IACjB,IAAIO,qBAAqB,GAAGjO,mCAAM,EAAE,CAACU,MAAM,EAAE;IAE7C,IAAI,IAAI,CAACsM,WAAW,EAAE;MACpBiB,qBAAqB,GAAGjO,mCAAM,EAAE,CAACkO,OAAO,CAAC,OAAO,CAAC,CAACxN,MAAM,EAAE;MAC1D,IAAI,CAACoL,OAAO,GAAG,IAAI,CAACqC,UAAU,CAACF,qBAAqB,CAAC;;IAEvD,IAAI,CAACpB,IAAI,GAAG,IAAI9M,qDAAS,CAAC;MACxB2L,OAAO,EAAE,IAAI5L,uDAAW,EAAE;MAC1BkO,SAAS,EAAE,IAAIlO,uDAAW,CAACmO,qBAAqB,EAAE,CAAC9C,sDAAU,CAACiD,QAAQ,CAAC,CAAC;MACxErC,OAAO,EAAE,IAAIjM,uDAAW,CAACE,mCAAM,EAAE,CAACqO,KAAK,CAAC,OAAO,CAAC,CAAC3N,MAAM,EAAE,EAAE,CAACyK,sDAAU,CAACiD,QAAQ,CAAC;KACjF,CAAC;IAEF,IAAI,CAACJ,SAAS,CAACtM,YAAY,CAACxP,SAAS,CAACwG,GAAG,IAAG;MAC1C,IAAI,CAACoT,OAAO,GAAG,IAAI,CAACqC,UAAU,CAAC,IAAI,CAACH,SAAS,CAACM,KAAK,CAAC;IACtD,CAAC,CAAC;IAEF,IAAI,CAAC5C,OAAO,CAAChK,YAAY,CAACxP,SAAS,CAACyP,GAAG,IAAG;MACxC,IAAI,CAAC4M,0BAA0B,CAAC5M,GAAG,CAAC;IACtC,CAAC,CAAC;IAEF,IAAI,CAAC6M,SAAS,EAAE;EAClB;EAEA;EACAhC,qBAAqBA,CAACiC,UAAoB;IACxC,IAAI,CAAC7C,eAAe,GAAG6C,UAAU;IACjC,IAAI,CAACD,SAAS,EAAE;EAClB;EAEA;EACQD,0BAA0BA,CAAC7C,OAAiB;IAClDgD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACnD,OAAO,CAAC,CAAC;EAC/D;EAEQ8C,SAASA,CAAA;IACf,MAAMM,iBAAiB,GAAG,IAAI,CAAClD,eAAe,EAAEjX,MAAM,KAAK,CAAC;IAC5D,IAAI,CAAC,IAAI,CAACmY,WAAW,IAAI,CAACgC,iBAAiB,EAAE;MAC3C,IAAI,CAACzS,SAAS,GAAG,IAAI;MACrB;;IAGF,IAAI,CAAC8D,cAAc,CAAC4O,KAAK,EAAE;IAC3B,MAAMC,gBAAgB,GAAGC,QAAQ,CAAC,IAAI,CAACrD,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACsB,YAAY,CAACpP,qBAAqB,CAACkR,gBAAgB,EAAE,KAAK,CAAC,CAAC9c,SAAS,CAAC;MACzEoC,IAAI,EAAG0J,QAAqB,IAAI;QAC9B,IAAI,CAACkR,wBAAwB,CAAClR,QAAQ,CAAC;QACvC,IAAI,CAACmC,cAAc,CAACgP,IAAI,EAAE;MAC5B,CAAC;MACDnW,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACgP,IAAI,EAAE;QAC1B,IAAI,CAAClW,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAkW,wBAAwBA,CAAClR,QAAqB;IAC5C,IAAI,CAAC3B,SAAS,GAAG2B,QAAQ;IACzB,IAAI,CAACoP,mBAAmB,GAAG,IAAI,CAACgC,mBAAmB,EAAE;IACrD,IAAI,CAAC1D,OAAO,CAAC2D,QAAQ,CAAC,IAAI,CAACjC,mBAAmB,CAAC;IAC/C,IAAI,CAACpG,EAAE,CAACU,YAAY,EAAE;EACxB;EAEA0H,mBAAmBA,CAAA;IACjB,IAAIE,eAAe,GAAaV,IAAI,CAACW,KAAK,CAACb,YAAY,CAACc,OAAO,CAAC,cAAc,CAAC,CAAC;IAChF,MAAMC,uBAAuB,GAAGH,eAAe,IAAI,IAAI,IAAIA,eAAe,CAAC3a,MAAM,GAAG,CAAC;IACrF,MAAM+a,eAAe,GAAG,CAAC,IAAI,CAAC3B,kBAAkB,IAAI0B,uBAAuB;IAE3E,OAAOC,eAAe,GAAGJ,eAAe,GAAG,IAAI,CAACK,qBAAqB,CAAC,IAAI,CAACtT,SAAS,CAAC;EACvF;EAEA+P,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACS,IAAI,CAAC+C,KAAK,IAAI,IAAI,CAAChE,eAAe,EAAEjX,MAAM,IAAI,CAAC,EAAE;MACzD;;IAEF,MAAM4L,OAAO,GAAkB,IAAI,CAACsP,gBAAgB,EAAE;IACtD,IAAI,CAACtP,OAAO,CAACgK,IAAI,CAAChK,OAAO,CAAC;EAC5B;EAEAsP,gBAAgBA,CAAA;IACd,MAAMtP,OAAO,GAAG,IAAI6K,+DAAa,EAAE;IACnC7K,OAAO,CAACuP,UAAU,GAAG,IAAI,CAACzd,kBAAkB;IAC5CkO,OAAO,CAACwP,UAAU,GAAG,IAAI,CAACnE,eAAe,CAAC/T,QAAQ,EAAE;IACpD0I,OAAO,CAACgH,QAAQ,GAAG,IAAI,CAACyI,0BAA0B,EAAE;IACpDzP,OAAO,CAACE,IAAI,GAAG6K,sEAAqB,CAAC,IAAI,CAAC0C,SAAS,CAACM,KAAK,CAAC;IAC1D/N,OAAO,CAAC0P,OAAO,GAAG3E,sEAAqB,CAAC,IAAI,CAACS,OAAO,CAACuC,KAAK,CAAC;IAC3D/N,OAAO,CAAC2P,eAAe,GAAG,IAAI,CAACvV,iBAAiB,GAAG,IAAI,CAAC0S,qBAAqB,GAAG,IAAI;IACpF,OAAO9M,OAAO;EAChB;EAEAyP,0BAA0BA,CAAA;IACxB,MAAMG,WAAW,GAAG,IAAI,CAACvE,eAAe,CAACjX,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC+W,OAAO,CAAC4C,KAAK,EAAE3Z,MAAM,GAAG,CAAC;IACvF,OAAOwb,WAAW,GAAG,IAAI,CAACzE,OAAO,CAAC4C,KAAK,CAACzW,QAAQ,EAAE,GAAG,IAAI;EAC3D;EAEA4T,gBAAgBA,CAAA;IACd,MAAM2E,wBAAwB,GAAG,IAAI,CAAC1E,OAAO,CAAC4C,KAAK,CAAC3Z,MAAM,IAAI,CAAC;IAC/D,IAAI0b,MAAM,GAAGD,wBAAwB,GAAG,IAAI,CAACT,qBAAqB,CAAC,IAAI,CAACtT,SAAS,CAAC,GAAG,EAAE;IACvF,IAAI,CAACqP,OAAO,CAAC2D,QAAQ,CAACgB,MAAM,CAAC;EAC/B;EAEAV,qBAAqBA,CAACW,SAAsB;IAC1C,OAAOA,SAAS,CAACrS,OAAO,CAACqB,GAAG,CAACiR,CAAC,IAAIA,CAAC,CAAC1T,OAAO,CAAC;EAC9C;EAEAsR,UAAUA,CAACH,SAAe;IACxB,OAAOhO,mCAAM,CAACgO,SAAS,CAAC,CAACwC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC/P,MAAM,EAAE;EACtE;;;uBAnLWuM,mBAAmB,EAAAra,+DAAA,CAAAE,wEAAA,GAAAF,+DAAA,CAAAE,kEAAA,GAAAF,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAI,8CAAA,GAAAJ,+DAAA,CAAAE,sEAAA;IAAA;EAAA;;;YAAnBma,mBAAmB;MAAA/Z,SAAA;MAAAmG,MAAA;QAAA2T,WAAA;QAAAD,aAAA;QAAAD,WAAA;MAAA;MAAAhC,OAAA;QAAAvK,OAAA;MAAA;MAAApN,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAod,6BAAAld,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtChCb,wDAAA,IAAAge,mCAAA,mBA6CO;;;UA7CAhe,wDAAA,SAAAc,GAAA,CAAAmZ,IAAA,CAAU;;;;;;;;;;;;;;;;;;;;;;;;ACA8B;;;;;ICIzCja,4DAAA,WAAkC;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAO;;;;IAAlCA,uDAAA,GAA2B;IAA3BA,gEAAA,OAAAsH,MAAA,CAAAyG,mBAAA,KAA2B;;;;;IAC7D/N,4DAAA,WAA2B;IAACA,oDAAA,GAA+C;;IAAAA,0DAAA,EAAO;;;;IAAtDA,uDAAA,GAA+C;IAA/CA,gEAAA,QAAAA,yDAAA,OAAAqE,MAAA,CAAAuJ,YAAA,yBAA+C;;;;;IAG7E5N,4DAAA,WAAiC;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAI;;;;;IAEjDA,4DAAA,aAA0D;IACxBA,oDAAA,GAAuB;IAAAA,0DAAA,EAAI;;;;IAA3BA,uDAAA,GAAuB;IAAvBA,gEAAA,KAAAqK,MAAA,CAAA6T,UAAA,YAAuB;;;ADArD,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAA7d,SAAA;MAAAmG,MAAA;QAAA4L,QAAA;QAAAuD,KAAA;QAAAhI,YAAA;QAAAG,mBAAA;QAAAmQ,UAAA;MAAA;MAAAE,UAAA;MAAA7d,QAAA,GAAAP,iEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA2d,gCAAAzd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCb,4DAAA,aAAiB;UAGXA,oDAAA,GACA;UAAAA,wDAAA,IAAAue,sCAAA,kBAAoE;UACpEve,wDAAA,IAAAwe,sCAAA,kBAAkF;UACpFxe,0DAAA,EAAI;UAEJA,wDAAA,IAAAye,mCAAA,eAA+C;UACjDze,0DAAA,EAAM;UACNA,wDAAA,IAAA0e,qCAAA,iBAEM;UACR1e,0DAAA,EAAM;;;UAVAA,uDAAA,GACA;UADAA,gEAAA,MAAAc,GAAA,CAAA8U,KAAA,MACA;UAAO5V,uDAAA,GAAyB;UAAzBA,wDAAA,SAAAc,GAAA,CAAAiN,mBAAA,CAAyB;UACzB/N,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAc,GAAA,CAAA8M,YAAA,CAAkB;UAGvB5N,uDAAA,GAAc;UAAdA,wDAAA,SAAAc,GAAA,CAAAuR,QAAA,CAAc;UAEdrS,uDAAA,GAAoC;UAApCA,wDAAA,SAAAc,GAAA,CAAAod,UAAA,IAAApd,GAAA,CAAAod,UAAA,OAAoC;;;qBDFhCD,yDAAY,EAAA/d,iDAAA,EAAAA,qDAAA;MAAA2e,MAAA;MAAAC,eAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;AEPqC;AAEoB;AACxB;;;;;;;;;ICFrD9e,4DAAA,aAAmD;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;;;;;IAChEA,4DAAA,aAAuC;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,gEAAA,KAAAmf,WAAA,CAAArc,QAAA,OAAwB;;;;;IAI/D9C,4DAAA,aAA2D;IAAAA,oDAAA,GAA0B;IAAAA,0DAAA,EAAK;;;;IAA/BA,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAqK,MAAA,CAAAY,YAAA,CAAAqE,OAAA,CAA0B;;;;;IACrFtP,4DAAA,aAAuC;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAK;;;;IAAvBA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAof,WAAA,CAAAtV,IAAA,CAAkB;;;;;IAIzD9J,4DAAA,aAAsC;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;;;;;IAElDA,4DAAA,WAAqD;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAO;;;;IAAhCA,uDAAA,GAAyB;IAAzBA,gEAAA,KAAAqf,UAAA,CAAAC,UAAA,OAAyB;;;;;IADhFtf,4DAAA,aAAuC;IACrCA,wDAAA,IAAAuf,8DAAA,mBAAqF;IACvFvf,0DAAA,EAAK;;;;;IADsBA,uDAAA,GAA0B;IAA1BA,wDAAA,YAAAsR,MAAA,CAAAkO,cAAA,CAAAC,WAAA,EAA0B;;;;;IAKrDzf,4DAAA,aAAsC;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;;;;;IACpDA,4DAAA,aAAuC;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA0f,WAAA,CAAAC,UAAA,CAAwB;;;;;IAI/D3f,4DAAA,aAA+D;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAGtEA,4DAAA,eAA8C;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAW;;;;;IAC5EA,4DAAA,eAA8C;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAW;;;;;IAH9EA,4DAAA,aAAuC;IAEnCA,wDAAA,IAAA4f,mEAAA,uBAA4E;IAC5E5f,wDAAA,IAAA6f,mEAAA,uBAA0E;IAC5E7f,0DAAA,EAAS;;;;;IAFIA,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA8f,OAAA,CAAAC,eAAA,KAAAC,WAAA,CAAiC;IACjChgB,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA8f,OAAA,CAAAC,eAAA,KAAAC,WAAA,CAAiC;;;;;IAOhDhgB,4DAAA,aAA+D;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAC1EA,4DAAA,aAA0F;IAEtFA,uDAAA,kCAQ2B;IAC7BA,0DAAA,EAAM;;;;;IAX+BA,yDAAA,YAAAoS,OAAA,CAAA8N,0BAAA,CAAAne,MAAA,CAAkD;IACxD/B,uDAAA,GAAuE;IAAvEA,wDAAA,kBAAAmgB,WAAA,IAAA/N,OAAA,CAAA2N,eAAA,4BAAuE;IAGlG/f,uDAAA,GAA4C;IAA5CA,wDAAA,iBAAAoS,OAAA,CAAAvN,YAAA,CAAAvD,YAAA,GAA4C,eAAA6e,WAAA,CAAAza,UAAA,mBAAA0M,OAAA,CAAA3M,aAAA,cAAA2M,OAAA,CAAAxM,QAAA,cAAAua,WAAA,CAAAR,UAAA,gBAAAQ,WAAA,CAAAC,UAAA;;;;;IAWpDpgB,uDAAA,aAA4D;;;;;;;;;;;IAC5DA,4DAAA,aAMC;IADCA,wDAAA,mBAAAqgB,6EAAA;MAAA,MAAAC,WAAA,GAAAtgB,2DAAA,CAAAugB,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1gB,2DAAA;MAAA,OAASA,yDAAA,CAAA0gB,OAAA,CAAAC,YAAA,CAAAH,WAAA,CAAqB;IAAA,EAAC;IAChCxgB,0DAAA,EAAK;;;;;IAFJA,wDAAA,YAAAA,6DAAA,IAAA+G,GAAA,EAAA8Z,OAAA,CAAAd,eAAA,KAAAS,WAAA,EAAsE;;;;;IAKxExgB,uDAAA,aAAiG;;;;;;;;IA7DnGA,4DAAA,eAAiG;IAC/FA,qEAAA,MAAsC;IACpCA,wDAAA,IAAA8gB,uDAAA,gBAAgE;IAChE9gB,wDAAA,IAAA+gB,uDAAA,gBAAoE;IACtE/gB,mEAAA,EAAe;IAEfA,qEAAA,MAAsC;IACpCA,wDAAA,IAAAghB,uDAAA,gBAA0F;IAC1FhhB,wDAAA,IAAAihB,uDAAA,gBAA8D;IAChEjhB,mEAAA,EAAe;IAEfA,qEAAA,MAAuC;IACrCA,wDAAA,IAAAkhB,uDAAA,gBAAoD;IACpDlhB,wDAAA,IAAAmhB,uDAAA,gBAEK;IACPnhB,mEAAA,EAAe;IAEfA,qEAAA,OAAsC;IACpCA,wDAAA,KAAAohB,wDAAA,gBAAoD;IACpDphB,wDAAA,KAAAqhB,wDAAA,gBAAoE;IACtErhB,mEAAA,EAAe;IAEfA,qEAAA,QAAyC;IACvCA,wDAAA,KAAAshB,wDAAA,iBAA0E;IAC1EthB,wDAAA,KAAAuhB,wDAAA,gBAKK;IACPvhB,mEAAA,EAAe;IAGfA,qEAAA,QAA4C;IAC1CA,wDAAA,KAAAwhB,wDAAA,iBAA0E;IAC1ExhB,wDAAA,KAAAyhB,wDAAA,gBAYK;IACPzhB,mEAAA,EAAe;IAEfA,wDAAA,KAAA0hB,wDAAA,iBAA4D;IAC5D1hB,wDAAA,KAAA2hB,wDAAA,iBAMM;IAGN3hB,wDAAA,KAAA4hB,wDAAA,iBAAiG;IACnG5hB,0DAAA,EAAQ;;;;IA9D4BA,wDAAA,eAAAsH,MAAA,CAAAhD,UAAA,CAAyB;IAmDvCtE,uDAAA,IAAiC;IAAjCA,wDAAA,oBAAAsH,MAAA,CAAA/C,gBAAA,CAAiC;IAG1BvE,uDAAA,GAAyB;IAAzBA,wDAAA,qBAAAsH,MAAA,CAAA/C,gBAAA,CAAyB;IAOnBvE,uDAAA,GAA2B;IAA3BA,wDAAA,qBAAAA,6DAAA,IAAAgH,GAAA,EAA2B;;;ADpD9D,MAAMxC,QAAQ,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC;AAc3E,MAAOqd,iCAAiC;EAa5CtjB,YAAoBujB,sBAA8C;IAA9C,KAAAA,sBAAsB,GAAtBA,sBAAsB;IAR1C,KAAAvd,gBAAgB,GAAGC,QAAQ;IAC3B,KAAA0b,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAAC3b,gBAAgB,EAAE,QAAQ,CAAC;IACjE,KAAAD,UAAU,GAAG,IAAIjC,uEAAkB,EAAY;IAG/C;IACA,KAAAwC,YAAY,GAAoB,IAAI7D,yCAAO,EAAU;EAEgB;EAErEpC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACqM,YAAY,EAAE;MACrB,IAAI,CAAC3G,UAAU,CAACe,IAAI,GAAG,IAAI,CAAC4F,YAAY,CAAC2B,IAAI;;IAG/C,IAAI,CAACmV,0BAA0B,GAAG,IAAI,CAACD,sBAAsB,CAACtgB,uBAAuB,CAAClC,SAAS,CAAC,MAAK;MACnG,IAAI,CAACygB,eAAe,GAAG,IAAI;MAC3B,IAAI,CAAC+B,sBAAsB,CAACngB,2BAA2B,EAAE;IAC3D,CAAC,CAAC;EACJ;EAEAqgB,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,EAAEhX,YAAY,EAAEiX,YAAY,EAAE;MACvC,IAAI,CAAC5d,UAAU,GAAG2d,OAAO,CAAChX,YAAY,CAACiX,YAAY,CAACtV,IAAI;;EAE5D;EAEAxH,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC2c,0BAA0B,EAAE;MACnC,IAAI,CAACA,0BAA0B,CAAChiB,WAAW,EAAE;;EAEjD;EAEA;;;;EAIA4gB,YAAYA,CAACwB,OAAiB;IAC5B,IAAI,CAACpC,eAAe,GAAG,IAAI,CAACA,eAAe,KAAKoC,OAAO,GAAG,IAAI,GAAGA,OAAO;IACxE,IAAI,IAAI,CAACpC,eAAe,EAAE;MACxB;MACA,IAAIhb,QAAQ,GAAG,IAAI,CAACgb,eAAe,CAACra,UAAU,CAACT,QAAQ,EAAE,GAAG,IAAI,CAAC8a,eAAe,CAACJ,UAAU;MAC3F,IAAI,CAAC9a,YAAY,CAACnD,IAAI,CAACqD,QAAQ,CAAC;;EAEpC;EAEA;;;;EAIAya,cAAcA,CAAC5S,IAAc;IAC3B,OAAOA,IAAI,EAAEwV,OAAO,CAAC,CAAC,CAAC,EAAEC,UAAU,EAAEtgB,MAAM,GAAG,CAAC,GAAG6K,IAAI,CAACwV,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU,GAAG,EAAE;EACnF;;;uBAzDWR,iCAAiC,EAAA7hB,+DAAA,CAAAE,wGAAA;IAAA;EAAA;;;YAAjC2hB,iCAAiC;MAAAvhB,SAAA;MAAAmG,MAAA;QAAAwE,YAAA;QAAAxF,aAAA;QAAAG,QAAA;MAAA;MAAArF,QAAA,GAAAP,kEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA4hB,2CAAA1hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB9Cb,wDAAA,IAAAwiB,kDAAA,oBA8DQ;;;UA9DAxiB,wDAAA,SAAAc,GAAA,CAAAwD,UAAA,CAAgB;;;;;;mBDeV,CACV4a,4DAAO,CAAC,cAAc,EAAE,CACtB3f,0DAAK,CAAC,WAAW,EAAEyf,0DAAK,CAAC;UAAEyD,MAAM,EAAE,KAAK;UAAEC,SAAS,EAAE;QAAG,CAAE,CAAC,CAAC,EAC5DnjB,0DAAK,CAAC,UAAU,EAAEyf,0DAAK,CAAC;UAAEyD,MAAM,EAAE;QAAG,CAAE,CAAC,CAAC,EACzCxD,+DAAU,CAAC,wBAAwB,EAAEF,4DAAO,CAAC,sCAAsC,CAAC,CAAC,CACtF,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AEpB0D;;;;;;;;ICO3D/e,uDAAA,qCAK+B;;;;;IAH7BA,wDAAA,iBAAA2iB,WAAA,CAAyB,kBAAArb,MAAA,CAAA7B,aAAA,cAAA6B,MAAA,CAAA1B,QAAA;;;;;IAM3B5F,uDAAA,aAA+D;;;ADL3D,MAAO4iB,+BAA+B;EAU1CrkB,YAAA;IAFA,KAAA+F,UAAU,GAAG,IAAIjC,uEAAkB,EAAY;EAEhC;EAEfzD,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC6G,aAAa,IAAI,IAAI,CAACG,QAAQ,EAAE;MACvC,IAAI,CAACH,aAAa,CAACE,QAAQ,GAAG,IAAI,CAACC,QAAQ;;EAE/C;EAEAoc,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,EAAExc,aAAa,EAAEyc,YAAY,EAAE;MACxC,IAAI,CAACzc,aAAa,CAACE,QAAQ,GAAG,IAAI,CAACC,QAAQ;;EAE/C;EAEAR,WAAWA,CAAA,GAAU;;;uBAxBVwd,+BAA+B;IAAA;EAAA;;;YAA/BA,+BAA+B;MAAAtiB,SAAA;MAAAmG,MAAA;QAAAb,QAAA;QAAAgI,YAAA;QAAAG,mBAAA;QAAA8U,cAAA;QAAApd,aAAA;QAAAqd,SAAA;MAAA;MAAAviB,QAAA,GAAAP,kEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAoiB,yCAAAliB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5Cb,4DAAA,aAAgC;UAC9BA,uDAAA,wBAKkB;;UAElBA,wDAAA,IAAAgjB,sEAAA,yCAK+B;UAG/BhjB,wDAAA,IAAAijB,8CAAA,iBAA+D;UACjEjjB,0DAAA,EAAM;;;UAfFA,uDAAA,GAAqC;UAArCA,wDAAA,UAAAA,yDAAA,OAAAc,GAAA,CAAA8E,QAAA,EAAqC,wBAAA9E,GAAA,CAAAiN,mBAAA,kBAAAjN,GAAA,CAAA8M,YAAA,eAAA9M,GAAA,CAAA+hB,cAAA,kBAAA/hB,GAAA,CAAA+hB,cAAA,CAAA9gB,MAAA;UAOhB/B,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAc,GAAA,CAAA+hB,cAAA,CAAiB;UAOlC7iB,uDAAA,GAAe;UAAfA,wDAAA,SAAAc,GAAA,CAAAgiB,SAAA,CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEJjB9iB,4DAAA,aAAoD;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAO/DA,4DAAA,aAA6C;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAkjB,OAAA,CAAAC,aAAA,CAAwB;;;;;IAEnEnjB,4DAAA,WAAkD;IAAAA,oDAAA,GAAyB;IAAAA,0DAAA,EAAO;;;;IAAhCA,uDAAA,GAAyB;IAAzBA,gEAAA,KAAAojB,SAAA,CAAA9D,UAAA,OAAyB;;;;;IAL/Etf,4DAAA,SAAuC;IAClBA,oDAAA,GAAqB;IAAAA,0DAAA,EAAK;IAC7CA,4DAAA,aAAmB;IAAAA,oDAAA,GAAe;IAAAA,0DAAA,EAAK;IACvCA,wDAAA,IAAAqjB,qDAAA,iBAA0E;IAC1ErjB,4DAAA,aAAmB;IACjBA,wDAAA,IAAAsjB,uDAAA,kBAAkF;IACpFtjB,0DAAA,EAAK;IACLA,4DAAA,aAAmB;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAK;;;;;IAN1BA,uDAAA,GAAqB;IAArBA,gEAAA,KAAAkjB,OAAA,CAAApgB,QAAA,OAAqB;IACrB9C,uDAAA,GAAe;IAAfA,+DAAA,CAAAkjB,OAAA,CAAApZ,IAAA,CAAe;IAC7B9J,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAqK,MAAA,CAAAtC,iBAAA,CAAuB;IAED/H,uDAAA,GAAuB;IAAvBA,wDAAA,YAAAqK,MAAA,CAAAmV,cAAA,CAAA0D,OAAA,EAAuB;IAE/BljB,uDAAA,GAAqB;IAArBA,+DAAA,CAAAkjB,OAAA,CAAAvD,UAAA,CAAqB;;;;;IAf5C3f,4DAAA,eAA8D;IAE9BA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;IACzCA,4DAAA,YAA4B;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAK;IACvDA,wDAAA,IAAAujB,+CAAA,gBAA+D;IAC/DvjB,4DAAA,YAA6B;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;IAC3CA,4DAAA,YAA4B;IAAAA,oDAAA,iBAAS;IAAAA,0DAAA,EAAK;IAE5CA,wDAAA,KAAAwjB,gDAAA,iBAQK;IACPxjB,0DAAA,EAAQ;;;;;IAdwBA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAyjB,WAAA,CAAAnU,OAAA,CAAsB;IAC7CtP,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsH,MAAA,CAAAS,iBAAA,CAAuB;IAIT/H,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAyjB,WAAA,CAAA7W,IAAA,CAAgB;;;ADNnC,MAAO8W,yBAAyB;EAOpCnlB,YAAA;IAFS,KAAAwJ,iBAAiB,GAAY,KAAK;EAE5B;EAEfnJ,QAAQA,CAAA,GAAU;EAElB;;;;EAIA4gB,cAAcA,CAAC5S,IAAc;IAC3B,OAAOA,IAAI,EAAEwV,OAAO,CAAC,CAAC,CAAC,EAAEC,UAAU,EAAEtgB,MAAM,GAAG,CAAC,GAAG6K,IAAI,CAACwV,OAAO,CAAC,CAAC,CAAC,CAACC,UAAU,GAAG,EAAE;EACnF;;;uBAjBWqB,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAApjB,SAAA;MAAAmG,MAAA;QAAAb,QAAA;QAAAgI,YAAA;QAAAG,mBAAA;QAAA8U,cAAA;QAAA9a,iBAAA;MAAA;MAAAtH,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAgjB,mCAAA9iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVtCb,4DAAA,aAA2B;UACzBA,uDAAA,wBAKkB;;UAElBA,wDAAA,IAAA4jB,0CAAA,oBAiBQ;UACV5jB,0DAAA,EAAM;;;UAxBFA,uDAAA,GAAqC;UAArCA,wDAAA,UAAAA,yDAAA,OAAAc,GAAA,CAAA8E,QAAA,EAAqC,wBAAA9E,GAAA,CAAAiN,mBAAA,kBAAAjN,GAAA,CAAA8M,YAAA,eAAA9M,GAAA,CAAA+hB,cAAA,kBAAA/hB,GAAA,CAAA+hB,cAAA,CAAA9gB,MAAA;UAMX/B,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAc,GAAA,CAAA+hB,cAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPd;AAOjC;AACiG;AAMtB;AACA;;;;;;;;;;;;;ICJzE7iB,4DAAA,aAAmE;IAY3DA,oDAAA,cACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,iBAA2F;IAAnFA,wDAAA,mBAAA6jB,4DAAA;MAAA7jB,2DAAA,CAAA8jB,GAAA;MAAA,MAAA9S,MAAA,GAAAhR,2DAAA;MAAA,OAASA,yDAAA,CAAAgR,MAAA,CAAA+S,0BAAA,EAA4B;IAAA,EAAC;IAC5C/jB,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;IARPA,uDAAA,GAAuB;IAAvBA,wDAAA,wBAAuB;;;;;IAc7BA,uDAAA,oCAQ6B;;;;IAL3BA,wDAAA,iBAAAqE,MAAA,CAAAuJ,YAAA,CAA6B,wBAAAvJ,MAAA,CAAA0J,mBAAA,oBAAA1J,MAAA,CAAA2f,kBAAA,mBAAA3f,MAAA,CAAAoB,aAAA,eAAApB,MAAA,CAAA1F,UAAA,CAAAM,KAAA,IAAAoF,MAAA,CAAA1F,UAAA,CAAAO,KAAA;;;;;IAO/Bc,uDAAA,oCAQ6B;;;;IAL3BA,wDAAA,iBAAA8T,MAAA,CAAAlG,YAAA,CAA6B,wBAAAkG,MAAA,CAAA/F,mBAAA,oBAAA+F,MAAA,CAAAmQ,iBAAA,mBAAAnQ,MAAA,CAAArO,aAAA,eAAAqO,MAAA,CAAAnV,UAAA,CAAAO,KAAA;;;;;IAO/Bc,uDAAA,oCAQ6B;;;;IAL3BA,wDAAA,iBAAAqK,MAAA,CAAAuD,YAAA,CAA6B,wBAAAvD,MAAA,CAAA0D,mBAAA,oBAAA1D,MAAA,CAAA6Z,iBAAA,mBAAA7Z,MAAA,CAAA5E,aAAA;;;ADnC7B,MAAO0e,oBAAqB,SAAQ7lB,mFAAmB;EAoB3DC,YACUgP,cAA8B,EAC9B7I,eAAgC,EACxClG,KAAuC,EAC/BsjB,sBAA8C,EAC5CrjB,cAA8B;IAExC,KAAK,CAACD,KAAK,EAAEC,cAAc,CAAC;IANpB,KAAA8O,cAAc,GAAdA,cAAc;IACd,KAAA7I,eAAe,GAAfA,eAAe;IAEf,KAAAod,sBAAsB,GAAtBA,sBAAsB;IACpB,KAAArjB,cAAc,GAAdA,cAAc;IAnB1B,KAAA2lB,eAAe,GAAW,CAAC;IAC3B,KAAAC,eAAe,GAAW,CAAC;IAC3B,KAAAC,cAAc,GAAW,CAAC;EAoB1B;EAEA1lB,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB,IAAI,CAACM,eAAe,EAAE;IAEtB,IAAI,IAAI,CAACT,eAAe,EAAE;MACxB,IAAI,CAAC2lB,eAAe,GAAG,IAAI,CAACC,cAAc;;IAG5C,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACzC,sBAAsB,CAACzgB,kBAAkB,CAAC/B,SAAS,CAACwG,GAAG,IAAG;MACtF,IAAI,CAAC0e,KAAK,EAAE;IACd,CAAC,CAAC;EACJ;EAEApf,WAAWA,CAAA;IACT,IAAI,IAAI,CAACmf,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACxkB,WAAW,EAAE;;IAEtC,IAAI,CAACD,mBAAmB,EAAE;EAC5B;EAEA4N,oBAAoBA,CAACC,OAAsB;IACzC,IAAI,CAAClI,aAAa,GAAGkI,OAAO;IAC5B,IAAI,CAACyW,eAAe,GAAG,CAAC;IAExB,IAAI,CAACxW,YAAY,GAAGR,mCAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IACjD,IAAI,CAACC,mBAAmB,GAAGJ,OAAO,CAACI,mBAAmB;IAEtD,IAAI,CAAC,IAAI,CAACrP,eAAe,EAAE;MACzB,IAAI,CAACsP,UAAU,CAAC;QAAE,GAAGL,OAAO;QAAEhI,QAAQ,EAAEmL,uDAAY,CAAC7R;MAAK,CAAE,CAAC;MAC7D,IAAI,CAAC+O,UAAU,CAAC;QAAE,GAAGL,OAAO;QAAEhI,QAAQ,EAAEmL,uDAAY,CAAC9R;MAAM,CAAE,CAAC;;IAGhE,IAAI,CAACgP,UAAU,CAAC;MAAE,GAAGL,OAAO;MAAEhI,QAAQ,EAAEmL,uDAAY,CAAC5R;IAAK,CAAE,CAAC;EAC/D;EAEQ8O,UAAUA,CAACL,OAAsB;IACvC,IAAI,CAACJ,cAAc,CAACW,aAAa,EAAE;IAEnC,IAAI,CAACxJ,eAAe,CAAC+f,gCAAgC,CAAC9W,OAAO,CAAC,CAACrO,SAAS,CAAC;MACvEoC,IAAI,EAAG0J,QAAoB,IAAI;QAC7B,QAAQuC,OAAO,CAAChI,QAAQ;UACtB,KAAKmL,uDAAY,CAAC9R,MAAM;YACtB,IAAI,CAAC0lB,oBAAoB,GAAGtZ,QAAQ;YACpC;UAEF,KAAK0F,uDAAY,CAAC7R,KAAK;YACrB,IAAI,CAAC0lB,mBAAmB,GAAGvZ,QAAQ;YACnC;UAEF,KAAK0F,uDAAY,CAAC5R,KAAK;YACrB,IAAI,CAAC0lB,mBAAmB,GAAGxZ,QAAQ;YACnC;UAEF;YACE;;QAEJ,IAAI,CAACyZ,kBAAkB,CAAC,KAAK,CAAC;QAC9B,IAAI,CAACtX,cAAc,CAACc,YAAY,EAAE;MACpC,CAAC;MACDjI,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEAmJ,0BAA0BA,CAACC,UAAoB;IAC7C,IAAI,CAACJ,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACvP,cAAc,CAACmG,OAAO,CAAC8J,KAAK,IAAG;MAClC,IAAIN,UAAU,CAACH,QAAQ,CAACS,KAAK,CAACgV,cAAc,CAAC,EAAE;QAC7C,IAAI,CAAC1V,qBAAqB,CAACjJ,IAAI,CAAC2J,KAAK,CAACpF,YAAY,CAAC;QACnD;;IAEJ,CAAC,CAAC;IACF,IAAI,CAACma,kBAAkB,CAAC,IAAI,CAAC;EAC/B;EAEAvW,oBAAoBA,CAACC,IAAuB;IAC1C,IAAI,CAAC5P,UAAU,GAAG4P,IAAI;IACtB,IAAI,CAAC3M,wBAAwB,EAAE;EACjC;EAEA;;;EAGAijB,kBAAkBA,CAACE,aAAsB;IACvC,IAAI,CAACX,eAAe,EAAE;IACtB,IAAI,IAAI,CAACA,eAAe,IAAI,IAAI,CAACC,eAAe,IAAI,CAACU,aAAa,EAAE;MAClE;;IAEF,IAAI,CAACf,kBAAkB,GAAG,IAAI,CAAC/U,kBAAkB,CAAC,IAAI,CAACyV,oBAAoB,CAAC;IAC5E,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAAChV,kBAAkB,CAAC,IAAI,CAAC0V,mBAAmB,CAAC;IAC1E,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACjV,kBAAkB,CAAC,IAAI,CAAC2V,mBAAmB,CAAC;IAE1E,IAAI,CAAChjB,wBAAwB,EAAE;EACjC;EAEA;;;EAGAA,wBAAwBA,CAAA;IACtB,IAAI,CAACkgB,sBAAsB,CAAC7f,gBAAgB,EAAE;IAC9C,IAAI,CAAC6f,sBAAsB,CAAClgB,wBAAwB,CAAC,IAAI,CAACjD,UAAU,CAACK,MAAM,EAAE,IAAI,CAACglB,kBAAkB,CAAC;IACrG,IAAI,CAAClC,sBAAsB,CAAClgB,wBAAwB,CAAC,IAAI,CAACjD,UAAU,CAACM,KAAK,EAAE,IAAI,CAACglB,iBAAiB,CAAC;IACnG,IAAI,CAACnC,sBAAsB,CAAClgB,wBAAwB,CAAC,IAAI,CAACjD,UAAU,CAACO,KAAK,EAAE,IAAI,CAACglB,iBAAiB,CAAC;EACrG;EAEA;;;;EAIAjV,kBAAkBA,CAACC,IAAgB;IACjC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,EAAE;;IAEX,OAAOA,IAAI,CAACC,MAAM,CAAElJ,EAAY,IAAI;MAClC,OAAO,IAAI,CAACmJ,qBAAqB,CAACC,QAAQ,CAACpJ,EAAE,CAACqJ,OAAO,CAAC;IACxD,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EAEA;;;EAGAyU,0BAA0BA,CAAA;IACxB,IAAI,CAACxW,cAAc,CAAC4O,KAAK,EAAE;IAC3B,IAAI,CAAC2F,sBAAsB,CAAC9f,2BAA2B,EAAE;IACzD,IAAI,CAAC8f,sBAAsB,CAACrgB,YAAY,EAAE;EAC5C;EAEA+iB,KAAKA,CAAA;IACHQ,UAAU,CAAC,MAAK;MACd;MACA;MACA,IAAI,CAACC,QAAQ,CAACC,aAAa,CAACC,KAAK,EAAE;MACnC,IAAI,CAAC5X,cAAc,CAACgP,IAAI,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5N,eAAeA,CAAA;IACb,OACE,IAAI,CAACgW,mBAAmB,EAAE5iB,MAAM,GAAG,CAAC,IACpC,IAAI,CAAC2iB,oBAAoB,EAAE3iB,MAAM,GAAG,CAAC,IACrC,IAAI,CAAC6iB,mBAAmB,EAAE7iB,MAAM,GAAG,CAAC;EAExC;;;uBAvLWoiB,oBAAoB,EAAAnkB,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA,GAAAF,+DAAA,CAAAI,8CAAA,GAAAJ,+DAAA,CAAAsW,wGAAA,GAAAtW,+DAAA,CAAAE,2DAAA;IAAA;EAAA;;;YAApBikB,oBAAoB;MAAA7jB,SAAA;MAAA8kB,SAAA,WAAAC,2BAAAxkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;oEACpBwZ,mFAAmB;;;;;;;;;;;;;;;UCxBhCra,4DAAA,aAA6B;UAIzBA,wDAAA,uBAAAslB,2EAAA9U,MAAA;YAAA,OAAa1P,GAAA,CAAA4M,oBAAA,CAAA8C,MAAA,CAA4B;UAAA,EAAC,4BAAA+U,gFAAA/U,MAAA;YAAA,OACxB1P,GAAA,CAAAyO,0BAAA,CAAAiB,MAAA,CAAkC;UAAA,EADV,4BAAAgV,gFAAAhV,MAAA;YAAA,OAExB1P,GAAA,CAAAwN,oBAAA,CAAAkC,MAAA,CAA4B;UAAA,EAFJ;UAG3CxQ,0DAAA,EAA0B;UAG3BA,uDAAA,aAAkD;UAElDA,wDAAA,IAAAylB,mCAAA,iBAoBM;UAENzlB,4DAAA,aAAuB;UACrBA,wDAAA,IAAA0lB,yDAAA,uCAQ6B;UAE7B1lB,wDAAA,IAAA2lB,yDAAA,uCAQ6B;UAE7B3lB,wDAAA,IAAA4lB,yDAAA,uCAQ6B;UAC/B5lB,0DAAA,EAAM;;;UA9DJA,uDAAA,GAAiC;UAAjCA,wDAAA,mBAAAc,GAAA,CAAAjB,cAAA,CAAiC,iBAAAiB,GAAA,CAAAnB,YAAA;UAU7BK,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAA6N,eAAA,GAAuB;UAwBxB3O,uDAAA,GAA2C;UAA3CA,wDAAA,UAAAc,GAAA,CAAApC,eAAA,IAAAoC,GAAA,CAAAnC,UAAA,CAAAK,MAAA,CAA2C;UAU3CgB,uDAAA,GAA0C;UAA1CA,wDAAA,UAAAc,GAAA,CAAApC,eAAA,IAAAoC,GAAA,CAAAnC,UAAA,CAAAM,KAAA,CAA0C;UAU1Ce,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAc,GAAA,CAAAnC,UAAA,CAAAO,KAAA,CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvDI;AAEjC;AACkF;;;;;;;;;ICmChEc,4DAAA,WAAmC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAO;;;;IAAnCA,uDAAA,GAA4B;IAA5BA,gEAAA,MAAA6lB,OAAA,CAAAlT,eAAA,MAA4B;;;;;IAFjE3S,4DAAA,SAAqC;IACnCA,oDAAA,GACA;IAAAA,wDAAA,IAAA8lB,gEAAA,mBAAsE;IACxE9lB,0DAAA,EAAK;;;;IAFHA,uDAAA,GACA;IADAA,gEAAA,MAAA6lB,OAAA,CAAA/S,QAAA,SAAA+S,OAAA,CAAAna,IAAA,MACA;IAAO1L,uDAAA,GAA0B;IAA1BA,wDAAA,SAAA6lB,OAAA,CAAAlT,eAAA,CAA0B;;;;;IAPzC3S,4DAAA,aAAuD;IACjDA,oDAAA,GAAuB;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,SAAI;IAAAA,oDAAA,GAAuC;;IAAAA,0DAAA,EAAK;IAChDA,4DAAA,SAAI;IAEAA,wDAAA,IAAA+lB,yDAAA,gBAGK;IACP/lB,0DAAA,EAAK;;;;IARHA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAgmB,QAAA,CAAA3S,WAAA,CAAuB;IACvBrT,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAA,yDAAA,OAAAgmB,QAAA,CAAApgB,QAAA,EAAuC;IAGlB5F,uDAAA,GAAc;IAAdA,wDAAA,YAAAgmB,QAAA,CAAA1S,KAAA,CAAc;;;;;IAd7CtT,4DAAA,UAA4C;IACdA,oDAAA,GAAkB;IAAAA,0DAAA,EAAI;IAElDA,4DAAA,eAAsB;IAEKA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;IACpCA,4DAAA,aAAuB;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,aAAuB;IAAAA,oDAAA,qBAAa;IAAAA,0DAAA,EAAK;IAE3CA,wDAAA,KAAAimB,oDAAA,iBAWK;IACPjmB,0DAAA,EAAQ;;;;IApBoBA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAkmB,SAAA,CAAAjS,KAAA,CAAkB;IAQtBjU,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAkmB,SAAA,CAAAhS,MAAA,CAAgB;;;ADjB1C,MAAOiS,+BAAgC,SAAQjoB,wDAAa;EAKhEK,YACU6V,EAAqB,EACrB7G,cAA8B,EAC9B7I,eAAgC;IAExC,KAAK,EAAE;IAJC,KAAA0P,EAAE,GAAFA,EAAE;IACF,KAAA7G,cAAc,GAAdA,cAAc;IACd,KAAA7I,eAAe,GAAfA,eAAe;IAPzB,KAAA4P,eAAe,GAAkB,EAAE;EAUnC;EAEA1V,QAAQA,CAAA,GAAI;EAEZ8V,SAASA,CAAC/G,OAAsB;IAC9B,IAAI,CAACJ,cAAc,CAACW,aAAa,EAAE;IACnC,IAAI,CAACN,YAAY,GAAGR,mCAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IAEjD;IACA,IAAI,CAACpJ,eAAe,CAAC0hB,kCAAkC,CAACzY,OAAO,CAAC,CAACrO,SAAS,CAAC;MACzEoC,IAAI,EAAG0J,QAAuB,IAAI;QAChC,IAAI,CAACkJ,eAAe,GAAGlJ,QAAQ;QAC/B,IAAI,CAAC6F,eAAe,GAAG,CAAC;QAExB,IAAI,IAAI,CAACqD,eAAe,EAAE;UACxB,IAAI,CAACA,eAAe,CAACtO,OAAO,CAAC6O,MAAM,IAAG;YACpC,IAAI,CAAC5D,eAAe,IAAI4D,MAAM,CAACX,MAAM,CAACnS,MAAM;UAC9C,CAAC,CAAC;;QAGJ,IAAI,CAACwL,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAC+F,EAAE,CAACU,YAAY,EAAE;MACxB,CAAC;MACD1O,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;;;uBAvCW+f,+BAA+B,EAAAnmB,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA;IAAA;EAAA;;;YAA/BimB,+BAA+B;MAAA7lB,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA0lB,yCAAAxlB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf5Cb,4DAAA,aAA6B;UAGvBA,oDAAA,wOAGF;UAAAA,0DAAA,EAAI;UAGNA,4DAAA,qBAA2C;UAA9BA,wDAAA,qBAAAsmB,wEAAA9V,MAAA;YAAA,OAAW1P,GAAA,CAAA4T,SAAA,CAAAlE,MAAA,CAAiB;UAAA,EAAC;UAACxQ,0DAAA,EAAc;UAGzDA,uDAAA,aAAkD;UAElDA,4DAAA,aAAuB;UAEnBA,uDAAA,wBAKkB;UAElBA,wDAAA,IAAAumB,8CAAA,kBAsBM;UACRvmB,0DAAA,EAAM;;;UA5BFA,uDAAA,GAA6B;UAA7BA,wDAAA,iBAAAc,GAAA,CAAA8M,YAAA,CAA6B,cAAA9M,GAAA,CAAAwT,eAAA,kBAAAxT,GAAA,CAAAwT,eAAA,CAAAvS,MAAA,uBAAAjB,GAAA,CAAAmQ,eAAA;UAKPjR,uDAAA,GAAkB;UAAlBA,wDAAA,YAAAc,GAAA,CAAAwT,eAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBJ;AAE5C;AAO+B;;;;;;;;;;;ICP7BtU,4DAAA,aAA8C;IACxCA,oDAAA,cAAO;IAAAA,0DAAA,EAAK;IAChBA,uDAAA,oCAI6B;IAC/BA,0DAAA,EAAM;;;;IAJFA,uDAAA,GAA0B;IAA1BA,wDAAA,2BAA0B,qCAAAsH,MAAA,CAAAmf,YAAA;;;;;;IAS9BzmB,4DAAA,cAAyE;IAGfA,wDAAA,mBAAA0mB,6DAAA;MAAA1mB,2DAAA,CAAA2mB,GAAA;MAAA,MAAAtc,MAAA,GAAArK,2DAAA;MAAA,OAASA,yDAAA,CAAAqK,MAAA,CAAAoH,SAAA,EAAW;IAAA,EAAC;IAACzR,oDAAA,UAAG;IAAAA,0DAAA,EAAS;IAExFA,4DAAA,cAAsC;IAQlCA,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;IAJPA,uDAAA,GAAuB;IAAvBA,wDAAA,wBAAuB;;;;;IAoBnBA,4DAAA,aAAuD;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAelEA,4DAAA,aAA4C;IAC1CA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAA4mB,QAAA,CAAAzD,aAAA,MACF;;;;;IAVFnjB,4DAAA,aAAuD;IACnCA,oDAAA,GAAqB;IAAAA,0DAAA,EAAK;IAC5CA,4DAAA,aAAmC;IACjCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,aAAmC;IACjCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,wDAAA,IAAA6mB,gEAAA,iBAEK;IACL7mB,4DAAA,aAAkB;IAChBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;;;;;IAlBaA,uDAAA,GAAqB;IAArBA,gEAAA,KAAA4mB,QAAA,CAAA9jB,QAAA,OAAqB;IACnC9C,uDAAA,GAA8B;IAA9BA,wDAAA,YAAA8mB,MAAA,CAAAC,gBAAA,GAA8B;IAChC/mB,uDAAA,GACF;IADEA,gEAAA,MAAA4mB,QAAA,CAAA9c,IAAA,MACF;IACI9J,uDAAA,GAA8B;IAA9BA,wDAAA,YAAA8mB,MAAA,CAAAC,gBAAA,GAA8B;IAChC/mB,uDAAA,GACF;IADEA,gEAAA,MAAA4mB,QAAA,CAAAI,eAAA,MACF;IACmBhnB,uDAAA,GAAuB;IAAvBA,wDAAA,SAAA8mB,MAAA,CAAA/e,iBAAA,CAAuB;IAIxC/H,uDAAA,GACF;IADEA,gEAAA,MAAA8mB,MAAA,CAAAG,wBAAA,CAAAL,QAAA,CAAAM,QAAA,OACF;IAEElnB,uDAAA,GACF;IADEA,gEAAA,MAAA8mB,MAAA,CAAAG,wBAAA,CAAAL,QAAA,CAAAO,YAAA,OACF;IAEEnnB,uDAAA,GACF;IADEA,gEAAA,MAAA8mB,MAAA,CAAAG,wBAAA,CAAAL,QAAA,CAAAQ,KAAA,OACF;;;;;IAhCNpnB,4DAAA,gBAAqE;IAGlCA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;IAC1CA,4DAAA,aAAoD;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAK;IAC/EA,4DAAA,aAAsD;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;IACpEA,wDAAA,IAAAqnB,0DAAA,iBAAkE;IAClErnB,4DAAA,cAAkB;IAAAA,oDAAA,WAAG;IAAAA,0DAAA,EAAK;IAC1BA,4DAAA,cAAkB;IAAAA,oDAAA,uBAAe;IAAAA,0DAAA,EAAK;IACtCA,4DAAA,cAAkB;IAAAA,oDAAA,sBAAc;IAAAA,0DAAA,EAAK;IAGzCA,4DAAA,aAAO;IACLA,wDAAA,KAAAsnB,2DAAA,kBAoBK;IACPtnB,0DAAA,EAAQ;IACRA,4DAAA,aAAO;IAEqCA,oDAAA,uBAAe;IAAAA,0DAAA,EAAK;IAC5DA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;;;;;IA1CgBA,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAgR,MAAA,CAAA+V,gBAAA,GAA8B;IAAC/mB,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAunB,WAAA,CAAAjY,OAAA,CAAsB;IACnDtP,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAgR,MAAA,CAAA+V,gBAAA,GAA8B;IACvB/mB,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAgR,MAAA,CAAAjJ,iBAAA,CAAuB;IAOlC/H,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAunB,WAAA,CAAAC,KAAA,CAAiB;IAwBhCxnB,uDAAA,GAAmC;IAAnCA,yDAAA,YAAAgR,MAAA,CAAAyW,kBAAA,CAAmC;IAErCznB,uDAAA,GACF;IADEA,gEAAA,MAAAgR,MAAA,CAAAiW,wBAAA,CAAAM,WAAA,CAAAG,QAAA,OACF;IAEE1nB,uDAAA,GACF;IADEA,gEAAA,MAAAgR,MAAA,CAAAiW,wBAAA,CAAAM,WAAA,CAAAI,iBAAA,OACF;IAEE3nB,uDAAA,GACF;IADEA,gEAAA,MAAAgR,MAAA,CAAAiW,wBAAA,CAAAM,WAAA,CAAAK,gBAAA,OACF;;;;;IAWA5nB,uDAAA,aAA4D;;;;;IANlEA,4DAAA,gBAAwC;IAGlCA,uDAAA,aAAgC;IAGhCA,wDAAA,IAAA6nB,0DAAA,iBAA4D;IAC5D7nB,uDAAA,aAAuB;IAGzBA,0DAAA,EAAK;IAEPA,4DAAA,aAAO;IAEqCA,oDAAA,cAAM;IAAAA,0DAAA,EAAK;IACnDA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAkB;IAChBA,oDAAA,IACF;IAAAA,0DAAA,EAAK;;;;IAnBgBA,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAsR,MAAA,CAAAyV,gBAAA,GAA8B;IAC5B/mB,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAsR,MAAA,CAAAyV,gBAAA,GAA8B;IAChD/mB,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsR,MAAA,CAAAvJ,iBAAA,CAAuB;IAQxB/H,uDAAA,GAAmC;IAAnCA,yDAAA,YAAAsR,MAAA,CAAAmW,kBAAA,CAAmC;IAErCznB,uDAAA,GACF;IADEA,gEAAA,MAAAsR,MAAA,CAAA2V,wBAAA,CAAA3V,MAAA,CAAAwW,UAAA,CAAAJ,QAAA,OACF;IAEE1nB,uDAAA,GACF;IADEA,gEAAA,MAAAsR,MAAA,CAAA2V,wBAAA,CAAA3V,MAAA,CAAAwW,UAAA,CAAAH,iBAAA,OACF;IAEE3nB,uDAAA,GACF;IADEA,gEAAA,MAAAsR,MAAA,CAAA2V,wBAAA,CAAA3V,MAAA,CAAAwW,UAAA,CAAAF,gBAAA,OACF;;;;;IA3ER5nB,qEAAA,GAAiC;IAC/BA,wDAAA,IAAA+nB,qDAAA,qBAiDQ;IAER/nB,wDAAA,IAAAgoB,qDAAA,qBA0BQ;IACVhoB,mEAAA,EAAe;;;;IA9EeA,uDAAA,GAAwB;IAAxBA,wDAAA,YAAA8T,MAAA,CAAAgU,UAAA,CAAAG,UAAA,CAAwB;IAmD5CjoB,uDAAA,GAAe;IAAfA,wDAAA,UAAA8T,MAAA,CAAAzB,QAAA,CAAe;;;ADpEzB,MAAO6V,qBAAsB,SAAQhqB,wDAAa;EAOtDK,YACSgP,cAA8B,EAC9B4a,eAAgC,EAChCC,YAAyB,EACxBhU,EAAqB,EACrBC,KAAqB;IAE7B,KAAK,EAAE;IANA,KAAA9G,cAAc,GAAdA,cAAc;IACd,KAAA4a,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAhU,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IAVf,KAAAtM,iBAAiB,GAAY,KAAK;IAClC,KAAA0f,kBAAkB,GAAW,CAAC;IAC9B,KAAApV,QAAQ,GAAY,IAAI;EAWxB;EAEAzT,QAAQA,CAAA;IACN,IAAI,CAACmJ,iBAAiB,GAAG,IAAI,CAACsM,KAAK,CAACG,QAAQ,CAACnP,IAAI,CAAC,aAAa,CAAC;EAClE;EAEAoM,SAASA,CAAA;IACP,IAAIsD,SAAS,GAAoB,EAAE;IAEnC,IAAI,CAAC+S,UAAU,CAACG,UAAU,CAACjiB,OAAO,CAACqiB,CAAC,IAAG;MACrCA,CAAC,CAACb,KAAK,CAACxhB,OAAO,CAACwG,CAAC,IAAG;QAClB,IAAII,IAAI,GAAkB,IAAI4Z,wDAAa,EAAE;QAC7C5Z,IAAI,CAAC0b,QAAQ,GAAGD,CAAC,CAAC/Y,OAAO;QACzB1C,IAAI,CAAC9C,IAAI,GAAG0C,CAAC,CAAC1C,IAAI;QAClB8C,IAAI,CAACwV,OAAO,GAAG5V,CAAC,CAACwa,eAAe;QAChCpa,IAAI,CAACwa,KAAK,GAAG5a,CAAC,CAAC4a,KAAK;QACpBxa,IAAI,CAAC9J,QAAQ,GAAG0J,CAAC,CAAC1J,QAAQ;QAC1B8J,IAAI,CAACua,YAAY,GAAG,IAAI,CAACF,wBAAwB,CAACza,CAAC,CAAC2a,YAAY,CAAC;QACjEva,IAAI,CAACsa,QAAQ,GAAG,IAAI,CAACD,wBAAwB,CAACza,CAAC,CAAC0a,QAAQ,CAAC;QAEzDnS,SAAS,CAAC5O,IAAI,CAACyG,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI2b,SAAS,GAAG,IAAI/B,wDAAa,EAAE;IACnC+B,SAAS,CAACD,QAAQ,GAAG,EAAE;IACvBC,SAAS,CAACze,IAAI,GAAG,OAAO;IACxBye,SAAS,CAACnG,OAAO,GAAG,EAAE;IACtBmG,SAAS,CAACzlB,QAAQ,GAAG,CAAC;IACtBylB,SAAS,CAACrB,QAAQ,GAAG,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAACa,UAAU,CAACJ,QAAQ,CAAC;IAC5Ea,SAAS,CAACpB,YAAY,GAAG,IAAI,CAACF,wBAAwB,CAAC,IAAI,CAACa,UAAU,CAACH,iBAAiB,CAAC;IACzFY,SAAS,CAACnB,KAAK,GAAG,CAAC,IAAI,CAACH,wBAAwB,CAAC,IAAI,CAACa,UAAU,CAACF,gBAAgB,CAAC;IAElF7S,SAAS,CAAC5O,IAAI,CAACoiB,SAAS,CAAC;IAEzB,MAAMjT,OAAO,GAAG;MACdC,cAAc,EAAE,GAAG;MACnBC,YAAY,EAAE,GAAG;MACjBC,gBAAgB,EAAE,GAAG;MACrBC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,KAAK;MAClBC,MAAM,EAAE,IAAI;MACZC,gBAAgB,EAAE;MAClB;KACD;;IAED,MAAMC,WAAW,GAAG,IAAInF,sDAAW,CAACyE,OAAO,CAAC;IAE5CU,WAAW,CAACC,WAAW,CAAClB,SAAS,CAAC;EACpC;EAEOyT,IAAIA,CAAC7a,OAAsB;IAChC,IAAI,CAACJ,cAAc,CAACW,aAAa,EAAE;IAEnC,IAAI,CAACia,eAAe,CAACM,iBAAiB,CAAC9a,OAAO,CAAC,CAACrO,SAAS,CAAC;MACxDoC,IAAI,EAAG0J,QAAoB,IAAI;QAC7B,IAAI,CAAC0c,UAAU,GAAG1c,QAAQ;QAC1B,IAAI,CAACmC,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAACgE,QAAQ,GAAG,CAACjH,QAAQ,EAAE6c,UAAU,EAAElmB,MAAM;QAC7C,IAAI,CAACqS,EAAE,CAACU,YAAY,EAAE;MACxB,CAAC;MACD1O,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC0hB,UAAU,GAAG,IAAI;QACtB,IAAI,CAACzV,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC9E,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA2gB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAChf,iBAAiB,GAAG,OAAO,GAAG,OAAO;EACnD;EAEAkf,wBAAwBA,CAACvL,KAAa;IACpC,IAAIgN,MAAM,CAAChN,KAAK,CAAC,GAAG,CAAC,EAAE;MACrB,OAAO,GAAG,GAAG,IAAI,CAAC0M,YAAY,CAACO,SAAS,CAACjN,KAAK,EAAE,OAAO,CAAC,CAACzW,QAAQ,EAAE;KACpE,MAAM;MACL,OAAO,GAAG;;EAEd;;;uBAlGWijB,qBAAqB,EAAAloB,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA,GAAAF,+DAAA,CAAAI,wDAAA,GAAAJ,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAsW,2DAAA;IAAA;EAAA;;;YAArB4R,qBAAqB;MAAA5nB,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAkoB,+BAAAhoB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBlCb,4DAAA,aAA6B;UACOA,wDAAA,qBAAA8oB,8DAAAtY,MAAA;YAAA,OAAW1P,GAAA,CAAA0nB,IAAA,CAAAhY,MAAA,CAAY;UAAA,EAAC;UAACxQ,0DAAA,EAAc;UAGzEA,wDAAA,IAAA+oB,oCAAA,iBAOM;UAGN/oB,uDAAA,aAAkD;UAElDA,wDAAA,IAAAgpB,oCAAA,iBAiBM;UAENhpB,4DAAA,aAAuB;UAEnBA,uDAAA,wBAA4E;UAE5EA,wDAAA,IAAAipB,6CAAA,0BA+Ee;UACjBjpB,0DAAA,EAAM;;;UAtHKA,uDAAA,GAAoB;UAApBA,wDAAA,qBAAoB;UAGZA,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAAiH,iBAAA,CAAuB;UAYtC/H,uDAAA,GAA6B;UAA7BA,wDAAA,UAAAc,GAAA,CAAAuR,QAAA,IAAAvR,GAAA,CAAAgnB,UAAA,CAA6B;UAqBM9nB,uDAAA,GAAqB;UAArBA,wDAAA,aAAAc,GAAA,CAAAuR,QAAA,CAAqB;UAE3CrS,uDAAA,GAAgB;UAAhBA,wDAAA,SAAAc,GAAA,CAAAgnB,UAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtCJ;AAEjC;AAC6F;AAKlB;;;;;;;;;;ICGzE9nB,4DAAA,aAAmE;IAU3DA,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;IAJPA,uDAAA,GAAuB;IAAvBA,wDAAA,wBAAuB;;;ADH3B,MAAOkpB,2BAA4B,SAAQhrB,wDAAa;EAS5DK,YAAoBgP,cAA8B,EAAU7I,eAAgC;IAC1F,KAAK,EAAE;IADW,KAAA6I,cAAc,GAAdA,cAAc;IAA0B,KAAA7I,eAAe,GAAfA,eAAe;IAH3E,KAAAykB,WAAW,GAAY,KAAK;IAC5B,KAAAC,eAAe,GAAa,EAAE;EAI9B;EAEAxqB,QAAQA,CAAA,GAAI;EAEZyqB,UAAUA,CAAC1b,OAAsB;IAC/B,IAAI,CAACC,YAAY,GAAGR,mCAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IACjD,IAAI,CAACC,mBAAmB,GAAGJ,OAAO,CAACI,mBAAmB;IACtDJ,OAAO,CAAChI,QAAQ,GAAGmL,uDAAY,CAAC5I,OAAO;IAEvC,IAAI,CAACqF,cAAc,CAAC4O,KAAK,EAAE;IAE3B,IAAI,CAACzX,eAAe,CAAC+f,gCAAgC,CAAC9W,OAAO,CAAC,CAACrO,SAAS,CAAC;MACvEoC,IAAI,EAAG0J,QAAoB,IAAI;QAC7B,IAAI,CAACke,qBAAqB,GAAGle,QAAQ;QACrC,IAAI,CAACmC,cAAc,CAACgP,IAAI,EAAE;MAC5B,CAAC;MACDnW,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACgP,IAAI,EAAE;QAC1B,IAAI,CAAClW,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAuI,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC2a,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAACvnB,MAAM,GAAG,CAAC;EAC5E;;;uBApCWmnB,2BAA2B,EAAAlpB,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA;IAAA;EAAA;;;YAA3BgpB,2BAA2B;MAAA5oB,SAAA;MAAA8kB,SAAA,WAAAmE,kCAAA1oB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;oEAC3BwZ,mFAAmB;;;;;;;;;;;;;UCjBhCra,4DAAA,aAA6B;UAC+BA,wDAAA,qBAAAwpB,oEAAAhZ,MAAA;YAAA,OAAW1P,GAAA,CAAAuoB,UAAA,CAAA7Y,MAAA,CAAkB;UAAA,EAAC;UAACxQ,0DAAA,EAAc;UAGvGA,4DAAA,aAAoB;UACiCA,oDAAA,cAAO;UAAAA,0DAAA,EAAK;UAC/DA,uDAAA,mCAAsG;UACxGA,0DAAA,EAAM;UAGNA,uDAAA,aAAkD;UAElDA,wDAAA,IAAAypB,0CAAA,iBAcM;UAENzpB,4DAAA,aAAuB;UAErBA,uDAAA,4BAOqB;UACvBA,0DAAA,EAAM;;;UArCOA,uDAAA,GAAoB;UAApBA,wDAAA,qBAAoB;UAKJA,uDAAA,GAA0B;UAA1BA,wDAAA,2BAA0B;UAMjDA,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAc,GAAA,CAAA6N,eAAA,GAAuB;UAoBzB3O,uDAAA,GAA6B;UAA7BA,wDAAA,iBAAAc,GAAA,CAAA8M,YAAA,CAA6B,wBAAA9M,GAAA,CAAAiN,mBAAA,oBAAAjN,GAAA,CAAAwoB,qBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BnC;AACiE;AAKrB;AAE+B;;;;;;;;ICAnEtpB,4DAAA,aAAsC;IAAAA,oDAAA,SAAE;IAAAA,0DAAA,EAAK;;;;;IAC7CA,4DAAA,aAAuC;IAC7BA,oDAAA,GAAsB;IAAAA,0DAAA,EAAS;;;;IAA/BA,uDAAA,GAAsB;IAAtBA,gEAAA,KAAAmD,WAAA,CAAAymB,OAAA,MAAsB;;;;;IAKhC5pB,4DAAA,aAAsC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;;;;;IAChDA,4DAAA,aAAuC;IAC7BA,oDAAA,GAAuB;IAAAA,0DAAA,EAAS;;;;IAAhCA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAsD,WAAA,CAAAumB,SAAA,CAAuB;;;;;IAKjC7pB,4DAAA,aAAsC;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAK;;;;;IACtDA,4DAAA,aAAuC;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAmf,WAAA,CAAA2K,UAAA,CAAwB;;;;;IAI/D9pB,4DAAA,aAAsC;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAK;;;;;IAC7DA,4DAAA,aAAuC;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAK;;;;IAAhCA,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAof,WAAA,CAAA2K,aAAA,CAA2B;;;;;IAIlE/pB,4DAAA,aAAsC;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAK;;;;;IACrDA,4DAAA,aAAuC;IACrCA,uDAAA,uBAA4E;IAC9EA,0DAAA,EAAK;;;;IADWA,uDAAA,GAA4B;IAA5BA,wDAAA,YAAAyf,WAAA,CAAAuK,QAAA,CAA4B;;;;;IAK5ChqB,uDAAA,aAA2C;;;;;IAC3CA,4DAAA,aAAuC;IACrCA,uDAAA,cAIE;IACJA,0DAAA,EAAK;;;;;IAGPA,uDAAA,aAA4D;;;;;IAC5DA,uDAAA,aAAiE;;;ADxCvE,MAAMwE,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC;AAO5D,MAAOylB,2BAA4B,SAAQP,0DAAe;EAK9DnrB,YACE2rB,oBAA0C,EAC1C3c,cAA8B,EACtB/O,KAAuC;IAE/C,KAAK,CAACgG,QAAQ,EAAE0lB,oBAAoB,EAAE3c,cAAc,CAAC;IAF7C,KAAA/O,KAAK,GAALA,KAAK;EAGf;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACurB,oBAAoB,GAAG,IAAI,CAAC3rB,KAAK,CACnCa,IAAI,CAACpB,mDAAM,CAAC0rB,qFAAe,CAAC,CAAC,CAC7BrqB,SAAS,CAAEqqB,eAAwB,IAAI;MACtC,IAAI,CAACS,cAAc,CAACT,eAAe,CAAC;IACtC,CAAC,CAAC;EACN;EAEAvkB,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC+kB,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACpqB,WAAW,EAAE;;EAE3C;;;uBAzBWkqB,2BAA2B,EAAAjqB,+DAAA,CAAAE,iEAAA,GAAAF,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAI,8CAAA;IAAA;EAAA;;;YAA3B6pB,2BAA2B;MAAA3pB,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA2pB,qCAAAzpB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBxCb,4DAAA,aAA6B;UAIiBA,oDAAA,uBAAgB;UAAAA,0DAAA,EAAI;UAKhEA,4DAAA,aAAoB;UAEhBA,qEAAA,MAAgC;UAC9BA,wDAAA,IAAAuqB,yCAAA,gBAA6C;UAC7CvqB,wDAAA,KAAAwqB,0CAAA,gBAEK;UACPxqB,mEAAA,EAAe;UAEfA,qEAAA,QAAkC;UAChCA,wDAAA,KAAAyqB,0CAAA,gBAAgD;UAChDzqB,wDAAA,KAAA0qB,0CAAA,gBAEK;UACP1qB,mEAAA,EAAe;UAEfA,qEAAA,QAAwC;UACtCA,wDAAA,KAAA2qB,0CAAA,gBAAsD;UACtD3qB,wDAAA,KAAA4qB,0CAAA,gBAAoE;UACtE5qB,mEAAA,EAAe;UAEfA,qEAAA,QAAmC;UACjCA,wDAAA,KAAA6qB,0CAAA,gBAA6D;UAC7D7qB,wDAAA,KAAA8qB,0CAAA,gBAAuE;UACzE9qB,mEAAA,EAAe;UAEfA,qEAAA,QAAsC;UACpCA,wDAAA,KAAA+qB,0CAAA,gBAAqD;UACrD/qB,wDAAA,KAAAgrB,0CAAA,gBAEK;UACPhrB,mEAAA,EAAe;UAEfA,qEAAA,QAAqC;UACnCA,wDAAA,KAAAirB,0CAAA,gBAA2C;UAC3CjrB,wDAAA,KAAAkrB,0CAAA,gBAMK;UACPlrB,mEAAA,EAAe;UAEfA,wDAAA,KAAAmrB,0CAAA,iBAA4D;UAC5DnrB,wDAAA,KAAAorB,0CAAA,iBAAiE;UACnEprB,0DAAA,EAAQ;;;UA7CSA,uDAAA,GAAyB;UAAzBA,wDAAA,eAAAc,GAAA,CAAAwD,UAAA,CAAyB;UA2CpBtE,uDAAA,IAAiC;UAAjCA,wDAAA,oBAAAc,GAAA,CAAAyD,gBAAA,CAAiC;UACpBvE,uDAAA,GAAyB;UAAzBA,wDAAA,qBAAAc,GAAA,CAAAyD,gBAAA,CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrD/B;AAEjC;AACkF;;;;;;;;;IC+BhEvE,4DAAA,WAAmC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAO;;;;IAAnCA,uDAAA,GAA4B;IAA5BA,gEAAA,MAAA6lB,OAAA,CAAAlT,eAAA,MAA4B;;;;;IAFjE3S,4DAAA,SAAqC;IACnCA,oDAAA,GACA;IAAAA,wDAAA,IAAAqrB,yDAAA,mBAAsE;IACxErrB,0DAAA,EAAK;;;;IAFHA,uDAAA,GACA;IADAA,gEAAA,MAAA6lB,OAAA,CAAA/S,QAAA,SAAA+S,OAAA,CAAAna,IAAA,MACA;IAAO1L,uDAAA,GAA0B;IAA1BA,wDAAA,SAAA6lB,OAAA,CAAAlT,eAAA,CAA0B;;;;;IAPzC3S,4DAAA,aAAuD;IACjDA,oDAAA,GAAuB;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,SAAI;IAAAA,oDAAA,GAAuC;;IAAAA,0DAAA,EAAK;IAChDA,4DAAA,SAAI;IAEAA,wDAAA,IAAAsrB,kDAAA,gBAGK;IACPtrB,0DAAA,EAAK;;;;IARHA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAgmB,QAAA,CAAA3S,WAAA,CAAuB;IACvBrT,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAA,yDAAA,OAAAgmB,QAAA,CAAApgB,QAAA,EAAuC;IAGlB5F,uDAAA,GAAc;IAAdA,wDAAA,YAAAgmB,QAAA,CAAA1S,KAAA,CAAc;;;;;IAd7CtT,4DAAA,UAA4C;IACdA,oDAAA,GAAkB;IAAAA,0DAAA,EAAI;IAElDA,4DAAA,eAAsB;IAEKA,oDAAA,eAAQ;IAAAA,0DAAA,EAAK;IACpCA,4DAAA,aAAuB;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,aAAuB;IAAAA,oDAAA,qBAAa;IAAAA,0DAAA,EAAK;IAE3CA,wDAAA,KAAAurB,6CAAA,iBAWK;IACPvrB,0DAAA,EAAQ;;;;IApBoBA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAkmB,SAAA,CAAAjS,KAAA,CAAkB;IAQtBjU,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAkmB,SAAA,CAAAhS,MAAA,CAAgB;;;ADb1C,MAAOsX,wBAAyB,SAAQttB,wDAAa;EAKzDK,YACU6V,EAAqB,EACrB7G,cAA8B,EAC9B7I,eAAgC;IAExC,KAAK,EAAE;IAJC,KAAA0P,EAAE,GAAFA,EAAE;IACF,KAAA7G,cAAc,GAAdA,cAAc;IACd,KAAA7I,eAAe,GAAfA,eAAe;IAPzB,KAAA4P,eAAe,GAAkB,EAAE;EAUnC;EAEAI,SAASA,CAAC/G,OAAsB;IAC9B,IAAI,CAACJ,cAAc,CAACW,aAAa,EAAE;IACnC,IAAI,CAACN,YAAY,GAAGR,mCAAM,CAACO,OAAO,CAACE,IAAI,CAAC,CAACC,MAAM,EAAE;IAEjD;IACA,IAAI,CAACpJ,eAAe,CAAC+mB,2BAA2B,CAAC9d,OAAO,CAAC,CAACrO,SAAS,CAAC;MAClEoC,IAAI,EAAG0J,QAAuB,IAAI;QAChC,IAAI,CAACsgB,kBAAkB,CAACtgB,QAAQ,CAAC;QACjC,IAAI,CAACmC,cAAc,CAACc,YAAY,EAAE;QAElC,IAAI,CAAC+F,EAAE,CAACU,YAAY,EAAE;MACxB,CAAC;MACD1O,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACmH,cAAc,CAACc,YAAY,EAAE;QAClC,IAAI,CAAChI,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAslB,kBAAkBA,CAACtgB,QAAuB;IACxC,IAAI,CAACkJ,eAAe,GAAGlJ,QAAQ;IAE/B,IAAI,CAAC6F,eAAe,GAAG,CAAC;IAExB,IAAI,IAAI,CAACqD,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACtO,OAAO,CAAC6O,MAAM,IAAG;QACpC,IAAI,CAAC5D,eAAe,IAAI4D,MAAM,CAACX,MAAM,CAACnS,MAAM;MAC9C,CAAC,CAAC;;EAEN;;;uBA1CWypB,wBAAwB,EAAAxrB,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAE,2DAAA,GAAAF,+DAAA,CAAAE,4DAAA;IAAA;EAAA;;;YAAxBsrB,wBAAwB;MAAAlrB,SAAA;MAAAC,QAAA,GAAAP,wEAAA;MAAAS,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAAgrB,kCAAA9qB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfrCb,4DAAA,aAA6B;UAEtBA,oDAAA,qFAA8E;UAAAA,0DAAA,EAAI;UAGvFA,4DAAA,qBAA2C;UAA9BA,wDAAA,qBAAA4rB,iEAAApb,MAAA;YAAA,OAAW1P,GAAA,CAAA4T,SAAA,CAAAlE,MAAA,CAAiB;UAAA,EAAC;UAACxQ,0DAAA,EAAc;UAGzDA,uDAAA,aAAkD;UAElDA,4DAAA,aAAuB;UAEnBA,uDAAA,wBAKkB;UAElBA,wDAAA,IAAA6rB,uCAAA,kBAsBM;UACR7rB,0DAAA,EAAM;;;UA5BFA,uDAAA,GAA6B;UAA7BA,wDAAA,iBAAAc,GAAA,CAAA8M,YAAA,CAA6B,cAAA9M,GAAA,CAAAwT,eAAA,kBAAAxT,GAAA,CAAAwT,eAAA,CAAAvS,MAAA,uBAAAjB,GAAA,CAAAmQ,eAAA;UAKPjR,uDAAA,GAAkB;UAAlBA,wDAAA,YAAAc,GAAA,CAAAwT,eAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBwB;AAChB;;;;;;;;;ICItDtU,4DAAA,aAA2D;IAE5CA,oDAAA,GAAqB;IAAAA,0DAAA,EAAY;IAC5CA,4DAAA,oBAAsC;IACPA,oDAAA,+BAAuB;IAAAA,0DAAA,EAAa;IACjEA,4DAAA,oBAA6B;IAACA,oDAAA,gCAAwB;IAAAA,0DAAA,EAAa;;;;IALvDA,wDAAA,cAAAsH,MAAA,CAAA2S,IAAA,CAAkB;IAErBja,uDAAA,GAAqB;IAArBA,+DAAA,CAAAsH,MAAA,CAAAwkB,eAAA,CAAqB;;;ADChC,MAAOC,4BAA4B;EASvCxtB,YAAA;IARU,KAAAytB,mBAAmB,GAA0B,IAAIpV,uDAAY,EAAE;IAGzE;IACA;IACA,KAAAqV,WAAW,GAAG;MAAEC,OAAO,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAU,CAAE;EAG3C;EAEfvtB,QAAQA,CAAA;IACN,IAAI,CAACkc,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACb,IAAI,GAAG,IAAI9M,qDAAS,CAAC;MACxBif,OAAO,EAAE,IAAIlf,uDAAW,CAAC,IAAI,CAAC+e,WAAW,CAACC,OAAO;KAClD,CAAC;IAEF,IAAI,CAACE,OAAO,CAACtd,YAAY,CAACxP,SAAS,CAACyP,GAAG,IAAG;MACxC,IAAIsd,eAAe,GAAGrU,OAAO,CAACjJ,GAAG,KAAK,IAAI,CAACkd,WAAW,CAACE,QAAQ,CAAC;MAChE,IAAI,CAACH,mBAAmB,CAACrU,IAAI,CAAC0U,eAAe,CAAC;IAChD,CAAC,CAAC;EACJ;EAEA,IAAID,OAAOA,CAAA;IACT,OAAO,IAAI,CAACnS,IAAI,CAACjL,GAAG,CAAC,SAAS,CAAC;EACjC;;;uBA5BW+c,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAzrB,SAAA;MAAA4X,OAAA;QAAA8T,mBAAA;MAAA;MAAAvrB,KAAA;MAAAC,IAAA;MAAAgG,MAAA;MAAA/F,QAAA,WAAA2rB,sCAAAzrB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRzCb,4DAAA,aAAyE;UAElEA,oDAAA,uBAAgB;UAAAA,0DAAA,EAAI;UAGzBA,wDAAA,IAAAusB,2CAAA,iBAQM;UACRvsB,0DAAA,EAAM;;;UATEA,uDAAA,GAAU;UAAVA,wDAAA,SAAAc,GAAA,CAAAmZ,IAAA,CAAU;;;;;;;;AD4ClB;;;AAGM,MAAOuS,0BAA0B;EAAvCjuB,YAAA;IACU,KAAAkuB,UAAU,GAAW,CAAC;IACtB,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAY,KAAK;EA6B5C;EA3BE;EACA;;IACe,KAAAC,iBAAiB,GAAW,CAAC;EAAC;EAEtCC,0BAA0BA,CAAA;IAC/B,OAAO,IAAI,CAACH,kBAAkB;EAChC;EAEOI,2BAA2BA,CAACJ,kBAA2B;IAC5D,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;EAC9C;EAEOK,mBAAmBA,CAAA;IACxB,OAAO,IAAI,CAACJ,iBAAiB;EAC/B;EAEA;;;;EAIOK,WAAWA,CAAA;IAChB,IAAI,CAACP,UAAU,EAAE;IAEjB,IAAI,IAAI,CAACA,UAAU,KAAKD,0BAA0B,CAACI,iBAAiB,EAAE;MACpE,IAAI,CAACD,iBAAiB,GAAG,IAAI;;EAEjC;;;;;;;;;;;;;;;;;AE7EI,MAAOM,qBAAqB;EAChC1uB,YAAA,GAAe;EAEfoqB,SAASA,CAACpiB,EAAU,EAAE2I,IAA6B,EAAEge,GAAW;IAC9D,IAAI1jB,KAAK,GAAG0F,IAAI,CAACie,SAAS,CAAC5gB,CAAC,IAAIA,CAAC,CAAC2gB,GAAG,CAAC,IAAI3mB,EAAE,CAAC;IAC7C,OAAOiD,KAAK,IAAI,CAAC,GAAG0F,IAAI,CAAC1F,KAAK,CAAC,CAAC1G,QAAQ,GAAG,GAAG;EAChD;;;uBANWmqB,qBAAqB;IAAA;EAAA;;;;YAArBA,qBAAqB;MAAAG,IAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;ACA5B,MAAOC,oBAAoB;EAC/B9uB,YAAA,GAAe;EAEfoqB,SAASA,CAAC2E,SAAkC;IAC1C,IAAIC,KAAK,GAAG,CAAC;IACbD,SAAS,CAACtnB,OAAO,CAAC4G,IAAI,IAAK2gB,KAAK,IAAI3gB,IAAI,CAAC9J,QAAS,CAAC;IACnD,OAAOyqB,KAAK;EACd;;;uBAPWF,oBAAoB;IAAA;EAAA;;;;YAApBA,oBAAoB;MAAAD,IAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;ACNU;AACD;;;;;;;;;;;;;;;;;ACKpC,MAAOI,mBAAmB;EAC9BjvB,YAAA,GAAe;EAEfoqB,SAASA,CAAC3jB,UAAkB,EAAEyoB,YAAuC;IACnE,IAAIF,KAAK,GAAG,CAAC;IAEbE,YAAY,CAACznB,OAAO,CAAC0nB,aAAa,IAAG;MACnCA,aAAa,CAAC1nB,OAAO,CAAC2nB,EAAE,IAAG;QACzB,IAAIA,EAAE,CAACjoB,UAAU,IAAIV,UAAU,EAAE;UAC/BuoB,KAAK,IAAII,EAAE,CAAC7qB,QAAQ;;MAExB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOyqB,KAAK;EACd;;;uBAfWC,mBAAmB;IAAA;EAAA;;;;YAAnBA,mBAAmB;MAAAJ,IAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;ACLuB;AAEvD;AAC8D;AAE9D;AAWsB;AACyB;;;AAE/C,MAAMU,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEnmB,2DAAkB;EAC7BomB,OAAO,EAAE;IAAEC,QAAQ,EAAEL,wEAAoBA;EAAA,CAAE;EAC3CM,QAAQ,EAAE;EACR;EACA;EACA;EACA;IACEJ,IAAI,EAAE,EAAE;IACRK,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE;GACb,EACD;IACEN,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE9F,8DAAqB;IAChC7iB,IAAI,EAAE;MAAEipB,WAAW,EAAE;IAAK;GAC3B,EACD;IACEP,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE7J,6DAAoBA;GAChC,EACD;IACE4J,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE7Z,+DAAsB;IACjC9O,IAAI,EAAE;MAAEkpB,UAAU,EAAEzd,uDAAY,CAAC5R;IAAK;GACvC,EACD;IACE6uB,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAExC,iEAAwBA;GACpC,EACD;IACEuC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE7H,wEAA+BA;GAC3C,EACD;IACE4H,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE7Z,+DAAsB;IACjC9O,IAAI,EAAE;MAAEkpB,UAAU,EAAEzd,uDAAY,CAAC7R;IAAK;GACvC,EACD;IACE8uB,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE1gB,oEAA2BA;GACvC;EACD;EACA;EACA;EACA;IACEygB,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE9F,8DAAqB;IAChC7iB,IAAI,EAAE;MAAEipB,WAAW,EAAE;IAAI;GAC1B,EACD;IACEP,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE9E,oEAA2BA;GACvC,EACD;IACE6E,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE7Z,+DAAsB;IACjC9O,IAAI,EAAE;MAAEkpB,UAAU,EAAEzd,uDAAY,CAAC5I;IAAO;GACzC,EACD;IACE6lB,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE/D,oEAA2BA;GACvC;CAEJ,CACF;AAMK,MAAOuE,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBZ,yDAAY,CAACa,QAAQ,CAACX,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXY,oBAAoB;IAAAE,OAAA,GAAAxuB,yDAAA;IAAAyuB,OAAA,GAFrBf,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3FoC;AACP;AACV;AAE3C;AAC2D;AACI;AACN;AACQ;AACV;AACE;AACzD;AACgE;AACQ;AACjB;AACiB;AAExE;AAoBsB;AACoC;AAEiC;AACE;AACG;AACF;;AAkDxF,MAAO4B,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAFb,CAAC5G,yDAAW,CAAC;MAAA8F,OAAA,GAjBtBzQ,0DAAY,EACZ2Q,gEAAmB,EACnBC,sDAAc,EACdL,yEAAoB,EACpBY,gFAAiB,EACjBG,kEAAa,EACbF,+DAAY;MACZ;MACAP,sEAAe,EACfE,oEAAc,EACdD,0EAAiB,EACjBE,4EAAkB,EAClBC,kEAAa,EACbC,oEAAc,EACdG,gFAAiB,EACjBnR,uGAAsB;IAAA;EAAA;;;uHAIbqR,aAAa;IAAAC,YAAA,GA9CtB5nB,2DAAkB,EAClBsc,6DAAoB,EACpBqH,iEAAwB,EACxBrF,wEAA+B,EAC/B9L,4DAAmB,EACnB6O,oEAA2B,EAC3Be,oEAA2B,EAC3BvG,kEAAyB,EACzBd,wEAA+B,EAC/Bf,0EAAiC,EACjCpd,sEAA6B,EAC7BsnB,qEAA4B,EAC5Bze,oEAA2B,EAC3B6J,sEAA6B,EAC7BA,sEAA6B,EAC7B7Y,4DAAmB,EACnBsM,sEAA6B,EAC7BrB,sEAA6B,EAC7B8C,qEAA4B;IAC5B;IACA4gB,yDAAqB,EACrBI,wDAAoB,EACpBG,uDAAmB,EACnBtF,sGAAqB,EACrB/T,yGAAsB;IAAAua,OAAA,GAGtBzQ,0DAAY,EACZ2Q,gEAAmB,EACnBC,sDAAc,EACdL,yEAAoB,EACpBY,gFAAiB,EACjBG,kEAAa,EACbF,+DAAY;IACZ;IACAP,sEAAe,EACfE,oEAAc,EACdD,0EAAiB,EACjBE,4EAAkB,EAClBC,kEAAa,EACbC,oEAAc,EACdG,gFAAiB,EACjBnR,uGAAsB;EAAA;AAAA;;;;;;;;;;AC1Fb;;AACb1P,8CAA6C;EAAEiN,KAAK,EAAE;AAAK,CAAC,EAAC;AAC7DiT,mBAAmB,GAAGA,sBAAsB,GAAGA,uBAAuB,GAAG,KAAK,CAAC;AAC/E,IAAIiB,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG,CAC3B;EACAA,eAAe,CAACC,GAAG,GAAG,MAAM;EAC5BD,eAAe,CAACE,GAAG,GAAG,QAAQ;EAC9BF,eAAe,CAACG,uBAAuB,GAAG,GAAG;EAC7CH,eAAe,CAACI,yBAAyB,GAAG,GAAG;EAC/CJ,eAAe,CAACK,aAAa,GAAG,GAAG;EACnCL,eAAe,CAACM,kBAAkB,GAAG,KAAK;EAC1CN,eAAe,CAACO,aAAa,GAAG,qBAAqB;EACrDP,eAAe,CAACQ,gBAAgB,GAAG,WAAW;EAC9CR,eAAe,CAACS,mBAAmB,GAAG,KAAK;EAC3CT,eAAe,CAACU,qBAAqB,GAAG,KAAK;EAC7CV,eAAe,CAACW,eAAe,GAAG,IAAI;EACtCX,eAAe,CAACY,cAAc,GAAG,EAAE;EACnCZ,eAAe,CAACa,uBAAuB,GAAG,KAAK;EAC/C,OAAOb,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJjB,uBAAuB,GAAGiB,eAAe;AACzCjB,sBAAsB,GAAG;EACrB+B,QAAQ,EAAEd,eAAe,CAACQ,gBAAgB;EAC1C7a,cAAc,EAAEqa,eAAe,CAACG,uBAAuB;EACvDva,YAAY,EAAEoa,eAAe,CAACK,aAAa;EAC3Cxa,gBAAgB,EAAEma,eAAe,CAACI,yBAAyB;EAC3Dta,UAAU,EAAEka,eAAe,CAACS,mBAAmB;EAC/C1a,SAAS,EAAEia,eAAe,CAACM,kBAAkB;EAC7Cta,KAAK,EAAEga,eAAe,CAACO,aAAa;EACpCta,WAAW,EAAE+Z,eAAe,CAACU,qBAAqB;EAClDxa,MAAM,EAAE8Z,eAAe,CAACW,eAAe;EACvCI,OAAO,EAAEf,eAAe,CAACY,cAAc;EACvCza,gBAAgB,EAAE6Z,eAAe,CAACa;AACtC,CAAC;AACD,IAAI5f,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACyE,OAAO,EAAE;IAC1B,IAAI,CAACsb,IAAI,GAAG,EAAE;IACd,IAAIC,MAAM,GAAGvb,OAAO,IAAI,CAAC,CAAC;IAC1B,IAAI,CAACwb,QAAQ,GAAGC,YAAY,CAAC,CAAC,CAAC,EAAEpC,OAAO,CAACgB,cAAc,EAAEkB,MAAM,CAAC;IAChE,IAAI,IAAI,CAACC,QAAQ,CAAC/a,gBAAgB,IAC9B,IAAI,CAAC+a,QAAQ,CAACH,OAAO,IACrB,IAAI,CAACG,QAAQ,CAACH,OAAO,CAAC5uB,MAAM,GAAG,CAAC,EAAE;MAClCivB,OAAO,CAACC,IAAI,CAAC,8EAA8E,CAAC;IAChG;EACJ;EACAxiB,MAAM,CAACihB,cAAc,CAAC7e,WAAW,CAACqgB,SAAS,EAAE,SAAS,EAAE;IACpDliB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAAC8hB,QAAQ;IACxB,CAAC;IACD/jB,GAAG,EAAE,SAAAA,CAAUuI,OAAO,EAAE;MACpB,IAAI,CAACwb,QAAQ,GAAGC,YAAY,CAAC,CAAC,CAAC,EAAEpC,OAAO,CAACgB,cAAc,EAAEra,OAAO,CAAC;IACrE,CAAC;IACD6b,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;EACIvgB,WAAW,CAACqgB,SAAS,CAACjb,WAAW,GAAG,UAAUob,QAAQ,EAAEC,eAAe,EAAE;IACrE,IAAIA,eAAe,KAAK,KAAK,CAAC,EAAE;MAAEA,eAAe,GAAG,KAAK;IAAE;IAC3D;IACA,IAAI,CAACV,IAAI,GAAG,EAAE;IACd,IAAI,CAACW,UAAU,CAACF,QAAQ,CAAC;IACzB,IAAI,IAAI,CAACP,QAAQ,CAAChb,MAAM,EAAE;MACtB,IAAI,CAAC8a,IAAI,IAAIhB,eAAe,CAACE,GAAG;IACpC;IACA,IAAI,IAAI,CAACgB,QAAQ,CAACnb,SAAS,EAAE;MACzB,IAAI,CAACib,IAAI,IAAI,IAAI,CAACE,QAAQ,CAAClb,KAAK,GAAG,QAAQ;IAC/C;IACA,IAAI,CAAC4b,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAACb,IAAI,IAAI,EAAE,EAAE;MACjBI,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;MAC3B;IACJ;IACA;IACA;IACA,IAAIJ,eAAe,EAAE;MACjB,OAAO,IAAI,CAACV,IAAI;IACpB;IACA;IACA;IACA,IAAIe,QAAQ,GAAG,IAAI,CAACb,QAAQ,CAACjb,WAAW,GAAG,OAAO,GAAG,KAAK;IAC1D,IAAI+b,aAAa,GAAG,IAAI,CAACd,QAAQ,CAACjb,WAAW,GAAG,MAAM,GAAG,MAAM;IAC/D,IAAIgc,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,IAAI,CAAClB,IAAI,CAAC,EAAE;MAC7B/hB,IAAI,EAAE,OAAO,GAAG8iB,QAAQ,GAAG;IAC/B,CAAC,CAAC;IACF,IAAII,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACtCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IACrCE,IAAI,CAACM,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC;IACzCN,IAAI,CAACO,QAAQ,GAAG,IAAI,CAACxB,QAAQ,CAACJ,QAAQ,CAAC6B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGX,aAAa;IACzEI,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC;IAC/BA,IAAI,CAAC5M,KAAK,CAAC,CAAC;IACZ6M,QAAQ,CAACQ,IAAI,CAACE,WAAW,CAACX,IAAI,CAAC;EACnC,CAAC;EACDlhB,WAAW,CAACqgB,SAAS,CAACyB,WAAW,GAAG,YAAY;IAC5C,IAAI,CAAC,IAAI,CAAC7B,QAAQ,CAACpb,UAAU,IAAI,CAAC,IAAI,CAACob,QAAQ,CAAC/a,gBAAgB,EAAE;MAC9D;IACJ;IACA,IAAIA,gBAAgB,GAAG,IAAI,CAAC+a,QAAQ,CAAC/a,gBAAgB;IACrD,IAAI4a,OAAO,GAAG5a,gBAAgB,GACxBtH,MAAM,CAACD,IAAI,CAAC,IAAI,CAACokB,KAAK,CAAC,CAAC,CAAC,CAAC,GAC1B,IAAI,CAAC9B,QAAQ,CAACH,OAAO;IAC3B,OAAOA,OAAO;EAClB,CAAC;EACD;AACJ;AACA;EACI9f,WAAW,CAACqgB,SAAS,CAACM,cAAc,GAAG,YAAY;IAC/C,IAAIb,OAAO,GAAG,IAAI,CAACgC,WAAW,CAAC,CAAC;IAChC,IAAIhC,OAAO,CAAC5uB,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI8wB,GAAG,GAAG,EAAE;MACZ,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGnC,OAAO,CAAC5uB,MAAM,EAAE+wB,MAAM,EAAE,EAAE;QACpDD,GAAG,IAAIlC,OAAO,CAACmC,MAAM,CAAC,GAAG,IAAI,CAAChC,QAAQ,CAACvb,cAAc;MACzD;MACAsd,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,IAAI,CAACnC,IAAI,IAAIiC,GAAG,GAAGjD,eAAe,CAACC,GAAG;IAC1C;EACJ,CAAC;EACD;AACJ;AACA;EACIhf,WAAW,CAACqgB,SAAS,CAACO,WAAW,GAAG,YAAY;IAC5C,IAAId,OAAO,GAAG,IAAI,CAACgC,WAAW,CAAC,CAAC;IAChC,KAAK,IAAInmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAComB,KAAK,CAAC7wB,MAAM,EAAEyK,CAAC,EAAE,EAAE;MACxC,IAAIqmB,GAAG,GAAG,EAAE;MACZ,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGnC,OAAO,CAAC5uB,MAAM,EAAE+wB,MAAM,EAAE,EAAE;QACpD,IAAIE,MAAM,GAAGrC,OAAO,CAACmC,MAAM,CAAC;QAC5BD,GAAG,IACC,IAAI,CAACI,WAAW,CAAC,IAAI,CAACL,KAAK,CAACpmB,CAAC,CAAC,CAACwmB,MAAM,CAAC,CAAC,GACnC,IAAI,CAAClC,QAAQ,CAACvb,cAAc;MACxC;MACAsd,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,IAAI,CAACnC,IAAI,IAAIiC,GAAG,GAAGjD,eAAe,CAACC,GAAG;IAC1C;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACIhf,WAAW,CAACqgB,SAAS,CAAC+B,WAAW,GAAG,UAAU5tB,IAAI,EAAE;IAChD,IAAI,IAAI,CAACyrB,QAAQ,CAACrb,gBAAgB,KAAK,QAAQ,IAAI,IAAI,CAACyd,QAAQ,CAAC7tB,IAAI,CAAC,EAAE;MACpE,OAAOA,IAAI,CAAC8tB,cAAc,CAAC,CAAC;IAChC;IACA,IAAI,IAAI,CAACrC,QAAQ,CAACrb,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAACyd,QAAQ,CAAC7tB,IAAI,CAAC,EAAE;MAC/D,OAAOA,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAACstB,OAAO,CAAC,GAAG,EAAE,IAAI,CAACzB,QAAQ,CAACrb,gBAAgB,CAAC;IACvE;IACA,IAAI,OAAOpQ,IAAI,KAAK,QAAQ,EAAE;MAC1BA,IAAI,GAAGA,IAAI,CAACktB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MAC/B,IAAI,IAAI,CAACzB,QAAQ,CAACtb,YAAY,IAC1BnQ,IAAI,CAAC+tB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IACtB/tB,IAAI,CAAC+tB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IACvB/tB,IAAI,CAAC+tB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACzB/tB,IAAI,GAAG,IAAI,CAACyrB,QAAQ,CAACtb,YAAY,GAAGnQ,IAAI,GAAG,IAAI,CAACyrB,QAAQ,CAACtb,YAAY;MACzE;MACA,OAAOnQ,IAAI;IACf;IACA,IAAI,OAAOA,IAAI,KAAK,SAAS,EAAE;MAC3B,OAAOA,IAAI,GAAG,MAAM,GAAG,OAAO;IAClC;IACA,OAAOA,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;EACIwL,WAAW,CAACqgB,SAAS,CAACgC,QAAQ,GAAG,UAAUG,KAAK,EAAE;IAC9C,OAAO,CAACA,KAAK,KAAKA,KAAK,KAAK,CAACC,QAAQ,CAACD,KAAK,CAAC,IAAIrb,OAAO,CAACqb,KAAK,GAAG,CAAC,CAAC,CAAC;EACvE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIxiB,WAAW,CAACqgB,SAAS,CAACK,UAAU,GAAG,UAAUF,QAAQ,EAAE;IACnD,IAAI,CAACuB,KAAK,GAAG,OAAOvB,QAAQ,IAAI,QAAQ,GAAGrV,IAAI,CAACW,KAAK,CAAC0U,QAAQ,CAAC,GAAGA,QAAQ;IAC1E,OAAO,IAAI,CAACuB,KAAK;EACrB,CAAC;EACD,OAAO/hB,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ8d,mBAAmB,GAAG9d,WAAW;AACjC,IAAI0iB,cAAc,GAAG9kB,MAAM,CAACyiB,SAAS,CAACqC,cAAc;AACpD,IAAIC,gBAAgB,GAAG/kB,MAAM,CAACyiB,SAAS,CAACuC,oBAAoB;AAC5D;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC3kB,GAAG,EAAE;EACnB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK4kB,SAAS,EAAE;IACnC,MAAM,IAAIC,SAAS,CAAC,uDAAuD,CAAC;EAChF;EACA,OAAOnlB,MAAM,CAACM,GAAG,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASgiB,YAAYA,CAAC8C,MAAM,EAAE;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACjyB,MAAM,EAAEgyB,EAAE,EAAE,EAAE;IAC1CD,MAAM,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAClC;EACA,IAAIE,IAAI;EACR,IAAIC,EAAE,GAAGR,QAAQ,CAACG,MAAM,CAAC;EACzB,IAAIM,OAAO;EACX,KAAK,IAAIxW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqW,SAAS,CAACjyB,MAAM,EAAE4b,CAAC,EAAE,EAAE;IACvCsW,IAAI,GAAGxlB,MAAM,CAACulB,SAAS,CAACrW,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIuP,GAAG,IAAI+G,IAAI,EAAE;MAClB,IAAIV,cAAc,CAACa,IAAI,CAACH,IAAI,EAAE/G,GAAG,CAAC,EAAE;QAChCgH,EAAE,CAAChH,GAAG,CAAC,GAAG+G,IAAI,CAAC/G,GAAG,CAAC;MACvB;IACJ;IACA,IAAIze,MAAM,CAAC4lB,qBAAqB,EAAE;MAC9BF,OAAO,GAAG1lB,MAAM,CAAC4lB,qBAAqB,CAACJ,IAAI,CAAC;MAC5C,KAAK,IAAIznB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2nB,OAAO,CAACpyB,MAAM,EAAEyK,CAAC,EAAE,EAAE;QACrC,IAAIgnB,gBAAgB,CAACY,IAAI,CAACH,IAAI,EAAEE,OAAO,CAAC3nB,CAAC,CAAC,CAAC,EAAE;UACzC0nB,EAAE,CAACC,OAAO,CAAC3nB,CAAC,CAAC,CAAC,GAAGynB,IAAI,CAACE,OAAO,CAAC3nB,CAAC,CAAC,CAAC;QACrC;MACJ;IACJ;EACJ;EACA,OAAO0nB,EAAE;AACb;;;;;;;;;;ACnOa;;AACb,IAAII,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,KAAM7lB,MAAM,CAAC8lB,MAAM,GAAI,UAASC,CAAC,EAAEC,CAAC,EAAE/lB,CAAC,EAAEgmB,EAAE,EAAE;EAC5F,IAAIA,EAAE,KAAKf,SAAS,EAAEe,EAAE,GAAGhmB,CAAC;EAC5B,IAAIimB,IAAI,GAAGlmB,MAAM,CAACmmB,wBAAwB,CAACH,CAAC,EAAE/lB,CAAC,CAAC;EAChD,IAAI,CAACimB,IAAI,KAAK,KAAK,IAAIA,IAAI,GAAG,CAACF,CAAC,CAACI,UAAU,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACvD,YAAY,CAAC,EAAE;IACjFuD,IAAI,GAAG;MAAExD,UAAU,EAAE,IAAI;MAAEniB,GAAG,EAAE,SAAAA,CAAA,EAAW;QAAE,OAAOylB,CAAC,CAAC/lB,CAAC,CAAC;MAAE;IAAE,CAAC;EAC/D;EACAD,MAAM,CAACihB,cAAc,CAAC8E,CAAC,EAAEE,EAAE,EAAEC,IAAI,CAAC;AACtC,CAAC,GAAK,UAASH,CAAC,EAAEC,CAAC,EAAE/lB,CAAC,EAAEgmB,EAAE,EAAE;EACxB,IAAIA,EAAE,KAAKf,SAAS,EAAEe,EAAE,GAAGhmB,CAAC;EAC5B8lB,CAAC,CAACE,EAAE,CAAC,GAAGD,CAAC,CAAC/lB,CAAC,CAAC;AAChB,CAAE,CAAC;AACH,IAAIqmB,YAAY,GAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAK,UAASN,CAAC,EAAE9F,OAAO,EAAE;EACnE,KAAK,IAAIqG,CAAC,IAAIP,CAAC,EAAE,IAAIO,CAAC,KAAK,SAAS,IAAI,CAACvmB,MAAM,CAACyiB,SAAS,CAACqC,cAAc,CAACa,IAAI,CAACzF,OAAO,EAAEqG,CAAC,CAAC,EAAEV,eAAe,CAAC3F,OAAO,EAAE8F,CAAC,EAAEO,CAAC,CAAC;AAC7H,CAAC;AACDvmB,8CAA6C;EAAEiN,KAAK,EAAE;AAAK,CAAC,EAAC;AAC7DqZ,YAAY,CAACE,mBAAO,CAAC,2BAAiB,CAAC,EAAEtG,OAAO,CAAC", "sources": ["./src/app/reports/components/base-report/base-report.component.ts", "./src/app/reports/components/index.ts", "./src/app/reports/components/item-report-print-service/item-report-print.service.ts", "./src/app/reports/components/item-student-list-table/item-student-list-table.component.ts", "./src/app/reports/components/item-student-list-table/item-student-list-table.component.html", "./src/app/reports/components/nav-report/nav-report.component.ts", "./src/app/reports/components/nav-report/nav-report.component.html", "./src/app/reports/components/report-class-item-components/hybrid-class-item-table/hybrid-class-item-table.component.ts", "./src/app/reports/components/report-class-item-components/hybrid-class-item-table/hybrid-class-item-table.component.html", "./src/app/reports/components/report-class-item-components/hybrid-class-list-table/hybrid-class-list-table.component.ts", "./src/app/reports/components/report-class-item-components/hybrid-class-list-table/hybrid-class-list-table.component.html", "./src/app/reports/components/report-class-item-components/hybrid-item-list-table/hybrid-item-list-table.component.ts", "./src/app/reports/components/report-class-item-components/hybrid-item-list-table/hybrid-item-list-table.component.html", "./src/app/reports/components/report-class-item-components/report-class-and-item/report-class-and-item.component.ts", "./src/app/reports/components/report-class-item-components/report-class-and-item/report-class-and-item.component.html", "./src/app/reports/components/report-class-v4/report-class-v4.component.ts", "./src/app/reports/components/report-class-v4/report-class-v4.component.html", "./src/app/reports/components/report-form-and-filters/report-form-and-filters.component.ts", "./src/app/reports/components/report-form-and-filters/report-form-and-filters.component.html", "./src/app/reports/components/report-form/report-form.component.ts", "./src/app/reports/components/report-form/report-form.component.html", "./src/app/reports/components/report-heading/report-heading.component.ts", "./src/app/reports/components/report-heading/report-heading.component.html", "./src/app/reports/components/report-items-category-table/report-items-category-table.component.ts", "./src/app/reports/components/report-items-category-table/report-items-category-table.component.html", "./src/app/reports/components/report-items-filter-table/report-items-filter-table.component.ts", "./src/app/reports/components/report-items-filter-table/report-items-filter-table.component.html", "./src/app/reports/components/report-items-table/report-items-table.component.ts", "./src/app/reports/components/report-items-table/report-items-table.component.html", "./src/app/reports/components/report-items/report-items.component.ts", "./src/app/reports/components/report-items/report-items.component.html", "./src/app/reports/components/report-printedcancelled/report-printedcancelled.component.ts", "./src/app/reports/components/report-printedcancelled/report-printedcancelled.component.html", "./src/app/reports/components/report-sale-v3/report-sale-v3.component.ts", "./src/app/reports/components/report-sale-v3/report-sale-v3.component.html", "./src/app/reports/components/report-uniform-items/report-uniform-items.component.ts", "./src/app/reports/components/report-uniform-items/report-uniform-items.component.html", "./src/app/reports/components/report-uniform-stock/report-uniform-stock.component.ts", "./src/app/reports/components/report-uniform-stock/report-uniform-stock.component.html", "./src/app/reports/components/report-unprinted/report-unprinted.component.ts", "./src/app/reports/components/report-unprinted/report-unprinted.component.html", "./src/app/reports/components/report-version-picker/report-version-picker.component.ts", "./src/app/reports/components/report-version-picker/report-version-picker.component.html", "./src/app/reports/pipes/class-item-quantity.pipe.ts", "./src/app/reports/pipes/class-report-total.pipe.ts", "./src/app/reports/pipes/index.ts", "./src/app/reports/pipes/item-report-total.pipe.ts", "./src/app/reports/reports-routing.module.ts", "./src/app/reports/reports.module.ts", "./node_modules/export-to-csv/build/export-to-csv.js", "./node_modules/export-to-csv/build/index.js"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { select, Store } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport {\n  BaseComponent,\n  CategoryEditor,\n  MenuPickerOptions,\n  MerchantTypeEnum,\n  ReportRequest,\n} from 'src/app/sharedModels';\nimport { CanteenService } from 'src/app/sharedServices';\nimport { CanteenState } from 'src/app/states';\nimport { canteenStateSelector } from 'src/app/states/canteen/canteen.selectors';\n\n@Component({\n  selector: '',\n  template: '',\n})\nexport class BaseReportComponent extends BaseComponent implements OnInit {\n  private canteenSubscription: Subscription;\n\n  isEventMerchant: boolean = false;\n  merchantType: string;\n  selectedDate: Date;\n  selectedCanteenName: string;\n  selectedMerchantId: number;\n  selectedCategoryNames: string[];\n  menuCategories: CategoryEditor[];\n  reportRequest: ReportRequest;\n  menuPicker: MenuPickerOptions = new MenuPickerOptions();\n\n  constructor(private store: Store<{ canteen: CanteenState }>, protected canteenService: CanteenService) {\n    super();\n  }\n\n  ngOnInit(): void {}\n\n  initMenuPicker() {\n    let currentMenuOptions = this.canteenService.GetFilters();\n    this.menuPicker.Recess = currentMenuOptions.Recess;\n    this.menuPicker.Lunch = currentMenuOptions.Lunch;\n    this.menuPicker.Event = currentMenuOptions.Event;\n  }\n\n  getMerchantInfo() {\n    this.canteenSubscription = this.store\n      .pipe(select(canteenStateSelector))\n      .subscribe((state: CanteenState) => {\n        if (state.selected) {\n          this.selectedMerchantId = state.selected.CanteenId;\n          this.merchantType = state.selected.CanteenType;\n          this.isEventMerchant = this.merchantType === MerchantTypeEnum.Event;\n          this.menuCategories = state.menuCategories;\n        }\n      });\n  }\n\n  baseReportOnDestroy() {\n    if (this.canteenSubscription) {\n      this.canteenSubscription.unsubscribe();\n    }\n  }\n}\n", "export * from './report-unprinted/report-unprinted.component';\nexport * from './report-printedcancelled/report-printedcancelled.component';\nexport * from './report-items/report-items.component';\nexport * from './nav-report/nav-report.component';\nexport * from './report-form/report-form.component';\nexport * from './report-uniform-items/report-uniform-items.component';\nexport * from './report-uniform-stock/report-uniform-stock.component';\nexport * from './report-version-picker/report-version-picker.component';\nexport * from './report-items-table/report-items-table.component';\nexport * from './report-items-category-table/report-items-category-table.component';\nexport * from './report-items-filter-table/report-items-filter-table.component';\nexport * from './item-student-list-table/item-student-list-table.component';\nexport * from './item-report-print-service/item-report-print.service';\nexport * from './report-class-item-components/report-class-and-item/report-class-and-item.component';\nexport * from './report-form-and-filters/report-form-and-filters.component';\nexport * from './base-report/base-report.component';\nexport * from './report-class-item-components/hybrid-class-item-table/hybrid-class-item-table.component';\nexport * from './report-class-item-components/hybrid-class-list-table/hybrid-class-list-table.component';\nexport * from './report-class-item-components/hybrid-item-list-table/hybrid-item-list-table.component';\nexport * from './report-class-v4/report-class-v4.component';\nexport * from './report-sale-v3/report-sale-v3.component';\n", "import { Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { Category } from 'src/app/sharedModels';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ItemReportPrintService {\n  closedAccordionRowCount: number = 0;\n  reportCategorySum: number = 0;\n\n  private triggerPrinting = new Subject<void>();\n  public triggerPrintEvent$ = this.triggerPrinting.asObservable();\n\n  private closeAccordionRow = new Subject<number>();\n  public closeAccordionRowEvent$ = this.closeAccordionRow.asObservable();\n\n  constructor() {}\n\n  triggerPrint() {\n    // trigger printing once all the of the accordion rows have been closed\n    if (this.closedAccordionRowCount === this.reportCategorySum) {\n      this.triggerPrinting.next();\n      this.closedAccordionRowCount = 0;\n    }\n  }\n\n  /**\n   * Called each time an accordion report row is closed\n   */\n  AddToAccordionRowCloseCount() {\n    this.closedAccordionRowCount++;\n    this.triggerPrint();\n  }\n\n  getSumOfReportCategories(showMenuType: boolean, filteredList: Category[]) {\n    if (showMenuType) {\n      this.reportCategorySum += filteredList.length;\n    }\n  }\n\n  closeAllReportAccordionRows() {\n    this.closeAccordionRow.next(null);\n  }\n\n  resetCategorySum() {\n    this.closedAccordionRowCount = 0;\n    this.reportCategorySum = 0;\n  }\n}\n", "import { Component, OnInit, Input, OnDestroy } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { Observable, Subscription } from 'rxjs';\n\n// Models\nimport { BaseComponent, ReportItemStudentList, ReportRequest } from 'src/app/sharedModels';\n\n// Services\nimport { OrderApiService } from 'src/app/sharedServices';\n\nconst _columns = ['quantity', 'orderId', 'student', 'schoolClass'];\n\n@Component({\n  selector: 'item-student-list-table',\n  templateUrl: './item-student-list-table.component.html',\n  styleUrls: ['./item-student-list-table.component.scss'],\n})\nexport class ItemStudentListTableComponent extends BaseComponent implements OnInit, OnDestroy {\n  @Input() menuItemId: number;\n  @Input() reportRequest: ReportRequest;\n  @Input() menuType: string;\n  @Input() studentIds: string;\n  @Input() orderIds: string;\n  studentIdArray: string[];\n  displayedColumns = _columns;\n  dataSource = new MatTableDataSource<ReportItemStudentList>();\n  loading: boolean = true;\n\n  /* receive the loadStudent trigger */\n  @Input() loadStudents: Observable<string>;\n  loadSubscription: Subscription;\n\n  constructor(private orderAPIService: OrderApiService) {\n    super();\n  }\n\n  ngOnInit(): void {\n    this.loadSubscription = this.loadStudents.subscribe((rowIdString: string) => {\n      //create id string to match incoming rowIdString\n      let idString = this.menuItemId.toString() + this.orderIds;\n      if (rowIdString === idString) {\n        this.loadStudentList();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.loadSubscription) {\n      this.loadSubscription.unsubscribe();\n    }\n  }\n\n  /**\n   * Fetches the student data from the api\n   */\n  loadStudentList() {\n    if (!this.loading && this.dataSource.data.length > 0) {\n      return;\n    }\n    this.studentIdArray = this.studentIds.split(',');\n    this.loading = true;\n\n    this.reportRequest.MenuItemId = this.menuItemId;\n    this.reportRequest.MenuType = this.menuType;\n\n    this.orderAPIService.GetOrderItemStudentList(this.reportRequest).subscribe({\n      next: (res: ReportItemStudentList[]) => {\n        this.loading = false;\n\n        let studentsToShow = [];\n        res.forEach(el => {\n          if (this.checkIfStudentHasMatchingOrderId(el)) {\n            studentsToShow.push(el);\n          }\n        });\n        this.dataSource.data = studentsToShow;\n      },\n      error: error => {\n        this.loading = false;\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  /**\n   * Check if studentId is included in the menuItem studentId list\n   * This function filters out students with modifiers that don't match the parent table modifiers\n   */\n  checkIfStudentHasMatchingOrderId(studentOrder: ReportItemStudentList) {\n    let res = false;\n    this.studentIdArray.forEach(id => {\n      if (id === studentOrder.StudentId.toString()) {\n        res = true;\n        return;\n      }\n    });\n    return res;\n  }\n}\n", "<div *ngIf=\"loading\" class=\"col-12 d-flex justify-content-center\">\n  <app-spinner [manual]=\"true\"></app-spinner>\n</div>\n\n<table *ngIf=\"!loading\" mat-table [dataSource]=\"dataSource\">\n  <ng-container matColumnDef=\"quantity\">\n    <th mat-header-cell *matHeaderCellDef></th>\n    <td mat-cell *matCellDef=\"let element\">({{ element.Quantity }})</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"orderId\">\n    <th mat-header-cell *matHeaderCellDef>Order Id</th>\n    <td mat-cell *matCellDef=\"let element\">{{ element.OrderId }}</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"student\">\n    <th mat-header-cell *matHeaderCellDef>Student</th>\n    <td mat-cell *matCellDef=\"let element\">\n      <a routerLink=\"../../students/details/{{ element.StudentId }}\" class=\"student-link\">{{\n        element.StudentName\n      }}</a>\n    </td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"schoolClass\">\n    <th mat-header-cell *matHeaderCellDef>Class</th>\n    <td mat-cell *matCellDef=\"let element\">{{ element.SchoolClass }}</td>\n  </ng-container>\n\n  <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n  <tr mat-row *matRowDef=\"let row; columns: displayedColumns\"></tr>\n</table>\n", "import { Component, OnDestroy, OnInit } from '@angular/core';\n\n// ngrx\nimport { Store, select } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport { MerchantTypeEnum } from 'src/app/sharedModels';\nimport { CanteenState } from '../../../states';\nimport { canteenStateSelector } from '../../../states/canteen/canteen.selectors';\nimport { FeatureFlagService } from 'src/app/sharedServices';\nimport { FeatureFlags } from 'src/constants';\n\n@Component({\n  selector: 'app-nav-report',\n  templateUrl: './nav-report.component.html',\n  styleUrls: ['./nav-report.component.scss'],\n})\nexport class NavReportComponent implements OnInit, OnDestroy {\n  private subscription: Subscription;\n  isSaleReportRole: boolean = false;\n  isUniformMerchant: boolean = false;\n  isEventMerchant: boolean = false;\n  IsOrdersNotPrintedReportsAvailable: boolean = false;\n  private featureFlagSubscription: Subscription;\n\n  constructor(\n    protected featureFlagService: FeatureFlagService,\n    private store: Store<{ canteen: CanteenState }>\n  ) {}\n\n  ngOnInit() {\n    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {\n      if (state.selected) {\n        this.isSaleReportRole = state.selected.IsSaleReportsAvailable;\n        this.IsOrdersNotPrintedReportsAvailable = state.selected.IsOrdersNotPrintedReportsAvailable;\n        this.isUniformMerchant = state.selected.CanteenType === MerchantTypeEnum.Uniform;\n        this.isEventMerchant = state.selected.CanteenType === MerchantTypeEnum.Event;\n      } else {\n        this.isSaleReportRole = false;\n        this.IsOrdersNotPrintedReportsAvailable = false;\n      }\n    });\n  }\n\n  getSaleReportNavigationLink(): string {\n    if (this.isUniformMerchant) {\n      return 'uniformsales';\n    } else {\n      return 'sales';\n    }\n  }\n\n  ngOnDestroy(): void {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n\n    if (this.featureFlagSubscription) {\n      this.featureFlagSubscription.unsubscribe();\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <!-- canteen links  -->\n      <ng-container *ngIf=\"!isUniformMerchant\">\n        <ul class=\"tabReport\">\n          <li [routerLink]=\"['items']\" routerLinkActive=\"active\">Items List Report</li>\n          <li *ngIf=\"!isEventMerchant\" routerLink=\"class\" routerLinkActive=\"active\">Class Report</li>\n          <li [routerLink]=\"['classanditem']\" routerLinkActive=\"active\">Class & Items Report</li>\n          <li routerLink=\"events\" routerLinkActive=\"active\">Events Report</li>\n          <li\n            *ngIf=\"IsOrdersNotPrintedReportsAvailable && !isEventMerchant\"\n            [routerLink]=\"['unprinted']\"\n            routerLinkActive=\"active\"\n          >\n            Unprinted Orderss\n          </li>\n          <li\n            *ngIf=\"IsOrdersNotPrintedReportsAvailable && !isEventMerchant\"\n            [routerLink]=\"['printedcancelled']\"\n            routerLinkActive=\"active\"\n          >\n            Adjusted Orders\n          </li>\n          <li *ngIf=\"isSaleReportRole\" routerLink=\"sales\" routerLinkActive=\"active\">\n            Sales Report\n          </li>\n        </ul>\n      </ng-container>\n\n      <!-- uniform links -->\n      <ng-container *ngIf=\"isUniformMerchant\">\n        <ul class=\"tabReport\">\n          <li [routerLink]=\"['uniformitems']\" routerLinkActive=\"active\">Items List Report</li>\n          <li routerLink=\"uniformclass\" routerLinkActive=\"active\">Class Report</li>\n          <li *ngIf=\"isSaleReportRole\" routerLink=\"uniformsales\" routerLinkActive=\"active\">\n            Sales Report\n          </li>\n          <li [routerLink]=\"['uniformstock']\" routerLinkActive=\"active\">Stock Management</li>\n        </ul>\n      </ng-container>\n    </div>\n  </div>\n</div>\n\n<router-outlet></router-outlet>\n", "import { Component, Input, OnInit, SimpleChanges } from '@angular/core';\nimport { SchoolClass } from 'src/app/sharedModels';\nimport { ReportHybridSchool } from 'src/app/sharedModels/report/reportHybridClass';\n\n@Component({\n  selector: 'hybrid-class-item-table',\n  templateUrl: './hybrid-class-item-table.component.html',\n  styleUrls: ['./hybrid-class-item-table.component.scss'],\n})\nexport class HybridClassItemTableComponent implements OnInit {\n  @Input() reportData: ReportHybridSchool[];\n  @Input() listClass: SchoolClass[];\n  @Input() showClassReport: boolean;\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  isLastIndex(index: number) {\n    return index === this.reportData.length - 1;\n  }\n}\n", "<div *ngIf=\"reportData\">\n  <div *ngFor=\"let school of reportData; let i = index\" class=\"school-table-container\">\n    <p class=\"menuType pb-2 m-0\">{{ school.SchoolName }}</p>\n\n    <hybrid-class-list-table\n      *ngIf=\"showClassReport; else showItemReport\"\n      [categoryData]=\"school.SortedCategoryList\"\n      [schoolId]=\"school.SchoolId\"\n    ></hybrid-class-list-table>\n\n    <ng-template #showItemReport>\n      <hybrid-item-list-table [categoryData]=\"school.SortedCategoryList\"></hybrid-item-list-table>\n    </ng-template>\n\n    <!-- Printing page break after each school-->\n    <div *ngIf=\"!isLastIndex(i)\" style=\"page-break-before: always\"></div>\n  </div>\n</div>\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { BaseComponent, ListClasses, ReportHybridTableComponent, SchoolClass } from 'src/app/sharedModels';\nimport { ReportHybridCategory, HybridReportData } from 'src/app/sharedModels/report/reportHybridClass';\nimport { SchoolClassesService } from 'src/app/sharedServices';\nimport * as _ from 'lodash';\n\n@Component({\n  selector: 'hybrid-class-list-table',\n  templateUrl: './hybrid-class-list-table.component.html',\n  styleUrls: ['./hybrid-class-list-table.component.scss'],\n})\nexport class HybridClassListTableComponent extends ReportHybridTableComponent implements OnInit {\n  @Input() categoryData: ReportHybridCategory[];\n  @Input() schoolId: number;\n  listClass: SchoolClass[];\n  tableData: HybridReportData[] = [];\n\n  constructor(private classesService: SchoolClassesService) {\n    super();\n  }\n\n  ngOnInit(): void {\n    this.getClass();\n    this.tableData = this.processData(this.categoryData, 'MenuItemId');\n  }\n\n  getClass() {\n    this.classesService.GetClassesBySchoolAPI(this.schoolId, true).subscribe({\n      next: (response: ListClasses) => {\n        this.listClass = response.Classes;\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n}\n", "<table *ngFor=\"let categ of tableData\">\n  <thead>\n    <tr>\n      <th class=\"left-column\">{{ categ.CategoryName }}</th>\n      <th *ngFor=\"let class of listClass\">{{ class.Name }}</th>\n      <th>TOTAL</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr *ngFor=\"let items of categ.Rows\">\n      <td class=\"left-column\">{{ items[0].MenuItemName }}</td>\n      <td *ngFor=\"let class of listClass\">\n        {{ class.ClassId | classItemQuantity : items : 'ClassId' }}\n      </td>\n      <td>{{ items | classReportTotal }}</td>\n    </tr>\n  </tbody>\n</table>\n", "import { Component, Input, OnInit } from '@angular/core';\nimport * as _ from 'lodash';\n\n// Models\nimport { ReportHybridTableComponent } from 'src/app/sharedModels';\nimport {\n  HybridReportData,\n  ReportHybridCategory,\n  ReportHybridMenuItems,\n} from 'src/app/sharedModels/report/reportHybridClass';\n\n@Component({\n  selector: 'hybrid-item-list-table',\n  templateUrl: './hybrid-item-list-table.component.html',\n  styleUrls: ['./hybrid-item-list-table.component.scss'],\n})\nexport class HybridItemListTableComponent extends ReportHybridTableComponent implements OnInit {\n  @Input() categoryData: ReportHybridCategory[];\n  tableData: HybridReportData[];\n  menuItemList: any[];\n\n  constructor() {\n    super();\n  }\n\n  ngOnInit(): void {\n    this.tableData = this.processData(this.categoryData, 'ClassId');\n    this.getDistinctMenuItems();\n  }\n\n  getDistinctMenuItems() {\n    this.categoryData.forEach((x, i) => {\n      if (!this.tableData) {\n        return;\n      }\n      const result = [];\n      const map = new Map();\n      for (const item of x.MenuItemList) {\n        if (!map.has(item.MenuItemId)) {\n          map.set(item.MenuItemId, true);\n          result.push({\n            id: item.MenuItemId,\n            name: item.MenuItemName,\n          });\n        }\n      }\n\n      this.tableData[i].DistinctMenuList = result;\n    });\n  }\n}\n", "<table *ngFor=\"let categ of tableData\">\n  <thead>\n    <tr>\n      <th class=\"left-column\">{{ categ.CategoryName }}</th>\n      <th *ngFor=\"let mi of categ.DistinctMenuList\">{{ mi.name }}</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr *ngFor=\"let items of categ.Rows\">\n      <td class=\"left-column\">{{ items[0].ClassName }}</td>\n\n      <td *ngFor=\"let mi of categ.DistinctMenuList\">\n        {{ mi.id | classItemQuantity : items : 'MenuItemId' }}\n      </td>\n    </tr>\n    <tr>\n      <td class=\"left-column\">TOTAL</td>\n      <td *ngFor=\"let items of categ.DistinctMenuList\">\n        {{ items.id | itemReportTotal : categ.Rows }}\n      </td>\n    </tr>\n  </tbody>\n</table>\n", "import { Component, OnInit } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { Store } from '@ngrx/store';\nimport moment from 'moment';\nimport { Category, MenuPickerOptions, ReportRequest } from 'src/app/sharedModels';\nimport { ReportHybridSchool } from 'src/app/sharedModels/report/reportHybridClass';\nimport { CanteenService, OrderApiService, SpinnerService } from 'src/app/sharedServices';\nimport { CanteenState } from 'src/app/states';\nimport { BaseReportComponent } from '../../base-report/base-report.component';\nimport * as _ from 'lodash';\n\n@Component({\n  selector: 'app-report-class-and-item',\n  templateUrl: './report-class-and-item.component.html',\n  styleUrls: ['./report-class-and-item.component.scss'],\n})\nexport class ReportClassAndItemComponent extends BaseReportComponent implements OnInit {\n  reportData: ReportHybridSchool[];\n  reportTypeForm: FormGroup;\n  showClassReport: boolean = true;\n  reportRequest: ReportRequest;\n  menuString: string = 'Recess,Lunch,Event';\n  selectedCategoryList: number[];\n  itemTotal: number;\n\n  constructor(\n    store: Store<{ canteen: CanteenState }>,\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService,\n    protected canteenService: CanteenService\n  ) {\n    super(store, canteenService);\n  }\n\n  ngOnInit(): void {\n    this.getMerchantInfo();\n\n    this.createForm();\n  }\n\n  ngOnDestroy(): void {\n    this.baseReportOnDestroy();\n  }\n\n  prepareReportRequest(request: ReportRequest) {\n    this.reportRequest = request;\n    this.selectedDate = moment(request.Date).toDate();\n    request.MenuType = this.menuString;\n    this.selectedCanteenName = request.selectedCanteenName;\n\n    this._getReport();\n  }\n\n  private _getReport() {\n    if (!this.reportRequest) {\n      return;\n    }\n    this._getClassListReport();\n  }\n\n  private _getClassListReport() {\n    this.spinnerService.animatedStart();\n\n    this.orderAPIService.GetReportClassItem(this.reportRequest).subscribe({\n      next: (response: ReportHybridSchool[]) => {\n        this.reportData = response;\n        this.filterSelectedCategories();\n        this.spinnerService.animatedStop();\n      },\n      error: error => {\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  menuSelectionChanged(menu: MenuPickerOptions) {\n    this.menuString = '';\n    let keys = Object.keys(menu);\n    keys.forEach(k => {\n      if (menu[k]) {\n        this.menuString += k + ',';\n      }\n    });\n    if (!this.reportRequest) {\n      return;\n    }\n\n    this.reportRequest.MenuType = this.menuString;\n    this.prepareReportRequest(this.reportRequest);\n  }\n\n  showPrintButton() {\n    return this.reportData?.length > 0;\n  }\n\n  createForm() {\n    this.reportTypeForm = new FormGroup({\n      type: new FormControl('class'),\n    });\n\n    this.type.valueChanges.subscribe(val => {\n      this.showClassReport = val == 'class';\n    });\n  }\n\n  get type() {\n    return this.reportTypeForm.get('type');\n  }\n\n  selectedCategories(list: Category[]) {\n    if (!list) {\n      return [];\n    }\n    return list.filter((el: Category) => {\n      return this.selectedCategoryNames.includes(el.CatName);\n    });\n  }\n\n  categoriesSelectionChanged(categories: number[]) {\n    this.selectedCategoryList = categories;\n\n    if (this.reportData) {\n      this.filterSelectedCategories();\n    }\n  }\n\n  filterSelectedCategories() {\n    this.selectedCategoryNames = [];\n\n    this.reportData.forEach(sch => {\n      sch.SortedCategoryList = sch.CategoryList.filter(x => this.selectedCategoryList.includes(x.CategoryId));\n    });\n\n    //get menu item quantity count\n    this.itemTotal = 0;\n    this.reportData.forEach(sch => {\n      sch.SortedCategoryList.forEach(categ => {\n        var sum = categ.MenuItemList.reduce(function (accumulated, curr) {\n          return accumulated + curr.Quantity;\n        }, 0);\n        this.itemTotal += sum;\n      });\n    });\n\n    //triggers ngChange when this var is passed to the table component\n    this.reportData = _.cloneDeep(this.reportData);\n  }\n}\n", "<div class=\"container-fluid\">\n  <report-form-and-filters\n    [menuCategories]=\"menuCategories\"\n    [merchantType]=\"merchantType\"\n    (getReport)=\"prepareReportRequest($event)\"\n    (categoryChange)=\"categoriesSelectionChanged($event)\"\n    (menuTypeChange)=\"menuSelectionChanged($event)\"\n    [showClassPicker]=\"false\"\n  ></report-form-and-filters>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div class=\"row\">\n    <div class=\"sort-container pb-2 col-sm-6 col-md-10 d-flex\">\n      <form [formGroup]=\"reportTypeForm\" class=\"align-self-end\">\n        <label id=\"type-label\" class=\"button-label\">Sort by:</label>\n        <mat-radio-group aria-labelledby=\"type-label\" formControlName=\"type\">\n          <mat-radio-button value=\"class\" class=\"ml-3 button-label\">Class</mat-radio-button>\n          <mat-radio-button value=\"item\" class=\"ml-3 button-label\">Item</mat-radio-button>\n        </mat-radio-group>\n      </form>\n    </div>\n\n    <div *ngIf=\"showPrintButton()\" class=\"col-sm-6 col-md-2 print-button-spacing\">\n      <button\n        #printBtn\n        class=\"PrimaryButton smaller\"\n        type=\"button\"\n        printSectionId=\"print-report\"\n        [useExistingCss]=\"true\"\n        ngxPrint\n      >\n        Print\n      </button>\n    </div>\n  </div>\n\n  <div id=\"print-report\">\n    <div class=\"sectionReport pb-4\">\n      <report-heading\n        title=\"Class & Items Report\"\n        [selectedDate]=\"selectedDate\"\n        [noResult]=\"!reportData?.length\"\n        [orderCount]=\"itemTotal\"\n      ></report-heading>\n\n      <div *ngIf=\"reportData?.length\">\n        <hybrid-class-item-table\n          [reportData]=\"reportData\"\n          [showClassReport]=\"showClassReport\"\n        ></hybrid-class-item-table>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';\nimport { ExportToCsv } from 'export-to-csv';\nimport * as moment from 'moment';\n\n// Models\nimport {\n  ReportRequest,\n  BaseComponent,\n  ClassReport,\n  MenuTypeEnum,\n  CsvEventsReports,\n} from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService } from '../../../sharedServices';\n\n// ngrx\nimport { Store, select } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport { CanteenState } from '../../../states';\nimport { canteenStateSelector } from '../../../states/canteen/canteen.selectors';\nimport { ActivatedRoute } from '@angular/router';\nimport { KeyValue } from '@angular/common';\n\n@Component({\n  selector: 'report-class-v4',\n  templateUrl: './report-class-v4.component.html',\n  styleUrls: ['./report-class-v4.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportClassV4Component extends BaseComponent implements OnInit {\n  listReportClass: ClassReport[] = [];\n  selectedDate: Date;\n  orderClassCount: number;\n  orderPerSchools: KeyValue<string, number>[] = [];\n  merchantHasManySchools: boolean;\n  private subscription: Subscription;\n  isUniformMerchant: boolean = false;\n  isEventReport: boolean = false;\n  noResult: boolean = true;\n  selectedSchoolName: string;\n  private menuType: string;\n  private schoolNameToDisplayForPrint: string;\n  private schoolNameToShowTitle: string;\n\n  constructor(\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService,\n    private cd: ChangeDetectorRef,\n    private route: ActivatedRoute,\n    private store: Store<{ canteen: CanteenState }>\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    this.menuType = this.route.snapshot.data['reportType'];\n\n    this.isUniformMerchant = this.menuType == MenuTypeEnum.Uniform;\n    this.isEventReport = this.menuType == MenuTypeEnum.Event;\n\n    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {\n      if (state.selected) {\n        this.merchantHasManySchools = state.selected.Schools && state.selected.Schools.length > 1;\n      }\n    });\n  }\n\n  ngOnDestroy(): void {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n  GetReport(request: ReportRequest) {\n    this.spinnerService.animatedStart();\n    this.selectedDate = moment(request.Date).toDate();\n    request.MenuType = null;\n    request.ClassIds = request.ClassIds;\n\n    if (this.menuType == MenuTypeEnum.Uniform || this.menuType == MenuTypeEnum.Event) {\n      request.MenuType = this.menuType;\n    }\n\n    this.orderAPIService.GetReportClassOrdersBySchoolAPI(request).subscribe({\n      next: (response: ClassReport[]) => {\n        this.ResetSchoolBreak();\n        this.orderPerSchools = [];\n\n        this.listReportClass = response;\n        this.orderClassCount = 0;\n        this.noResult = this.listReportClass == null || this.listReportClass.length == 0;\n\n        if (!this.noResult) {\n          this.listReportClass.forEach(classe => {\n            this.orderClassCount += classe.orders.length;\n\n            if(this.orderPerSchools[classe.school] >= 0){\n              this.orderPerSchools[classe.school] += classe.orders.length;\n            }else{\n              this.orderPerSchools[classe.school] = classe.orders.length;\n            }\n          });\n        }\n        this.spinnerService.animatedStop();\n        this.cd.markForCheck();\n      },\n      error: error => {\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  ExportCSV() {\n    const listItems: CsvEventsReports[] = [];\n\n    this.listReportClass.forEach(record => {\n      // each record is a class\n      record.orders.forEach(order => {\n        let item = new CsvEventsReports();\n        item.Class = record.class;\n        item.Customer = order.studentName;\n        item.MenuType = order.menuType;\n        item.OrderId = order.orderId;\n        item.OrderDetails = '';\n        // Menu Item\n        order.items.map(menuitem => {\n          item.OrderDetails += menuitem.quantity + 'x' + menuitem.name;\n\n          // options\n          if (menuitem.selectedOptions != null && menuitem.selectedOptions != '') {\n            item.OrderDetails += '\\n' + '(' + menuitem.selectedOptions + ')';\n          }\n        });\n        listItems.push(item);\n      });\n    });\n\n    const options = {\n      fieldSeparator: ',',\n      quoteStrings: '\"',\n      decimalSeparator: '.',\n      showLabels: true,\n      showTitle: true,\n      title: 'Export CSV',\n      useTextFile: false,\n      useBom: true,\n      useKeysAsHeaders: true,\n      // headers: ['Column 1', 'Column 2', etc...] <-- Won't work with useKeysAsHeaders present!\n    };\n\n    const csvExporter = new ExportToCsv(options);\n\n    csvExporter.generateCsv(listItems);\n  }\n\n  SchoolChangedBreak(school: string){\n    if(this.schoolNameToDisplayForPrint == null){\n      this.schoolNameToDisplayForPrint = school;\n      return false;\n    }else if(this.schoolNameToDisplayForPrint == school){\n      return false;\n    }else{\n      this.schoolNameToDisplayForPrint = school;\n      return true;\n    }\n  }\n\n  SchoolShowTitle(school: string){\n    if(!this.merchantHasManySchools){\n      return false;\n    }\n\n    if(this.schoolNameToShowTitle == null){\n      this.schoolNameToShowTitle = school;\n      return true;\n    }else if(this.schoolNameToShowTitle == school){\n      return false;\n    }else{\n      this.schoolNameToShowTitle = school;\n      return true;\n    }\n  }\n\n  ResetSchoolBreak(){\n    this.schoolNameToDisplayForPrint = null;\n  }\n\n  GetOrdersPerSchools(school: string){\n    return this.orderPerSchools[school];\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"col-6\">\n    <p class=\"\">This report shows all orders for the selected date.</p>\n  </div>\n\n  <div class=\"container-fluid\">\n    <report-form\n      (request)=\"GetReport($event)\"\n      [showClasses]=\"true\"\n      [showStartDate]=\"!isUniformMerchant\"\n    ></report-form>\n\n    <!-- order filter for uniforms -->\n    <div class=\"col-12\" *ngIf=\"isUniformMerchant\">\n      <h4>Filters</h4>\n      <canteen-order-type-filter merchantType=\"Uniform\" [forReports]=\"true\"></canteen-order-type-filter>\n    </div>\n\n    <!-- vertical spacing -->\n    <div class=\"col-12 report-vertical-spacing\"></div>\n\n    <div *ngIf=\"listReportClass && listReportClass.length > 0\" class=\"col-12 print-button-spacing\">\n      <div class=\"row justify-content-end\">\n        <div *ngIf=\"merchantHasManySchools\" class=\"col-12 col-sm-4 col-md-2\">\n          <p *ngIf=\"orderClassCount\" class=\"totalOrdersReport\">{{ orderClassCount }} Orders</p>\n        </div>\n        <div *ngIf=\"isEventReport\" class=\"col-12 col-sm-4 col-md-2\">\n          <button class=\"PrimaryButton smaller\" type=\"button\" (click)=\"ExportCSV()\">CSV</button>\n        </div>\n        <div class=\"col-12 col-sm-4 col-md-2\">\n          <button\n            (click)=\"ResetSchoolBreak()\"\n            class=\"PrimaryButton smaller\"\n            type=\"button\"\n            printSectionId=\"print-report\"\n            [useExistingCss]=\"true\"\n            ngxPrint\n          >\n            Print\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <div id=\"print-report\">\n      <div class=\"sectionReport\">\n\n        <report-heading\n        title=\"Class report\"\n        [selectedDate]=\"!isUniformMerchant && !merchantHasManySchools && selectedDate\"\n        [noResult]=\"noResult\"\n        [orderCount]=\"!merchantHasManySchools ? orderClassCount : null\"\n        ></report-heading>\n\n        <div *ngFor=\"let classe of listReportClass\">\n \n          <!-- Printing page break after each school-->\n          <div *ngIf=\"SchoolChangedBreak(classe.school)\" style=\"page-break-before: always\"></div>\n\n          <div *ngIf=\"SchoolShowTitle(classe.school)\" style=\"margin-top: 20px;\">\n            <report-heading\n              [title]=\"classe.school\"\n              [selectedDate]=\"!isUniformMerchant && selectedDate\"\n              [noResult]=\"noResult\"\n              [orderCount]=\"GetOrdersPerSchools(classe.school)\"\n            ></report-heading>\n          </div>\n\n\n          <p class=\"menuType smaller\">\n            {{ classe.class }}\n            <span *ngIf=\"merchantHasManySchools\" class=\"schoolName\">({{ classe.school }})</span>\n          </p>\n \n          <table class=\"report\">\n            <tr class=\"border header\">\n              <th *ngIf=\"isEventReport\" class=\"colWidth10\">Order ID</th>\n              <th class=\"colWidth20\">Customer</th>\n              <th class=\"colWidth10\">Menu</th>\n              <th *ngIf=\"isUniformMerchant\" class=\"colWidth10\">Status</th>\n              <th class=\"colWidth35\">Order Details</th>\n              <th *ngIf=\"!isEventReport\" class=\"colWidth10\">Order ID</th>\n            </tr>\n            <tr *ngFor=\"let order of classe.orders\" class=\"border\">\n              <td *ngIf=\"isEventReport\">\n                {{ order.orderId }}\n              </td>\n              <td>{{ order.studentName }}</td>\n              <td>{{ order.menuType | merchantMenuName }}</td>\n              <td *ngIf=\"isUniformMerchant\">\n                {{ order.canteenStatus }}\n              </td>\n              <td>\n                <ul class=\"orderDetail\">\n                  <li *ngFor=\"let item of order.items\">\n                    {{ item.quantity }} x {{ item.name }}\n                    <span *ngIf=\"item.selectedOptions\">({{ item.selectedOptions }})</span>\n                  </li>\n                </ul>\n              </td>\n              <td *ngIf=\"!isEventReport\">\n                {{ order.runNumber }}\n              </td>\n            </tr>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { CanteenFilters, CategoryEditor, MenuPickerOptions, ReportRequest } from 'src/app/sharedModels';\nimport { CanteenService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'report-form-and-filters',\n  templateUrl: './report-form-and-filters.component.html',\n  styleUrls: ['./report-form-and-filters.component.scss'],\n})\nexport class ReportFormAndFiltersComponent implements OnInit {\n  @Output() getReport: EventEmitter<ReportRequest> = new EventEmitter();\n  @Output() menuTypeChange: EventEmitter<MenuPickerOptions> = new EventEmitter();\n  @Output() categoryChange: EventEmitter<number[]> = new EventEmitter();\n  @Input() menuCategories: CategoryEditor[];\n  @Input() merchantType: string;\n  @Input() showClassPicker: boolean = true;\n  canteenFilters: CanteenFilters;\n  menuData: MenuPickerOptions = new MenuPickerOptions();\n  menuForm: FormGroup;\n\n  constructor(private canteenService: CanteenService) {}\n\n  ngOnInit(): void {\n    this._createMenuTypeForm();\n  }\n\n  getReportEvent(reportRequest: ReportRequest) {\n    this.getReport.emit(reportRequest);\n  }\n\n  categoriesChangedEvent(categories: number[]) {\n    this.categoryChange.emit(categories);\n  }\n\n  _createMenuTypeForm() {\n    this.canteenFilters = this.canteenService.GetFilters();\n\n    this.menuForm = new FormGroup({\n      recess: new FormControl(this.canteenFilters.Recess),\n      lunch: new FormControl(this.canteenFilters.Lunch),\n      event: new FormControl(this.canteenFilters.Event),\n    });\n\n    this.menuForm.valueChanges.subscribe(res => {\n      this.canteenFilters.Recess = Boolean(res.recess);\n      this.canteenFilters.Lunch = Boolean(res.lunch);\n      this.canteenFilters.Event = Boolean(res.event);\n      this.menuData.Recess = Boolean(res.recess);\n      this.menuData.Lunch = Boolean(res.lunch);\n      this.menuData.Event = Boolean(res.event);\n\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.menuTypeChange.emit(this.menuData);\n    });\n  }\n}\n", "<div class=\"row mt-3\">\n  <div class=\"col-12\">\n    <report-form\n      [showClasses]=\"showClassPicker\"\n      [showStartDate]=\"true\"\n      (request)=\"getReportEvent($event)\"\n    ></report-form>\n  </div>\n</div>\n\n<!-- Menu Filters-->\n<div class=\"row mt-3\">\n  <div class=\"col-12\" *ngIf=\"menuForm\">\n    <canteen-order-type-filter\n      [merchantType]=\"merchantType\"\n      [(formGroup)]=\"menuForm\"\n    ></canteen-order-type-filter>\n  </div>\n</div>\n\n<!-- Item categories Filters-->\n<div *ngIf=\"menuCategories\" class=\"row mt-3\">\n  <div class=\"col-12\">\n    <canteen-order-category-filter\n      [categories]=\"menuCategories\"\n      (categoriesSelected)=\"categoriesChangedEvent($event)\"\n      rowExpanded=\"true\"\n    ></canteen-order-category-filter>\n  </div>\n</div>\n", "import {\n  Component,\n  OnInit,\n  Input,\n  Output,\n  EventEmitter,\n  OnDestroy,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as moment from 'moment';\n\n// Models\nimport {\n  BaseComponent,\n  ReportRequest,\n  ListClasses,\n  CanteenStatusEnum,\n  MerchantTypeEnum,\n} from 'src/app/sharedModels';\n\n// Services\nimport { OrderStatusService, SchoolClassesService, SpinnerService } from 'src/app/sharedServices';\n\n// ngrx\nimport { select, Store } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport { CanteenState } from '../../../states';\nimport { canteenStateSelector } from 'src/app/states/canteen/canteen.selectors';\nimport { formatDateToUniversal, UNIVERSAL_DATE_FORMAT } from 'src/app/utility';\n\n@Component({\n  selector: 'report-form',\n  templateUrl: './report-form.component.html',\n  styleUrls: ['./report-form.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportFormComponent extends BaseComponent implements OnInit, OnDestroy {\n  @Input() showEndDate: boolean = false;\n  @Input() showStartDate: boolean = true;\n  @Input() showClasses: boolean = false;\n  @Output() request = new EventEmitter<ReportRequest>();\n\n  selectedSchools: string[];\n  selectedMerchantId: number;\n  form: FormGroup;\n  listClass: ListClasses;\n  trackOrderFilter: Subscription;\n  private defaultValueClasses: number[] = [];\n  private subscription: Subscription;\n  isUniformMerchant: boolean;\n  hasMultipleSchools: boolean;\n  maxDate: Date;\n  activeCanteenStatuses: string[] = [\n    CanteenStatusEnum.Processing,\n    CanteenStatusEnum.Ready,\n    CanteenStatusEnum.Completed,\n    CanteenStatusEnum.New,\n  ];\n\n  constructor(\n    private classService: SchoolClassesService,\n    private spinnerService: SpinnerService,\n    private cd: ChangeDetectorRef,\n    private store: Store<{ canteen: CanteenState }>,\n    private orderStatusService: OrderStatusService\n  ) {\n    super();\n  }\n\n  ngOnInit(): void {\n    this._createForm();\n\n    this.trackOrderFilter = this.orderStatusService.statusFilterUpdatedEvent$.subscribe((res: string[]) => {\n      this.activeCanteenStatuses = res;\n      this.LoadReport();\n    });\n\n    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {\n      this.processCanteenStateUpdate(state);\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.subscription?.unsubscribe();\n  }\n\n  processCanteenStateUpdate(state: CanteenState) {\n    const merchantHasSchools = state?.selected?.Schools?.length > 0;\n    if (merchantHasSchools) {\n      this.isUniformMerchant = Boolean(state.selected.CanteenType === MerchantTypeEnum.Uniform);\n      this.selectedMerchantId = state.selected.CanteenId;\n      this.hasMultipleSchools = state.selected.Schools.length > 1;\n    }\n  }\n\n  get classes() {\n    return this.form.get('classes');\n  }\n\n  get startDate() {\n    return this.form.get('startDate');\n  }\n\n  get endDate() {\n    return this.form.get('endDate');\n  }\n\n  /** Form creation */\n  private _createForm(): void {\n    let startDateDefaultValue = moment().toDate();\n\n    if (this.showEndDate) {\n      startDateDefaultValue = moment().startOf('month').toDate();\n      this.maxDate = this.getMaxDate(startDateDefaultValue);\n    }\n    this.form = new FormGroup({\n      classes: new FormControl(),\n      startDate: new FormControl(startDateDefaultValue, [Validators.required]),\n      endDate: new FormControl(moment().endOf('month').toDate(), [Validators.required]),\n    });\n\n    this.startDate.valueChanges.subscribe(res => {\n      this.maxDate = this.getMaxDate(this.startDate.value);\n    });\n\n    this.classes.valueChanges.subscribe(val => {\n      this._SaveClassesInLocalStorage(val);\n    });\n\n    this.LoadClass();\n  }\n\n  /** Triggered when School picker input changes */\n  schoolSelectionChange(schoolList: string[]): void {\n    this.selectedSchools = schoolList;\n    this.LoadClass();\n  }\n\n  /** Save the class list in localStorage */\n  private _SaveClassesInLocalStorage(classes: number[]): void {\n    localStorage.setItem('prefClassIds', JSON.stringify(classes));\n  }\n\n  private LoadClass(): void {\n    const oneSchoolSelected = this.selectedSchools?.length === 1;\n    if (!this.showClasses || !oneSchoolSelected) {\n      this.listClass = null;\n      return;\n    }\n\n    this.spinnerService.start();\n    const selectedSchoolId = parseInt(this.selectedSchools[0]);\n    this.classService.GetClassesBySchoolAPI(selectedSchoolId, false).subscribe({\n      next: (response: ListClasses) => {\n        this.processClassListResponse(response);\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.spinnerService.stop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  processClassListResponse(response: ListClasses): void {\n    this.listClass = response;\n    this.defaultValueClasses = this.getPreferedClassIds();\n    this.classes.setValue(this.defaultValueClasses);\n    this.cd.markForCheck();\n  }\n\n  getPreferedClassIds(): number[] {\n    let savedValueClass: number[] = JSON.parse(localStorage.getItem('prefClassIds'));\n    const hasValidSavedClassValue = savedValueClass != null && savedValueClass.length > 0;\n    const useSavedClasses = !this.hasMultipleSchools && hasValidSavedClassValue;\n\n    return useSavedClasses ? savedValueClass : this.getArrayOfAllClassIds(this.listClass);\n  }\n\n  LoadReport(): void {\n    if (!this.form.valid && this.selectedSchools?.length <= 0) {\n      return;\n    }\n    const request: ReportRequest = this.getReportRequest();\n    this.request.emit(request);\n  }\n\n  getReportRequest(): ReportRequest {\n    const request = new ReportRequest();\n    request.MerchantId = this.selectedMerchantId;\n    request.SchoolsIds = this.selectedSchools.toString();\n    request.ClassIds = this.getClassIdStringForRequest();\n    request.Date = formatDateToUniversal(this.startDate.value);\n    request.EndDate = formatDateToUniversal(this.endDate.value);\n    request.CanteenStatuses = this.isUniformMerchant ? this.activeCanteenStatuses : null;\n    return request;\n  }\n\n  getClassIdStringForRequest(): string {\n    const useClassIds = this.selectedSchools.length === 1 && this.classes.value?.length > 0;\n    return useClassIds ? this.classes.value.toString() : null;\n  }\n\n  toggleAllClasses(): void {\n    const oneOrLessClassesSelected = this.classes.value.length <= 1;\n    let values = oneOrLessClassesSelected ? this.getArrayOfAllClassIds(this.listClass) : [];\n    this.classes.setValue(values);\n  }\n\n  getArrayOfAllClassIds(classList: ListClasses): number[] {\n    return classList.Classes.map(s => s.ClassId);\n  }\n\n  getMaxDate(startDate: Date): Date {\n    return moment(startDate).add(1, 'month').subtract(1, 'day').toDate();\n  }\n}\n", "<form *ngIf=\"form\" [formGroup]=\"form\" class=\"report-form-container\">\n  <div class=\"row form-section\">\n    <div class=\"col-lg-9 col-md-12\">\n      <div class=\"row\">\n        <div class=\"col-md-3 col-lg-4\">\n          <multi-school-select-list\n            (selectedSchoolChanged)=\"schoolSelectionChange($event)\"\n          ></multi-school-select-list>\n        </div>\n        <div *ngIf=\"showClasses\" class=\"col-md-3 col-lg-4\">\n          <ng-container *ngIf=\"listClass && selectedSchools.length > 0\">\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Classes</mat-label>\n              <mat-select [formControl]=\"classes\" multiple>\n                <mat-option *ngFor=\"let class of listClass.Classes\" [value]=\"class.ClassId\">{{\n                  class.Name\n                }}</mat-option>\n              </mat-select>\n            </mat-form-field>\n            <div class=\"selectAllLink\">\n              <a (click)=\"toggleAllClasses()\">Select/Unselect All</a>\n            </div>\n          </ng-container>\n        </div>\n        <div *ngIf=\"showStartDate\" class=\"col-md-3 col-lg-4\">\n          <input-date placeholder=\"Start date\" formControlName=\"startDate\"></input-date>\n        </div>\n        <div *ngIf=\"showEndDate\" class=\"col-md-3 col-lg-4\">\n          <input-date placeholder=\"End date\" formControlName=\"endDate\" [maxDate]=\"maxDate\"></input-date>\n          <mat-error *ngIf=\"endDate.invalid\">Time period selected cannot exceed 1 month</mat-error>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"col-md-3 col-lg-3\">\n      <button\n        *ngIf=\"selectedSchools.length >= 1\"\n        class=\"PrimaryButton smaller\"\n        type=\"button\"\n        (click)=\"LoadReport()\"\n      >\n        Load\n      </button>\n    </div>\n  </div>\n</form>\n", "import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input } from '@angular/core';\n\n@Component({\n  selector: 'report-heading',\n  templateUrl: './report-heading.component.html',\n  styleUrls: ['./report-heading.component.scss'],\n  standalone: true,\n  imports: [CommonModule],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportHeadingComponent {\n  @Input() noResult: boolean;\n  @Input({ required: true }) title: string;\n  @Input() selectedDate: string;\n  @Input() selectedCanteenName: string;\n  @Input() orderCount: number;\n}\n", "<div class=\"row\">\n  <div class=\"col-6\">\n    <p class=\"menuType\">\n      {{ title }}\n      <span *ngIf=\"selectedCanteenName\">- {{ selectedCanteenName }}</span>\n      <span *ngIf=\"selectedDate\"> - {{ selectedDate | date : 'EEEE dd/MM/yyyy' }}</span>\n    </p>\n\n    <p *ngIf=\"noResult\" class=\"mt-0\">No Results</p>\n  </div>\n  <div *ngIf=\"orderCount || orderCount === 0\" class=\"col-6\">\n    <p class=\"menuType totalOrders\">{{ orderCount }} Orders</p>\n  </div>\n</div>\n", "import { Component, OnInit, Input, OnChanges, SimpleChanges, OnDestroy } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { Category, MenuItem, Option, ReportRequest } from 'src/app/sharedModels';\nimport { animate, state, style, transition, trigger } from '@angular/animations';\nimport { Subject, Observable, Subscription } from 'rxjs';\n\n//service\nimport { ItemReportPrintService } from '../item-report-print-service/item-report-print.service';\n\nconst _columns = ['quantity', 'itemName', 'modifiers', 'orderIds', 'expandArrow'];\n\n@Component({\n  selector: 'report-items-category-table',\n  templateUrl: './report-items-category-table.component.html',\n  styleUrls: ['./report-items-category-table.component.scss'],\n  animations: [\n    trigger('detailExpand', [\n      state('collapsed', style({ height: '0px', minHeight: '0' })),\n      state('expanded', style({ height: '*' })),\n      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),\n    ]),\n  ],\n})\nexport class ReportItemsCategoryTableComponent implements OnInit, OnChanges, OnDestroy {\n  @Input() categoryData: Category;\n  @Input() reportRequest: ReportRequest;\n  @Input() menuType: string;\n  expandedElement: MenuItem;\n  displayedColumns = _columns;\n  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];\n  dataSource = new MatTableDataSource<MenuItem>();\n  accordionCloseSubscription: Subscription;\n\n  /* Subject to trigger loading the student table (in accordion row)*/\n  loadStudents: Subject<string> = new Subject<string>();\n\n  constructor(private itemReportPrintService: ItemReportPrintService) {}\n\n  ngOnInit(): void {\n    if (this.categoryData) {\n      this.dataSource.data = this.categoryData.item;\n    }\n\n    this.accordionCloseSubscription = this.itemReportPrintService.closeAccordionRowEvent$.subscribe(() => {\n      this.expandedElement = null;\n      this.itemReportPrintService.AddToAccordionRowCloseCount();\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes?.categoryData?.currentValue) {\n      this.dataSource = changes.categoryData.currentValue.item;\n    }\n  }\n\n  ngOnDestroy(): void {\n    if (this.accordionCloseSubscription) {\n      this.accordionCloseSubscription.unsubscribe();\n    }\n  }\n\n  /**\n   * Called when a table row is clicked\n   * This function triggers the accordion row to be opened\n   */\n  itemRowClick(element: MenuItem) {\n    this.expandedElement = this.expandedElement === element ? null : element;\n    if (this.expandedElement) {\n      //create unique id for the selected table row\n      let idString = this.expandedElement.MenuItemId.toString() + this.expandedElement.RunNumbers;\n      this.loadStudents.next(idString);\n    }\n  }\n\n  /**\n   * Returns array of selected options\n   * @param item each report menu item\n   */\n  GetOptionsItem(item: MenuItem) {\n    return item?.Options[0]?.SubOptions?.length > 0 ? item.Options[0].SubOptions : [];\n  }\n}\n", "<table *ngIf=\"dataSource\" mat-table [dataSource]=\"dataSource\" multiTemplateDataRows class=\"mt-3\">\n  <ng-container matColumnDef=\"quantity\">\n    <th mat-header-cell *matHeaderCellDef class=\"pl-3\">Quantity</th>\n    <td mat-cell *matCellDef=\"let element\">{{ element.Quantity }} x</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"itemName\">\n    <th mat-header-cell *matHeaderCellDef class=\"categ-header\">{{ categoryData.CatName }}</th>\n    <td mat-cell *matCellDef=\"let element\">{{ element.Name }}</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"modifiers\">\n    <th mat-header-cell *matHeaderCellDef>Modifiers</th>\n    <td mat-cell *matCellDef=\"let element\">\n      <span *ngFor=\"let option of GetOptionsItem(element)\">{{ option.OptionName }}, </span>\n    </td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"orderIds\">\n    <th mat-header-cell *matHeaderCellDef>Order Ids</th>\n    <td mat-cell *matCellDef=\"let element\">{{ element.RunNumbers }}</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"expandArrow\">\n    <th mat-header-cell *matHeaderCellDef aria-label=\"row actions\">&nbsp;</th>\n    <td mat-cell *matCellDef=\"let element\">\n      <button mat-icon-button aria-label=\"expand row\">\n        <mat-icon *ngIf=\"expandedElement !== element\">keyboard_arrow_down</mat-icon>\n        <mat-icon *ngIf=\"expandedElement === element\">keyboard_arrow_up</mat-icon>\n      </button>\n    </td>\n  </ng-container>\n\n  <!-- Drop down Content -->\n  <ng-container matColumnDef=\"expandedDetail\">\n    <th mat-header-cell *matHeaderCellDef aria-label=\"row actions\">&nbsp;</th>\n    <td mat-cell *matCellDef=\"let element\" [attr.colspan]=\"columnsToDisplayWithExpand.length\">\n      <div class=\"accordion-content\" [@detailExpand]=\"element == expandedElement ? 'expanded' : 'collapsed'\">\n        <item-student-list-table\n          style=\"width: 100%\"\n          [loadStudents]=\"loadStudents.asObservable()\"\n          [menuItemId]=\"element.MenuItemId\"\n          [reportRequest]=\"reportRequest\"\n          [menuType]=\"menuType\"\n          [orderIds]=\"element.RunNumbers\"\n          [studentIds]=\"element.StudentIds\"\n        ></item-student-list-table>\n      </div>\n    </td>\n  </ng-container>\n\n  <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n  <tr\n    mat-row\n    *matRowDef=\"let element; columns: displayedColumns\"\n    class=\"item-table-row\"\n    [ngClass]=\"{ 'expanded-row-background': expandedElement === element }\"\n    (click)=\"itemRowClick(element)\"\n  ></tr>\n\n  <!-- Drop down Content Row -->\n  <tr mat-row *matRowDef=\"let row; columns: ['expandedDetail']\" class=\"accordion-content-row\"></tr>\n</table>\n", "import { Component, Input, OnInit, SimpleChanges } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\n\n// models\nimport { Category, MenuTypeEnum, ReportRequest } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'report-items-filter-table',\n  templateUrl: './report-items-filter-table.component.html',\n  styleUrls: ['../report.scss'],\n})\nexport class ReportItemsFilterTableComponent implements OnInit {\n  @Input() menuType: MenuTypeEnum;\n  @Input() selectedDate: Date;\n  @Input() selectedCanteenName: string;\n  @Input() listCategories: Category[];\n  @Input() reportRequest: ReportRequest;\n  /** page break is true if there is another report-items-filter-table stacked below the current component  */\n  @Input() pageBreak: boolean;\n  dataSource = new MatTableDataSource<Category>();\n\n  constructor() {}\n\n  ngOnInit(): void {\n    if (this.reportRequest && this.menuType) {\n      this.reportRequest.MenuType = this.menuType;\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if (changes?.reportRequest?.currentValue) {\n      this.reportRequest.MenuType = this.menuType;\n    }\n  }\n\n  ngOnDestroy(): void {}\n}\n", "<div class=\"sectionReport pb-4\">\n  <report-heading\n    [title]=\"menuType | merchantMenuName\"\n    [selectedCanteenName]=\"selectedCanteenName\"\n    [selectedDate]=\"selectedDate\"\n    [noResult]=\"listCategories?.length == 0\"\n  ></report-heading>\n\n  <report-items-category-table\n    *ngFor=\"let category of listCategories\"\n    [categoryData]=\"category\"\n    [reportRequest]=\"reportRequest\"\n    [menuType]=\"menuType\"\n  ></report-items-category-table>\n\n  <!-- Printing page break -->\n  <div *ngIf=\"pageBreak\" style=\"page-break-before: always\"></div>\n</div>\n", "import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\n\n// models\nimport { Category, MenuItem, MenuTypeEnum, Option } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'report-items-table',\n  templateUrl: './report-items-table.component.html',\n  styleUrls: ['../report.scss'],\n})\nexport class ReportItemsTableComponent implements OnInit {\n  @Input() menuType: MenuTypeEnum;\n  @Input() selectedDate: Date;\n  @Input() selectedCanteenName: string;\n  @Input() listCategories: Category[];\n  @Input() isUniformMerchant: boolean = false;\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  /**\n   * Returns array of selected options\n   * @param item each report menu item\n   */\n  GetOptionsItem(item: MenuItem) {\n    return item?.Options[0]?.SubOptions?.length > 0 ? item.Options[0].SubOptions : [];\n  }\n}\n", "<div class=\"sectionReport\">\n  <report-heading\n    [title]=\"menuType | merchantMenuName\"\n    [selectedCanteenName]=\"selectedCanteenName\"\n    [selectedDate]=\"selectedDate\"\n    [noResult]=\"listCategories?.length == 0\"\n  ></report-heading>\n\n  <table *ngFor=\"let category of listCategories\" class=\"report\">\n    <tr>\n      <th class=\"border quantity\">Quantity</th>\n      <th class=\"border category\">{{ category.CatName }}</th>\n      <th *ngIf=\"isUniformMerchant\" class=\"border status\">Status</th>\n      <th class=\"border modifiers\">Modifiers</th>\n      <th class=\"border orderIds\">Order IDs</th>\n    </tr>\n    <tr *ngFor=\"let item of category.item\">\n      <td class=\"border\">{{ item.Quantity }} x</td>\n      <td class=\"border\">{{ item.Name }}</td>\n      <td *ngIf=\"isUniformMerchant\" class=\"border\">{{ item.CanteenStatus }}</td>\n      <td class=\"border\">\n        <span *ngFor=\"let option of GetOptionsItem(item)\">{{ option.OptionName }}, </span>\n      </td>\n      <td class=\"border\">{{ item.RunNumbers }}</td>\n    </tr>\n  </table>\n</div>\n", "import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild, ElementRef } from '@angular/core';\nimport * as moment from 'moment';\n\n//ngrx\nimport { Subscription } from 'rxjs';\nimport { Store } from '@ngrx/store';\nimport { CanteenState } from 'src/app/states';\n\n// Models\nimport { ReportRequest, Category, MenuTypeEnum, MenuPickerOptions } from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService, CanteenService } from '../../../sharedServices';\nimport { ItemReportPrintService } from '../item-report-print-service/item-report-print.service';\n\nimport { ReportFormComponent } from '../report-form/report-form.component';\nimport { BaseReportComponent } from '../base-report/base-report.component';\n\n@Component({\n  selector: 'report-items',\n  templateUrl: './report-items.component.html',\n  styleUrls: ['../report.scss'],\n})\nexport class ReportItemsComponent extends BaseReportComponent implements OnInit, On<PERSON><PERSON>roy {\n  @ViewChild(ReportFormComponent, { static: false }) reportComponent: ReportFormComponent;\n  @ViewChild('printBtn') printBtn: ElementRef<HTMLElement>;\n\n  private printSubscription: Subscription;\n\n  reportLoadCount: number = 0;\n  reportMenuCount: number = 3;\n  eventMenuCount: number = 1;\n\n  /*holds data received from api*/\n  listCategoriesLunch: Category[];\n  listCategoriesRecess: Category[];\n  listCategoriesEvent: Category[];\n\n  /*data that has been filtered by selected categories*/\n  filteredLunchList: Category[];\n  filteredRecessList: Category[];\n  filteredEventList: Category[];\n\n  constructor(\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService,\n    store: Store<{ canteen: CanteenState }>,\n    private itemReportPrintService: ItemReportPrintService,\n    protected canteenService: CanteenService\n  ) {\n    super(store, canteenService);\n  }\n\n  ngOnInit() {\n    this.initMenuPicker();\n\n    this.getMerchantInfo();\n\n    if (this.isEventMerchant) {\n      this.reportMenuCount = this.eventMenuCount;\n    }\n\n    this.printSubscription = this.itemReportPrintService.triggerPrintEvent$.subscribe(res => {\n      this.print();\n    });\n  }\n\n  ngOnDestroy(): void {\n    if (this.printSubscription) {\n      this.printSubscription.unsubscribe();\n    }\n    this.baseReportOnDestroy();\n  }\n\n  prepareReportRequest(request: ReportRequest) {\n    this.reportRequest = request;\n    this.reportLoadCount = 0;\n\n    this.selectedDate = moment(request.Date).toDate();\n    this.selectedCanteenName = request.selectedCanteenName;\n\n    if (!this.isEventMerchant) {\n      this._getReport({ ...request, MenuType: MenuTypeEnum.Lunch });\n      this._getReport({ ...request, MenuType: MenuTypeEnum.Recess });\n    }\n\n    this._getReport({ ...request, MenuType: MenuTypeEnum.Event });\n  }\n\n  private _getReport(request: ReportRequest) {\n    this.spinnerService.animatedStart();\n\n    this.orderAPIService.GetReportOrdersByMenuBySchoolAPI(request).subscribe({\n      next: (response: Category[]) => {\n        switch (request.MenuType) {\n          case MenuTypeEnum.Recess:\n            this.listCategoriesRecess = response;\n            break;\n\n          case MenuTypeEnum.Lunch:\n            this.listCategoriesLunch = response;\n            break;\n\n          case MenuTypeEnum.Event:\n            this.listCategoriesEvent = response;\n            break;\n\n          default:\n            break;\n        }\n        this.getFilteredResults(false);\n        this.spinnerService.animatedStop();\n      },\n      error: error => {\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  /////////////////////////\n  // Menu/Category filters\n  ////////////////////////\n\n  categoriesSelectionChanged(categories: number[]) {\n    this.selectedCategoryNames = [];\n    this.menuCategories.forEach(categ => {\n      if (categories.includes(categ.MenuCategoryId)) {\n        this.selectedCategoryNames.push(categ.CategoryName);\n        return;\n      }\n    });\n    this.getFilteredResults(true);\n  }\n\n  menuSelectionChanged(menu: MenuPickerOptions) {\n    this.menuPicker = menu;\n    this.getSumOfReportCategories();\n  }\n\n  /**\n   * Filter all 3 sets of data to remove any of the unselected categories\n   */\n  getFilteredResults(filterChanged: boolean) {\n    this.reportLoadCount++;\n    if (this.reportLoadCount != this.reportMenuCount && !filterChanged) {\n      return;\n    }\n    this.filteredRecessList = this.selectedCategories(this.listCategoriesRecess);\n    this.filteredLunchList = this.selectedCategories(this.listCategoriesLunch);\n    this.filteredEventList = this.selectedCategories(this.listCategoriesEvent);\n\n    this.getSumOfReportCategories();\n  }\n\n  /**\n   * Get the sum of all visible report categories\n   */\n  getSumOfReportCategories() {\n    this.itemReportPrintService.resetCategorySum();\n    this.itemReportPrintService.getSumOfReportCategories(this.menuPicker.Recess, this.filteredRecessList);\n    this.itemReportPrintService.getSumOfReportCategories(this.menuPicker.Lunch, this.filteredLunchList);\n    this.itemReportPrintService.getSumOfReportCategories(this.menuPicker.Event, this.filteredEventList);\n  }\n\n  /**\n   * Remove all unselected categories from a data list\n   * @param list\n   */\n  selectedCategories(list: Category[]) {\n    if (!list) {\n      return [];\n    }\n    return list.filter((el: Category) => {\n      return this.selectedCategoryNames.includes(el.CatName);\n    });\n  }\n\n  /////////////////////////\n  // Printing\n  ////////////////////////\n\n  /**\n   * Called when the print button is pressed\n   */\n  closeReportRowsBeforePrint() {\n    this.spinnerService.start();\n    this.itemReportPrintService.closeAllReportAccordionRows();\n    this.itemReportPrintService.triggerPrint();\n  }\n\n  print() {\n    setTimeout(() => {\n      //set timeout to ensure the accordion row transition has time to complete before\n      //printing is triggered\n      this.printBtn.nativeElement.click();\n      this.spinnerService.stop();\n    }, 400);\n  }\n\n  showPrintButton() {\n    return (\n      this.listCategoriesLunch?.length > 0 ||\n      this.listCategoriesRecess?.length > 0 ||\n      this.listCategoriesEvent?.length > 0\n    );\n  }\n}\n", "<div class=\"container-fluid\">\n  <report-form-and-filters\n    [menuCategories]=\"menuCategories\"\n    [merchantType]=\"merchantType\"\n    (getReport)=\"prepareReportRequest($event)\"\n    (categoryChange)=\"categoriesSelectionChanged($event)\"\n    (menuTypeChange)=\"menuSelectionChanged($event)\"\n  ></report-form-and-filters>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div *ngIf=\"showPrintButton()\" class=\"col-12 print-button-spacing\">\n    <div class=\"row justify-content-end\">\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <!-- hidden print button -->\n        <button\n          #printBtn\n          style=\"display: none\"\n          type=\"button\"\n          printSectionId=\"print-report\"\n          [useExistingCss]=\"true\"\n          ngxPrint\n        >\n          Print\n        </button>\n        <!-- visible print button -->\n        <button (click)=\"closeReportRowsBeforePrint()\" class=\"PrimaryButton smaller\" type=\"button\">\n          Print\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div id=\"print-report\">\n    <report-items-filter-table\n      *ngIf=\"!isEventMerchant && menuPicker.Recess\"\n      menuType=\"Recess\"\n      [selectedDate]=\"selectedDate\"\n      [selectedCanteenName]=\"selectedCanteenName\"\n      [listCategories]=\"filteredRecessList\"\n      [reportRequest]=\"reportRequest\"\n      [pageBreak]=\"menuPicker.Lunch || menuPicker.Event\"\n    ></report-items-filter-table>\n\n    <report-items-filter-table\n      *ngIf=\"!isEventMerchant && menuPicker.Lunch\"\n      menuType=\"Lunch\"\n      [selectedDate]=\"selectedDate\"\n      [selectedCanteenName]=\"selectedCanteenName\"\n      [listCategories]=\"filteredLunchList\"\n      [reportRequest]=\"reportRequest\"\n      [pageBreak]=\"menuPicker.Event\"\n    ></report-items-filter-table>\n\n    <report-items-filter-table\n      *ngIf=\"menuPicker.Event\"\n      menuType=\"Event\"\n      [selectedDate]=\"selectedDate\"\n      [selectedCanteenName]=\"selectedCanteenName\"\n      [listCategories]=\"filteredEventList\"\n      [reportRequest]=\"reportRequest\"\n      [pageBreak]=\"false\"\n    ></report-items-filter-table>\n  </div>\n</div>\n", "import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';\nimport * as moment from 'moment';\n\n// Models\nimport { ReportRequest, BaseComponent, ClassReport } from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService } from '../../../sharedServices';\n\n@Component({\n  selector: 'report-printedcancelled',\n  templateUrl: './report-printedcancelled.component.html',\n  styleUrls: ['./report-printedcancelled.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportPrintedCancelledComponent extends BaseComponent implements OnInit {\n  listReportClass: ClassReport[] = [];\n  selectedDate: Date;\n  orderClassCount: number;\n\n  constructor(\n    private cd: ChangeDetectorRef,\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService\n  ) {\n    super();\n  }\n\n  ngOnInit() {}\n\n  GetReport(request: ReportRequest) {\n    this.spinnerService.animatedStart();\n    this.selectedDate = moment(request.Date).toDate();\n\n    // Use the current implementation\n    this.orderAPIService.GetReportOrdersPrintedCancelledAPI(request).subscribe({\n      next: (response: ClassReport[]) => {\n        this.listReportClass = response;\n        this.orderClassCount = 0;\n\n        if (this.listReportClass) {\n          this.listReportClass.forEach(classe => {\n            this.orderClassCount += classe.orders.length;\n          });\n        }\n\n        this.spinnerService.animatedStop();\n        this.cd.markForCheck();\n      },\n      error: error => {\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"col-6\">\n    <p>\n      This report shows all orders that have been printed then subsequently cancelled. This is useful if you\n      print orders before cut off. If you would like this report automatically emailed to you, please contact\n      Spriggy Schools.\n    </p>\n  </div>\n\n  <report-form (request)=\"GetReport($event)\"></report-form>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div id=\"print-report\">\n    <div class=\"sectionReport\">\n      <report-heading\n        title=\"Cancelled Orders\"\n        [selectedDate]=\"selectedDate\"\n        [noResult]=\"listReportClass?.length === 0\"\n        [orderCount]=\"orderClassCount\"\n      ></report-heading>\n\n      <div *ngFor=\"let classe of listReportClass\">\n        <p class=\"menuType smaller\">{{ classe.class }}</p>\n\n        <table class=\"report\">\n          <tr class=\"border header\">\n            <th class=\"colWidth20\">Customer</th>\n            <th class=\"colWidth10\">Menu</th>\n            <th class=\"colWidth35\">Order Details</th>\n          </tr>\n          <tr *ngFor=\"let order of classe.orders\" class=\"border\">\n            <td>{{ order.studentName }}</td>\n            <td>{{ order.menuType | merchantMenuName }}</td>\n            <td>\n              <ul class=\"orderDetail\">\n                <li *ngFor=\"let item of order.items\">\n                  {{ item.quantity }} x {{ item.name }}\n                  <span *ngIf=\"item.selectedOptions\">({{ item.selectedOptions }})</span>\n                </li>\n              </ul>\n            </td>\n          </tr>\n        </table>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';\nimport { DecimalPipe } from '@angular/common';\nimport { ExportToCsv } from 'export-to-csv';\n\n// Models\nimport {\n  BaseComponent,\n  ReportRequest,\n  CsvSaleExport,\n  CanteenStatusTotals,\n  SaleReport,\n} from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService } from '../../../sharedServices';\nimport { ActivatedRoute } from '@angular/router';\n\n@Component({\n  selector: 'report-sale-v3',\n  templateUrl: './report-sale-v3.component.html',\n  styleUrls: ['./report-sale-v3.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportSaleV3Component extends BaseComponent implements OnInit {\n  saleReport: SaleReport;\n  isUniformMerchant: boolean = false;\n  priceColumnColspan: number = 3;\n  noResult: boolean = true;\n  statusTotals: CanteenStatusTotals;\n\n  constructor(\n    public spinnerService: SpinnerService,\n    public orderApiService: OrderApiService,\n    public _decimalPipe: DecimalPipe,\n    private cd: ChangeDetectorRef,\n    private route: ActivatedRoute\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    this.isUniformMerchant = this.route.snapshot.data['uniformShop'];\n  }\n\n  ExportCSV() {\n    let listItems: CsvSaleExport[] = [];\n\n    this.saleReport.Categories.forEach(c => {\n      c.Sales.forEach(i => {\n        let item: CsvSaleExport = new CsvSaleExport();\n        item.Category = c.CatName;\n        item.Name = i.Name;\n        item.Options = i.SelectedOptions;\n        item.Price = i.Price;\n        item.Quantity = i.Quantity;\n        item.PriceExclGst = this.GetDecimalOrDefaultValue(i.PriceExclGst);\n        item.GstValue = this.GetDecimalOrDefaultValue(i.GstValue);\n\n        listItems.push(item);\n      });\n    });\n\n    // add a total Item\n    let totalItem = new CsvSaleExport();\n    totalItem.Category = '';\n    totalItem.Name = 'Total';\n    totalItem.Options = '';\n    totalItem.Quantity = 0;\n    totalItem.GstValue = this.GetDecimalOrDefaultValue(this.saleReport.TotalGST);\n    totalItem.PriceExclGst = this.GetDecimalOrDefaultValue(this.saleReport.TotalPriceExclGST);\n    totalItem.Price = +this.GetDecimalOrDefaultValue(this.saleReport.TotalPriceIncGST);\n\n    listItems.push(totalItem);\n\n    const options = {\n      fieldSeparator: ',',\n      quoteStrings: '\"',\n      decimalSeparator: '.',\n      showLabels: true,\n      showTitle: true,\n      title: 'Export CSV',\n      useTextFile: false,\n      useBom: true,\n      useKeysAsHeaders: true,\n      // headers: ['Column 1', 'Column 2', etc...] <-- Won't work with useKeysAsHeaders present!\n    };\n\n    const csvExporter = new ExportToCsv(options);\n\n    csvExporter.generateCsv(listItems);\n  }\n\n  public Load(request: ReportRequest) {\n    this.spinnerService.animatedStart();\n\n    this.orderApiService.GetReportSalesAPI(request).subscribe({\n      next: (response: SaleReport) => {\n        this.saleReport = response;\n        this.spinnerService.animatedStop();\n        this.noResult = !response?.Categories?.length;\n        this.cd.markForCheck();\n      },\n      error: error => {\n        this.saleReport = null;\n        this.noResult = true;\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  GetNumberColumns(): string {\n    return this.isUniformMerchant ? 'col-2' : 'col-3';\n  }\n\n  GetDecimalOrDefaultValue(value: number): string {\n    if (Number(value) > 0) {\n      return '$' + this._decimalPipe.transform(value, '1.2-2').toString();\n    } else {\n      return '-';\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <report-form [showEndDate]=\"true\" (request)=\"Load($event)\"></report-form>\n\n  <!-- order filter for uniforms -->\n  <div class=\"col-12\" *ngIf=\"isUniformMerchant\">\n    <h4>Filters</h4>\n    <canteen-order-type-filter\n      [merchantType]=\"'Uniform'\"\n      [forReports]=\"true\"\n      [statusTotals]=\"statusTotals\"\n    ></canteen-order-type-filter>\n  </div>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div *ngIf=\"!noResult && saleReport\" class=\"col-12 print-button-spacing\">\n    <div class=\"row justify-content-end\">\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <button class=\"PrimaryButton smaller\" type=\"button\" (click)=\"ExportCSV()\">CSV</button>\n      </div>\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <button\n          class=\"PrimaryButton smaller\"\n          type=\"button\"\n          printSectionId=\"print-report\"\n          [useExistingCss]=\"true\"\n          ngxPrint\n        >\n          Print\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div id=\"print-report\">\n    <div class=\"sectionReport\">\n      <report-heading title=\"Sales Report\" [noResult]=\"noResult\"></report-heading>\n\n      <ng-container *ngIf=\"saleReport\">\n        <table *ngFor=\"let category of saleReport.Categories\" class=\"report\">\n          <thead>\n            <tr class=\"border\">\n              <th class=\"colWidth10 col-1\">Quantity</th>\n              <th class=\"category\" [ngClass]=\"GetNumberColumns()\">{{ category.CatName }}</th>\n              <th class=\"colWidth25\" [ngClass]=\"GetNumberColumns()\">Modifiers</th>\n              <th class=\"colWidth25 col-3\" *ngIf=\"isUniformMerchant\">Status</th>\n              <th class=\"col-1\">GST</th>\n              <th class=\"col-2\">Price(Excl GST)</th>\n              <th class=\"col-2\">Price(Inc GST)</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let item of category.Sales\" class=\"border\">\n              <td class=\"col-1\">{{ item.Quantity }} x</td>\n              <td [ngClass]=\"GetNumberColumns()\">\n                {{ item.Name }}\n              </td>\n              <td [ngClass]=\"GetNumberColumns()\">\n                {{ item.SelectedOptions }}\n              </td>\n              <td class=\"col-3\" *ngIf=\"isUniformMerchant\">\n                {{ item.CanteenStatus }}\n              </td>\n              <td class=\"col-1\">\n                {{ GetDecimalOrDefaultValue(item.GstValue) }}\n              </td>\n              <td class=\"col-2\">\n                {{ GetDecimalOrDefaultValue(item.PriceExclGst) }}\n              </td>\n              <td class=\"col-2\">\n                {{ GetDecimalOrDefaultValue(item.Price) }}\n              </td>\n            </tr>\n          </tbody>\n          <tfoot>\n            <tr class=\"totalRow\">\n              <td [attr.colspan]=\"priceColumnColspan\">Category Total:</td>\n              <td class=\"col-1\">\n                {{ GetDecimalOrDefaultValue(category.TotalGST) }}\n              </td>\n              <td class=\"col-1\">\n                {{ GetDecimalOrDefaultValue(category.TotalPriceExclGST) }}\n              </td>\n              <td class=\"col-1\">\n                {{ GetDecimalOrDefaultValue(category.TotalPriceIncGST) }}\n              </td>\n            </tr>\n          </tfoot>\n        </table>\n\n        <table *ngIf=\"!noResult\" class=\"report\">\n          <thead>\n            <tr class=\"border\">\n              <th class=\"quantity col-1\"></th>\n              <th class=\"category\" [ngClass]=\"GetNumberColumns()\"></th>\n              <th class=\"colWidth25\" [ngClass]=\"GetNumberColumns()\"></th>\n              <th *ngIf=\"isUniformMerchant\" class=\"colWidth25 col-3\"></th>\n              <th class=\"col-1\"></th>\n              <th class=\"colWidth25 col-2\"></th>\n              <th class=\"col-2\"></th>\n            </tr>\n          </thead>\n          <tfoot>\n            <tr class=\"totalRow\">\n              <td [attr.colspan]=\"priceColumnColspan\">Total:</td>\n              <td class=\"col-1\">\n                {{ GetDecimalOrDefaultValue(saleReport.TotalGST) }}\n              </td>\n              <td class=\"col-2\">\n                {{ GetDecimalOrDefaultValue(saleReport.TotalPriceExclGST) }}\n              </td>\n              <td class=\"col-2\">\n                {{ GetDecimalOrDefaultValue(saleReport.TotalPriceIncGST) }}\n              </td>\n            </tr>\n          </tfoot>\n        </table>\n      </ng-container>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit, ViewChild } from '@angular/core';\nimport * as moment from 'moment';\n\n// Models\nimport { ReportRequest, Category, BaseComponent, MenuTypeEnum } from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService } from '../../../sharedServices';\n\nimport { ReportFormComponent } from '../report-form/report-form.component';\n\n@Component({\n  selector: 'report-items-uniform',\n  templateUrl: './report-uniform-items.component.html',\n  styleUrls: ['../report.scss'],\n})\nexport class ReportUniformItemsComponent extends BaseComponent implements OnInit {\n  @ViewChild(ReportFormComponent, { static: false }) reportComponent: ReportFormComponent;\n\n  listCategoriesUniform: Category[];\n  selectedDate: Date;\n  selectedCanteenName: string;\n  dataFetched: boolean = false;\n  canteenStatuses: string[] = [];\n\n  constructor(private spinnerService: SpinnerService, private orderAPIService: OrderApiService) {\n    super();\n  }\n\n  ngOnInit() {}\n\n  GetReports(request: ReportRequest) {\n    this.selectedDate = moment(request.Date).toDate();\n    this.selectedCanteenName = request.selectedCanteenName;\n    request.MenuType = MenuTypeEnum.Uniform;\n\n    this.spinnerService.start();\n\n    this.orderAPIService.GetReportOrdersByMenuBySchoolAPI(request).subscribe({\n      next: (response: Category[]) => {\n        this.listCategoriesUniform = response;\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.spinnerService.stop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  showPrintButton() {\n    return this.listCategoriesUniform && this.listCategoriesUniform.length > 0;\n  }\n}\n", "<div class=\"container-fluid\">\n  <report-form [showClasses]=\"true\" [showStartDate]=\"false\" (request)=\"GetReports($event)\"></report-form>\n\n  <!-- order filter for uniforms -->\n  <div class=\"col-12\">\n    <h4 style=\"margin-bottom: 10px; padding-left: 8px\">Filters</h4>\n    <canteen-order-type-filter [merchantType]=\"'Uniform'\" [forReports]=\"true\"></canteen-order-type-filter>\n  </div>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div *ngIf=\"showPrintButton()\" class=\"col-12 print-button-spacing\">\n    <div class=\"row justify-content-end\">\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <button\n          class=\"PrimaryButton smaller\"\n          type=\"button\"\n          printSectionId=\"print-report\"\n          [useExistingCss]=\"true\"\n          ngxPrint\n        >\n          Print\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <div id=\"print-report\">\n    <!-- Uniform -->\n    <report-items-table\n      menuType=\"Uniform\"\n      [selectedDate]=\"selectedDate\"\n      [selectedCanteenName]=\"selectedCanteenName\"\n      [listCategories]=\"listCategoriesUniform\"\n      [isUniformMerchant]=\"true\"\n    >\n    </report-items-table>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { UntypedFormGroup } from '@angular/forms';\nimport { Subscription } from 'rxjs';\n\n// Models\nimport { StockManagement, Canteen } from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, MenuEditorApiService } from '../../../sharedServices';\n\nimport { Store, select } from '@ngrx/store';\nimport { CanteenState } from 'src/app/states';\nimport { selectedCanteen } from 'src/app/states/canteen/canteen.selectors';\n\nconst _columns = ['id', 'name', 'dailyStock', 'stock', 'isActive'];\n\n@Component({\n  selector: 'report-uniform-stock',\n  templateUrl: './report-uniform-stock.component.html',\n  styleUrls: ['../report.scss'],\n})\nexport class ReportUniformStockComponent extends StockManagement implements OnInit {\n  schoolForm: UntypedFormGroup;\n  operatorSubscribtion: Subscription;\n  canteen: Canteen;\n\n  constructor(\n    menuEditorAPIService: MenuEditorApiService,\n    spinnerService: SpinnerService,\n    private store: Store<{ canteen: CanteenState }>\n  ) {\n    super(_columns, menuEditorAPIService, spinnerService);\n  }\n\n  ngOnInit() {\n    this.operatorSubscribtion = this.store\n      .pipe(select(selectedCanteen))\n      .subscribe((selectedCanteen: Canteen) => {\n        this.CanteenChanged(selectedCanteen);\n      });\n  }\n\n  ngOnDestroy() {\n    if (this.operatorSubscribtion) {\n      this.operatorSubscribtion.unsubscribe();\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"col-12 print-button-spacing\">\n    <div class=\"row justify-content-between align-items-end\">\n      <div class=\"col-6\">\n        <p class=\"menuType\" style=\"margin: 0\">Stock Management</p>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"col-12\">\n    <table mat-table [dataSource]=\"dataSource\" class=\"mat-elevation-z0 tableau stockTable\">\n      <ng-container matColumnDef=\"id\">\n        <th mat-header-cell *matHeaderCellDef>ID</th>\n        <td mat-cell *matCellDef=\"let element\">\n          <strong>{{ element.StockId }} </strong>\n        </td>\n      </ng-container>\n\n      <ng-container matColumnDef=\"name\">\n        <th mat-header-cell *matHeaderCellDef>Stock</th>\n        <td mat-cell *matCellDef=\"let element\">\n          <strong>{{ element.StockName }}</strong>\n        </td>\n      </ng-container>\n\n      <ng-container matColumnDef=\"dailyStock\">\n        <th mat-header-cell *matHeaderCellDef>Daily Stock</th>\n        <td mat-cell *matCellDef=\"let element\">{{ element.DailyStock }}</td>\n      </ng-container>\n\n      <ng-container matColumnDef=\"stock\">\n        <th mat-header-cell *matHeaderCellDef>Remaining Quantity</th>\n        <td mat-cell *matCellDef=\"let element\">{{ element.StockQuantity }}</td>\n      </ng-container>\n\n      <ng-container matColumnDef=\"isActive\">\n        <th mat-header-cell *matHeaderCellDef>Visibility</th>\n        <td mat-cell *matCellDef=\"let element\">\n          <mat-checkbox [checked]=\"element.IsActive\" [disabled]=\"true\"></mat-checkbox>\n        </td>\n      </ng-container>\n\n      <ng-container matColumnDef=\"actions\">\n        <th mat-header-cell *matHeaderCellDef></th>\n        <td mat-cell *matCellDef=\"let element\">\n          <img\n            src=\"../../../../assets/icons/pencil.svg\"\n            alt=\"edit symbol\"\n            style=\"float: right; cursor: pointer\"\n          />\n        </td>\n      </ng-container>\n\n      <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n      <tr mat-row *matRowDef=\"let row; columns: displayedColumns\"></tr>\n    </table>\n  </div>\n</div>\n", "import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';\nimport * as moment from 'moment';\n\n// Models\nimport { ReportRequest, BaseComponent, ClassReport } from '../../../sharedModels';\n\n// Services\nimport { SpinnerService, OrderApiService } from '../../../sharedServices';\n\n@Component({\n  selector: 'report-unprinted',\n  templateUrl: './report-unprinted.component.html',\n  styleUrls: ['./report-unprinted.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ReportUnprintedComponent extends BaseComponent {\n  listReportClass: ClassReport[] = [];\n  selectedDate: Date;\n  orderClassCount: number;\n\n  constructor(\n    private cd: ChangeDetectorRef,\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService\n  ) {\n    super();\n  }\n\n  GetReport(request: ReportRequest): void {\n    this.spinnerService.animatedStart();\n    this.selectedDate = moment(request.Date).toDate();\n\n    // Use current implementation\n    this.orderAPIService.GetReportOrdersUnprintedAPI(request).subscribe({\n      next: (response: ClassReport[]) => {\n        this.processApiResponse(response);\n        this.spinnerService.animatedStop();\n\n        this.cd.markForCheck();\n      },\n      error: error => {\n        this.spinnerService.animatedStop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  processApiResponse(response: ClassReport[]): void {\n    this.listReportClass = response;\n\n    this.orderClassCount = 0;\n\n    if (this.listReportClass) {\n      this.listReportClass.forEach(classe => {\n        this.orderClassCount += classe.orders.length;\n      });\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <div class=\"col-6\">\n    <p>This report shows all orders that have not been printed for the selected date.</p>\n  </div>\n\n  <report-form (request)=\"GetReport($event)\"></report-form>\n\n  <!-- vertical spacing -->\n  <div class=\"col-12 report-vertical-spacing\"></div>\n\n  <div id=\"print-report\">\n    <div class=\"sectionReport\">\n      <report-heading\n        title=\"Unprinted Orders Report\"\n        [selectedDate]=\"selectedDate\"\n        [noResult]=\"listReportClass?.length === 0\"\n        [orderCount]=\"orderClassCount\"\n      ></report-heading>\n\n      <div *ngFor=\"let classe of listReportClass\">\n        <p class=\"menuType smaller\">{{ classe.class }}</p>\n\n        <table class=\"report\">\n          <tr class=\"border header\">\n            <th class=\"colWidth20\">Customer</th>\n            <th class=\"colWidth10\">Menu</th>\n            <th class=\"colWidth35\">Order Details</th>\n          </tr>\n          <tr *ngFor=\"let order of classe.orders\" class=\"border\">\n            <td>{{ order.studentName }}</td>\n            <td>{{ order.menuType | merchantMenuName }}</td>\n            <td>\n              <ul class=\"orderDetail\">\n                <li *ngFor=\"let item of order.items\">\n                  {{ item.quantity }} x {{ item.name }}\n                  <span *ngIf=\"item.selectedOptions\">({{ item.selectedOptions }})</span>\n                </li>\n              </ul>\n            </td>\n          </tr>\n        </table>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit, EventEmitter, Output } from '@angular/core';\nimport { FormGroup, FormControl } from '@angular/forms';\n\n@Component({\n  selector: 'report-version-picker',\n  templateUrl: './report-version-picker.component.html',\n  styleUrls: ['./report-version-picker.component.scss'],\n})\nexport class ReportVersionPickerComponent implements OnInit {\n  @Output() updateReportVersion: EventEmitter<boolean> = new EventEmitter();\n  form: FormGroup;\n\n  // Current version = Dapper implementation\n  // Original version = Stored proc based implementation\n  versionEnum = { current: 'current', original: 'original' };\n  selectedVersion: string;\n\n  constructor() {}\n\n  ngOnInit(): void {\n    this._createForm();\n  }\n\n  _createForm() {\n    this.form = new FormGroup({\n      version: new FormControl(this.versionEnum.current),\n    });\n\n    this.version.valueChanges.subscribe(val => {\n      let originalVersion = Boolean(val === this.versionEnum.original);\n      this.updateReportVersion.emit(originalVersion);\n    });\n  }\n\n  get version() {\n    return this.form.get('version');\n  }\n}\n\n/**\n * Methods that need to be impmented to handle click events\n * to show the ReportVersionPickerComponent\n */\nexport interface OnReportVersionPickerClick {\n  showReportVersionPicker(): boolean;\n  updateReportVersion(useOriginalVersion: boolean): void;\n  onReportVersionPickerClick(): void;\n}\n\n/**\n * Handle clicks to show the ReportVersionPickerComponent\n */\nexport class ReportVersionPickerHandler {\n  private clickCount: number = 0;\n  private useOriginalVersion: boolean = false;\n  private showVersionPicker: boolean = false;\n\n  // Number of times to click before the\n  // version picker is displayed\n  private static DefaultClickCount: number = 7;\n\n  public isUseOriginalReportVersion(): boolean {\n    return this.useOriginalVersion;\n  }\n\n  public setUseOriginalReportVersion(useOriginalVersion: boolean): void {\n    this.useOriginalVersion = useOriginalVersion;\n  }\n\n  public isShowVersionPicker(): boolean {\n    return this.showVersionPicker;\n  }\n\n  /**\n   * Count the number of clicks and show the version picker\n   * component if required\n   */\n  public handleClick(): void {\n    this.clickCount++;\n\n    if (this.clickCount === ReportVersionPickerHandler.DefaultClickCount) {\n      this.showVersionPicker = true;\n    }\n  }\n}\n", "<div class=\"d-flex align-items-center\" style=\"gap: 6px; min-width: 100%\">\n  <div>\n    <p>Report Settings:</p>\n  </div>\n\n  <div *ngIf=\"form\" [formGroup]=\"form\" class=\"report-picker\">\n    <mat-form-field appearance=\"outline\">\n      <mat-label>{{ selectedVersion }}</mat-label>\n      <mat-select formControlName=\"version\">\n        <mat-option value=\"current\"> Current Implementation </mat-option>\n        <mat-option value=\"original\"> Original Implementation </mat-option>\n      </mat-select>\n    </mat-form-field>\n  </div>\n</div>\n", "import { Pipe, PipeTransform } from '@angular/core';\nimport { ReportHybridMenuItems } from 'src/app/sharedModels';\n\n@Pipe({\n  name: 'classItemQuantity',\n})\nexport class ClassItemQuantityPipe implements PipeTransform {\n  constructor() {}\n\n  transform(id: string, list: ReportHybridMenuItems[], key: string) {\n    let index = list.findIndex(x => x[key] == id);\n    return index >= 0 ? list[index].Quantity : '-';\n  }\n}\n", "import { Pipe, PipeTransform } from '@angular/core';\nimport { ReportHybridMenuItems } from 'src/app/sharedModels';\n\n@Pipe({\n  name: 'classReportTotal',\n})\nexport class ClassReportTotalPipe implements PipeTransform {\n  constructor() {}\n\n  transform(menuItems: ReportHybridMenuItems[]) {\n    let count = 0;\n    menuItems.forEach(item => (count += item.Quantity));\n    return count;\n  }\n}\n", "export * from './class-item-quantity.pipe';\nexport * from './class-report-total.pipe';\nexport * from './item-report-total.pipe';\n", "import { Pipe, PipeTransform } from '@angular/core';\nimport { ReportHybridMenuItems } from 'src/app/sharedModels';\n\n@Pipe({\n  name: 'itemReportTotal',\n})\nexport class ItemReportTotalPipe implements PipeTransform {\n  constructor() {}\n\n  transform(menuItemId: number, menuItemList: ReportHybridMenuItems[][]) {\n    let count = 0;\n\n    menuItemList.forEach(menuItemArray => {\n      menuItemArray.forEach(mi => {\n        if (mi.MenuItemId == menuItemId) {\n          count += mi.Quantity;\n        }\n      });\n    });\n\n    return count;\n  }\n}\n", "import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\n// Services\nimport { ListCanteensResolver } from 'src/app/sharedServices';\n\n// Components\nimport {\n  ReportItemsComponent,\n  ReportUnprintedComponent,\n  NavReportComponent,\n  ReportPrintedCancelledComponent,\n  ReportUniformItemsComponent,\n  ReportUniformStockComponent,\n  ReportClassAndItemComponent,\n  ReportClassV4Component,\n  ReportSaleV3Component,\n} from './components';\nimport { MenuTypeEnum } from '../sharedModels';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: NavReportComponent,\n    resolve: { canteens: ListCanteensResolver },\n    children: [\n      ///////////////////////\n      //canteen report routes\n      ///////////////////////\n      {\n        path: '',\n        pathMatch: 'full',\n        redirectTo: 'items',\n      },\n      {\n        path: 'sales',\n        component: ReportSaleV3Component,\n        data: { uniformShop: false }\n      },\n      {\n        path: 'items',\n        component: ReportItemsComponent,\n      },\n      {\n        path: 'events',\n        component: ReportClassV4Component,\n        data: { reportType: MenuTypeEnum.Event }\n      },\n      {\n        path: 'unprinted',\n        component: ReportUnprintedComponent,\n      },\n      {\n        path: 'printedcancelled',\n        component: ReportPrintedCancelledComponent,\n      },\n      {\n        path: 'class',\n        component: ReportClassV4Component,\n        data: { reportType: MenuTypeEnum.Lunch }\n      },\n      {\n        path: 'classanditem',\n        component: ReportClassAndItemComponent,\n      },\n      ///////////////////////\n      //uniform report routes\n      ///////////////////////\n      {\n        path: 'uniformsales',\n        component: ReportSaleV3Component,\n        data: { uniformShop: true }\n      },\n      {\n        path: 'uniformitems',\n        component: ReportUniformItemsComponent,\n      },\n      {\n        path: 'uniformclass',\n        component: ReportClassV4Component,\n        data: { reportType: MenuTypeEnum.Uniform }\n      },\n      {\n        path: 'uniformstock',\n        component: ReportUniformStockComponent,\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class ReportsRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule, DecimalPipe } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { NgxPrintModule } from 'ngx-print';\n\n// google material\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatRadioModule } from '@angular/material/radio';\n// modules\nimport { ReportsRoutingModule } from './reports-routing.module';\nimport { SharedToolsModule } from '../shared-tools/shared-tools.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { SchoolsFormModule } from '../schools-form/schools-form.module';\n\n// components\nimport {\n  NavReportComponent,\n  ReportFormComponent,\n  ReportItemsComponent,\n  ReportUnprintedComponent,\n  ReportPrintedCancelledComponent,\n  ReportUniformItemsComponent,\n  ReportUniformStockComponent,\n  ReportVersionPickerComponent,\n  ReportItemsTableComponent,\n  ReportItemsFilterTableComponent,\n  ReportItemsCategoryTableComponent,\n  ItemStudentListTableComponent,\n  ReportClassAndItemComponent,\n  ReportFormAndFiltersComponent,\n  BaseReportComponent,\n  HybridClassItemTableComponent,\n  HybridClassListTableComponent,\n  HybridItemListTableComponent,\n} from './components';\nimport { CanteenModule } from '../canteen/canteen.module';\n\nimport { ClassItemQuantityPipe, ClassReportTotalPipe, ItemReportTotalPipe } from './pipes';\nimport { ReportSaleV3Component } from './components/report-sale-v3/report-sale-v3.component';\nimport { ReportClassV4Component } from './components/report-class-v4/report-class-v4.component';\nimport { ReportHeadingComponent } from './components/report-heading/report-heading.component';\n\n@NgModule({\n  declarations: [\n    NavReportComponent,\n    ReportItemsComponent,\n    ReportUnprintedComponent,\n    ReportPrintedCancelledComponent,\n    ReportFormComponent,\n    ReportUniformItemsComponent,\n    ReportUniformStockComponent,\n    ReportItemsTableComponent,\n    ReportItemsFilterTableComponent,\n    ReportItemsCategoryTableComponent,\n    ItemStudentListTableComponent,\n    ReportVersionPickerComponent,\n    ReportClassAndItemComponent,\n    ReportFormAndFiltersComponent,\n    ReportFormAndFiltersComponent,\n    BaseReportComponent,\n    HybridClassListTableComponent,\n    HybridClassItemTableComponent,\n    HybridItemListTableComponent,\n    //pipes\n    ClassItemQuantityPipe,\n    ClassReportTotalPipe,\n    ItemReportTotalPipe,\n    ReportSaleV3Component,\n    ReportClassV4Component,\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    NgxPrintModule,\n    ReportsRoutingModule,\n    SharedToolsModule,\n    CanteenModule,\n    SharedModule,\n    // material\n    MatSelectModule,\n    MatTableModule,\n    MatCheckboxModule,\n    MatExpansionModule,\n    MatIconModule,\n    MatRadioModule,\n    SchoolsFormModule,\n    ReportHeadingComponent,\n  ],\n  providers: [DecimalPipe],\n})\nexport class ReportsModule {}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ExportToCsv = exports.ConfigDefaults = exports.CsvConfigConsts = void 0;\nvar CsvConfigConsts = /** @class */ (function () {\n    function CsvConfigConsts() {\n    }\n    CsvConfigConsts.EOL = \"\\r\\n\";\n    CsvConfigConsts.BOM = \"\\ufeff\";\n    CsvConfigConsts.DEFAULT_FIELD_SEPARATOR = \",\";\n    CsvConfigConsts.DEFAULT_DECIMAL_SEPARATOR = \".\";\n    CsvConfigConsts.DEFAULT_QUOTE = '\"';\n    CsvConfigConsts.DEFAULT_SHOW_TITLE = false;\n    CsvConfigConsts.DEFAULT_TITLE = \"My Generated Report\";\n    CsvConfigConsts.DEFAULT_FILENAME = \"generated\";\n    CsvConfigConsts.DEFAULT_SHOW_LABELS = false;\n    CsvConfigConsts.DEFAULT_USE_TEXT_FILE = false;\n    CsvConfigConsts.DEFAULT_USE_BOM = true;\n    CsvConfigConsts.DEFAULT_HEADER = [];\n    CsvConfigConsts.DEFAULT_KEYS_AS_HEADERS = false;\n    return CsvConfigConsts;\n}());\nexports.CsvConfigConsts = CsvConfigConsts;\nexports.ConfigDefaults = {\n    filename: CsvConfigConsts.DEFAULT_FILENAME,\n    fieldSeparator: CsvConfigConsts.DEFAULT_FIELD_SEPARATOR,\n    quoteStrings: CsvConfigConsts.DEFAULT_QUOTE,\n    decimalSeparator: CsvConfigConsts.DEFAULT_DECIMAL_SEPARATOR,\n    showLabels: CsvConfigConsts.DEFAULT_SHOW_LABELS,\n    showTitle: CsvConfigConsts.DEFAULT_SHOW_TITLE,\n    title: CsvConfigConsts.DEFAULT_TITLE,\n    useTextFile: CsvConfigConsts.DEFAULT_USE_TEXT_FILE,\n    useBom: CsvConfigConsts.DEFAULT_USE_BOM,\n    headers: CsvConfigConsts.DEFAULT_HEADER,\n    useKeysAsHeaders: CsvConfigConsts.DEFAULT_KEYS_AS_HEADERS,\n};\nvar ExportToCsv = /** @class */ (function () {\n    function ExportToCsv(options) {\n        this._csv = \"\";\n        var config = options || {};\n        this._options = objectAssign({}, exports.ConfigDefaults, config);\n        if (this._options.useKeysAsHeaders &&\n            this._options.headers &&\n            this._options.headers.length > 0) {\n            console.warn(\"Option to use object keys as headers was set, but headers were still passed!\");\n        }\n    }\n    Object.defineProperty(ExportToCsv.prototype, \"options\", {\n        get: function () {\n            return this._options;\n        },\n        set: function (options) {\n            this._options = objectAssign({}, exports.ConfigDefaults, options);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Generate and Download Csv\n     */\n    ExportToCsv.prototype.generateCsv = function (jsonData, shouldReturnCsv) {\n        if (shouldReturnCsv === void 0) { shouldReturnCsv = false; }\n        // Make sure to reset csv data on each run\n        this._csv = \"\";\n        this._parseData(jsonData);\n        if (this._options.useBom) {\n            this._csv += CsvConfigConsts.BOM;\n        }\n        if (this._options.showTitle) {\n            this._csv += this._options.title + \"\\r\\n\\n\";\n        }\n        this._createHeaders();\n        this._createBody();\n        if (this._csv == \"\") {\n            console.log(\"Invalid data\");\n            return;\n        }\n        // When the consumer asks for the data, exit the function\n        // by returning the CSV data built at this point\n        if (shouldReturnCsv) {\n            return this._csv;\n        }\n        // Create CSV blob to download if requesting in the browser and the\n        // consumer doesn't set the shouldReturnCsv param\n        var FileType = this._options.useTextFile ? \"plain\" : \"csv\";\n        var fileExtension = this._options.useTextFile ? \".txt\" : \".csv\";\n        var blob = new Blob([this._csv], {\n            type: \"text/\" + FileType + \";charset=utf8;\",\n        });\n        var link = document.createElement(\"a\");\n        link.href = URL.createObjectURL(blob);\n        link.setAttribute(\"visibility\", \"hidden\");\n        link.download = this._options.filename.replace(/ /g, \"_\") + fileExtension;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    ExportToCsv.prototype._getHeaders = function () {\n        if (!this._options.showLabels && !this._options.useKeysAsHeaders) {\n            return;\n        }\n        var useKeysAsHeaders = this._options.useKeysAsHeaders;\n        var headers = useKeysAsHeaders\n            ? Object.keys(this._data[0])\n            : this._options.headers;\n        return headers;\n    };\n    /**\n     * Create Headers\n     */\n    ExportToCsv.prototype._createHeaders = function () {\n        var headers = this._getHeaders();\n        if (headers.length > 0) {\n            var row = \"\";\n            for (var keyPos = 0; keyPos < headers.length; keyPos++) {\n                row += headers[keyPos] + this._options.fieldSeparator;\n            }\n            row = row.slice(0, -1);\n            this._csv += row + CsvConfigConsts.EOL;\n        }\n    };\n    /**\n     * Create Body\n     */\n    ExportToCsv.prototype._createBody = function () {\n        var headers = this._getHeaders();\n        for (var i = 0; i < this._data.length; i++) {\n            var row = \"\";\n            for (var keyPos = 0; keyPos < headers.length; keyPos++) {\n                var header = headers[keyPos];\n                row +=\n                    this._formatData(this._data[i][header]) +\n                        this._options.fieldSeparator;\n            }\n            row = row.slice(0, -1);\n            this._csv += row + CsvConfigConsts.EOL;\n        }\n    };\n    /**\n     * Format Data\n     * @param {any} data\n     */\n    ExportToCsv.prototype._formatData = function (data) {\n        if (this._options.decimalSeparator === \"locale\" && this._isFloat(data)) {\n            return data.toLocaleString();\n        }\n        if (this._options.decimalSeparator !== \".\" && this._isFloat(data)) {\n            return data.toString().replace(\".\", this._options.decimalSeparator);\n        }\n        if (typeof data === \"string\") {\n            data = data.replace(/\"/g, '\"\"');\n            if (this._options.quoteStrings ||\n                data.indexOf(\",\") > -1 ||\n                data.indexOf(\"\\n\") > -1 ||\n                data.indexOf(\"\\r\") > -1) {\n                data = this._options.quoteStrings + data + this._options.quoteStrings;\n            }\n            return data;\n        }\n        if (typeof data === \"boolean\") {\n            return data ? \"TRUE\" : \"FALSE\";\n        }\n        return data;\n    };\n    /**\n     * Check if is Float\n     * @param {any} input\n     */\n    ExportToCsv.prototype._isFloat = function (input) {\n        return +input === input && (!isFinite(input) || Boolean(input % 1));\n    };\n    /**\n     * Parse the collection given to it\n     *\n     * @private\n     * @param {*} jsonData\n     * @returns {any[]}\n     * @memberof ExportToCsv\n     */\n    ExportToCsv.prototype._parseData = function (jsonData) {\n        this._data = typeof jsonData != \"object\" ? JSON.parse(jsonData) : jsonData;\n        return this._data;\n    };\n    return ExportToCsv;\n}());\nexports.ExportToCsv = ExportToCsv;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n/**\n * Convet to Object\n * @param {any} val\n */\nfunction toObject(val) {\n    if (val === null || val === undefined) {\n        throw new TypeError(\"Object.assign cannot be called with null or undefined\");\n    }\n    return Object(val);\n}\n/**\n * Assign data  to new Object\n * @param {any}   target\n * @param {any[]} ...source\n */\nfunction objectAssign(target) {\n    var source = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        source[_i - 1] = arguments[_i];\n    }\n    var from;\n    var to = toObject(target);\n    var symbols;\n    for (var s = 1; s < arguments.length; s++) {\n        from = Object(arguments[s]);\n        for (var key in from) {\n            if (hasOwnProperty.call(from, key)) {\n                to[key] = from[key];\n            }\n        }\n        if (Object.getOwnPropertySymbols) {\n            symbols = Object.getOwnPropertySymbols(from);\n            for (var i = 0; i < symbols.length; i++) {\n                if (propIsEnumerable.call(from, symbols[i])) {\n                    to[symbols[i]] = from[symbols[i]];\n                }\n            }\n        }\n    }\n    return to;\n}\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./export-to-csv\"), exports);\n"], "names": ["select", "BaseComponent", "MenuPickerOptions", "MerchantTypeEnum", "canteenStateSelector", "BaseReportComponent", "constructor", "store", "canteenService", "isEventMerchant", "menuPicker", "ngOnInit", "initMenuPicker", "currentMenuOptions", "GetFilters", "Recess", "Lunch", "Event", "getMerchantInfo", "canteenSubscription", "pipe", "subscribe", "state", "selected", "selectedMerchantId", "CanteenId", "merchantType", "CanteenType", "menuCategories", "baseReportOnDestroy", "unsubscribe", "i0", "ɵɵdirectiveInject", "i1", "Store", "i2", "CanteenService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "template", "BaseReportComponent_Template", "rf", "ctx", "encapsulation", "Subject", "ItemReportPrintService", "closedAccordionRowCount", "reportCategorySum", "triggerPrinting", "triggerPrintEvent$", "asObservable", "closeAccordionRow", "closeAccordionRowEvent$", "triggerPrint", "next", "AddToAccordionRowCloseCount", "getSumOfReportCategories", "showMenuType", "filteredList", "length", "closeAllReportAccordionRows", "resetCategorySum", "factory", "ɵfac", "providedIn", "MatTableDataSource", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtext", "ɵɵtextInterpolate1", "element_r12", "Quantity", "ɵɵtextInterpolate", "element_r13", "OrderId", "ɵɵpropertyInterpolate1", "element_r14", "StudentId", "StudentName", "element_r15", "SchoolClass", "ɵɵelementContainerStart", "ɵɵtemplate", "ItemStudentListTableComponent_table_1_th_2_Template", "ItemStudentListTableComponent_table_1_td_3_Template", "ɵɵelementContainerEnd", "ItemStudentListTableComponent_table_1_th_5_Template", "ItemStudentListTableComponent_table_1_td_6_Template", "ItemStudentListTableComponent_table_1_th_8_Template", "ItemStudentListTableComponent_table_1_td_9_Template", "ItemStudentListTableComponent_table_1_th_11_Template", "ItemStudentListTableComponent_table_1_td_12_Template", "ItemStudentListTableComponent_table_1_tr_13_Template", "ItemStudentListTableComponent_table_1_tr_14_Template", "ctx_r1", "dataSource", "displayedColumns", "_columns", "ItemStudentListTableComponent", "orderAPIService", "loading", "loadSubscription", "loadStudents", "rowIdString", "idString", "menuItemId", "toString", "orderIds", "loadStudentList", "ngOnDestroy", "data", "studentIdArray", "studentIds", "split", "reportRequest", "MenuItemId", "MenuType", "menuType", "GetOrderItemStudentList", "res", "studentsToShow", "for<PERSON>ach", "el", "checkIfStudentHasMatchingOrderId", "push", "error", "handleErrorFromService", "studentOrder", "id", "OrderApiService", "inputs", "consts", "ItemStudentListTableComponent_Template", "ItemStudentListTableComponent_div_0_Template", "ItemStudentListTableComponent_table_1_Template", "ɵɵpureFunction0", "_c0", "_c1", "NavReportComponent_ng_container_3_li_4_Template", "NavReportComponent_ng_container_3_li_9_Template", "NavReportComponent_ng_container_3_li_10_Template", "NavReportComponent_ng_container_3_li_11_Template", "_c2", "ctx_r0", "_c3", "IsOrdersNotPrintedReportsAvailable", "isSaleReportRole", "NavReportComponent_ng_container_4_li_6_Template", "_c4", "_c5", "NavReportComponent", "featureFlagService", "isUniformMerchant", "subscription", "IsSaleReportsAvailable", "Uniform", "getSaleReportNavigationLink", "featureFlagSubscription", "FeatureFlagService", "NavReportComponent_Template", "NavReportComponent_ng_container_3_Template", "NavReportComponent_ng_container_4_Template", "school_r2", "SortedCategoryList", "SchoolId", "HybridClassItemTableComponent_div_0_div_1_hybrid_class_list_table_3_Template", "HybridClassItemTableComponent_div_0_div_1_ng_template_4_Template", "ɵɵtemplateRefExtractor", "HybridClassItemTableComponent_div_0_div_1_div_6_Template", "SchoolName", "showClassReport", "_r5", "isLastIndex", "i_r3", "HybridClassItemTableComponent_div_0_div_1_Template", "reportData", "HybridClassItemTableComponent", "index", "listClass", "HybridClassItemTableComponent_Template", "HybridClassItemTableComponent_div_0_Template", "ReportHybridTableComponent", "class_r4", "Name", "ɵɵpipeBind3", "class_r7", "ClassId", "items_r5", "HybridClassListTableComponent_table_0_tr_9_td_3_Template", "MenuItemName", "ctx_r3", "ɵɵpipeBind1", "HybridClassListTableComponent_table_0_th_5_Template", "HybridClassListTableComponent_table_0_tr_9_Template", "categ_r1", "CategoryName", "Rows", "HybridClassListTableComponent", "classesService", "tableData", "getClass", "processData", "categoryData", "GetClassesBySchoolAPI", "schoolId", "response", "Classes", "SchoolClassesService", "HybridClassListTableComponent_Template", "HybridClassListTableComponent_table_0_Template", "mi_r5", "name", "mi_r8", "items_r6", "HybridItemListTableComponent_table_0_tr_7_td_3_Template", "ClassName", "DistinctMenuList", "ɵɵpipeBind2", "items_r11", "HybridItemListTableComponent_table_0_th_5_Template", "HybridItemListTableComponent_table_0_tr_7_Template", "HybridItemListTableComponent_table_0_td_11_Template", "HybridItemListTableComponent", "getDistinctMenuItems", "x", "i", "result", "map", "Map", "item", "MenuItemList", "has", "set", "HybridItemListTableComponent_Template", "HybridItemListTableComponent_table_0_Template", "FormControl", "FormGroup", "moment", "_", "ReportClassAndItemComponent", "spinnerService", "menuString", "createForm", "prepareReportRequest", "request", "selectedDate", "Date", "toDate", "selectedCanteenName", "_getReport", "_getClassListReport", "animatedStart", "GetReportClassItem", "filterSelectedCategories", "animatedStop", "menuSelectionChanged", "menu", "keys", "Object", "k", "showPrintButton", "reportTypeForm", "type", "valueChanges", "val", "get", "selectedCategories", "list", "filter", "selectedCategoryNames", "includes", "CatName", "categoriesSelectionChanged", "categories", "selectedCategoryList", "sch", "CategoryList", "CategoryId", "itemTotal", "categ", "sum", "reduce", "accumulated", "curr", "cloneDeep", "SpinnerService", "ReportClassAndItemComponent_Template", "ɵɵlistener", "ReportClassAndItemComponent_Template_report_form_and_filters_getReport_1_listener", "$event", "ReportClassAndItemComponent_Template_report_form_and_filters_categoryChange_1_listener", "ReportClassAndItemComponent_Template_report_form_and_filters_menuTypeChange_1_listener", "ReportClassAndItemComponent_div_13_Template", "ReportClassAndItemComponent_div_17_Template", "ExportToCsv", "MenuTypeEnum", "CsvEventsReports", "ctx_r5", "orderClassCount", "ReportClassV4Component_div_8_div_2_p_1_Template", "ReportClassV4Component_div_8_div_3_Template_button_click_1_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "ExportCSV", "ReportClassV4Component_div_8_div_2_Template", "ReportClassV4Component_div_8_div_3_Template", "ReportClassV4Component_div_8_Template_button_click_5_listener", "_r9", "ctx_r8", "ResetSchoolBreak", "merchantHasManySchools", "isEventReport", "classe_r10", "school", "ctx_r12", "noResult", "GetOrdersPerSchools", "order_r20", "orderId", "canteenStatus", "item_r27", "selectedOptions", "ReportClassV4Component_div_12_tr_17_li_10_span_2_Template", "ɵɵtextInterpolate2", "quantity", "runNumber", "ReportClassV4Component_div_12_tr_17_td_1_Template", "ReportClassV4Component_div_12_tr_17_td_7_Template", "ReportClassV4Component_div_12_tr_17_li_10_Template", "ReportClassV4Component_div_12_tr_17_td_11_Template", "ctx_r17", "studentName", "items", "ReportClassV4Component_div_12_div_1_Template", "ReportClassV4Component_div_12_div_2_Template", "ReportClassV4Component_div_12_span_5_Template", "ReportClassV4Component_div_12_th_8_Template", "ReportClassV4Component_div_12_th_13_Template", "ReportClassV4Component_div_12_th_16_Template", "ReportClassV4Component_div_12_tr_17_Template", "ctx_r2", "SchoolChangedBreak", "SchoolShowTitle", "class", "orders", "ReportClassV4Component", "cd", "route", "listReportClass", "orderPerSchools", "snapshot", "Schools", "GetReport", "ClassIds", "GetReportClassOrdersBySchoolAPI", "classe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listItems", "record", "order", "Class", "Customer", "OrderDetails", "menuitem", "options", "fieldSeparator", "quoteStrings", "decimalSeparator", "showLabels", "showTitle", "title", "useTextFile", "useBom", "useKeysAsHeaders", "csvExporter", "generateCsv", "schoolNameToDisplayForPrint", "schoolNameToShowTitle", "ChangeDetectorRef", "ActivatedRoute", "i3", "ReportClassV4Component_Template", "ReportClassV4Component_Template_report_form_request_5_listener", "ReportClassV4Component_div_6_Template", "ReportClassV4Component_div_8_Template", "ReportClassV4Component_div_12_Template", "EventEmitter", "ReportFormAndFiltersComponent_div_4_Template_canteen_order_type_filter_formGroupChange_1_listener", "_r3", "menuForm", "ReportFormAndFiltersComponent_div_5_Template_canteen_order_category_filter_categoriesSelected_2_listener", "ctx_r4", "categoriesChangedEvent", "ReportFormAndFiltersComponent", "getReport", "menuTypeChange", "categoryChange", "showClassPicker", "menuData", "_createMenuTypeForm", "getReportEvent", "emit", "canteenFilters", "recess", "lunch", "event", "Boolean", "SetFilters", "outputs", "ReportFormAndFiltersComponent_Template", "ReportFormAndFiltersComponent_Template_report_form_request_2_listener", "ReportFormAndFiltersComponent_div_4_Template", "ReportFormAndFiltersComponent_div_5_Template", "Validators", "ReportRequest", "CanteenStatusEnum", "formatDateToUniversal", "ReportFormComponent_form_0_div_6_ng_container_1_mat_option_5_Template", "ReportFormComponent_form_0_div_6_ng_container_1_Template_a_click_7_listener", "toggleAllClasses", "classes", "ReportFormComponent_form_0_div_6_ng_container_1_Template", "selectedSchools", "ReportFormComponent_form_0_div_8_mat_error_2_Template", "maxDate", "endDate", "invalid", "ReportFormComponent_form_0_button_10_Template_button_click_0_listener", "_r12", "ctx_r11", "LoadReport", "ReportFormComponent_form_0_Template_multi_school_select_list_selectedSchoolChanged_5_listener", "_r14", "ctx_r13", "schoolSelectionChange", "ReportFormComponent_form_0_div_6_Template", "ReportFormComponent_form_0_div_7_Template", "ReportFormComponent_form_0_div_8_Template", "ReportFormComponent_form_0_button_10_Template", "form", "showClasses", "showStartDate", "showEndDate", "ReportFormComponent", "classService", "orderStatusService", "defaultValueClasses", "activeCanteenStatuses", "Processing", "Ready", "Completed", "New", "_createForm", "track<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilterUpdatedEvent$", "processCanteenStateUpdate", "merchantHasSchools", "hasMultipleSchools", "startDate", "startDateDefaultValue", "startOf", "getMaxDate", "required", "endOf", "value", "_SaveClassesInLocalStorage", "LoadClass", "schoolList", "localStorage", "setItem", "JSON", "stringify", "oneSchoolSelected", "start", "selectedSchoolId", "parseInt", "processClassListResponse", "stop", "getPreferedClassIds", "setValue", "savedValueClass", "parse", "getItem", "hasValidSavedClassValue", "useSavedClasses", "getArrayOfAllClassIds", "valid", "getReportRequest", "MerchantId", "SchoolsIds", "getClassIdStringForRequest", "EndDate", "CanteenStatuses", "useClassIds", "oneOrLessClassesSelected", "values", "classList", "s", "add", "subtract", "OrderStatusService", "ReportFormComponent_Template", "ReportFormComponent_form_0_Template", "CommonModule", "orderCount", "ReportHeadingComponent", "standalone", "ɵɵStandaloneFeature", "ReportHeadingComponent_Template", "ReportHeadingComponent_span_4_Template", "ReportHeadingComponent_span_5_Template", "ReportHeadingComponent_p_6_Template", "ReportHeadingComponent_div_7_Template", "NgIf", "DatePipe", "styles", "changeDetection", "animate", "style", "transition", "trigger", "element_r16", "element_r17", "option_r20", "OptionName", "ReportItemsCategoryTableComponent_table_0_td_9_span_1_Template", "GetOptionsItem", "element_r18", "element_r21", "RunNumbers", "ReportItemsCategoryTableComponent_table_0_td_15_mat_icon_2_Template", "ReportItemsCategoryTableComponent_table_0_td_15_mat_icon_3_Template", "ctx_r10", "expandedElement", "element_r22", "ɵɵattribute", "columnsToDisplayWithExpand", "element_r25", "StudentIds", "ReportItemsCategoryTableComponent_table_0_tr_20_Template_tr_click_0_listener", "restoredCtx", "_r28", "element_r26", "$implicit", "ctx_r27", "itemRowClick", "ɵɵpureFunction1", "ctx_r14", "ReportItemsCategoryTableComponent_table_0_th_2_Template", "ReportItemsCategoryTableComponent_table_0_td_3_Template", "ReportItemsCategoryTableComponent_table_0_th_5_Template", "ReportItemsCategoryTableComponent_table_0_td_6_Template", "ReportItemsCategoryTableComponent_table_0_th_8_Template", "ReportItemsCategoryTableComponent_table_0_td_9_Template", "ReportItemsCategoryTableComponent_table_0_th_11_Template", "ReportItemsCategoryTableComponent_table_0_td_12_Template", "ReportItemsCategoryTableComponent_table_0_th_14_Template", "ReportItemsCategoryTableComponent_table_0_td_15_Template", "ReportItemsCategoryTableComponent_table_0_th_17_Template", "ReportItemsCategoryTableComponent_table_0_td_18_Template", "ReportItemsCategoryTableComponent_table_0_tr_19_Template", "ReportItemsCategoryTableComponent_table_0_tr_20_Template", "ReportItemsCategoryTableComponent_table_0_tr_21_Template", "ReportItemsCategoryTableComponent", "itemReportPrintService", "accordionCloseSubscription", "ngOnChanges", "changes", "currentValue", "element", "Options", "SubOptions", "ɵɵNgOnChangesFeature", "ReportItemsCategoryTableComponent_Template", "ReportItemsCategoryTableComponent_table_0_Template", "height", "minHeight", "category_r2", "ReportItemsFilterTableComponent", "listCategories", "pageBreak", "ReportItemsFilterTableComponent_Template", "ReportItemsFilterTableComponent_report_items_category_table_3_Template", "ReportItemsFilterTableComponent_div_4_Template", "item_r4", "CanteenStatus", "option_r8", "ReportItemsTableComponent_table_3_tr_11_td_5_Template", "ReportItemsTableComponent_table_3_tr_11_span_7_Template", "ReportItemsTableComponent_table_3_th_6_Template", "ReportItemsTableComponent_table_3_tr_11_Template", "category_r1", "ReportItemsTableComponent", "ReportItemsTableComponent_Template", "ReportItemsTableComponent_table_3_Template", "ReportItemsComponent_div_3_Template_button_click_6_listener", "_r6", "closeReportRowsBeforePrint", "filteredRecessList", "filteredLunchList", "filteredEventList", "ReportItemsComponent", "reportLoadCount", "reportMenuCount", "eventMenuCount", "printSubscription", "print", "GetReportOrdersByMenuBySchoolAPI", "listCategoriesRecess", "listCategoriesLunch", "listCategoriesEvent", "getFilteredResults", "MenuCategoryId", "filterChanged", "setTimeout", "printBtn", "nativeElement", "click", "viewQuery", "ReportItemsComponent_Query", "ReportItemsComponent_Template_report_form_and_filters_getReport_1_listener", "ReportItemsComponent_Template_report_form_and_filters_categoryChange_1_listener", "ReportItemsComponent_Template_report_form_and_filters_menuTypeChange_1_listener", "ReportItemsComponent_div_3_Template", "ReportItemsComponent_report_items_filter_table_5_Template", "ReportItemsComponent_report_items_filter_table_6_Template", "ReportItemsComponent_report_items_filter_table_7_Template", "item_r5", "ReportPrintedCancelledComponent_div_9_tr_11_li_8_span_2_Template", "ReportPrintedCancelledComponent_div_9_tr_11_li_8_Template", "order_r3", "ReportPrintedCancelledComponent_div_9_tr_11_Template", "classe_r1", "ReportPrintedCancelledComponent", "GetReportOrdersPrintedCancelledAPI", "ReportPrintedCancelledComponent_Template", "ReportPrintedCancelledComponent_Template_report_form_request_4_listener", "ReportPrintedCancelledComponent_div_9_Template", "CsvSaleExport", "statusTotals", "ReportSaleV3Component_div_4_Template_button_click_3_listener", "_r4", "item_r10", "ReportSaleV3Component_ng_container_8_table_1_tr_17_td_7_Template", "ctx_r9", "GetNumberColumns", "SelectedOptions", "GetDecimalOrDefaultValue", "GstValue", "PriceExclGst", "Price", "ReportSaleV3Component_ng_container_8_table_1_th_9_Template", "ReportSaleV3Component_ng_container_8_table_1_tr_17_Template", "category_r7", "Sales", "priceColumnColspan", "TotalGST", "TotalPriceExclGST", "TotalPriceIncGST", "ReportSaleV3Component_ng_container_8_table_2_th_6_Template", "saleReport", "ReportSaleV3Component_ng_container_8_table_1_Template", "ReportSaleV3Component_ng_container_8_table_2_Template", "Categories", "ReportSaleV3Component", "orderApiService", "_decimalPipe", "c", "Category", "totalItem", "Load", "GetReportSalesAPI", "Number", "transform", "DecimalPipe", "ReportSaleV3Component_Template", "ReportSaleV3Component_Template_report_form_request_1_listener", "ReportSaleV3Component_div_2_Template", "ReportSaleV3Component_div_4_Template", "ReportSaleV3Component_ng_container_8_Template", "ReportUniformItemsComponent", "dataFetched", "canteenStatuses", "GetReports", "listCategoriesUniform", "ReportUniformItemsComponent_Query", "ReportUniformItemsComponent_Template_report_form_request_1_listener", "ReportUniformItemsComponent_div_7_Template", "StockManagement", "selectedCanteen", "StockId", "StockName", "DailyStock", "StockQuantity", "IsActive", "ReportUniformStockComponent", "menuEditorAPIService", "operatorSubscribtion", "CanteenChanged", "MenuEditorApiService", "ReportUniformStockComponent_Template", "ReportUniformStockComponent_th_9_Template", "ReportUniformStockComponent_td_10_Template", "ReportUniformStockComponent_th_12_Template", "ReportUniformStockComponent_td_13_Template", "ReportUniformStockComponent_th_15_Template", "ReportUniformStockComponent_td_16_Template", "ReportUniformStockComponent_th_18_Template", "ReportUniformStockComponent_td_19_Template", "ReportUniformStockComponent_th_21_Template", "ReportUniformStockComponent_td_22_Template", "ReportUniformStockComponent_th_24_Template", "ReportUniformStockComponent_td_25_Template", "ReportUniformStockComponent_tr_26_Template", "ReportUniformStockComponent_tr_27_Template", "ReportUnprintedComponent_div_9_tr_11_li_8_span_2_Template", "ReportUnprintedComponent_div_9_tr_11_li_8_Template", "ReportUnprintedComponent_div_9_tr_11_Template", "ReportUnprintedComponent", "GetReportOrdersUnprintedAPI", "processApiResponse", "ReportUnprintedComponent_Template", "ReportUnprintedComponent_Template_report_form_request_4_listener", "ReportUnprintedComponent_div_9_Template", "selectedVersion", "ReportVersionPickerComponent", "updateReportVersion", "versionEnum", "current", "original", "version", "originalVersion", "ReportVersionPickerComponent_Template", "ReportVersionPickerComponent_div_4_Template", "ReportVersionPickerHandler", "clickCount", "useOriginalVersion", "showVersionPicker", "DefaultClickCount", "isUseOriginalReportVersion", "setUseOriginalReportVersion", "isShowVersionPicker", "handleClick", "ClassItemQuantityPipe", "key", "findIndex", "pure", "ClassReportTotalPipe", "menuItems", "count", "ItemReportTotalPipe", "menuItemList", "menuItemArray", "mi", "RouterModule", "ListCanteensResolver", "routes", "path", "component", "resolve", "canteens", "children", "pathMatch", "redirectTo", "uniformShop", "reportType", "ReportsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "ReactiveFormsModule", "NgxPrintModule", "MatSelectModule", "MatCheckboxModule", "MatTableModule", "MatExpansionModule", "MatIconModule", "MatRadioModule", "SharedToolsModule", "SharedModule", "SchoolsFormModule", "CanteenModule", "ReportsModule", "declarations", "defineProperty", "Config<PERSON><PERSON><PERSON><PERSON>", "CsvConfigConsts", "EOL", "BOM", "DEFAULT_FIELD_SEPARATOR", "DEFAULT_DECIMAL_SEPARATOR", "DEFAULT_QUOTE", "DEFAULT_SHOW_TITLE", "DEFAULT_TITLE", "DEFAULT_FILENAME", "DEFAULT_SHOW_LABELS", "DEFAULT_USE_TEXT_FILE", "DEFAULT_USE_BOM", "DEFAULT_HEADER", "DEFAULT_KEYS_AS_HEADERS", "filename", "headers", "_csv", "config", "_options", "objectAssign", "console", "warn", "prototype", "enumerable", "configurable", "jsonData", "shouldReturnCsv", "_parseData", "_createHeaders", "_createBody", "log", "FileType", "fileExtension", "blob", "Blob", "link", "document", "createElement", "href", "URL", "createObjectURL", "setAttribute", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_getHeaders", "_data", "row", "keyPos", "slice", "header", "_formatData", "_isFloat", "toLocaleString", "indexOf", "input", "isFinite", "hasOwnProperty", "propIsEnumerable", "propertyIsEnumerable", "toObject", "undefined", "TypeError", "target", "source", "_i", "arguments", "from", "to", "symbols", "call", "getOwnPropertySymbols", "__createBinding", "create", "o", "m", "k2", "desc", "getOwnPropertyDescriptor", "__esModule", "writable", "__exportStar", "p", "require"], "sourceRoot": "webpack:///", "x_google_ignoreList": [49, 50]}