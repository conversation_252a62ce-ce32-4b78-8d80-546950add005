using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Schools.BLL.ThirdParty;
using Schools.BLL.ThirdParty.General;
using Card = Schools.BLL.Classes.Payments.Card;
using PaymentMethod = Schools.BLL.Classes.Payments.PaymentMethod;

namespace Schools.BLL.Services.Interfaces;

public interface IStripeService
{
    Task<string> CreateCustomer(string custRef);
    Task<TokenResponse> GetToken(string customerId, long amount);
    Task<bool> CreateAndConfirmPaymentIntent(string customerId, long amount, string paymentMethodId);
    Task<(bool Success, string TransactionId, string ErrorMessage)> ProcessGuestPayment(string cardNumber, int expiryMonth, int expiryYear, string cvv, string cardholderName, long amountInCents);
    Task<(bool Success, string TransactionId, string ErrorMessage)> ProcessGuestPaymentWithMethod(string paymentMethodId, long amountInCents);
    Task<List<PaymentMethod>> GetPaymentMethods(string customerId);
    Task<bool> DeletePaymentMethod(string customerId, string paymentMethodId);
    Task ProcessEvent(string json, string signature);
    Task ProcessEventWithCallback(string json, string signature, Func<string, decimal, Task> paymentIntentSuccessCallback);

    #region Test methods - delete them later

    // Task<PaymentIntent> CreatePaymentIntent(string customerId, long amount);
    Task<Stripe.PaymentMethod> AddPaymentMethod();
    Task<Stripe.PaymentMethod> AttachPaymentMethod(string customerId, string paymentMethodId);
    Task<Stripe.PaymentMethod> DeletePaymentMethod(string paymentMethodId);

    #endregion
}