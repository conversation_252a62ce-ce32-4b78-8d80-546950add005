﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes.Fees.Requests;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Controllers;


[Authorize]
[CheckUserRole(UserRole.Admin)]
[ApiController]
[Route("api/[controller]")]
[Route("[controller]")]
public class FeeCalculatorController : ControllerBase
{
    private readonly ILogger<FeeCalculatorController> _logger;
    private readonly IFeeCalculatorService _feeCalculatorService;
    public FeeCalculatorController(ILogger<FeeCalculatorController> logger, IFeeCalculatorService feeCalculatorService)
    {
        _logger = logger;
        _feeCalculatorService = feeCalculatorService;
    }

    [Route("GetAllSchoolFeeMappings")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetAllSchoolMappings()
    {
        var response = await _feeCalculatorService.GetAllSchoolFeeMappings();
        return new OkObjectResult(response);
    }

    [Route("GetAll")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var response = await _feeCalculatorService.GetAll();
        return new OkObjectResult(response);
    }

    [Route("GetFeeCalculator/{feeCalculatorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetFeeCalculator(long feeCalculatorId)
    {
        var response = await _feeCalculatorService.GetFeeCalculator(feeCalculatorId);
        return new OkObjectResult(response);
    }

    [Route("GetSchoolsByFeeCalculator/{feeCalculatorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetSchoolsByFeeCalculator(long feeCalculatorId)
    {
        var response = await _feeCalculatorService.GetSchoolsByFeeCalculator(feeCalculatorId);
        return new OkObjectResult(response);
    }

    [Route("GetFeeCalculatorByMerchantAndSchool/{merchantId}/{schoolId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetFeeCalculatorByMerchantAndSchool(long merchantId, long schoolId)
    {
        var response = await _feeCalculatorService.GetFeeCalculatorByMerchantAndSchool(merchantId, schoolId);
        return new OkObjectResult(response);
    }

    [Route("AddFeeCalculator")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> AddFeeCalculator([FromBody] AddFeeCalculatorRequest request)
    {
        var response = await _feeCalculatorService.AddFeeCalculator(request);
        return new OkObjectResult(response);
    }

    [Route("RemoveFeeCalculator/{feeCalculatorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpDelete]
    public async Task<IActionResult> RemoveFeeCalculator(long feeCalculatorId)
    {
        var response = await _feeCalculatorService.RemoveFeeCalculator(feeCalculatorId);
        return new OkObjectResult(response);
    }

    [Route("AddSchoolsToFeeCalculator")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> AddSchoolsToFeeCalculator([FromBody] SchoolsFeeCalculatorRequest request)
    {
        var response = await _feeCalculatorService.AddSchoolsToFeeCalculator(request);
        return new OkObjectResult(response);
    }

    [Route("RemoveSchoolFromFeeCalculator/{merchantId}/{schoolId}/{feeCalculatorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpDelete]
    public async Task<IActionResult> RemoveSchoolFromFeeCalculator(long merchantId, long schoolId, long feeCalculatorId)
    {
        var request = _feeCalculatorService.GetSchoolFeeCalculatorRequest(merchantId, schoolId, feeCalculatorId);
        var response = await _feeCalculatorService.RemoveSchoolsFromFeeCalculator(request);
        return new OkObjectResult(response);
    }

    [Route("UpdateSchoolsToFeeCalculator")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> UpdateSchoolsToFeeCalculator([FromBody] SchoolsFeeCalculatorRequest request)
    {
        await _feeCalculatorService.UpdateSchoolOrderTypeFeeCalculator(request);
        return new OkResult();
    }

    [Route("GetFeeCalculatorIdByName/{feeCalculatorName}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> GetFeeCalculatorIdByName(string feeCalculatorName)
    {
        var response = await _feeCalculatorService.GetFeeCalculatorIdByName(feeCalculatorName);
        return new OkObjectResult(response);
    }

    [Route("HasActiveSchoolFeeConfiguration/{merchantId}/{schoolId}/{feeCalculatorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpGet]
    public async Task<IActionResult> HasActiveSchoolFeeConfiguration(long merchantId, long schoolId, long feeCalculatorId)
    {
        var response = await _feeCalculatorService.HasActiveSchoolFeeConfiguration(merchantId, schoolId, feeCalculatorId);
        return new OkObjectResult(response);
    }
}
