﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Schools.BLL.Classes.SchoolFeature;

public static class SchoolFeatureNameConstant
{
    public const string PayAtCanteen = "Pay at Canteen";
    public const string Allergies = "Allergy Alert";
    public const string Uniform = "Uniform Shop";
}

/// <summary>
/// School features enum
/// </summary>
public enum FeatureTypeEnum
{
    PayAtCanteen = 1,
    Allergies = 2,
    Uniform = 3,
}

/// <summary>
/// school feature Dto used for create/update
/// </summary>
public class SchoolFeatureDto
{
    [Required]
    public FeatureTypeEnum Type { get; set; }

    [StringLength(200)]
    public string Description { get; set; }

    [Required]
    public bool IsActive { get; set; }
}

public class AdminSchoolFeaturesDto
{
    public List<SchoolFeatureDto> Features { get; set; }
    public bool UniformFeatureAvailable { get; set; }
}
