{"ast": null, "code": "import { signal } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\nimport { BaseComponent } from '../../../sharedModels';\nimport { validateCardNumber, validateExpiryDate, validateCVV, detectCardType, formatCardNumber } from '../../../sharedModels/guest-payment/guest-payment.models';\nimport { injectStripe, StripeCardComponent, StripeElementsDirective } from 'ngx-stripe';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../sharedServices/guest-payment/guest-payment.service\";\nimport * as i3 from \"../../../sharedServices\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nfunction GuestPaymentStripeDialogComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\", 8);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"h2\", 9);\n    i0.ɵɵtext(5, \"Payment Successful!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7, \"Your order has been placed successfully.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"p\");\n    i0.ɵɵtext(10, \"Amount: \");\n    i0.ɵɵelementStart(11, \"strong\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"Thank you for your order!\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.totalAmount));\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.name || item_r10.itemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"x\", item_r10.quantity, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatCurrency(item_r10.price * item_r10.quantity));\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_hint_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.cardType());\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Card number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Invalid card number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_1_Template, 2, 0, \"span\", 24);\n    i0.ɵɵtemplate(2, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.paymentForm.get(\"cardNumber\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.cardNumberValid() && ((tmp_1_0 = ctx_r12.paymentForm.get(\"cardNumber\")) == null ? null : tmp_1_0.value));\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const month_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", month_r21.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", month_r21.label, \" \");\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Month is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const year_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", year_r22.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", year_r22.label, \" \");\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Year is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-error\");\n    i0.ɵɵtext(2, \"Card has expired\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"CVV is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Invalid CVV\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_1_Template, 2, 0, \"span\", 24);\n    i0.ɵɵtemplate(2, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r18.paymentForm.get(\"cvv\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.cvvValid() && ((tmp_1_0 = ctx_r18.paymentForm.get(\"cvv\")) == null ? null : tmp_1_0.value));\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Test mode: Use test card **************** for testing\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-form-field\", 22)(7, \"mat-label\");\n    i0.ɵɵtext(8, \"Card Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 37);\n    i0.ɵɵelementStart(10, \"mat-icon\", 38);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, GuestPaymentStripeDialogComponent_div_12_div_14_mat_hint_12_Template, 2, 1, \"mat-hint\", 24);\n    i0.ɵɵtemplate(13, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_Template, 3, 2, \"mat-error\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"mat-form-field\", 40)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Month\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-select\", 41);\n    i0.ɵɵtemplate(19, GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_19_Template, 2, 2, \"mat-option\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_20_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"mat-form-field\", 43)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-select\", 44);\n    i0.ɵɵtemplate(25, GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_25_Template, 2, 2, \"mat-option\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_26_Template, 2, 0, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, GuestPaymentStripeDialogComponent_div_12_div_14_div_27_Template, 3, 0, \"div\", 45);\n    i0.ɵɵelementStart(28, \"mat-form-field\", 46)(29, \"mat-label\");\n    i0.ɵɵtext(30, \"CVV\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 47);\n    i0.ɵɵelementStart(32, \"mat-icon\", 48);\n    i0.ɵɵtext(33, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_Template, 3, 2, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_3_0;\n    let tmp_5_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵclassMap(ctx_r3.getCardTypeClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.getCardTypeIcon());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.cardType());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r3.paymentForm.get(\"cardNumber\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r3.paymentForm.get(\"cardNumber\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.expiryMonths);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r3.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r3.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.expiryYears);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r3.paymentForm.get(\"expiryYear\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r3.paymentForm.get(\"expiryYear\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.expiryValid() && ((tmp_8_0 = ctx_r3.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_8_0.value) && ((tmp_8_0 = ctx_r3.paymentForm.get(\"expiryYear\")) == null ? null : tmp_8_0.value));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r3.paymentForm.get(\"cvv\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r3.paymentForm.get(\"cvv\")) == null ? null : tmp_9_0.touched));\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Secure payment processing with Stripe\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53)(7, \"label\", 54);\n    i0.ɵɵtext(8, \"Card Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 55)(10, \"ngx-stripe-elements\", 56)(11, \"ngx-stripe-card\", 57);\n    i0.ɵɵlistener(\"change\", function GuestPaymentStripeDialogComponent_div_12_div_15_Template_ngx_stripe_card_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onStripeCardChange($event));\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"stripe\", ctx_r4.stripe)(\"elementsOptions\", ctx_r4.elementsOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r4.cardOptions);\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cardholder name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_1_Template, 2, 0, \"span\", 24);\n    i0.ɵɵtemplate(2, GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.paymentForm.get(\"cardholderName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r5.paymentForm.get(\"cardholderName\")) == null ? null : tmp_1_0.value) && ((tmp_1_0 = ctx_r5.paymentForm.get(\"cardholderName\")) == null ? null : tmp_1_0.value.length) < 2);\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"mat-error\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.errorMessage());\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_mat_spinner_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 59);\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Pay \", ctx_r8.formatCurrency(ctx_r8.totalAmount), \"\");\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Processing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentStripeDialogComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h2\", 13);\n    i0.ɵɵtext(2, \"Guest Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4, \"Complete your order with secure card payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 15)(6, \"h3\");\n    i0.ɵɵtext(7, \"Order Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 16);\n    i0.ɵɵtemplate(9, GuestPaymentStripeDialogComponent_div_12_div_9_Template, 7, 3, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"strong\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"form\", 19);\n    i0.ɵɵtemplate(14, GuestPaymentStripeDialogComponent_div_12_div_14_Template, 35, 11, \"div\", 20);\n    i0.ɵɵtemplate(15, GuestPaymentStripeDialogComponent_div_12_div_15_Template, 12, 3, \"div\", 21);\n    i0.ɵɵelementStart(16, \"mat-form-field\", 22)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"Cardholder Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 23);\n    i0.ɵɵtemplate(20, GuestPaymentStripeDialogComponent_div_12_mat_error_20_Template, 3, 2, \"mat-error\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, GuestPaymentStripeDialogComponent_div_12_div_21_Template, 3, 1, \"div\", 25);\n    i0.ɵɵelementStart(22, \"div\", 26)(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"Your payment information is secure and encrypted\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function GuestPaymentStripeDialogComponent_div_12_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.closeDialog());\n    });\n    i0.ɵɵtext(29, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function GuestPaymentStripeDialogComponent_div_12_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.processPayment());\n    });\n    i0.ɵɵtemplate(31, GuestPaymentStripeDialogComponent_div_12_mat_spinner_31_Template, 1, 0, \"mat-spinner\", 30);\n    i0.ɵɵtemplate(32, GuestPaymentStripeDialogComponent_div_12_span_32_Template, 2, 1, \"span\", 24);\n    i0.ɵɵtemplate(33, GuestPaymentStripeDialogComponent_div_12_span_33_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_5_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.orderItems);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Total: \", ctx_r1.formatCurrency(ctx_r1.totalAmount), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.paymentForm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTestEnvironment());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTestEnvironment());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r1.paymentForm.get(\"cardholderName\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r1.paymentForm.get(\"cardholderName\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isFormValid() || ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing());\n  }\n}\nexport class GuestPaymentStripeDialogComponent extends BaseComponent {\n  constructor(fb, guestPaymentService, appInsightsService, dialogRef, data) {\n    super();\n    this.fb = fb;\n    this.guestPaymentService = guestPaymentService;\n    this.appInsightsService = appInsightsService;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    // Stripe setup\n    this.stripe = injectStripe(environment.stripePublicKey);\n    this.isProcessing = signal(false);\n    this.paymentSuccess = signal(false);\n    this.errorMessage = signal(null);\n    // Stripe Elements state\n    this.stripeCardValid = signal(false);\n    this.stripeCardComplete = signal(false);\n    // Environment detection\n    this.isTestEnvironment = signal(true);\n    // Legacy form validation states (for test mode)\n    this.cardNumberValid = signal(false);\n    this.expiryValid = signal(false);\n    this.cvvValid = signal(false);\n    this.nameValid = signal(false);\n    this.cardType = signal('');\n    // Available options for legacy form\n    this.expiryMonths = this.guestPaymentService.getExpiryMonths();\n    this.expiryYears = this.guestPaymentService.getExpiryYears();\n    // Stripe Elements options\n    this.cardOptions = {\n      hidePostalCode: true,\n      style: {\n        base: {\n          fontSize: '16px',\n          color: '#424770',\n          '::placeholder': {\n            color: '#aab7c4'\n          }\n        }\n      }\n    };\n    this.elementsOptions = {\n      locale: 'en'\n    };\n    this.initializeOrderData();\n    this.detectEnvironment();\n    this.initializeForm();\n  }\n  ngOnInit() {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {\n      canteenId: this.canteenId.toString(),\n      totalAmount: this.totalAmount.toString(),\n      environment: this.isTestEnvironment() ? 'test' : 'production'\n    });\n  }\n  initializeOrderData() {\n    this.totalAmount = this.data.totalAmount;\n    this.orderItems = this.data.orders;\n    this.guestUserId = this.data.selectedStudent?.id || 0;\n    this.canteenId = this.data.canteenId;\n    this.menuId = this.data.menuId;\n    this.menuType = this.data.menuType;\n    this.orderDate = this.data.orderDate;\n  }\n  detectEnvironment() {\n    // Detect environment based on Stripe public key\n    const isTest = environment.stripePublicKey?.startsWith('pk_test_') || false;\n    this.isTestEnvironment.set(isTest);\n  }\n  initializeForm() {\n    if (this.isTestEnvironment()) {\n      // Legacy form for test environment\n      this.paymentForm = this.fb.group({\n        cardNumber: ['', [Validators.required]],\n        expiryMonth: ['', [Validators.required]],\n        expiryYear: ['', [Validators.required]],\n        cvv: ['', [Validators.required]],\n        cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n      });\n      this.setupLegacyFormValidation();\n    } else {\n      // Minimal form for production (Stripe Elements handles card details)\n      this.paymentForm = this.fb.group({\n        cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n      });\n    }\n  }\n  setupLegacyFormValidation() {\n    // Card number validation\n    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {\n      if (value) {\n        const isValid = validateCardNumber(value);\n        this.cardNumberValid.set(isValid);\n        this.cardType.set(detectCardType(value));\n        // Format card number\n        const formatted = formatCardNumber(value);\n        if (formatted !== value) {\n          this.paymentForm.get('cardNumber')?.setValue(formatted, {\n            emitEvent: false\n          });\n        }\n      } else {\n        this.cardNumberValid.set(false);\n        this.cardType.set('');\n      }\n    });\n    // Expiry validation\n    const validateExpiry = () => {\n      const month = parseInt(this.paymentForm.get('expiryMonth')?.value);\n      const year = parseInt(this.paymentForm.get('expiryYear')?.value);\n      if (month && year) {\n        this.expiryValid.set(validateExpiryDate(month, year));\n      } else {\n        this.expiryValid.set(false);\n      }\n    };\n    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(validateExpiry);\n    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(validateExpiry);\n    // CVV validation\n    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {\n      this.cvvValid.set(value ? validateCVV(value) : false);\n    });\n    // Name validation\n    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {\n      this.nameValid.set(value ? value.trim().length >= 2 : false);\n    });\n  }\n  // Stripe Elements event handlers\n  onStripeCardChange(event) {\n    this.stripeCardValid.set(!event.error && event.complete);\n    this.stripeCardComplete.set(event.complete);\n    if (event.error) {\n      this.errorMessage.set(event.error.message);\n    } else {\n      this.errorMessage.set(null);\n    }\n  }\n  isFormValid() {\n    if (this.isTestEnvironment()) {\n      return this.paymentForm.valid && this.cardNumberValid() && this.expiryValid() && this.cvvValid() && this.nameValid();\n    } else {\n      return this.paymentForm.valid && this.stripeCardValid() && this.stripeCardComplete();\n    }\n  }\n  processPayment() {\n    if (!this.isFormValid() || this.isProcessing()) {\n      return;\n    }\n    this.isProcessing.set(true);\n    this.errorMessage.set(null);\n    try {\n      if (this.isTestEnvironment()) {\n        this.processTestPayment();\n      } else {\n        this.processProductionPayment();\n      }\n    } catch (error) {\n      console.error('Payment processing error:', error);\n      this.errorMessage.set('An unexpected error occurred. Please try again.');\n      this.isProcessing.set(false);\n    }\n  }\n  processTestPayment() {\n    const formValue = this.paymentForm.value;\n    const request = {\n      cardNumber: formValue.cardNumber.replace(/\\s/g, ''),\n      expiryMonth: parseInt(formValue.expiryMonth),\n      expiryYear: parseInt(formValue.expiryYear),\n      cvv: formValue.cvv,\n      cardholderName: formValue.cardholderName,\n      amount: this.totalAmount,\n      canteenId: this.canteenId,\n      guestUserId: this.guestUserId,\n      items: this.orderItems.map(item => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name || item.itemName,\n        itemDescription: item.description || ''\n      })),\n      orderDate: this.orderDate,\n      menuId: this.menuId,\n      menuType: this.menuType\n    };\n    this.guestPaymentService.processPaymentAndCreateOrder(request).subscribe({\n      next: response => {\n        this.handlePaymentResponse(response);\n      },\n      error: error => {\n        this.handlePaymentError(error);\n      }\n    });\n  }\n  processProductionPayment() {\n    // Create payment method using Stripe Elements\n    this.stripe.createPaymentMethod({\n      type: 'card',\n      card: this.card.element,\n      billing_details: {\n        name: this.paymentForm.value.cardholderName\n      }\n    }).subscribe({\n      next: result => {\n        if (result.error) {\n          this.errorMessage.set(result.error.message || 'Failed to create payment method');\n          this.isProcessing.set(false);\n          return;\n        }\n        const paymentMethod = result.paymentMethod;\n        // Process payment with the backend\n        const request = {\n          paymentMethodId: paymentMethod.id,\n          amount: this.totalAmount,\n          canteenId: this.canteenId,\n          guestUserId: this.guestUserId,\n          items: this.orderItems.map(item => ({\n            menuItemId: item.menuItemId,\n            quantity: item.quantity,\n            price: item.price,\n            itemName: item.name || item.itemName,\n            itemDescription: item.description || ''\n          })),\n          orderDate: this.orderDate,\n          menuId: this.menuId,\n          menuType: this.menuType\n        };\n        this.guestPaymentService.processPaymentWithMethodAndCreateOrder(request).subscribe({\n          next: response => {\n            this.handlePaymentResponse(response);\n          },\n          error: error => {\n            this.handlePaymentError(error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Stripe payment method creation error:', error);\n        this.errorMessage.set('Failed to process payment. Please try again.');\n        this.isProcessing.set(false);\n      }\n    });\n  }\n  handlePaymentResponse(response) {\n    this.isProcessing.set(false);\n    if (response.success || response.isSuccess) {\n      this.paymentSuccess.set(true);\n      this.appInsightsService.TrackEvent('GuestPaymentSuccess', {\n        orderId: response.orderId,\n        transactionId: response.transactionId,\n        amount: this.totalAmount.toString(),\n        environment: this.isTestEnvironment() ? 'test' : 'production'\n      });\n      // Close dialog after 2 seconds\n      setTimeout(() => {\n        this.dialogRef.close({\n          success: true,\n          orderId: response.orderId,\n          message: response.message\n        });\n      }, 2000);\n    } else {\n      this.errorMessage.set(response.error || response.message || 'Payment failed');\n      this.appInsightsService.TrackEvent('GuestPaymentFailed', {\n        error: response.error || response.message,\n        amount: this.totalAmount,\n        environment: this.isTestEnvironment() ? 'test' : 'production'\n      });\n    }\n  }\n  handlePaymentError(error) {\n    this.isProcessing.set(false);\n    console.error('Payment error:', error);\n    const errorMessage = error.error?.error || error.message || 'Payment processing failed';\n    this.errorMessage.set(errorMessage);\n    this.appInsightsService.TrackEvent('GuestPaymentError', {\n      error: errorMessage,\n      amount: this.totalAmount,\n      environment: this.isTestEnvironment() ? 'test' : 'production'\n    });\n  }\n  closeDialog() {\n    this.dialogRef.close({\n      success: false\n    });\n  }\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  }\n  // Legacy form helper methods\n  getCardTypeClass() {\n    const type = this.cardType().toLowerCase();\n    return `card-type-${type}`;\n  }\n  getCardTypeIcon() {\n    switch (this.cardType()) {\n      case 'Visa':\n        return 'credit_card';\n      case 'Mastercard':\n        return 'credit_card';\n      case 'American Express':\n        return 'credit_card';\n      default:\n        return 'credit_card';\n    }\n  }\n  static {\n    this.ɵfac = function GuestPaymentStripeDialogComponent_Factory(t) {\n      return new (t || GuestPaymentStripeDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GuestPaymentService), i0.ɵɵdirectiveInject(i3.CashlessAppInsightsService), i0.ɵɵdirectiveInject(i4.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GuestPaymentStripeDialogComponent,\n      selectors: [[\"app-guest-payment-stripe-dialog\"]],\n      viewQuery: function GuestPaymentStripeDialogComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StripeCardComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.card = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 8,\n      consts: [[1, \"guest-payment-dialog\"], [1, \"close-button-container\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [1, \"environment-indicator\"], [\"class\", \"success-state\", 4, \"ngIf\"], [\"class\", \"payment-form-container\", 4, \"ngIf\"], [1, \"success-state\"], [1, \"success-icon\"], [\"color\", \"primary\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"success-details\"], [1, \"payment-form-container\"], [1, \"dialog-title\"], [1, \"dialog-subtitle\"], [1, \"order-summary\"], [1, \"order-items\"], [\"class\", \"order-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-total\"], [1, \"payment-form\", 3, \"formGroup\"], [\"class\", \"test-payment-form\", 4, \"ngIf\"], [\"class\", \"production-payment-form\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"cardholderName\", \"placeholder\", \"John Smith\", \"autocomplete\", \"cc-name\"], [4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"action-buttons\"], [\"mat-stroked-button\", \"\", 1, \"cancel-btn\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"pay-btn\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"order-item\"], [1, \"item-name\"], [1, \"item-quantity\"], [1, \"item-price\"], [1, \"test-payment-form\"], [1, \"test-mode-notice\"], [\"matInput\", \"\", \"formControlName\", \"cardNumber\", \"placeholder\", \"4242 4242 4242 4242\", \"maxlength\", \"19\", \"autocomplete\", \"cc-number\"], [\"matSuffix\", \"\"], [1, \"expiry-row\"], [\"appearance\", \"outline\", 1, \"expiry-month\"], [\"formControlName\", \"expiryMonth\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"appearance\", \"outline\", 1, \"expiry-year\"], [\"formControlName\", \"expiryYear\"], [\"class\", \"expiry-error\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"cvv-field\"], [\"matInput\", \"\", \"formControlName\", \"cvv\", \"placeholder\", \"123\", \"maxlength\", \"4\", \"type\", \"password\", \"autocomplete\", \"cc-csc\"], [\"matSuffix\", \"\", \"matTooltip\", \"3-4 digit security code on the back of your card\"], [3, \"value\"], [1, \"expiry-error\"], [1, \"production-payment-form\"], [1, \"production-mode-notice\"], [1, \"stripe-card-container\"], [1, \"stripe-card-label\"], [1, \"stripe-card-element\"], [3, \"stripe\", \"elementsOptions\"], [3, \"options\", \"change\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n      template: function GuestPaymentStripeDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-dialog-content\")(1, \"div\", 0)(2, \"div\", 1)(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function GuestPaymentStripeDialogComponent_Template_button_click_3_listener() {\n            return ctx.closeDialog();\n          });\n          i0.ɵɵelementStart(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"close\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"mat-icon\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\");\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, GuestPaymentStripeDialogComponent_div_11_Template, 15, 1, \"div\", 4);\n          i0.ɵɵtemplate(12, GuestPaymentStripeDialogComponent_div_12_Template, 34, 12, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"test-mode\", ctx.isTestEnvironment())(\"production-mode\", !ctx.isTestEnvironment());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isTestEnvironment() ? \"science\" : \"security\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isTestEnvironment() ? \"TEST MODE\" : \"PRODUCTION MODE\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.paymentSuccess());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.paymentSuccess());\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, MatDialogModule, i4.MatDialogContent, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatHint, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatButtonModule, i8.MatButton, i8.MatIconButton, MatIconModule, i9.MatIcon, MatProgressSpinnerModule, i10.MatProgressSpinner, MatSelectModule, i11.MatSelect, i12.MatOption, StripeElementsDirective, StripeCardComponent],\n      styles: [\".guest-payment-dialog[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  width: 100%;\\n  position: relative;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -10px;\\n  right: -10px;\\n  z-index: 10;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.1);\\n  color: #666;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .environment-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  margin-bottom: 16px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .environment-indicator.test-mode[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n  border: 1px solid #ffeaa7;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .environment-indicator.test-mode[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #856404;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .environment-indicator.production-mode[_ngcontent-%COMP%] {\\n  background-color: #d1ecf1;\\n  color: #0c5460;\\n  border: 1px solid #bee5eb;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .environment-indicator.production-mode[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #4caf50;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 16px;\\n  font-size: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .dialog-subtitle[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 500;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-quantity[_ngcontent-%COMP%] {\\n  margin: 0 12px;\\n  color: #666;\\n  font-size: 14px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-total[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding-top: 12px;\\n  border-top: 2px solid #dee2e6;\\n  text-align: right;\\n  font-size: 18px;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .test-mode-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background-color: #e7f3ff;\\n  border: 1px solid #b3d9ff;\\n  border-radius: 4px;\\n  margin-bottom: 16px;\\n  font-size: 14px;\\n  color: #0066cc;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .test-mode-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #0066cc;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-month[_ngcontent-%COMP%], .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-year[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .expiry-error[_ngcontent-%COMP%] {\\n  margin-top: -12px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .cvv-field[_ngcontent-%COMP%] {\\n  width: 150px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .production-mode-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background-color: #d1ecf1;\\n  border: 1px solid #bee5eb;\\n  border-radius: 4px;\\n  margin-bottom: 16px;\\n  font-size: 14px;\\n  color: #0c5460;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .production-mode-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .stripe-card-container[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .stripe-card-container[_ngcontent-%COMP%]   .stripe-card-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .stripe-card-container[_ngcontent-%COMP%]   .stripe-card-element[_ngcontent-%COMP%] {\\n  border: 1px solid #ccc;\\n  border-radius: 4px;\\n  padding: 12px;\\n  background: white;\\n  transition: border-color 0.3s ease;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .production-payment-form[_ngcontent-%COMP%]   .stripe-card-container[_ngcontent-%COMP%]   .stripe-card-element[_ngcontent-%COMP%]:focus-within {\\n  border-color: #3f51b5;\\n  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-visa[_ngcontent-%COMP%] {\\n  color: #1a1f71;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-mastercard[_ngcontent-%COMP%] {\\n  color: #eb001b;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-american[_ngcontent-%COMP%] {\\n  color: #006fcf;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  padding: 12px;\\n  background-color: #ffebee;\\n  border: 1px solid #ffcdd2;\\n  border-radius: 4px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   mat-error[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-weight: 500;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .security-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background-color: #f1f8e9;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 4px;\\n  margin: 16px 0;\\n  font-size: 14px;\\n  color: #2e7d32;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .security-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  margin-top: 24px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  position: relative;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 600px) {\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .test-payment-form[_ngcontent-%COMP%]   .cvv-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["signal", "Validators", "MAT_DIALOG_DATA", "CommonModule", "ReactiveFormsModule", "MatDialogModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSelectModule", "BaseComponent", "validateCardNumber", "validateExpiryDate", "validateCVV", "detectCardType", "formatCardNumber", "injectStripe", "StripeCardComponent", "StripeElementsDirective", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "formatCurrency", "totalAmount", "item_r10", "name", "itemName", "ɵɵtextInterpolate1", "quantity", "ctx_r2", "price", "ctx_r11", "cardType", "ɵɵtemplate", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_1_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_span_2_Template", "ɵɵproperty", "tmp_0_0", "ctx_r12", "paymentForm", "get", "errors", "cardNumberValid", "tmp_1_0", "value", "month_r21", "label", "year_r22", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_1_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_span_2_Template", "ctx_r18", "cvvValid", "ɵɵelement", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_hint_12_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_13_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_19_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_20_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_option_25_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_26_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_div_27_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_mat_error_34_Template", "ɵɵclassMap", "ctx_r3", "getCardTypeClass", "getCardTypeIcon", "tmp_3_0", "invalid", "touched", "expiry<PERSON><PERSON><PERSON>", "tmp_5_0", "expiryYears", "tmp_7_0", "expiry<PERSON><PERSON><PERSON>", "tmp_8_0", "tmp_9_0", "ɵɵlistener", "GuestPaymentStripeDialogComponent_div_12_div_15_Template_ngx_stripe_card_change_11_listener", "$event", "ɵɵrestoreView", "_r26", "ctx_r25", "ɵɵnextContext", "ɵɵresetView", "onStripeCardChange", "ctx_r4", "stripe", "elementsOptions", "cardOptions", "GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_1_Template", "GuestPaymentStripeDialogComponent_div_12_mat_error_20_span_2_Template", "ctx_r5", "length", "ctx_r6", "errorMessage", "ctx_r8", "GuestPaymentStripeDialogComponent_div_12_div_9_Template", "GuestPaymentStripeDialogComponent_div_12_div_14_Template", "GuestPaymentStripeDialogComponent_div_12_div_15_Template", "GuestPaymentStripeDialogComponent_div_12_mat_error_20_Template", "GuestPaymentStripeDialogComponent_div_12_div_21_Template", "GuestPaymentStripeDialogComponent_div_12_Template_button_click_28_listener", "_r30", "ctx_r29", "closeDialog", "GuestPaymentStripeDialogComponent_div_12_Template_button_click_30_listener", "ctx_r31", "processPayment", "GuestPaymentStripeDialogComponent_div_12_mat_spinner_31_Template", "GuestPaymentStripeDialogComponent_div_12_span_32_Template", "GuestPaymentStripeDialogComponent_div_12_span_33_Template", "ctx_r1", "orderItems", "isTestEnvironment", "isProcessing", "isFormValid", "GuestPaymentStripeDialogComponent", "constructor", "fb", "guestPaymentService", "appInsightsService", "dialogRef", "data", "stripePublicKey", "paymentSuccess", "stripeCardValid", "stripeCardComplete", "<PERSON><PERSON><PERSON><PERSON>", "getExpiryMonths", "getExpiryYears", "hidePostalCode", "style", "base", "fontSize", "color", "locale", "initializeOrderData", "detectEnvironment", "initializeForm", "ngOnInit", "TrackEvent", "canteenId", "toString", "orders", "guestUserId", "selectedStudent", "id", "menuId", "menuType", "orderDate", "isTest", "startsWith", "set", "group", "cardNumber", "required", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "cvv", "cardholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setupLegacyFormValidation", "valueChanges", "subscribe", "<PERSON><PERSON><PERSON><PERSON>", "formatted", "setValue", "emitEvent", "validateExpiry", "month", "parseInt", "year", "trim", "event", "error", "complete", "message", "valid", "processTestPayment", "processProductionPayment", "console", "formValue", "request", "replace", "amount", "items", "map", "item", "menuItemId", "itemDescription", "description", "processPaymentAndCreateOrder", "next", "response", "handlePaymentResponse", "handlePaymentError", "createPaymentMethod", "type", "card", "element", "billing_details", "result", "paymentMethod", "paymentMethodId", "processPaymentWithMethodAndCreateOrder", "success", "isSuccess", "orderId", "transactionId", "setTimeout", "close", "Intl", "NumberFormat", "currency", "format", "toLowerCase", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "GuestPaymentService", "i3", "CashlessAppInsightsService", "i4", "MatDialogRef", "selectors", "viewQuery", "GuestPaymentStripeDialogComponent_Query", "rf", "ctx", "GuestPaymentStripeDialogComponent_Template_button_click_3_listener", "GuestPaymentStripeDialogComponent_div_11_Template", "GuestPaymentStripeDialogComponent_div_12_Template", "ɵɵclassProp", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "FormGroupDirective", "FormControlName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "MatButton", "MatIconButton", "i9", "MatIcon", "i10", "MatProgressSpinner", "i11", "MatSelect", "i12", "MatOption", "styles", "changeDetection"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\guest-payment-stripe-dialog\\guest-payment-stripe-dialog.component.ts", "D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\guest-payment-stripe-dialog\\guest-payment-stripe-dialog.component.html"], "sourcesContent": ["import { Component, Inject, OnInit, signal, ViewChild, ChangeDetectionStrategy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSelectModule } from '@angular/material/select';\n\nimport { BaseComponent } from '../../../sharedModels';\nimport { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';\nimport { CashlessAppInsightsService } from '../../../sharedServices';\nimport {\n  GuestPaymentDialogData,\n  GuestPaymentDialogResult,\n  GuestPaymentRequest,\n  GuestPaymentWithMethodRequest,\n  validateCardNumber,\n  validateExpiryDate,\n  validateCVV,\n  detectCardType,\n  formatCardNumber\n} from '../../../sharedModels/guest-payment/guest-payment.models';\n\n// Stripe imports\nimport {\n  StripeCardElementChangeEvent,\n  StripeCardElementOptions,\n  StripeElementsOptions,\n  PaymentMethod\n} from '@stripe/stripe-js';\nimport {\n  injectStripe,\n  StripeCardComponent,\n  StripeElementsDirective\n} from 'ngx-stripe';\nimport { environment } from '../../../../environments/environment';\n\n@Component({\n  selector: 'app-guest-payment-stripe-dialog',\n  templateUrl: './guest-payment-stripe-dialog.component.html',\n  styleUrls: ['./guest-payment-stripe-dialog.component.scss'],\n  standalone: true,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatDialogModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatSelectModule,\n    StripeElementsDirective,\n    StripeCardComponent\n  ]\n})\nexport class GuestPaymentStripeDialogComponent extends BaseComponent implements OnInit {\n  \n  @ViewChild(StripeCardComponent) card: StripeCardComponent;\n  \n  // Stripe setup\n  readonly stripe = injectStripe(environment.stripePublicKey);\n  \n  // Form and state\n  paymentForm: FormGroup;\n  isProcessing = signal<boolean>(false);\n  paymentSuccess = signal<boolean>(false);\n  errorMessage = signal<string | null>(null);\n  \n  // Stripe Elements state\n  stripeCardValid = signal<boolean>(false);\n  stripeCardComplete = signal<boolean>(false);\n  \n  // Environment detection\n  isTestEnvironment = signal<boolean>(true);\n  \n  // Legacy form validation states (for test mode)\n  cardNumberValid = signal<boolean>(false);\n  expiryValid = signal<boolean>(false);\n  cvvValid = signal<boolean>(false);\n  nameValid = signal<boolean>(false);\n  cardType = signal<string>('');\n  \n  // Available options for legacy form\n  expiryMonths = this.guestPaymentService.getExpiryMonths();\n  expiryYears = this.guestPaymentService.getExpiryYears();\n  \n  // Order details\n  totalAmount: number;\n  orderItems: any[];\n  guestUserId: number;\n  canteenId: number;\n  menuId: number;\n  menuType: string;\n  orderDate: string;\n  \n  // Stripe Elements options\n  cardOptions: StripeCardElementOptions = {\n    hidePostalCode: true,\n    style: {\n      base: {\n        fontSize: '16px',\n        color: '#424770',\n        '::placeholder': {\n          color: '#aab7c4',\n        },\n      },\n    },\n  };\n  \n  elementsOptions: StripeElementsOptions = {\n    locale: 'en',\n  };\n\n  constructor(\n    private fb: FormBuilder,\n    private guestPaymentService: GuestPaymentService,\n    private appInsightsService: CashlessAppInsightsService,\n    public dialogRef: MatDialogRef<GuestPaymentStripeDialogComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: GuestPaymentDialogData\n  ) {\n    super();\n    this.initializeOrderData();\n    this.detectEnvironment();\n    this.initializeForm();\n  }\n\n  ngOnInit(): void {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {\n      canteenId: this.canteenId.toString(),\n      totalAmount: this.totalAmount.toString(),\n      environment: this.isTestEnvironment() ? 'test' : 'production'\n    });\n  }\n\n  private initializeOrderData(): void {\n    this.totalAmount = this.data.totalAmount;\n    this.orderItems = this.data.orders;\n    this.guestUserId = this.data.selectedStudent?.id || 0;\n    this.canteenId = this.data.canteenId;\n    this.menuId = this.data.menuId;\n    this.menuType = this.data.menuType;\n    this.orderDate = this.data.orderDate;\n  }\n\n  private detectEnvironment(): void {\n    // Detect environment based on Stripe public key\n    const isTest = environment.stripePublicKey?.startsWith('pk_test_') || false;\n    this.isTestEnvironment.set(isTest);\n  }\n\n  private initializeForm(): void {\n    if (this.isTestEnvironment()) {\n      // Legacy form for test environment\n      this.paymentForm = this.fb.group({\n        cardNumber: ['', [Validators.required]],\n        expiryMonth: ['', [Validators.required]],\n        expiryYear: ['', [Validators.required]],\n        cvv: ['', [Validators.required]],\n        cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n      });\n      \n      this.setupLegacyFormValidation();\n    } else {\n      // Minimal form for production (Stripe Elements handles card details)\n      this.paymentForm = this.fb.group({\n        cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n      });\n    }\n  }\n\n  private setupLegacyFormValidation(): void {\n    // Card number validation\n    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {\n      if (value) {\n        const isValid = validateCardNumber(value);\n        this.cardNumberValid.set(isValid);\n        this.cardType.set(detectCardType(value));\n        \n        // Format card number\n        const formatted = formatCardNumber(value);\n        if (formatted !== value) {\n          this.paymentForm.get('cardNumber')?.setValue(formatted, { emitEvent: false });\n        }\n      } else {\n        this.cardNumberValid.set(false);\n        this.cardType.set('');\n      }\n    });\n\n    // Expiry validation\n    const validateExpiry = () => {\n      const month = parseInt(this.paymentForm.get('expiryMonth')?.value);\n      const year = parseInt(this.paymentForm.get('expiryYear')?.value);\n      if (month && year) {\n        this.expiryValid.set(validateExpiryDate(month, year));\n      } else {\n        this.expiryValid.set(false);\n      }\n    };\n\n    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(validateExpiry);\n    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(validateExpiry);\n\n    // CVV validation\n    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {\n      this.cvvValid.set(value ? validateCVV(value) : false);\n    });\n\n    // Name validation\n    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {\n      this.nameValid.set(value ? value.trim().length >= 2 : false);\n    });\n  }\n\n  // Stripe Elements event handlers\n  onStripeCardChange(event: StripeCardElementChangeEvent): void {\n    this.stripeCardValid.set(!event.error && event.complete);\n    this.stripeCardComplete.set(event.complete);\n    \n    if (event.error) {\n      this.errorMessage.set(event.error.message);\n    } else {\n      this.errorMessage.set(null);\n    }\n  }\n\n  isFormValid(): boolean {\n    if (this.isTestEnvironment()) {\n      return this.paymentForm.valid && \n             this.cardNumberValid() && \n             this.expiryValid() && \n             this.cvvValid() && \n             this.nameValid();\n    } else {\n      return this.paymentForm.valid && this.stripeCardValid() && this.stripeCardComplete();\n    }\n  }\n\n  processPayment(): void {\n    if (!this.isFormValid() || this.isProcessing()) {\n      return;\n    }\n\n    this.isProcessing.set(true);\n    this.errorMessage.set(null);\n\n    try {\n      if (this.isTestEnvironment()) {\n        this.processTestPayment();\n      } else {\n        this.processProductionPayment();\n      }\n    } catch (error) {\n      console.error('Payment processing error:', error);\n      this.errorMessage.set('An unexpected error occurred. Please try again.');\n      this.isProcessing.set(false);\n    }\n  }\n\n  private processTestPayment(): void {\n    const formValue = this.paymentForm.value;\n    \n    const request: GuestPaymentRequest = {\n      cardNumber: formValue.cardNumber.replace(/\\s/g, ''),\n      expiryMonth: parseInt(formValue.expiryMonth),\n      expiryYear: parseInt(formValue.expiryYear),\n      cvv: formValue.cvv,\n      cardholderName: formValue.cardholderName,\n      amount: this.totalAmount,\n      canteenId: this.canteenId,\n      guestUserId: this.guestUserId,\n      items: this.orderItems.map(item => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name || item.itemName,\n        itemDescription: item.description || ''\n      })),\n      orderDate: this.orderDate,\n      menuId: this.menuId,\n      menuType: this.menuType\n    };\n\n    this.guestPaymentService.processPaymentAndCreateOrder(request).subscribe({\n      next: (response) => {\n        this.handlePaymentResponse(response);\n      },\n      error: (error) => {\n        this.handlePaymentError(error);\n      }\n    });\n  }\n\n  private processProductionPayment(): void {\n    // Create payment method using Stripe Elements\n    this.stripe.createPaymentMethod({\n      type: 'card',\n      card: this.card.element,\n      billing_details: {\n        name: this.paymentForm.value.cardholderName,\n      },\n    }).subscribe({\n      next: (result) => {\n        if (result.error) {\n          this.errorMessage.set(result.error.message || 'Failed to create payment method');\n          this.isProcessing.set(false);\n          return;\n        }\n\n        const paymentMethod = result.paymentMethod;\n\n        // Process payment with the backend\n        const request: GuestPaymentWithMethodRequest = {\n          paymentMethodId: paymentMethod.id,\n          amount: this.totalAmount,\n          canteenId: this.canteenId,\n          guestUserId: this.guestUserId,\n          items: this.orderItems.map(item => ({\n            menuItemId: item.menuItemId,\n            quantity: item.quantity,\n            price: item.price,\n            itemName: item.name || item.itemName,\n            itemDescription: item.description || ''\n          })),\n          orderDate: this.orderDate,\n          menuId: this.menuId,\n          menuType: this.menuType\n        };\n\n        this.guestPaymentService.processPaymentWithMethodAndCreateOrder(request).subscribe({\n          next: (response) => {\n            this.handlePaymentResponse(response);\n          },\n          error: (error) => {\n            this.handlePaymentError(error);\n          }\n        });\n      },\n      error: (error) => {\n        console.error('Stripe payment method creation error:', error);\n        this.errorMessage.set('Failed to process payment. Please try again.');\n        this.isProcessing.set(false);\n      }\n    });\n  }\n\n  private handlePaymentResponse(response: any): void {\n    this.isProcessing.set(false);\n    \n    if (response.success || response.isSuccess) {\n      this.paymentSuccess.set(true);\n      \n      this.appInsightsService.TrackEvent('GuestPaymentSuccess', {\n        orderId: response.orderId,\n        transactionId: response.transactionId,\n        amount: this.totalAmount.toString(),\n        environment: this.isTestEnvironment() ? 'test' : 'production'\n      });\n\n      // Close dialog after 2 seconds\n      setTimeout(() => {\n        this.dialogRef.close({\n          success: true,\n          orderId: response.orderId,\n          message: response.message\n        } as GuestPaymentDialogResult);\n      }, 2000);\n    } else {\n      this.errorMessage.set(response.error || response.message || 'Payment failed');\n      \n      this.appInsightsService.TrackEvent('GuestPaymentFailed', {\n        error: response.error || response.message,\n        amount: this.totalAmount,\n        environment: this.isTestEnvironment() ? 'test' : 'production'\n      });\n    }\n  }\n\n  private handlePaymentError(error: any): void {\n    this.isProcessing.set(false);\n    console.error('Payment error:', error);\n    \n    const errorMessage = error.error?.error || error.message || 'Payment processing failed';\n    this.errorMessage.set(errorMessage);\n    \n    this.appInsightsService.TrackEvent('GuestPaymentError', {\n      error: errorMessage,\n      amount: this.totalAmount,\n      environment: this.isTestEnvironment() ? 'test' : 'production'\n    });\n  }\n\n  closeDialog(): void {\n    this.dialogRef.close({\n      success: false\n    } as GuestPaymentDialogResult);\n  }\n\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  }\n\n  // Legacy form helper methods\n  getCardTypeClass(): string {\n    const type = this.cardType().toLowerCase();\n    return `card-type-${type}`;\n  }\n\n  getCardTypeIcon(): string {\n    switch (this.cardType()) {\n      case 'Visa': return 'credit_card';\n      case 'Mastercard': return 'credit_card';\n      case 'American Express': return 'credit_card';\n      default: return 'credit_card';\n    }\n  }\n}\n", "<mat-dialog-content>\n  <div class=\"guest-payment-dialog\">\n    <!-- Close Button -->\n    <div class=\"close-button-container\">\n      <button mat-icon-button class=\"close-button\" (click)=\"closeDialog()\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n\n    <!-- Environment Indicator -->\n    <div class=\"environment-indicator\" [class.test-mode]=\"isTestEnvironment()\" [class.production-mode]=\"!isTestEnvironment()\">\n      <mat-icon>{{ isTestEnvironment() ? 'science' : 'security' }}</mat-icon>\n      <span>{{ isTestEnvironment() ? 'TEST MODE' : 'PRODUCTION MODE' }}</span>\n    </div>\n\n    <!-- Success State -->\n    <div *ngIf=\"paymentSuccess()\" class=\"success-state\">\n      <div class=\"success-icon\">\n        <mat-icon color=\"primary\">check_circle</mat-icon>\n      </div>\n      <h2 class=\"success-title\">Payment Successful!</h2>\n      <p class=\"success-message\">Your order has been placed successfully.</p>\n      <div class=\"success-details\">\n        <p>Amount: <strong>{{ formatCurrency(totalAmount) }}</strong></p>\n        <p>Thank you for your order!</p>\n      </div>\n    </div>\n\n    <!-- Payment Form -->\n    <div *ngIf=\"!paymentSuccess()\" class=\"payment-form-container\">\n      <!-- Header -->\n      <h2 class=\"dialog-title\">Guest Payment</h2>\n      <p class=\"dialog-subtitle\">Complete your order with secure card payment</p>\n\n      <!-- Order Summary -->\n      <div class=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div class=\"order-items\">\n          <div *ngFor=\"let item of orderItems\" class=\"order-item\">\n            <span class=\"item-name\">{{ item.name || item.itemName }}</span>\n            <span class=\"item-quantity\">x{{ item.quantity }}</span>\n            <span class=\"item-price\">{{ formatCurrency(item.price * item.quantity) }}</span>\n          </div>\n        </div>\n        <div class=\"order-total\">\n          <strong>Total: {{ formatCurrency(totalAmount) }}</strong>\n        </div>\n      </div>\n\n      <!-- Payment Form -->\n      <form [formGroup]=\"paymentForm\" class=\"payment-form\">\n        \n        <!-- TEST ENVIRONMENT: Legacy Card Form -->\n        <div *ngIf=\"isTestEnvironment()\" class=\"test-payment-form\">\n          <div class=\"test-mode-notice\">\n            <mat-icon>info</mat-icon>\n            <span>Test mode: Use test card **************** for testing</span>\n          </div>\n\n          <!-- Card Number -->\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\n            <mat-label>Card Number</mat-label>\n            <input \n              matInput \n              formControlName=\"cardNumber\"\n              placeholder=\"4242 4242 4242 4242\"\n              maxlength=\"19\"\n              autocomplete=\"cc-number\">\n            <mat-icon matSuffix [class]=\"getCardTypeClass()\">{{ getCardTypeIcon() }}</mat-icon>\n            <mat-hint *ngIf=\"cardType()\">{{ cardType() }}</mat-hint>\n            <mat-error *ngIf=\"paymentForm.get('cardNumber')?.invalid && paymentForm.get('cardNumber')?.touched\">\n              <span *ngIf=\"paymentForm.get('cardNumber')?.errors?.['required']\">Card number is required</span>\n              <span *ngIf=\"!cardNumberValid() && paymentForm.get('cardNumber')?.value\">Invalid card number</span>\n            </mat-error>\n          </mat-form-field>\n\n          <!-- Expiry Date -->\n          <div class=\"expiry-row\">\n            <mat-form-field appearance=\"outline\" class=\"expiry-month\">\n              <mat-label>Month</mat-label>\n              <mat-select formControlName=\"expiryMonth\">\n                <mat-option *ngFor=\"let month of expiryMonths\" [value]=\"month.value\">\n                  {{ month.label }}\n                </mat-option>\n              </mat-select>\n              <mat-error *ngIf=\"paymentForm.get('expiryMonth')?.invalid && paymentForm.get('expiryMonth')?.touched\">\n                Month is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"expiry-year\">\n              <mat-label>Year</mat-label>\n              <mat-select formControlName=\"expiryYear\">\n                <mat-option *ngFor=\"let year of expiryYears\" [value]=\"year.value\">\n                  {{ year.label }}\n                </mat-option>\n              </mat-select>\n              <mat-error *ngIf=\"paymentForm.get('expiryYear')?.invalid && paymentForm.get('expiryYear')?.touched\">\n                Year is required\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <div *ngIf=\"!expiryValid() && paymentForm.get('expiryMonth')?.value && paymentForm.get('expiryYear')?.value\" \n               class=\"expiry-error\">\n            <mat-error>Card has expired</mat-error>\n          </div>\n\n          <!-- CVV -->\n          <mat-form-field appearance=\"outline\" class=\"cvv-field\">\n            <mat-label>CVV</mat-label>\n            <input \n              matInput \n              formControlName=\"cvv\"\n              placeholder=\"123\"\n              maxlength=\"4\"\n              type=\"password\"\n              autocomplete=\"cc-csc\">\n            <mat-icon matSuffix matTooltip=\"3-4 digit security code on the back of your card\">help_outline</mat-icon>\n            <mat-error *ngIf=\"paymentForm.get('cvv')?.invalid && paymentForm.get('cvv')?.touched\">\n              <span *ngIf=\"paymentForm.get('cvv')?.errors?.['required']\">CVV is required</span>\n              <span *ngIf=\"!cvvValid() && paymentForm.get('cvv')?.value\">Invalid CVV</span>\n            </mat-error>\n          </mat-form-field>\n        </div>\n\n        <!-- PRODUCTION ENVIRONMENT: Stripe Elements -->\n        <div *ngIf=\"!isTestEnvironment()\" class=\"production-payment-form\">\n          <div class=\"production-mode-notice\">\n            <mat-icon>security</mat-icon>\n            <span>Secure payment processing with Stripe</span>\n          </div>\n\n          <!-- Stripe Card Element -->\n          <div class=\"stripe-card-container\">\n            <label class=\"stripe-card-label\">Card Details</label>\n            <div class=\"stripe-card-element\">\n              <ngx-stripe-elements [stripe]=\"stripe\" [elementsOptions]=\"elementsOptions\">\n                <ngx-stripe-card\n                  [options]=\"cardOptions\"\n                  (change)=\"onStripeCardChange($event)\">\n                </ngx-stripe-card>\n              </ngx-stripe-elements>\n            </div>\n          </div>\n        </div>\n\n        <!-- Cardholder Name (Both Environments) -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Cardholder Name</mat-label>\n          <input \n            matInput \n            formControlName=\"cardholderName\"\n            placeholder=\"John Smith\"\n            autocomplete=\"cc-name\">\n          <mat-error *ngIf=\"paymentForm.get('cardholderName')?.invalid && paymentForm.get('cardholderName')?.touched\">\n            <span *ngIf=\"paymentForm.get('cardholderName')?.errors?.['required']\">Cardholder name is required</span>\n            <span *ngIf=\"paymentForm.get('cardholderName')?.value && paymentForm.get('cardholderName')?.value.length < 2\">Name must be at least 2 characters</span>\n          </mat-error>\n        </mat-form-field>\n      </form>\n\n      <!-- Error Message -->\n      <div *ngIf=\"errorMessage()\" class=\"error-message\">\n        <mat-error>{{ errorMessage() }}</mat-error>\n      </div>\n\n      <!-- Security Notice -->\n      <div class=\"security-notice\">\n        <mat-icon>security</mat-icon>\n        <span>Your payment information is secure and encrypted</span>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"action-buttons\">\n        <button \n          mat-stroked-button \n          class=\"cancel-btn\" \n          (click)=\"closeDialog()\"\n          [disabled]=\"isProcessing()\">\n          Cancel\n        </button>\n        \n        <button \n          mat-raised-button \n          color=\"primary\"\n          class=\"pay-btn\"\n          (click)=\"processPayment()\"\n          [disabled]=\"!isFormValid() || isProcessing()\">\n          <mat-spinner *ngIf=\"isProcessing()\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!isProcessing()\">Pay {{ formatCurrency(totalAmount) }}</span>\n          <span *ngIf=\"isProcessing()\">Processing...</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</mat-dialog-content>\n"], "mappings": "AAAA,SAAoCA,MAAM,QAA4C,eAAe;AACrG,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,eAAe,QAAsB,0BAA0B;AACxE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,aAAa,QAAQ,uBAAuB;AAGrD,SAKEC,kBAAkB,EAClBC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdC,gBAAgB,QACX,0DAA0D;AASjE,SACEC,YAAY,EACZC,mBAAmB,EACnBC,uBAAuB,QAClB,YAAY;AACnB,SAASC,WAAW,QAAQ,sCAAsC;;;;;;;;;;;;;;;;ICxB9DC,EAAA,CAAAC,cAAA,aAAoD;IAEtBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnDH,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,cAA6B;IACxBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADbH,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAE,WAAA,EAAiC;;;;;IAelDR,EAAA,CAAAC,cAAA,cAAwD;IAC9BD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFxDH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAI,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAE,QAAA,CAAgC;IAC5BX,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,kBAAA,MAAAH,QAAA,CAAAI,QAAA,KAAoB;IACvBb,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAS,MAAA,CAAAP,cAAA,CAAAE,QAAA,CAAAM,KAAA,GAAAN,QAAA,CAAAI,QAAA,EAAgD;;;;;IA4BzEb,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA3BH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAW,OAAA,CAAAC,QAAA,GAAgB;;;;;IAE3CjB,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChGH,EAAA,CAAAC,cAAA,WAAyE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFrGH,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAkB,UAAA,IAAAC,4EAAA,mBAAgG;IAChGnB,EAAA,CAAAkB,UAAA,IAAAE,4EAAA,mBAAmG;IACrGpB,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAC,OAAA,CAAAC,WAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;IACzD1B,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAqB,UAAA,UAAAE,OAAA,CAAAI,eAAA,QAAAC,OAAA,GAAAL,OAAA,CAAAC,WAAA,CAAAC,GAAA,iCAAAG,OAAA,CAAAC,KAAA,EAAgE;;;;;IASrE7B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAqB,UAAA,UAAAS,SAAA,CAAAD,KAAA,CAAqB;IAClE7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAkB,SAAA,CAAAC,KAAA,MACF;;;;;IAEF/B,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMVH,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAqB,UAAA,UAAAW,QAAA,CAAAH,KAAA,CAAoB;IAC/D7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAoB,QAAA,CAAAD,KAAA,MACF;;;;;IAEF/B,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAIhBH,EAAA,CAAAC,cAAA,cAC0B;IACbD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAerCH,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjFH,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF/EH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAkB,UAAA,IAAAe,4EAAA,mBAAiF;IACjFjC,EAAA,CAAAkB,UAAA,IAAAgB,4EAAA,mBAA6E;IAC/ElC,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAa,OAAA,CAAAX,WAAA,CAAAC,GAAA,0BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkD;IAClD1B,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAqB,UAAA,UAAAc,OAAA,CAAAC,QAAA,QAAAR,OAAA,GAAAO,OAAA,CAAAX,WAAA,CAAAC,GAAA,0BAAAG,OAAA,CAAAC,KAAA,EAAkD;;;;;IApE/D7B,EAAA,CAAAC,cAAA,cAA2D;IAE7CD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIpEH,EAAA,CAAAC,cAAA,yBAAwD;IAC3CD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAqC,SAAA,gBAK2B;IAC3BrC,EAAA,CAAAC,cAAA,oBAAiD;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnFH,EAAA,CAAAkB,UAAA,KAAAoB,oEAAA,uBAAwD;IACxDtC,EAAA,CAAAkB,UAAA,KAAAqB,qEAAA,wBAGY;IACdvC,EAAA,CAAAG,YAAA,EAAiB;IAGjBH,EAAA,CAAAC,cAAA,eAAwB;IAETD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAC,cAAA,sBAA0C;IACxCD,EAAA,CAAAkB,UAAA,KAAAsB,sEAAA,yBAEa;IACfxC,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAkB,UAAA,KAAAuB,qEAAA,wBAEY;IACdzC,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,0BAAyD;IAC5CD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3BH,EAAA,CAAAC,cAAA,sBAAyC;IACvCD,EAAA,CAAAkB,UAAA,KAAAwB,sEAAA,yBAEa;IACf1C,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAkB,UAAA,KAAAyB,qEAAA,wBAEY;IACd3C,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAkB,UAAA,KAAA0B,+DAAA,kBAGM;IAGN5C,EAAA,CAAAC,cAAA,0BAAuD;IAC1CD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1BH,EAAA,CAAAqC,SAAA,iBAMwB;IACxBrC,EAAA,CAAAC,cAAA,oBAAkF;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzGH,EAAA,CAAAkB,UAAA,KAAA2B,qEAAA,wBAGY;IACd7C,EAAA,CAAAG,YAAA,EAAiB;;;;;;;;;IAvDKH,EAAA,CAAAI,SAAA,IAA4B;IAA5BJ,EAAA,CAAA8C,UAAA,CAAAC,MAAA,CAAAC,gBAAA,GAA4B;IAAChD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAA0C,MAAA,CAAAE,eAAA,GAAuB;IAC7DjD,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAqB,UAAA,SAAA0B,MAAA,CAAA9B,QAAA,GAAgB;IACfjB,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAqB,UAAA,WAAA6B,OAAA,GAAAH,MAAA,CAAAvB,WAAA,CAAAC,GAAA,iCAAAyB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAH,MAAA,CAAAvB,WAAA,CAAAC,GAAA,iCAAAyB,OAAA,CAAAE,OAAA,EAAsF;IAWhEpD,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAqB,UAAA,YAAA0B,MAAA,CAAAM,YAAA,CAAe;IAInCrD,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAqB,UAAA,WAAAiC,OAAA,GAAAP,MAAA,CAAAvB,WAAA,CAAAC,GAAA,kCAAA6B,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAP,MAAA,CAAAvB,WAAA,CAAAC,GAAA,kCAAA6B,OAAA,CAAAF,OAAA,EAAwF;IAQrEpD,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAqB,UAAA,YAAA0B,MAAA,CAAAQ,WAAA,CAAc;IAIjCvD,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAqB,UAAA,WAAAmC,OAAA,GAAAT,MAAA,CAAAvB,WAAA,CAAAC,GAAA,iCAAA+B,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAT,MAAA,CAAAvB,WAAA,CAAAC,GAAA,iCAAA+B,OAAA,CAAAJ,OAAA,EAAsF;IAMhGpD,EAAA,CAAAI,SAAA,GAAqG;IAArGJ,EAAA,CAAAqB,UAAA,UAAA0B,MAAA,CAAAU,WAAA,QAAAC,OAAA,GAAAX,MAAA,CAAAvB,WAAA,CAAAC,GAAA,kCAAAiC,OAAA,CAAA7B,KAAA,OAAA6B,OAAA,GAAAX,MAAA,CAAAvB,WAAA,CAAAC,GAAA,iCAAAiC,OAAA,CAAA7B,KAAA,EAAqG;IAgB7F7B,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAqB,UAAA,WAAAsC,OAAA,GAAAZ,MAAA,CAAAvB,WAAA,CAAAC,GAAA,0BAAAkC,OAAA,CAAAR,OAAA,OAAAQ,OAAA,GAAAZ,MAAA,CAAAvB,WAAA,CAAAC,GAAA,0BAAAkC,OAAA,CAAAP,OAAA,EAAwE;;;;;;IAQxFpD,EAAA,CAAAC,cAAA,cAAkE;IAEpDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIpDH,EAAA,CAAAC,cAAA,cAAmC;IACAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAC,cAAA,cAAiC;IAI3BD,EAAA,CAAA4D,UAAA,oBAAAC,4FAAAC,MAAA;MAAA9D,EAAA,CAAA+D,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAkE,aAAA;MAAA,OAAUlE,EAAA,CAAAmE,WAAA,CAAAF,OAAA,CAAAG,kBAAA,CAAAN,MAAA,CAA0B;IAAA,EAAC;IACvC9D,EAAA,CAAAG,YAAA,EAAkB;;;;IAJCH,EAAA,CAAAI,SAAA,IAAiB;IAAjBJ,EAAA,CAAAqB,UAAA,WAAAgD,MAAA,CAAAC,MAAA,CAAiB,oBAAAD,MAAA,CAAAE,eAAA;IAElCvE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAqB,UAAA,YAAAgD,MAAA,CAAAG,WAAA,CAAuB;;;;;IAiB7BxE,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxGH,EAAA,CAAAC,cAAA,WAA8G;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzJH,EAAA,CAAAC,cAAA,gBAA4G;IAC1GD,EAAA,CAAAkB,UAAA,IAAAuD,qEAAA,mBAAwG;IACxGzE,EAAA,CAAAkB,UAAA,IAAAwD,qEAAA,mBAAuJ;IACzJ1E,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAqD,MAAA,CAAAnD,WAAA,CAAAC,GAAA,qCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA6D;IAC7D1B,EAAA,CAAAI,SAAA,GAAqG;IAArGJ,EAAA,CAAAqB,UAAA,WAAAO,OAAA,GAAA+C,MAAA,CAAAnD,WAAA,CAAAC,GAAA,qCAAAG,OAAA,CAAAC,KAAA,OAAAD,OAAA,GAAA+C,MAAA,CAAAnD,WAAA,CAAAC,GAAA,qCAAAG,OAAA,CAAAC,KAAA,CAAA+C,MAAA,MAAqG;;;;;IAMlH5E,EAAA,CAAAC,cAAA,cAAkD;IACrCD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhCH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAwE,MAAA,CAAAC,YAAA,GAAoB;;;;;IAyB7B9E,EAAA,CAAAqC,SAAA,sBAAgE;;;;;IAChErC,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5CH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,kBAAA,SAAAmE,MAAA,CAAAxE,cAAA,CAAAwE,MAAA,CAAAvE,WAAA,MAAqC;;;;;IACnER,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlKvDH,EAAA,CAAAC,cAAA,cAA8D;IAEnCD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG3EH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,UAAA,IAAA8D,uDAAA,kBAIM;IACRhF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAK7DH,EAAA,CAAAC,cAAA,gBAAqD;IAGnDD,EAAA,CAAAkB,UAAA,KAAA+D,wDAAA,oBAuEM;IAGNjF,EAAA,CAAAkB,UAAA,KAAAgE,wDAAA,mBAkBM;IAGNlF,EAAA,CAAAC,cAAA,0BAAwD;IAC3CD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAqC,SAAA,iBAIyB;IACzBrC,EAAA,CAAAkB,UAAA,KAAAiE,8DAAA,wBAGY;IACdnF,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAkB,UAAA,KAAAkE,wDAAA,kBAEM;IAGNpF,EAAA,CAAAC,cAAA,eAA6B;IACjBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/DH,EAAA,CAAAC,cAAA,eAA4B;IAIxBD,EAAA,CAAA4D,UAAA,mBAAAyB,2EAAA;MAAArF,EAAA,CAAA+D,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAkE,aAAA;MAAA,OAASlE,EAAA,CAAAmE,WAAA,CAAAoB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAEvBxF,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKgD;IAD9CD,EAAA,CAAA4D,UAAA,mBAAA6B,2EAAA;MAAAzF,EAAA,CAAA+D,aAAA,CAAAuB,IAAA;MAAA,MAAAI,OAAA,GAAA1F,EAAA,CAAAkE,aAAA;MAAA,OAASlE,EAAA,CAAAmE,WAAA,CAAAuB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAE1B3F,EAAA,CAAAkB,UAAA,KAAA0E,gEAAA,0BAAgE;IAChE5F,EAAA,CAAAkB,UAAA,KAAA2E,yDAAA,mBAA0E;IAC1E7F,EAAA,CAAAkB,UAAA,KAAA4E,yDAAA,mBAAiD;IACnD9F,EAAA,CAAAG,YAAA,EAAS;;;;;IA1JeH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAqB,UAAA,YAAA0E,MAAA,CAAAC,UAAA,CAAa;IAO3BhG,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAY,kBAAA,YAAAmF,MAAA,CAAAxF,cAAA,CAAAwF,MAAA,CAAAvF,WAAA,MAAwC;IAK9CR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,cAAA0E,MAAA,CAAAvE,WAAA,CAAyB;IAGvBxB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,SAAA0E,MAAA,CAAAE,iBAAA,GAAyB;IA0EzBjG,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAqB,UAAA,UAAA0E,MAAA,CAAAE,iBAAA,GAA0B;IA4BlBjG,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAqB,UAAA,WAAAiC,OAAA,GAAAyC,MAAA,CAAAvE,WAAA,CAAAC,GAAA,qCAAA6B,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAyC,MAAA,CAAAvE,WAAA,CAAAC,GAAA,qCAAA6B,OAAA,CAAAF,OAAA,EAA8F;IAQxGpD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAA0E,MAAA,CAAAjB,YAAA,GAAoB;IAgBtB9E,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAqB,UAAA,aAAA0E,MAAA,CAAAG,YAAA,GAA2B;IAS3BlG,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,UAAA,cAAA0E,MAAA,CAAAI,WAAA,MAAAJ,MAAA,CAAAG,YAAA,GAA6C;IAC/BlG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAA0E,MAAA,CAAAG,YAAA,GAAoB;IAC3BlG,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,UAAA0E,MAAA,CAAAG,YAAA,GAAqB;IACrBlG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAA0E,MAAA,CAAAG,YAAA,GAAoB;;;ADjIrC,OAAM,MAAOE,iCAAkC,SAAQ9G,aAAa;EA0DlE+G,YACUC,EAAe,EACfC,mBAAwC,EACxCC,kBAA8C,EAC/CC,SAA0D,EACjCC,IAA4B;IAE5D,KAAK,EAAE;IANC,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IA3DtC;IACS,KAAApC,MAAM,GAAG1E,YAAY,CAACG,WAAW,CAAC4G,eAAe,CAAC;IAI3D,KAAAT,YAAY,GAAGxH,MAAM,CAAU,KAAK,CAAC;IACrC,KAAAkI,cAAc,GAAGlI,MAAM,CAAU,KAAK,CAAC;IACvC,KAAAoG,YAAY,GAAGpG,MAAM,CAAgB,IAAI,CAAC;IAE1C;IACA,KAAAmI,eAAe,GAAGnI,MAAM,CAAU,KAAK,CAAC;IACxC,KAAAoI,kBAAkB,GAAGpI,MAAM,CAAU,KAAK,CAAC;IAE3C;IACA,KAAAuH,iBAAiB,GAAGvH,MAAM,CAAU,IAAI,CAAC;IAEzC;IACA,KAAAiD,eAAe,GAAGjD,MAAM,CAAU,KAAK,CAAC;IACxC,KAAA+E,WAAW,GAAG/E,MAAM,CAAU,KAAK,CAAC;IACpC,KAAA0D,QAAQ,GAAG1D,MAAM,CAAU,KAAK,CAAC;IACjC,KAAAqI,SAAS,GAAGrI,MAAM,CAAU,KAAK,CAAC;IAClC,KAAAuC,QAAQ,GAAGvC,MAAM,CAAS,EAAE,CAAC;IAE7B;IACA,KAAA2E,YAAY,GAAG,IAAI,CAACkD,mBAAmB,CAACS,eAAe,EAAE;IACzD,KAAAzD,WAAW,GAAG,IAAI,CAACgD,mBAAmB,CAACU,cAAc,EAAE;IAWvD;IACA,KAAAzC,WAAW,GAA6B;MACtC0C,cAAc,EAAE,IAAI;MACpBC,KAAK,EAAE;QACLC,IAAI,EAAE;UACJC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChB,eAAe,EAAE;YACfA,KAAK,EAAE;;;;KAId;IAED,KAAA/C,eAAe,GAA0B;MACvCgD,MAAM,EAAE;KACT;IAUC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnB,kBAAkB,CAACoB,UAAU,CAAC,0BAA0B,EAAE;MAC7DC,SAAS,EAAE,IAAI,CAACA,SAAS,CAACC,QAAQ,EAAE;MACpCtH,WAAW,EAAE,IAAI,CAACA,WAAW,CAACsH,QAAQ,EAAE;MACxC/H,WAAW,EAAE,IAAI,CAACkG,iBAAiB,EAAE,GAAG,MAAM,GAAG;KAClD,CAAC;EACJ;EAEQuB,mBAAmBA,CAAA;IACzB,IAAI,CAAChH,WAAW,GAAG,IAAI,CAACkG,IAAI,CAAClG,WAAW;IACxC,IAAI,CAACwF,UAAU,GAAG,IAAI,CAACU,IAAI,CAACqB,MAAM;IAClC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACtB,IAAI,CAACuB,eAAe,EAAEC,EAAE,IAAI,CAAC;IACrD,IAAI,CAACL,SAAS,GAAG,IAAI,CAACnB,IAAI,CAACmB,SAAS;IACpC,IAAI,CAACM,MAAM,GAAG,IAAI,CAACzB,IAAI,CAACyB,MAAM;IAC9B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC1B,IAAI,CAAC0B,QAAQ;IAClC,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC3B,IAAI,CAAC2B,SAAS;EACtC;EAEQZ,iBAAiBA,CAAA;IACvB;IACA,MAAMa,MAAM,GAAGvI,WAAW,CAAC4G,eAAe,EAAE4B,UAAU,CAAC,UAAU,CAAC,IAAI,KAAK;IAC3E,IAAI,CAACtC,iBAAiB,CAACuC,GAAG,CAACF,MAAM,CAAC;EACpC;EAEQZ,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACzB,iBAAiB,EAAE,EAAE;MAC5B;MACA,IAAI,CAACzE,WAAW,GAAG,IAAI,CAAC8E,EAAE,CAACmC,KAAK,CAAC;QAC/BC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC/J,UAAU,CAACgK,QAAQ,CAAC,CAAC;QACvCC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACjK,UAAU,CAACgK,QAAQ,CAAC,CAAC;QACxCE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAClK,UAAU,CAACgK,QAAQ,CAAC,CAAC;QACvCG,GAAG,EAAE,CAAC,EAAE,EAAE,CAACnK,UAAU,CAACgK,QAAQ,CAAC,CAAC;QAChCI,cAAc,EAAE,CAAC,EAAE,EAAE,CAACpK,UAAU,CAACgK,QAAQ,EAAEhK,UAAU,CAACqK,SAAS,CAAC,CAAC,CAAC,CAAC;OACpE,CAAC;MAEF,IAAI,CAACC,yBAAyB,EAAE;KACjC,MAAM;MACL;MACA,IAAI,CAACzH,WAAW,GAAG,IAAI,CAAC8E,EAAE,CAACmC,KAAK,CAAC;QAC/BM,cAAc,EAAE,CAAC,EAAE,EAAE,CAACpK,UAAU,CAACgK,QAAQ,EAAEhK,UAAU,CAACqK,SAAS,CAAC,CAAC,CAAC,CAAC;OACpE,CAAC;;EAEN;EAEQC,yBAAyBA,CAAA;IAC/B;IACA,IAAI,CAACzH,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEyH,YAAY,CAACC,SAAS,CAACtH,KAAK,IAAG;MACjE,IAAIA,KAAK,EAAE;QACT,MAAMuH,OAAO,GAAG7J,kBAAkB,CAACsC,KAAK,CAAC;QACzC,IAAI,CAACF,eAAe,CAAC6G,GAAG,CAACY,OAAO,CAAC;QACjC,IAAI,CAACnI,QAAQ,CAACuH,GAAG,CAAC9I,cAAc,CAACmC,KAAK,CAAC,CAAC;QAExC;QACA,MAAMwH,SAAS,GAAG1J,gBAAgB,CAACkC,KAAK,CAAC;QACzC,IAAIwH,SAAS,KAAKxH,KAAK,EAAE;UACvB,IAAI,CAACL,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE6H,QAAQ,CAACD,SAAS,EAAE;YAAEE,SAAS,EAAE;UAAK,CAAE,CAAC;;OAEhF,MAAM;QACL,IAAI,CAAC5H,eAAe,CAAC6G,GAAG,CAAC,KAAK,CAAC;QAC/B,IAAI,CAACvH,QAAQ,CAACuH,GAAG,CAAC,EAAE,CAAC;;IAEzB,CAAC,CAAC;IAEF;IACA,MAAMgB,cAAc,GAAGA,CAAA,KAAK;MAC1B,MAAMC,KAAK,GAAGC,QAAQ,CAAC,IAAI,CAAClI,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEI,KAAK,CAAC;MAClE,MAAM8H,IAAI,GAAGD,QAAQ,CAAC,IAAI,CAAClI,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEI,KAAK,CAAC;MAChE,IAAI4H,KAAK,IAAIE,IAAI,EAAE;QACjB,IAAI,CAAClG,WAAW,CAAC+E,GAAG,CAAChJ,kBAAkB,CAACiK,KAAK,EAAEE,IAAI,CAAC,CAAC;OACtD,MAAM;QACL,IAAI,CAAClG,WAAW,CAAC+E,GAAG,CAAC,KAAK,CAAC;;IAE/B,CAAC;IAED,IAAI,CAAChH,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEyH,YAAY,CAACC,SAAS,CAACK,cAAc,CAAC;IAC3E,IAAI,CAAChI,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEyH,YAAY,CAACC,SAAS,CAACK,cAAc,CAAC;IAE1E;IACA,IAAI,CAAChI,WAAW,CAACC,GAAG,CAAC,KAAK,CAAC,EAAEyH,YAAY,CAACC,SAAS,CAACtH,KAAK,IAAG;MAC1D,IAAI,CAACO,QAAQ,CAACoG,GAAG,CAAC3G,KAAK,GAAGpC,WAAW,CAACoC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvD,CAAC,CAAC;IAEF;IACA,IAAI,CAACL,WAAW,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEyH,YAAY,CAACC,SAAS,CAACtH,KAAK,IAAG;MACrE,IAAI,CAACkF,SAAS,CAACyB,GAAG,CAAC3G,KAAK,GAAGA,KAAK,CAAC+H,IAAI,EAAE,CAAChF,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC;IAC9D,CAAC,CAAC;EACJ;EAEA;EACAR,kBAAkBA,CAACyF,KAAmC;IACpD,IAAI,CAAChD,eAAe,CAAC2B,GAAG,CAAC,CAACqB,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,QAAQ,CAAC;IACxD,IAAI,CAACjD,kBAAkB,CAAC0B,GAAG,CAACqB,KAAK,CAACE,QAAQ,CAAC;IAE3C,IAAIF,KAAK,CAACC,KAAK,EAAE;MACf,IAAI,CAAChF,YAAY,CAAC0D,GAAG,CAACqB,KAAK,CAACC,KAAK,CAACE,OAAO,CAAC;KAC3C,MAAM;MACL,IAAI,CAAClF,YAAY,CAAC0D,GAAG,CAAC,IAAI,CAAC;;EAE/B;EAEArC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,iBAAiB,EAAE,EAAE;MAC5B,OAAO,IAAI,CAACzE,WAAW,CAACyI,KAAK,IACtB,IAAI,CAACtI,eAAe,EAAE,IACtB,IAAI,CAAC8B,WAAW,EAAE,IAClB,IAAI,CAACrB,QAAQ,EAAE,IACf,IAAI,CAAC2E,SAAS,EAAE;KACxB,MAAM;MACL,OAAO,IAAI,CAACvF,WAAW,CAACyI,KAAK,IAAI,IAAI,CAACpD,eAAe,EAAE,IAAI,IAAI,CAACC,kBAAkB,EAAE;;EAExF;EAEAnB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACQ,WAAW,EAAE,IAAI,IAAI,CAACD,YAAY,EAAE,EAAE;MAC9C;;IAGF,IAAI,CAACA,YAAY,CAACsC,GAAG,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC1D,YAAY,CAAC0D,GAAG,CAAC,IAAI,CAAC;IAE3B,IAAI;MACF,IAAI,IAAI,CAACvC,iBAAiB,EAAE,EAAE;QAC5B,IAAI,CAACiE,kBAAkB,EAAE;OAC1B,MAAM;QACL,IAAI,CAACC,wBAAwB,EAAE;;KAElC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAI,CAAChF,YAAY,CAAC0D,GAAG,CAAC,iDAAiD,CAAC;MACxE,IAAI,CAACtC,YAAY,CAACsC,GAAG,CAAC,KAAK,CAAC;;EAEhC;EAEQ0B,kBAAkBA,CAAA;IACxB,MAAMG,SAAS,GAAG,IAAI,CAAC7I,WAAW,CAACK,KAAK;IAExC,MAAMyI,OAAO,GAAwB;MACnC5B,UAAU,EAAE2B,SAAS,CAAC3B,UAAU,CAAC6B,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACnD3B,WAAW,EAAEc,QAAQ,CAACW,SAAS,CAACzB,WAAW,CAAC;MAC5CC,UAAU,EAAEa,QAAQ,CAACW,SAAS,CAACxB,UAAU,CAAC;MAC1CC,GAAG,EAAEuB,SAAS,CAACvB,GAAG;MAClBC,cAAc,EAAEsB,SAAS,CAACtB,cAAc;MACxCyB,MAAM,EAAE,IAAI,CAAChK,WAAW;MACxBqH,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBG,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7ByC,KAAK,EAAE,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAACC,IAAI,KAAK;QAClCC,UAAU,EAAED,IAAI,CAACC,UAAU;QAC3B/J,QAAQ,EAAE8J,IAAI,CAAC9J,QAAQ;QACvBE,KAAK,EAAE4J,IAAI,CAAC5J,KAAK;QACjBJ,QAAQ,EAAEgK,IAAI,CAACjK,IAAI,IAAIiK,IAAI,CAAChK,QAAQ;QACpCkK,eAAe,EAAEF,IAAI,CAACG,WAAW,IAAI;OACtC,CAAC,CAAC;MACHzC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBF,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,QAAQ,EAAE,IAAI,CAACA;KAChB;IAED,IAAI,CAAC7B,mBAAmB,CAACwE,4BAA4B,CAACT,OAAO,CAAC,CAACnB,SAAS,CAAC;MACvE6B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACC,qBAAqB,CAACD,QAAQ,CAAC;MACtC,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACqB,kBAAkB,CAACrB,KAAK,CAAC;MAChC;KACD,CAAC;EACJ;EAEQK,wBAAwBA,CAAA;IAC9B;IACA,IAAI,CAAC7F,MAAM,CAAC8G,mBAAmB,CAAC;MAC9BC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,IAAI,CAACA,IAAI,CAACC,OAAO;MACvBC,eAAe,EAAE;QACf9K,IAAI,EAAE,IAAI,CAACc,WAAW,CAACK,KAAK,CAACkH;;KAEhC,CAAC,CAACI,SAAS,CAAC;MACX6B,IAAI,EAAGS,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC3B,KAAK,EAAE;UAChB,IAAI,CAAChF,YAAY,CAAC0D,GAAG,CAACiD,MAAM,CAAC3B,KAAK,CAACE,OAAO,IAAI,iCAAiC,CAAC;UAChF,IAAI,CAAC9D,YAAY,CAACsC,GAAG,CAAC,KAAK,CAAC;UAC5B;;QAGF,MAAMkD,aAAa,GAAGD,MAAM,CAACC,aAAa;QAE1C;QACA,MAAMpB,OAAO,GAAkC;UAC7CqB,eAAe,EAAED,aAAa,CAACxD,EAAE;UACjCsC,MAAM,EAAE,IAAI,CAAChK,WAAW;UACxBqH,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBG,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7ByC,KAAK,EAAE,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAACC,IAAI,KAAK;YAClCC,UAAU,EAAED,IAAI,CAACC,UAAU;YAC3B/J,QAAQ,EAAE8J,IAAI,CAAC9J,QAAQ;YACvBE,KAAK,EAAE4J,IAAI,CAAC5J,KAAK;YACjBJ,QAAQ,EAAEgK,IAAI,CAACjK,IAAI,IAAIiK,IAAI,CAAChK,QAAQ;YACpCkK,eAAe,EAAEF,IAAI,CAACG,WAAW,IAAI;WACtC,CAAC,CAAC;UACHzC,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBF,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBC,QAAQ,EAAE,IAAI,CAACA;SAChB;QAED,IAAI,CAAC7B,mBAAmB,CAACqF,sCAAsC,CAACtB,OAAO,CAAC,CAACnB,SAAS,CAAC;UACjF6B,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAACC,qBAAqB,CAACD,QAAQ,CAAC;UACtC,CAAC;UACDnB,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACqB,kBAAkB,CAACrB,KAAK,CAAC;UAChC;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfM,OAAO,CAACN,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAAChF,YAAY,CAAC0D,GAAG,CAAC,8CAA8C,CAAC;QACrE,IAAI,CAACtC,YAAY,CAACsC,GAAG,CAAC,KAAK,CAAC;MAC9B;KACD,CAAC;EACJ;EAEQ0C,qBAAqBA,CAACD,QAAa;IACzC,IAAI,CAAC/E,YAAY,CAACsC,GAAG,CAAC,KAAK,CAAC;IAE5B,IAAIyC,QAAQ,CAACY,OAAO,IAAIZ,QAAQ,CAACa,SAAS,EAAE;MAC1C,IAAI,CAAClF,cAAc,CAAC4B,GAAG,CAAC,IAAI,CAAC;MAE7B,IAAI,CAAChC,kBAAkB,CAACoB,UAAU,CAAC,qBAAqB,EAAE;QACxDmE,OAAO,EAAEd,QAAQ,CAACc,OAAO;QACzBC,aAAa,EAAEf,QAAQ,CAACe,aAAa;QACrCxB,MAAM,EAAE,IAAI,CAAChK,WAAW,CAACsH,QAAQ,EAAE;QACnC/H,WAAW,EAAE,IAAI,CAACkG,iBAAiB,EAAE,GAAG,MAAM,GAAG;OAClD,CAAC;MAEF;MACAgG,UAAU,CAAC,MAAK;QACd,IAAI,CAACxF,SAAS,CAACyF,KAAK,CAAC;UACnBL,OAAO,EAAE,IAAI;UACbE,OAAO,EAAEd,QAAQ,CAACc,OAAO;UACzB/B,OAAO,EAAEiB,QAAQ,CAACjB;SACS,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;KACT,MAAM;MACL,IAAI,CAAClF,YAAY,CAAC0D,GAAG,CAACyC,QAAQ,CAACnB,KAAK,IAAImB,QAAQ,CAACjB,OAAO,IAAI,gBAAgB,CAAC;MAE7E,IAAI,CAACxD,kBAAkB,CAACoB,UAAU,CAAC,oBAAoB,EAAE;QACvDkC,KAAK,EAAEmB,QAAQ,CAACnB,KAAK,IAAImB,QAAQ,CAACjB,OAAO;QACzCQ,MAAM,EAAE,IAAI,CAAChK,WAAW;QACxBT,WAAW,EAAE,IAAI,CAACkG,iBAAiB,EAAE,GAAG,MAAM,GAAG;OAClD,CAAC;;EAEN;EAEQkF,kBAAkBA,CAACrB,KAAU;IACnC,IAAI,CAAC5D,YAAY,CAACsC,GAAG,CAAC,KAAK,CAAC;IAC5B4B,OAAO,CAACN,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IAEtC,MAAMhF,YAAY,GAAGgF,KAAK,CAACA,KAAK,EAAEA,KAAK,IAAIA,KAAK,CAACE,OAAO,IAAI,2BAA2B;IACvF,IAAI,CAAClF,YAAY,CAAC0D,GAAG,CAAC1D,YAAY,CAAC;IAEnC,IAAI,CAAC0B,kBAAkB,CAACoB,UAAU,CAAC,mBAAmB,EAAE;MACtDkC,KAAK,EAAEhF,YAAY;MACnB0F,MAAM,EAAE,IAAI,CAAChK,WAAW;MACxBT,WAAW,EAAE,IAAI,CAACkG,iBAAiB,EAAE,GAAG,MAAM,GAAG;KAClD,CAAC;EACJ;EAEAT,WAAWA,CAAA;IACT,IAAI,CAACiB,SAAS,CAACyF,KAAK,CAAC;MACnBL,OAAO,EAAE;KACkB,CAAC;EAChC;EAEAtL,cAAcA,CAACiK,MAAc;IAC3B,OAAO,IAAI2B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCjF,KAAK,EAAE,UAAU;MACjBkF,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC9B,MAAM,CAAC;EACnB;EAEA;EACAxH,gBAAgBA,CAAA;IACd,MAAMqI,IAAI,GAAG,IAAI,CAACpK,QAAQ,EAAE,CAACsL,WAAW,EAAE;IAC1C,OAAO,aAAalB,IAAI,EAAE;EAC5B;EAEApI,eAAeA,CAAA;IACb,QAAQ,IAAI,CAAChC,QAAQ,EAAE;MACrB,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC,KAAK,YAAY;QAAE,OAAO,aAAa;MACvC,KAAK,kBAAkB;QAAE,OAAO,aAAa;MAC7C;QAAS,OAAO,aAAa;;EAEjC;;;uBA3WWmF,iCAAiC,EAAApG,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1M,EAAA,CAAAwM,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA5M,EAAA,CAAAwM,iBAAA,CAAAK,EAAA,CAAAC,0BAAA,GAAA9M,EAAA,CAAAwM,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAhN,EAAA,CAAAwM,iBAAA,CA+DlC5N,eAAe;IAAA;EAAA;;;YA/DdwH,iCAAiC;MAAA6G,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAEjCvN,mBAAmB;;;;;;;;;;;;;;UChEhCG,EAAA,CAAAC,cAAA,yBAAoB;UAI+BD,EAAA,CAAA4D,UAAA,mBAAA0J,mEAAA;YAAA,OAASD,GAAA,CAAA7H,WAAA,EAAa;UAAA,EAAC;UAClExF,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAK9BH,EAAA,CAAAC,cAAA,aAA0H;UAC9GD,EAAA,CAAAE,MAAA,GAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACvEH,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,IAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI1EH,EAAA,CAAAkB,UAAA,KAAAqM,iDAAA,kBAUM;UAGNvN,EAAA,CAAAkB,UAAA,KAAAsM,iDAAA,mBAqKM;UACRxN,EAAA,CAAAG,YAAA,EAAM;;;UAzL+BH,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAyN,WAAA,cAAAJ,GAAA,CAAApH,iBAAA,GAAuC,qBAAAoH,GAAA,CAAApH,iBAAA;UAC9DjG,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAK,iBAAA,CAAAgN,GAAA,CAAApH,iBAAA,4BAAkD;UACtDjG,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAK,iBAAA,CAAAgN,GAAA,CAAApH,iBAAA,qCAA2D;UAI7DjG,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzG,cAAA,GAAsB;UAatB5G,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzG,cAAA,GAAuB;;;qBDoB7B/H,YAAY,EAAA6O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9O,mBAAmB,EAAA2N,EAAA,CAAAoB,aAAA,EAAApB,EAAA,CAAAqB,oBAAA,EAAArB,EAAA,CAAAsB,eAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,kBAAA,EAAAxB,EAAA,CAAAyB,kBAAA,EAAAzB,EAAA,CAAA0B,eAAA,EACnBpP,eAAe,EAAAgO,EAAA,CAAAqB,gBAAA,EACfpP,kBAAkB,EAAAqP,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,QAAA,EAAAJ,EAAA,CAAAK,SAAA,EAClBzP,cAAc,EAAA0P,EAAA,CAAAC,QAAA,EACd1P,eAAe,EAAA2P,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf5P,aAAa,EAAA6P,EAAA,CAAAC,OAAA,EACb7P,wBAAwB,EAAA8P,GAAA,CAAAC,kBAAA,EACxB9P,eAAe,EAAA+P,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfzP,uBAAuB,EACvBD,mBAAmB;MAAA2P,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}