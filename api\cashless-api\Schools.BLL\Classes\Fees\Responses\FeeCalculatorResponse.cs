namespace Schools.BLL.Classes.Fees.Responses;
using Schools.DAL.Enums;

/// <summary>
/// Migrated this class to Cashless.Common
/// </summary>
public class FeeCalculatorResponse
{
    public long FeeCalculatorId { get; set; }

    public string FeeCalculatorName { get; set; }

    public string ConfigurationName { get; set; }

    public OrderType CalculatorOrderType { get; set; }

    public bool IsActive { get; set; }

    public bool IsDefault { get; set; }
}
