{"version": 3, "file": "node_modules_braze_web-sdk_src_Push_request-push-permission_js.js", "mappings": ";;;;;;;;;;;;;;;AAA8C;AACH;AACpC,SAASE,qBAAqBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1C,IAAIJ,mEAAC,CAACK,EAAE,CAAC,CAAC,EACR,OAAOJ,gEAAE,CAACK,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,KAAK;IACnC,MAAMC,CAAC,GAAGV,mEAAC,CAACW,EAAE,CAAC,CAAC;IAChBD,CAAC,IAAIA,CAAC,CAACE,yBAAyB,CAAC,CAAC,EAAE,UAAU,IAAI,OAAOT,CAAC,IAAIA,CAAC,CAACC,CAAC,EAAEI,CAAC,EAAEC,CAAC,CAAC;EAC1E,CAAC,EAAEL,CAAC,CAAC;AACT", "sources": ["./node_modules/@braze/web-sdk/src/Push/request-push-permission.js"], "sourcesContent": ["import e from \"../managers/braze-instance.js\";\nimport na from \"./push-manager-factory.js\";\nexport function requestPushPermission(r, n) {\n  if (e.rr())\n    return na.m().subscribe((n, o, t) => {\n      const s = e.cr();\n      s && s.requestImmediateDataFlush(), \"function\" == typeof r && r(n, o, t);\n    }, n);\n}\n"], "names": ["e", "na", "requestPushPermission", "r", "n", "rr", "m", "subscribe", "o", "t", "s", "cr", "requestImmediateDataFlush"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}