﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Payments;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class BillingController : Controller
{
    private readonly IDBHelper _dbHelper;
    private readonly IOrderService _orderService;
    private readonly IBillingService _billingService;
    private readonly IBillingValidator _billingValidator;
    private readonly ITransactionHistoryService _transactionHistoryService;
    private readonly ILogger<BillingController> _logger;

    public BillingController(IDBHelper dbHelper, IOrderService orderService,
                            IBillingService billingService, IBillingValidator billingValidator,
                            ITransactionHistoryService transactionHistoryService, ILogger<BillingController> logger)
    {
        _dbHelper = dbHelper;
        _orderService = orderService;
        _billingService = billingService;
        _billingValidator = billingValidator;
        _transactionHistoryService = transactionHistoryService;
        _logger = logger;
    }

    /// <summary>
    /// This is used by the cashless-process-order logic app. 
    /// 
    /// NOTE: PaymentId is no longer needed. We no longer have subscription billing. 
    ///       It is kept as a param to avoid changing the logic app 
    /// 
    /// Needs anonymous access, but we will check if the request
    /// contains the required cashless-api-secret header.
    /// </summary>
    [AllowAnonymous]
    [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
    [Route("UpdateBillingState/{paymentId}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> UpdateBillingState([FromBody] MultipleOrders multipleOrders, int paymentId)
    {
        await _billingValidator.ValidateAccess(multipleOrders);

        // Transfer order fees to Fee Account
        await _orderService.MoveOrdersFee(multipleOrders);

        return new OkResult();
    }

    [Route("GetBillingHistory/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    public async Task<IActionResult> GetBillingHistory(int userId)
    {
        await _billingValidator.ValidateAccessToUser(userId);

        var res = await _dbHelper.ExecSprocByParams<List<BillingHistory>>("sp_Billing_History_GetBy_UserId",
                                        new Dictionary<string, string>() { { "userId", $"{userId}" } });

        return new OkObjectResult(res);
    }

    #region tblUserPayments

    [Route("GetTopUpHistory/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    [Obsolete("Contains payment transactions before Term 1, 2023")]
    public async Task<IActionResult> GetTopUpHistory(int userId)
    {
        await _billingValidator.ValidateAccessToUser(userId);

        var res = await _dbHelper.ExecSprocByParams<List<TopUp>>("sp_TopUp_History_GetBy_UserId",
                                        new Dictionary<string, string>() { { "userId", $"{userId}" } });

        return new OkObjectResult(res);
    }

    [Route("GetCreditHistory/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    [Obsolete("Contains payment transactions before Term 1, 2023")]
    public async Task<IActionResult> GetCreditHistory(int userId)
    {
        await _billingValidator.ValidateAccessToUser(userId);

        var res = await _dbHelper.ExecSprocByParams<List<TopUp>>("sp_Credit_History_GetBy_UserId",
                                        new Dictionary<string, string>() { { "userId", $"{userId}" } });

        return new OkObjectResult(res);
    }

    [Route("GetPaymentHistory/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    [Obsolete("Contains payment transactions before Term 1, 2023")]
    public async Task<IActionResult> GetPaymentHistory(int userId)
    {
        await _billingValidator.ValidateAccessToUser(userId);

        // get history
        List<PaymentHistoryDto> res = await _billingService.GetPaymentHistory(userId);

        return new OkObjectResult(res);
    }

    #endregion

    #region tblTransactions

    [Route("GetTransactionHistory")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    public async Task<IActionResult> GetTransactionHistory([FromBody] TransactionHistorySearchRequest request)
    {
        await _billingValidator.ValidateRequest(request);

        // This does not fetch the Orders associated with the Transactions fetched
        var transactions = await _transactionHistoryService.GetTransactions(request.UserId, Convert.ToInt32(request.RecordLimit));

        return new OkObjectResult(transactions);
    }

    [Route("GetTransactionOrders/{transactionId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    public async Task<IActionResult> GetTransactionOrders(long transactionId)
    {
        await _billingValidator.ValidateRequest(transactionId);

        var orders = await _transactionHistoryService.GetOrders(transactionId);

        return new OkObjectResult(orders);
    }

    #endregion
}
