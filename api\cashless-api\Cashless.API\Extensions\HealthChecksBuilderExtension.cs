using System;
using System.Collections.Generic;
using System.Net.Http;
using Cashless.API.HealthChecks;
using Cashless.APIs.HealthChecks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Cashless.APIs.Extensions;

/// <summary>
/// Extension to register custom IHealthCheck instances that
/// require parameters and a DI container. This is achieved by
/// passing a `Func` that returns the istance of the IHealthCheck
/// to add
/// </summary>
#nullable enable
public static class HealthChecksBuilderExtension
{
    /// <summary>
    /// Default name of the spriggy-core API health check
    /// </summary>
    private const string ScoreApi = "score-api";

    /// <summary>
    /// Default name of the schools-audit health check
    /// </summary>
    private const string SchoolsAudit = "schools-audit";

    /// <summary>
    /// Default name of the schools-orders health check
    /// </summary>
    private const string SchoolsOrders = "schools-orders";

    /// <summary>
    /// Default name of the spriggy-core health check
    /// </summary>
    private const string SpriggyCore = "spriggy-core";

    /// <summary>
    /// Create and configure a new Core API healthcheck
    /// </summary>
    public static IHealthChecksBuilder AddScoreApi(this IHealthChecksBuilder builder,
              ScoreApiOperation operation, string name = ScoreApi, HealthStatus? failureStatus = default,
              IEnumerable<string>? tags = default, TimeSpan? timeout = default)
    {
        return builder.AddScoreApi(_ => operation, operation, name, failureStatus, tags, timeout);
    }

    /// <summary>
    /// Create and configure a new Core API healthcheck
    /// </summary>
    public static IHealthChecksBuilder AddScoreApi(this IHealthChecksBuilder builder,
              Func<IServiceProvider, ScoreApiOperation> healthcheck, ScoreApiOperation operation, string name = ScoreApi,
              HealthStatus? failureStatus = default, IEnumerable<string>? tags = default,
              TimeSpan? timeout = default)
    {
        if (healthcheck == null)
        {
            throw new ArgumentNullException(nameof(healthcheck));
        }

        // Create a factory to create IHealthCheck instances on the fly
        return builder.Add(new HealthCheckRegistration(name,
                            sp => new ScoreApiHealthCheck(sp, operation),
                            failureStatus, tags, timeout));
    }

    /// <summary>
    /// Configure a new an Audit function app health check
    /// </summary>
    public static IHealthChecksBuilder AddSchoolsAudit(this IHealthChecksBuilder builder,
              string baseUrl, string name = SchoolsAudit, HealthStatus? failureStatus = default,
              IEnumerable<string>? tags = default, TimeSpan? timeout = default)
    {
        return builder.AddSchoolsAudit(_ => baseUrl, baseUrl, name, failureStatus, tags, timeout);
    }

    /// <summary>
    /// Configure a new an Audit function app health check
    /// </summary>
    public static IHealthChecksBuilder AddSchoolsAudit(this IHealthChecksBuilder builder,
              Func<IHttpClientFactory, string> healthcheck, string baseUrl, string name = SchoolsAudit,
              HealthStatus? failureStatus = default, IEnumerable<string>? tags = default,
              TimeSpan? timeout = default)
    {
        if (healthcheck == null)
        {
            throw new ArgumentNullException(nameof(healthcheck));
        }

        // Create a factory to create IHealthCheck instances on the fly
        return builder.Add(new HealthCheckRegistration(name,
                            sp => new PingHealthCheck(sp.GetRequiredService<IHttpClientFactory>(), sp.GetRequiredService<ILogger<PingHealthCheck>>(), baseUrl),
                            failureStatus, tags, timeout));
    }

    ///// <summary>
    ///// Configure a new an Orders function app health check
    ///// </summary>
    //public static IHealthChecksBuilder AddSchoolsOrders(this IHealthChecksBuilder builder,
    //          string baseUrl, string name = SchoolsOrders, HealthStatus? failureStatus = default,
    //          IEnumerable<string>? tags = default, TimeSpan? timeout = default)
    //{
    //    return builder.AddSchoolsOrders(_ => baseUrl, baseUrl, name, failureStatus, tags, timeout);
    //}

    ///// <summary>
    ///// Configure a new an Orders function app health check
    ///// </summary>
    //public static IHealthChecksBuilder AddSchoolsOrders(this IHealthChecksBuilder builder,
    //          Func<IHttpClientFactory, string> healthcheck, string baseUrl, string name = SchoolsOrders,
    //          HealthStatus? failureStatus = default, IEnumerable<string>? tags = default,
    //          TimeSpan? timeout = default)
    //{
    //    if (healthcheck == null)
    //    {
    //        throw new ArgumentNullException(nameof(healthcheck));
    //    }

    //    // Create a factory to create IHealthCheck instances on the fly
    //    return builder.Add(new HealthCheckRegistration(name,
    //                        sp => new PingHealthCheck(sp.GetRequiredService<IHttpClientFactory>(), sp.GetRequiredService<ILogger<PingHealthCheck>>(), baseUrl),
    //                        failureStatus, tags, timeout));
    //}

    /// <summary>
    /// Configure a new a Spriggy Core health check
    /// </summary>
    public static IHealthChecksBuilder AddSpriggyCore(this IHealthChecksBuilder builder,
              string baseUrl, string name = SchoolsOrders, HealthStatus? failureStatus = default,
              IEnumerable<string>? tags = default, TimeSpan? timeout = default)
    {
        return builder.AddSpriggyCore(_ => baseUrl, baseUrl, name, failureStatus, tags, timeout);
    }

    /// <summary>
    /// Configure a new a Spriggy Core health check
    /// </summary>
    public static IHealthChecksBuilder AddSpriggyCore(this IHealthChecksBuilder builder,
              Func<IHttpClientFactory, string> healthcheck, string baseUrl, string name = SchoolsOrders,
              HealthStatus? failureStatus = default, IEnumerable<string>? tags = default,
              TimeSpan? timeout = default)
    {
        if (healthcheck == null)
        {
            throw new ArgumentNullException(nameof(healthcheck));
        }

        // Create a factory to create IHealthCheck instances on the fly
        return builder.Add(new HealthCheckRegistration(name,
                            sp => new SpriggyCoreHealthCheck(sp.GetRequiredService<IHttpClientFactory>(), baseUrl),
                            failureStatus, tags, timeout));
    }
}
#nullable disable
