﻿using System;
using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders.Events;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Helpers;
using Schools.BLL.ThirdParty.General;

namespace Schools.Orders.Helpers;

public static class ConsumerHelper
{
    public static OrdersPaymentLinkRequest GetOrdersPaymentLinkRequest(IEnumerable<OrderPublishInfo> orders, CashlessMakePaymentResponse paymentResult, long userId)
    {
        var linkOrdersRequest = new OrdersPaymentLinkRequest()
        {
            PaymentId = paymentResult.PaymentId,
            TransactionId = paymentResult.TransactionIds.FirstOrDefault(),
            UserId = userId
        };

        // get correlationId for each order
        List<OrderCorrelation> orderIds = new();

        foreach (var ord in orders)
        {
            var correlation = new OrderCorrelation
            {
                OrderId = ord.OrderId,
                CorrelationId = ord.CorrelationId
            };

            orderIds.Add(correlation);
        }

        linkOrdersRequest.OrderIds = orderIds;

        return linkOrdersRequest;
    }
}