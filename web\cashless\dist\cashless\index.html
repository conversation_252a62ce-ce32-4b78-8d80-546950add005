<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' *;script-src 'self' 'unsafe-inline' 'unsafe-eval' *;img-src 'self' 'unsafe-inline' 'unsafe-eval' *;font-src 'self' * data:"/>
    <title>Spriggy Schools</title>
    <base href="/"/>

    <!-- <meta name="viewport" http-equiv="X-UA-Compatible" content="IE=edge, chrome=1" /> -->

    <!-- Trying to disable cache -->
    <meta http-equiv="cache-control" content="no-cache, must-revalidate, post-check=0, pre-check=0"/>
    <meta http-equiv="cache-control" content="max-age=0"/>
    <meta http-equiv="expires" content="0"/>
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT"/>
    <meta http-equiv="pragma" content="no-cache"/>

    <meta name="viewport" content="width=device-width, initial-scale=1"/>

    <!-- Preload critical fonts for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/>

    <!-- Preload local Bariol fonts -->
    <link rel="preload" href="assets/fonts/bariol_regular-webfont.woff" as="font" type="font/woff" crossorigin=""/>
    <link rel="preload" href="assets/fonts/bariol_bold-webfont.woff" as="font" type="font/woff" crossorigin=""/>

    <!-- Material Icons with font-display for better loading -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons&amp;display=swap" rel="stylesheet"/>

    <!-- Preload Material Icons font file -->
    <link rel="preload" href="https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2" as="font" type="font/woff2" crossorigin=""/>

    <link rel="icon" type="image/x-icon" href="favicon.ico"/>
    <link rel="manifest" href="manifest.json"/>

    <!-- iOS Specific PWA features-->
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="#ffffff"/>
    <meta name="apple-mobile-web-app-title" content="Cashless UAT"/>
    <link rel="apple-touch-icon" href="assets/icons/icon-512x512.png"/>

    <!-- iOS splash screens-->
    <!-- iPhone Xs Max (1242px x 2688px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1242x2688.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2688x1242.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPhone Xr (828px x 1792px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_828x1792.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_1792x828.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPhone X, Xs (1125px x 2436px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1125x2436.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2436x1125.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (1242px x 2208px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1242x2208.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2208x1242.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPhone 8, 7, 6s, 6 (750px x 1334px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_750x1334.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_1334x750.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPad Pro 12.9" (2048px x 2732px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_2048x2732.png" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2732x2048.png" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPad Pro 11” (1668px x 2388px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1668x2388.png" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2388x1668.png" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPad Pro 10.5" (1668px x 2224px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1668x2224.png" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2224x1668.png" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>
    <!-- iPad Mini, Air (1536px x 2048px) -->
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_portrait_1536x2048.png" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"/>
    <link rel="apple-touch-startup-image" href="assets/splash/ios_splash_landscape_2048x1536.png" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)"/>

    <meta name="theme-color" content="#1976d2"/>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-Y49VD12VR4"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-Y49VD12VR4');
    </script>

    <!-- Hotjar Tracking Code for http://cashless.school -->
    <!-- <script>
    (function (h, o, t, j, a, r) {
      h.hj = h.hj || function () { (h.hj.q = h.hj.q || []).push(arguments) };
      h._hjSettings = { hjid: 1330918, hjsv: 6 };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script'); r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  </script> -->
  <link rel="stylesheet" href="styles.css"></head>

  <body style="background-color: #f2f2f2; height: 100vh">
    <app-root>
      <div class="splashSpinner">
        <div class="la-ball-clip-rotate la-3x">
          <div></div>
        </div>
      </div>
    </app-root>

    <!-- Temp disabled until we can move it properly. -->
    <!-- <div class="feedier-widget" data-type="engager" data-layout="horizontal" data-shake="true" data-position="left"
    data-in-site="true" data-carrier-id="9052" data-key="4Cgap4sXU3b5p2d0vVdoyuJg2cln1xis" data-widget-title="feedback">
  </div> -->

    <!-- Include this line only one time, also if you have multiple widgets on the current page -->
    <!-- <script src="https://feedier.com/js/widgets/widgets.min.js" type="text/javascript" async></script> -->

    <!-- Paypal -->
    <!-- <script src="https://www.paypalobjects.com/api/checkout.js" data-version-4></script>
<script>
  var paypal = (function() { 
      return paypal;
  })()
</script> -->
  <script src="runtime.js" type="module"></script><script src="polyfills.js" type="module"></script><script src="vendor.js" type="module"></script><script src="main.js" type="module"></script></body>
</html>
