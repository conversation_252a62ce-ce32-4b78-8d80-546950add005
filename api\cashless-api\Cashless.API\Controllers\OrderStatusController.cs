﻿using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Schools.BLL.Classes.Orders;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrderStatusController : ControllerBase
{
    private readonly IOrderStatusService _orderStatusService;
    private readonly ILogger<OrderStatusController> _logger;

    public OrderStatusController(IOrderStatusService orderStatusService, ILogger<OrderStatusController> logger)
    {
        _orderStatusService = orderStatusService;
        _logger = logger;
    }

    [Route("{orderId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> Get(long orderId)
    {
        await this._orderStatusService.GetByOrderId(orderId);

        return new OkResult();
    }

    [Route("Error/{orderStatusId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public IActionResult GetErrorsByOrderStatus(long orderStatusId)
    {
        // await this._orderStatusService.GetByOrderId(orderId);

        return new OkResult();
    }

    [Route("DashboardErrors")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> GetDashboardErrors()
    {
        var res = await this._orderStatusService.GetOrderswithError();

        return new OkObjectResult(res);
    }

    /// <summary>
    /// Called from the queues to add errors
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
    [Route("Error")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> CreateError([FromBody] CreateOrderErrorDto request)
    {
        await this._orderStatusService.CreateOrderError(request);

        return new OkResult();
    }

}

