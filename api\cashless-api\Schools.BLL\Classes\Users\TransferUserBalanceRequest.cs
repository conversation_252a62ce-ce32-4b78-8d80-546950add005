﻿using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Users
{
    public class TransferUserBalanceRequest
    {
        [Required]
        public int FromUserId { get; set; }

        [Required]
        public int ToUserId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public string Message { get; set; }
    }

    public class TransferUserBalanceResponse
    {
        [JsonProperty(PropertyName = "FromUserUpdatedBalance")]
        public decimal? FromUserUpdatedBalance { get; set; }

        [JsonProperty(PropertyName = "ToUserUpdatedBalance")]
        public decimal? ToUserUpdatedBalance { get; set; }

        [JsonProperty(PropertyName = "ErrorMessage")]
        public string ErrorMessage { get; set; }
    }
}
