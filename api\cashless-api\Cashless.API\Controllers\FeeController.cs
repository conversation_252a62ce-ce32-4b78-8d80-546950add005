﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes.Fees.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.DAL.Enums;
using Schools.BLL.Validators;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Controllers;

[Authorize]
[CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
[ApiController]
[Route("api/[controller]")]
[Route("[controller]")]
public class FeeController : ControllerBase
{
    private readonly ILogger<FeeController> _logger;
    private readonly IFeeValidator _feeValidator;
    private readonly IFeeService _feeService;

    public FeeController(ILogger<FeeController> logger, IHttpContextAccessor httpContext, IFeeValidator feeValidator, IFeeService feeService)
    {
        _logger = logger;
        _feeValidator = feeValidator;
        _feeService = feeService;
    }

    [Route("Get")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> GetFee([FromBody] FeesRequest request)
    {
        _feeValidator.ValidateRequest(request);
        var response = await _feeService.GetFee(request);

        return new OkObjectResult(response);
    }

    /**TODO: make obsolete when not being used in mobile app**/
    [Route("GetMany")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> GetFees([FromBody] IEnumerable<FeesRequestNoMerchantId> requests)
    {
        var request = _feeService.ConvertNoMerchantIdRequestsToFeesRequest(requests);

        _feeValidator.ValidateRequest(request);
        var response = await _feeService.GetFees(request);

        return new OkObjectResult(response);
    }

    [Route("GetManyFees")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> GetManyFees([FromBody] IEnumerable<FeesRequest> requests)
    {
        _feeValidator.ValidateRequest(requests);
        var response = await _feeService.GetFees(requests);

        return new OkObjectResult(response);
    }

    [Route("Add")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> AddFee([FromBody] AddFeeUsedRequest request)
    {
        _feeValidator.ValidateRequest(request);
        var response = await _feeService.AddFeeUsed(request);

        return new OkObjectResult(response);
    }

    [Route("AddMany")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> AddFees([FromBody] IEnumerable<AddFeeUsedRequest> requests)
    {
        _feeValidator.ValidateRequest(requests);
        await _feeService.AddFeesUsed(requests);

        return new OkResult();
    }

    [Route("UpdateStatusByOrder")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [HttpPost]
    public async Task<IActionResult> UpdateFeeStatusByOrder([FromBody] UpdateFeeStatusRequest request)
    {
        _feeValidator.ValidateRequest(request);
        var response = await _feeService.UpdateFeeStatus(request);

        return new OkObjectResult(response);
    }
}

