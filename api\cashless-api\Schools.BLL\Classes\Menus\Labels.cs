﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.BLL.Helpers;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes
{
    public class LabelFormat
    {
        [JsonProperty("LabelName")]
        public string LabelName { get; set; }

        [JsonProperty("LabelPrintChoice")]
        public string LabelPrintChoice { get; set; }

        [JsonProperty("Labels")]
        public List<Label> Labels { get; set; }
    }

    public class Label
    {
        [JsonProperty("OrderId")]
        public int OrderId { get; set; }

        [JsonProperty("ItemId")]
        public int? ItemId { get; set; }

        [JsonProperty("TitleLeft")]
        public string TitleLeft { get; set; }

        [JsonProperty("TitleRight")]
        public string TitleRight { get; set; }

        [JsonProperty("SubTitle")]
        public string SubTitle { get; set; }

        [JsonProperty("Items")]
        public List<string> Items { get; set; }

        [JsonProperty("LabelNumber")]
        public string LabelNumber { get; set; }

        // add options to item
        public string GetItemOptions(MenuItem item)
        {
            string res = string.Empty;

            if (item.Options != null)
            {
                item.Options.ForEach(option =>
                {
                    var optionList = option.SelectedOptionList;

                    if (optionList != null && optionList.Count > 0)
                    {
                        if (!res.Contains("("))
                        {
                            res += "(";
                        }

                        optionList.ForEach(optList =>
                        {
                            res += optList + ',';
                        });
                    }
                });

                if (res.Contains("("))
                {
                    res = res.Substring(0, res.Length - 1) + ")";
                }

                // res += ')';
                // used to be !String.IsNullOrEmpty(x.SelectedOption)
                // var options = new List<string>();
                var options = item.Options.FindAll(x => !String.IsNullOrEmpty(x.SelectedOption));

                // item.Options.FindAll(x => x.SelectedOptionList);
                if (options != null && options.Count > 0)
                {
                    res = "(";

                    options.ForEach(opt =>
                    {
                        if (res != "(")
                        {
                            res += ", ";
                        }

                        if (!String.IsNullOrEmpty(opt.SelectedOption))
                        {
                            res += opt.SelectedOption;
                        }
                        else if (opt.SelectedOptionList != null && opt.SelectedOptionList.Count > 0)
                        {
                            foreach (var sopt in opt.SelectedOptionList)
                            {
                                res += sopt;
                            }
                        }
                    });

                    res += ')';
                }
            }

            return res;
        }

        public class LabelType
        {
            [JsonProperty("LabelTypeId")]
            public int LabelTypeId { get; set; }

            [JsonProperty("Name")]
            public string Name { get; set; }

            [JsonProperty("Size")]
            public string Size { get; set; }

            [JsonProperty("Printer")]
            public string Printer { get; set; }

            [JsonProperty("UsePrintingApp")]
            public bool? UsePrintingApp { get; set; }
        }

        public class LabelPosition
        {
            [JsonProperty("Name")]
            public string Name { get; set; }

            [JsonProperty("Coordinates")]
            public string Coordinates { get; set; }

            [JsonProperty("CoordinatesList")]
            public List<int> CoordinatesList { get; set; }

            [JsonProperty("MaxLength")]
            public Int16? MaxLength { get; set; }

            [JsonProperty("MaxItems")]
            public Int16? MaxItems { get; set; }

            [JsonProperty("Value")]
            public string Value { get; set; }

            [JsonProperty("DataList")]
            public string DataList { get; set; }
        }

        public class LabelFormatV2
        {
            [JsonProperty("ThermalSetup")]
            public List<string> ThermalSetup { get; set; }

            [JsonProperty("ThermalItemsStartPoint")]
            public int? ThermalItemsStartPoint { get; set; }

            [JsonProperty("Labels")]
            public List<LabelV2> Labels { get; set; }

            public LabelFormatV2(string labelName)
            {
                this.Labels = new List<LabelV2>();
                this.ThermalItemsStartPoint = 90;

                // thermal setup
                this.ThermalSetup = new List<string>
                {
                    "50",
                    labelName == "50x100" ? "75" : "100",
                    "4",
                    "8",
                    "0",
                    "5",
                    "0"
                };
            }
        }

        public class LabelV2
        {
            [JsonProperty("OrderId")]
            public int OrderId { get; set; }

            [JsonProperty("ItemId")]
            public int? ItemId { get; set; }

            [JsonProperty("Positions")]
            public List<LabelPosition> Positions { get; set; }

            [JsonProperty("Items")]
            public List<string> Items { get; set; }

            [JsonProperty("LabelNumber")]
            public string LabelNumber { get; set; }

            public LabelV2(Order o, int? menuItemId, List<LabelPosition> schoolsPositions, HashSet<string> customMenuNameSchoolIds)
            {
                this.OrderId = Convert.ToInt32(o.OrderId);
                this.ItemId = menuItemId;
                this.Items = new List<string>();

                // setup positions
                this.Positions = new List<LabelPosition>();

                if (schoolsPositions != null && schoolsPositions.Count > 0)
                {
                    schoolsPositions.ForEach(p =>
                    {
                        var tempPosition = new LabelPosition();
                        tempPosition.DataList = p.DataList;
                        tempPosition.Coordinates = p.Coordinates;
                        tempPosition.CoordinatesList = p.CoordinatesList;
                        tempPosition.MaxLength = p.MaxLength;
                        tempPosition.Name = p.Name;

                        // get data for the position
                        string[] data = tempPosition.DataList.Split(",");

                        // setup value for the position
                        if (data != null && data.Length > 0 && tempPosition.Name != "Items" && tempPosition.Name != "LabelNumber")
                        {
                            if (data.Length == 1)
                            {
                                tempPosition.Value = this.GetPropertyByName(o, data[0]);
                            }
                            else
                            {
                                // for each data to display
                                for (int i = 0; i < data.Length; i++)
                                {
                                    tempPosition.Value += this.GetPropertyByName(o, data[i]);

                                    // if not end of the string add - between the values
                                    if (i != data.Length - 1)
                                    {
                                        tempPosition.Value += " - ";
                                    }
                                }
                            }

                            // cut string if too long (allergies are handled on the client side)
                            // (Web app also don't always need to check the length, in this case MaxLength will be null)
                            if (tempPosition.Value.Length > tempPosition.MaxLength && tempPosition.Name != "Allergies")
                            {
                                tempPosition.Value = tempPosition.Value.Substring(0, Convert.ToInt32(tempPosition.MaxLength));
                            }

                            if (tempPosition.Name == "Allergies" && o.MenuType == MenuTypeEnum.Uniform)
                            {
                                tempPosition.Value = string.Empty;
                            }

                            if (p.DataList == "MenuType" && !CustomMenuNameHelper.BlockCustomMenuName(customMenuNameSchoolIds, o.SchoolId))
                            {
                                tempPosition.Value = GetCustomMenuName(tempPosition.Value);
                            }
                        }

                        this.Positions.Add(tempPosition);
                    });
                }
            }

            // get the value by property name
            private string GetPropertyByName(Object o, string propertyName)
            {
                object obj = o.GetType().GetProperty(propertyName).GetValue(o, null);
                string value = string.Empty;

                if (obj != null)
                {
                    value = obj.ToString();
                }

                if (propertyName == "OrderDate")
                {
                    value = Convert.ToDateTime(value).ToString("dd-MM-yy");
                }

                return value;
            }

            private static string GetCustomMenuName(string menuType)
            {
                if (menuType == MenuTypeEnum.Recess)
                {
                    return MerchantCustomMenuName.FirstBreak;
                }

                if (menuType == MenuTypeEnum.Lunch)
                {
                    return MerchantCustomMenuName.SecondBreak;
                }

                return menuType;
            }
        }
    }
}
