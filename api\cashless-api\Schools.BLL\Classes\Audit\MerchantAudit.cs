﻿using System;
namespace Schools.BLL.Classes.Audit;

public enum TypeMerchantAuditLogEnum
{
    Menu,
    Item,
    Stock,
    Option,
    Settings,
    Accounts,
    Details,
    Schools
}

public enum MenuSubTypeAuditlogEnum
{
    Create,
    Edit,
    Delete
}

public enum ItemSubTypeAuditlogEnum
{
    Create,
    Edit,
    Delete
}

public enum OptionSubTypeAuditlogEnum
{
    Create,
    Edit,
    Delete
}

public enum StockSubTypeAuditlogEnum
{
    Create,
    Edit,
    Delete
}

public enum SettingsSubTypeAuditlogEnum
{
    Edit
}

public enum MerchantAccountsSubTypeAuditlogEnum
{
    Add,
    Edit,
    Delete
}

public enum MerchantDetailsSubTypeAuditlogEnum
{
    Create,
    Edit,
    Delete,
    EditFee,
    EditOwner
}

public enum SchoolsSubTypeAuditlogEnum
{
    Link,
    UnLink,
    Edit
}

public abstract class MerchantAuditLog : BaseAuditLog
{
    public long MerchantId { get; set; }
    public long? SchoolId { get; set; }
    public long? TargetUserId { get; set; }

    public MerchantAuditLog(TypeMerchantAuditLogEnum type, string subType) : base()
    {
        this.LogType = type.ToString();
        this.LogSubType = subType;
    }
}

public class MerchantMenuAuditLog : MerchantAuditLog
{
    public MerchantMenuAuditLog(MenuSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Menu, subType.ToString())
    {
    }
}

public class MerchantItemAuditLog : MerchantAuditLog
{
    public MerchantItemAuditLog(ItemSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Item, subType.ToString())
    {
    }
}

public class MerchantOptionAuditLog : MerchantAuditLog
{
    public MerchantOptionAuditLog(OptionSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Option, subType.ToString())
    {
    }
}

public class MerchantStockAuditLog : MerchantAuditLog
{
    public MerchantStockAuditLog(StockSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Stock, subType.ToString())
    {
    }
}

public class MerchantSettingsAuditLog : MerchantAuditLog
{
    public MerchantSettingsAuditLog(SettingsSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Settings, subType.ToString())
    {
    }
}

public class MerchantAccountsAuditLog : MerchantAuditLog
{
    public MerchantAccountsAuditLog(MerchantAccountsSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Accounts, subType.ToString())
    {
    }
}

public class MerchantDetailsAuditLog : MerchantAuditLog
{
    public MerchantDetailsAuditLog(MerchantDetailsSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Details, subType.ToString())
    {
    }
}

public class MerchantSchoolsAuditLog : MerchantAuditLog
{
    public MerchantSchoolsAuditLog(SchoolsSubTypeAuditlogEnum subType) : base(TypeMerchantAuditLogEnum.Schools, subType.ToString())
    {
    }
}
