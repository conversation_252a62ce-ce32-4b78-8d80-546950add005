{"version": 3, "file": "default-node_modules_braze_web-sdk_src_Push_push-manager-factory_js.js", "mappings": ";;;;;;;;;;;;;;;AAAgE;AAC7B;AACnC,MAAMI,EAAE,GAAG;EACTC,CAAC,EAAE,CAAC,CAAC;EACLC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAEA,CAAA,MACDH,EAAE,CAACI,CAAC,CAAC,CAAC,EACNJ,EAAE,CAACE,CAAC,KACDF,EAAE,CAACE,CAAC,GAAG,IAAIH,wDAAE,CACZH,mEAAC,CAACS,EAAE,CAAC,CAAC,EACNT,mEAAC,CAACU,EAAE,CAAC,CAAC,EACNV,mEAAC,CAACW,EAAE,CAAC,CAAC,EACNX,mEAAC,CAACY,EAAE,CAAC,CAAC,EACNZ,mEAAC,CAACa,EAAE,CAACX,gEAAC,CAACY,EAAE,CAAC,EACVd,mEAAC,CAACa,EAAE,CAACX,gEAAC,CAACa,EAAE,CAAC,EACVf,mEAAC,CAACgB,EAAE,CAAC,CAAC,EACNhB,mEAAC,CAACa,EAAE,CAACX,gEAAC,CAACe,EAAE,CAAC,EACVjB,mEAAC,CAACa,EAAE,CAACX,gEAAC,CAACgB,EAAE,CAAC,EACVlB,mEAAC,CAACmB,CAAC,CAAC,CACN,CAAC,CAAC,EACJf,EAAE,CAACE,CAAC,CACL;EACDE,CAAC,EAAEA,CAAA,KAAM;IACPJ,EAAE,CAACC,CAAC,KAAKL,mEAAC,CAACoB,CAAC,CAAChB,EAAE,CAAC,EAAGA,EAAE,CAACC,CAAC,GAAG,CAAC,CAAE,CAAC;EAChC,CAAC;EACDgB,OAAO,EAAEA,CAAA,KAAM;IACZjB,EAAE,CAACE,CAAC,GAAG,IAAI,EAAIF,EAAE,CAACC,CAAC,GAAG,CAAC,CAAE;EAC5B;AACF,CAAC;AACD,iEAAeD,EAAE;;;;;;;;;;;;;;;;;;;;;;AC7BmD;AAC3B;AACY;AACc;AAC3B;AACmB;AACpB;AACwB;AAChD,MAAMD,EAAE,CAAC;EACtBgC,WAAWA,CAAC7B,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAEoC,CAAC,EAAET,CAAC,EAAEU,CAAC,EAAE7B,CAAC,EAAE8B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACvC,IAAI,CAACC,EAAE,GAAGnC,CAAC,EACT,IAAI,CAACoC,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,EAAE,GAAG3C,CAAC,EACX,IAAI,CAAC4C,EAAE,GAAGjB,CAAC,EACX,IAAI,CAACkB,EAAE,GAAGR,CAAC,EACX,IAAI,CAACS,EAAE,GAAGtC,CAAC,EACX,IAAI,CAACuC,EAAE,GAAGT,CAAC,EACX,IAAI,CAACU,EAAE,GAAGT,CAAC,EACX,IAAI,CAACD,CAAC,GAAGE,CAAC,EACV,IAAI,CAACC,EAAE,GAAGnC,CAAC,EACX,IAAI,CAACoC,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,EAAE,GAAG3C,CAAC,EACX,IAAI,CAACiD,EAAE,GAAGb,CAAC,GAAG,UAAU,GAAG/B,CAAC,EAC5B,IAAI,CAACuC,EAAE,GAAGjB,CAAC,IAAI,oBAAoB,EACnC,IAAI,CAACkB,EAAE,GAAGR,CAAC,EACX,IAAI,CAACS,EAAE,GAAGtC,CAAC,EACX,IAAI,CAACuC,EAAE,GAAGT,CAAC,IAAI,CAAC,CAAC,EACjB,IAAI,CAACU,EAAE,GAAGT,CAAC,IAAI,CAAC,CAAC,EACjB,IAAI,CAACD,CAAC,GAAGE,CAAC,EACV,IAAI,CAACU,EAAE,GAAGlB,4DAAE,CAACmB,EAAE,CAAC,CAAC,EACjB,IAAI,CAACC,EAAE,GAAGpB,4DAAE,CAACqB,EAAE,CAAC,CAAE;EACvB;EACAC,EAAEA,CAAA,EAAG;IACH,OAAO,IAAI,CAACN,EAAE;EAChB;EACAO,EAAEA,CAACjD,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAEoC,CAAC,EAAEC,CAAC,EAAE;IAChB/B,CAAC,CAACkD,WAAW,CAAC,CAAC,CACZC,IAAI,CAAEnD,CAAC,IAAK;MACXA,CAAC,GACG,IAAI,CAACoD,EAAE,CAACrD,CAAC,EAAEL,CAAC,EAAEoC,CAAC,EAAEC,CAAC,CAAC,IAClBV,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,yCAAyC,CAAC,EACrD,UAAU,IAAI,OAAOvB,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CACDwB,KAAK,CAAEvD,CAAC,IAAK;MACZqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,6BAA6B,GAAGtD,CAAC,CAAC,EAC1C,UAAU,IAAI,OAAO+B,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACAyB,EAAEA,CAACxD,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAE;IACV,MAAMoC,CAAC,GAAG,CAAE9B,CAAC,IAAK;MAChB,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOA,CAAC;MAClC,IAAI,CAAC,KAAKA,CAAC,CAACyD,QAAQ,CAACC,OAAO,CAAC,yCAAyC,CAAC,EACrE,OAAO1D,CAAC,CAACyD,QAAQ;MACnB,IAAI1D,CAAC,GAAGC,CAAC,CAACyD,QAAQ;MAClB,MAAM/D,CAAC,GAAGM,CAAC;MACX,OACEN,CAAC,CAACiE,EAAE,IACF,CAAC,CAAC,KAAK3D,CAAC,CAACyD,QAAQ,CAACC,OAAO,CAAChE,CAAC,CAACiE,EAAE,CAAC,KAC9B5D,CAAC,GAAGC,CAAC,CAACyD,QAAQ,GAAG,GAAG,GAAG/D,CAAC,CAACiE,EAAE,CAAC,EAC/B5D,CAAC;IAEL,CAAC,EAAEC,CAAC,CAAC;IACL,IAAIqB,CAAC,GAAG,IAAI;MACVU,CAAC,GAAG,IAAI;IACV,MAAM7B,CAAC,GAAGF,CAAC;IACX,IAAI,IAAI,IAAIE,CAAC,CAAC0D,MAAM,EAClB,IAAI;MACF,MAAM5D,CAAC,GAAG6D,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAAC7D,CAAC,CAAC0D,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD7D,CAAC,GAAG8D,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAAC7D,CAAC,CAAC0D,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;MACjDvC,CAAC,GAAG2C,IAAI,CAACC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEnE,CAAC,CAAC,CAAC,EAC1C+B,CAAC,GAAGiC,IAAI,CAACC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEpE,CAAC,CAAC,CAAE;IAClD,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,IAAI,mBAAmB,KAAK4B,qEAAE,CAAC5B,CAAC,CAAC,EAAE,MAAMA,CAAC;IAC5C;IACF,MAAMgC,CAAC,GAAG,CAAEhC,CAAC,IAAK;MAChB,IAAID,CAAC;MACL,OAAOC,CAAC,CAACoE,OAAO,KACbrE,CAAC,GAAGC,CAAC,CAACoE,OAAO,CAACC,oBAAoB,CAAC,IACpCtE,CAAC,CAACuE,UAAU,IACZvE,CAAC,CAACuE,UAAU,GAAG,CAAC,GACdN,IAAI,CAACC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEN,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CACjEwE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GACtB,IAAI;IACV,CAAC,EAAErE,CAAC,CAAC;IACL,IAAI,CAACiC,EAAE,IAAI,IAAI,CAACA,EAAE,CAACqC,EAAE,CAAC1C,CAAC,EAAE/B,CAAC,EAAEsB,CAAC,EAAEU,CAAC,EAAEC,CAAC,CAAC,EAClCF,CAAC,IAAI,UAAU,IAAI,OAAOpC,CAAC,IAAIA,CAAC,CAACoC,CAAC,EAAET,CAAC,EAAEU,CAAC,CAAC;EAC7C;EACA0C,EAAEA,CAAA,EAAG;IACH,IAAI,CAACtC,EAAE,IAAI,IAAI,CAACA,EAAE,CAACuC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3B;EACAC,EAAEA,CAAC3E,CAAC,EAAED,CAAC,EAAE;IACP,IAAI,CAACoC,EAAE,IAAI,IAAI,CAACA,EAAE,CAACuC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAErD,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC5E,CAAC,CAAC,EAAE,UAAU,IAAI,OAAOD,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE;EACA8E,EAAEA,CAAC7E,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAEoC,CAAC,EAAE;IACb,IAAIC,CAAC;IACL,IAAI,SAAS,KAAKhC,CAAC,CAAC+E,UAAU,EAC5B,IAAI;MACFC,MAAM,CAACC,MAAM,CAACC,gBAAgB,CAACC,iBAAiB,CAC9C,IAAI,CAACvC,EAAE,EACP3C,CAAC,EACD;QACEmF,OAAO,EAAE,IAAI,CAAC/C,EAAE;QAChBgD,SAAS,EACP,CAAC,IAAI,MAAMrD,CAAC,GAAG,IAAI,CAACM,EAAE,CAAC,IAAI,KAAK,CAAC,KAAKN,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACsD,EAAE,CAAC,CAAC,CAACC,EAAE,KAC5D;MACJ,CAAC,EACAvF,CAAC,IAAK;QACL,SAAS,KAAKA,CAAC,CAAC+E,UAAU,IACxB,IAAI,CAAC3C,EAAE,IACP,IAAI,CAACA,EAAE,CAACoD,mCAAmC,CACzChE,sDAAI,CAACiE,6BAA6B,CAACC,QACrC,CAAC,EACD,IAAI,CAACZ,EAAE,CAAC7E,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAEoC,CAAC,CAAC;MACvB,CACF,CAAC;IACH,CAAC,CAAC,OAAO9B,CAAC,EAAE;MACV,IAAI,CAAC2E,EAAE,CAAC,yCAAyC,GAAG3E,CAAC,EAAE8B,CAAC,CAAC;IAC3D,CAAC,MAED,QAAQ,KAAK/B,CAAC,CAAC+E,UAAU,GACrB,IAAI,CAACH,EAAE,CACL,6GAA6G,EAC7G7C,CACF,CAAC,GACD,SAAS,KAAK/B,CAAC,CAAC+E,UAAU,KACzBzD,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,yCAAyC,CAAC,EACpD,IAAI,CAACpB,EAAE,CAACzD,CAAC,CAAC2F,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,EAAEjG,CAAC,CAAC,CAAC;EAC9C;EACAwF,iBAAiBA,CAAClF,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAE;IACzB,MAAMoC,CAAC,GAAIA,CAAC,IAAK;MACf,QAAQA,CAAC;QACP,KAAK,SAAS;UACZ,OAAO,MAAM,UAAU,IAAI,OAAO9B,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;QAC7C,KAAK,SAAS;UACZ,OAAO,MAAM,UAAU,IAAI,OAAOD,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;QAC7C,KAAK,QAAQ;UACX,OAAO,MAAM,UAAU,IAAI,OAAOL,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;QAC7C;UACE2B,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,wCAAwC,GAAGxB,CAAC,CAAC;MAC3D;IACF,CAAC;IACD,IAAIC,CAAC,GAAG,CAAC,CAAC;IACV,MAAM7B,CAAC,GAAG6E,MAAM,CAACa,YAAY,CAACV,iBAAiB,CAAElF,CAAC,IAAK;MACrD+B,CAAC,IAAID,CAAC,CAAC9B,CAAC,CAAC;IACX,CAAC,CAAC;IACFE,CAAC,GACGA,CAAC,CAACiD,IAAI,CAAEnD,CAAC,IAAK;MACZ8B,CAAC,CAAC9B,CAAC,CAAC;IACN,CAAC,CAAC,GACD+B,CAAC,GAAG,CAAC,CAAE;EACd;EACAqB,EAAEA,CAACpD,CAAC,EAAED,CAAC,EAAEL,CAAC,EAAEoC,CAAC,EAAE;IACb,MAAMC,CAAC,GAAG;MAAE8D,eAAe,EAAE,CAAC;IAAE,CAAC;IACjC,IAAI,IAAI9F,CAAC,KAAKgC,CAAC,CAACsC,oBAAoB,GAAGtE,CAAC,CAAC,EACvCC,CAAC,CAAC8F,WAAW,CACVC,SAAS,CAAChE,CAAC,CAAC,CACZoB,IAAI,CAAEnD,CAAC,IAAK;MACXqB,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,yCAAyC,CAAC,EACjD,IAAI,CAACpB,EAAE,CAACxD,CAAC,EAAE,IAAI2F,IAAI,CAAC,CAAC,EAAEjG,CAAC,CAAC;IAC7B,CAAC,CAAC,CACD6D,KAAK,CAAEvD,CAAC,IAAK;MACZ0B,4DAAE,CAACsE,aAAa,CAAC,CAAC,IACb3E,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,+CAA+C,CAAC,EAC1D,UAAU,IAAI,OAAO9C,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,KAC9BT,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,4BAA4B,GAAGtD,CAAC,CAAC,EAC5C,UAAU,IAAI,OAAO8B,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;EACR;EACAmE,EAAEA,CAAA,EAAG;IACH,OAAO,IAAI,CAACxD,EAAE,GACVyD,SAAS,CAACC,aAAa,CAACC,eAAe,CAAC,IAAI,CAAC9D,EAAE,CAAC,GAChD4D,SAAS,CAACC,aAAa,CAACE,QAAQ,CAAC,IAAI,CAAC/D,EAAE,CAAC,CAACa,IAAI,CAAC,MAC7C+C,SAAS,CAACC,aAAa,CAACG,KAAK,CAACnD,IAAI,CAC/BnD,CAAC,KACAA,CAAC,IACC,UAAU,IAAI,OAAOA,CAAC,CAACuG,MAAM,IAC7BvG,CAAC,CAACuG,MAAM,CAAC,CAAC,CAAChD,KAAK,CAAEvD,CAAC,IAAK;MACtBqB,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,+BAA+B,GAAG5E,CAAC,CAAC;IAC/C,CAAC,CAAC,EACJA,CAAC,CAEL,CACF,CAAC;EACP;EACAwG,EAAEA,CAACxG,CAAC,EAAE;IACJ,IAAI,CAACyC,EAAE,KACJzC,CAAC,CAACyG,UAAU,CAAC,CAAC,EAAEpF,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,2CAA2C,CAAC,CAAC;EAC3E;EACAmB,SAASA,CAAChG,CAAC,EAAEL,CAAC,EAAE;IACd,IAAI,CAACgC,4DAAE,CAACgF,eAAe,CAAC,CAAC,EACvB,OAAOrF,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC/E,EAAE,CAAC8G,EAAE,CAAC,EAAE,MAAM,UAAU,IAAI,OAAOjH,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,IAAI,CAACkD,EAAE,EAAE;MACX,IAAI,CAAC,IAAI,CAACH,EAAE,IAAI,IAAI,IAAIsC,MAAM,CAAC6B,QAAQ,EAAE;QACvC,IAAI5G,CAAC,GAAG,IAAI,CAACsC,EAAE;QACf,CAAC,CAAC,KAAKtC,CAAC,CAAC0D,OAAO,CAACqB,MAAM,CAAC6B,QAAQ,CAACC,IAAI,CAAC,KACnC7G,CAAC,GAAG+E,MAAM,CAAC6B,QAAQ,CAACC,IAAI,GAAG7G,CAAC,CAAC,EAC9B,CAAC,CAAC,KAAKA,CAAC,CAAC0D,OAAO,CAACqB,MAAM,CAAC6B,QAAQ,CAACE,QAAQ,CAAC,KACvC9G,CAAC,GAAG+E,MAAM,CAAC6B,QAAQ,CAACE,QAAQ,GAAG,IAAI,GAAG9G,CAAC,CAAC;QAC7C,MAAMD,CAAC,GAAGC,CAAC,CAAC+G,MAAM,CAAC,CAAC,EAAE/G,CAAC,CAACgH,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAKvF,8DAAC,CAACwF,EAAE,CAAC,CAAC,CAACvD,OAAO,CAAC3D,CAAC,CAAC,EACzB,OACEsB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CACP,wGAAwG,GACtGyB,MAAM,CAAC6B,QAAQ,CAACM,QAAQ,GACxB,4BAA4B,GAC5BlH,CAAC,GACD,GACJ,CAAC,EACD,MAAM,UAAU,IAAI,OAAON,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAE5C;MACA,IAAIgC,4DAAE,CAACsE,aAAa,CAAC,CAAC,EACpB,OAAO,KAAK,IAAI,CAACrB,EAAE,CACjB,kGAAkG,EAClGjF,CACF,CAAC;MACH,IAAI,IAAI,CAAC8C,EAAE,IAAI,CAAC,IAAI,CAACA,EAAE,CAAC2E,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC3E,EAAE,CAAC4E,EAAE,CAAC,CAAC,EAChD,OACE/F,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACN,sEACF,CAAC,EACD,KAAK,IAAI,CAACpC,EAAE,CAAC6E,EAAE,CAAC,MAAM;QACpB,IAAI,CAACtB,SAAS,CAAChG,CAAC,EAAEL,CAAC,CAAC;MACtB,CAAC,CAAC;MAEN,MAAMoC,CAAC,GAAGA,CAAA,KAAM;UACZT,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,+CAA+C,CAAC,EACvD,UAAU,IAAI,OAAOlF,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACDqC,CAAC,GAAGA,CAAA,KAAM;UACR,IAAI/B,CAAC,GAAG,gDAAgD;UACxD0B,4DAAE,CAACsE,aAAa,CAAC,CAAC,KACfhG,CAAC,IACA,oGAAoG,CAAC,EACvGqB,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC5E,CAAC,CAAC,EACX,UAAU,IAAI,OAAON,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACDQ,CAAC,GAAGwB,4DAAE,CAAC4F,uBAAuB,CAAC,CAAC;QAChCtF,CAAC,GAAGA,CAAA,KAAM;UACR,CAAC9B,CAAC,IACA,IAAI,CAACiC,EAAE,IACP,IAAI,CAACA,EAAE,CAACoD,mCAAmC,CACzChE,sDAAI,CAACiE,6BAA6B,CAACC,QACrC,CAAC,EACD,IAAI,CAACQ,EAAE,CAAC,CAAC,CACN9C,IAAI,CAAErB,CAAC,IAAK;YACX,IAAI,IAAI,IAAIA,CAAC,EACX,OACET,uEAAC,CAACgC,CAAC,CAACC,KAAK,CACP,sLACF,CAAC,EACD,MAAM,UAAU,IAAI,OAAO5D,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1CoC,CAAC,CAACgE,WAAW,CACVyB,eAAe,CAAC,CAAC,CACjBpE,IAAI,CAAEpB,CAAC,IAAK;cACX,IAAI7B,CAAC,GAAG,IAAI;cACZ,IACG,IAAI,CAACsC,EAAE,IACN,IAAI,IAAI,IAAI,CAACA,EAAE,CAAC2E,EAAE,CAAC,CAAC,KACnBjH,CAAC,GAAGmB,uEAAC,CAACmG,EAAE,CAACC,EAAE,CAAC,IAAI,CAACjF,EAAE,CAAC2E,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7BpF,CAAC,EACD;gBACA,IAAIC,CAAC;kBACHC,CAAC,GAAG,IAAI;kBACRC,CAAC,GAAG,IAAI;gBACV,IAAK,IAAI,CAACF,CAAC,KAAKA,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC0F,CAAC,CAAC1H,sEAAC,CAAC2H,CAAC,CAACC,EAAE,CAAC,CAAC,EAAE5F,CAAC,IAAI,CAACf,4DAAC,CAACe,CAAC,CAAC,EAAG;kBAClD,IAAIhC,CAAC;kBACL,IAAI;oBACFA,CAAC,GAAGoB,6DAAE,CAACyG,EAAE,CAAC7F,CAAC,CAAC,CAAC8F,EAAE;kBACjB,CAAC,CAAC,OAAO/H,CAAC,EAAE;oBACVC,CAAC,GAAG,IAAI;kBACV;kBACA,IAAI,IAAIA,CAAC,IACP+H,KAAK,CAAC/H,CAAC,CAACgI,OAAO,CAAC,CAAC,CAAC,IAClB,CAAC,KAAKhI,CAAC,CAACgI,OAAO,CAAC,CAAC,KACf/F,CAAC,GAAGjC,CAAC,EACNkC,CAAC,GAAG,IAAIyD,IAAI,CAAC1D,CAAC,CAAC,EAChBC,CAAC,CAAC+F,QAAQ,CAAChG,CAAC,CAACiG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjC;gBACA,IAAI,IAAIhI,CAAC,IACT6B,CAAC,CAACqC,OAAO,IACTrC,CAAC,CAACqC,OAAO,CAACC,oBAAoB,IAC9BtC,CAAC,CAACqC,OAAO,CAACC,oBAAoB,CAACC,UAAU,IACzCvC,CAAC,CAACqC,OAAO,CAACC,oBAAoB,CAACC,UAAU,GAAG,CAAC,IAC7C,CAACnD,4DAAE,CAACjB,CAAC,EAAE,IAAI6D,UAAU,CAAChC,CAAC,CAACqC,OAAO,CAACC,oBAAoB,CAAC,CAAC,IACjDtC,CAAC,CAACqC,OAAO,CAACC,oBAAoB,CAACC,UAAU,GAAG,EAAE,GAC3CjD,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACN,oGACF,CAAC,GACDvD,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACN,4KACF,CAAC,EACL,IAAI,CAAC3B,EAAE,CAAClB,CAAC,EAAED,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC,IACtBqC,CAAC,CAACoG,cAAc,IAChB,IAAIxC,IAAI,CAAC5D,CAAC,CAACoG,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,IAClC,IAAIzC,IAAI,CAAC,CAAC,CAACyC,OAAO,CAAC,CAAC,IACrB/G,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACP,0DACF,CAAC,EACD,IAAI,CAAC3B,EAAE,CAAClB,CAAC,EAAED,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC,IACtBsC,CAAC,IAAIf,4DAAC,CAACe,CAAC,CAAC,GACT,IAAI,CAACiB,EAAE,CAAClB,CAAC,EAAED,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC,GACtB,IAAI,IAAIwC,CAAC,IACRb,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACP,sEACF,CAAC,EACD,IAAI,CAAC3B,EAAE,CAAClB,CAAC,EAAED,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC,IACtBwC,CAAC,CAACkG,OAAO,CAAC,CAAC,IAAI,IAAIzC,IAAI,CAAC,CAAC,CAACyC,OAAO,CAAC,CAAC,IAClC/G,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACP,mEACF,CAAC,EACD,IAAI,CAAC3B,EAAE,CAAClB,CAAC,EAAED,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC,KACrB2B,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACP,8EACF,CAAC,EACD,IAAI,CAACpB,EAAE,CAACzB,CAAC,EAAEE,CAAC,EAAElC,CAAC,CAAC,CAAC;cACvB,CAAC,MAAM,IAAI,CAACqD,EAAE,CAACtB,CAAC,EAAE5B,CAAC,EAAEH,CAAC,EAAEL,CAAC,CAAC;YAC5B,CAAC,CAAC,CACD6D,KAAK,CAAEvD,CAAC,IAAK;cACZqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CACP,6CAA6C,GAAGtD,CAClD,CAAC;YACH,CAAC,CAAC;UACN,CAAC,CAAC,CACDuD,KAAK,CAAEvD,CAAC,IAAK;YACZqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,qCAAqC,GAAGtD,CAAC,CAAC;UACtD,CAAC,CAAC;QACR,CAAC;MACH,IAAI,CAACkF,iBAAiB,CAAClD,CAAC,EAAED,CAAC,EAAED,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI,IAAI,CAACgB,EAAE,EAAE;MAClB,IAAI,IAAI,IAAI,IAAI,CAACP,EAAE,IAAI,EAAE,KAAK,IAAI,CAACA,EAAE,EACnC,OACElB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CACP,sGACF,CAAC,EACD,MAAM,UAAU,IAAI,OAAO5D,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C,MAAMM,CAAC,GAAG+E,MAAM,CAACC,MAAM,CAACC,gBAAgB,CAACH,UAAU,CAAC,IAAI,CAACvC,EAAE,CAAC;MAC5D,IAAI,CAACsC,EAAE,CAAC,IAAI,CAACtC,EAAE,EAAEvC,CAAC,EAAED,CAAC,EAAEL,CAAC,CAAC;IAC3B;EACF;EACAwD,WAAWA,CAAClD,CAAC,EAAED,CAAC,EAAE;IAChB,IAAI,CAAC2B,4DAAE,CAACgF,eAAe,CAAC,CAAC,EACvB,OAAOrF,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC/E,EAAE,CAAC8G,EAAE,CAAC,EAAE,MAAM,UAAU,IAAI,OAAO5G,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC6C,EAAE,GACHsD,SAAS,CAACC,aAAa,CAACC,eAAe,CAAC,CAAC,CAACjD,IAAI,CAAEzD,CAAC,IAAK;MACpDA,CAAC,GACGA,CAAC,CAACoG,WAAW,CACVyB,eAAe,CAAC,CAAC,CACjBpE,IAAI,CAAErB,CAAC,IAAK;QACXA,CAAC,KACE,IAAI,CAAC2C,EAAE,CAAC,CAAC,EACV3C,CAAC,CACEoB,WAAW,CAAC,CAAC,CACbC,IAAI,CAAErB,CAAC,IAAK;UACXA,CAAC,IACIT,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CACP,6CACF,CAAC,EACD,UAAU,IAAI,OAAO5E,CAAC,IAAIA,CAAC,CAAC,CAAC,KAC5BqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CACR,yCACF,CAAC,EACD,UAAU,IAAI,OAAOvD,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,EAChC,IAAI,CAACyG,EAAE,CAAC9G,CAAC,CAAC;QACd,CAAC,CAAC,CACD6D,KAAK,CAAEvD,CAAC,IAAK;UACZqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,6BAA6B,GAAGtD,CAAC,CAAC,EAC1C,UAAU,IAAI,OAAOD,CAAC,IAAIA,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CACDwD,KAAK,CAAEvD,CAAC,IAAK;QACZqB,uEAAC,CAACgC,CAAC,CAACC,KAAK,CAAC,iCAAiC,GAAGtD,CAAC,CAAC,EAC9C,UAAU,IAAI,OAAOD,CAAC,IAAIA,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,IACHsB,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,wCAAwC,CAAC,EACnD,UAAU,IAAI,OAAO5E,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,GACF,IAAI,CAAC8C,EAAE,KACN,IAAI,CAAC2B,EAAE,CAAC,CAAC,EACVpD,uEAAC,CAACgC,CAAC,CAACuB,IAAI,CAAC,gCAAgC,CAAC,EAC1C,UAAU,IAAI,OAAO5E,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;EACpC;AACF;AACAH,EAAE,CAAC8G,EAAE,GAAG,uDAAuD", "sources": ["./node_modules/@braze/web-sdk/src/Push/push-manager-factory.js", "./node_modules/@braze/web-sdk/src/Push/push-manager.js"], "sourcesContent": ["import e, { OPTIONS as L } from \"../managers/braze-instance.js\";\nimport ea from \"./push-manager.js\";\nconst na = {\n  t: !1,\n  i: null,\n  m: () => (\n    na.o(),\n    na.i ||\n      (na.i = new ea(\n        e.br(),\n        e.<PERSON>(),\n        e.te(),\n        e.Ys(),\n        e.nn(L._a),\n        e.nn(L.ka),\n        e.tr(),\n        e.nn(L.qa),\n        e.nn(L.Aa),\n        e.l(),\n      )),\n    na.i\n  ),\n  o: () => {\n    na.t || (e.g(na), (na.t = !0));\n  },\n  destroy: () => {\n    (na.i = null), (na.t = !1);\n  },\n};\nexport default na;\n", "import { isArray as p, isEqual as ii } from \"../util/code-utils.js\";\nimport ti from \"../models/push-token.js\";\nimport r from \"../../shared-lib/braze-shared-lib.js\";\nimport { STORAGE_KEYS as i } from \"../managers/storage-manager.js\";\nimport { User } from \"../User/index.js\";\nimport { WindowUtils as Z } from \"../util/window-utils.js\";\nimport jt from \"./utils/push-utils.js\";\nimport { getErrorMessage as ei } from \"../util/error-utils.js\";\nexport default class ea {\n  constructor(i, t, e, s, r, n, o, u, h, a) {\n    (this.hn = i),\n      (this.cn = t),\n      (this.fn = e),\n      (this.dn = r),\n      (this.bn = n),\n      (this.wt = o),\n      (this.yn = u),\n      (this.gn = h),\n      (this.u = a),\n      (this.hn = i),\n      (this.cn = t),\n      (this.fn = e),\n      (this.wn = s + \"/safari/\" + t),\n      (this.dn = r || \"/service-worker.js\"),\n      (this.bn = n),\n      (this.wt = o),\n      (this.yn = u || !1),\n      (this.gn = h || !1),\n      (this.u = a),\n      (this.vn = jt.kn()),\n      (this.Pn = jt.Dn());\n  }\n  Sn() {\n    return this.gn;\n  }\n  An(i, t, e, s, n) {\n    i.unsubscribe()\n      .then((i) => {\n        i\n          ? this.jn(t, e, s, n)\n          : (r.j.error(\"Failed to unsubscribe device from push.\"),\n            \"function\" == typeof n && n(!1));\n      })\n      .catch((i) => {\n        r.j.error(\"Push unsubscription error: \" + i),\n          \"function\" == typeof n && n(!1);\n      });\n  }\n  Un(i, t, e) {\n    const s = ((i) => {\n      if (\"string\" == typeof i) return i;\n      if (0 !== i.endpoint.indexOf(\"https://android.googleapis.com/gcm/send\"))\n        return i.endpoint;\n      let t = i.endpoint;\n      const e = i;\n      return (\n        e.Wn &&\n          -1 === i.endpoint.indexOf(e.Wn) &&\n          (t = i.endpoint + \"/\" + e.Wn),\n        t\n      );\n    })(i);\n    let r = null,\n      n = null;\n    const o = i;\n    if (null != o.getKey)\n      try {\n        const i = Array.from(new Uint8Array(o.getKey(\"p256dh\"))),\n          t = Array.from(new Uint8Array(o.getKey(\"auth\")));\n        (r = btoa(String.fromCharCode.apply(null, i))),\n          (n = btoa(String.fromCharCode.apply(null, t)));\n      } catch (i) {\n        if (\"invalid arguments\" !== ei(i)) throw i;\n      }\n    const u = ((i) => {\n      let t;\n      return i.options &&\n        (t = i.options.applicationServerKey) &&\n        t.byteLength &&\n        t.byteLength > 0\n        ? btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(t))))\n            .replace(/\\+/g, \"-\")\n            .replace(/\\//g, \"_\")\n        : null;\n    })(o);\n    this.hn && this.hn._n(s, t, r, n, u),\n      s && \"function\" == typeof e && e(s, r, n);\n  }\n  xn() {\n    this.hn && this.hn.Nn(!0);\n  }\n  Tn(i, t) {\n    this.hn && this.hn.Nn(!1), r.j.info(i), \"function\" == typeof t && t(!1);\n  }\n  zn(i, t, e, s) {\n    var n;\n    if (\"default\" === t.permission)\n      try {\n        window.safari.pushNotification.requestPermission(\n          this.wn,\n          i,\n          {\n            api_key: this.cn,\n            device_id:\n              (null === (n = this.fn) || void 0 === n ? void 0 : n.ce().id) ||\n              \"\",\n          },\n          (t) => {\n            \"granted\" === t.permission &&\n              this.hn &&\n              this.hn.setPushNotificationSubscriptionType(\n                User.NotificationSubscriptionTypes.OPTED_IN,\n              ),\n              this.zn(i, t, e, s);\n          },\n        );\n      } catch (i) {\n        this.Tn(\"Could not request permission for push: \" + i, s);\n      }\n    else\n      \"denied\" === t.permission\n        ? this.Tn(\n            \"The user has blocked notifications from this site, or Safari push is not configured in the Braze dashboard.\",\n            s,\n          )\n        : \"granted\" === t.permission &&\n          (r.j.info(\"Device successfully subscribed to push.\"),\n          this.Un(t.deviceToken, new Date(), e));\n  }\n  requestPermission(i, t, e) {\n    const s = (s) => {\n      switch (s) {\n        case \"granted\":\n          return void (\"function\" == typeof i && i());\n        case \"default\":\n          return void (\"function\" == typeof t && t());\n        case \"denied\":\n          return void (\"function\" == typeof e && e());\n        default:\n          r.j.error(\"Received unexpected permission result \" + s);\n      }\n    };\n    let n = !1;\n    const o = window.Notification.requestPermission((i) => {\n      n && s(i);\n    });\n    o\n      ? o.then((i) => {\n          s(i);\n        })\n      : (n = !0);\n  }\n  jn(i, t, e, s) {\n    const n = { userVisibleOnly: !0 };\n    null != t && (n.applicationServerKey = t),\n      i.pushManager\n        .subscribe(n)\n        .then((i) => {\n          r.j.info(\"Device successfully subscribed to push.\"),\n            this.Un(i, new Date(), e);\n        })\n        .catch((i) => {\n          jt.isPushBlocked()\n            ? (r.j.info(\"Permission for push notifications was denied.\"),\n              \"function\" == typeof s && s(!1))\n            : (r.j.error(\"Push subscription failed: \" + i),\n              \"function\" == typeof s && s(!0));\n        });\n  }\n  In() {\n    return this.yn\n      ? navigator.serviceWorker.getRegistration(this.dn)\n      : navigator.serviceWorker.register(this.dn).then(() =>\n          navigator.serviceWorker.ready.then(\n            (i) => (\n              i &&\n                \"function\" == typeof i.update &&\n                i.update().catch((i) => {\n                  r.j.info(\"ServiceWorker update failed: \" + i);\n                }),\n              i\n            ),\n          ),\n        );\n  }\n  Vn(i) {\n    this.yn ||\n      (i.unregister(), r.j.info(\"Service worker successfully unregistered.\"));\n  }\n  subscribe(t, e) {\n    if (!jt.isPushSupported())\n      return r.j.info(ea.qn), void (\"function\" == typeof e && e(!1));\n    if (this.vn) {\n      if (!this.yn && null != window.location) {\n        let i = this.dn;\n        -1 === i.indexOf(window.location.host) &&\n          (i = window.location.host + i),\n          -1 === i.indexOf(window.location.protocol) &&\n            (i = window.location.protocol + \"//\" + i);\n        const t = i.substr(0, i.lastIndexOf(\"/\") + 1);\n        if (0 !== Z.Cn().indexOf(t))\n          return (\n            r.j.error(\n              \"Cannot subscribe to push from a path higher than the service worker location (tried to subscribe from \" +\n                window.location.pathname +\n                \" but service worker is at \" +\n                i +\n                \")\",\n            ),\n            void (\"function\" == typeof e && e(!0))\n          );\n      }\n      if (jt.isPushBlocked())\n        return void this.Tn(\n          \"Notifications from this site are blocked. This may be a temporary embargo or a permanent denial.\",\n          e,\n        );\n      if (this.wt && !this.wt.En() && 0 === this.wt.li())\n        return (\n          r.j.info(\n            \"Waiting for VAPID key from server config before subscribing to push.\",\n          ),\n          void this.wt.Rn(() => {\n            this.subscribe(t, e);\n          })\n        );\n      const s = () => {\n          r.j.info(\"Permission for push notifications was denied.\"),\n            \"function\" == typeof e && e(!1);\n        },\n        n = () => {\n          let i = \"Permission for push notifications was ignored.\";\n          jt.isPushBlocked() &&\n            (i +=\n              \" The browser has automatically blocked further permission requests for a period (probably 1 week).\"),\n            r.j.info(i),\n            \"function\" == typeof e && e(!0);\n        },\n        o = jt.isPushPermissionGranted(),\n        u = () => {\n          !o &&\n            this.hn &&\n            this.hn.setPushNotificationSubscriptionType(\n              User.NotificationSubscriptionTypes.OPTED_IN,\n            ),\n            this.In()\n              .then((s) => {\n                if (null == s)\n                  return (\n                    r.j.error(\n                      \"No service worker registration. Set the `manageServiceWorkerExternally` initialization option to false or ensure that your service worker is registered before calling registerPush.\",\n                    ),\n                    void (\"function\" == typeof e && e(!0))\n                  );\n                s.pushManager\n                  .getSubscription()\n                  .then((n) => {\n                    let o = null;\n                    if (\n                      (this.wt &&\n                        null != this.wt.En() &&\n                        (o = r.On.Fn(this.wt.En())),\n                      n)\n                    ) {\n                      let u,\n                        h = null,\n                        a = null;\n                      if ((this.u && (u = this.u.v(i.k.Bn)), u && !p(u))) {\n                        let i;\n                        try {\n                          i = ti.Yn(u).Mn;\n                        } catch (t) {\n                          i = null;\n                        }\n                        null == i ||\n                          isNaN(i.getTime()) ||\n                          0 === i.getTime() ||\n                          ((h = i),\n                          (a = new Date(h)),\n                          a.setMonth(h.getMonth() + 6));\n                      }\n                      null != o &&\n                      n.options &&\n                      n.options.applicationServerKey &&\n                      n.options.applicationServerKey.byteLength &&\n                      n.options.applicationServerKey.byteLength > 0 &&\n                      !ii(o, new Uint8Array(n.options.applicationServerKey))\n                        ? (n.options.applicationServerKey.byteLength > 12\n                            ? r.j.info(\n                                \"Device was already subscribed to push using a different VAPID provider, creating new subscription.\",\n                              )\n                            : r.j.info(\n                                \"Attempting to upgrade a gcm_sender_id-based push registration to VAPID - depending on the browser this may or may not result in the same gcm_sender_id-based subscription.\",\n                              ),\n                          this.An(n, s, o, t, e))\n                        : n.expirationTime &&\n                          new Date(n.expirationTime).valueOf() <=\n                            new Date().valueOf()\n                        ? (r.j.info(\n                            \"Push subscription is expired, creating new subscription.\",\n                          ),\n                          this.An(n, s, o, t, e))\n                        : u && p(u)\n                        ? this.An(n, s, o, t, e)\n                        : null == a\n                        ? (r.j.info(\n                            \"No push subscription creation date found, creating new subscription.\",\n                          ),\n                          this.An(n, s, o, t, e))\n                        : a.valueOf() <= new Date().valueOf()\n                        ? (r.j.info(\n                            \"Push subscription older than 6 months, creating new subscription.\",\n                          ),\n                          this.An(n, s, o, t, e))\n                        : (r.j.info(\n                            \"Device already subscribed to push, sending existing subscription to backend.\",\n                          ),\n                          this.Un(n, h, t));\n                    } else this.jn(s, o, t, e);\n                  })\n                  .catch((i) => {\n                    r.j.error(\n                      \"Error checking current push subscriptions: \" + i,\n                    );\n                  });\n              })\n              .catch((i) => {\n                r.j.error(\"ServiceWorker registration failed: \" + i);\n              });\n        };\n      this.requestPermission(u, n, s);\n    } else if (this.Pn) {\n      if (null == this.bn || \"\" === this.bn)\n        return (\n          r.j.error(\n            \"You must supply the safariWebsitePushId initialization option in order to use registerPush on Safari\",\n          ),\n          void (\"function\" == typeof e && e(!0))\n        );\n      const i = window.safari.pushNotification.permission(this.bn);\n      this.zn(this.bn, i, t, e);\n    }\n  }\n  unsubscribe(i, t) {\n    if (!jt.isPushSupported())\n      return r.j.info(ea.qn), void (\"function\" == typeof t && t());\n    this.vn\n      ? navigator.serviceWorker.getRegistration().then((e) => {\n          e\n            ? e.pushManager\n                .getSubscription()\n                .then((s) => {\n                  s &&\n                    (this.xn(),\n                    s\n                      .unsubscribe()\n                      .then((s) => {\n                        s\n                          ? (r.j.info(\n                              \"Device successfully unsubscribed from push.\",\n                            ),\n                            \"function\" == typeof i && i())\n                          : (r.j.error(\n                              \"Failed to unsubscribe device from push.\",\n                            ),\n                            \"function\" == typeof t && t()),\n                          this.Vn(e);\n                      })\n                      .catch((i) => {\n                        r.j.error(\"Push unsubscription error: \" + i),\n                          \"function\" == typeof t && t();\n                      }));\n                })\n                .catch((i) => {\n                  r.j.error(\"Error unsubscribing from push: \" + i),\n                    \"function\" == typeof t && t();\n                })\n            : (r.j.info(\"Device already unsubscribed from push.\"),\n              \"function\" == typeof i && i());\n        })\n      : this.Pn &&\n        (this.xn(),\n        r.j.info(\"Device unsubscribed from push.\"),\n        \"function\" == typeof i && i());\n  }\n}\nea.qn = \"Push notifications are not supported in this browser.\";\n"], "names": ["e", "OPTIONS", "L", "ea", "na", "t", "i", "m", "o", "br", "Ma", "te", "Ys", "nn", "_a", "ka", "tr", "qa", "Aa", "l", "g", "destroy", "isArray", "p", "isEqual", "ii", "ti", "r", "STORAGE_KEYS", "User", "WindowUtils", "Z", "jt", "getErrorMessage", "ei", "constructor", "s", "n", "u", "h", "a", "hn", "cn", "fn", "dn", "bn", "wt", "yn", "gn", "wn", "vn", "kn", "Pn", "Dn", "Sn", "An", "unsubscribe", "then", "jn", "j", "error", "catch", "Un", "endpoint", "indexOf", "Wn", "<PERSON><PERSON><PERSON>", "Array", "from", "Uint8Array", "btoa", "String", "fromCharCode", "apply", "options", "applicationServerKey", "byteLength", "replace", "_n", "xn", "Nn", "Tn", "info", "zn", "permission", "window", "safari", "pushNotification", "requestPermission", "api_key", "device_id", "ce", "id", "setPushNotificationSubscriptionType", "NotificationSubscriptionTypes", "OPTED_IN", "deviceToken", "Date", "Notification", "userVisibleOnly", "pushManager", "subscribe", "isPushBlocked", "In", "navigator", "serviceWorker", "getRegistration", "register", "ready", "update", "Vn", "unregister", "isPushSupported", "qn", "location", "host", "protocol", "substr", "lastIndexOf", "Cn", "pathname", "En", "li", "Rn", "isPushPermissionGranted", "getSubscription", "On", "Fn", "v", "k", "Bn", "Yn", "Mn", "isNaN", "getTime", "setMonth", "getMonth", "expirationTime", "valueOf"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}