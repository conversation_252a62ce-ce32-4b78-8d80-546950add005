using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes
{
    public class ListOptionsResponse : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Options")]
        public List<Option> Options { get; set; }
    }


    public class SubOptionEdit
    {
        [JsonProperty(PropertyName = "MenuItemOptionId")]
        public int MenuItemOptionId { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool IsActive { get; set; }

        [JsonProperty(PropertyName = "OptionName")]
        public string OptionName { get; set; }

        [JsonProperty(PropertyName = "OptionCost")]
        public decimal OptionCost { get; set; }

        [JsonProperty(PropertyName = "OptionOrder")]
        public int OptionOrder { get; set; }
    }

    public class AddRemoveOptionRequest
    {
        [JsonProperty(PropertyName = "ItemId")]
        public int ItemId { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public int? CanteenId { get; set; }

        [JsonProperty(PropertyName = "OptionId")]
        public int OptionId { get; set; }

        [JsonProperty(PropertyName = "MenuId")]
        public int MenuId { get; set; }
    }
}
