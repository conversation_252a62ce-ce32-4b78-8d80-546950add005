﻿using System;
using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Menus;
using Schools.BLL.Helpers;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers.Menus;

public class MenuAssembler
{
    public static MenuDto ConvertMenuWithCutOffTimeDtoToListMenuDto(MenuWithCutOffTimeDto entity)
    {
        MenuDto dto = new()
        {
            MenuId = entity.MenuId,
            Name = entity.FriendlyName,
            MenuType = entity.Name,
            CanteenId = entity.CanteenId,
            SchoolId = entity.SchoolId,
            MenuAvailableDays = entity.MenuAvailableDays,
            MenuUniqueDate = entity.MenuUniqueDate,
            EarlyCutOffTime = MenuHelper.GetEarlyCutOffTime(entity.ItemsCutOffTimes),
            LateCutOffTime = MenuHelper.GetLateCutOffTime(entity.MenuCutOffTime, entity.ItemsCutOffTimes),
            CutOffTime = entity.MenuCutOffTime
        };

        return dto;
    }

    public static List<MenuDto> ConvertListMenuWithCutOffTimeDtoToListMenuDto(IEnumerable<MenuWithCutOffTimeDto> entities)
    {
        List<MenuWithCutOffTimeDto> updatedEntities = new();
        List<MenuDto> dto = new();

        if (entities != null && entities.Any())
        {
            // first loop to setup the items for each menu
            foreach (var e in entities)
            {
                var index = updatedEntities.FindIndex(f => f.MenuId == e.MenuId);
                if (index >= 0)
                {
                    MenuItemWithCutOffTimeDto i = new()
                    {
                        CutOffTime = e.CutOffTime,
                        CutOffTimeType = e.CutOffTimeType
                    };

                    updatedEntities[index].ItemsCutOffTimes.Add(i);
                }
                else
                {
                    MenuItemWithCutOffTimeDto i = new()
                    {
                        CutOffTime = e.CutOffTime,
                        CutOffTimeType = e.CutOffTimeType
                    };

                    e.ItemsCutOffTimes = new()
                    {
                        i
                    };

                    updatedEntities.Add(e);
                }
            }

            // second loop to calculate cut off times
            foreach (var e in updatedEntities)
            {
                dto.Add(ConvertMenuWithCutOffTimeDtoToListMenuDto(e));
            }
        }

        return dto;
    }

    /// <summary>
    /// Convert a Menu Entity to an MenuNamesDto
    /// </summary>
    /// <param name="entity"></param>
    public static MenuNamesDto ConvertMenuEntityToMenuNamesDto(Menu entity)
    {
        MenuNamesDto dto = new()
        {
            MenuId = Convert.ToInt32(entity.MenuId),
            Name = entity.FriendlyName,
            MenuType = entity.Name,
            FriendlyName = entity.FriendlyName
        };

        return dto;
    }

    /// <summary>
    /// Convert a list of Menu entities to a list of MenuNamesDto
    /// </summary>
    /// <param name="entities"></param>
    public static List<MenuNamesDto> ConvertListMenuEntityToListMenuNamesDto(List<Menu> entities)
    {
        List<MenuNamesDto> dto = new();

        foreach (var ent in entities)
        {
            dto.Add(ConvertMenuEntityToMenuNamesDto(ent));
        }

        return dto;
    }
}