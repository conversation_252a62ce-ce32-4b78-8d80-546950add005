using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using Schools.DAL.DtosToMoveToBLL;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface IPrintingValidator
{
    void ValidateOrderRequest(OrderRequest request);
    Task ValidateUserAccessToSchools(string schools);
    Task ValidateAccessToOrder(Order order);
    Task ValidateAccessToSchool(long schoolId);
}

public class PrintingValidator : IPrintingValidator
{
    private readonly IUserService _userService;
    private readonly ICanteenService _canteenService;
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<PrintingValidator> _logger;
    public PrintingValidator(IUserService userService, ICanteenService canteenService, IAuthenticationValidator authenticationValidator, ILogger<PrintingValidator> logger)
    {
        _logger = logger;
        _authenticationValidator = authenticationValidator;
        _userService = userService;
        _canteenService = canteenService;
    }

    public void ValidateOrderRequest(OrderRequest request)
    {
        if (request == null) throw new ValidationException("request must be provided");
        if (request.merchantId < 1) throw new ValidationException("merchant id must be provided");
        if (string.IsNullOrEmpty(request.SchoolIds)) throw new ValidationException("school ids must be provided");
    }

    public async Task ValidateUserAccessToSchools(string schools)
    {
        var schoolIds = new List<long>();
        try
        {
            schoolIds = schools.Split(',').Select(long.Parse).ToList();
        }
        catch
        {
            throw new ValidationException("invalid school ids");
        }
        await _authenticationValidator.ValidateAccessToSchools(schoolIds);
    }

    public async Task ValidateAccessToSchool(long schoolId)
    {
        await _authenticationValidator.ValidateAccessToSchool(schoolId);
    }

    public async Task ValidateAccessToOrder(Order order)
    {
        var user = await _userService.GetCurrentUser();

        if (user.IsAdmin) return;

        var canteens = await _canteenService.GetMerchantCanteenAndSchoolLink(user.UserId);

        if (canteens.Any(c => c.CanteenId == order.CanteenId)) return;

        throw new ValidationException("user does not have access to order");
    }
}