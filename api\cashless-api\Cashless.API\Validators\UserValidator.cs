using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Users;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using Schools.DAL.DtosToMoveToBLL;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface IUserValidator
{
    Task ValidateAccessToCurrentUser(long userId);
    Task ValidateAccessToUser(User requestedUser);
    Task ValidateAccessToStudent(int studentId);
    Task ValidateAccessToCanteen(long canteenId);
    Task ValidateRequest(CreateUserRequest request);
    Task ValidateRequest(UserRequest request);
    Task ValidateRequest(UserUpdateProfile userProfileRequest);
    Task ValidateRequest(Student request);
    void ValidateRequest(ResetPasswordRequest request);
    void CheckTransferUserBalance(TransferUserBalanceRequest request, User from, User to);
    Task ValidateCurrentAndRequestedUserAtCanteen(long canteenId, long requestedUserId);
    void ValidateRequest(CanteenUserSettingsRequest request);
    Task ValidateAccessToUsers(IEnumerable<User> users);
    Task ValidateRequest(CreateSchoolsUserRequest request);
    Task ValidateRequest(UpdateSchoolsUserRequest request);
}

/// <summary>
/// Validator to check various User API calls
/// </summary>
public class UserValidator : IUserValidator
{
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly IUserService _userService;
    private readonly ICanteenService _canteenService;
    private readonly ILogger<UserValidator> _logger;

    public UserValidator(IAuthenticationValidator authenticationValidator, IUserService userService, ICanteenService canteenService, ILogger<UserValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _userService = userService;
        _canteenService = canteenService;
        _logger = logger;
    }
    public async Task ValidateAccessToCurrentUser(long userId)
    {
        await _authenticationValidator.ValidateAccessForCurrentUser(userId);
    }
    public async Task ValidateAccessToUser(User requestedUser)
    {
        await _authenticationValidator.ValidateAccessToUser(requestedUser);
    }

    public async Task ValidateAccessToStudent(int studentId)
    {
        if (studentId < 1) throw new ValidationException("Valid student id is requried");
        await _authenticationValidator.ValidateAccessToStudent(studentId);
    }

    public async Task ValidateAccessToCanteen(long canteenId)
    {
        await _authenticationValidator.ValidateAccessToCanteen(canteenId);
    }


    public async Task ValidateRequest(CreateSchoolsUserRequest request)
    {
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        if (string.IsNullOrWhiteSpace(request.FirstName))
        {
            throw new ValidationException("FirstName", "First name is a required field");
        }

        if (string.IsNullOrWhiteSpace(request.Lastname))
        {
            throw new ValidationException("Lastname", "Last name is a required field");
        }

        if (request.Role == UserRole.Unknown)
        {
            throw new ValidationException("Role", "Invalid user role");
        }

        var currentUser = await _userService.GetCurrentUser();

        if (request.Role == UserRole.Student)
        {
            if (!currentUser.IsAdmin && !currentUser.IsParent)
            {
                throw new UnauthorizedAccessException("Invalid user access");
            }

            if (request.ParentId == null)
            {
                throw new ValidationException("ParentId", "Parent ID is a required field");
            }

            if (currentUser.IsParent && request.ParentId != currentUser.UserId)
            {
                throw new ValidationException("Invalid parent user");
            }

            if (request.SchoolId == null)
            {
                throw new ValidationException("SchoolId", "School ID is a required field");
            }

            if (request.ClassId == null || request.ClassId < 1)
            {
                throw new ValidationException("ClassId", "Class ID is a required field");
            }
            return;
        }

        if (request.Role == UserRole.Parent)
        {
            if (!currentUser.IsAdmin)
            {
                throw new UnauthorizedAccessException("Invalid user access");
            }
            if (string.IsNullOrWhiteSpace(request.FirebaseUserId))
            {
                throw new ValidationException("FirebaseUserId", "Fiberbase ID is a required field");
            }

            if (string.IsNullOrWhiteSpace(request.Email))
            {
                throw new ValidationException("Email", "Email is a required field");
            }

            if (string.IsNullOrWhiteSpace(request.Mobile))
            {
                throw new ValidationException("Mobile", "Mobile is a required field");
            }
            return;
        }

        _logger.LogError($"user role of {request.Role.ToString()} is invalid");
        throw new ValidationException("Invalid user role");
    }

    public async Task ValidateRequest(UpdateSchoolsUserRequest request)
    {
        if (request == null || request.UserId < 1)
        {
            throw new ValidationException("Invalid request");
        }

        await ValidateUserUpdateAccess(request);
    }

    /// <summary>
    /// Check a Student insert / update 
    /// </summary>
    public async Task ValidateRequest(Student request)
    {
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        // TODO - Add field validations

        var parent = await _userService.GetCurrentUser();

        if (request.UserId.HasValue)
        {
            await ValidateUpdate(request, parent);
        }
        else
        {
            ValidateInsert(request, parent);
        }
    }

    /// <summary>
    /// Check a Student update request
    /// </summary>
    private async Task ValidateUpdate(Student request, User parent)
    {
        // Fetch the existing Student by ID
        var student = await _userService.GetUserById(request.UserId.Value);

        if (!student.IsStudent)
        {
            _logger.LogError("Current user attempting to update User with UserId = {UserId} as a Student", request.UserId);

            throw new UnauthorizedAccessException("Invalid user update");
        }

        _logger.LogDebug("Comparing current user #{UserId} against Student #{StudentId} with Parent #{ParentId}", parent.UserId, student.UserId, student.ParentId);

        if (student.ParentId != parent.UserId)
        {
            _logger.LogError("Current user does not have access to Student with UserId = {UserId}", request.UserId);

            throw new UnauthorizedAccessException("Invalid user access");
        }

        if (student.ParentId != request.ParentId)
        {
            _logger.LogError("Current user attempting to change the parent on Student with UserId = {UserId}", request.UserId);

            throw new UnauthorizedAccessException("Invalid user update");
        }
    }

    /// <summary>
    /// Check a Student insert request
    /// </summary>
    private void ValidateInsert(Student request, User parent)
    {
        if (request.ParentId != parent.UserId)
        {
            throw new UnauthorizedAccessException("Invalid user access");
        }
    }

    /// <summary>
    /// Validate the user profile update request
    /// </summary>
    public async Task ValidateRequest(UserUpdateProfile request)
    {
        if (request == null || request.UserId <= 0)
        {
            throw new ValidationException("Invalid request");
        }

        // Field validations - can be moved to the request class
        if (string.IsNullOrWhiteSpace(request.FirstName))
        {
            throw new ValidationException("FirstName", "First name is a required field");
        }

        if (string.IsNullOrWhiteSpace(request.Lastname))
        {
            throw new ValidationException("LastName", "Last name is a required field");
        }

        if (string.IsNullOrWhiteSpace(request.Mobile))
        {
            throw new ValidationException("Mobile", "Mobile phone number is a required field");
        }

        // Throws an exception if current user is not found
        var user = await _userService.GetCurrentUser();

        // Check the current user is the one referenced in the request 
        if (user.UserId != request.UserId)
        {
            _logger.LogError("Current user does not have access to user with UserId = {UserId}", request.UserId);

            throw new UnauthorizedAccessException("Invalid user access");
        }
    }

    /// <summary>
    /// Check that the user requested is the logged in user!
    /// </summary>
    public async Task ValidateRequest(UserRequest request)
    {
        // Invalid request
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        if (string.IsNullOrWhiteSpace(request.FirebaseUserId) &&
            (request.UserId == null || request.UserId.Value <= 0) &&

            string.IsNullOrWhiteSpace(request.Email))
        {
            throw new ValidationException("Invalid request");
        }

        // Throws an exception if current user is not found
        var user = await _userService.GetCurrentUser();

        // Check the current user is the one referenced in the request 
        if (request.UserId != null && user.UserId != request.UserId)
        {
            _logger.LogError("Current user does not have access to user with UserId = {UserId}", request.UserId);

            throw new UnauthorizedAccessException("Invalid user access");
        }

        if (!string.IsNullOrWhiteSpace(request.FirebaseUserId) && user.FirebaseUserId != request.FirebaseUserId)
        {
            _logger.LogError("Current user does not have access to user with FirebaseUserId = {FirebaseUserId}", request.FirebaseUserId);

            throw new UnauthorizedAccessException("Invalid user access");
        }

        if (!string.IsNullOrWhiteSpace(request.Email) && user.Email != request.Email)
        {
            _logger.LogError("Current user does not have access to user with Email = {Email}", request.Email);

            throw new UnauthorizedAccessException("Invalid user access");
        }
    }

    /// <summary>
    /// Check the transfer user balance operation
    /// </summary>
    public void CheckTransferUserBalance(TransferUserBalanceRequest request, User fromUser, User toUser)
    {
        if (fromUser == null)
        {
            throw new ValidationException("FromUserId", $"Source user #{request.FromUserId} is not found.");
        }

        if (toUser == null)
        {
            throw new ValidationException("ToUserId", $"Destination user #{request.ToUserId} is not found.");
        }

        if (toUser.IsStudent)
        {
            throw new ValidationException("ToUserId", $"Destination user #{request.ToUserId} is a Child. Balance transfers can only be made to Parent, Merchant or Admin users");
        }
    }

    /// <summary>
    /// Ensure request contains valid looking details
    /// </summary>
    public void ValidateRequest(ResetPasswordRequest request)
    {
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        if (string.IsNullOrWhiteSpace(request.Email))
        {
            throw new ValidationException("Email", "Email is required");
        }
    }

    /// <summary>
    /// Validate the request to create a new User before attempting the create
    /// </summary>
    public async Task ValidateRequest(CreateUserRequest request)
    {
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        // Validate required fields
        if (string.IsNullOrWhiteSpace(request.Email))
        {
            throw new ValidationException("Email", "Email is required");
        }

        if (string.IsNullOrWhiteSpace(request.FirstName))
        {
            throw new ValidationException("FirstName", "First name is required");
        }

        if (string.IsNullOrWhiteSpace(request.Lastname))
        {
            throw new ValidationException("Lastname", "Last name is required");
        }

        if (string.IsNullOrWhiteSpace(request.Mobile))
        {
            throw new ValidationException("Mobile", "Mobile is required");
        }

        if (string.IsNullOrWhiteSpace(request.FirebaseUserId))
        {
            if (string.IsNullOrWhiteSpace(request.Password))
            {
                throw new ValidationException("Password", "Password is required");
            }
        }
        else
        {
            var user1 = await _userService.GetUser(new UserRequest()
            {
                FirebaseUserId = request.FirebaseUserId
            });

            if (user1 != null)
            {
                throw new ValidationException("FirebaseUserId", "Another user with this Firebase User ID already exists");
            }
        }

        // Check new User is unique
        var user2 = await _userService.GetUser(new UserRequest()
        {
            Email = request.Email
        });

        if (user2 != null)
        {
            throw new ValidationException("Email", "There's already an account with the email address: " + request.Email);
        }
    }

    public void ValidateRequest(CanteenUserSettingsRequest request)
    {
        if (request == null)
            throw new ValidationException("Invalid request");

        if (request.UserId < 1)
            throw new ValidationException("invalid user id");

        if (request.CanteenId < 1)
            throw new ValidationException("invalid canteen id");
    }

    public async Task ValidateCurrentAndRequestedUserAtCanteen(long canteenId, long requestedUserId)
    {
        var user = await _userService.GetCurrentUser();

        if (!user.IsAdmin && !user.IsMerchant)
        {
            _logger.LogError($"user with id {user.UserId} is not a merchant and does not have permission to access canteen with id {canteenId} in this way");
            throw new UnauthorizedAccessException("Invalid user access");
        }

        var canteenUsers = await _canteenService.GetMerchantCanteenAndSchoolLinkForCanteen(canteenId);
        if (canteenUsers == null || !canteenUsers.Any())
        {
            _logger.LogError($"no users found for canteen with id {canteenId}");
            throw new UnauthorizedAccessException("Invalid user access");
        }

        if (!canteenUsers.Any(u => u.UserId == requestedUserId))
        {
            _logger.LogError($"user with id {requestedUserId} not found for canteen with id {canteenId}");
            throw new UnauthorizedAccessException("Invalid user access");
        }

        if (user.IsAdmin) return;

        if (canteenUsers.Any(u => u.UserId == user.UserId)) return;

        _logger.LogError($"user with id {user.UserId} not found for canteen with id {canteenId}");
        throw new UnauthorizedAccessException("Invalid user access");
    }

    public async Task ValidateAccessToUsers(IEnumerable<User> users)
    {
        if (users == null || !users.Any()) return;

        var user = await _userService.GetCurrentUser();

        if (user.IsAdmin) return;

        var userSchools = await _canteenService.GetMerchantCanteenAndSchoolLink(user.UserId);

        if (userSchools == null || !userSchools.Any())
        {
            _logger.LogError($"user with id {user.UserId} does not have access to any schools");
            throw new UnauthorizedAccessException("Invalid user access");
        }

        var schools = userSchools.Select(s => s.SchoolId).ToHashSet();

        foreach (var u in users)
        {
            if (u.SchoolId != null && schools.Contains((long)u.SchoolId)) continue;

            _logger.LogError($"user with id {user.UserId} does not have access to school with id {u.SchoolId}");
            throw new UnauthorizedAccessException("Invalid user access");
        }
    }

    public async Task ValidateUserUpdateAccess(UpdateSchoolsUserRequest request)
    {
        var currentUser = await _userService.GetCurrentUser();
        if (currentUser.IsAdmin) return;

        var requestUser = await _userService.GetUserById(request.UserId);

        var userRole = Enum.Parse<UserRole>(requestUser.Role);

        if (currentUser.IsParent)
        {
            if (request.Role == UserRole.Parent)
            {
                if (currentUser.UserId == request.UserId && (request.Role == null || userRole == request.Role)) return;
                _logger.LogError($"Parent user with id {currentUser.UserId} cannot change their role or update another user. Request user id {request.UserId} - request user role {request.Role}");
                throw new ValidationException(validationError: "Invalid user update request");
            }

            if (request.Role == UserRole.Student)
            {
                bool validAccess = true;
                if (currentUser.UserId != requestUser.ParentId) validAccess = false;
                if (request.ParentId != null && request.ParentId != requestUser.ParentId) validAccess = false;
                if (userRole != UserRole.Student) validAccess = false;

                if (validAccess) return;

                _logger.LogError($"Parent user with id {currentUser.UserId} cannot change their childrens role or update another parents children. Request user id {request.UserId} - request user role {request.Role}");
                throw new ValidationException(validationError: "Invalid user update request");
            }
        }

        _logger.LogError($"user with id {currentUser.UserId} cannot update user with id {request.UserId}");
        throw new UnauthorizedAccessException("Invalid user access");
    }

}
