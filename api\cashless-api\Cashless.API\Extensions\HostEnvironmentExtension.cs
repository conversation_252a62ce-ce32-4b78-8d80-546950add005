#region Assembly Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60
// Microsoft.Extensions.Hosting.Abstractions.dll
#endregion


using Microsoft.Extensions.Hosting;

namespace Cashless.APIs.Extensions
{
    public static class HostEnvironmentExtension
    {
        /// <summary>
        /// Check if the env. is UAT
        /// </summary>
        public static bool IsUAT(this IHostEnvironment env)
        {
            return env.IsEnvironment("UAT");
        }

        public static bool IsPRODAWS(this IHostEnvironment env)
        {
            return env.IsEnvironment("prod");
        }

        /// <summary>
        /// Provide a 3-4 letter code in place of the env. name
        /// </summary>
        public static string GetEnvironmentCode(this IHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                return "dev";
            }

            if (env.IsUAT())
            {
                return "uat";
            }

            if (env.IsStaging())
            {
                return "stg";
            }

            if (env.IsPRODAWS())
            {
                return "prod";
            }

            // Failed!
            return env.EnvironmentName;
        }
    }
}