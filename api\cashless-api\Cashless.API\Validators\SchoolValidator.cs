using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using Schools.DAL.DtosToMoveToBLL;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface ISchoolValidator
{
    Task ValidateCurrentUser(string userId);
    Task ValidateAccessToSchool(long schoolId);
    void ValidateOptionSchool(OptionSchool request);
}

public class SchoolValidator : ISchoolValidator
{
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<SchoolValidator> _logger;

    public SchoolValidator(IAuthenticationValidator authenticationValidator, ILogger<SchoolValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _logger = logger;
    }

    public async Task ValidateCurrentUser(string userId)
    {
        if (string.IsNullOrEmpty(userId))
        {
            throw new ValidationException("UserId", "User ID is required");
        }

        var isValidId = long.TryParse(userId, out long currentUserId);

        if (!isValidId)
        {
            throw new ValidationException("Invalid User ID");
        }

        await _authenticationValidator.ValidateAccessForCurrentUser(currentUserId);
    }

    public async Task ValidateAccessToSchool(long schoolId)
    {
        await _authenticationValidator.ValidateAccessToSchool(schoolId);
    }

    public void ValidateOptionSchool(OptionSchool request)
    {
        if (request == null)
        {
            throw new ValidationException("Invalid request");
        }

        if (request.SchoolId < 1)
        {
            throw new ValidationException("SchoolId", "School ID is required");
        }

        if (request.SchoolOptionsId < 1)
        {
            throw new ValidationException("SchoolOptionsId", "School Options ID is required");
        }
    }
}
