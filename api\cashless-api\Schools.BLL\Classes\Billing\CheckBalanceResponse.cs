﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Billing;

public class CheckBalanceResponse
{
    [JsonProperty(PropertyName = "Status")]
    public string Status { get; set; }

    [JsonProperty(PropertyName = "BalanceDiff")]
    public decimal? BalanceDiff { get; set; }
}

public static class CheckBalanceStatusEnum
{
    public static String Correct
    {
        get { return "Correct"; }
    }

    public static String Surplus
    {
        get { return "Surplus"; }
    }

    public static String Deficit
    {
        get { return "Deficit"; }
    }
}
