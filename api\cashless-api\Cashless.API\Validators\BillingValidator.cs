using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using Schools.BLL.Validators;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Validators;

public interface IBillingValidator
{
    Task ValidateAccessToStudents(IEnumerable<long> studentIds);
    Task ValidateAccessToUser(long userId);
    Task ValidateAccess(MultipleOrders orders);
    Task ValidateRequest(MultipleOrders request);
    Task ValidateRequest(TransactionHistorySearchRequest request);
    Task ValidateRequest(long transactionId);
}

/// <summary>
/// Validate requests into the Order API
/// </summary>
public class BillingValidator : IBillingValidator
{
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly IUserService _userService;
    private readonly ILogger<BillingValidator> _logger;

    public BillingValidator(IAuthenticationValidator authenticationValidator, IUserService userService, ILogger<BillingValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _userService = userService;
        _logger = logger;
    }

    public async Task ValidateAccessToStudents(IEnumerable<long> studentIds)
    {
        if (studentIds == null || !studentIds.Any())
        {
            throw new ValidationException("No students provided");
        }

        await _authenticationValidator.ValidateAccessToStudents(studentIds);
    }

    public async Task ValidateAccessToUser(long userId)
    {
        await _authenticationValidator.ValidateAccessForCurrentUser(userId);
    }

    public async Task ValidateAccess(MultipleOrders orders)
    {
        if (orders == null || orders.Orders == null || !orders.Orders.Any())
        {
            throw new ValidationException("Invalid request");
        }

        User user = null;
        try
        {
            user = await _userService.GetCurrentUser();
        }
        catch
        {
            _logger.LogInformation("No current user - assessing access to orders based on requested ids");
        }

        if (user != null)
        {
            await this.ValidateAccessToStudents(orders.Orders.Select(selector: o => (long)o.StudentId).ToList());

            return;
        }

        var students = new Dictionary<long, User>();

        foreach (var o in orders.Orders)
        {
            User student;

            // only look up each student once
            if (students.ContainsKey(o.StudentId))
            {
                student = students[o.StudentId];
            }
            else
            {
                student = await _userService.GetUserById(o.StudentId);
                students.Add(o.StudentId, student);
            }

            if (o.UserId == student.ParentId)
            {
                continue;
            }

            _logger.LogError($"order with id {o.OrderId} has user id {o.UserId} that is not associated to student with id {o.StudentId}");

            throw new UnauthorizedAccessException("Invalid user access");
        }
    }

    public async Task ValidateRequest(MultipleOrders request)
    {
        if (request == null || request.Orders == null || !request.Orders.Any())
        {
            throw new ValidationException("Invalid request");
        }

        await ValidateAccessToStudents(request.Orders.Select(o => (long)o.StudentId).ToList());
    }

    public async Task ValidateRequest(TransactionHistorySearchRequest request)
    {
        if (request == null || request.UserId <= 0)
        {
            throw new ValidationException("Invalid request");
        }

        await ValidateAccessToUser(request.UserId);
    }

    public async Task ValidateRequest(long transactionId)
    {
        if (transactionId <= 0)
        {
            throw new ValidationException("Invalid request");
        }

        await _authenticationValidator.ValidateAccessToTransaction(transactionId);
    }
}