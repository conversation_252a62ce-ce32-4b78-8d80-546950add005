﻿using System;
using Schools.BLL.Helpers;
using Schools.DAL.Entities;
using Schools.DAL.Enums;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Audit;

public abstract class OrderAuditLog : BaseAuditLog
{
    public long OrderId { get; set; }
    public string CorrelationId { get; set; }

    public OrderAuditLog(OrderActionsEnum type, string subType, long orderId, string correlationId) : base()
    {
        this.LogType = type.ToString();
        this.LogSubType = subType;
        this.OrderId = orderId;
        this.CorrelationId = !String.IsNullOrEmpty(correlationId) ? correlationId : String.Empty;
    }

    /// <summary>
    /// Add the items details in the metadata to be able to track the changes
    /// </summary>
    /// <param name="order"></param>
    protected void GenerateItemsMetadata(Order order)
    {
        if (order.Items != null && order.Items.Count > 0)
        {
            foreach (var i in order.Items)
            {
                string itemText = i.Quantity + " x " + i.Name + ItemOptionHelper.GetItemOptions(i);

                this.Metadata.Add("item" + Guid.NewGuid().ToString(), itemText);
            }
        }
    }
}

public class OrderAudit : OrderAuditLog
{
    public OrderAudit(long orderId, long actionnedBy, OrderActionsEnum type, string correlationId, Order order = null) : base(type, string.Empty, orderId, correlationId)
    {
        this.ActionnedByUserId = actionnedBy;

        if (order != null)
        {
            // add metadata
            this.GenerateItemsMetadata(order);
        }
    }
}

public class OrderPaymentAudit : OrderAuditLog
{
    public OrderPaymentAudit(long orderId, OrderProcessStatusEnum subType, string correlationId, string errorMessage = null) : base(OrderActionsEnum.Payment, subType.ToString(), orderId, correlationId)
    {
        if (!String.IsNullOrEmpty(errorMessage))
        {
            this.Failed = true;
            this.Description = errorMessage;
        }
    }
}