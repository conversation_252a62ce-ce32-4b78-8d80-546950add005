using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.BLL.Classes.Messaging;
using Schools.BLL.Classes.Orders.Events;
using Schools.BLL.Classes.Stripe.Events;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.ThirdParty.General;
using Schools.BLL.Exceptions;
using Stripe;
using Card = Schools.BLL.Classes.Payments.Card;
using Event = Stripe.Event;
using PaymentMethod = Schools.BLL.Classes.Payments.PaymentMethod;

namespace Schools.BLL.Services;

public class StripeService : IStripeService
{
    private const string DEFAULT_CURRENCY = "aud";
    private readonly ITelemetryService _telemetryService;
    private readonly ILogger<StripeService> _logger;
    private readonly IConfiguration _config;
    public static bool IsStripeGatewayEnabled;
    private readonly ISendEndpointProvider _sendEndpointProvider;

    public StripeService(ITelemetryService telemetryService, ILogger<StripeService> logger, IConfiguration config, ISendEndpointProvider sendEndpointProvider)
    {
        _telemetryService = telemetryService;
        _logger = logger;
        _config = config;
        _sendEndpointProvider = sendEndpointProvider;
    }

    public async Task<string> CreateCustomer(string custRef) //string firstName, string lastname, string email
    {
        ArgumentNullException.ThrowIfNull(custRef, "custRef");

        var options = new CustomerCreateOptions
        {
            Name = custRef,
            // Email = email
        };
        var service = new CustomerService();
        var customer = await service.CreateAsync(options);

        var customerId = customer?.Id;

        _logger.LogDebug($"Stripe Customer {customerId} created.");

        return customerId;
    }

    private async Task<PaymentIntent> CreatePaymentIntent(string customerId, long amount)
    {
        var paymentIntentOptions = new PaymentIntentCreateOptions
        {
            Amount = amount,
            Currency = DEFAULT_CURRENCY,
            Customer = customerId,
            SetupFutureUsage = "on_session"
        };
        var paymentIntentService = new PaymentIntentService();
        var paymentIntent = await paymentIntentService.CreateAsync(paymentIntentOptions);

        _logger.LogDebug($"PaymentIntent {paymentIntent.Id} created for Stripe Customer {paymentIntent.CustomerId}, Amount {paymentIntent.Amount} (in {paymentIntent.Currency} cents).");

        return paymentIntent;
    }

    private async Task<SetupIntent> CreateSetupIntent(string customerId)
    {
        var setupIntentOptions = new SetupIntentCreateOptions
        {
            Customer = customerId
        };
        var setupIntentService = new SetupIntentService();
        var setupIntent = await setupIntentService.CreateAsync(setupIntentOptions);

        _logger.LogDebug($"SetupIntent {setupIntent.Id} created for Stripe Customer {setupIntent.CustomerId}.");

        return setupIntent;
    }

    private async Task<EphemeralKey> CreateEphemeralKey(string customerId)
    {
        var ephemeralKeyOptions = new EphemeralKeyCreateOptions
        {
            Customer = customerId,
            StripeVersion = _config["STRIPE_VERSION"]
        };
        var ephemeralKeyService = new EphemeralKeyService();
        var ephemeralKey = await ephemeralKeyService.CreateAsync(ephemeralKeyOptions);

        _logger.LogDebug($"CustomerEphemeralKey {ephemeralKey.Id} created for Stripe Customer {customerId}.");

        return ephemeralKey;
    }

    public async Task<bool> CreateAndConfirmPaymentIntent(string customerId, long amount, string paymentMethodId)
    {
        var paymentIntentService = new PaymentIntentService();
        var paymentIntent = await CreatePaymentIntent(customerId, amount);

        var options = new PaymentIntentConfirmOptions
        {
            PaymentMethod = paymentMethodId,
            ReturnUrl = "https://www.spriggy.com.au",
        };

        try
        {
            paymentIntent = await paymentIntentService.ConfirmAsync(paymentIntent.Id, options);
        }
        catch (StripeException ex)
        {
            string customErrorMessage = String.Empty;

            if (ex.StripeError?.Code == "card_declined")
            {
                switch (ex.StripeError?.DeclineCode)
                {
                    case "card_velocity_exceeded":
                        customErrorMessage = "Your card was declined for making repeated attempts too frequently or exceeding its amount limit. If the issue persists, consider contacting your bank for assistance.";
                        break;
                    case "insufficient_funds":
                        customErrorMessage = "Your card has insufficient funds. Please add funds or use a different payment method.";
                        break;
                    default:
                        customErrorMessage = "Your card was declined. Please contact your bank for assistance.";
                        break;
                }
            }
            else if (ex.StripeError?.Code == "expired_card")
            {
                customErrorMessage = "Your card has expired. Please update your payment information to continue.";
            }

            if (!String.IsNullOrEmpty(customErrorMessage))
            {
                throw new TopUpException(customErrorMessage);
            }

            throw ex;
        }

        return paymentIntent.Status == "succeeded";
    }

    /// <summary>
    /// Process a guest payment directly with card details (no customer creation)
    /// For testing, this uses Stripe test tokens. For production, this should be integrated with Stripe Elements.
    /// </summary>
    public async Task<(bool Success, string TransactionId, string ErrorMessage)> ProcessGuestPayment(
        string cardNumber, int expiryMonth, int expiryYear, string cvv, string cardholderName, long amountInCents)
    {
        try
        {
            _logger.LogInformation("Processing guest payment for amount {Amount} cents", amountInCents);

            // For testing purposes, use Stripe test tokens based on card number
            string sourceToken = GetStripeTestToken(cardNumber);

            // Create PaymentIntent with the test token
            var paymentIntentOptions = new PaymentIntentCreateOptions
            {
                Amount = amountInCents,
                Currency = DEFAULT_CURRENCY,
                PaymentMethodData = new PaymentIntentPaymentMethodDataOptions
                {
                    Type = "card",
                    Card = new PaymentIntentPaymentMethodDataCardOptions
                    {
                        Token = sourceToken
                    }
                },
                ConfirmationMethod = "manual",
                Confirm = true,
                Description = "Guest payment - POS transaction"
            };

            var paymentIntentService = new PaymentIntentService();
            var paymentIntent = await paymentIntentService.CreateAsync(paymentIntentOptions);

            _logger.LogInformation("Guest PaymentIntent {PaymentIntentId} created with status {Status}",
                paymentIntent.Id, paymentIntent.Status);

            if (paymentIntent.Status == "succeeded")
            {
                _logger.LogInformation("Guest payment successful. Transaction ID: {TransactionId}", paymentIntent.Id);
                return (true, paymentIntent.Id, null);
            }
            else if (paymentIntent.Status == "requires_action")
            {
                // Handle 3D Secure or other authentication requirements
                _logger.LogWarning("Guest payment requires additional authentication: {PaymentIntentId}", paymentIntent.Id);
                return (false, paymentIntent.Id, "Payment requires additional authentication");
            }
            else
            {
                _logger.LogWarning("Guest payment failed with status: {Status}", paymentIntent.Status);
                return (false, paymentIntent.Id, $"Payment failed with status: {paymentIntent.Status}");
            }
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error during guest payment processing");

            string customErrorMessage = GetUserFriendlyErrorMessage(ex);
            return (false, null, customErrorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during guest payment processing");
            return (false, null, "An unexpected error occurred while processing the payment");
        }
    }

    /// <summary>
    /// Get Stripe test token for common test card numbers
    /// In production, this would be replaced with Stripe Elements integration
    /// </summary>
    private string GetStripeTestToken(string cardNumber)
    {
        // Remove spaces and dashes from card number
        var cleanCardNumber = cardNumber?.Replace(" ", "").Replace("-", "");

        // Return Stripe test tokens for common test cards
        // These are predefined tokens provided by Stripe for testing
        return cleanCardNumber switch
        {
            "****************" => "tok_visa", // Visa test card
            "****************" => "tok_visa_debit", // Visa debit test card
            "****************" => "tok_mastercard", // Mastercard test card
            "2223003122003222" => "tok_mastercard", // Mastercard 2-series test card
            "****************" => "tok_mastercard_debit", // Mastercard debit test card
            "****************" => "tok_chargeDeclined", // Card declined test card
            "****************" => "tok_chargeDeclinedInsufficientFunds", // Insufficient funds test card
            "****************" => "tok_chargeDeclinedLostCard", // Lost card test card
            "****************" => "tok_chargeDeclinedStolenCard", // Stolen card test card
            "****************" => "tok_chargeDeclinedExpiredCard", // Expired card test card
            "****************" => "tok_chargeDeclinedIncorrectCvc", // Incorrect CVC test card
            _ => "tok_visa" // Default to Visa test token for any other card
        };
    }

    private string GetUserFriendlyErrorMessage(StripeException ex)
    {
        if (ex.StripeError?.Code == "card_declined")
        {
            return ex.StripeError?.DeclineCode switch
            {
                "card_velocity_exceeded" => "Your card was declined for making repeated attempts too frequently or exceeding its amount limit. If the issue persists, consider contacting your bank for assistance.",
                "insufficient_funds" => "Your card has insufficient funds. Please add funds or use a different payment method.",
                "expired_card" => "Your card has expired. Please update your payment information to continue.",
                "incorrect_cvc" => "The security code (CVV) you entered is incorrect. Please check and try again.",
                "processing_error" => "An error occurred while processing your card. Please try again or use a different payment method.",
                _ => "Your card was declined. Please contact your bank for assistance."
            };
        }
        else if (ex.StripeError?.Code == "expired_card")
        {
            return "Your card has expired. Please update your payment information to continue.";
        }
        else if (ex.StripeError?.Code == "incorrect_cvc")
        {
            return "The security code (CVV) you entered is incorrect. Please check and try again.";
        }
        else if (ex.StripeError?.Code == "invalid_expiry_month" || ex.StripeError?.Code == "invalid_expiry_year")
        {
            return "The expiry date you entered is invalid. Please check and try again.";
        }
        else if (ex.StripeError?.Code == "invalid_number")
        {
            return "The card number you entered is invalid. Please check and try again.";
        }

        return "An error occurred while processing your payment. Please try again or contact support.";
    }

    public async Task<TokenResponse> GetToken(string customerId, long amount)
    {
        ArgumentNullException.ThrowIfNull(customerId, "customerId");
        if (amount < 0)
        {
            throw new ArgumentException("Amount provided cannot be less than zero.");
        }

        var ephemeralKey = await CreateEphemeralKey(customerId);
        var response = new TokenResponse()
        {
            CustomerToken = ephemeralKey.Secret,
            CustomerId = customerId
        };

        if (amount == 0)
        {
            var setupIntent = await CreateSetupIntent(customerId);
            response.token = setupIntent.ClientSecret;
        }
        else
        {
            var paymentIntent = await CreatePaymentIntent(customerId, amount);
            response.token = paymentIntent.ClientSecret;
        }

        return response;
    }

    public async Task<List<PaymentMethod>> GetPaymentMethods(string customerId)
    {
        ArgumentNullException.ThrowIfNull(customerId, "customerId");

        var service = new CustomerService();
        var result = await service.ListPaymentMethodsAsync(customerId);

        List<PaymentMethod> paymentMethods = new();

        if (result != null && result.Data != null && result.Data.Any())
        {
            paymentMethods = result.Data.Select(x => new PaymentMethod
            {
                id = x.Id,
                type = x.Card != null ? "card" : null, // currently we support only card as payment method
                card = x.Card != null ?
                                new Card()
                                {
                                    last_four = x.Card.Last4,
                                    card_type = x.Card.Brand,
                                    expiry_month = x.Card.ExpMonth.ToString(),
                                    expiry_year = x.Card.ExpYear.ToString()
                                }
                                : null
            }).ToList();
        }

        _logger.LogDebug($"Returned {paymentMethods.Count} payment methods for Stripe Customer {customerId} => {string.Join(", ", paymentMethods.Select(p => p.id).ToArray())}.");

        return paymentMethods;
    }

    public async Task<bool> DeletePaymentMethod(string customerId, string paymentMethodId)
    {
        ArgumentNullException.ThrowIfNull(customerId, "customerId");
        ArgumentNullException.ThrowIfNull(paymentMethodId, "paymentMethodId");

        var existingPaymentMethods = await GetPaymentMethods(customerId);
        var message = string.Empty;

        if (!existingPaymentMethods.Any())
        {
            message = $"Customer {customerId} does not have any attached payment methods.";
            _logger.LogDebug(message);
            throw new StripeException(message);
        }

        var exists = existingPaymentMethods.Select(x => x.id).Any(i => i == paymentMethodId);

        if (!exists)
        {
            message = $"Customer {customerId} does not have Stripe payment method id {paymentMethodId} attached.";
            _logger.LogDebug(message);
            throw new StripeException(message);
        }

        var service = new PaymentMethodService();
        var paymentMethod = await service.DetachAsync(paymentMethodId);

        var paymentMethodIdDetached = paymentMethod?.Id;

        _logger.LogDebug($"Detached payment method {paymentMethodIdDetached} for Stripe Customer {customerId}.");

        return paymentMethodIdDetached != null;
    }

    public async Task ProcessEvent(string json, string signature)
    {
        if (json == null)
        {
            var message = $"Received unprocessable {json} Stripe Event JSON";
            throw new StripeException(message);
        }

        Regex regexNewLine = new Regex("(\r\n|\r|\n)");
        var jsonString = regexNewLine.Replace(json, "");
        _telemetryService.TrackTrace($"Received Stripe Event JSON: {jsonString}");
        _logger.LogDebug($"Received Stripe Event JSON: {jsonString}");

        var endpoint = await _sendEndpointProvider.GetSendEndpoint(new Uri($"queue:{_config["environment"]}-{MessagingConstants.StripeTopupEventsQueue}"));
        await endpoint.Send(new StripeTopupEvent
        {
            Json = json,
            Signature = signature
        });
    }

    public async Task ProcessEventWithCallback(string json, string signature, Func<string, decimal, Task> paymentIntentSuccessCallback)
    {
        //var stripeEvent = EventUtility.ParseEvent(json);

        // Signature + Webhook Secret provided during construction verifies that the events received are indeed from Stripe.
        var stripeEvent = EventUtility.ConstructEvent(json, signature, _config["stripeWebhookSecret"]);

        if (stripeEvent.Type == Events.PaymentIntentSucceeded)
        {
            var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
            _telemetryService.TrackTrace($"Received Stripe Event {stripeEvent.Id}: PaymentIntent {paymentIntent.Id} with Status={paymentIntent.Status} received for Stripe Customer {paymentIntent.CustomerId} with PaymentMethod {paymentIntent.PaymentMethod}, Amount {paymentIntent.Amount} (in {paymentIntent.Currency} cents).");
            _logger.LogDebug($"Received Stripe Event {stripeEvent.Id}: PaymentIntent {paymentIntent.Id} with Status={paymentIntent.Status} received for Stripe Customer {paymentIntent.CustomerId} with PaymentMethod {paymentIntent.PaymentMethod}, Amount {paymentIntent.Amount} (in {paymentIntent.Currency} cents).");

            try
            {
                await paymentIntentSuccessCallback(paymentIntent.CustomerId, paymentIntent.Amount);

                _telemetryService.TrackTrace($"Stripe Event {stripeEvent.Id} PaymentIntent {paymentIntent.Id} with Status={paymentIntent.Status} processed successfully.");
                _logger.LogDebug($"Stripe Event {stripeEvent.Id} PaymentIntent {paymentIntent.Id} with Status={paymentIntent.Status} processed successfully.");
            }
            catch (Exception e)
            {
                var message = $"Failed to process Stripe Event {stripeEvent.Id} PaymentIntent {paymentIntent.Id} with Status={paymentIntent.Status}: {e.Message}";

                throw new StripeException(message);
            }
        }
        else if (stripeEvent.Type == Events.PaymentMethodAttached)
        {
            var paymentMethod = stripeEvent.Data.Object as Stripe.PaymentMethod;
            _telemetryService.TrackTrace($"Received Stripe Event {stripeEvent.Id}: Payment method {paymentMethod.Id} attached to Stripe Customer {paymentMethod.CustomerId}.");
            _logger.LogDebug($"Received Stripe Event {stripeEvent.Id}: Payment method {paymentMethod.Id} attached to Stripe Customer {paymentMethod.CustomerId}.");
        }
        else
        {
            _telemetryService.TrackTrace($"Received Stripe Event {stripeEvent.Id}: Unhandled Stripe Event is of type {stripeEvent.Type}");
            _logger.LogDebug($"Received Stripe Event {stripeEvent.Id}: Unhandled Stripe Event is of type {stripeEvent.Type}");
        }
    }

    #region Test methods for Stripe Integration - should be removed later

    public async Task<Stripe.PaymentMethod> AddPaymentMethod()
    {
        var options = new PaymentMethodCreateOptions
        {
            Type = "card",
            Card = new PaymentMethodCardOptions
            {
                Number = "****************",
                ExpMonth = 8,
                ExpYear = 2026,
                Cvc = "314",
            },
            // You cannot attach a PaymentMethod to a Customer during PaymentMethod creation
        };
        var service = new PaymentMethodService();
        var paymentMethod = await service.CreateAsync(options);
        return paymentMethod;
    }

    public async Task<Stripe.PaymentMethod> AttachPaymentMethod(string customerId, string paymentMethodId)
    {
        var options = new PaymentMethodAttachOptions
        {
            Customer = customerId
        };
        var service = new PaymentMethodService();
        var paymentMethod = await service.AttachAsync(paymentMethodId, options);
        return paymentMethod;
    }

    public async Task<Stripe.PaymentMethod> DeletePaymentMethod(string paymentMethodId)
    {
        var service = new PaymentMethodService();
        var paymentMethod = await service.DetachAsync(paymentMethodId);
        return paymentMethod;
    }

    #endregion
}
