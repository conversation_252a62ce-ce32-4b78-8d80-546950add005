{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Datadog.Trace.MSBuild/3.3.1": {"dependencies": {"Datadog.Trace": "3.3.1", "Microsoft.Build.Framework": "14.3.0", "StyleCop.Analyzers": "1.2.0-beta.556"}, "runtime": {"Datadog.Trace.MSBuild.dll": {}}}, "Microsoft.Build.Framework/14.3.0": {"dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}}, "Microsoft.NETCore.Platforms/1.0.1": {}, "Microsoft.NETCore.Targets/1.0.1": {}, "StyleCop.Analyzers/1.2.0-beta.556": {"dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.556"}}, "StyleCop.Analyzers.Unstable/1.2.0.556": {}, "System.Collections/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Diagnostics.DiagnosticSource/4.4.1": {}, "System.IO/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.Reflection/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "System.Runtime.Handles/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Text.Encoding/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Threading.Tasks/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "Datadog.Trace/3.3.1": {"dependencies": {"System.Diagnostics.DiagnosticSource": "4.4.1"}, "runtime": {"Datadog.Trace.dll": {}}}}}, "libraries": {"Datadog.Trace.MSBuild/3.3.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Build.Framework/14.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GX3MdQMQ3YVx/jWerzd5vDW/VrcLxf80Ts4T9AVEUsrmL6wZK1YTGNX3jLapll+5Y7PBDbvf5R815A64hlEQ+g==", "path": "microsoft.build.framework/14.3.0", "hashPath": "microsoft.build.framework.14.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ==", "path": "microsoft.netcore.platforms/1.0.1", "hashPath": "microsoft.netcore.platforms.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw==", "path": "microsoft.netcore.targets/1.0.1", "hashPath": "microsoft.netcore.targets.1.0.1.nupkg.sha512"}, "StyleCop.Analyzers/1.2.0-beta.556": {"type": "package", "serviceable": true, "sha512": "sha512-llRPgmA1fhC0I0QyFLEcjvtM2239QzKr/tcnbsjArLMJxJlu0AA5G7Fft0OI30pHF3MW63Gf4aSSsjc5m82J1Q==", "path": "stylecop.analyzers/1.2.0-beta.556", "hashPath": "stylecop.analyzers.1.2.0-beta.556.nupkg.sha512"}, "StyleCop.Analyzers.Unstable/1.2.0.556": {"type": "package", "serviceable": true, "sha512": "sha512-zvn9Mqs/ox/83cpYPignI8hJEM2A93s2HkHs8HYMOAQW0PkampyoErAiIyKxgTLqbbad29HX/shv/6LGSjPJNQ==", "path": "stylecop.analyzers.unstable/1.2.0.556", "hashPath": "stylecop.analyzers.unstable.1.2.0.556.nupkg.sha512"}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "path": "system.collections/4.0.11", "hashPath": "system.collections.4.0.11.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-U/KcC19fyLsPN1GLmeU2zQq15MMVcPwMOYPADVo1+WIoJpvMHxrzvl+BLLZwTEZSneGwaPFZ0aWr0nJ7B7LSdA==", "path": "system.diagnostics.diagnosticsource/4.4.1", "hashPath": "system.diagnostics.diagnosticsource.4.4.1.nupkg.sha512"}, "System.IO/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "path": "system.io/4.1.0", "hashPath": "system.io.4.1.0.nupkg.sha512"}, "System.Reflection/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "path": "system.reflection/4.1.0", "hashPath": "system.reflection.4.1.0.nupkg.sha512"}, "System.Reflection.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "path": "system.reflection.primitives/4.0.1", "hashPath": "system.reflection.primitives.4.0.1.nupkg.sha512"}, "System.Runtime/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "path": "system.runtime/4.1.0", "hashPath": "system.runtime.4.1.0.nupkg.sha512"}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "path": "system.runtime.handles/4.0.1", "hashPath": "system.runtime.handles.4.0.1.nupkg.sha512"}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "path": "system.runtime.interopservices/4.1.0", "hashPath": "system.runtime.interopservices.4.1.0.nupkg.sha512"}, "System.Text.Encoding/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "path": "system.text.encoding/4.0.11", "hashPath": "system.text.encoding.4.0.11.nupkg.sha512"}, "System.Threading.Tasks/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "path": "system.threading.tasks/4.0.11", "hashPath": "system.threading.tasks.4.0.11.nupkg.sha512"}, "Datadog.Trace/3.3.1": {"type": "project", "serviceable": false, "sha512": ""}}}