using System;
using System.ComponentModel.DataAnnotations;

using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;
namespace Schools.BLL.Classes.Notices
{
    public class UpdateNoticeRequest
    {
        [JsonProperty(PropertyName = "NoticeId")]
        [Required]
        [Range(1, long.MaxValue)]
        public long NoticeId { get; set; }

        [JsonProperty(PropertyName = "Title")]
        [MaxLength(50)]
        public string Title { get; set; }

        [JsonProperty(PropertyName = "Description")]
        [MaxLength(1000)]
        public string Description { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool? IsActive { get; set; }

        [JsonProperty(PropertyName = "EndDate")]
        public DateTime? EndDate { get; set; }

        [JsonProperty(PropertyName = "NoticeType")]
        public NoticeTypes? NoticeType { get; set; }
    }
}
