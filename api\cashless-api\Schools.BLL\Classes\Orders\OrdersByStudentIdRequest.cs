﻿using System;
using System.Collections.Generic;
using System.Text;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Orders
{
    public class OrdersByStudentIdRequest
    {
        public bool StudentId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class OrderByStudentOrderDateAndMenuTypeRequest
    {
        public int StudentId { get; set; }
        public string OrderDate { get; set; }
        public string MenuType { get; set; }
    }

    public class OrderByStudentOrderDateAndMenuTypeResponse
    {
        public Order[] Order { get; set; }
        public bool IsSchoolClosed { get; set; }
    }
}
