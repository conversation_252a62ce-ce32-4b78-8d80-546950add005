using System;

namespace Schools.BLL.Classes
{
    public class SchoolEventUpdateRequest
    {
        public int SchoolEventId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }
        public bool? WaiveEventOrderFee { get; set; }

        public DateTime EventDate { get; set; }

        public DateTime CutOffDate { get; set; }

        public string SpecificClasses { get; set; }

        public int? WeeksPreOrder { get; set; }

        public bool IsActive { get; set; }
        public long? EventTemplateId { get; set; }
    }
}
