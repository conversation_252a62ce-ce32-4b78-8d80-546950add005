﻿using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes.Orders;
using Schools.DAL.Entities;
using Microsoft.Azure.Amqp.Framing;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Assemblers;

public static class OrderStatusAssembler
{
    public static OrderStatusDto Convert_OrderStatusEntity_To_OrderStatusDto(OrderStatusEntity request)
    {
        OrderStatusDto dto = new()
        {
            OrderStatusId = request.OrderStatusId,
            OrderId = request.OrderId,
            ActionType = request.ActionType,
            PaymentStatus = request.PaymentStatus,
            StockStatus = request.StockStatus,
            NotificationStatus = request.NotificationStatus,
            TriggeredBy = request.TriggeredBy,
            CorrelationId = request.CorrelationId,
            DateCreatedUtc = request.DateCreatedUtc,
            DateModifiedUtc = request.DateModifiedUtc
        };

        return dto;
    }

    public static List<OrderStatusDto> Convert_ListOrderStatusEntity_To_ListOrderStatusDto(IEnumerable<OrderStatusEntity> request)
    {
        List<OrderStatusDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(Convert_OrderStatusEntity_To_OrderStatusDto(e));
            }
        }

        return dto;
    }

    public static IEnumerable<OrderWithErrorDto> Convert_ListOrderWithErrorSearch_To_ListOrderWithErrorDto(IEnumerable<OrderWithErrorSearch> request)
    {
        var res = new List<OrderWithErrorDto>();

        foreach (var r in request)
        {
            var existingInListIndex = res.FindIndex(s => s.OrderId == r.OrderId);

            if (existingInListIndex >= 0)
            {
                res[existingInListIndex].Errors.Add(Convert_OrderWithErrorSearch_To_OrderErrorDto(r));
            }
            else
            {
                res.Add(Convert_OrderWithErrorSearch_To_OrderWithErrorDto(r));
            }
        }

        return res;
    }

    public static OrderWithErrorDto Convert_OrderWithErrorSearch_To_OrderWithErrorDto(OrderWithErrorSearch request)
    {
        var dto = new OrderWithErrorDto
        {
            School = request.School,
            ParentName = request.ParentName,
            OrderStatusId = request.OrderStatusId,
            OrderId = request.OrderId,
            OrderDate = request.OrderDate,
            SchoolId = request.SchoolId,
            ParentId = request.UserId,
            Action = request.ActionType.ToString(),
            Menu = request.Menu,
            DateCreatedUtc = request.DateCreatedUtc,
            CutOffTime = request.CutOffTime,
            Errors = new List<OrderErrorDto>()
        };

        dto.Errors.Add(Convert_OrderWithErrorSearch_To_OrderErrorDto(request));

        return dto;
    }

    public static OrderErrorDto Convert_OrderWithErrorSearch_To_OrderErrorDto(OrderWithErrorSearch request)
    {
        var dto = new OrderErrorDto
        {
            ErrorId = request.OrderErrorId,
            ErrorMessage = request.Message,
            ErrorType = request.Type.ToString()
        };

        return dto;
    }

    public static OrderDashboardDto Convert_OrderWithErrorSearch_To_OrderDashboardDto(IEnumerable<OrderWithErrorDto> request)
    {
        var dto = new OrderDashboardDto
        {
            Orders = request,
            OrderDatesFilter = new(),
            SchoolsFilter = new()
        };

        foreach (var r in request)
        {
            // date filter
            var dateIndex = dto.OrderDatesFilter.FindIndex(d => d.Id == r.OrderDate.DayOfYear);

            if (dateIndex > -1)
            {
                dto.OrderDatesFilter[dateIndex].AdditionalNumber++;
            }
            else
            {
                var itemList = new SelectListDataDto
                {
                    Id = r.OrderDate.DayOfYear,
                    Label = r.OrderDate.Date.ToString(),
                    AdditionalNumber = 1
                };

                dto.OrderDatesFilter.Add(itemList);
            }

            // school Filter
            var schoolIndex = dto.SchoolsFilter.FindIndex(d => d.Id == r.SchoolId);

            if (schoolIndex > -1)
            {
                dto.SchoolsFilter[schoolIndex].AdditionalNumber++;
            }
            else
            {
                var itemList = new SelectListDataDto
                {
                    Id = r.SchoolId,
                    Label = r.School,
                    AdditionalNumber = 1
                };

                dto.SchoolsFilter.Add(itemList);
            }
        }

        return dto;
    }
}