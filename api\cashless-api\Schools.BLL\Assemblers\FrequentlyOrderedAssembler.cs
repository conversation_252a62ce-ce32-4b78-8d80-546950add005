using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Microsoft.IdentityModel.Tokens;
using Schools.BLL.Classes.FrequentlyOrdered;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public class FrequentlyOrderedAssembler
{
    public FrequentlyOrderedDto Convert_FrequentlyOrderedItemEntity_To_FrequentlyOrderedDto(FrequentlyOrderedItemEntity entity)
    {
        FrequentlyOrderedDto dto = new()
        {
            MenuItemId = entity.MenuItemId,
            SelectedOptions = string.IsNullOrEmpty(entity.SelectedOptions) ? new List<long>() : entity.SelectedOptions.Split(",").Where(s => !s.IsNullOrEmpty()).Select(i => Int64.Parse(i)).ToList()
        };

        return dto;
    }

    public List<FrequentlyOrderedDto> Convert_ListFrequentlyOrderedItemEntity_To_ListFrequentlyOrderedDto(List<FrequentlyOrderedItemEntity> entities)
    {
        List<FrequentlyOrderedDto> dto = new();

        if (entities != null && entities.Count > 0)
        {
            entities.ForEach(e =>
            {
                dto.Add(Convert_FrequentlyOrderedItemEntity_To_FrequentlyOrderedDto(e));
            });
        }

        return dto;
    }
}
