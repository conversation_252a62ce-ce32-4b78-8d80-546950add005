using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders;
using Schools.BLL.Exceptions;
using Schools.BLL.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderController : ControllerBase
    {
        public static string NewOrderProcessSchoolIds { get; set; }
        private static HashSet<long> _orderProcessSchoolIds { get; set; }
        private readonly ITelemetryService _telemetryService;
        private readonly IUserService _userService;
        private readonly IOrderService _orderService;
        private readonly ISchoolService _schoolService;
        private readonly IFeeApiSerice _feeApiSerice;
        private readonly ICanteenService _canteenService;
        private readonly IPaymentService2 _paymentService2;
        private readonly INotificationService _notificationService;
        private readonly IStocksService _stockService;
        private readonly ILogicAppService _logicAppService;
        private readonly IMenuService _menuService;
        private readonly IOrderRepository _orderRepository;
        private readonly IOrderValidator _orderValidator;

        public OrderController(ITelemetryService telemetryService, IUserService userService,
                                IOrderService orderService, ISchoolService schoolService,
                                IFeeApiSerice feeApiSerice, ICanteenService canteenService, IPaymentService2 paymentService2,
                                INotificationService notificationService, IStocksService stockService, ILogicAppService logicAppService, IOrderRepository orderRepository,
                                IOrderValidator orderValidator, IMenuService menuService
                                )
        {
            _telemetryService = telemetryService;
            _userService = userService;
            _orderService = orderService;
            _schoolService = schoolService;
            _logicAppService = logicAppService;
            _feeApiSerice = feeApiSerice;
            _canteenService = canteenService;
            _paymentService2 = paymentService2;
            _notificationService = notificationService;
            _stockService = stockService;
            _orderRepository = orderRepository;
            _orderValidator = orderValidator;
            _menuService = menuService;

            if (_orderProcessSchoolIds == null)
            {
                _orderProcessSchoolIds = new HashSet<long>();
                if (!string.IsNullOrEmpty(NewOrderProcessSchoolIds))
                {
                    var orderProcessIds = NewOrderProcessSchoolIds.Split(',');
                    foreach (var id in orderProcessIds)
                    {
                        var isValidSchoolId = long.TryParse(id, out long orderProccessSchoolId);
                        if (isValidSchoolId)
                        {
                            _orderProcessSchoolIds.Add(orderProccessSchoolId);
                        }
                    }
                }
            }
        }

        [Route("{orderId}/pickedUp/{pickedUp}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> OrderPickedUp(int orderId, bool pickedUp)
        {
            await _orderValidator.ValidateAccessToOrder(orderId);
            string utcDate = await _orderService.UpdatePickedUpStatus(orderId, pickedUp);

            return new OkObjectResult(utcDate);
        }

        /// <summary>
        /// Get all orders for a given school.
        /// </summary>
        /// <returns></returns>
        [Route("GetOrderListBySchoolandMerchant")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetOrderListBySchoolandMerchant([FromBody] OrderRequest request)
        {
            _orderValidator.ValidateRequest(request);

            ListOrders listOrders = new();

            listOrders.Orders = await _orderService.GetOrderListBySchool(request);

            await _orderValidator.ValidateAccessToOrders(listOrders.Orders);
            return new OkObjectResult(listOrders);
        }

        [Route("GetDashboardOrders/{date}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetDashboardOrders(string date)
        {
            if (String.IsNullOrEmpty(date))
            {
                throw new ValidationException("Invalid request");
            }

            // get orders
            var res = await _orderService.GetTotalOrdersPerSchool(date);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Save the orders to print
        /// </summary>
        [Route("SetOrdersToPrint")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> SetOrdersToPrint([FromBody] OrderRequest request)
        {
            _orderValidator.ValidateRequest(request);
            await _orderValidator.ValidateAccessToCanteen(request.MerchantId);

            var guid = Guid.NewGuid().ToString();

            await _orderService.SetOrdersToPrint(request, guid);

            var response = new PrintGuid
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        [Route("SetOrderToPrintByOrderId/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> SetOrderToPrintByOrderId(int orderId)
        {
            await _orderValidator.ValidateAccessToOrder(orderId);

            var guid = Guid.NewGuid().ToString();

            await _orderService.SetOrderToPrintByOrderId(orderId, guid);

            var response = new PrintGuid
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        [Route("AdminFixUsersBalanceAfterPaymentOutage")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [Obsolete("No longer used in admin portal")]
        public ActionResult FixUsersBalanceAfterPaymentOutage()
        {
            throw new DeprecatedApiException();
        }

        [Route("AdminCopyOrderItems")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [Obsolete("No longer used in admin portal")]
        public ActionResult AdminCopyOrderItems()
        {
            throw new DeprecatedApiException();
        }

        [Route("GetOrdersToPrintByGuid/{guid}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetOrdersToPrintByGuid(string guid)
        {
            if (string.IsNullOrEmpty(guid))
            {
                throw new ValidationException("Invalid request");
            }

            // get schools by printing guid
            var school = await _schoolService.GetSchoolByPrintingGuid(guid);

            // get orders based on classname
            var orders = await _orderService.GetOrdersToPrintByGuidByClassName(guid, school.PrintSortBy, school.PrintSortDirection);

            await _orderValidator.ValidateAccessToOrders(orders);

            LabelFormat res = new LabelFormat
            {
                Labels = new List<Label>()
            };

            // assign label values
            if (orders != null && orders.Count > 0)
            {
                res.LabelName = school.LabelFormat;
                res.LabelPrintChoice = school.LabelPrintChoice;

                orders.ForEach(o =>
                {
                    if (school.LabelPrintChoice == "Item")
                    {
                        // print only 1 item per label
                        var totalItems = o.TotalItems;
                        var counter = 1;

                        o.Items.ForEach(i =>
                        {

                            var label = new Label
                            {
                                OrderId = Convert.ToInt32(o.OrderId),
                                ItemId = i.MenuItemId,
                                TitleLeft = o.StudentName + " - " + o.MenuType,
                                TitleRight = o.ClassName,
                                SubTitle = "Date: " + Convert.ToDateTime(o.OrderDate).ToString("dd-MM-yy"),
                                LabelNumber = counter.ToString() + "/" + totalItems.ToString(),
                                Items = new List<string>()
                            };

                            string item = "- " + i.Quantity + " x " + i.Name + label.GetItemOptions(i);
                            label.Items.Add(item);

                            res.Labels.Add(label);
                            counter++;
                        });
                    }
                    else
                    {
                        // Print the complete order on 1 label
                        var label = new Label
                        {
                            OrderId = Convert.ToInt32(o.OrderId),
                            ItemId = null,
                            TitleLeft = o.StudentName + " - " + o.MenuType,
                            TitleRight = o.ClassName,
                            SubTitle = "Date: " + Convert.ToDateTime(o.OrderDate).ToString("dd-MM-yy"),
                            LabelNumber = "1/1",
                            Items = new List<string>()
                        };

                        o.Items.ForEach(i =>
                        {
                            string item = "- " + i.Quantity + " x " + i.Name + label.GetItemOptions(i);
                            label.Items.Add(item);
                        });

                        res.Labels.Add(label);
                    }
                });
            }

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get all orders for a given school.
        /// </summary>
        /// <returns></returns>
        [Route("SetOrdersHasPrinted")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> SetOrdersHasPrinted([FromBody] SetPrintedRequest request)
        {
            _orderValidator.ValidateRequest(request);

            await _orderValidator.ValidateAccessToOrders(request.Orders);

            await _orderService.SetOrdersToPrinted(request.Orders.Split(',').Select(o => long.Parse(o)).ToList());

            return new OkResult();
        }

        /// <summary>
        /// This is used by the cashless-process-order logic app.
        ///
        /// Needs anonymous access, but we will check if the request
        /// contains the required cashless-api-secret header.
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("SaveItemsOrder/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SaveItemsOrder(int orderId)
        {
            // get orders
            await _orderService.SaveOrderItems(orderId);

            return new OkResult();
        }

        // /// <summary>
        // /// Get report for the canteen's orders group by Class
        // /// </summary>
        // [Route("GetReportClassOrdersBySchool")]
        // [HttpPost]
        // [ProducesResponseType(200)]
        // [ProducesResponseType(400)]
        // [ProducesResponseType(500)]
        // [CheckUserRole(UserRole.Merchant)]
        // public async Task<IActionResult> GetReportClassOrdersBySchoolV2([FromBody] ReportRequest request)
        // {
        //     await CommonValidateRquest(request, false);

        //     ListReportClass report = new();
        //     report.Report = await _orderRepository.GetReportClassOrdersBySchoolV2(request);

        //     return new OkObjectResult(report);
        // }

        [Route("GetReportClassOrdersBySchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportClassOrdersBySchoolV4([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request, false);

            var res = await _orderService.GetReportClassOrdersBySchoolV4(request);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get report for Hybrid class/item list
        /// </summary>
        [Route("GetReportClassItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportClassItem([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request, false);

            List<ReportHybridSchool> report = new();
            report = await _orderService.GetReportHybridClassItemList(request);

            return new OkObjectResult(report);
        }

        // [Route("GetReportClassOrdersBySchoolEvent")]
        // [HttpPost]
        // [ProducesResponseType(200)]
        // [ProducesResponseType(400)]
        // [ProducesResponseType(500)]
        // [CheckUserRole(UserRole.Merchant)]
        // public async Task<IActionResult> GetReportClassOrdersBySchoolEvent([FromBody] ReportRequest request)
        // {
        //     await CommonValidateRquest(request);

        //     ListReportClass reportClassResponse = new ListReportClass
        //     {
        //         Report = await _orderRepository.GetReportClassOrdersBySchoolV2(request)
        //     };


        //     return new OkObjectResult(reportClassResponse);
        // }

        #region GetReportOrdersUnprintedBySchool

        /// <summary>
        /// Get a report of all labels that haven't been printed for the given day.
        /// </summary>
        [Route("GetReportOrdersUnprintedBySchool-V1")]
        [Route("GetReportOrdersUnprintedBySchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportOrdersUnprintedBySchool([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request);

            ListReportClass reportClassResponse = new()
            {
                Report = await _orderRepository.GetUnprintedOrdersBySchoolV3(request)
            };

            return new OkObjectResult(reportClassResponse);
        }

        /// <summary>
        /// Get a report of all labels that haven't been printed for the given day.
        /// </summary>
        [Route("GetReportOrdersUnprintedBySchool-V4")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportOrdersUnprintedBySchoolV4([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request);

            var res = await _orderService.GetUnprintedOrdersBySchool(request);

            return new OkObjectResult(res);
        }

        #endregion

        # region GetReportOrdersPrintedCancelledBySchool

        [Route("GetReportOrdersPrintedCancelledBySchool-V1")]
        [Route("GetReportOrdersPrintedCancelledBySchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportOrdersPrintedCancelledBySchool([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request);

            ListReportClass reportClassResponse = new()
            {
                Report = await _orderRepository.GetCancelledPrintedOrdersBySchoolV3(request)
            };

            return new OkObjectResult(reportClassResponse);
        }

        [Route("GetReportOrdersPrintedCancelledBySchool-V4")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportOrdersPrintedCancelledBySchoolV4([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request);

            var res = await _orderService.GetCancelledPrintedOrdersBySchool(request);

            return new OkObjectResult(res);
        }

        #endregion

        /// <summary>
        /// Get report for canteen's orders group by MenuType
        /// </summary>
        [Route("GetReportOrdersByMenuBySchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportOrdersByMenuBySchool([FromBody] ReportRequest request)
        {
            await CommonValidateRquest(request, false, true);

            var report = await _orderService.GetReportOrdersByMenuBySchool(request);

            return new OkObjectResult(report);
        }

        [Route("GetReportSales")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetReportSales([FromBody] ReportRequest request)
        {
            _orderValidator.ValidateRequest(request);
            await _orderValidator.ValidateAccessToSchools(request.SchoolsIds);
            var report = await _orderService.GetReportSalesV3(request);

            return new OkObjectResult(report);
        }

        /// <summary>
        /// Angular only: Get all orders for a given parent.
        /// </summary>
        [Route("GetOrderListByParent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetOrderListByParent([FromBody] OrderHistoryUserRequest request)
        {
            _orderValidator.ValidateRequest(request);

            ListOrders listOrders = new();

            var orders = await _orderService.GetOrdersByParent(new SearchByUser { EndOrderDate = DateTime.Parse(request.EndOrderDate), IncludeError = false, UserId = request.UserId, StartOrderDate = DateTime.Parse(request.StartOrderDate) });
            listOrders.Orders = orders.ToList();

            return new OkObjectResult(listOrders);
        }

        /// <summary>
        /// Rebuild: get orders history
        /// </summary>
        [Route("GetOrdersHistoryByParent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrdersHistoryByParent([FromBody] OrderHistoryUserRequest request)
        {
            _orderValidator.ValidateRequest(request);
            await _orderValidator.ValidateAccessToUser(request.UserId);
            var result = await _orderService.GetOrdersByParent(new SearchByUser { EndOrderDate = DateTime.Parse(request.EndOrderDate), IncludeError = false, UserId = request.UserId, StartOrderDate = DateTime.Parse(request.StartOrderDate) });
            return new OkObjectResult(result);
        }

        /// <summary>
        /// Get all orders for a given payment transaction
        /// </summary>
        [Route("GetOrderListByPayment/{paymentId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetOrderListByPayment(int paymentId)
        {
            var res = await _orderService.GetOrdersByPayment(paymentId);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get all uniform orders for a given student.
        /// </summary>
        /// <returns></returns>
        [Route("GetOrderUniformListByStudent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrderUniformListByStudent([FromBody] OrderHistoryUserRequest request)
        {
            await _orderValidator.ValidateAccessToStudent(request.UserId);
            _orderValidator.ValidateRequest(request);

            ListOrders listOrders = new();

            var orders = await _orderService.GetUniformOrdersByStudnet(request.UserId, DateTime.Parse(request.StartOrderDate), DateTime.Parse(request.EndOrderDate));
            listOrders.Orders = orders?.ToList();

            // If we have a schoolId than we get the menus
            if (request.SchoolId > 0)
            {
                var menus = await _menuService.GetMenuList(Utils.NullableIntToLong(request.SchoolId));
                listOrders.Menus = menus?.ToList();
            }

            return new OkObjectResult(listOrders);
        }

        /// <summary>
        /// Cancel an Order (start at Client version => 2019-12-03)
        /// </summary>
        [Route("CancelOrder")]
        [Route("CancelOrderMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        [Obsolete]
        public async Task<IActionResult> CancelOrder([FromBody] OrderWithPayment request)
        {
            // Place a user audit event
            _telemetryService.TrackTrace("cancelOrder_request",
                new Dictionary<string, object>() {
                    { "request", request }
                });

            return await CancelOrder(request.Order.OrderId.Value);
        }

        [Route("CancelOrder/{orderId}")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> CancelOrder(int orderId)
        {
            var order = await _orderService.GetOrderViewById(orderId);
            await _orderValidator.ValidateAccessToOrder(order);
            await _orderService.CancelOrder(order);
            await _feeApiSerice.UpdateFeeStatus(orderId, FeeStatus.Cancelled);
            var resp = new
            {
                status = "success"
            };

            return new OkObjectResult(resp);
        }

        /// <summary>
        ///  Check if a child already have an existing order
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("CheckIfOrderExist")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> CheckIfOrderExist([FromBody] CheckExistingOrderRequest request)
        {
            await _orderValidator.ValidateAccessToStudent(request.ChildId);
            // Must ust have either MenuType or MenuId
            if (request.ChildId > 0 && request.ForDate != null &&
                (!string.IsNullOrEmpty(request.MenuType) || request.MenuId > 0))
            {
                var result = await _orderService.CheckIfOrderExist(request);

                return new OkObjectResult(result);
            }

            throw new ValidationException("Request details are incorrect");
        }

        [Route("RefundFullOrder")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> RefundFullOrder(FullOrderRefundRequest request)
        {
            OrderRefundResponse resp = await _orderService.RefundOrder(request.OrderId, OrderStatusEnum.Cancelled, request.Message);

            if (resp == null)
            {
                throw new PaymentException($"Failed to refund Order #{request.OrderId}");
            }

            return new OkObjectResult(resp);
        }

        // private async Task<IActionResult> RefundOrderCore(int orderId, OrderStatusEnum finalOrderStatus, string message = null)
        // {
        //     OrderRefundResponse resp = await _orderService.RefundOrder(orderId, finalOrderStatus, message);

        //     if (resp == null)
        //     {
        //         throw new PaymentException($"Failed to refund Order #{orderId}");
        //     }

        //     await _feeApiSerice.UpdateFeeStatus(orderId, FeeStatus.Refunded);
        //     // RefundOrder should have added the required tblUserPayment records
        //     //
        //     // TODO - Remove
        //     //
        //     // User fromUser = await this.userService.GetUserByExternalUserId(resp.FromExternalUserID);
        //     // User toUser = await this.userService.GetUserById(Convert.ToInt32(resp.ToUserId));
        //     // await this.paymentService.AddAccountCredit(fromUser, toUser, resp.Amount + resp.Fee);

        //     return new OkObjectResult(resp);
        // }

        /// <summary>
        /// Fetch orders for a given student
        /// </summary>
        [Route("GetOrdersByStudentWithFilters/{studentId}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetOrdersByStudentWithFilters([FromBody] OrderFilter filter, int studentId)
        {
            _orderValidator.ValidateRequest(filter);
            await _orderValidator.ValidateAccessToStudent(studentId);

            var orders = await _orderService.GetOrderByStudentWithFilters(studentId, filter);

            return new OkObjectResult(orders);
        }

        [Route("GetOrdersByUserWithFilters/{id}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        [Obsolete]
        public async Task<IActionResult> GetOrdersByUserWithFilters([FromBody] ArrayFilter filter, int id)
        {
            await _orderValidator.ValidateArrayFilterAccess(filter);

            ListOrders listOrders = new();
            var orders = await _orderService.GetOrderByUserWithFilter(id, filter);
            listOrders.Orders = orders?.ToList();

            return new OkObjectResult(listOrders);
        }

        [Route("OrdersByUserWithFilters/{userId}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetOrdersByUserWithFilters2([FromBody] ArrayFilter filter, int userId)
        {
            await _orderValidator.ValidateArrayFilterAccess(filter);

            var result = await _orderService.GetOrderByUserWithFilter2(userId, filter);

            return new OkObjectResult(result);
        }

        [Route("ValidOrder")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> ValidOrder([FromBody] Order order)
        {
            await _orderService.ValidOrder(Utils.NullableIntToLong(order.OrderId));

            return new OkResult();
        }

        [Route("UpdateCanteenStatusOrderList")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> UpdateCanteenStatusOrderList([FromBody] UpdateOrderListCanteenStatusRequest request)
        {
            _orderValidator.ValidateRequest(request);
            var orderIds = request.OrderIds.Select(o => (long)o);
            await _orderValidator.ValidateAccessToOrders(orderIds);

            await _orderService.UpdateCanteenStatusOrderList(orderIds, request.CanteenStatus);

            return new OkResult();
        }

        [Route("ChangeMenuTypeOrder")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        [Obsolete]
        public async Task<IActionResult> ChangeMenuTypeOrder([FromBody] Order order)
        {
            await _orderService.ChangeMenuTypeOrderAsync(order.OrderId.Value);

            return new OkResult();
        }

        [Route("ChangeMenuTypeOrder/{orderId}")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> ChangeMenuTypeOrder(int orderId)
        {
            await _orderService.ChangeMenuTypeOrderAsync(orderId);

            return new OkResult();
        }

        #region

        // Adding new end points for the mobile app

        [Route("GetRecentOrdersHistoryByParentMobile")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetRecentOrdersHistoryByParentMobile()
        {
            var user = await _userService.GetCurrentUser();

            var result = await _orderService.GetRecentOrdersByParent(user.UserId, true);

            return new OkObjectResult(result);
        }

        [Route("RecentOrdersHistoryByParentMobile")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetRecentOrdersHistoryByParentMobile2()
        {
            var user = await _userService.GetCurrentUser();

            var result = await _orderService.GetRecentOrdersByParent2(user.UserId, true);

            return new OkObjectResult(result);
        }

        /// <summary>
        /// Return the newest non cancelled order for the given order date and menu type,
        /// as well as whether the school is closed or not!
        ///
        /// Inputs
        /// -- Student Id
        /// -- Order Date in yyyy-MM-dd format
        /// -- Menu Type
        ///
        /// Outputs
        /// -- Order
        /// -- Is School Closed
        /// </summary>
        [Route("GetOrderByStudentOrderDateAndMenuType")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetOrderByStudentOrderDateAndMenuType([FromBody] OrderByStudentOrderDateAndMenuTypeRequest request)
        {
            _orderValidator.ValidateRequest(request);

            await _orderValidator.ValidateAccessToStudent(request.StudentId);

            OrderByStudentOrderDateAndMenuTypeResponse response = new();

            var orderDate = DateTime.Parse(request.OrderDate);

            // Expecting the following inputs: 18494, '2022-06-21T00:47:28.763Z', 'Recess'
            var orders = await _orderService.GetOrderByStudentAndDateAndMenu(request.StudentId, orderDate, request.MenuType);

            // Return the non cancelled orders for the given date and menu type
            if (orders.Any())
            {
                response.Order = orders.ToArray();
            }
            else
            {
                response.Order = null; // React native app checks for null value
            }

            // Check for school closure on this date
            var closureDates = await _schoolService.GetSchoolDatesByStudent(request.StudentId, orderDate, orderDate);

            // Closure dates are found
            response.IsSchoolClosed = closureDates != null && closureDates.Any();

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Get all orders for a given student.
        /// </summary>
        /// <returns></returns>
        [Route("GetOrderListByStudentMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrderListByStudentMobile([FromBody] OrderHistoryUserRequest request)
        {
            return await GetOrdersMenusAndSchoolClosures(request, true);
        }

        /// <summary>
        /// Get all orders for a given student. This is called from the web app
        /// </summary>
        /// <returns></returns>
        [Route("GetOrderListByStudent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetOrderListByStudent([FromBody] OrderHistoryUserRequest request)
        {
            return await GetOrdersMenusAndSchoolClosures(request, false);
        }

        private async Task<IActionResult> GetOrdersMenusAndSchoolClosures(OrderHistoryUserRequest request, bool includeError)
        {
            _orderValidator.ValidateRequest(request);
            await _orderValidator.ValidateAccessToStudent(request.UserId);

            var orders = await _orderService.GetOrdersByStudent(new SearchByUser
            {
                IncludeError = includeError,
                UserId = request.UserId,
                StartOrderDate = DateTime.Parse(request.StartOrderDate),
                EndOrderDate = DateTime.Parse(request.EndOrderDate),
            });

            _telemetryService.TrackTrace("order_list", new Dictionary<string, string>() {
                { "response", System.Text.Json.JsonSerializer.Serialize(orders) }
            });

            // Returning null as the mobile app does not handle an empty list/array
            ListOrders listOrders = new();
            listOrders.Orders = orders != null && orders.Any() ? orders.ToList() : null;

            // If we have a schoolId than we get the menus and dates
            if (request.SchoolId > 0)
            {
                // Get menus for the student's school and filter out any "uniform" menus if the
                // uniform shop feature is not turned on for the given school
                var menus = await _menuService.GetFilteredMenuList(Utils.NullableIntToLong(request.SchoolId));

                // Returning null as the mobile app does not handle an empty list/array
                listOrders.Menus = menus != null && menus.Any() ? menus.ToList() : null;

                // Get closed dates
                var schoolDates = await _schoolService.GetSchoolDatesBySchoolId(Utils.NullableIntToLong(request.SchoolId),
                                                            DateTime.Parse(request.StartOrderDate),
                                                            DateTime.Parse(request.EndOrderDate));

                // Returning null as the mobile app does not handle an empty list/array
                listOrders.Dates = schoolDates != null && schoolDates.Any() ? schoolDates.ToList() : null;
            }

            return new OkObjectResult(listOrders);
        }

        [Route("EventOrderHistoryByParent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> EventOrderHistoryByParent([FromBody] EventUniformHistoryRequest request)
        {
            try
            {
                // Can only fetch orders associated to current authenticated user
                var user = await _userService.GetCurrentUser();
                var response = await _orderService.GetOrderEventHistoryByParent(user.UserId, request.StartIndex, request.NumberOfOrders);

                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                // Send some telemetry and re-throw the exception caught
                _telemetryService.TrackTrace("EventOrderHistoryByParent_request",
                                new Dictionary<string, string>() {
                                    {"request", JsonConvert.SerializeObject(request)}
                                });

                throw;
            }
        }

        [Route("UniformOrderHistoryByParent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> UniformOrderHistoryByParent([FromBody] EventUniformHistoryRequest request)
        {
            try
            {
                // Can only fetch orders associated to current authenticated user
                var user = await _userService.GetCurrentUser();

                var response = await _orderService.GetUniformOrderHistoryByParent(user.UserId, request.StartIndex, request.NumberOfOrders);

                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                _telemetryService.TrackTrace("UniformOrderHistoryByParent_request",
                                    new Dictionary<string, string>() {
                                        {"request", JsonConvert.SerializeObject(request)}
                                    });

                throw;
            }
        }

        [Route("UniformOrEventsOrderHistoryByParent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetUniformOrEventsOrderHistoryByParent([FromBody] EventUniformHistoryRequest request)
        {
            try
            {
                // Can only fetch orders associated to current authenticated user
                var user = await _userService.GetCurrentUser();

                var response = await _orderService.GetUniformOrEventsOrderHistoryByParent(user.UserId, request.MenuType, request.StartIndex, request.NumberOfOrders);

                return new OkObjectResult(response);
            }
            catch (Exception)
            {
                _telemetryService.TrackTrace("UniformOrEventsOrderHistoryByParent_request",
                                    new Dictionary<string, string>() {
                                        {"request", JsonConvert.SerializeObject(request)}
                                    });

                throw;
            }
        }

        /// <summary>
        /// Make a payment and place / edit order
        /// </summary>
        [Route("RefundPartialOrder")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        [Obsolete]
        public async Task<IActionResult> RefundPartialOrder([FromBody] OrderWithPayment request)
        {

            request.Payment.transactionType = TransactionTypeEnum.PartialRefund;

            // make payment
            // get canteen external user Id
            if (request.Payment.canteenId > 0)
            {
                CanteenEntity canteen = await this._canteenService.GetCanteenById(request.Payment.canteenId);

                if (!string.IsNullOrEmpty(request.Payment.toExternalCustomerId) && string.IsNullOrEmpty(request.Payment.fromExternalCustomerId))
                {
                    request.Payment.fromExternalCustomerId = canteen.ExternalUserId;
                }
            }

            var paymentRes = await _paymentService2.MakeOrderPayment(request.Payment, new List<int>() { request.Order.OrderId.Value }, 0m);

            await _orderService.UpdateOrderPaymentAndItems(request.Order, request.Payment, paymentRes);
            return new OkObjectResult(request.Order);
        }

        [Route("RefundPartialOrderV2")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> RefundPartialOrderV2([FromBody] OrderEditRequest request)
        {
            var existingOrder = await _orderValidator.ValidateEditOrderRequest(request);

            var response = await _orderService.PartialRefundOrder(request, existingOrder);

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Make a payment and place / edit order
        /// </summary>
        [Route("MakePaymentAndMultipleOrders")]
        [Route("MakePaymentAndMultipleOrdersMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> MakePaymentAndMultipleOrdersMobile([FromBody] MultipleOrdersWithPayment request)
        {
            // Update the request to remove any duplicated orders
            await _orderService.FixMultipleOrdersWithPaymentRequest(request);

            await _orderValidator.ValidateRequest(request);

            List<OrderUpsertResponse> res = new();

            // Use new order process for approved schools
            if (request.UseNewOrderProcess(_orderProcessSchoolIds))
            {

                // draft order
                var draftRes = await _orderService.UpsertDraftOrders(request.Orders.Orders, String.Empty);
                var orderIds = draftRes.Select(s => s.OrderId);
                res = draftRes;

                // make payment
                // get canteen external user Id
                if (request.Payment.canteenId > 0)
                {
                    CanteenEntity canteen = await this._canteenService.GetCanteenById(request.Payment.canteenId);

                    if (string.IsNullOrEmpty(request.Payment.toExternalCustomerId) && !string.IsNullOrEmpty(request.Payment.fromExternalCustomerId))
                    {
                        request.Payment.toExternalCustomerId = canteen.ExternalUserId;
                    }
                    else if (!string.IsNullOrEmpty(request.Payment.toExternalCustomerId) && string.IsNullOrEmpty(request.Payment.fromExternalCustomerId))
                    {
                        request.Payment.fromExternalCustomerId = canteen.ExternalUserId;
                    }
                }

                decimal? totalFee = 0;

                if (!request.IsEditOrder)
                {
                    totalFee = request.Orders?.Orders?.Sum(o => o.OrderFee);

                    // remove fee from the merchant payment
                    request.Payment.chargeAmount = Convert.ToDecimal(request.Orders.Price - totalFee);
                }

                request.Payment.transactionType = TransactionTypeEnum.OrderPayment;
                var paymentRes = await _paymentService2.MakeOrderPayment(request.Payment, orderIds, Convert.ToDecimal(totalFee));

                // update draft order
                await _orderService.UpdateDraftOrders(draftRes, request.Payment, paymentRes, false);

                OrderStatusEnum orderStatus = paymentRes.IsSuccess == true ? OrderStatusEnum.Confirmed : OrderStatusEnum.Error;

                if (paymentRes.IsSuccess == true)
                {
                    // update stock
                    foreach (var ord in draftRes)
                    {
                        Order order = await this._orderService.GetOrderById(ord.OrderId);

                        await this._stockService.OrderUpdateStocks(order);
                    }
                }

                // send email
                await this._notificationService.SendNotification(draftRes, Convert.ToInt32(orderStatus));
            }
            else
            {
                // old process
                var orderFees = await _feeApiSerice.ManageFees(request.Orders.Orders);

                // Call logic app
                res = await _logicAppService.ProcessOrder(request);

                foreach (var o in request.Orders.Orders)
                {
                    var orderId = res.FirstOrDefault(r =>
                                                        r.MenuType == o.MenuType
                                                        && r.StudentId == o.StudentId
                                                        && r.OrderDate == o.GetOrderDate().Date)?.OrderId;
                    if (orderId.HasValue)
                    {
                        o.OrderId = orderId;
                    }
                }

                await _feeApiSerice.SaveFeesUsed(orderFees, request.Orders.Orders.Where(o => o.OrderId.HasValue));
            }

            return new OkObjectResult(res);
        }

        [Route("ReOrderInfo")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> ReOrderInfo([FromBody] MultipleOrderRequests request)
        {
            var orderRequestEnhancementDto = await _orderValidator.ValidateReOrderRequest(request);

            var response = await _orderService.GetReOrderInfo(request, orderRequestEnhancementDto);

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Create Order
        /// </summary>
        [Route("CreateOrders")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> CreateOrders([FromBody] MultipleOrderRequests request)
        {
            var orderRequestEnhancementDto = await _orderValidator.ValidateCreateOrdersRequest(request);

            var response = await _orderService.CreateOrders(request, orderRequestEnhancementDto);

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Edit Orders
        /// </summary>
        [Route("EditOrder")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> EditOrder([FromBody] OrderEditRequest request)
        {
            var existingOrder = await _orderValidator.ValidateEditOrderRequest(request);

            var response = await _orderService.EditOrder(request, existingOrder);

            return new OkObjectResult(response);
        }

        [Route("CreateOrdersSummary")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> CreateOrdersSummary([FromBody] MultipleOrderRequests request)
        {
            var orderRequestEnhancementDto = await _orderValidator.ValidateCreateOrdersRequest(request);

            var response = await _orderService.GetCreateOrdersSummary(request, orderRequestEnhancementDto);

            return new OkObjectResult(response);
        }

        // This endpoint EditOrderSummaryWithDelayForTestOnly was created to test 504 Gateway Timeout Error of the pods and ShutdownService
        // [Route("EditOrderSummaryWithDelayForTestOnly/{delayInMilliseconds}")]
        // [HttpPatch]
        // [ProducesResponseType(200)]
        // [ProducesResponseType(400)]
        // [ProducesResponseType(500)]
        // [CheckUserRole(UserRole.Parent)]
        // public async Task<IActionResult> EditOrderSummaryWithDelayForTestOnly([FromBody] OrderEditRequest request, int delayInMilliseconds)
        // {
        //     await Task.Delay(delayInMilliseconds);
        //     return await EditOrderSummary(request);
        // }

        [Route("EditOrderSummary")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> EditOrderSummary([FromBody] OrderEditRequest request)
        {
            var existingOrder = await _orderValidator.ValidateEditOrderRequest(request);

            var response = await _orderService.GetEditOrderSummary(request, existingOrder);

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Insert or update draft Orders on submit or edit
        /// </summary>
        [AllowAnonymous]
        [Route("SaveDraftOrders/{logicAppIdentifier}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        public async Task<IActionResult> SaveDraftOrders(string logicAppIdentifier, [FromBody] MultipleOrders orders)
        {
            if (orders == null || orders.Orders == null || orders.Orders.Count == 0)
            {
                throw new ValidationException("Invalid request");
            }

            var res = await _orderService.UpsertDraftOrders(orders.Orders, logicAppIdentifier);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Update draft Orders following payment processing
        /// </summary>
        [AllowAnonymous]
        [Route("UpdateDraftOrders/{logicAppIdentifier}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        public async Task<IActionResult> UpdateDraftOrders(string logicAppIdentifier, [FromBody] UpdateDraftOrdersRequest request)
        {
            if (request.Orders == null || !request.Orders.Any() ||
                request.PaymentRequest == null || request.PaymentResponse == null)
            {
                throw new ValidationException("Invalid request");
            }

            await _orderService.UpdateDraftOrders(request.Orders, request.PaymentRequest, request.PaymentResponse);

            return new OkResult();
        }

        /// <summary>
        /// Update Failed Orders following payment processing
        /// </summary>
        [AllowAnonymous]
        [Route(template: "UpdateFailedOrders")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        public async Task<IActionResult> UpdateFailedOrders(string logicAppIdentifier, [FromBody] FailedOrdersRequest request)
        {
            await _orderService.UpdateFailedOrders(request);

            return new OkResult();
        }

        [Route("RetryFailedPayments")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> RetryFailedPayments()
        {
            await _orderService.RetryFailedPayments();

            return new OkResult();
        }

        /// <summary>
        /// Get the order history for a given parent
        ///
        /// TODO - Combine with the "GetOrdersHistoryByParent" endpoint... The difference being
        ///        the original endpoint returns a List of Order objects and this returns a List
        ///        of OrderHistory objects
        /// </summary>
        [Route("GetOrdersHistoryByParentMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetOrdersHistoryByParentMobile([FromBody] OrderHistoryRequestMobile request)
        {
            _orderValidator.ValidateRequest(request);

            // Only able to fetch orders for current authenticated user
            var user = await _userService.GetCurrentUser();

            // get orders history
            var result = await _orderService.GetOrdersByParent(new SearchByUser { EndOrderDate = DateTime.Parse(request.EndDate), IncludeError = true, UserId = user.UserId, StartOrderDate = DateTime.Parse(request.StartDate) });

            return new OkObjectResult(result);
        }

        [Route("OrdersHistoryByParentMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrdersHistoryByParentMobile2([FromBody] OrderHistoryRequestMobile request)
        {
            _orderValidator.ValidateRequest(request);

            // Only able to fetch orders for current authenticated user
            var user = await _userService.GetCurrentUser();

            // get orders history
            var result = await _orderService.GetOrdersByParent2(new SearchByUser { EndOrderDate = DateTime.Parse(request.EndDate), IncludeError = true, UserId = user.UserId, StartOrderDate = DateTime.Parse(request.StartDate) });

            return new OkObjectResult(result);
        }

        /// <summary>
        /// Get student list for a report menu item
        /// </summary>
        [Route("GetOrderItemStudentList")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> GetOrderItemStudentList([FromBody] ReportRequest request)
        {
            if (String.IsNullOrEmpty(request.SchoolsIds))
            {
                throw new ValidationException("SchoolIds", "SchoolIds must be supplied");
            }
            else if (String.IsNullOrEmpty(request.Date))
            {
                throw new ValidationException("Date", "Date must be supplied");
            }
            else if (String.IsNullOrEmpty(request.MenuType))
            {
                throw new ValidationException("MenuType", "MenuType must be supplied");
            }

            var res = await _orderService.GetReportItemStudentList(request);

            return new OkObjectResult(res);
        }

        [Route("{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrderById(int orderId)
        {
            var order = await _orderService.GetOrderById(orderId);

            await _orderValidator.ValidateAccessToOrder(order);

            return new OkObjectResult(order);
        }

        [Route("{orderId}/CutOffTimes")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetOrderByIdWithCutOffTimes(int orderId)
        {
            var res = await _orderService.GetOrderByIdWithCutOffTimes(orderId);

            await _orderValidator.ValidateAccessToOrder(res.Order);

            return new OkObjectResult(res);
        }

        [Route("{orderId}/CutOffTimes2")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetOrderViewByIdWithCutOffTimes(int orderId)
        {
            var res = await _orderService.GetOrderViewByIdWithCutOffTimes(orderId);

            var order = res.Order;
            await _orderValidator.ValidateAccessToOrder(new Order()
            {
                CanteenId = order.CanteenId,
                UserId = order.UserId,
                StudentId = order.StudentId,
                OrderId = order.OrderId

            }); // Only minimal properties are enough to validate access, no need to transform into full Order object.

            return new OkObjectResult(res);
        }

        #endregion

        /// <summary>
        /// Common validation for the Reports endpoints
        /// </summary>
        /// <param name="request"></param>
        /// <param name="checkDate"></param>
        /// <param name="checkMenuType"></param>
        private async Task CommonValidateRquest(ReportRequest request, bool checkDate = true, bool checkMenuType = false)
        {
            _orderValidator.ValidateRequest(request, checkDate, checkMenuType);

            await _orderValidator.ValidateAccessToSchools(request.SchoolsIds);
            await _orderValidator.ValidateAccessToCanteen(request.MerchantId);
        }
    }
}