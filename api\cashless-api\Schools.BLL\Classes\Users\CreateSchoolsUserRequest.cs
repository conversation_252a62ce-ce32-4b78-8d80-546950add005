using System;
using Newtonsoft.Json;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes;

public class CreateSchoolsUserRequest
{
    [JsonProperty(PropertyName = "IsActive")]
    public bool IsActive { get; set; }

    [JsonProperty(PropertyName = "SchoolId")]
    public long? SchoolId { get; set; }

    [JsonProperty(PropertyName = "ClassId")]
    public long? ClassId { get; set; }

    [JsonProperty(PropertyName = "ParentId")]
    public long? ParentId { get; set; }

    [JsonProperty(PropertyName = "FirstName")]
    public string FirstName { get; set; }

    [JsonProperty(PropertyName = "Lastname")]
    public string Lastname { get; set; }

    [JsonProperty(PropertyName = "Email")]
    public string Email { get; set; }

    [JsonProperty(PropertyName = "DateOfBirth")]
    public DateTime? DateOfBirth { get; set; }

    [JsonProperty(PropertyName = "Role")]
    public UserRole Role { get; set; }

    [JsonProperty(PropertyName = "Mobile")]
    public string Mobile { get; set; }

    [JsonProperty(PropertyName = "AllowCanteenToOrder")]
    public bool? AllowCanteenToOrder { get; set; }

    [JsonProperty(PropertyName = "FavouriteColour")]
    public string FavouriteColour { get; set; }

    [JsonProperty(PropertyName = "Allergies")]
    public string Allergies { get; set; }

    [JsonProperty(PropertyName = "SpriggyUserId")]
    public string SpriggyUserId { get; set; }

    [JsonProperty(PropertyName = "FirebaseUserId")]
    public string FirebaseUserId { get; set; }
}