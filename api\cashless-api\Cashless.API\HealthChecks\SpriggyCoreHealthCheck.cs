using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Cashless.API.HealthChecks;
using Schools.BLL.Services;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Cashless.APIs.HealthChecks;

/// <summary>
/// Check the health of Spriggy Core
/// </summary>
public class SpriggyCoreHealthCheck : BaseHealthCheck, IHealthCheck
{
    /// <summary>
    /// Result from https://api.sandbox.spriggy.io/health
    /// </summary>
    class HealthResult
    {
        [JsonPropertyName("status")]
        public string Status { get; set; }

        /// <summary>
        /// Check if the response is healthy
        /// </summary>
        public bool IsHealthy() => Status == "healthy";
    }

    /// <summary>
    /// URL for the health endpoint on the spriggy-core infrastructure
    /// </summary>
    private readonly string _url;

    /// <summary>
    /// HTTP client factory
    /// </summary>
    private readonly IHttpClientFactory _httpClientFactory;

    public SpriggyCoreHealthCheck(IHttpClientFactory httpClientFactory, string baseUrl)
    {
        _httpClientFactory = httpClientFactory;
        _url = GetUrl(baseUrl, "health");
    }

    /// <summary>
    /// Check spriggy-core is available
    /// </summary>
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Use the HTTP client configured to pass all the required headers 
            // for spriggy-core. Using a normal HttpClient will fail with a 401
            using (var client = _httpClientFactory.CreateClient(ScoreApiClient.HttpClientName))
            {
                var response = await client.GetAsync(_url);
                if (response == null || response.StatusCode != HttpStatusCode.OK)
                {
                    return GetUnhealthyResult(stopwatch, response, _url);
                }

                var body = await response.Content.ReadAsStringAsync();
                if (body == null)
                {
                    return GetUnhealthyResult(stopwatch, response, _url);
                }

                var result = JsonSerializer.Deserialize<HealthResult>(body);
                if (!result.IsHealthy())
                {
                    return GetUnhealthyResult(stopwatch, result, _url);
                }

                return GetHealthyResult(stopwatch, result, _url);
            }
        }
        catch (Exception ex)
        {
            return GetUnhealthyResult(stopwatch, ex, _url);
        }
    }
}