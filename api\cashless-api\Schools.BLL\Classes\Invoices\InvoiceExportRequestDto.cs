using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Invoices;

/// <summary>
/// Invoice export request
/// {
///    "StartDate": "2022-08-15",
///    "EndDate": "2022-08-21",
///    "ExportType": "Settlement"
/// }
/// </summary>
public class InvoiceExportRequestDto : GenerateInvoiceRequestDto
{
    [JsonPropertyName("ExportType")]
    [Required]
    public InvoiceExportTypeEnum? ExportType { get; set; }
}

public class InvoiceFileRequestDto
{
    [Required]
    public long InvoiceId { get; set; }

    [Required]
    public InvoiceExportTypeEnum ExportType { get; set; }
}

public class GenerateInvoiceRequestDto
{
    [JsonPropertyName("StartDate")]
    [Required]
    public DateTime? StartDate { get; set; }

    [JsonPropertyName("EndDate")]
    [Required]
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// The generic response being returned for invoice exports
/// </summary>
public class InvoiceExportResponseDto
{
}

/// <summary>
/// A line item from the settlement CSV export: https://docs.google.com/spreadsheets/d/15tclyx6JmhpHXk5jL5ULGcEkQBQrq0mqbyiqiwan6UI/edit#gid=0
/// </summary>
public class SettlementRecordDto : InvoiceExportResponseDto
{
    [JsonPropertyName("InvoiceDate")]
    public string InvoiceDate { get; set; }

    [JsonPropertyName("InvoiceNumber")]
    public string InvoiceNumber { get; set; }

    [JsonPropertyName("ContactName")]
    public string ContactName { get; set; }

    [JsonPropertyName("Quantity")]
    public decimal Quantity { get; set; }

    [JsonPropertyName("UnitAmount")]
    public decimal UnitAmount { get; set; }

    [JsonPropertyName("TaxType")]
    public string TaxType { get; set; }

    [JsonPropertyName("DueDate")]
    public string DueDate { get; set; }

    [JsonPropertyName("AccountCode")]
    public string AccountCode { get; set; }

    [JsonPropertyName("Description")]
    public string Description { get; set; }

    [JsonPropertyName("CanteenId")]
    public long CanteenId { get; set; }

    [JsonPropertyName("SpecialInstructions")]
    public string SpecialInstructions { get; set; }
}

/// <summary>
/// A line item from the invoice CSV export: https://docs.google.com/spreadsheets/d/1HG_ayu_DpKHR4hpp32APzldhpOvajvuytpn2iu3LlbY/edit#gid=0
/// </summary>
public class InvoiceRecordDto : SettlementRecordDto
{
    [JsonPropertyName("Reference")]
    public string Reference { get; set; }
}

public class RevenueRecordDto : InvoiceExportResponseDto
{
    [JsonPropertyName("PeriodStart")]
    public string PeriodStart { get; set; }

    [JsonPropertyName("PeriodEnd")]
    public string PeriodEnd { get; set; }

    [JsonPropertyName("CanteenId")]
    public long CanteenId { get; set; }

    [JsonPropertyName("MerchantName")]
    public string MerchantName { get; set; }

    [JsonPropertyName("CanteenFee")]
    public decimal CanteenFee { get; set; }

    [JsonPropertyName("SettledOrders")]
    public int SettledOrders { get; set; }

    [JsonPropertyName("SettledOrderAmount")]
    public decimal SettledOrderAmount { get; set; }

    [JsonPropertyName("SettledOrderFees")]
    public decimal SettledOrderFees { get; set; }

    [JsonPropertyName("CanteenFeeRevenue")]
    public decimal CanteenFeeRevenue { get; set; }

    [JsonPropertyName("SpecialInstructions")]
    public string SpecialInstructions { get; set; }
}