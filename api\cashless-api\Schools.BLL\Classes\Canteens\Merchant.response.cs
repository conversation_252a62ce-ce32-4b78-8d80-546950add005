﻿using System;

namespace Schools.BLL.Classes.Canteens;

public class GetAllMerchantResponse
{
    public int CanteenId { get; set; }
    public string MerchantName { get; set; }
    public int OwnerId { get; set; }
    public string OwnerName { get; set; }
}

public class GetMerchantResponse
{
    public int CanteenId { get; set; }
    public string Name { get; set; }
    public MerchantFeeInformationResponse FeePayment { get; set; }
    public MerchantContactInformationResponse ContactDetails { get; set; }
    public MerchantDetailsInformationResponse MerchantDetails { get; set; }
}

public class MerchantFeeInformationResponse
{
    public DateTime? MerchantFeeStartDate { get; set; }
    public Decimal? MerchantFee { get; set; }
    public Decimal? ParentFeeCoveredByMerchants { get; set; }
}

public class MerchantContactInformationResponse
{
    public int OwnerId { get; set; }
    public string OwnerFirstName { get; set; }
    public string OwnerLastName { get; set; }
    public string OwnerMobile { get; set; }
    public string OwnerEmail { get; set; }
}

public class MerchantDetailsInformationResponse
{
    public string Name { get; set; }
    public string FriendlyName { get; set; }
    public string Type { get; set; }
    public string Phone { get; set; }
    public string Abn { get; set; }
    public string Status { get; set; }
    public string StatusDate { get; set; }
}
