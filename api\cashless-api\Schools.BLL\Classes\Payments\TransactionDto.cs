using System;
using System.Collections.Generic;
using Schools.BLL.Classes.Orders;
using Schools.DAL.Entities;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Payments;

/// <summary>
/// Criteria for searching for Transactions
/// </summary>
public class TransactionHistorySearchRequest
{
    public long UserId { get; set; }
    public DateTime? StartDateUtc { get; set; }
    public DateTime? EndDateUtc { get; set; }
    public int? RecordLimit { get; set; }
}
