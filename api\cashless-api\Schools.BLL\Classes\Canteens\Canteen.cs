﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public partial class CanteenIntegration
    {
        [JsonProperty(PropertyName = "CanteenIntegrationId")]
        public long CanteenIntegrationId { get; set; }

        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        [JsonProperty(PropertyName = "AccessToken")]
        public string AccessToken { get; set; }

        [JsonProperty(PropertyName = "RefreshToken")]
        public string RefreshToken { get; set; }

        [JsonProperty(PropertyName = "TenantId")]
        public string TenantId { get; set; }

        [JsonProperty(PropertyName = "TenantName")]
        public string TenantName { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public long CanteenId { get; set; }
    }

    public class MerchantDto
    {
        [JsonProperty(PropertyName = "MerchantId")]
        public long MerchantId { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool IsActive { get; set; }

        [JsonProperty(PropertyName = "MerchantName")]
        public string MerchantName { get; set; }

        [JsonProperty(PropertyName = "FriendlyName")]
        public string FriendlyName { get; set; }

        [JsonProperty(PropertyName = "MerchantType")]
        public string MerchantType { get; set; }

        [JsonProperty(PropertyName = "ExternalUserId")]
        public string ExternalUserId { get; set; }

        [JsonProperty(PropertyName = "MerchantFeeStartDate")]
        public DateTime MerchantFeeStartDate { get; set; }

        [JsonProperty(PropertyName = "MerchantFee")]
        public Decimal? MerchantFee { get; set; }

        [JsonProperty(PropertyName = "ParentFeeCoveredByMerchants")]
        public Decimal? ParentFeeCoveredByMerchants { get; set; }
    }
}
