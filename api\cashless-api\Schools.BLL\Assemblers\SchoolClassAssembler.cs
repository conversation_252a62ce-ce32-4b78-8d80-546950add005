using System.Collections.Generic;
using System.Data.SqlClient;
using Schools.BLL.Classes;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class SchoolClassAssembler
{
    public static SchoolClass Convert_ClassEntity_To_ClassDto(SchoolClassEntity request)
    {
        SchoolClass schoolClass = new()
        {
            ClassId = request.ClassId,
            SchoolId = request.SchoolId,
            IsActive = request.IsActive,
            YearGroup = request.YearGroup,
            IsArchived = request.IsArchived,
            Teacher = request.Teacher,
            DateCreated = request.DateCreated,
            DateModified = request.DateModified,
            Name = request.Name,
            SortOrder = request.SortOrder
        };

        return schoolClass;
    }

    public static List<SchoolClass> Convert_ListClassEntity_To_ListClassDto(List<SchoolClassEntity> request)
    {
        List<SchoolClass> dto = new();

        if (request != null && request.Count > 0)
        {
            request.ForEach(e =>
            {
                dto.Add(Convert_ClassEntity_To_ClassDto(e));
            });
        }

        return dto;
    }
}
