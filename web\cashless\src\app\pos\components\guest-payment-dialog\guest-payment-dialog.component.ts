import { Component, Inject, OnInit, signal } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BaseComponent } from '../../../sharedModels';
import { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';
import { CashlessAppInsightsService } from '../../../sharedServices';
import {
  GuestPaymentDialogData,
  GuestPaymentDialogResult,
  GuestPaymentRequest,
  validateCardNumber,
  validateExpiryDate,
  validateCVV,
  detectCardType,
  formatCardNumber
} from '../../../sharedModels/guest-payment/guest-payment.models';

@Component({
  selector: 'app-guest-payment-dialog',
  templateUrl: './guest-payment-dialog.component.html',
  styleUrls: ['./guest-payment-dialog.component.scss']
})
export class GuestPaymentDialogComponent extends BaseComponent implements OnInit {
  
  paymentForm: FormGroup;
  isProcessing = signal<boolean>(false);
  isValidating = signal<boolean>(false);
  paymentSuccess = signal<boolean>(false);
  errorMessage = signal<string | null>(null);
  cardType = signal<string>('');
  
  // Form validation states
  cardNumberValid = signal<boolean>(false);
  expiryValid = signal<boolean>(false);
  cvvValid = signal<boolean>(false);
  nameValid = signal<boolean>(false);
  
  // Available options
  expiryMonths = this.guestPaymentService.getExpiryMonths();
  expiryYears = this.guestPaymentService.getExpiryYears();
  
  // Order details
  totalAmount: number;
  orderItems: any[];
  guestUserId: number;
  canteenId: number;

  constructor(
    private fb: FormBuilder,
    private guestPaymentService: GuestPaymentService,
    private appInsightsService: CashlessAppInsightsService,
    public dialogRef: MatDialogRef<GuestPaymentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GuestPaymentDialogData
  ) {
    super();
    this.initializeFormData();
    this.createPaymentForm();
  }

  ngOnInit(): void {
    this.setupFormValidation();
    this.trackDialogOpened();
  }

  private initializeFormData(): void {
    this.totalAmount = this.data.totalAmount;
    this.orderItems = this.data.orders;
    this.guestUserId = this.data.selectedStudent?.UserId;
    this.canteenId = this.data.canteenId;
  }

  private createPaymentForm(): void {
    this.paymentForm = this.fb.group({
      cardNumber: ['', [Validators.required, Validators.minLength(13)]],
      expiryMonth: ['', [Validators.required]],
      expiryYear: ['', [Validators.required]],
      cvv: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(4)]],
      cardholderName: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  private setupFormValidation(): void {
    // Card number validation and formatting
    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {
      if (value) {
        const formatted = formatCardNumber(value);
        const isValid = validateCardNumber(value);
        const type = detectCardType(value);
        
        this.cardNumberValid.set(isValid);
        this.cardType.set(type);
        
        // Update form value without triggering valueChanges again
        if (formatted !== value) {
          this.paymentForm.get('cardNumber')?.setValue(formatted, { emitEvent: false });
        }
      } else {
        this.cardNumberValid.set(false);
        this.cardType.set('');
      }
    });

    // Expiry date validation
    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(() => this.validateExpiry());
    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(() => this.validateExpiry());

    // CVV validation
    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {
      this.cvvValid.set(value ? validateCVV(value) : false);
    });

    // Name validation
    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {
      this.nameValid.set(value ? value.trim().length >= 2 : false);
    });
  }

  private validateExpiry(): void {
    const month = parseInt(this.paymentForm.get('expiryMonth')?.value);
    const year = parseInt(this.paymentForm.get('expiryYear')?.value);
    
    if (month && year) {
      this.expiryValid.set(validateExpiryDate(month, year));
    } else {
      this.expiryValid.set(false);
    }
  }

  private trackDialogOpened(): void {
    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {
      GuestUserId: this.guestUserId.toString(),
      TotalAmount: this.totalAmount.toString(),
      CanteenId: this.canteenId.toString(),
      ItemCount: (this.orderItems?.length || 0).toString()
    });
  }

  isFormValid(): boolean {
    return this.cardNumberValid() && 
           this.expiryValid() && 
           this.cvvValid() && 
           this.nameValid() &&
           this.paymentForm.valid;
  }

  async processPayment(): Promise<void> {
    if (!this.isFormValid() || this.isProcessing()) {
      return;
    }

    await this.processPaymentWithRetry();
  }

  private async processPaymentWithRetry(retryCount: number = 0): Promise<void> {
    const maxRetries = 2;

    this.isProcessing.set(true);
    this.errorMessage.set(null);

    try {
      // Create payment request
      const paymentRequest: GuestPaymentRequest = this.createPaymentRequest();

      // Track payment attempt
      this.appInsightsService.TrackEvent('GuestPaymentAttempt', {
        GuestUserId: this.guestUserId.toString(),
        Amount: this.totalAmount.toString(),
        CardType: this.cardType(),
        RetryCount: retryCount.toString()
      });

      // Process payment and create order with timeout
      const response = await Promise.race([
        this.guestPaymentService.processPaymentAndCreateOrder(paymentRequest).toPromise(),
        this.createTimeoutPromise(30000) // 30 second timeout
      ]);

      if (response?.isSuccess) {
        this.paymentSuccess.set(true);

        // Track success
        this.appInsightsService.TrackEvent('GuestPaymentSuccess', {
          OrderId: response.orderId,
          Amount: response.amountCharged.toString(),
          GuestUserId: this.guestUserId.toString(),
          RetryCount: retryCount.toString()
        });

        // Close dialog with success result
        setTimeout(() => {
          this.dialogRef.close({
            success: true,
            orderId: response.orderId,
            message: response.message
          } as GuestPaymentDialogResult);
        }, 2000);

      } else {
        this.handlePaymentError(response?.errorCode, response?.message);
      }

    } catch (error: any) {
      // Check if this is a network error that can be retried
      const isRetryableError = this.isRetryableError(error);

      if (isRetryableError && retryCount < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          this.processPaymentWithRetry(retryCount + 1);
        }, delay);
        return;
      }

      // Handle final error
      if (error.name === 'TimeoutError') {
        this.handlePaymentError('PAYMENT_TIMEOUT', 'Payment processing timed out. Please try again.');
      } else if (this.isNetworkError(error)) {
        this.handlePaymentError('NETWORK_ERROR', 'Network connection issue. Please check your connection and try again.');
      } else {
        this.handlePaymentError('PROCESSING_ERROR', 'An unexpected error occurred. Please try again.');
      }

      this.handleErrorFromService(error);
    } finally {
      this.isProcessing.set(false);
    }
  }

  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        const error = new Error('Payment processing timed out');
        error.name = 'TimeoutError';
        reject(error);
      }, timeout);
    });
  }

  private isRetryableError(error: any): boolean {
    // Network errors, timeouts, and 5xx server errors are retryable
    return this.isNetworkError(error) ||
           error.name === 'TimeoutError' ||
           (error.status >= 500 && error.status < 600);
  }

  private isNetworkError(error: any): boolean {
    return !navigator.onLine ||
           error.name === 'NetworkError' ||
           error.status === 0 ||
           error.message?.includes('network') ||
           error.message?.includes('connection');
  }

  private createPaymentRequest(): GuestPaymentRequest {
    const formValue = this.paymentForm.value;

    // Security: Sanitize and validate input data
    const sanitizedCardNumber = this.sanitizeCardNumber(formValue.cardNumber);
    const sanitizedCardholderName = this.sanitizeCardholderName(formValue.cardholderName);
    const sanitizedCVV = this.sanitizeCVV(formValue.cvv);

    return {
      cardNumber: sanitizedCardNumber,
      expiryMonth: parseInt(formValue.expiryMonth),
      expiryYear: parseInt(formValue.expiryYear),
      cvv: sanitizedCVV,
      cardholderName: sanitizedCardholderName,
      amount: this.totalAmount,
      canteenId: this.canteenId,
      guestUserId: this.guestUserId,
      items: this.orderItems.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        price: item.itemPriceIncGst, // Use the correct property name from CartItem
        itemName: item.name,
        itemDescription: item.name // Use name as description if no separate description
      })),
      orderDate: this.data.orderDate,
      menuId: this.data.menuId,
      menuType: this.data.menuType
    };
  }

  private sanitizeCardNumber(cardNumber: string): string {
    if (!cardNumber) return '';
    // Remove all non-digit characters
    return cardNumber.replace(/\D/g, '');
  }

  private sanitizeCardholderName(name: string): string {
    if (!name) return '';
    // Remove special characters, keep only letters, spaces, hyphens, and apostrophes
    return name.replace(/[^a-zA-Z\s\-']/g, '').trim();
  }

  private sanitizeCVV(cvv: string): string {
    if (!cvv) return '';
    // Remove all non-digit characters
    return cvv.replace(/\D/g, '');
  }

  private handlePaymentError(errorCode?: string, message?: string): void {
    let errorMsg = message || 'Payment processing failed. Please try again.';

    // Provide user-friendly error messages based on error codes
    switch (errorCode) {
      case 'VALIDATION_ERROR':
        errorMsg = 'Please check your payment details and try again.';
        break;
      case 'INVALID_CARD':
        errorMsg = 'Invalid card details. Please check your card information.';
        break;
      case 'PAYMENT_DECLINED':
        errorMsg = 'Your payment was declined. Please try a different card.';
        break;
      case 'INSUFFICIENT_FUNDS':
        errorMsg = 'Insufficient funds. Please check your card balance.';
        break;
      case 'PAYMENT_TIMEOUT':
        errorMsg = 'Payment timed out. Please try again.';
        break;
      case 'NETWORK_ERROR':
        errorMsg = 'Network connection issue. Please check your connection and try again.';
        break;
      case 'PERMISSION_DENIED':
        errorMsg = 'You do not have permission to make this payment.';
        break;
      case 'CANTEEN_NOT_FOUND':
        errorMsg = 'Unable to process payment for this location.';
        break;
      case 'PROCESSING_ERROR':
      default:
        errorMsg = message || 'An unexpected error occurred. Please try again or contact support.';
        break;
    }

    this.errorMessage.set(errorMsg);

    // Track failure with additional context
    this.appInsightsService.TrackEvent('GuestPaymentFailure', {
      ErrorCode: errorCode || 'UNKNOWN',
      ErrorMessage: errorMsg,
      OriginalMessage: message || '',
      GuestUserId: this.guestUserId.toString(),
      Amount: this.totalAmount.toString(),
      CardType: this.cardType()
    });
  }

  closeDialog(): void {
    this.appInsightsService.TrackEvent('GuestPaymentDialogClosed', {
      GuestUserId: this.guestUserId.toString(),
      PaymentCompleted: this.paymentSuccess().toString()
    });

    this.dialogRef.close({
      success: false,
      message: 'Payment cancelled'
    } as GuestPaymentDialogResult);
  }

  // Helper methods for template
  getCardTypeIcon(): string {
    switch (this.cardType()) {
      case 'Visa': return 'credit_card';
      case 'Mastercard': return 'credit_card';
      case 'American Express': return 'credit_card';
      default: return 'credit_card';
    }
  }

  getCardTypeClass(): string {
    return `card-type-${this.cardType().toLowerCase().replace(/\s/g, '-')}`;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  }
}
