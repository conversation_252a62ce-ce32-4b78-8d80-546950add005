import { Component, Inject, OnInit, signal } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BaseComponent } from '../../../sharedModels';
import { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';
import { CashlessAppInsightsService } from '../../../sharedServices';
import {
  GuestPaymentDialogData,
  GuestPaymentDialogResult,
  GuestPaymentRequest,
  validateCardNumber,
  validateExpiryDate,
  validateCVV,
  detectCardType,
  formatCardNumber
} from '../../../sharedModels/guest-payment/guest-payment.models';

@Component({
  selector: 'app-guest-payment-dialog',
  templateUrl: './guest-payment-dialog.component.html',
  styleUrls: ['./guest-payment-dialog.component.scss']
})
export class GuestPaymentDialogComponent extends BaseComponent implements OnInit {
  
  paymentForm: FormGroup;
  isProcessing = signal<boolean>(false);
  isValidating = signal<boolean>(false);
  paymentSuccess = signal<boolean>(false);
  errorMessage = signal<string | null>(null);
  cardType = signal<string>('');
  
  // Form validation states
  cardNumberValid = signal<boolean>(false);
  expiryValid = signal<boolean>(false);
  cvvValid = signal<boolean>(false);
  nameValid = signal<boolean>(false);
  
  // Available options
  expiryMonths = this.guestPaymentService.getExpiryMonths();
  expiryYears = this.guestPaymentService.getExpiryYears();
  
  // Order details
  totalAmount: number;
  orderItems: any[];
  guestUserId: number;
  canteenId: number;

  constructor(
    private fb: FormBuilder,
    private guestPaymentService: GuestPaymentService,
    private appInsightsService: CashlessAppInsightsService,
    public dialogRef: MatDialogRef<GuestPaymentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GuestPaymentDialogData
  ) {
    super();
    this.initializeFormData();
    this.createPaymentForm();
  }

  ngOnInit(): void {
    this.setupFormValidation();
    this.trackDialogOpened();
  }

  private initializeFormData(): void {
    this.totalAmount = this.data.totalAmount;
    this.orderItems = this.data.orders;
    this.guestUserId = this.data.selectedStudent?.UserId;
    this.canteenId = this.data.canteenId;
  }

  private createPaymentForm(): void {
    this.paymentForm = this.fb.group({
      cardNumber: ['', [Validators.required, Validators.minLength(13)]],
      expiryMonth: ['', [Validators.required]],
      expiryYear: ['', [Validators.required]],
      cvv: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(4)]],
      cardholderName: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  private setupFormValidation(): void {
    // Card number validation and formatting
    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {
      if (value) {
        const formatted = formatCardNumber(value);
        const isValid = validateCardNumber(value);
        const type = detectCardType(value);
        
        this.cardNumberValid.set(isValid);
        this.cardType.set(type);
        
        // Update form value without triggering valueChanges again
        if (formatted !== value) {
          this.paymentForm.get('cardNumber')?.setValue(formatted, { emitEvent: false });
        }
      } else {
        this.cardNumberValid.set(false);
        this.cardType.set('');
      }
    });

    // Expiry date validation
    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(() => this.validateExpiry());
    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(() => this.validateExpiry());

    // CVV validation
    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {
      this.cvvValid.set(value ? validateCVV(value) : false);
    });

    // Name validation
    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {
      this.nameValid.set(value ? value.trim().length >= 2 : false);
    });
  }

  private validateExpiry(): void {
    const month = parseInt(this.paymentForm.get('expiryMonth')?.value);
    const year = parseInt(this.paymentForm.get('expiryYear')?.value);
    
    if (month && year) {
      this.expiryValid.set(validateExpiryDate(month, year));
    } else {
      this.expiryValid.set(false);
    }
  }

  private trackDialogOpened(): void {
    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {
      GuestUserId: this.guestUserId,
      TotalAmount: this.totalAmount,
      CanteenId: this.canteenId,
      ItemCount: this.orderItems?.length || 0
    });
  }

  isFormValid(): boolean {
    return this.cardNumberValid() && 
           this.expiryValid() && 
           this.cvvValid() && 
           this.nameValid() &&
           this.paymentForm.valid;
  }

  async processPayment(): Promise<void> {
    if (!this.isFormValid() || this.isProcessing()) {
      return;
    }

    this.isProcessing.set(true);
    this.errorMessage.set(null);

    try {
      // Create payment request
      const paymentRequest: GuestPaymentRequest = this.createPaymentRequest();

      // Track payment attempt
      this.appInsightsService.TrackEvent('GuestPaymentAttempt', {
        GuestUserId: this.guestUserId,
        Amount: this.totalAmount,
        CardType: this.cardType()
      });

      // Process payment and create order
      const response = await this.guestPaymentService.processPaymentAndCreateOrder(paymentRequest).toPromise();

      if (response?.isSuccess) {
        this.paymentSuccess.set(true);
        
        // Track success
        this.appInsightsService.TrackEvent('GuestPaymentSuccess', {
          OrderId: response.orderId,
          Amount: response.amountCharged,
          GuestUserId: this.guestUserId
        });

        // Close dialog with success result
        setTimeout(() => {
          this.dialogRef.close({
            success: true,
            orderId: response.orderId,
            message: response.message
          } as GuestPaymentDialogResult);
        }, 2000);

      } else {
        this.handlePaymentError(response?.errorCode, response?.message);
      }

    } catch (error) {
      this.handlePaymentError('NETWORK_ERROR', 'Network error occurred. Please try again.');
      this.handleErrorFromService(error);
    } finally {
      this.isProcessing.set(false);
    }
  }

  private createPaymentRequest(): GuestPaymentRequest {
    const formValue = this.paymentForm.value;
    
    return {
      cardNumber: formValue.cardNumber.replace(/\s/g, ''), // Remove spaces
      expiryMonth: parseInt(formValue.expiryMonth),
      expiryYear: parseInt(formValue.expiryYear),
      cvv: formValue.cvv,
      cardholderName: formValue.cardholderName.trim(),
      amount: this.totalAmount,
      canteenId: this.canteenId,
      guestUserId: this.guestUserId,
      items: this.orderItems.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        price: item.price,
        itemName: item.name || item.itemName,
        itemDescription: item.description || item.itemDescription
      })),
      orderDate: this.data.orderDate,
      menuId: this.data.menuId,
      menuType: this.data.menuType
    };
  }

  private handlePaymentError(errorCode?: string, message?: string): void {
    const errorMsg = message || 'Payment processing failed. Please try again.';
    this.errorMessage.set(errorMsg);

    // Track failure
    this.appInsightsService.TrackEvent('GuestPaymentFailure', {
      ErrorCode: errorCode,
      ErrorMessage: errorMsg,
      GuestUserId: this.guestUserId
    });
  }

  closeDialog(): void {
    this.appInsightsService.TrackEvent('GuestPaymentDialogClosed', {
      GuestUserId: this.guestUserId,
      PaymentCompleted: this.paymentSuccess()
    });

    this.dialogRef.close({
      success: false,
      message: 'Payment cancelled'
    } as GuestPaymentDialogResult);
  }

  // Helper methods for template
  getCardTypeIcon(): string {
    switch (this.cardType()) {
      case 'Visa': return 'credit_card';
      case 'Mastercard': return 'credit_card';
      case 'American Express': return 'credit_card';
      default: return 'credit_card';
    }
  }

  getCardTypeClass(): string {
    return `card-type-${this.cardType().toLowerCase().replace(/\s/g, '-')}`;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  }
}
