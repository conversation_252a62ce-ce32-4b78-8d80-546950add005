{"version": 3, "file": "src_app_authentication_authentication_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;AACuD;AAEgC;;;AAEvF,MAAMI,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,gEAAuB;EAClCM,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE;GACb,EACD;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEJ,uDAAc;IACzBQ,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAI;GACpB,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEJ,uDAAc;IACzBQ,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAK;GACrB,EACD;IACEN,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEH,uDAAcA;GAC1B;CAEJ,CACF;AAMK,MAAOS,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BZ,yDAAY,CAACa,QAAQ,CAACT,MAAM,CAAC,EAC7BJ,yDAAY;IAAA;EAAA;;;sHAEXY,2BAA2B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF5BhB,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AClCuB;AACmB;AAElE;AAC2D;AACI;AACG;AACT;AAEqB;AACV;AAEpE;AAC+F;AAC3B;;AAgB9D,MAAOwB,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAZ7BP,yDAAY,EACZL,uFAA2B;MAC3B;MACAM,uDAAW,EACXC,+DAAmB;MACnB;MACAG,4EAAkB,EAClBD,yEAAiB,EACjBE,mEAAc,EACdH,sEAAe;IAAA;EAAA;;;sHAGNI,oBAAoB;IAAAC,YAAA,GAdhBvB,6EAAc,EAAED,wGAAuB,EAAEE,6EAAc;IAAAW,OAAA,GAEpEG,yDAAY,EACZL,uFAA2B;IAC3B;IACAM,uDAAW,EACXC,+DAAmB;IACnB;IACAG,4EAAkB,EAClBD,yEAAiB,EACjBE,mEAAc,EACdH,sEAAe;EAAA;AAAA;;;;;;;;;;;;;;;;;AC3BwC;;;AAOrD,MAAOnB,uBAAuB;EAKlC0B,YAAA,GAAe;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAGH,qEAAW,CAACI,UAAU;EAC1C;;;uBATW7B,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRpCE,4DAAA,aAAuB;UAKfA,uDAAA,aAAyF;UAC3FA,0DAAA,EAAM;UAIVA,4DAAA,aAAwC;UAEpCA,uDAAA,oBAA+B;UACjCA,0DAAA,EAAM;UAERA,4DAAA,aAAwC;UAEXA,oDAAA,IAAyB;UAAAA,0DAAA,EAAO;;;UAAhCA,uDAAA,IAAyB;UAAzBA,gEAAA,cAAAD,GAAA,CAAAT,UAAA,KAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBhB;AACkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCU;AAI3B;AAEkB;AACA;AAGkC;AAErB;AACpC;AAOH;;;;;;;;;;;;;IChBzBU,4DAAA,gBAAqC;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAY;;;;IAA5CA,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAqB,OAAA,CAAAC,wBAAA,GAAgC;;;;;IAHvEtB,4DAAA,wBAAwD;IAC3CA,oDAAA,iBAAU;IAAAA,0DAAA,EAAY;IACjCA,uDAAA,gBAA4F;IAC5FA,wDAAA,IAAAwB,oDAAA,uBAAiF;IACnFxB,0DAAA,EAAiB;;;;IADHA,uDAAA,GAAuB;IAAvBA,wDAAA,SAAA0B,MAAA,CAAAC,SAAA,CAAAC,OAAA,CAAuB;;;;;IAKnC5B,4DAAA,gBAAoC;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAY;;;;IAA3CA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAA6B,OAAA,CAAAC,uBAAA,GAA+B;;;;;IAHrE9B,4DAAA,wBAAwD;IAC3CA,oDAAA,gBAAS;IAAAA,0DAAA,EAAY;IAChCA,uDAAA,gBAA0F;IAC1FA,wDAAA,IAAA+B,oDAAA,uBAA+E;IACjF/B,0DAAA,EAAiB;;;;IADHA,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAgC,MAAA,CAAAC,QAAA,CAAAL,OAAA,CAAsB;;;;;IAYlC5B,4DAAA,gBAAkC;IAAAA,oDAAA,GAA6B;IAAAA,0DAAA,EAAY;;;;IAAzCA,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAkC,OAAA,CAAAC,qBAAA,GAA6B;;;;;;IAVjEnC,4DAAA,wBAAwD;IAC3CA,oDAAA,oBAAa;IAAAA,0DAAA,EAAY;IACpCA,4DAAA,gBAOE;IAFAA,wDAAA,mBAAAqC,gEAAA;MAAArC,2DAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAAxC,2DAAA;MAAA,OAASA,yDAAA,CAAAwC,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAL/B3C,0DAAA,EAOE;IACFA,wDAAA,IAAA4C,oDAAA,uBAA2E;IAC7E5C,0DAAA,EAAiB;;;;IADHA,uDAAA,GAAoB;IAApBA,wDAAA,SAAA6C,MAAA,CAAAC,MAAA,CAAAlB,OAAA,CAAoB;;;;;IAKhC5B,4DAAA,gBAAiC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAY;;;;IAAxCA,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA+C,MAAA,CAAAC,oBAAA,GAA4B;;;;;IAK7DhD,4DAAA,gBAAoC;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAY;;;;IAA3CA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAiD,MAAA,CAAAC,uBAAA,GAA+B;;;;;IAqBnElD,4DAAA,gBAAoE;IAAAA,oDAAA,GAElE;IAAAA,0DAAA,EAAY;;;;IAFsDA,uDAAA,GAElE;IAFkEA,+DAAA,CAAAmD,OAAA,CAAAC,oBAAA,GAElE;;;;;IArBJpD,4DAAA,cAA0C;IAEtCA,uDAAA,uBAA+D;IAC/DA,4DAAA,WAAM;IACJA,oDAAA,uCACA;IAAAA,4DAAA,YAAsC;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAI;IAAAA,oDAAA,SAC5D;IAAAA,4DAAA,YAAsC;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAI;IACxDA,oDAAA,aACA;IAAAA,4DAAA,aAAsC;IAAAA,oDAAA,gCAAwB;IAAAA,0DAAA,EAAI;IAAAA,oDAAA,UACpE;IAAAA,0DAAA,EAAO;IAETA,4DAAA,eAA+B;IAC7BA,uDAAA,wBAA6D;IAC7DA,4DAAA,YAAM;IACJA,oDAAA,iKAEA;IAAAA,4DAAA,aAAsC;IAAAA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAI;IAAAA,oDAAA,UAC9D;IAAAA,0DAAA,EAAO;IAETA,wDAAA,KAAAqD,2CAAA,uBAEc;IAChBrD,0DAAA,EAAM;;;;IAjBmBA,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAsD,MAAA,CAAAC,SAAA,EAAAvD,2DAAA,CAAkB;IAClBA,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAsD,MAAA,CAAAC,SAAA,EAAAvD,2DAAA,CAAkB;IAElBA,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAsD,MAAA,CAAAC,SAAA,EAAAvD,2DAAA,CAAkB;IAQlBA,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAsD,MAAA,CAAAC,SAAA,EAAAvD,2DAAA,CAAkB;IAG7BA,uDAAA,GAAsD;IAAtDA,wDAAA,SAAAsD,MAAA,CAAAG,eAAA,CAAA7B,OAAA,IAAA0B,MAAA,CAAAI,aAAA,CAAA9B,OAAA,CAAsD;;;;;IAOpE5B,4DAAA,cAAsD;IACXA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAI;;;;;IAG/DA,4DAAA,gBAAgC;IAC9BA,uDAAA,SAAM;IACNA,oDAAA,GACF;IAAAA,0DAAA,EAAY;;;;IADVA,uDAAA,GACF;IADEA,gEAAA,MAAA2D,MAAA,CAAAC,YAAA,MACF;;;;;IAOF5D,4DAAA,eACG;IAAAA,oDAAA,gCACD;IAAAA,4DAAA,QAAG;IAAuBA,oDAAA,aAAM;IAAAA,0DAAA,EAAI;;;;;IAEtCA,4DAAA,eAA8C;IAAAA,oDAAA,mCAA4B;IAAAA,0DAAA,EAAO;;;;;IACjFA,4DAAA,iBAAqF;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAS;;;;IAA9FA,wDAAA,YAAA6D,OAAA,CAAAC,MAAA,CAAkB;;;ADvCxB,MAAOnG,cAAe,SAAQkD,+DAAa;EAU/CzB,YACS2E,MAAuB,EACtBC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,kBAAsC;IAE9C,KAAK,EAAE;IATA,KAAAP,MAAM,GAANA,MAAM;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAZ5B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAhB,SAAS,GAAW5C,qEAA0B;EAc9C;EAEAtB,QAAQA,CAAA;IACN,IAAI,CAAC2E,KAAK,CAAC7F,IAAI,CAACqG,SAAS,CAAErG,IAAwB,IAAI;MACrD,IAAI,CAACsG,SAAS,GAAGtG,IAAI,CAACC,KAAK;MAC3B,IAAI,CAACsG,WAAW,EAAE;IACpB,CAAC,CAAC;IAEF,IAAI,CAACP,WAAW,CAACQ,YAAY,EAAE;EACjC;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,WAAW,EAAE;;EAE3C;EAEA;EACA;EACA;EACA,IAAIC,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC;EAC/B;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACF,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;EAClC;EAEA,IAAItD,SAASA,CAAA;IACX,OAAO,IAAI,CAACqD,IAAI,CAACC,GAAG,CAAC,WAAW,CAAC;EACnC;EAEA,IAAIhD,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC+C,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;EAClC;EAEA,IAAInC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACkC,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC;EAChC;EAEA,IAAIxB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACuB,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;EACzC;EAEA,IAAIvB,aAAaA,CAAA;IACf,OAAO,IAAI,CAACsB,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC;EACvC;EAEAjC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACgC,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,GAC9C,wBAAwB,GACxB,IAAI,CAACH,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,CAACE,QAAQ,CAAC,OAAO,CAAC,GACxC,mBAAmB,GACnB,EAAE;EACR;EAEAjC,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACgC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACtC,OAAO,wBAAwB;KAChC,MAAM,IAAI,IAAI,CAACD,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC9C,OAAO,gCAAgC;KACxC,MAAM;MACL,OAAO,EAAE;;EAEb;EAEA7D,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC0D,IAAI,CAACC,GAAG,CAAC,WAAW,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,GAAG,wBAAwB,GAAG,EAAE;EACxF;EAEArD,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACkD,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,GAAG,wBAAwB,GAAG,EAAE;EACvF;EAEAhD,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAC6C,IAAI,CAACC,GAAG,CAAC,QAAQ,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;MAChD,OAAO,wBAAwB;;IAGjC,OAAO,sCAAsC;EAC/C;EAEA/B,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC4B,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,IAC1D,IAAI,CAACH,IAAI,CAACC,GAAG,CAAC,eAAe,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,GACjD,yCAAyC,GACzC,EAAE;EACR;EAEA;EACA;EACA;EACAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,IAAI,CAACK,KAAK,EAAE;MACnB,IAAI,CAACC,aAAa,EAAE;MAEpB;MACA,IAAI,CAACpB,cAAc,CAACqB,KAAK,EAAE;MAE3B,IAAI,IAAI,CAACd,SAAS,EAAE;QAClB,IAAI,CAACe,MAAM,EAAE;OACd,MAAM;QACL,IAAI,CAACC,MAAM,EAAE;;;EAGnB;EAEA;EACQD,MAAMA,CAAA;IAEZ,IAAI,CAACzB,MAAM,CACR2B,0BAA0B,CAAC,IAAI,CAACX,KAAK,CAACY,KAAK,EAAE,IAAI,CAACT,QAAQ,CAACS,KAAK,CAAC,CACjEC,IAAI,CAACC,KAAK,IAAG;MACZ,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;IAC7B,CAAC,CAAC,CACDE,KAAK,CAACC,KAAK,IAAG;MACb,IAAI,CAACC,YAAY,CAACD,KAAK,CAAC;IAC1B,CAAC,CAAC;EACN;EAEA;EACQP,MAAMA,CAAA;IAEZ;IACA,IAAIS,OAAO,GAAsB,IAAIpF,mEAAiB,EAAE;IACxDoF,OAAO,CAACC,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACY,KAAK;IAChCO,OAAO,CAACE,QAAQ,GAAG,IAAI,CAAClB,QAAQ,CAACS,KAAK;IACtCO,OAAO,CAACG,IAAI,GAAGzF,uDAAK,CAAC0F,MAAM;IAC3BJ,OAAO,CAACK,SAAS,GAAG,IAAI,CAAC5E,SAAS,CAACgE,KAAK;IACxCO,OAAO,CAACM,QAAQ,GAAG,IAAI,CAACvE,QAAQ,CAAC0D,KAAK;IACtCO,OAAO,CAACO,MAAM,GAAG,IAAI,CAACnC,kBAAkB,CAACoC,kBAAkB,CAAC,IAAI,CAAC5D,MAAM,CAAC6C,KAAK,CAAC;IAE9E,IAAI,CAACxB,WAAW,CAACwC,YAAY,CAACT,OAAO,CAAC,CAAC1B,SAAS,CAAC;MAC/CoC,IAAI,EAAGC,QAAsB,IAAI;QAC/B,IAAI,CAACrB,MAAM,EAAE;MACf,CAAC;MACDQ,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACc,YAAY,CAACd,KAAK,CAAC;MAC1B;KACD,CAAC;EACJ;EAEA;EACQC,YAAYA,CAACD,KAAoB;IAEvC,IAAGA,KAAK,CAACe,IAAI,IAAI,uBAAuB,IAAIf,KAAK,CAACe,IAAI,IAAI,qBAAqB,IAAIf,KAAK,CAACe,IAAI,IAAI,yBAAyB,IAAIf,KAAK,CAACe,IAAI,IAAK,qBAAqB,EAAC;MACjK,IAAI,CAACnD,YAAY,GAAG,yDAAyD;KAC9E,MAAI;MACF,IAAI,CAACA,YAAY,GAAG,6BAA6B;;IAGpD,IAAI,CAACM,cAAc,CAAC8C,IAAI,EAAE;EAC5B;EAEA;EACA;EACA;EACQF,YAAYA,CAACd,KAAU;IAC7B,IAAI,CAAC9B,cAAc,CAAC8C,IAAI,EAAE;IAC1B,IAAI,CAACpD,YAAY,GAAG,IAAI,CAACqD,WAAW,CAACjB,KAAK,CAAC;EAC7C;EAEA;EACQF,eAAeA,CAACD,KAAmC;IACzD;IAEA,IAAI,CAACqB,yBAAyB,EAAE;IAChC,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACA,QAAQ,GAAGtB,KAAK,CAACuB,IAAI;IAC1B,IAAI,CAACD,QAAQ,CAACE,UAAU,EAAE,CAACzB,IAAI,CAAC0B,KAAK,IAAG;MACtC,IAAI,CAAClD,WAAW,CAACmD,QAAQ,CAACD,KAAK,CAAC;MAChC,IAAI,CAACE,QAAQ,EAAE;IACjB,CAAC,CAAC;IAEF;IAEA;IACA,IAAI,CAACtD,cAAc,CAAC8C,IAAI,EAAE;IAC1B,IAAI,CAAC1B,aAAa,EAAE;EACtB;EAEQkC,QAAQA,CAAA;IACd,IAAI,CAACrD,WAAW,CACbsD,mBAAmB,CAAC,IAAI,CAACN,QAAQ,CAACO,GAAG,CAAC,CACtCC,IAAI,CAACjH,uDAAO,CAAC,IAAI,CAAC,CAAC,CACnB8D,SAAS,CAAC;MACToC,IAAI,EAAGgB,YAA0B,IAAI;QACnC,IAAIA,YAAY,EAAE;UAChB,IAAI,CAACzD,WAAW,CAAC0D,gBAAgB,CAACD,YAAY,CAAC;UAC/C,IAAI,CAACE,aAAa,CAACF,YAAY,CAAC;;MAEpC,CAAC;MACD5B,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC9B,cAAc,CAAC8C,IAAI,EAAE;QAC1B,IAAI,CAACe,sBAAsB,CAAC/B,KAAK,CAAC;MACpC;KACD,CAAC;EACN;EAEQkB,yBAAyBA,CAAA;IAC/B,IAAIc,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAErD,IAAIF,YAAY,IAAI7I,qEAAW,CAACI,UAAU,EAAE;MAC1C0I,YAAY,CAACE,KAAK,EAAE;MACpBF,YAAY,CAACG,OAAO,CAAC,YAAY,EAAEjJ,qEAAW,CAACI,UAAU,CAAC;;EAE9D;EAEQuI,aAAaA,CAACV,IAAkB;IACtC,IAAIA,IAAI,CAACf,IAAI,IAAIzF,uDAAK,CAACyH,KAAK,EAAE;MAC5B,IAAI,CAAChE,MAAM,CAACiE,GAAG,CAAC,MAAM,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;KACxD,MAAM,IAAInB,IAAI,CAACf,IAAI,IAAIzF,uDAAK,CAAC4H,OAAO,EAAE;MACrC,IAAI,CAACnE,MAAM,CAACiE,GAAG,CAAC,MAAM,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;KAC1D,MAAM;MACL,IAAI,CAAClE,MAAM,CAACiE,GAAG,CAAC,MAAM,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE5D;EAEQ7D,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACD,SAAS,EAAE;MAClB,IAAI,CAACO,IAAI,GAAG,IAAIxE,qDAAS,CAAC;QACxBuE,KAAK,EAAE,IAAIxE,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,EAAEhI,sDAAU,CAACsE,KAAK,CAAC,CAAC;QACnEG,QAAQ,EAAE,IAAI3E,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,CAAC;OACpD,CAAC;KACH,MAAM;MACL,IAAI,CAACzD,IAAI,GAAG,IAAIxE,qDAAS,CAAC;QACxBuE,KAAK,EAAE,IAAIxE,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,EAAEhI,sDAAU,CAACsE,KAAK,CAAC,CAAC;QACnEG,QAAQ,EAAE,IAAI3E,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,EAAEhI,sDAAU,CAACiI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E/G,SAAS,EAAE,IAAIpB,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,CAAC,CAAC;QACrDxG,QAAQ,EAAE,IAAI1B,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,CAAC,CAAC;QACpD3F,MAAM,EAAE,IAAIvC,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,EAAEhI,sDAAU,CAACiI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5EjF,eAAe,EAAE,IAAIlD,uDAAW,CAAC,IAAI,EAAE,CAACE,sDAAU,CAACkI,YAAY,CAAC,CAAC;QACjEjF,aAAa,EAAE,IAAInD,uDAAW,CAAC,IAAI,EAAE,CAACE,sDAAU,CAACkI,YAAY,CAAC;OAC/D,CAAC;;EAEN;EAEA;EACA;EACA;EACAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnE,SAAS,EAAE;MAClB,OAAO,SAAS;KACjB,MAAM;MACL,OAAO,SAAS;;EAEpB;EAEA9B,iBAAiBA,CAAA;IACf,IAAIkG,GAAG,GAAG,IAAI,CAACvE,kBAAkB,CAACwE,mBAAmB,CAAC,IAAI,CAAChG,MAAM,CAAC6C,KAAK,CAAC;IACxE,IAAI,CAAC7C,MAAM,CAACiG,QAAQ,CAACF,GAAG,CAAC;EAC3B;EAEA;EACA;EACA;EACA;EACA;EAEA;EACQvD,aAAaA,CAAA;IACnB,IAAI,CAAC1B,YAAY,GAAG,IAAI;EAC1B;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACOqD,WAAWA,CAACjB,KAAU;IAC3B,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,IAAI;;IAGb;IAEA,IAAIA,KAAK,CAACgD,MAAM,EAAE;MAChB,IAAIC,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAClD,KAAK,CAACgD,MAAM,CAAC;MAElD;MACA,IAAI7H,+CAAO,CAAC8H,QAAQ,CAAC,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;QAC5C,OAAOF,QAAQ,CAAC,CAAC,CAAC;;;IAItB;IACA,IAAIjD,KAAK,CAACoD,OAAO,EAAE;MACjB,OAAOpD,KAAK,CAACoD,OAAO;;IAGtB,IAAIpD,KAAK,CAACqD,KAAK,EAAE;MACf,OAAOrD,KAAK,CAACqD,KAAK;;IAGpB;IACA,OAAO,6BAA6B;EACtC;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACQH,gBAAgBA,CAACF,MAAW;IAClC,IAAI,CAACA,MAAM,EAAE;MACX;;IAGF,IAAIC,QAAQ,GAAkB,EAAE;IAChCK,MAAM,CAACC,OAAO,CAACP,MAAM,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAG;MACnC,IAAItI,+CAAO,CAACsI,GAAG,CAAC,IAAIA,GAAG,CAACN,MAAM,GAAG,CAAC,IAAIhI,+CAAO,CAACsI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,CAACN,MAAM,GAAG,CAAC,EAAE;QAC1E;QACAF,QAAQ,CAACS,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5B,CAAC,CAAC;IAEF,OAAOR,QAAQ;EACjB;;;uBA/VWtL,cAAc,EAAAqC,+DAAA,CAAAxB,sEAAA,GAAAwB,+DAAA,CAAA6J,4DAAA,GAAA7J,+DAAA,CAAA6J,oDAAA,GAAA7J,+DAAA,CAAAgK,2DAAA,GAAAhK,+DAAA,CAAAgK,wDAAA,GAAAhK,+DAAA,CAAAmK,2FAAA,GAAAnK,+DAAA,CAAAA,iDAAA,GAAAA,+DAAA,CAAAgK,+DAAA;IAAA;EAAA;;;YAAdrM,cAAc;MAAA6B,SAAA;MAAA+K,QAAA,GAAAvK,wEAAA;MAAAP,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6K,wBAAA3K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzC3BE,4DAAA,aAAoC;UAEPA,wDAAA,sBAAA0K,iDAAA;YAAA,OAAY3K,GAAA,CAAAqF,QAAA,EAAU;UAAA,EAAC;UAC9CpF,wDAAA,IAAA2K,wCAAA,4BAIiB;UACjB3K,wDAAA,IAAA4K,wCAAA,4BAIiB;UACjB5K,wDAAA,IAAA6K,wCAAA,4BAWiB;UACjB7K,4DAAA,wBAAqC;UACxBA,oDAAA,YAAK;UAAAA,0DAAA,EAAY;UAC5BA,uDAAA,eAAoF;UACpFA,wDAAA,KAAA8K,oCAAA,uBAAyE;UAC3E9K,0DAAA,EAAiB;UACjBA,4DAAA,yBAAqC;UACxBA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAY;UAC/BA,uDAAA,gBAA6F;UAC7FA,wDAAA,KAAA+K,oCAAA,uBAA+E;UACjF/K,0DAAA,EAAiB;UACjBA,wDAAA,KAAAgL,8BAAA,kBAsBM;UAENhL,4DAAA,iBAA8D;UAAAA,oDAAA,IAAqB;UAAAA,0DAAA,EAAS;UAE5FA,wDAAA,KAAAiL,8BAAA,kBAEM;UAENjL,wDAAA,KAAAkL,oCAAA,uBAGY;UACdlL,0DAAA,EAAO;UAIXA,4DAAA,eAA6B;UAEzBA,wDAAA,KAAAmL,+BAAA,mBAGC;UACDnL,wDAAA,KAAAoL,+BAAA,mBAAiF;UACjFpL,wDAAA,KAAAqL,iCAAA,qBAAsG;UACxGrL,0DAAA,EAAM;;;UA/EEA,uDAAA,GAAkB;UAAlBA,wDAAA,cAAAD,GAAA,CAAAiF,IAAA,CAAkB;UACLhF,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA0E,SAAA,CAAgB;UAKhBzE,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA0E,SAAA,CAAgB;UAKhBzE,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA0E,SAAA,CAAgB;UAenBzE,uDAAA,GAAmB;UAAnBA,wDAAA,SAAAD,GAAA,CAAAgF,KAAA,CAAAnD,OAAA,CAAmB;UAKnB5B,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAD,GAAA,CAAAmF,QAAA,CAAAtD,OAAA,CAAsB;UAE9B5B,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA0E,SAAA,CAAgB;UAwBdzE,uDAAA,GAAkB;UAAlBA,wDAAA,YAAAD,GAAA,CAAA+D,MAAA,CAAkB;UAAoC9D,uDAAA,GAAqB;UAArBA,+DAAA,CAAAD,GAAA,CAAA6I,aAAA,GAAqB;UAE7E5I,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAA0E,SAAA,CAAe;UAITzE,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAD,GAAA,CAAA6D,YAAA,CAAkB;UAUH5D,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA0E,SAAA,CAAgB;UAIhBzE,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAA0E,SAAA,CAAe;UAChBzE,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAA0E,SAAA,CAAe;;;;;;mBDnDjC,CACV1D,6DAAO,CAAC,QAAQ,EAAE,CAChBC,gEAAU,CACR,QAAQ,EACRC,kEAAY,CAACC,+CAAM,EAAE;UACnB;UACAoK,MAAM,EAAE;YAAEC,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAC;SAChC,CAAC,CACH,CACF,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AErCiE;;;;;;;;;;;;ICChExL,4DAAA,gBAAiC;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAY;;;;IAAxCA,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA0B,MAAA,CAAAsB,oBAAA,GAA4B;;;;;IAG/DhD,4DAAA,gBAA0C;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAY;;;;IAA5BA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAgC,MAAA,CAAAyJ,UAAA,CAAgB;;;;;IAK1DzL,qEAAA,GAAgC;IAC9BA,4DAAA,QAAG;IACDA,oDAAA,2GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,WAA8B;IAC5BA,oDAAA,4EACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,QAAG;IACKA,oDAAA,oBAAa;IAAAA,4DAAA,WAAuB;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAI;IAACA,oDAAA,0CAAiC;IAAAA,0DAAA,EAAO;IAElGA,mEAAA,EAAe;;;;;IAIjBA,4DAAA,aAAwD;IAChDA,oDAAA,kBAAW;IAAAA,4DAAA,WAAuB;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAI;;;ADf/C,MAAOpC,cAAc;EAQzBwB,YACS2E,MAAuB,EACtBE,MAAc,EACd2H,kBAA8C,EAC9CzH,WAAwB;IAHzB,KAAAJ,MAAM,GAANA,MAAM;IACL,KAAAE,MAAM,GAANA,MAAM;IACN,KAAA2H,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAzH,WAAW,GAAXA,WAAW;IAVrB,KAAA0H,SAAS,GAAY,KAAK;IAE1B,KAAA7G,IAAI,GAAG,IAAIxE,qDAAS,CAAC;MACnBuE,KAAK,EAAE,IAAIxE,uDAAW,CAAC,EAAE,EAAE,CAACE,sDAAU,CAACgI,QAAQ,EAAEhI,sDAAU,CAACsE,KAAK,CAAC;KACnE,CAAC;EAOC;EAEH1F,QAAQA,CAAA,GAAI;EAEZ;EACA;EACA;EACA,IAAI0F,KAAKA,CAAA;IACP,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC;EAC/B;EAEAjC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACgC,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,CAACE,QAAQ,CAAC,UAAU,CAAC,GAC9C,wBAAwB,GACxB,IAAI,CAACH,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,CAACE,QAAQ,CAAC,OAAO,CAAC,GACxC,mBAAmB,GACnB,EAAE;EACR;EAEA;EACA;EACA;EAEM2G,aAAaA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,yJAAA;MACjBD,KAAI,CAACN,UAAU,GAAG,EAAE;MACpBM,KAAI,CAACF,SAAS,GAAG,IAAI;MACrBE,KAAI,CAAC5H,WAAW,CAAC8H,sBAAsB,CAACF,KAAI,CAAChH,KAAK,CAACY,KAAK,CAAC,CAACnB,SAAS,CAAC;QAClEoC,IAAI,EAAEiC,GAAG,IAAG;UACVkD,KAAI,CAACH,kBAAkB,CAACM,UAAU,CAAC,wBAAwB,EAAE;YAAE/F,KAAK,EAAE4F,KAAI,CAAChH,KAAK,CAACY;UAAK,CAAE,CAAC;QAC3F,CAAC;QACDK,KAAK,EAAEyD,GAAG,IAAG;UACXsC,KAAI,CAACF,SAAS,GAAG,KAAK;UACtBE,KAAI,CAACN,UAAU,GAAG,4EAA4E;QAChG;OACD,CAAC;IAAC;EACL;;;uBAhDW7N,cAAc,EAAAoC,+DAAA,CAAAxB,sEAAA,GAAAwB,+DAAA,CAAA6J,mDAAA,GAAA7J,+DAAA,CAAAgK,8EAAA,GAAAhK,+DAAA,CAAAgK,+DAAA;IAAA;EAAA;;;YAAdpM,cAAc;MAAA4B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwM,wBAAAtM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX3BE,4DAAA,cAA2E;UAAlDA,wDAAA,sBAAAqM,iDAAA;YAAA,OAAYtM,GAAA,CAAA+L,aAAA,EAAe;UAAA,EAAC;UACnD9L,4DAAA,qBAAgB;UACdA,uDAAA,eAAoF;UACpFA,wDAAA,IAAAsM,mCAAA,uBAAyE;UAC3EtM,0DAAA,EAAiB;UAEjBA,wDAAA,IAAAuM,mCAAA,uBAAsE;UACtEvM,4DAAA,QAAG;UAC8EA,oDAAA,YAAK;UAAAA,0DAAA,EAAS;UAG/FA,wDAAA,IAAAwM,sCAAA,2BAUe;UAEjBxM,0DAAA,EAAO;UAEPA,wDAAA,IAAAyM,6BAAA,iBAEM;;;UA3BAzM,wDAAA,cAAAD,GAAA,CAAAiF,IAAA,CAAkB;UAGRhF,uDAAA,GAAmB;UAAnBA,wDAAA,SAAAD,GAAA,CAAAgF,KAAA,CAAAnD,OAAA,CAAmB;UAGrB5B,uDAAA,GAA4B;UAA5BA,wDAAA,UAAAD,GAAA,CAAA0L,UAAA,kBAAA1L,GAAA,CAAA0L,UAAA,CAAAtC,MAAA,MAA4B;UAEgBnJ,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAD,GAAA,CAAAiF,IAAA,CAAAK,KAAA,CAAwB;UAGjErF,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAA8L,SAAA,CAAe;UAc1B7L,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAA8L,SAAA,CAAgB", "sources": ["./src/app/authentication/authentication-routing.module.ts", "./src/app/authentication/authentication.module.ts", "./src/app/authentication/components/authentication/authentication.component.ts", "./src/app/authentication/components/authentication/authentication.component.html", "./src/app/authentication/components/index.ts", "./src/app/authentication/components/login/login.component.ts", "./src/app/authentication/components/login/login.component.html", "./src/app/authentication/components/reset/reset.component.ts", "./src/app/authentication/components/reset/reset.component.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\nimport { AuthenticationComponent, LoginComponent, ResetComponent } from './components';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: AuthenticationComponent,\n    children: [\n      {\n        path: '',\n        pathMatch: 'full',\n        redirectTo: 'login',\n      },\n      {\n        path: 'login',\n        component: LoginComponent,\n        data: { login: true },\n      },\n      {\n        path: 'register',\n        component: LoginComponent,\n        data: { login: false },\n      },\n      {\n        path: 'reset',\n        component: ResetComponent,\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class AuthenticationRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\n// google material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\n\nimport { AuthenticationRoutingModule } from './authentication-routing.module';\nimport { LoginComponent } from './components/login/login.component';\n\n//import { CashlessCoreModule } from 'cashless-core';\nimport { AuthenticationComponent } from './components/authentication/authentication.component';\nimport { ResetComponent } from './components/reset/reset.component';\n@NgModule({\n  declarations: [LoginComponent, AuthenticationComponent, ResetComponent],\n  imports: [\n    CommonModule,\n    AuthenticationRoutingModule,\n    //CashlessCoreModule,\n    FormsModule,\n    ReactiveFormsModule,\n    // material\n    MatFormFieldModule,\n    MatCheckboxModule,\n    MatInputModule,\n    MatButtonModule,\n  ],\n})\nexport class AuthenticationModule {}\n", "import { Component, OnInit } from '@angular/core';\nimport { environment } from 'src/environments/environment';\n\n@Component({\n  selector: 'app-authentication',\n  templateUrl: './authentication.component.html',\n  styleUrls: ['./authentication.component.scss'],\n})\nexport class AuthenticationComponent implements OnInit {\n  signIn: boolean;\n  register: boolean;\n  appVersion: string;\n\n  constructor() {}\n\n  ngOnInit() {\n    this.appVersion = environment.AppVersion;\n  }\n}\n", "<div class=\"container\">\n  <!-- <div class=\"row justify-content-center\"> -->\n  <div class=\"row justify-content-center\">\n    <div class=\"col-12 col-sm-10 col-md-8 col-lg-6 col-xl-4\">\n      <div class=\"containerLogo\">\n        <img class=\"logo\" src=\"./assets/icons/spriggyschoolicon.png\" alt=\"spriggy school logo\" />\n      </div>\n    </div>\n  </div>\n\n  <div class=\"row justify-content-center\">\n    <div class=\"col-12 col-sm-10 col-md-8 col-lg-6 col-xl-4\">\n      <router-outlet></router-outlet>\n    </div>\n  </div>\n  <div class=\"row justify-content-center\">\n    <div class=\"col-12 col-sm-4 col-md-4 col-lg-2 versionRow\">\n      <span class=\"appVersion\">Version: {{ appVersion }}</span>\n    </div>\n  </div>\n</div>\n", "export * from './login/login.component';\nexport * from './authentication/authentication.component';\nexport * from './reset/reset.component';\n", "import { Component, OnInit, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\nimport { User } from 'firebase/auth';\nimport firebase from 'firebase/compat/app';\nimport { timeout } from 'rxjs/operators';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { SPRIGGY_SCHOOLS_TERMS_LINK } from 'src/constants';\n\nimport { SpinnerService, UserService, PhoneNumberService } from '../../../sharedServices';\nimport { UserCashless, Roles, BaseComponent, CreateUserRequest } from 'src/app/sharedModels';\n\nimport { trigger, transition, useAnimation } from '@angular/animations';\nimport { fadeIn } from 'ng-animate';\n\n// ngrx\nimport { Store } from '@ngrx/store';\nimport { AppState } from 'src/app/states';\nimport { SetConnectedUser } from 'src/app/states/user/user.actions';\nimport { AuthService } from 'src/app/sharedServices/authentication/auth.service';\nimport { isArray } from 'lodash';\nimport { FirebaseError } from 'firebase/app';\n\n@Component({\n  selector: 'authentication-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss'],\n  animations: [\n    trigger('bounce', [\n      transition(\n        '* => *',\n        useAnimation(fadeIn, {\n          // Set the duration to 5seconds and delay to 2seconds\n          params: { timing: 0.5, delay: 0 },\n        })\n      ),\n    ]),\n  ],\n})\nexport class LoginComponent extends BaseComponent implements OnInit, OnDestroy {\n  loginMode: boolean;\n  form: FormGroup;\n  private userFire: User;\n  bounce: any;\n  private _offlineSubscription: Subscription;\n  isOffline: boolean = false;\n  termsLink: string = SPRIGGY_SCHOOLS_TERMS_LINK;\n  public errorMessage: string;\n\n  constructor(\n    public afAuth: AngularFireAuth,\n    private route: ActivatedRoute,\n    private router: Router,\n    private spinnerService: SpinnerService,\n    private userService: UserService,\n    private authService: AuthService,\n    private ngZone: NgZone,\n    private phoneNumberService: PhoneNumberService\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    this.route.data.subscribe((data: { login: boolean }) => {\n      this.loginMode = data.login;\n      this._createForm();\n    });\n\n    this.userService.IdentifyUser();\n  }\n\n  ngOnDestroy() {\n    if (this._offlineSubscription) {\n      this._offlineSubscription.unsubscribe();\n    }\n  }\n\n  ////////////////////////////////////////\n  // Form\n  ////////////////////////////////////////\n  get email() {\n    return this.form.get('email');\n  }\n\n  get password() {\n    return this.form.get('password');\n  }\n\n  get firstname() {\n    return this.form.get('firstname');\n  }\n\n  get lastname() {\n    return this.form.get('lastname');\n  }\n\n  get mobile() {\n    return this.form.get('mobile');\n  }\n\n  get termsConditions() {\n    return this.form.get('termsConditions');\n  }\n\n  get ageConditions() {\n    return this.form.get('ageConditions');\n  }\n\n  getErrorMessageEmail() {\n    return this.form.get('email').hasError('required')\n      ? 'You must enter a value'\n      : this.form.get('email').hasError('email')\n      ? 'Not a valid email'\n      : '';\n  }\n\n  getErrorMessagePassword() {\n    if (this.password.hasError('required')) {\n      return 'You must enter a value';\n    } else if (this.password.hasError('minlength')) {\n      return 'Must be at least 14 characters';\n    } else {\n      return '';\n    }\n  }\n\n  getErrorMessageFirstname() {\n    return this.form.get('firstname').hasError('required') ? 'You must enter a value' : '';\n  }\n\n  getErrorMessageLastname() {\n    return this.form.get('lastname').hasError('required') ? 'You must enter a value' : '';\n  }\n\n  getErrorMessageMobile() {\n    if (this.form.get('mobile').hasError('required')) {\n      return 'You must enter a value';\n    }\n\n    return 'You must enter a valid mobile number';\n  }\n\n  getErrorMessageTerms() {\n    return this.form.get('termsConditions').hasError('required') ||\n      this.form.get('ageConditions').hasError('required')\n      ? 'You must agree to the Terms to continue'\n      : '';\n  }\n\n  ////////////////////////////////////////\n  // Log in / Sign up\n  ////////////////////////////////////////\n  onSubmit() {\n    if (this.form.valid) {\n      this.clearApiError();\n\n      // Start spinner\n      this.spinnerService.start();\n\n      if (this.loginMode) {\n        this.signIn();\n      } else {\n        this.signUp();\n      }\n    }\n  }\n\n  // Call Firebase to sign in\n  private signIn(): void {\n\n    this.afAuth\n      .signInWithEmailAndPassword(this.email.value, this.password.value)\n      .then(fuser => {\n        this.signInSucceeded(fuser);\n      })\n      .catch(error => {\n        this.signInFailed(error);\n      });\n  }\n\n  // Register a new User\n  private signUp(): void {\n\n    // Create all accounts as parents for the time being\n    let request: CreateUserRequest = new CreateUserRequest();\n    request.Email = this.email.value;\n    request.Password = this.password.value;\n    request.Role = Roles.Parent;\n    request.FirstName = this.firstname.value;\n    request.Lastname = this.lastname.value;\n    request.Mobile = this.phoneNumberService.serverMobileNumber(this.mobile.value);\n\n    this.userService.RegisterUser(request).subscribe({\n      next: (response: UserCashless) => {\n        this.signIn();\n      },\n      error: error => {\n        this.signUpFailed(error);\n      },\n    });\n  }\n\n  // Sign in failed\n  private signInFailed(error: FirebaseError): void {\n\n    if(error.code == 'auth/invalid-password' || error.code == 'auth/wrong-password' || error.code == 'auth/invalid-credential' || error.code  == 'auth/user-not-found'){\n      this.errorMessage = 'Sorry this email and password combination was incorrect'\n    }else{\n       this.errorMessage = 'Something has gone wrong...'\n    }\n\n    this.spinnerService.stop();\n  }\n\n  // Sign up failed\n  // - 4xx: Failed validations or authorisations\n  // - 5xx: API errors\n  private signUpFailed(error: any): void {\n    this.spinnerService.stop();\n    this.errorMessage = this.getApiError(error);\n  }\n\n  // Sign in an existing user\n  private signInSucceeded(fuser: firebase.auth.UserCredential): void {\n    // console.log(\"signInSucceeded(): Sign in user success - \" + JSON.stringify(fuser));\n\n    this._ClearStorageOnNewVersion();\n    this.userFire = null;\n    this.userFire = fuser.user;\n    this.userFire.getIdToken().then(token => {\n      this.authService.SetToken(token);\n      this._GetUser();\n    });\n\n    // console.log(\"signInSucceeded(): Clean up after sign in\");\n\n    // Will we get to these statements once a redirect is issued?\n    this.spinnerService.stop();\n    this.clearApiError();\n  }\n\n  private _GetUser() {\n    this.userService\n      .GetUserByFireBaseId(this.userFire.uid)\n      .pipe(timeout(5000))\n      .subscribe({\n        next: (userResponse: UserCashless) => {\n          if (userResponse) {\n            this.userService.SetUserConnected(userResponse);\n            this._RedirectUser(userResponse);\n          }\n        },\n        error: error => {\n          this.spinnerService.stop();\n          this.handleErrorFromService(error);\n        },\n      });\n  }\n\n  private _ClearStorageOnNewVersion() {\n    let savedVersion = localStorage.getItem('appVersion');\n\n    if (savedVersion != environment.AppVersion) {\n      localStorage.clear();\n      localStorage.setItem('appVersion', environment.AppVersion);\n    }\n  }\n\n  private _RedirectUser(user: UserCashless) {\n    if (user.Role == Roles.Admin) {\n      this.ngZone.run(() => this.router.navigate(['/admin']));\n    } else if (user.Role == Roles.Canteen) {\n      this.ngZone.run(() => this.router.navigate(['/canteen']));\n    } else {\n      this.ngZone.run(() => this.router.navigate(['/family']));\n    }\n  }\n\n  private _createForm() {\n    if (this.loginMode) {\n      this.form = new FormGroup({\n        email: new FormControl('', [Validators.required, Validators.email]),\n        password: new FormControl('', [Validators.required]),\n      });\n    } else {\n      this.form = new FormGroup({\n        email: new FormControl('', [Validators.required, Validators.email]),\n        password: new FormControl('', [Validators.required, Validators.minLength(14)]),\n        firstname: new FormControl('', [Validators.required]),\n        lastname: new FormControl('', [Validators.required]),\n        mobile: new FormControl('', [Validators.required, Validators.minLength(12)]),\n        termsConditions: new FormControl(true, [Validators.requiredTrue]),\n        ageConditions: new FormControl(true, [Validators.requiredTrue]),\n      });\n    }\n  }\n\n  ////////////////////////////////////////\n  // Get Text\n  ////////////////////////////////////////\n  GetTextSubmit(): string {\n    if (this.loginMode) {\n      return 'Sign In';\n    } else {\n      return 'Sign Up';\n    }\n  }\n\n  formatMobileInput() {\n    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);\n    this.mobile.setValue(res);\n  }\n\n  ////////////////////////////////////////\n  // API error handling                 //\n  //                                    //\n  // TODO - Move to a shared component  //\n  ////////////////////////////////////////\n\n  // Initialiate Schools API error handling\n  private clearApiError(): void {\n    this.errorMessage = null;\n  }\n\n  // // Process Schools API errors\n  // private processApiError(error: any): void {\n  //   this.error = error;\n  // }\n\n  // Fetch the first error message returned by the API. Expecting a structure that looks like this:\n  // {\n  //    \"errors\": {\n  //      \"Email\": [\"There's already an account with the email address: <EMAIL>\"]\n  //    },\n  //    \"type\": \"https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1\",\n  //    \"title\": \"One or more validation errors occurred.\",\n  //    \"status\": 400\n  // }\n  public getApiError(error: any): string {\n    if (!error) {\n      return null;\n    }\n\n    // console.log(\"getApiError(): Fetching API error from - \" + JSON.stringify(this.error));\n\n    if (error.errors) {\n      let messages = this.getErrorMessages(error.errors);\n\n      // Fetch the first error\n      if (isArray(messages) && messages.length > 0) {\n        return messages[0];\n      }\n    }\n\n    // Old format\n    if (error.message) {\n      return error.message;\n    }\n\n    if (error.title) {\n      return error.title;\n    }\n\n    // Return a generic message\n    return 'Something has gone wrong...';\n  }\n\n  // Get all the error messages in a structure that looks like this:\n  // {\n  //    \"errors\": {\n  //      \"Email\": [\"There's already an account with the email address: <EMAIL>\"]\n  //    },\n  //    \"type\": \"https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1\",\n  //    \"title\": \"One or more validation errors occurred.\",\n  //    \"status\": 400\n  // }\n  private getErrorMessages(errors: any): Array<string> {\n    if (!errors) {\n      return;\n    }\n\n    let messages: Array<string> = [];\n    Object.entries(errors).forEach(err => {\n      if (isArray(err) && err.length > 1 && isArray(err[1]) && err[1].length > 0) {\n        // Expecting err = [\"FieldName\" : [\"Some sort of error message\"]]\n        messages.push(err[1][0]);\n      }\n    });\n\n    return messages;\n  }\n}\n", "<div class=\"justify-content-center\">\n  <div class=\"container\">\n    <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"cashlessForm\">\n      <mat-form-field *ngIf=\"!loginMode\" appearance=\"outline\">\n        <mat-label>First name</mat-label>\n        <input matInput placeholder=\"First name\" formControlName=\"firstname\" type=\"text\" required />\n        <mat-error *ngIf=\"firstname.invalid\">{{ getErrorMessageFirstname() }}</mat-error>\n      </mat-form-field>\n      <mat-form-field *ngIf=\"!loginMode\" appearance=\"outline\">\n        <mat-label>Last name</mat-label>\n        <input matInput placeholder=\"Last name\" formControlName=\"lastname\" type=\"text\" required />\n        <mat-error *ngIf=\"lastname.invalid\">{{ getErrorMessageLastname() }}</mat-error>\n      </mat-form-field>\n      <mat-form-field *ngIf=\"!loginMode\" appearance=\"outline\">\n        <mat-label>Mobile number</mat-label>\n        <input\n          matInput\n          placeholder=\"Mobile number\"\n          formControlName=\"mobile\"\n          type=\"text\"\n          (keyup)=\"formatMobileInput()\"\n          required\n        />\n        <mat-error *ngIf=\"mobile.invalid\">{{ getErrorMessageMobile() }}</mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Email</mat-label>\n        <input matInput placeholder=\"Email\" formControlName=\"email\" type=\"email\" required />\n        <mat-error *ngIf=\"email.invalid\">{{ getErrorMessageEmail() }}</mat-error>\n      </mat-form-field>\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Password</mat-label>\n        <input matInput placeholder=\"Password\" formControlName=\"password\" type=\"password\" required />\n        <mat-error *ngIf=\"password.invalid\">{{ getErrorMessagePassword() }}</mat-error>\n      </mat-form-field>\n      <div *ngIf=\"!loginMode\" class=\"tcWrapper\">\n        <div class=\"subtext\" matPrefix>\n          <mat-checkbox formControlName=\"termsConditions\"></mat-checkbox>\n          <span>\n            I agree to the Spriggy Schools\n            <a target=\"_blank\" [href]=\"termsLink\">Terms & Conditions</a>,\n            <a target=\"_blank\" [href]=\"termsLink\">Privacy Policy</a>\n            and\n            <a target=\"_blank\" [href]=\"termsLink\">Financial Services Guide</a>.\n          </span>\n        </div>\n        <div class=\"subtext\" matPrefix>\n          <mat-checkbox formControlName=\"ageConditions\"></mat-checkbox>\n          <span>\n            I agree that I am 18 years of age or older, or that I am between 15 and 18 years of age and my\n            parent or legal guardian authorises me entering into the\n            <a target=\"_blank\" [href]=\"termsLink\">Terms & Conditions</a>.\n          </span>\n        </div>\n        <mat-error *ngIf=\"termsConditions.invalid || ageConditions.invalid\">{{\n          getErrorMessageTerms()\n        }}</mat-error>\n      </div>\n\n      <button [@bounce]=\"bounce\" class=\"submitButton\" type=\"submit\">{{ GetTextSubmit() }}</button>\n\n      <div *ngIf=\"loginMode\" class=\"cashlessLink forgotPwd\">\n        <a class=\"resetLink\" routerLink=\"/reset\">Forgot password?</a>\n      </div>\n\n      <mat-error *ngIf=\"errorMessage\">\n        <br />\n        {{ errorMessage }}\n      </mat-error>\n    </form>\n  </div>\n</div>\n\n<div class=\"containerBottom\">\n  <div class=\"cashlessLink bottomLink\">\n    <span class=\"registerLabel\" *ngIf=\"!loginMode\"\n      >Already have an account?\n      <p><a routerLink=\"/login\">Log in</a></p></span\n    >\n    <span class=\"registerLabel\" *ngIf=\"loginMode\">No account? Register today! </span>\n    <button [@bounce]=\"bounce\" *ngIf=\"loginMode\" routerLink=\"/register\" class=\"register\">Register</button>\n  </div>\n</div>\n<!-- <div *ngIf=\"isOffline\" class=\"justify-content-center\">\n    <p class=\"offlineMode\">\n        Offline Mode. Please connect to the internet to continue.\n    </p>\n</div> -->\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\nimport { CashlessAppInsightsService, UserService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'authentication-reset',\n  templateUrl: './reset.component.html',\n  styleUrls: ['./reset.component.scss'],\n})\nexport class ResetComponent implements OnInit {\n  errorReset: string;\n  submitted: boolean = false;\n\n  form = new FormGroup({\n    email: new FormControl('', [Validators.required, Validators.email]),\n  });\n\n  constructor(\n    public afAuth: AngularFireAuth,\n    private router: Router,\n    private appInsightsService: CashlessAppInsightsService,\n    private userService: UserService\n  ) {}\n\n  ngOnInit() {}\n\n  ////////////////////////////////////////\n  // Form\n  ////////////////////////////////////////\n  get email() {\n    return this.form.get('email');\n  }\n\n  getErrorMessageEmail() {\n    return this.form.get('email').hasError('required')\n      ? 'You must enter a value'\n      : this.form.get('email').hasError('email')\n      ? 'Not a valid email'\n      : '';\n  }\n\n  ////////////////////////////////////////\n  // Functions\n  ////////////////////////////////////////\n\n  async ResetPassword() {\n    this.errorReset = '';\n    this.submitted = true;\n    this.userService.UserResetPasswordEmail(this.email.value).subscribe({\n      next: res => {\n        this.appInsightsService.TrackEvent('ResetPasswordEmailSent', { Email: this.email.value });\n      },\n      error: err => {\n        this.submitted = false;\n        this.errorReset = 'Oops, something went wrong. Please try again or contact us for assistance.';\n      },\n    });\n  }\n}\n", "<form [formGroup]=\"form\" (ngSubmit)=\"ResetPassword()\" class=\"cashlessForm\">\n  <mat-form-field>\n    <input matInput placeholder=\"Email\" formControlName=\"email\" type=\"email\" required />\n    <mat-error *ngIf=\"email.invalid\">{{ getErrorMessageEmail() }}</mat-error>\n  </mat-form-field>\n\n  <mat-error *ngIf=\"errorReset?.length > 0\">{{ errorReset }}</mat-error>\n  <p>\n    <button mat-flat-button color=\"primary\" type=\"submit\" [disabled]=\"!form.valid\">Reset</button>\n  </p>\n\n  <ng-container *ngIf=\"submitted\">\n    <p>\n      If the email address you entered is valid, instructions to reset your password will be sent there. \n    </p>\n    <p style=\"font-weight: bold;\">\n      If you cannot locate the email, please check your junk/spam folder.\n    </p>\n    <p>\n      <span>Come back to <a routerLink=\"/login\">sign in</a> once your password has been reset</span>\n    </p>\n  </ng-container>\n\n</form>\n\n<div *ngIf=\"!submitted\" class=\"cashlessLink bottomLink\">\n  <span>Go back to <a routerLink=\"/login\">Sign in</a></span>\n</div>\n"], "names": ["RouterModule", "AuthenticationComponent", "LoginComponent", "ResetComponent", "routes", "path", "component", "children", "pathMatch", "redirectTo", "data", "login", "AuthenticationRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatFormFieldModule", "MatInputModule", "AuthenticationModule", "declarations", "environment", "constructor", "ngOnInit", "appVersion", "AppVersion", "selectors", "decls", "vars", "consts", "template", "AuthenticationComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "FormControl", "FormGroup", "Validators", "timeout", "SPRIGGY_SCHOOLS_TERMS_LINK", "Roles", "BaseComponent", "CreateUserRequest", "trigger", "transition", "useAnimation", "fadeIn", "isArray", "ɵɵtextInterpolate", "ctx_r11", "getErrorMessageFirstname", "ɵɵtemplate", "LoginComponent_mat_form_field_3_mat_error_4_Template", "ɵɵproperty", "ctx_r0", "firstname", "invalid", "ctx_r12", "getErrorMessageLastname", "LoginComponent_mat_form_field_4_mat_error_4_Template", "ctx_r1", "lastname", "ctx_r13", "getErrorMessageMobile", "ɵɵlistener", "LoginComponent_mat_form_field_5_Template_input_keyup_3_listener", "ɵɵrestoreView", "_r15", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "formatMobileInput", "LoginComponent_mat_form_field_5_mat_error_4_Template", "ctx_r2", "mobile", "ctx_r3", "getErrorMessageEmail", "ctx_r4", "getErrorMessagePassword", "ctx_r16", "getErrorMessageTerms", "LoginComponent_div_16_mat_error_21_Template", "ctx_r5", "termsLink", "ɵɵsanitizeUrl", "termsConditions", "ageConditions", "ctx_r7", "errorMessage", "ctx_r10", "bounce", "afAuth", "route", "router", "spinnerService", "userService", "authService", "ngZone", "phoneNumberService", "isOffline", "subscribe", "loginMode", "_createForm", "IdentifyUser", "ngOnDestroy", "_offlineSubscription", "unsubscribe", "email", "form", "get", "password", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "valid", "clearApiError", "start", "signIn", "signUp", "signInWithEmailAndPassword", "value", "then", "fuser", "signInSucceeded", "catch", "error", "signInFailed", "request", "Email", "Password", "Role", "Parent", "FirstName", "Lastname", "Mobile", "serverMobileNumber", "RegisterUser", "next", "response", "signUpFailed", "code", "stop", "getApiError", "_ClearStorageOnNewVersion", "userFire", "user", "getIdToken", "token", "SetToken", "_GetUser", "GetUserByFireBaseId", "uid", "pipe", "userResponse", "SetUserConnected", "_RedirectUser", "handleErrorFromService", "savedVersion", "localStorage", "getItem", "clear", "setItem", "Admin", "run", "navigate", "Canteen", "required", "<PERSON><PERSON><PERSON><PERSON>", "requiredTrue", "GetTextSubmit", "res", "au<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setValue", "errors", "messages", "getErrorMessages", "length", "message", "title", "Object", "entries", "for<PERSON>ach", "err", "push", "ɵɵdirectiveInject", "AngularFireAuth", "i2", "ActivatedRoute", "Router", "i3", "SpinnerService", "UserService", "i4", "AuthService", "NgZone", "PhoneNumberService", "features", "ɵɵInheritDefinitionFeature", "LoginComponent_Template", "LoginComponent_Template_form_ngSubmit_2_listener", "LoginComponent_mat_form_field_3_Template", "LoginComponent_mat_form_field_4_Template", "LoginComponent_mat_form_field_5_Template", "LoginComponent_mat_error_10_Template", "LoginComponent_mat_error_15_Template", "LoginComponent_div_16_Template", "LoginComponent_div_19_Template", "LoginComponent_mat_error_20_Template", "LoginComponent_span_23_Template", "LoginComponent_span_24_Template", "LoginComponent_button_25_Template", "params", "timing", "delay", "errorR<PERSON>t", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "appInsightsService", "submitted", "ResetPassword", "_this", "_asyncToGenerator", "UserResetPasswordEmail", "TrackEvent", "CashlessAppInsightsService", "ResetComponent_Template", "ResetComponent_Template_form_ngSubmit_0_listener", "ResetComponent_mat_error_3_Template", "ResetComponent_mat_error_4_Template", "ResetComponent_ng_container_8_Template", "ResetComponent_div_9_Template"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}