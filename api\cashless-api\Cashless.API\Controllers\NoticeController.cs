﻿using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes.Notices;
using Schools.BLL.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class NoticeController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly INoticeValidator _noticeValidator;
        private readonly INoticeService _noticeService;

        public NoticeController(IUserService userService, INoticeValidator noticeValidator, INoticeService noticeService)
        {
            _userService = userService;
            _noticeValidator = noticeValidator;
            _noticeService = noticeService;
        }

        /// <summary>
        /// Get all notice for the given merchant and school
        /// Used in the canteen side only
        /// </summary>
        [Route("GetAllSchoolNotices/{merchantId}/{schoolId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetAllSchoolNotices([FromRoute] int merchantId, [FromRoute] int schoolId)
        {
            var response = await _noticeService.GetAllSchoolNotices(merchantId, schoolId);

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Used to get all the notices for the given school
        /// </summary>
        [Route("GetActiveSchoolNotices/{schoolId}")]
        [Route("GetActiveSchoolNoticesMobile/{schoolId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Parent)]
        public async Task<IActionResult> GetActiveSchoolNotices([FromRoute] int schoolId)
        {
            var response = await _noticeService.GetActiveSchoolNotices(schoolId);

            return new OkObjectResult(response);
        }

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> CreateNotice(CreateNoticeRequest request)
        {
            var notice = await _noticeService.CreateNotice(request);
            if (notice == null)
                throw new NoticeException("Unable to create notice");

            return new OkObjectResult(notice);
        }

        [Route("Update")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateNotice(UpdateNoticeRequest request)
        {
            var notice = await _noticeService.UpdateNotice(request);

            if (notice == null)
                throw new NoticeException("Unable to update notice");

            return new OkObjectResult(notice);
        }

        [Route("{noticeId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteNotice(int noticeId)
        {
            await _noticeService.ArchiveNotice(noticeId);
            return new OkResult();
        }

        [Route("UpdateNoticeStatus")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> UpdateNoticeStatus(UpdateNoticeStatusRequest request)
        {
            var notice = await _noticeService.UpdateNoticeStatus(request);

            if (notice == null)
                throw new NoticeException("Unable to update notice");

            return new OkObjectResult(notice);
        }

        /// <summary>
        /// Get all the notice waiting validation from an admin
        /// </summary>
        [Route("GetWaitingNotice")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetAllNoticeWaitingForValidation()
        {
            var response = await _noticeService.GetAllNoticeWaitingForValidation();

            return new OkObjectResult(response);
        }
    }
}
