using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Cashless.APIs.Attributes;

namespace Cashless.APIs.Controllers
{
    /// <summary>
    /// Controller for handling guest user payments in POS system
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class GuestPaymentController : ControllerBase
    {
        private readonly IGuestPaymentService _guestPaymentService;
        private readonly ITelemetryService _telemetryService;
        private readonly ILogger<GuestPaymentController> _logger;

        public GuestPaymentController(
            IGuestPaymentService guestPaymentService,
            ITelemetryService telemetryService,
            ILogger<GuestPaymentController> logger)
        {
            _guestPaymentService = guestPaymentService;
            _telemetryService = telemetryService;
            _logger = logger;
        }

        /// <summary>
        /// Process a guest payment with card details
        /// </summary>
        /// <param name="request">Guest payment request</param>
        /// <returns>Payment response</returns>
        [Route("ProcessPayment")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ProcessPayment([FromBody] GuestPaymentRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                _logger.LogInformation("Processing guest payment for user {GuestUserId}, amount {Amount}", 
                    request.GuestUserId, request.Amount);

                // Track the payment attempt
                _telemetryService.TrackEvent("GuestPaymentAttempt", new System.Collections.Generic.Dictionary<string, object>
                {
                    { "GuestUserId", request.GuestUserId },
                    { "Amount", request.Amount },
                    { "CanteenId", request.CanteenId },
                    { "ItemCount", request.Items?.Count ?? 0 }
                });

                var response = await _guestPaymentService.ProcessGuestPayment(request);

                if (response.IsSuccess)
                {
                    _telemetryService.TrackEvent("GuestPaymentSuccess", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "OrderId", response.OrderId },
                        { "Amount", response.AmountCharged },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogInformation("Guest payment successful for user {GuestUserId}, order {OrderId}", 
                        request.GuestUserId, response.OrderId);
                }
                else
                {
                    _telemetryService.TrackEvent("GuestPaymentFailure", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "ErrorCode", response.ErrorCode },
                        { "ErrorMessage", response.Message },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogWarning("Guest payment failed for user {GuestUserId}: {ErrorCode} - {Message}", 
                        request.GuestUserId, response.ErrorCode, response.Message);
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest payment for user {GuestUserId}", request?.GuestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "INTERNAL_ERROR",
                    Message = "An internal error occurred while processing the payment"
                });
            }
        }

        /// <summary>
        /// Validate guest card details
        /// </summary>
        /// <param name="request">Card validation request</param>
        /// <returns>Validation response</returns>
        [Route("ValidateCard")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ValidateCard([FromBody] GuestCardValidationRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                _logger.LogInformation("Validating guest card details");

                var response = await _guestPaymentService.ValidateGuestCard(request);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating guest card");
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestCardValidationResponse
                {
                    IsValid = false,
                    ErrorMessage = "An error occurred while validating the card"
                });
            }
        }

        /// <summary>
        /// Get payment status for a guest order
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <returns>Payment status response</returns>
        [Route("PaymentStatus/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetPaymentStatus(string orderId)
        {
            try
            {
                if (string.IsNullOrEmpty(orderId))
                {
                    return BadRequest("Order ID is required");
                }

                _logger.LogInformation("Getting payment status for order {OrderId}", orderId);

                var request = new GuestPaymentStatusRequest { OrderId = orderId };
                var response = await _guestPaymentService.GetGuestPaymentStatus(request);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment status for order {OrderId}", orderId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentStatusResponse
                {
                    Status = "ERROR",
                    ErrorMessage = "An error occurred while retrieving payment status"
                });
            }
        }

        /// <summary>
        /// Process guest payment and create order in one step
        /// </summary>
        /// <param name="request">Guest payment and order request</param>
        /// <returns>Combined payment and order response</returns>
        [Route("ProcessPaymentAndCreateOrder")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ProcessPaymentAndCreateOrder([FromBody] GuestPaymentRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                _logger.LogInformation("Processing guest payment and order for user {GuestUserId}, amount {Amount}",
                    request.GuestUserId, request.Amount);

                // Track the combined operation
                _telemetryService.TrackEvent("GuestPaymentAndOrderAttempt", new System.Collections.Generic.Dictionary<string, object>
                {
                    { "GuestUserId", request.GuestUserId },
                    { "Amount", request.Amount },
                    { "CanteenId", request.CanteenId },
                    { "ItemCount", request.Items?.Count ?? 0 }
                });

                var response = await _guestPaymentService.ProcessGuestPayment(request);

                if (response.IsSuccess)
                {
                    _telemetryService.TrackEvent("GuestPaymentAndOrderSuccess", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "OrderId", response.OrderId },
                        { "Amount", response.AmountCharged },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogInformation("Guest payment and order successful for user {GuestUserId}, order {OrderId}",
                        request.GuestUserId, response.OrderId);
                }
                else
                {
                    _telemetryService.TrackEvent("GuestPaymentAndOrderFailure", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "ErrorCode", response.ErrorCode },
                        { "ErrorMessage", response.Message },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogWarning("Guest payment and order failed for user {GuestUserId}: {ErrorCode} - {Message}",
                        request.GuestUserId, response.ErrorCode, response.Message);
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest payment and order for user {GuestUserId}", request?.GuestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "INTERNAL_ERROR",
                    Message = "An internal error occurred while processing the payment and order"
                });
            }
        }

        /// <summary>
        /// Validate guest user permissions for payment
        /// </summary>
        /// <param name="guestUserId">Guest user ID</param>
        /// <param name="canteenId">Canteen ID</param>
        /// <returns>Permission validation result</returns>
        [Route("ValidatePermissions/{guestUserId}/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ValidatePermissions(int guestUserId, int canteenId)
        {
            try
            {
                _logger.LogInformation("Validating permissions for guest user {GuestUserId} at canteen {CanteenId}",
                    guestUserId, canteenId);

                var hasPermission = await _guestPaymentService.ValidateGuestPermissions(guestUserId, canteenId);

                return Ok(new { HasPermission = hasPermission });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating permissions for guest user {GuestUserId}", guestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new { HasPermission = false, Error = "Permission validation failed" });
            }
        }
    }
}
