using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Cashless.APIs.Attributes;

namespace Cashless.APIs.Controllers
{
    /// <summary>
    /// Controller for handling guest user payments in POS system
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class GuestPaymentController : ControllerBase
    {
        private readonly IGuestPaymentService _guestPaymentService;
        private readonly ITelemetryService _telemetryService;
        private readonly ILogger<GuestPaymentController> _logger;
        private readonly IMemoryCache _memoryCache;

        // Rate limiting constants
        private const int MaxPaymentAttemptsPerMinute = 5;
        private const int MaxPaymentAttemptsPerHour = 20;
        private const int MaxValidationAttemptsPerMinute = 10;

        public GuestPaymentController(
            IGuestPaymentService guestPaymentService,
            ITelemetryService telemetryService,
            ILogger<GuestPaymentController> logger,
            IMemoryCache memoryCache)
        {
            _guestPaymentService = guestPaymentService;
            _telemetryService = telemetryService;
            _logger = logger;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Process a guest payment with card details
        /// </summary>
        /// <param name="request">Guest payment request</param>
        /// <returns>Payment response</returns>
        [Route("ProcessPayment")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(429)] // Too Many Requests
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ProcessPayment([FromBody] GuestPaymentRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                // Security: Rate limiting check
                var rateLimitResult = CheckRateLimit(request.GuestUserId, "payment");
                if (!rateLimitResult.IsAllowed)
                {
                    _logger.LogWarning("Rate limit exceeded for guest payment - User: {GuestUserId}, IP: {ClientIP}",
                        request.GuestUserId, GetClientIpAddress());

                    return StatusCode(429, new GuestPaymentResponse
                    {
                        IsSuccess = false,
                        ErrorCode = "RATE_LIMIT_EXCEEDED",
                        Message = rateLimitResult.Message
                    });
                }

                // Security: Sanitize sensitive data in logs
                _logger.LogInformation("Processing guest payment for user {GuestUserId}, amount {Amount}, IP: {ClientIP}",
                    request.GuestUserId, request.Amount, GetClientIpAddress());

                // Track the payment attempt
                _telemetryService.TrackEvent("GuestPaymentAttempt", new System.Collections.Generic.Dictionary<string, object>
                {
                    { "GuestUserId", request.GuestUserId },
                    { "Amount", request.Amount },
                    { "CanteenId", request.CanteenId },
                    { "ItemCount", request.Items?.Count ?? 0 }
                });

                var response = await _guestPaymentService.ProcessGuestPayment(request);

                if (response.IsSuccess)
                {
                    _telemetryService.TrackEvent("GuestPaymentSuccess", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "OrderId", response.OrderId },
                        { "Amount", response.AmountCharged },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogInformation("Guest payment successful for user {GuestUserId}, order {OrderId}", 
                        request.GuestUserId, response.OrderId);
                }
                else
                {
                    _telemetryService.TrackEvent("GuestPaymentFailure", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "ErrorCode", response.ErrorCode },
                        { "ErrorMessage", response.Message },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogWarning("Guest payment failed for user {GuestUserId}: {ErrorCode} - {Message}", 
                        request.GuestUserId, response.ErrorCode, response.Message);
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest payment for user {GuestUserId}", request?.GuestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "INTERNAL_ERROR",
                    Message = "An internal error occurred while processing the payment"
                });
            }
        }

        /// <summary>
        /// Validate guest card details
        /// </summary>
        /// <param name="request">Card validation request</param>
        /// <returns>Validation response</returns>
        [Route("ValidateCard")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ValidateCard([FromBody] GuestCardValidationRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                _logger.LogInformation("Validating guest card details");

                var response = await _guestPaymentService.ValidateGuestCard(request);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating guest card");
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestCardValidationResponse
                {
                    IsValid = false,
                    ErrorMessage = "An error occurred while validating the card"
                });
            }
        }

        /// <summary>
        /// Get payment status for a guest order
        /// </summary>
        /// <param name="orderId">Order ID</param>
        /// <returns>Payment status response</returns>
        [Route("PaymentStatus/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetPaymentStatus(string orderId)
        {
            try
            {
                if (string.IsNullOrEmpty(orderId))
                {
                    return BadRequest("Order ID is required");
                }

                _logger.LogInformation("Getting payment status for order {OrderId}", orderId);

                var request = new GuestPaymentStatusRequest { OrderId = orderId };
                var response = await _guestPaymentService.GetGuestPaymentStatus(request);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment status for order {OrderId}", orderId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentStatusResponse
                {
                    Status = "ERROR",
                    ErrorMessage = "An error occurred while retrieving payment status"
                });
            }
        }

        /// <summary>
        /// Process guest payment and create order in one step
        /// </summary>
        /// <param name="request">Guest payment and order request</param>
        /// <returns>Combined payment and order response</returns>
        [Route("ProcessPaymentAndCreateOrder")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ProcessPaymentAndCreateOrder([FromBody] GuestPaymentRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("Invalid request");
                }

                _logger.LogInformation("Processing guest payment and order for user {GuestUserId}, amount {Amount}",
                    request.GuestUserId, request.Amount);

                // Track the combined operation
                _telemetryService.TrackEvent("GuestPaymentAndOrderAttempt", new System.Collections.Generic.Dictionary<string, object>
                {
                    { "GuestUserId", request.GuestUserId },
                    { "Amount", request.Amount },
                    { "CanteenId", request.CanteenId },
                    { "ItemCount", request.Items?.Count ?? 0 }
                });

                var response = await _guestPaymentService.ProcessGuestPayment(request);

                if (response.IsSuccess)
                {
                    _telemetryService.TrackEvent("GuestPaymentAndOrderSuccess", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "OrderId", response.OrderId },
                        { "Amount", response.AmountCharged },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogInformation("Guest payment and order successful for user {GuestUserId}, order {OrderId}",
                        request.GuestUserId, response.OrderId);
                }
                else
                {
                    _telemetryService.TrackEvent("GuestPaymentAndOrderFailure", new System.Collections.Generic.Dictionary<string, object>
                    {
                        { "ErrorCode", response.ErrorCode },
                        { "ErrorMessage", response.Message },
                        { "GuestUserId", request.GuestUserId }
                    });

                    _logger.LogWarning("Guest payment and order failed for user {GuestUserId}: {ErrorCode} - {Message}",
                        request.GuestUserId, response.ErrorCode, response.Message);
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest payment and order for user {GuestUserId}", request?.GuestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "INTERNAL_ERROR",
                    Message = "An internal error occurred while processing the payment and order"
                });
            }
        }

        /// <summary>
        /// Validate guest user permissions for payment
        /// </summary>
        /// <param name="guestUserId">Guest user ID</param>
        /// <param name="canteenId">Canteen ID</param>
        /// <returns>Permission validation result</returns>
        [Route("ValidatePermissions/{guestUserId}/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> ValidatePermissions(int guestUserId, int canteenId)
        {
            try
            {
                _logger.LogInformation("Validating permissions for guest user {GuestUserId} at canteen {CanteenId}",
                    guestUserId, canteenId);

                var hasPermission = await _guestPaymentService.ValidateGuestPermissions(guestUserId, canteenId);

                return Ok(new { HasPermission = hasPermission });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating permissions for guest user {GuestUserId}", guestUserId);
                _telemetryService.TrackException(ex);

                return StatusCode(500, new { HasPermission = false, Error = "Permission validation failed" });
            }
        }

        #region Security and Rate Limiting

        private (bool IsAllowed, string Message) CheckRateLimit(int guestUserId, string operation)
        {
            var clientIp = GetClientIpAddress();
            var userKey = $"rate_limit_{operation}_user_{guestUserId}";
            var ipKey = $"rate_limit_{operation}_ip_{clientIp}";

            var now = DateTime.UtcNow;

            // Check per-user rate limits
            var userAttempts = GetRateLimitAttempts(userKey);
            var ipAttempts = GetRateLimitAttempts(ipKey);

            // Different limits based on operation
            int maxPerMinute, maxPerHour;
            switch (operation.ToLower())
            {
                case "payment":
                    maxPerMinute = MaxPaymentAttemptsPerMinute;
                    maxPerHour = MaxPaymentAttemptsPerHour;
                    break;
                case "validation":
                    maxPerMinute = MaxValidationAttemptsPerMinute;
                    maxPerHour = MaxValidationAttemptsPerMinute * 6; // 6x per hour
                    break;
                default:
                    maxPerMinute = 5;
                    maxPerHour = 20;
                    break;
            }

            // Check minute-based limits
            var recentUserAttempts = userAttempts.Count(a => a > now.AddMinutes(-1));
            var recentIpAttempts = ipAttempts.Count(a => a > now.AddMinutes(-1));

            if (recentUserAttempts >= maxPerMinute)
            {
                return (false, $"Too many {operation} attempts. Please wait before trying again.");
            }

            if (recentIpAttempts >= maxPerMinute * 2) // IP limit is 2x user limit
            {
                return (false, $"Too many {operation} attempts from this location. Please wait before trying again.");
            }

            // Check hour-based limits
            var hourlyUserAttempts = userAttempts.Count(a => a > now.AddHours(-1));
            var hourlyIpAttempts = ipAttempts.Count(a => a > now.AddHours(-1));

            if (hourlyUserAttempts >= maxPerHour)
            {
                return (false, $"Hourly {operation} limit exceeded. Please try again later.");
            }

            if (hourlyIpAttempts >= maxPerHour * 3) // IP hourly limit is 3x user limit
            {
                return (false, $"Hourly {operation} limit exceeded for this location. Please try again later.");
            }

            // Record this attempt
            RecordRateLimitAttempt(userKey, now);
            RecordRateLimitAttempt(ipKey, now);

            return (true, string.Empty);
        }

        private List<DateTime> GetRateLimitAttempts(string key)
        {
            return _memoryCache.GetOrCreate(key, entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                return new List<DateTime>();
            });
        }

        private void RecordRateLimitAttempt(string key, DateTime timestamp)
        {
            var attempts = GetRateLimitAttempts(key);
            attempts.Add(timestamp);

            // Clean up old attempts (older than 1 hour)
            var cutoff = timestamp.AddHours(-1);
            attempts.RemoveAll(a => a < cutoff);

            _memoryCache.Set(key, attempts, TimeSpan.FromHours(1));
        }

        private string GetClientIpAddress()
        {
            // Try to get the real IP address from various headers
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();

            // Check for forwarded IP addresses (common in load balancer scenarios)
            if (HttpContext.Request.Headers.ContainsKey("X-Forwarded-For"))
            {
                var forwardedIps = HttpContext.Request.Headers["X-Forwarded-For"].ToString();
                if (!string.IsNullOrEmpty(forwardedIps))
                {
                    // Take the first IP in the chain
                    ipAddress = forwardedIps.Split(',')[0].Trim();
                }
            }
            else if (HttpContext.Request.Headers.ContainsKey("X-Real-IP"))
            {
                ipAddress = HttpContext.Request.Headers["X-Real-IP"].ToString();
            }

            return ipAddress ?? "unknown";
        }

        private GuestPaymentRequest SanitizePaymentRequest(GuestPaymentRequest request)
        {
            // Create a copy for logging that doesn't contain sensitive data
            return new GuestPaymentRequest
            {
                GuestUserId = request.GuestUserId,
                CanteenId = request.CanteenId,
                Amount = request.Amount,
                Items = request.Items,
                OrderDate = request.OrderDate,
                MenuId = request.MenuId,
                MenuType = request.MenuType,
                // Sensitive fields are excluded or masked
                CardNumber = MaskCardNumber(request.CardNumber),
                CardholderName = MaskName(request.CardholderName),
                ExpiryMonth = request.ExpiryMonth,
                ExpiryYear = request.ExpiryYear,
                CVV = "***" // Never log CVV
            };
        }

        private string MaskCardNumber(string cardNumber)
        {
            if (string.IsNullOrEmpty(cardNumber) || cardNumber.Length < 4)
                return "****";

            var cleaned = cardNumber.Replace(" ", "").Replace("-", "");
            if (cleaned.Length < 4)
                return "****";

            return "**** **** **** " + cleaned.Substring(cleaned.Length - 4);
        }

        private string MaskName(string name)
        {
            if (string.IsNullOrEmpty(name) || name.Length <= 2)
                return "***";

            return name.Substring(0, 1) + "***" + name.Substring(name.Length - 1);
        }

        #endregion
    }
}
