using System;
using System.Threading.Tasks;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Schools.BLL.Classes.Messaging;
using Schools.BLL.Classes.Orders.Events;
using Schools.BLL.Services;
using Schools.BLL.Services.Interfaces;
using Schools.Orders.Extensions;
using Schools.Orders.Middleware;
using Schools.Orders.Services;

namespace Schools.Orders;

public class Program
{
    /// <summary>
    /// Connection string for App Configuration
    /// </summary>
    public const string AppConfigurationConnectionString = "ConnectionStrings:AppConfiguration";

    /// <summary>
    /// Connection string for Service Bus
    /// </summary>
    public const string ServiceBusConnectionString = "ConnectionStrings:ServiceBus";

    /// <summary>
    /// Build and run the functions app
    /// </summary>
    public static async Task Main(string[] args)
    {
        // Adding Application Insights using: https://learn.microsoft.com/en-us/azure/azure-functions/dotnet-isolated-process-guide
        // is broken in Function v4 (dotnet-isolated) as per: https://github.com/MicrosoftDocs/azure-docs/issues/100912
        //
        // A fix should be coming when the following package is release: https://www.nuget.org/packages/Microsoft.Azure.Functions.Worker.ApplicationInsights
        var host = new HostBuilder()
                        .ConfigureFunctionsWorkerDefaults(app =>
                        {
                            app.UseMiddleware<ExceptionHandlingMiddleware>();
                        })
                        .ConfigureAppConfiguration((context, config) =>
                        {
                            // Load the settings file containing release / build settings
                            config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                            // Build config. It has not been initialised yet
                            var settings = config.Build();

                            config.AddAzureAppConfiguration(options =>
                            {
                                options.Connect(settings[AppConfigurationConnectionString]);
                            });
                        })
                        .ConfigureLogging(logging =>
                        {
                            logging.ClearProviders();
                            logging.AddConsole();
                        })
                        .ConfigureServices((context, services) =>
                        {
                            ConfigureIntegrations(context);
                            ConfigureDependancyInjection(services);

                            // ConfigureMassTransit(context, services); // This configures the Azure Service Bus integrations.
                        })
                        .Build();

        var env = host.Services.GetRequiredService<IHostEnvironment>();
        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        var configService = host.Services.GetRequiredService<IConfigService>();

        logger.LogWarning("***** Functions app initialisation complete. Environment: {Env} *****", env.EnvironmentName);
        configService.CheckConfig();

        await host.RunAsync();

        logger.LogWarning("***** Functions app shutting down *****");
    }

    /// <summary>
    /// Add all service definitions
    /// </summary>
    private static void ConfigureDependancyInjection(IServiceCollection services)
    {
        services.AddExternalServices();
        services.AddServiceLayerServices();
    }

    // Masstransit is used to configure
    //  - the service bus topics
    //  - the subscribers for the service bus topic
    //  - the consumers for how those messages are processed
    private static void ConfigureMassTransit(HostBuilderContext context, IServiceCollection services)
    {
        services.AddMassTransit(x =>
        {
            // Register consumers & their retry policies
            // Consumers manage how the service bus messages are processed and handled
            // x.AddConsumer<ManageOrdersCreatedStockConsumer, ManageOrdersCreatedStockConsumerDefinition>();
            // x.AddConsumer<ProcessOrdersCreatedPaymentConsumer, ProcessOrdersCreatedPaymentConsumerDefinition>();
            // x.AddConsumer<NotifyUserOrdersCreatedConsumer, NotifyUserOrdersCreatedConsumerDefinition>();
            // x.AddConsumer<OrdersCreatedFaultConsumer>();
            // x.AddConsumer<ManageOrdersEditedStockConsumer, ManageOrdersEditedStockConsumerDefinition>();
            // x.AddConsumer<ProcessOrdersEditedPaymentConsumer, ProcessOrdersEditedPaymentConsumerDefinition>();
            // x.AddConsumer<NotifyUserOrdersEditedConsumer, NotifyUserOrdersEditedConsumerDefinition>();
            // x.AddConsumer<OrdersEditedFaultConsumer>();
            // x.AddConsumer<OrderFaultConsumer>();

            // Attach to Azure Service Bus
            // Configure service bus topic and subscribers
            x.UsingAzureServiceBus((ctx, config) =>
            {
                // Connection to the service bus
                config.Host(context.Configuration[ServiceBusConnectionString]);

                // Configure the service bus topic to manage orders created events
                // Creates the topic
                config.Message<OrdersCreated>(x =>
                {
                    // Set a specific name for the new service bus topic
                    x.SetEntityName(MessagingConstants.OrdersCreatedTopic);
                });

                // Configure the service bus topic to manage orders edited events
                // Creates the topic
                config.Message<OrdersEdited>(x =>
                {
                    // Set a specific name for the new service bus topic
                    x.SetEntityName(MessagingConstants.OrdersEditedTopic);
                });

                // Configure the subscriptions in azure for the orders created topic and associate the subscription to a consuner
                // config.SubscriptionEndpoint<OrdersCreated>(OrderEventConstants.ManageStockSubscription, e => { e.ConfigureConsumer<ManageOrdersCreatedStockConsumer>(ctx); });
                // config.SubscriptionEndpoint<OrdersCreated>(OrderEventConstants.NotifyUserSubscription, e => { e.ConfigureConsumer<NotifyUserOrdersCreatedConsumer>(ctx); });
                // config.SubscriptionEndpoint<OrdersCreated>(OrderEventConstants.ProcessPaymentSubscription, e => { e.ConfigureConsumer<ProcessOrdersCreatedPaymentConsumer>(ctx); });
                // config.SubscriptionEndpoint<OrdersEdited>(OrderEventConstants.ManageStockSubscription, e => { e.ConfigureConsumer<ManageOrdersEditedStockConsumer>(ctx); });
                // config.SubscriptionEndpoint<OrdersEdited>(OrderEventConstants.NotifyUserSubscription, e => { e.ConfigureConsumer<NotifyUserOrdersEditedConsumer>(ctx); });
                // config.SubscriptionEndpoint<OrdersEdited>(OrderEventConstants.ProcessPaymentSubscription, e => { e.ConfigureConsumer<ProcessOrdersEditedPaymentConsumer>(ctx); });

                // Sets up all other endpoints (such as fualt endpoints)
                config.ConfigureEndpoints(ctx);

                // Set default service bus configuration
                config.LockDuration = TimeSpan.FromMinutes(3); // Max 5min
                config.MaxDeliveryCount = 3;
                config.ConcurrentMessageLimit = 100;
                config.DefaultMessageTimeToLive = TimeSpan.FromDays(3);
            });

            // Configure JSON message serialization
            x.AddConfigureEndpointsCallback((_, config) =>
            {
                config.ClearSerialization();
                config.UseRawJsonSerializer();
            });
        });
    }

    /// <summary>
    /// Configure integration settings
    /// </summary>
    private static void ConfigureIntegrations(HostBuilderContext context)
    {
        SchoolsApiClient.ApimKey = context.Configuration["apimKey"];
        SchoolsApiClient.CashlessApiSecret = context.Configuration["cashlessApiSecret"];
        SchoolsApiClient.BaseUrl = context.Configuration["SchoolsApiBaseUri"];
    }
}