using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class SchoolClass
    {
        [JsonProperty(PropertyName = "ClassId")]
        public int? ClassId { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public int SchoolId { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool IsActive { get; set; }

        [JsonProperty(PropertyName = "DateCreated")]
        public DateTime DateCreated { get; set; }

        [JsonProperty(PropertyName = "DateModified")]
        public DateTime DateModified { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "Teacher")]
        public string Teacher { get; set; }

        [JsonProperty(PropertyName = "IsArchived")]
        public bool? IsArchived { get; set; }

        [JsonProperty(PropertyName = "YearGroup")]
        public string YearGroup { get; set; }

        [JsonProperty(PropertyName = "SortOrder")]
        public Int32? SortOrder { get; set; }
    }
}
