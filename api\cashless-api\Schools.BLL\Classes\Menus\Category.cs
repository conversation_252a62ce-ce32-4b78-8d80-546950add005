using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes
{

    public class ItemCategory
    {
        [JsonProperty(PropertyName = "MenuCategoryId")]
        public long? MenuCategoryId { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool? IsActive { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public long CanteenId { get; set; }

        [JsonProperty(PropertyName = "CategoryName")]
        public string CategoryName { get; set; }

        [JsonProperty(PropertyName = "CategoryUrl")]
        public string CategoryUrl { get; set; }

        [JsonProperty(PropertyName = "SortOrder")]
        public int? SortOrder { get; set; }

        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        [JsonProperty(PropertyName = "SubDescription")]
        public string SubDescription { get; set; }
    }

    public class ItemCategoryInsertRequest
    {
        [Range(1, int.MaxValue, ErrorMessage = "Enter a valid CanteenId")]
        public int CanteenId { get; set; }

        [Required(AllowEmptyStrings = false)]
        public string CategoryName { get; set; }

        [Required(AllowEmptyStrings = false)]
        public string CategoryUrl { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Enter a valid SortOrder")]
        public int SortOrder { get; set; }
    }

    public class ItemCategoryUpdateRequest
    {
        [Range(1, int.MaxValue, ErrorMessage = "Enter a valid CategoryId")]
        public int CategoryId { get; set; }

        [Required(AllowEmptyStrings = false)]
        public string CategoryName { get; set; }

        [Required(AllowEmptyStrings = false)]
        public string CategoryUrl { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Enter a valid SortOrder")]
        public int SortOrder { get; set; }
    }

    public class ItemCategoryResponse
    {
        public int CategoryId { get; set; }

        public int CanteenId { get; set; }

        public string CategoryName { get; set; }

        public string CategoryUrl { get; set; }

        public int SortOrder { get; set; }

        public bool IsArchived { get; set; }
    }

    public static class CategoryConverter
    {
        public static ItemCategoryResponse ConvertToResponse(this CategoryEntity category)
        {
            return new ItemCategoryResponse()
            {
                CategoryId = category.MenuCategoryId,
                CanteenId = category.CanteenId,
                CategoryName = category.CategoryName,
                CategoryUrl = category.CategoryUrl,
                SortOrder = category.SortOrder,
                IsArchived = category.IsArchived
            };
        }
    }

    public class ListCategoriesResponse : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Categories")]
        public List<ItemCategory> Categories { get; set; }
    }
}
