{"version": 3, "file": "src_app_manage-children_manage-children_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE4F;AAGD;AAE3F;AAC4C;AAGgC;AACG;AAYhD;AAEoC;;;;;;;;;;;;;;;;;;ICrB7De,4DAAA,SAAoB;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAK;;;;;IACpCA,4DAAA,SAAmB;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAK;;;;;IAMpCA,4DAAA,gBAAqC;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAY;;;;IAA5CA,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAM,MAAA,CAAAC,wBAAA,GAAgC;;;;;IAKrEP,4DAAA,gBAAoC;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAY;;;;IAA3CA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAQ,OAAA,CAAAC,uBAAA,GAA+B;;;;;IAKjET,4DAAA,qBAAyE;IAAAA,oDAAA,GAEvE;IAAAA,0DAAA,EAAa;;;;IAFgCA,wDAAA,UAAAW,UAAA,CAAAC,QAAA,CAAyB;IAACZ,uDAAA,GAEvE;IAFuEA,+DAAA,CAAAW,UAAA,CAAAE,IAAA,CAEvE;;;;;IALNb,4DAAA,yBAAyD;IAC5CA,oDAAA,kBAAW;IAAAA,0DAAA,EAAY;IAClCA,4DAAA,qBAAmE;IACjEA,wDAAA,IAAAe,gEAAA,yBAEe;IACjBf,0DAAA,EAAa;;;;IAHoBA,uDAAA,GAAc;IAAdA,wDAAA,YAAAgB,OAAA,CAAAC,WAAA,CAAc;;;;;IAS7CjB,4DAAA,qBAA0C;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAa;;;;;IAC/DA,4DAAA,qBAAoE;IAAAA,oDAAA,GAElE;IAAAA,0DAAA,EAAa;;;;IAF6BA,wDAAA,UAAAkB,SAAA,CAAAC,OAAA,CAAuB;IAACnB,uDAAA,GAElE;IAFkEA,+DAAA,CAAAkB,SAAA,CAAAL,IAAA,CAElE;;;;;;IA1BRb,4DAAA,eAAmF;IAA7CA,wDAAA,sBAAAqB,2DAAA;MAAArB,2DAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,2DAAA;MAAA,OAAYA,yDAAA,CAAAwB,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC3D3B,4DAAA,yBAAqC;IACxBA,oDAAA,iBAAU;IAAAA,0DAAA,EAAY;IACjCA,uDAAA,gBAA4F;IAC5FA,wDAAA,IAAA6B,6CAAA,uBAAiF;IACnF7B,0DAAA,EAAiB;IACjBA,4DAAA,yBAAqC;IACxBA,oDAAA,gBAAS;IAAAA,0DAAA,EAAY;IAChCA,uDAAA,gBAA0F;IAC1FA,wDAAA,KAAA8B,8CAAA,uBAA+E;IACjF9B,0DAAA,EAAiB;IACjBA,wDAAA,KAAA+B,mDAAA,6BAOiB;IAEjB/B,4DAAA,0BAAqC;IACxBA,oDAAA,aAAK;IAAAA,0DAAA,EAAY;IAC5BA,4DAAA,sBAAiE;IAC/DA,wDAAA,KAAAgC,+CAAA,yBAA+D;IAC/DhC,wDAAA,KAAAiC,+CAAA,yBAEe;IACjBjC,0DAAA,EAAa;;;;IA3BEA,wDAAA,cAAAkC,MAAA,CAAAC,IAAA,CAAkB;IAIrBnC,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAkC,MAAA,CAAAE,SAAA,CAAAC,OAAA,CAAuB;IAKvBrC,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAkC,MAAA,CAAAI,QAAA,CAAAD,OAAA,CAAsB;IAEErC,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAkC,MAAA,CAAAjB,WAAA,CAAiB;IAYtCjB,uDAAA,GAAgB;IAAhBA,wDAAA,UAAAkC,MAAA,CAAAK,SAAA,CAAgB;IACCvC,uDAAA,GAAY;IAAZA,wDAAA,YAAAkC,MAAA,CAAAK,SAAA,CAAY;;;;;;IAwB9CvC,4DAAA,wBAIC;IADCA,wDAAA,oCAAAwC,wGAAAC,MAAA;MAAAzC,2DAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAA3C,2DAAA;MAAA,OAA0BA,yDAAA,CAAA2C,OAAA,CAAAC,eAAA,CAAAC,QAAA,CAAAJ,MAAA,CAAgC;IAAA,EAAC;IAC5DzC,0DAAA,EAAgB;;;;IAFfA,wDAAA,oBAAA8C,OAAA,CAAAF,eAAA,CAAAG,KAAA,CAAyC;;;;;;IAP7C/C,4DAAA,eAAmF;IAA7CA,wDAAA,sBAAAgD,iEAAA;MAAAhD,2DAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAlD,2DAAA;MAAA,OAAYA,yDAAA,CAAAkD,OAAA,CAAAvB,QAAA,EAAU;IAAA,EAAC;IAC3D3B,4DAAA,cAA+B;IACgBA,oDAAA,8BAAuB;IAAAA,0DAAA,EAAe;IAGrFA,wDAAA,IAAAmD,uDAAA,4BAIiB;IACnBnD,0DAAA,EAAO;;;;IAVYA,wDAAA,cAAAoD,OAAA,CAAAjB,IAAA,CAAkB;IAMhCnC,uDAAA,GAAgC;IAAhCA,wDAAA,SAAAoD,OAAA,CAAAC,YAAA,CAAAN,KAAA,SAAgC;;;;;IAjBzC/C,4DAAA,aAA8D;IACxDA,oDAAA,oBAAa;IAAAA,0DAAA,EAAK;IACtBA,4DAAA,aAAgC;IAE5BA,oDAAA,+LAEF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,QAAG;IACDA,oDAAA,kKAEF;IAAAA,0DAAA,EAAI;IACJA,wDAAA,IAAAsD,uCAAA,kBAUO;IACTtD,0DAAA,EAAM;;;;IAXGA,uDAAA,GAAU;IAAVA,wDAAA,SAAAuD,MAAA,CAAApB,IAAA,CAAU;;;;;IAuBbnC,4DAAA,uBAIG;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAC7B;;;;;IAFCA,wDAAA,oBAAAwD,KAAA,CAAqB;IACpBxD,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAyD,OAAA,CAAAC,YAAA,CAAAF,KAAA,EAAAG,KAAA,CAA2B;;;;;;IANlC3D,4DAAA,eAA0F;IAA7CA,wDAAA,sBAAA4D,iEAAA;MAAA5D,2DAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA9D,2DAAA;MAAA,OAAYA,yDAAA,CAAA8D,OAAA,CAAAnC,QAAA,EAAU;IAAA,EAAC;IAClE3B,4DAAA,cAA4C;IAC1CA,wDAAA,IAAA+D,sDAAA,2BAKC;IACH/D,0DAAA,EAAM;;;;IARWA,wDAAA,cAAAgE,OAAA,CAAAC,WAAA,CAAyB;IAGpBjE,uDAAA,GAA8B;IAA9BA,wDAAA,YAAAgE,OAAA,CAAAE,gBAAA,CAAAC,QAAA,CAA8B;;;;;IAV1DnE,4DAAA,aAA8D;IACxDA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;IAClBA,4DAAA,aAAgC;IAE5BA,oDAAA,sHAEF;IAAAA,0DAAA,EAAI;IACJA,wDAAA,IAAAoE,uCAAA,kBASO;IACTpE,0DAAA,EAAM;;;;IAVGA,uDAAA,GAAU;IAAVA,wDAAA,SAAAqE,MAAA,CAAAlC,IAAA,CAAU;;;;;IAmBXnC,4DAAA,WAAsB;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAO;;;;;IACtCA,4DAAA,WAAqB;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAO;;;;;;IASpCA,4DAAA,cAA8C;IACkBA,wDAAA,mBAAAsE,0DAAA;MAAAtE,2DAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAAxE,2DAAA;MAAA,OAASA,yDAAA,CAAAwE,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IACtFzE,oDAAA,qBACF;IAAAA,0DAAA,EAAS;;;;;IAGTA,4DAAA,gBAA4B;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAY;;;;IAA9BA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA0E,MAAA,CAAAC,UAAA,GAAkB;;;AD1EpD,MAAOC,iBAAkB,SAAQnF,wDAAa;EAclDoF,YACUC,KAAqC,EACrCC,cAA8B,EAC9BC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,cAAoC,EACpCC,cAA8B,EAC/BC,MAAiB,EAChBC,WAAwB;IAEhC,KAAK,EAAE;IAVC,KAAAR,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,WAAW,GAAXA,WAAW;IAtBrB,KAAArE,WAAW,GAAa,EAAE;IAM1B,KAAAsE,MAAM,GAAY,KAAK;IAIvB,KAAA7B,YAAY,GAAqBrE,wDAAa;EAe9C;EAEAmG,QAAQA,CAAA;IACN,IAAIC,IAAI,GAAG,IAAI,CAACV,cAAc,CAACW,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IACvD,IAAI,CAAC1E,WAAW,GAAGwE,IAAI,CAACG,OAAO;IAC/B,IAAI,CAACC,cAAc,GAAG,IAAInG,uDAAY,EAAE;IAExC;IACA,IAAI,IAAI,CAACsF,MAAM,CAACc,GAAG,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACzC,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAAClB,KAAK,CACnCmB,IAAI,CAAC3G,mDAAM,CAACC,8EAAa,CAAC,CAAC,CAC3B2G,SAAS,CAAEC,OAAqB,IAAI;QACnC,IAAI,CAACN,cAAc,GAAGM,OAAO;QAC7B,IAAI,CAACZ,MAAM,GAAG,IAAI;QAElB,IAAI,CAAC,IAAI,CAACM,cAAc,EAAE;UACxB,IAAI,CAACA,cAAc,GAAG,IAAInG,uDAAY,EAAE;UACxC,IAAI,CAAC0G,MAAM,EAAE;;QAEf,IAAI,CAACC,UAAU,EAAE;MACnB,CAAC,CAAC;KACL,MAAM;MACL,IAAI,CAACA,UAAU,EAAE;;EAErB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,oBAAoB,EAAEO,WAAW,EAAE;EAC1C;EAEAH,MAAMA,CAAA;IACJ,IAAI,CAACnB,cAAc,CAACuB,KAAK,EAAE;IAC3B,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;MAAEC,UAAU,EAAE,IAAI,CAAC3B;IAAc,CAAE,CAAC;EACjF;EAEA;EACA;EACA;EACA,IAAI3C,SAASA,CAAA;IACX,OAAO,IAAI,CAACD,IAAI,CAACwE,GAAG,CAAC,WAAW,CAAC;EACnC;EAEA,IAAIrE,QAAQA,CAAA;IACV,OAAO,IAAI,CAACH,IAAI,CAACwE,GAAG,CAAC,UAAU,CAAC;EAClC;EAEA,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACzE,IAAI,CAACwE,GAAG,CAAC,QAAQ,CAAC;EAChC;EAEA,IAAIE,KAAKA,CAAA;IACP,OAAO,IAAI,CAAC1E,IAAI,CAACwE,GAAG,CAAC,OAAO,CAAC;EAC/B;EAEA,IAAItD,YAAYA,CAAA;IACd,OAAO,IAAI,CAAClB,IAAI,CAACwE,GAAG,CAAC,cAAc,CAAC;EACtC;EAEA,IAAI/D,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACT,IAAI,CAACwE,GAAG,CAAC,iBAAiB,CAAC;EACzC;EAEA,IAAIzC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACD,WAAW,CAAC0C,GAAG,CAAC,WAAW,CAAc;EACvD;EAEApG,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC6B,SAAS,CAAC0E,QAAQ,CAAC,UAAU,CAAC,GAAG,wBAAwB,GAAG,EAAE;EAC5E;EAEArG,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC6B,QAAQ,CAACwE,QAAQ,CAAC,UAAU,CAAC,GAAG,wBAAwB,GAAG,EAAE;EAC3E;EAEAC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,wBAAwB,GAAG,EAAE;EACxE;EAEAT,UAAUA,CAAA;IACR,IAAI,CAACW,SAAS,GAAG,IAAI,CAACnB,cAAc,CAACjF,QAAQ;IAE7C,IAAI,CAAC,IAAI,CAACiF,cAAc,CAACoB,eAAe,IAAI,IAAI,CAACpB,cAAc,CAACoB,eAAe,IAAI,EAAE,EAAE;MACrF,IAAIC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI1H,uDAAY,EAAE,EAAE,IAAI,CAACmG,cAAc,CAAC;MACxEqB,WAAW,CAACD,eAAe,GAAG,MAAM;MACpC,IAAI,CAACpB,cAAc,GAAGqB,WAAW;;IAGnC,IAAI,CAAC/E,IAAI,GAAG,IAAIhD,qDAAS,CAAC;MACxBiD,SAAS,EAAE,IAAIlD,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAACwB,SAAS,EAAE,CAACjI,sDAAU,CAACkI,QAAQ,CAAC,CAAC;MAChFhF,QAAQ,EAAE,IAAIpD,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAAC0B,QAAQ,EAAE,CAACnI,sDAAU,CAACkI,QAAQ,CAAC,CAAC;MAC9EV,MAAM,EAAE,IAAI1H,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAACjF,QAAQ,CAAC;MACrDiG,KAAK,EAAE,IAAI3H,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAAC1E,OAAO,EAAE,CAAC/B,sDAAU,CAACkI,QAAQ,CAAC,CAAC;MAC1EjE,YAAY,EAAE,IAAInE,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAAC2B,mBAAmB,CAAC;MACtE5E,eAAe,EAAE,IAAI1D,uDAAW,CAAC,IAAI,CAAC2G,cAAc,CAACoB,eAAe;KACrE,CAAC;IAEF,IAAI,CAACQ,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAACb,MAAM,CAACc,YAAY,CAACxB,SAAS,CAACyB,QAAQ,IAAG;MAC5C,IAAI,CAACX,SAAS,GAAGW,QAAQ;MACzB,IAAI,CAACC,SAAS,CAACD,QAAQ,CAAC;IAC1B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACpC,MAAM,IAAI,IAAI,CAACM,cAAc,EAAE;MACtC,IAAI,CAAC+B,SAAS,CAAC,IAAI,CAAC/B,cAAc,CAACjF,QAAQ,CAAC;;EAEhD;EAEA6G,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACxD,WAAW,GAAG,IAAI,CAACqB,WAAW,CAACuC,KAAK,CAAC;MACxCC,SAAS,EAAE,IAAI7I,qDAAS,CAAC,EAAE;KAC5B,CAAC;IAEF;IACA,IAAI,CAACyE,YAAY,CAACqE,OAAO,CAACC,OAAO,IAAG;MAClC,IAAI,CAAC9D,gBAAgB,CAAC+D,IAAI,CAAC,IAAI/I,uDAAW,CAAC,IAAI,CAAC2G,cAAc,EAAEqC,SAAS,EAAEC,QAAQ,CAACH,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;IACrG,CAAC,CAAC;EACJ;EAEA;EACQR,SAASA,CAACS,QAAgB;IAChC,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACpD,cAAc,CAACuB,KAAK,EAAE;MAC3B,IAAI,CAACrB,cAAc,CAACmD,qBAAqB,CAACD,QAAQ,EAAE,IAAI,CAAC,CAACnC,SAAS,CAAC;QAClEqC,IAAI,EAAGC,QAAqB,IAAI;UAC9B,IAAI,CAACjG,SAAS,GAAGiG,QAAQ,CAACC,OAAO;UACjC,IAAI,CAACxD,cAAc,CAACyD,IAAI,EAAE;QAC5B,CAAC;QACDC,KAAK,EAAEA,KAAK,IAAG;UACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;UAClC,IAAI,CAAC1D,cAAc,CAACyD,IAAI,EAAE;QAC5B;OACD,CAAC;;EAEN;EAEA;EACA;EACA;EACA/G,QAAQA,CAAA;IACN,IAAI,CAACsD,cAAc,CAACuB,KAAK,EAAE;IAE3B,IAAIqC,aAAa,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,gBAAgB,EAAE;IAEvD,IAAI,CAAC5D,WAAW,CAAC6D,UAAU,CAAC,IAAI,CAACC,aAAa,EAAE,CAAC,CAAC9C,SAAS,CAAC;MAC1DqC,IAAI,EAAGU,IAAkB,IAAI;QAC3B;QACA,IAAI,CAAC5C,UAAU,EAAE;QAEjB;QACA,IAAI,CAACvB,KAAK,CAACoE,QAAQ,CAAC1J,qFAAkB,CAAC;UAAE2J,QAAQ,EAAEN,aAAa,CAACO;QAAM,CAAE,CAAC,CAAC;QAE3E,IAAI,CAAChD,MAAM,EAAE;MACf,CAAC;MACDuC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC1D,cAAc,CAACyD,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;EAEQM,aAAaA,CAAA;IACnB;IACA,IAAIH,aAAa,GAAG,IAAI,CAAC3D,WAAW,CAAC4D,gBAAgB,EAAE;IAEvD,IAAID,aAAa,IAAI,IAAI,EAAE;MACzB,IAAIQ,QAAQ,GAAG,IAAI3J,uDAAY,EAAE;MACjC2J,QAAQ,CAAC1J,OAAO,GAAG,IAAIA,kDAAO,EAAE;MAEhC,IAAI,IAAI,CAAC4F,MAAM,EAAE;QACf8D,QAAQ,GAAG,IAAI,CAACxD,cAAc;;MAGhC,IAAIoD,IAAI,GAAG9B,MAAM,CAACC,MAAM,CAAC,IAAI1H,uDAAY,EAAE,EAAE2J,QAAQ,CAAC;MAEtDJ,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAACjF,SAAS,CAACW,KAAK;MACrCkG,IAAI,CAAC1B,QAAQ,GAAG,IAAI,CAACjF,QAAQ,CAACS,KAAK;MACnCkG,IAAI,CAAC9H,OAAO,GAAG,IAAI,CAAC0F,KAAK,CAAC9D,KAAK;MAC/BkG,IAAI,CAACrI,QAAQ,GAAG,IAAI,CAACgG,MAAM,CAAC7D,KAAK;MACjCkG,IAAI,CAACK,cAAc,GAAG,EAAE;MACxBL,IAAI,CAACzB,mBAAmB,GAAG,IAAI,CAACnE,YAAY,CAACN,KAAK;MAClDkG,IAAI,CAAChC,eAAe,GAAG,IAAI,CAACrE,eAAe,CAACG,KAAK;MACjDkG,IAAI,CAACf,SAAS,GAAG,IAAI,CAACqB,gBAAgB,EAAE;MAExC,IAAI,CAAC,IAAI,CAAChE,MAAM,EAAE;QAChB0D,IAAI,CAACO,QAAQ,GAAGX,aAAa,CAACO,MAAM;QACpCH,IAAI,CAACQ,IAAI,GAAG7J,gDAAK,CAAC8J,KAAK;;MAGzB,OAAOT,IAAI;;EAEf;EAEAM,gBAAgBA,CAAA;IACd,MAAMI,sBAAsB,GAA0B,IAAI,CAACzF,gBAAgB,CAACnB,KAAK,CAAC6G,GAAG,CACnF,CAAC7G,KAAc,EAAE8G,KAAa,MAAM;MAClCC,WAAW,EAAE,IAAI,CAACpG,YAAY,CAACmG,KAAK,CAAC,CAACzB,IAAI;MAC1C2B,QAAQ,EAAEhH;KACX,CAAC,CACH;IAED,IAAIiH,qBAAqB,GAAGL,sBAAsB,CAC/CM,MAAM,CAACjC,OAAO,IAAIA,OAAO,CAAC+B,QAAQ,CAAC,CACnCH,GAAG,CAAC5B,OAAO,IAAIA,OAAO,CAAC8B,WAAW,CAAC,CACnCI,IAAI,CAAC,GAAG,CAAC;IAEZ,OAAOF,qBAAqB;EAC9B;EAEAG,UAAUA,CAAA;IACR,IAAI,CAAC9D,UAAU,EAAE;IACjB,IAAI,CAACD,MAAM,EAAE;EACf;EAEAgE,kBAAkBA,CAAA;IAChB,OAAO,mBAAmB,GAAG,IAAI,CAACxH,eAAe,CAACG,KAAK,GAAG,GAAG;EAC/D;EAEAsH,sBAAsBA,CAAA;IACpB,IAAIC,GAAG,GAAG,KAAK;IAEf,IAAI,IAAI,CAACtD,SAAS,EAAE;MAClB,IAAIJ,MAAM,GAAG,IAAI,CAAC3F,WAAW,CAACsJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5J,QAAQ,IAAI,IAAI,CAACoG,SAAS,CAAC;MAErE,IAAIJ,MAAM,CAAC6D,cAAc,EAAE;QACzB,IAAIC,UAAU,GAAG9D,MAAM,CAAC6D,cAAc,CAACE,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,UAAU,IAAI/K,4DAAiB,CAACgL,YAAY,CAAC;QACrGP,GAAG,GAAGI,UAAU,IAAI,CAAC;;;IAGzB,OAAOJ,GAAG;EACZ;EAEAQ,sBAAsBA,CAAA;IACpB,IAAIR,GAAG,GAAG,KAAK;IAEf,IAAI,IAAI,CAACtD,SAAS,EAAE;MAClB,IAAIJ,MAAM,GAAG,IAAI,CAAC3F,WAAW,CAACsJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5J,QAAQ,IAAI,IAAI,CAACoG,SAAS,CAAC;MAErE,IAAIJ,MAAM,CAAC6D,cAAc,EAAE;QACzB,IAAIC,UAAU,GAAG9D,MAAM,CAAC6D,cAAc,CAACE,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,UAAU,IAAI/K,4DAAiB,CAACkL,YAAY,CAAC;QACrGT,GAAG,GAAGI,UAAU,IAAI,CAAC;;;IAGzB,OAAOJ,GAAG;EACZ;EAEA;EACA7F,cAAcA,CAAA;IACZ,IAAIkB,IAAI,GAAG,IAAI7F,uDAAY,EAAE;IAC7B6F,IAAI,CAACqF,KAAK,GAAG,cAAc;IAC3BrF,IAAI,CAACsF,IAAI,GACP,sGAAsG;IACxGtF,IAAI,CAACuF,YAAY,GAAG,IAAI;IACxBvF,IAAI,CAACwF,aAAa,GAAG,KAAK;IAE1B,MAAMC,SAAS,GAAG,IAAI,CAAC/F,MAAM,CAACgG,IAAI,CAACtL,6EAAsB,EAAE;MACzDuL,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClB5F,IAAI,EAAEA;KACP,CAAC;IAEFyF,SAAS,CAACI,WAAW,EAAE,CAACtF,SAAS,CAACuF,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,qBAAqB,EAAE;;IAEhC,CAAC,CAAC;EACJ;EAEAA,qBAAqBA,CAAA;IACnB,IAAI,CAACzG,cAAc,CAACuB,KAAK,EAAE;IAE3B,IAAI,CAACpB,cAAc,CAACuG,iBAAiB,CAAC,IAAI,CAAC9F,cAAc,CAACuD,MAAM,CAAC,CAAClD,SAAS,CAAC;MAC1EqC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACpC,MAAM,EAAE;QACb,IAAI,CAACnB,cAAc,CAACyD,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC1D,cAAc,CAACyD,IAAI,EAAE;QAC1B,IAAI,CAACE,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAiD,UAAUA,CAAA;IACR,OAAO,IAAI,CAAC/F,cAAc,IAAI,IAAI,CAACA,cAAc,CAACuD,MAAM,GAAG,CAAC;EAC9D;;;uBA1TWxE,iBAAiB,EAAA5E,+DAAA,CAAA8L,8CAAA,GAAA9L,+DAAA,CAAAgM,4DAAA,GAAAhM,+DAAA,CAAAgM,oDAAA,GAAAhM,+DAAA,CAAAmM,2DAAA,GAAAnM,+DAAA,CAAAmM,wDAAA,GAAAnM,+DAAA,CAAAmM,iEAAA,GAAAnM,+DAAA,CAAAmM,2DAAA,GAAAnM,+DAAA,CAAAwM,gEAAA,GAAAxM,+DAAA,CAAA0M,uDAAA;IAAA;EAAA;;;YAAjB9H,iBAAiB;MAAAgI,SAAA;MAAAC,QAAA,GAAA7M,wEAAA;MAAA+M,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjC9BpN,4DAAA,aAA6B;UACVA,wDAAA,qBAAAsN,8DAAA;YAAA,OAAWD,GAAA,CAAAjH,MAAA,EAAQ;UAAA,EAAC;UAAgBpG,0DAAA,EAAkB;UAEvEA,4DAAA,aAAiB;UAEbA,wDAAA,IAAAuN,+BAAA,gBAAoC;UACpCvN,wDAAA,IAAAwN,+BAAA,gBAA0C;UAC1CxN,4DAAA,aAAgC;UAC9BA,wDAAA,IAAAyN,iCAAA,mBA6BO;UACTzN,0DAAA,EAAM;UAERA,wDAAA,IAAA0N,gCAAA,iBAuBM;UAEN1N,wDAAA,IAAA2N,gCAAA,iBAkBM;UACR3N,0DAAA,EAAM;UACNA,4DAAA,cAAiB;UAKqCA,wDAAA,mBAAA4N,oDAAA;YAAA,OAASP,GAAA,CAAA1L,QAAA,EAAU;UAAA,EAAC;UAC9D3B,wDAAA,KAAA6N,kCAAA,kBAAsC;UACtC7N,wDAAA,KAAA8N,kCAAA,kBAAgC;UAClC9N,0DAAA,EAAS;UAEXA,4DAAA,cAAoB;UACyDA,wDAAA,mBAAA+N,oDAAA;YAAA,OAASV,GAAA,CAAAlD,UAAA,EAAY;UAAA,EAAC;UAC/FnK,oDAAA,gBACF;UAAAA,0DAAA,EAAS;UAGXA,wDAAA,KAAAgO,iCAAA,kBAIM;UACNhO,4DAAA,cAAoB;UAClBA,wDAAA,KAAAiO,uCAAA,uBAA0D;UAC5DjO,0DAAA,EAAM;;;UAvGLA,uDAAA,GAAa;UAAbA,wDAAA,UAAAqN,GAAA,CAAA9H,MAAA,CAAa;UACbvF,uDAAA,GAAY;UAAZA,wDAAA,SAAAqN,GAAA,CAAA9H,MAAA,CAAY;UAERvF,uDAAA,GAAU;UAAVA,wDAAA,SAAAqN,GAAA,CAAAlL,IAAA,CAAU;UAgCfnC,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAqN,GAAA,CAAAhD,sBAAA,GAA8B;UAyB9BrK,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAqN,GAAA,CAAAvC,sBAAA,GAA8B;UAyBqC9K,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAqN,GAAA,CAAAlL,IAAA,CAAA+L,KAAA,CAAwB;UAChFlO,uDAAA,GAAa;UAAbA,wDAAA,UAAAqN,GAAA,CAAA9H,MAAA,CAAa;UACbvF,uDAAA,GAAY;UAAZA,wDAAA,SAAAqN,GAAA,CAAA9H,MAAA,CAAY;UASjBvF,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAqN,GAAA,CAAAzB,UAAA,GAAkB;UAMV5L,uDAAA,GAAc;UAAdA,wDAAA,SAAAqN,GAAA,CAAAc,QAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;AC3GU;;;;;;;;;;;;;;;;;;;;;;;;;;ACGhD;AAC4C;AAGiC;AACN;AAKvE;AACwE;AACC;AACN;;;;;;;;;ICfjEnO,4DAAA,aAA6E;IAE/CA,oDAAA,kBAAW;IAAAA,0DAAA,EAAI;IAE3CA,4DAAA,aAAmB;IACWA,oDAAA,kBAAW;IAAAA,0DAAA,EAAI;IAE7CA,4DAAA,aAAmC;IAC8BA,oDAAA,gBAAS;IAAAA,0DAAA,EAAS;IACjFA,4DAAA,kBAAiE;IAAAA,oDAAA,iBAAS;IAAAA,0DAAA,EAAS;;;;IAT7BA,wDAAA,YAAA0O,MAAA,CAAAC,MAAA,CAAkB;;;;;;IAcxE3O,4DAAA,cAIC;IADCA,wDAAA,mBAAA4O,+EAAA;MAAA,MAAAC,WAAA,GAAA7O,2DAAA,CAAA8O,GAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAtK,MAAA,GAAA1E,2DAAA;MAAA,OAASA,yDAAA,CAAA0E,MAAA,CAAAuK,cAAA,CAAAF,UAAA,CAAuB;IAAA,EAAC;IAEjC/O,uDAAA,cAA8D;IAE9DA,4DAAA,cAAiB;IAESA,oDAAA,GAAuB;IAAAA,0DAAA,EAAK;IAClDA,4DAAA,WAAM;IAAAA,oDAAA,GAAiD;IAAAA,0DAAA,EAAO;;;;IADxCA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAA+O,UAAA,CAAA1H,SAAA,CAAuB;IACvCrH,uDAAA,GAAiD;IAAjDA,gEAAA,KAAA+O,UAAA,CAAAI,SAAA,QAAAJ,UAAA,CAAAK,UAAA,KAAiD;;;;;IAX/DpP,qEAAA,GAAyC;IACvCA,wDAAA,IAAAsP,yDAAA,kBAaM;IACRtP,mEAAA,EAAe;;;;IAbSA,uDAAA,GAAe;IAAfA,wDAAA,YAAAuD,MAAA,CAAAiM,YAAA,CAAe;;;;;;;;IAevCxP,4DAAA,cAAyC;IAGnCA,oDAAA,2CAAmC;IAAAA,4DAAA,YAAmC;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAI;IAAAA,oDAAA,SACxF;IAAAA,0DAAA,EAAI;;;IADoCA,uDAAA,GAA+B;IAA/BA,wDAAA,eAAAA,6DAAA,IAAA0P,GAAA,EAA+B;;;;;IAK3E1P,4DAAA,cAAiE;IAEzCA,oDAAA,qDAA8C;IAAAA,0DAAA,EAAK;;;;;IA5B7EA,4DAAA,cAA+E;IAC7EA,wDAAA,IAAA2P,mDAAA,2BAee;IAEf3P,wDAAA,IAAA4P,0CAAA,iBAMM;IAEN5P,wDAAA,IAAA6P,0CAAA,iBAIM;IACR7P,0DAAA,EAAM;;;;IA/BsDA,wDAAA,YAAA8P,MAAA,CAAAnB,MAAA,CAAkB;IAC7D3O,uDAAA,GAAwB;IAAxBA,wDAAA,SAAA8P,MAAA,CAAAC,gBAAA,GAAwB;IAiBjC/P,uDAAA,GAAqB;IAArBA,wDAAA,SAAA8P,MAAA,CAAAE,eAAA,CAAqB;IAQrBhQ,uDAAA,GAA6C;IAA7CA,wDAAA,UAAA8P,MAAA,CAAAC,gBAAA,OAAAD,MAAA,CAAAG,eAAA,CAA6C;;;;;IAOrDjQ,4DAAA,cAAyC;IAErCA,uDAAA,sBAGe;IACjBA,0DAAA,EAAM;;;ADlBJ,MAAOkQ,qBAAqB;EAQhCrL,YACUC,KAAqC,EACrCE,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBE,cAA8B,EAC9B+K,eAAgC;IALhC,KAAArL,KAAK,GAALA,KAAK;IACL,KAAAE,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAE,cAAc,GAAdA,cAAc;IACd,KAAA+K,eAAe,GAAfA,eAAe;IAZzB,KAAAX,YAAY,GAAmB,EAAE;IAEjC,KAAAQ,eAAe,GAAY,KAAK;IAEhC,KAAAC,eAAe,GAAY,KAAK;IA+ChC,KAAAG,sBAAsB,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,CAAC,IAAI,CAACC,cAAc,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;EAtC9F;EAEH9K,QAAQA,CAAA;IACN,IAAI,CAAC+K,wBAAwB,GAAG,IAAI,CAACzL,KAAK,CAACmB,IAAI,CAAC3G,mDAAM,CAAC+O,yEAAQ,CAAC,CAAC,CAACnI,SAAS,CAAEsK,KAAoB,IAAI;MACnG,IAAI,CAACvL,cAAc,CAACuB,KAAK,EAAE;MAC3B,IAAI,CAACgJ,YAAY,GAAGgB,KAAK,CAACC,IAAI;MAC9B,IAAI,CAACL,sBAAsB,EAAE;MAE7B;MACA,IAAI,CAAC,IAAI,CAACZ,YAAY,IAAI,IAAI,CAACA,YAAY,CAACkB,MAAM,GAAG,CAAC,EAAE;QACtD,IAAI,CAACtL,cAAc,CAACuL,YAAY,CAAC,IAAI,CAAC;OACvC,MAAM;QACL,IAAI,IAAI,CAACvL,cAAc,CAACwL,YAAY,EAAE,EAAE;UACtC,IAAI,CAACZ,eAAe,GAAG,IAAI;UAC3B,IAAI,CAAC5K,cAAc,CAACuL,YAAY,CAAC,KAAK,CAAC;;;MAI3C,IAAI,CAAC1L,cAAc,CAACyD,IAAI,EAAE;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACmI,yBAAyB,GAAG,IAAI,CAAC/L,KAAK,CACxCmB,IAAI,CAAC3G,mDAAM,CAACuJ,6EAAa,CAAC,CAAC,CAC3B3C,SAAS,CAAE+C,IAAkB,IAAI;MAChC,IAAI,CAACgH,eAAe,GAAG,CAAChH,IAAI,CAAC6H,QAAQ;IACvC,CAAC,CAAC;EACN;EAEAxK,WAAWA,CAAA;IACT,IAAI,IAAI,CAACiK,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAAChK,WAAW,EAAE;;IAG7C,IAAI,IAAI,CAACsK,yBAAyB,EAAE;MAClC,IAAI,CAACA,yBAAyB,CAACtK,WAAW,EAAE;;EAEhD;EAIA+J,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACd,YAAY,EAAE;MACrB,MAAMuB,gBAAgB,GAAG,IAAI,CAACvB,YAAY,CAAC5F,GAAG,CAACoH,CAAC,IAAIA,CAAC,CAAC5B,UAAU,CAAC,CAAC6B,QAAQ,EAAE;MAC5E,MAAMC,aAAa,GAAG,IAAI,CAAC1B,YAAY,CAAC5F,GAAG,CAACoH,CAAC,IAAIA,CAAC,CAACpQ,QAAQ,CAACqQ,QAAQ,EAAE,CAAC;MAEvE,IAAI,CAAC/L,WAAW,CAACiM,UAAU,CAACJ,gBAAgB,EAAEG,aAAa,CAAC;;EAEhE;EAEAnB,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACP,YAAY,EAAE;MACtB,OAAO,KAAK;KACb,MAAM;MACL,OAAO,IAAI,CAACA,YAAY,CAACkB,MAAM,GAAG,CAAC;;EAEvC;EAEAzB,cAAcA,CAAC9I,OAAqB;IAClC,IAAI,CAACrB,KAAK,CAACoE,QAAQ,CAACkF,mFAAgB,CAAC;MAAEgD,KAAK,EAAEjL;IAAO,CAAE,CAAC,CAAC;IACzD,IAAI,CAACnB,MAAM,CAACyB,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAChD;;;uBA5EWyJ,qBAAqB,EAAAlQ,+DAAA,CAAA8L,8CAAA,GAAA9L,+DAAA,CAAAgM,mDAAA,GAAAhM,+DAAA,CAAAmM,2DAAA,GAAAnM,+DAAA,CAAAmM,wDAAA,GAAAnM,+DAAA,CAAAmM,2DAAA,GAAAnM,+DAAA,CAAAmM,4DAAA;IAAA;EAAA;;;YAArB+D,qBAAqB;MAAAtD,SAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoE,+BAAAlE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClClCpN,4DAAA,aAAwC;UACtCA,wDAAA,IAAAuR,oCAAA,kBAWM;UACNvR,wDAAA,IAAAwR,oCAAA,iBA+BM;UAENxR,wDAAA,IAAAyR,oCAAA,iBAOM;UACRzR,0DAAA,EAAM;;;UArDEA,uDAAA,GAAsB;UAAtBA,wDAAA,UAAAqN,GAAA,CAAA4C,eAAA,CAAsB;UAYtBjQ,uDAAA,GAAsB;UAAtBA,wDAAA,UAAAqN,GAAA,CAAA4C,eAAA,CAAsB;UAiCtBjQ,uDAAA,GAAqB;UAArBA,wDAAA,SAAAqN,GAAA,CAAA4C,eAAA,CAAqB;;;;;;mBDxBf,CACV3B,4DAAO,CAAC,QAAQ,EAAE,CAChBC,+DAAU,CACR,QAAQ,EACRC,iEAAY,CAACC,+CAAM,EAAE;UACnB;UACAiD,MAAM,EAAE;YAAEC,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAC;SAChC,CAAC,CACH,CACF,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;AE/BoD;AAEvD;AACwE;AAExE;AAC8E;;;AAE9E,MAAMI,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEhC,8DAAqB;EAChCiC,OAAO,EAAE;IAAEC,QAAQ,EAAEN,iEAAoBA;EAAA;CAC1C,EACD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEtN,0DAAiB;EAC5BuN,OAAO,EAAE;IAAEvM,OAAO,EAAEmM,gEAAmBA;EAAA;CACxC,EACD;EACEE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEtN,0DAAiB;EAC5BuN,OAAO,EAAE;IAAEvM,OAAO,EAAEmM,gEAAmBA;EAAA;CACxC,CACF;AAMK,MAAOM,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BR,yDAAY,CAACS,QAAQ,CAACN,MAAM,CAAC,EAC7BH,yDAAY;IAAA;EAAA;;;sHAEXQ,2BAA2B;IAAAE,OAAA,GAAAzG,yDAAA;IAAA0G,OAAA,GAF5BX,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BuB;AACM;AAErD;AAC2D;AACI;AACG;AACX;AACE;AACE;AAE3D;AACuD;AAEvD;AAC0C;AACI;AAE9C;AAC0F;AAE1F;AACsE;AAES;AACP;;;;AAsBlE,MAAO0B,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAjB7Bd,yDAAY,EACZC,+DAAmB,EACnBL,wFAA2B,EAC3Ba,oDAAW,CAACM,UAAU,CAACH,iFAAkB,EAAED,8EAAe,CAAC,EAC3DD,wDAAa,CAACK,UAAU,CAAC,CAACF,8EAAe,CAAC,CAAC;MAE3C;MACAT,6EAAkB,EAClBG,sEAAe,EACfF,kEAAa,EACbC,oEAAc,EACdJ,sEAAe,EACfC,0EAAiB;MACjB;MACAK,+DAAY;IAAA;EAAA;;;sHAGHM,oBAAoB;IAAAE,YAAA,GAnBhBvD,8DAAqB,EAAEtL,0DAAiB;IAAA2N,OAAA,GAErDE,yDAAY,EACZC,+DAAmB,EACnBL,wFAA2B,EAAAvG,2DAAA,EAAAE,+DAAA;IAI3B;IACA6G,6EAAkB,EAClBG,sEAAe,EACfF,kEAAa,EACbC,oEAAc,EACdJ,sEAAe,EACfC,0EAAiB;IACjB;IACAK,+DAAY;EAAA;AAAA", "sources": ["./src/app/manage-children/components/add-child/add-child.component.ts", "./src/app/manage-children/components/add-child/add-child.component.html", "./src/app/manage-children/components/index.ts", "./src/app/manage-children/components/list-children/list-children.component.ts", "./src/app/manage-children/components/list-children/list-children.component.html", "./src/app/manage-children/manage-children-routing.module.ts", "./src/app/manage-children/manage-children.module.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\n\nimport { AllergiesModel, AllergyCheckBoxData, allergiesData } from '../../../sharedModels';\n\n// Ngrx\nimport { Store, select } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport { FamilyState } from '../../../states';\nimport { selectedChild } from '../../../states/children/children.selectors';\nimport { familyLoadChildren } from '../../../states/children/children.actions';\n\nimport {\n  BaseComponent,\n  UserCashless,\n  Address,\n  Roles,\n  School,\n  SchoolClass,\n  ListClasses,\n  SchoolFeatureEnum,\n  ConfirmModal,\n} from '../../../sharedModels';\nimport { SpinnerService, UserService, SchoolClassesService, StudentService } from '../../../sharedServices';\nimport { DialogConfirmComponent } from 'src/app/shared/components';\n\n@Component({\n  selector: 'manage-children-add-child',\n  templateUrl: './add-child.component.html',\n  styleUrls: ['./add-child.component.scss'],\n})\nexport class AddChildComponent extends BaseComponent implements OnInit, OnDestroy {\n  listSchools: School[] = [];\n  listClass: SchoolClass[];\n  errorAPI: any;\n  form: FormGroup;\n  textSubmit: string;\n  showButtonCancel: boolean;\n  IsEdit: boolean = false;\n  private _schoolId: number;\n  private _studentToEdit: UserCashless;\n  private subscriptionStudent$: Subscription;\n  ALLERGY_DATA: AllergiesModel[] = allergiesData;\n  allergyForm: FormGroup;\n\n  constructor(\n    private store: Store<{ family: FamilyState }>,\n    private activatedRoute: ActivatedRoute,\n    private router: Router,\n    private spinnerService: SpinnerService,\n    private userService: UserService,\n    private classesService: SchoolClassesService,\n    private studentService: StudentService,\n    public dialog: MatDialog,\n    private formBuilder: FormBuilder\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    let temp = this.activatedRoute.snapshot.data['schools'];\n    this.listSchools = temp.schools;\n    this._studentToEdit = new UserCashless();\n\n    // If Edit\n    if (this.router.url.indexOf('/edit') > -1) {\n      this.subscriptionStudent$ = this.store\n        .pipe(select(selectedChild))\n        .subscribe((student: UserCashless) => {\n          this._studentToEdit = student;\n          this.IsEdit = true;\n\n          if (!this._studentToEdit) {\n            this._studentToEdit = new UserCashless();\n            this.GoBack();\n          }\n          this.CreateForm();\n        });\n    } else {\n      this.CreateForm();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptionStudent$?.unsubscribe();\n  }\n\n  GoBack() {\n    this.spinnerService.start();\n    this.router.navigate(['/family/children'], { relativeTo: this.activatedRoute });\n  }\n\n  ////////////////////////////////////////\n  // Form\n  ////////////////////////////////////////\n  get firstName() {\n    return this.form.get('firstName');\n  }\n\n  get lastName() {\n    return this.form.get('lastName');\n  }\n\n  get school() {\n    return this.form.get('school');\n  }\n\n  get class() {\n    return this.form.get('class');\n  }\n\n  get allowCanteen() {\n    return this.form.get('allowCanteen');\n  }\n\n  get favouriteColour() {\n    return this.form.get('favouriteColour');\n  }\n\n  get allergyFormArray() {\n    return this.allergyForm.get('allergies') as FormArray;\n  }\n\n  getErrorMessageFirstName() {\n    return this.firstName.hasError('required') ? 'You must enter a value' : '';\n  }\n\n  getErrorMessageLastName() {\n    return this.lastName.hasError('required') ? 'You must enter a value' : '';\n  }\n\n  getErrorMessageClass() {\n    return this.class.hasError('required') ? 'You must enter a value' : '';\n  }\n\n  CreateForm() {\n    this._schoolId = this._studentToEdit.SchoolId;\n\n    if (!this._studentToEdit.FavouriteColour || this._studentToEdit.FavouriteColour == '') {\n      let studentCopy = Object.assign(new UserCashless(), this._studentToEdit);\n      studentCopy.FavouriteColour = 'blue';\n      this._studentToEdit = studentCopy;\n    }\n\n    this.form = new FormGroup({\n      firstName: new FormControl(this._studentToEdit.FirstName, [Validators.required]),\n      lastName: new FormControl(this._studentToEdit.Lastname, [Validators.required]),\n      school: new FormControl(this._studentToEdit.SchoolId),\n      class: new FormControl(this._studentToEdit.ClassId, [Validators.required]),\n      allowCanteen: new FormControl(this._studentToEdit.AllowCanteenToOrder),\n      favouriteColour: new FormControl(this._studentToEdit.FavouriteColour),\n    });\n\n    this.generateAllergyForm();\n\n    // on value change\n    this.school.valueChanges.subscribe(newValue => {\n      this._schoolId = newValue;\n      this.LoadClass(newValue);\n    });\n\n    // If edit we need to load the class from the start\n    if (this.IsEdit && this._studentToEdit) {\n      this.LoadClass(this._studentToEdit.SchoolId);\n    }\n  }\n\n  generateAllergyForm() {\n    //generate dynamic allergy form\n    this.allergyForm = this.formBuilder.group({\n      allergies: new FormArray([]),\n    });\n\n    // Create form control for each allergy checkbox value\n    this.ALLERGY_DATA.forEach(allergy => {\n      this.allergyFormArray.push(new FormControl(this._studentToEdit?.Allergies?.includes(allergy.code)));\n    });\n  }\n\n  /** Load Class */\n  private LoadClass(schoolId: number) {\n    if (schoolId) {\n      this.spinnerService.start();\n      this.classesService.GetClassesBySchoolAPI(schoolId, true).subscribe({\n        next: (response: ListClasses) => {\n          this.listClass = response.Classes;\n          this.spinnerService.stop();\n        },\n        error: error => {\n          this.handleErrorFromService(error);\n          this.spinnerService.stop();\n        },\n      });\n    }\n  }\n\n  ////////////////////////////////////////\n  // Add User\n  ////////////////////////////////////////\n  onSubmit() {\n    this.spinnerService.start();\n\n    let connectedUser = this.userService.GetUserConnected();\n\n    this.userService.UpsertUser(this.convertObject()).subscribe({\n      next: (user: UserCashless) => {\n        // init form\n        this.CreateForm();\n\n        // refreshStore\n        this.store.dispatch(familyLoadChildren({ parentId: connectedUser.UserId }));\n\n        this.GoBack();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n\n  private convertObject(): UserCashless {\n    // Get parent Id\n    let connectedUser = this.userService.GetUserConnected();\n\n    if (connectedUser != null) {\n      let initUser = new UserCashless();\n      initUser.Address = new Address();\n\n      if (this.IsEdit) {\n        initUser = this._studentToEdit;\n      }\n\n      let user = Object.assign(new UserCashless(), initUser);\n\n      user.FirstName = this.firstName.value;\n      user.Lastname = this.lastName.value;\n      user.ClassId = this.class.value;\n      user.SchoolId = this.school.value;\n      user.FirebaseUserId = '';\n      user.AllowCanteenToOrder = this.allowCanteen.value;\n      user.FavouriteColour = this.favouriteColour.value;\n      user.Allergies = this.getAllergyString();\n\n      if (!this.IsEdit) {\n        user.ParentId = connectedUser.UserId;\n        user.Role = Roles.Child;\n      }\n\n      return user;\n    }\n  }\n\n  getAllergyString(): string {\n    const processedAllergyValues: AllergyCheckBoxData[] = this.allergyFormArray.value.map(\n      (value: boolean, index: number) => ({\n        allergyCode: this.ALLERGY_DATA[index].code,\n        selected: value,\n      })\n    );\n\n    let selectedAllergyString = processedAllergyValues\n      .filter(allergy => allergy.selected)\n      .map(allergy => allergy.allergyCode)\n      .join(',');\n\n    return selectedAllergyString;\n  }\n\n  CancelForm() {\n    this.CreateForm();\n    this.GoBack();\n  }\n\n  GetBackgroundColor() {\n    return 'background-color:' + this.favouriteColour.value + ';';\n  }\n\n  HasPayAtCanteenFeature(): boolean {\n    let res = false;\n\n    if (this._schoolId) {\n      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);\n\n      if (school.SchoolFeatures) {\n        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == SchoolFeatureEnum.PayAtCanteen);\n        res = hasFeature >= 0;\n      }\n    }\n    return res;\n  }\n\n  HasAllergyAlertFeature(): boolean {\n    let res = false;\n\n    if (this._schoolId) {\n      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);\n\n      if (school.SchoolFeatures) {\n        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == SchoolFeatureEnum.AllergyAlert);\n        res = hasFeature >= 0;\n      }\n    }\n    return res;\n  }\n\n  ////////////////////////// Archive child\n  ArchiveClicked() {\n    let data = new ConfirmModal();\n    data.Title = 'Delete Child';\n    data.Text =\n      'Deleting will inactivate the child profile permanently and cannot be undone. Do you want to proceed?';\n    data.CancelButton = 'No';\n    data.ConfirmButton = 'Yes';\n\n    const dialogRef = this.dialog.open(DialogConfirmComponent, {\n      width: '500px',\n      disableClose: true,\n      data: data,\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.ArchiveClickConfirmed();\n      }\n    });\n  }\n\n  ArchiveClickConfirmed() {\n    this.spinnerService.start();\n\n    this.studentService.ArchiveStudentAPI(this._studentToEdit.UserId).subscribe({\n      next: (response: any) => {\n        this.GoBack();\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.spinnerService.stop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  CanArchive() {\n    return this._studentToEdit && this._studentToEdit.UserId > 0;\n  }\n}\n", "<div class=\"container-fluid\">\n  <nav-back-button (navBack)=\"GoBack()\" text=\"Go back\"></nav-back-button>\n\n  <div class=\"row\">\n    <div class=\"col-12 col-md-6\">\n      <h2 *ngIf=\"!IsEdit\">Add a child</h2>\n      <h2 *ngIf=\"IsEdit\">Edit child details</h2>\n      <div class=\"cardDefaultCanteen\">\n        <form *ngIf=\"form\" [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"cashlessForm\">\n          <mat-form-field appearance=\"outline\">\n            <mat-label>First name</mat-label>\n            <input matInput placeholder=\"First name\" formControlName=\"firstName\" type=\"text\" required />\n            <mat-error *ngIf=\"firstName.invalid\">{{ getErrorMessageFirstName() }}</mat-error>\n          </mat-form-field>\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Last name</mat-label>\n            <input matInput placeholder=\"Last name\" formControlName=\"lastName\" type=\"text\" required />\n            <mat-error *ngIf=\"lastName.invalid\">{{ getErrorMessageLastName() }}</mat-error>\n          </mat-form-field>\n          <mat-form-field appearance=\"outline\" *ngIf=\"listSchools\">\n            <mat-label>School name</mat-label>\n            <mat-select placeholder=\"School\" formControlName=\"school\" required>\n              <mat-option *ngFor=\"let school of listSchools\" [value]=\"school.SchoolId\">{{\n                school.Name\n              }}</mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Class</mat-label>\n            <mat-select placeholder=\"Class\" formControlName=\"class\" required>\n              <mat-option *ngIf=\"!listClass\" [value]=\"\">No class</mat-option>\n              <mat-option *ngFor=\"let class of listClass\" [value]=\"class.ClassId\">{{\n                class.Name\n              }}</mat-option>\n            </mat-select>\n          </mat-form-field>\n        </form>\n      </div>\n    </div>\n    <div *ngIf=\"HasPayAtCanteenFeature()\" class=\"col-12 col-md-6\">\n      <h2>Canteen order</h2>\n      <div class=\"cardDefaultCanteen\">\n        <p>\n          Allow the canteen to place orders on your behalf when your child wants to order directly at the\n          counter. To confirm the order, the canteen will ask your child their favourite colour.\n        </p>\n        <p>\n          The order amount will be automatically deducted from your balance. There are no automatic top ups,\n          so if your balance is low, the order will be declined.\n        </p>\n        <form *ngIf=\"form\" [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"cashlessForm\">\n          <div class=\"authorizeCheckbox\">\n            <mat-checkbox formControlName=\"allowCanteen\">Authorize Canteen Order</mat-checkbox>\n          </div>\n\n          <colour-picker\n            *ngIf=\"allowCanteen.value == true\"\n            [favouriteColour]=\"favouriteColour.value\"\n            (favouriteColourChanged)=\"favouriteColour.setValue($event)\"\n          ></colour-picker>\n        </form>\n      </div>\n    </div>\n\n    <div *ngIf=\"HasAllergyAlertFeature()\" class=\"col-12 col-md-6\">\n      <h2>Allergies</h2>\n      <div class=\"cardDefaultCanteen\">\n        <p>\n          Let us know about any allergies, so they can be printed on your child’s lunch bag labels by the\n          canteen.\n        </p>\n        <form *ngIf=\"form\" [formGroup]=\"allergyForm\" (ngSubmit)=\"onSubmit()\" class=\"cashlessForm\">\n          <div class=\"pb-2\" formArrayName=\"allergies\">\n            <mat-checkbox\n              *ngFor=\"let alias of allergyFormArray.controls; let i = index\"\n              class=\"allergyCheckBox\"\n              [formControlName]=\"i\"\n              >{{ ALLERGY_DATA[i].title }}</mat-checkbox\n            >\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n  <div class=\"row\">\n    <div class=\"col-12 col-md-6\">\n      <div class=\"cardDefaultCanteen\">\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <button class=\"PrimaryButton\" type=\"button\" (click)=\"onSubmit()\" [disabled]=\"!form.valid\">\n              <span *ngIf=\"!IsEdit\">Add child</span>\n              <span *ngIf=\"IsEdit\">Save</span>\n            </button>\n          </div>\n          <div class=\"col-12\">\n            <button mat-flat-button type=\"button\" class=\"SecondaryButton cancelButton\" (click)=\"CancelForm()\">\n              Cancel\n            </button>\n          </div>\n\n          <div *ngIf=\"CanArchive()\" class=\"col-12 pt-2\">\n            <button mat-stroked-button color=\"warn\" class=\"archiveButton\" (click)=\"ArchiveClicked()\">\n              Delete child\n            </button>\n          </div>\n          <div class=\"col-12\">\n            <mat-error *ngIf=\"errorAPI\">{{ WriteError() }}</mat-error>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "export * from './add-child/add-child.component';\nexport * from './list-children/list-children.component';\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\n\n// Ngrx\nimport { Store, select } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport { ChildrenState, FamilyState } from '../../../states';\nimport { SetSelectedChild } from '../../../states/children/children.actions';\nimport { children } from '../../../states/children/children.selectors';\n\nimport { UserCashless } from '../../../sharedModels';\nimport { SpinnerService, UserService, StudentService, DebounceService } from '../../../sharedServices';\n\n//Animations\nimport { trigger, transition, useAnimation } from '@angular/animations';\nimport { bounce, fadeIn, flipInX, shake, jello, flip } from 'ng-animate';\nimport { connectedUser } from 'src/app/states/user/user.selectors';\n\n@Component({\n  selector: 'manage-children-list-children',\n  templateUrl: './list-children.component.html',\n  styleUrls: ['./list-children.component.scss'],\n  animations: [\n    trigger('bounce', [\n      transition(\n        '* => *',\n        useAnimation(fadeIn, {\n          // Set the duration to 5seconds and delay to 2seconds\n          params: { timing: 0.5, delay: 0 },\n        })\n      ),\n    ]),\n  ],\n})\nexport class ListChildrenComponent implements OnInit, OnDestroy {\n  private childrenListSubscription: Subscription;\n  listStudents: UserCashless[] = [];\n  bounce: any;\n  addedFirstChild: boolean = false;\n  private connectedUserSubscription: Subscription;\n  deactivatedUser: boolean = false;\n\n  constructor(\n    private store: Store<{ family: FamilyState }>,\n    private router: Router,\n    private spinnerService: SpinnerService,\n    private userService: UserService,\n    private studentService: StudentService,\n    private debounceService: DebounceService\n  ) {}\n\n  ngOnInit() {\n    this.childrenListSubscription = this.store.pipe(select(children)).subscribe((state: ChildrenState) => {\n      this.spinnerService.start();\n      this.listStudents = state.list;\n      this.UpdateIntercomDebounce();\n\n      // Set text to explain what to do after adding the first student\n      if (!this.listStudents || this.listStudents.length < 1) {\n        this.studentService.SetNoStudent(true);\n      } else {\n        if (this.studentService.GetNoStudent()) {\n          this.addedFirstChild = true;\n          this.studentService.SetNoStudent(false);\n        }\n      }\n\n      this.spinnerService.stop();\n    });\n\n    this.connectedUserSubscription = this.store\n      .pipe(select(connectedUser))\n      .subscribe((user: UserCashless) => {\n        this.deactivatedUser = !user.IsActive;\n      });\n  }\n\n  ngOnDestroy(): void {\n    if (this.childrenListSubscription) {\n      this.childrenListSubscription.unsubscribe();\n    }\n\n    if (this.connectedUserSubscription) {\n      this.connectedUserSubscription.unsubscribe();\n    }\n  }\n\n  UpdateIntercomDebounce = this.debounceService.callDebounce(this.updateIntercom, 350, false, true);\n\n  updateIntercom(): void {\n    // get schools info into Intercom\n    if (this.listStudents) {\n      const schoolNameString = this.listStudents.map(s => s.SchoolName).toString();\n      const schoolIdArray = this.listStudents.map(s => s.SchoolId.toString());\n\n      this.userService.UpdateUser(schoolNameString, schoolIdArray);\n    }\n  }\n\n  ShowListStudents(): boolean {\n    if (!this.listStudents) {\n      return false;\n    } else {\n      return this.listStudents.length > 0;\n    }\n  }\n\n  EditChildClick(student: UserCashless) {\n    this.store.dispatch(SetSelectedChild({ child: student }));\n    this.router.navigate(['family/children/edit']);\n  }\n}\n", "<div class=\"container-fluid divWrapper\">\n  <div *ngIf=\"!deactivatedUser\" class=\"row headerContainer\" [@bounce]=\"bounce\">\n    <div class=\"col-4\">\n      <p class=\"headerTextLeft\">My Children</p>\n    </div>\n    <div class=\"col-4\">\n      <p class=\"headerTextCentre\">My Children</p>\n    </div>\n    <div class=\"col-4 buttonContainer\">\n      <button type=\"button\" class=\"PrimaryButton\" routerLink=\"./add\">Add Child</button>\n      <button type=\"button\" class=\"buttonAddMobile\" routerLink=\"./add\">Add Child</button>\n    </div>\n  </div>\n  <div *ngIf=\"!deactivatedUser\" class=\"row cardDefaultParent\" [@bounce]=\"bounce\">\n    <ng-container *ngIf=\"ShowListStudents()\">\n      <div\n        *ngFor=\"let student of listStudents\"\n        class=\"row col-12 cardChild\"\n        (click)=\"EditChildClick(student)\"\n      >\n        <img class=\"child-image\" src=\"assets/icons/child-blank.svg\" />\n        <!-- <div class=\"row childName\">{{student.FirstName}}</div> -->\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <h4 class=\"childName\">{{ student.FirstName }}</h4>\n            <span>{{ student.ClassName }}, {{ student.SchoolName }}</span>\n          </div>\n        </div>\n      </div>\n    </ng-container>\n\n    <div *ngIf=\"addedFirstChild\" class=\"row\">\n      <div class=\"col-12\">\n        <p class=\"firstChild\">\n          You're now ready to make an order. <a [routerLink]=\"['/family/home']\">Go to Orders</a>.\n        </p>\n      </div>\n    </div>\n\n    <div *ngIf=\"!ShowListStudents() && !deactivatedUser\" class=\"row\">\n      <div class=\"col-12\">\n        <h3 class=\"noChild\">Add your child to begin using Spriggy Schools!</h3>\n      </div>\n    </div>\n  </div>\n\n  <div *ngIf=\"deactivatedUser\" class=\"row\">\n    <div class=\"col-12\">\n      <app-warning\n        title=\"Your account is deactivated\"\n        description=\"Please get in touch with us if you would like to reopen your account.\"\n      ></app-warning>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\n// Components\nimport { AddChildComponent, ListChildrenComponent } from './components';\n\n// Services\nimport { ListStudentsResolver, ListSchoolsResolver } from '../sharedServices';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ListChildrenComponent,\n    resolve: { students: ListStudentsResolver },\n  },\n  {\n    path: 'add',\n    component: AddChildComponent,\n    resolve: { schools: ListSchoolsResolver },\n  },\n  {\n    path: 'edit',\n    component: AddChildComponent,\n    resolve: { schools: ListSchoolsResolver },\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class ManageChildrenRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\n\n// google material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\n\n// modules\nimport { SharedModule } from '../shared/shared.module';\n\n// Ngrx\nimport { StoreModule } from '@ngrx/store';\nimport { EffectsModule } from '@ngrx/effects';\n\n// reducers\nimport { childrenReducer, childrenFeatureKey } from '../states/children/children.reducer';\n\n// Effects\nimport { ChildrenEffects } from '../states/children/children.effects';\n\nimport { ManageChildrenRoutingModule } from './manage-children-routing.module';\nimport { ListChildrenComponent, AddChildComponent } from './components';\n\n@NgModule({\n  declarations: [ListChildrenComponent, AddChildComponent],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    ManageChildrenRoutingModule,\n    StoreModule.forFeature(childrenFeatureKey, childrenReducer),\n    EffectsModule.forFeature([ChildrenEffects]),\n\n    // material\n    MatFormFieldModule,\n    MatSelectModule,\n    MatIconModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCheckboxModule,\n    // other\n    SharedModule,\n  ],\n})\nexport class ManageChildrenModule {}\n"], "names": ["FormArray", "FormControl", "FormGroup", "Validators", "allergiesData", "select", "<PERSON><PERSON><PERSON><PERSON>", "familyLoadC<PERSON><PERSON>n", "BaseComponent", "UserCashless", "Address", "Roles", "SchoolFeatureEnum", "ConfirmModal", "DialogConfirmComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r9", "getErrorMessageFirstName", "ctx_r10", "getErrorMessageLastName", "ɵɵproperty", "school_r15", "SchoolId", "Name", "ɵɵtemplate", "AddChildComponent_form_7_mat_form_field_11_mat_option_4_Template", "ctx_r11", "listSchools", "class_r16", "ClassId", "ɵɵlistener", "AddChildComponent_form_7_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵelement", "AddChildComponent_form_7_mat_error_5_Template", "AddChildComponent_form_7_mat_error_10_Template", "AddChildComponent_form_7_mat_form_field_11_Template", "AddChildComponent_form_7_mat_option_16_Template", "AddChildComponent_form_7_mat_option_17_Template", "ctx_r2", "form", "firstName", "invalid", "lastName", "listClass", "AddChildComponent_div_8_form_8_colour_picker_4_Template_colour_picker_favouriteColourChanged_0_listener", "$event", "_r22", "ctx_r21", "favouriteColour", "setValue", "ctx_r20", "value", "AddChildComponent_div_8_form_8_Template_form_ngSubmit_0_listener", "_r24", "ctx_r23", "AddChildComponent_div_8_form_8_colour_picker_4_Template", "ctx_r19", "allowCanteen", "AddChildComponent_div_8_form_8_Template", "ctx_r3", "i_r28", "ctx_r26", "ALLERGY_DATA", "title", "AddChildComponent_div_9_form_6_Template_form_ngSubmit_0_listener", "_r30", "ctx_r29", "AddChildComponent_div_9_form_6_mat_checkbox_2_Template", "ctx_r25", "allergyForm", "allergyFormArray", "controls", "AddChildComponent_div_9_form_6_Template", "ctx_r4", "AddChildComponent_div_21_Template_button_click_1_listener", "_r32", "ctx_r31", "ArchiveClicked", "ctx_r8", "WriteError", "AddChildComponent", "constructor", "store", "activatedRoute", "router", "spinnerService", "userService", "classesService", "studentService", "dialog", "formBuilder", "IsEdit", "ngOnInit", "temp", "snapshot", "data", "schools", "_studentToEdit", "url", "indexOf", "subscriptionStudent$", "pipe", "subscribe", "student", "GoBack", "CreateForm", "ngOnDestroy", "unsubscribe", "start", "navigate", "relativeTo", "get", "school", "class", "<PERSON><PERSON><PERSON><PERSON>", "getErrorMessageClass", "_schoolId", "FavouriteColour", "studentCopy", "Object", "assign", "FirstName", "required", "Lastname", "AllowCanteenToOrder", "generateAllergyForm", "valueChanges", "newValue", "LoadClass", "group", "allergies", "for<PERSON>ach", "allergy", "push", "Allergies", "includes", "code", "schoolId", "GetClassesBySchoolAPI", "next", "response", "Classes", "stop", "error", "handleErrorFromService", "connectedUser", "GetUserConnected", "UpsertUser", "convertObject", "user", "dispatch", "parentId", "UserId", "initUser", "FirebaseUserId", "getAllergyString", "ParentId", "Role", "Child", "processedAllergyValues", "map", "index", "allergyCode", "selected", "selectedAllergyString", "filter", "join", "CancelForm", "GetBackgroundColor", "HasPayAtCanteenFeature", "res", "find", "x", "SchoolFeatures", "hasFeature", "findIndex", "OptionName", "PayAtCanteen", "HasAllergyAlertFeature", "Allergy<PERSON>lert", "Title", "Text", "CancelButton", "ConfirmButton", "dialogRef", "open", "width", "disableClose", "afterClosed", "result", "ArchiveClickConfirmed", "ArchiveStudentAPI", "CanArchive", "ɵɵdirectiveInject", "i1", "Store", "i2", "ActivatedRoute", "Router", "i3", "SpinnerService", "UserService", "SchoolClassesService", "StudentService", "i4", "MatDialog", "i5", "FormBuilder", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AddChildComponent_Template", "rf", "ctx", "AddChildComponent_Template_nav_back_button_navBack_1_listener", "AddChildComponent_h2_4_Template", "AddChildComponent_h2_5_Template", "AddChildComponent_form_7_Template", "AddChildComponent_div_8_Template", "AddChildComponent_div_9_Template", "AddChildComponent_Template_button_click_15_listener", "AddChildComponent_span_16_Template", "AddChildComponent_span_17_Template", "AddChildComponent_Template_button_click_19_listener", "AddChildComponent_div_21_Template", "AddChildComponent_mat_error_23_Template", "valid", "errorAPI", "SetSelectedChild", "children", "trigger", "transition", "useAnimation", "fadeIn", "ctx_r0", "bounce", "ListChildrenComponent_div_2_ng_container_1_div_1_Template_div_click_0_listener", "restoredCtx", "_r9", "student_r7", "$implicit", "EditChildClick", "ɵɵtextInterpolate2", "ClassName", "SchoolName", "ɵɵelementContainerStart", "ListChildrenComponent_div_2_ng_container_1_div_1_Template", "ɵɵelementContainerEnd", "listStudents", "ɵɵpureFunction0", "_c0", "ListChildrenComponent_div_2_ng_container_1_Template", "ListChildrenComponent_div_2_div_2_Template", "ListChildrenComponent_div_2_div_3_Template", "ctx_r1", "ShowListStudents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deactivatedUser", "ListChildrenComponent", "debounceService", "UpdateIntercomDebounce", "callDebounce", "updateIntercom", "childrenListSubscription", "state", "list", "length", "SetNoStudent", "GetNoStudent", "connectedUserSubscription", "IsActive", "schoolNameString", "s", "toString", "schoolIdArray", "UpdateUser", "child", "DebounceService", "ListChildrenComponent_Template", "ListChildrenComponent_div_1_Template", "ListChildrenComponent_div_2_Template", "ListChildrenComponent_div_3_Template", "params", "timing", "delay", "RouterModule", "ListStudentsResolver", "ListSchoolsResolver", "routes", "path", "component", "resolve", "students", "ManageChildrenRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "ReactiveFormsModule", "MatButtonModule", "MatCheckboxModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatSelectModule", "SharedModule", "StoreModule", "EffectsModule", "childrenReducer", "children<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChildrenEffects", "ManageChildrenModule", "forFeature", "declarations", "StoreFeatureModule", "EffectsFeatureModule"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}