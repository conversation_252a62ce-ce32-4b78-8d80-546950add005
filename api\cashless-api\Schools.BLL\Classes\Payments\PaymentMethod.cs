using System.Collections.Generic;
using Schools.BLL.ThirdParty.General;

namespace Schools.BLL.Classes.Payments
{
    public class PaymentMethods
    {
        public List<PaymentMethod> payment_methods { get; set; }
    }

    /// <summary>
    /// "{\"payment_methods\": [
    ///     {
    ///         \"id\": \"da0101f3-530d-4489-a533-953f47d8296d\",
    ///         \"type\": \"card\",
    ///         \"card\": {
    ///             \"bin\": \"411111\",
    ///             \"last_four\": \"1111\",
    ///             \"masked_number\": \"411111******1111\",
    ///             \"card_type\": \"Visa\",
    ///             \"is_debit\": \"Unknown\",
    ///             \"expiry_month\": \"10\",
    ///             \"expiry_year\": \"2023\",
    ///             \"is_default\": true}}]}"
    /// </summary>
    public class PaymentMethod
    {
        public string id { get; set; }
        public string type { get; set; }
        public Card card { get; set; }
    }

    public class AddPaymentMethodResponse : APIBase
    {
        public string payment_method_id { get; set; }
        public string payment_method_type { get; set; }
        public Card card { get; set; }
    }

    public class Card
    {
        public string bin { get; set; }
        public string last_four { get; set; }
        public string masked_number { get; set; }
        public string card_type { get; set; }
        public string is_debit { get; set; }
        public string expiry_month { get; set; }
        public string expiry_year { get; set; }
        public bool is_default { get; set; }
    }
}