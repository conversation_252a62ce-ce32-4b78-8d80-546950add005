﻿using System;
using System.Collections.Generic;

namespace Schools.BLL.Classes.Audit;

public abstract class BaseAuditLog
{
    public long? ActionnedByUserId { get; set; }
    public string LogType { get; set; }
    public string LogSubType { get; set; }
    public string Description { get; set; }
    public bool Failed { get; set; }
    public DateTime DateCreatedUtc { get; set; }
    public IDictionary<string, string> Metadata { get; set; }

    public BaseAuditLog()
    {
        this.Failed = false;
        this.DateCreatedUtc = DateTime.UtcNow;
        this.Metadata = new Dictionary<string, string>();
    }
}

public static class AuditQueuesConstants
{
    public const string AuditPayment = "auditpayment";
    public const string AuditUser = "audituser";
    public const string AuditMerchant = "auditmerchant";
    public const string AuditOrder = "auditorder";
}