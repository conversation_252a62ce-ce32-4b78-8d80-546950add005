<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <CodeAnalysisRuleSet>../.vscode/StyleCop.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <!--
    // TODO - Move common services into a separate project and import the required libraries
    -->
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.5" />
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="6.0.0" />
    <PackageReference Include="FirebaseAdmin" Version="2.3.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <!--
    // TODO - Move common services into a separate project / NuGet package and import this
    -->
  <ItemGroup>
    <ProjectReference Include="..\..\Cashless.API\Cashless.APIs.csproj" />
    <ProjectReference Include="..\..\Schools.BLL\Schools.BLL.csproj" />
    <ProjectReference Include="..\..\Schools.DAL\Schools.DAL.csproj" />
  </ItemGroup>
</Project>
