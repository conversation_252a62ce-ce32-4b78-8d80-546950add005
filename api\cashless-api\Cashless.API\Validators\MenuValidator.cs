using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using Schools.DAL.Entities;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface IMenuValidator
{
    Task ValidateAccessToCanteen(long canteenId);
    Task ValidateUserAccessToMenu(Menu menu);
    Task ValidateAccessToSchool(long schoolId);
    Task ValidateAccessToSchool(string schoolId);
    Task ValidateAccessToMenuItems(IEnumerable<MenuItem> items);
}

public class MenuValidator : IMenuValidator
{
    private readonly IMenuService _menuService;
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<MenuValidator> _logger;

    public MenuValidator(IMenuService menuService, IAuthenticationValidator authenticationValidator, ILogger<MenuValidator> logger)
    {
        _menuService = menuService;
        _authenticationValidator = authenticationValidator;
        _logger = logger;
    }

    public async Task ValidateUserAccessToMenu(Menu menu)
    {
        if (menu == null) throw new ValidationException("No menu found");
        await _authenticationValidator.ValidateAccessToCanteen((long)menu.CanteenId);
    }

    public async Task ValidateAccessToCanteen(long canteenId)
    {
        await _authenticationValidator.ValidateAccessToCanteen(canteenId);
    }

    public async Task ValidateAccessToSchool(string schoolId)
    {
        var isValidInt = int.TryParse(schoolId, out int id);

        if (!isValidInt) throw new ValidationException("invalid school id");

        await _authenticationValidator.ValidateAccessToSchool(id);
    }

    public async Task ValidateAccessToSchool(long schoolId)
    {
        await _authenticationValidator.ValidateAccessToSchool(schoolId);
    }

    public async Task ValidateAccessToMenuItems(IEnumerable<MenuItem> items)
    {
        // No items found - nothing to check permission for
        if (items == null || !items.Any()) return;

        var canteenIds = items.Select(i => (long)i.CanteenId).ToHashSet();
        await _authenticationValidator.ValidateAccessToCanteens(canteenIds);
    }
}