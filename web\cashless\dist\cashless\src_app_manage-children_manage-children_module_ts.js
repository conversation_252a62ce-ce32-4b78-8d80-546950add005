"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_manage-children_manage-children_module_ts"],{

/***/ 40760:
/*!*****************************************************************************!*\
  !*** ./src/app/manage-children/components/add-child/add-child.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddChildComponent: () => (/* binding */ AddChildComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../states/children/children.selectors */ 54186);
/* harmony import */ var _states_children_children_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../states/children/children.actions */ 21391);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _shared_components_colour_picker_colour_picker_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../shared/components/colour-picker/colour-picker.component */ 13057);


// Ngrx




















function AddChildComponent_h2_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "Add a child");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function AddChildComponent_h2_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "Edit child details");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function AddChildComponent_form_7_mat_error_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](ctx_r9.getErrorMessageFirstName());
  }
}
function AddChildComponent_form_7_mat_error_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](ctx_r10.getErrorMessageLastName());
  }
}
function AddChildComponent_form_7_mat_form_field_11_mat_option_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const school_r15 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("value", school_r15.SchoolId);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](school_r15.Name);
  }
}
function AddChildComponent_form_7_mat_form_field_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-form-field", 13)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "School name");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "mat-select", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, AddChildComponent_form_7_mat_form_field_11_mat_option_4_Template, 2, 2, "mat-option", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngForOf", ctx_r11.listSchools);
  }
}
function AddChildComponent_form_7_mat_option_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "No class");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function AddChildComponent_form_7_mat_option_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const class_r16 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("value", class_r16.ClassId);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](class_r16.Name);
  }
}
function AddChildComponent_form_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("ngSubmit", function AddChildComponent_form_7_Template_form_ngSubmit_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r18);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r17.onSubmit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "mat-form-field", 13)(2, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, "First name");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](4, "input", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](5, AddChildComponent_form_7_mat_error_5_Template, 2, 1, "mat-error", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](6, "mat-form-field", 13)(7, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](8, "Last name");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](9, "input", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](10, AddChildComponent_form_7_mat_error_10_Template, 2, 1, "mat-error", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](11, AddChildComponent_form_7_mat_form_field_11_Template, 5, 1, "mat-form-field", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](12, "mat-form-field", 13)(13, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](14, "Class");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](15, "mat-select", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](16, AddChildComponent_form_7_mat_option_16_Template, 2, 0, "mat-option", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](17, AddChildComponent_form_7_mat_option_17_Template, 2, 2, "mat-option", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx_r2.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r2.firstName.invalid);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r2.lastName.invalid);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r2.listSchools);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx_r2.listClass);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngForOf", ctx_r2.listClass);
  }
}
function AddChildComponent_div_8_form_8_colour_picker_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "colour-picker", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("favouriteColourChanged", function AddChildComponent_div_8_form_8_colour_picker_4_Template_colour_picker_favouriteColourChanged_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r22);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r21.favouriteColour.setValue($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("favouriteColour", ctx_r20.favouriteColour.value);
  }
}
function AddChildComponent_div_8_form_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("ngSubmit", function AddChildComponent_div_8_form_8_Template_form_ngSubmit_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r23.onSubmit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "div", 22)(2, "mat-checkbox", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, "Authorize Canteen Order");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, AddChildComponent_div_8_form_8_colour_picker_4_Template, 1, 1, "colour-picker", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx_r19.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r19.allowCanteen.value == true);
  }
}
function AddChildComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 3)(1, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "Canteen order");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "div", 5)(4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](5, " Allow the canteen to place orders on your behalf when your child wants to order directly at the counter. To confirm the order, the canteen will ask your child their favourite colour. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](6, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](7, " The order amount will be automatically deducted from your balance. There are no automatic top ups, so if your balance is low, the order will be declined. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](8, AddChildComponent_div_8_form_8_Template, 5, 2, "form", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r3.form);
  }
}
function AddChildComponent_div_9_form_6_mat_checkbox_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-checkbox", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const i_r28 = ctx.index;
    const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formControlName", i_r28);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](ctx_r26.ALLERGY_DATA[i_r28].title);
  }
}
function AddChildComponent_div_9_form_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("ngSubmit", function AddChildComponent_div_9_form_6_Template_form_ngSubmit_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r30);
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r29.onSubmit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](2, AddChildComponent_div_9_form_6_mat_checkbox_2_Template, 2, 2, "mat-checkbox", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx_r25.allergyForm);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngForOf", ctx_r25.allergyFormArray.controls);
  }
}
function AddChildComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 3)(1, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "Allergies");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "div", 5)(4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](5, " Let us know about any allergies, so they can be printed on your child\u2019s lunch bag labels by the canteen. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](6, AddChildComponent_div_9_form_6_Template, 3, 2, "form", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r4.form);
  }
}
function AddChildComponent_span_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "Add child");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function AddChildComponent_span_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "Save");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function AddChildComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r32 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 29)(1, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function AddChildComponent_div_21_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r32);
      const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r31.ArchiveClicked());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, " Delete child ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function AddChildComponent_mat_error_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](ctx_r8.WriteError());
  }
}
class AddChildComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(store, activatedRoute, router, spinnerService, userService, classesService, studentService, dialog, formBuilder) {
    super();
    this.store = store;
    this.activatedRoute = activatedRoute;
    this.router = router;
    this.spinnerService = spinnerService;
    this.userService = userService;
    this.classesService = classesService;
    this.studentService = studentService;
    this.dialog = dialog;
    this.formBuilder = formBuilder;
    this.listSchools = [];
    this.IsEdit = false;
    this.ALLERGY_DATA = _sharedModels__WEBPACK_IMPORTED_MODULE_0__.allergiesData;
  }
  ngOnInit() {
    let temp = this.activatedRoute.snapshot.data['schools'];
    this.listSchools = temp.schools;
    this._studentToEdit = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.UserCashless();
    // If Edit
    if (this.router.url.indexOf('/edit') > -1) {
      this.subscriptionStudent$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_8__.select)(_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__.selectedChild)).subscribe(student => {
        this._studentToEdit = student;
        this.IsEdit = true;
        if (!this._studentToEdit) {
          this._studentToEdit = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.UserCashless();
          this.GoBack();
        }
        this.CreateForm();
      });
    } else {
      this.CreateForm();
    }
  }
  ngOnDestroy() {
    this.subscriptionStudent$?.unsubscribe();
  }
  GoBack() {
    this.spinnerService.start();
    this.router.navigate(['/family/children'], {
      relativeTo: this.activatedRoute
    });
  }
  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get firstName() {
    return this.form.get('firstName');
  }
  get lastName() {
    return this.form.get('lastName');
  }
  get school() {
    return this.form.get('school');
  }
  get class() {
    return this.form.get('class');
  }
  get allowCanteen() {
    return this.form.get('allowCanteen');
  }
  get favouriteColour() {
    return this.form.get('favouriteColour');
  }
  get allergyFormArray() {
    return this.allergyForm.get('allergies');
  }
  getErrorMessageFirstName() {
    return this.firstName.hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageLastName() {
    return this.lastName.hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageClass() {
    return this.class.hasError('required') ? 'You must enter a value' : '';
  }
  CreateForm() {
    this._schoolId = this._studentToEdit.SchoolId;
    if (!this._studentToEdit.FavouriteColour || this._studentToEdit.FavouriteColour == '') {
      let studentCopy = Object.assign(new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.UserCashless(), this._studentToEdit);
      studentCopy.FavouriteColour = 'blue';
      this._studentToEdit = studentCopy;
    }
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormGroup({
      firstName: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.FirstName, [_angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.required]),
      lastName: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.Lastname, [_angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.required]),
      school: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.SchoolId),
      class: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.ClassId, [_angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.required]),
      allowCanteen: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.AllowCanteenToOrder),
      favouriteColour: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit.FavouriteColour)
    });
    this.generateAllergyForm();
    // on value change
    this.school.valueChanges.subscribe(newValue => {
      this._schoolId = newValue;
      this.LoadClass(newValue);
    });
    // If edit we need to load the class from the start
    if (this.IsEdit && this._studentToEdit) {
      this.LoadClass(this._studentToEdit.SchoolId);
    }
  }
  generateAllergyForm() {
    //generate dynamic allergy form
    this.allergyForm = this.formBuilder.group({
      allergies: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormArray([])
    });
    // Create form control for each allergy checkbox value
    this.ALLERGY_DATA.forEach(allergy => {
      this.allergyFormArray.push(new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this._studentToEdit?.Allergies?.includes(allergy.code)));
    });
  }
  /** Load Class */
  LoadClass(schoolId) {
    if (schoolId) {
      this.spinnerService.start();
      this.classesService.GetClassesBySchoolAPI(schoolId, true).subscribe({
        next: response => {
          this.listClass = response.Classes;
          this.spinnerService.stop();
        },
        error: error => {
          this.handleErrorFromService(error);
          this.spinnerService.stop();
        }
      });
    }
  }
  ////////////////////////////////////////
  // Add User
  ////////////////////////////////////////
  onSubmit() {
    this.spinnerService.start();
    let connectedUser = this.userService.GetUserConnected();
    this.userService.UpsertUser(this.convertObject()).subscribe({
      next: user => {
        // init form
        this.CreateForm();
        // refreshStore
        this.store.dispatch((0,_states_children_children_actions__WEBPACK_IMPORTED_MODULE_2__.familyLoadChildren)({
          parentId: connectedUser.UserId
        }));
        this.GoBack();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  convertObject() {
    // Get parent Id
    let connectedUser = this.userService.GetUserConnected();
    if (connectedUser != null) {
      let initUser = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.UserCashless();
      initUser.Address = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.Address();
      if (this.IsEdit) {
        initUser = this._studentToEdit;
      }
      let user = Object.assign(new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.UserCashless(), initUser);
      user.FirstName = this.firstName.value;
      user.Lastname = this.lastName.value;
      user.ClassId = this.class.value;
      user.SchoolId = this.school.value;
      user.FirebaseUserId = '';
      user.AllowCanteenToOrder = this.allowCanteen.value;
      user.FavouriteColour = this.favouriteColour.value;
      user.Allergies = this.getAllergyString();
      if (!this.IsEdit) {
        user.ParentId = connectedUser.UserId;
        user.Role = _sharedModels__WEBPACK_IMPORTED_MODULE_0__.Roles.Child;
      }
      return user;
    }
  }
  getAllergyString() {
    const processedAllergyValues = this.allergyFormArray.value.map((value, index) => ({
      allergyCode: this.ALLERGY_DATA[index].code,
      selected: value
    }));
    let selectedAllergyString = processedAllergyValues.filter(allergy => allergy.selected).map(allergy => allergy.allergyCode).join(',');
    return selectedAllergyString;
  }
  CancelForm() {
    this.CreateForm();
    this.GoBack();
  }
  GetBackgroundColor() {
    return 'background-color:' + this.favouriteColour.value + ';';
  }
  HasPayAtCanteenFeature() {
    let res = false;
    if (this._schoolId) {
      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);
      if (school.SchoolFeatures) {
        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == _sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureEnum.PayAtCanteen);
        res = hasFeature >= 0;
      }
    }
    return res;
  }
  HasAllergyAlertFeature() {
    let res = false;
    if (this._schoolId) {
      let school = this.listSchools.find(x => x.SchoolId == this._schoolId);
      if (school.SchoolFeatures) {
        let hasFeature = school.SchoolFeatures.findIndex(x => x.OptionName == _sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureEnum.AllergyAlert);
        res = hasFeature >= 0;
      }
    }
    return res;
  }
  ////////////////////////// Archive child
  ArchiveClicked() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ConfirmModal();
    data.Title = 'Delete Child';
    data.Text = 'Deleting will inactivate the child profile permanently and cannot be undone. Do you want to proceed?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_3__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed();
      }
    });
  }
  ArchiveClickConfirmed() {
    this.spinnerService.start();
    this.studentService.ArchiveStudentAPI(this._studentToEdit.UserId).subscribe({
      next: response => {
        this.GoBack();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  CanArchive() {
    return this._studentToEdit && this._studentToEdit.UserId > 0;
  }
  static {
    this.ɵfac = function AddChildComponent_Factory(t) {
      return new (t || AddChildComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_8__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_10__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_10__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.SchoolClassesService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.StudentService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_11__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormBuilder));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: AddChildComponent,
      selectors: [["manage-children-add-child"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"]],
      decls: 24,
      vars: 10,
      consts: [[1, "container-fluid"], ["text", "Go back", 3, "navBack"], [1, "row"], [1, "col-12", "col-md-6"], [4, "ngIf"], [1, "cardDefaultCanteen"], ["class", "cashlessForm", 3, "formGroup", "ngSubmit", 4, "ngIf"], ["class", "col-12 col-md-6", 4, "ngIf"], [1, "col-12"], ["type", "button", 1, "PrimaryButton", 3, "disabled", "click"], ["mat-flat-button", "", "type", "button", 1, "SecondaryButton", "cancelButton", 3, "click"], ["class", "col-12 pt-2", 4, "ngIf"], [1, "cashlessForm", 3, "formGroup", "ngSubmit"], ["appearance", "outline"], ["matInput", "", "placeholder", "First name", "formControlName", "firstName", "type", "text", "required", ""], ["matInput", "", "placeholder", "Last name", "formControlName", "lastName", "type", "text", "required", ""], ["appearance", "outline", 4, "ngIf"], ["placeholder", "Class", "formControlName", "class", "required", ""], [3, "value", 4, "ngIf"], [3, "value", 4, "ngFor", "ngForOf"], ["placeholder", "School", "formControlName", "school", "required", ""], [3, "value"], [1, "authorizeCheckbox"], ["formControlName", "allowCanteen"], [3, "favouriteColour", "favouriteColourChanged", 4, "ngIf"], [3, "favouriteColour", "favouriteColourChanged"], ["formArrayName", "allergies", 1, "pb-2"], ["class", "allergyCheckBox", 3, "formControlName", 4, "ngFor", "ngForOf"], [1, "allergyCheckBox", 3, "formControlName"], [1, "col-12", "pt-2"], ["mat-stroked-button", "", "color", "warn", 1, "archiveButton", 3, "click"]],
      template: function AddChildComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0)(1, "nav-back-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("navBack", function AddChildComponent_Template_nav_back_button_navBack_1_listener() {
            return ctx.GoBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, AddChildComponent_h2_4_Template, 2, 0, "h2", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](5, AddChildComponent_h2_5_Template, 2, 0, "h2", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](7, AddChildComponent_form_7_Template, 18, 6, "form", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](8, AddChildComponent_div_8_Template, 9, 1, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](9, AddChildComponent_div_9_Template, 7, 1, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](10, "div", 2)(11, "div", 3)(12, "div", 5)(13, "div", 2)(14, "div", 8)(15, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function AddChildComponent_Template_button_click_15_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](16, AddChildComponent_span_16_Template, 2, 0, "span", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](17, AddChildComponent_span_17_Template, 2, 0, "span", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](18, "div", 8)(19, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function AddChildComponent_Template_button_click_19_listener() {
            return ctx.CancelForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](20, " Cancel ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](21, AddChildComponent_div_21_Template, 3, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](22, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](23, AddChildComponent_mat_error_23_Template, 2, 1, "mat-error", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx.IsEdit);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.IsEdit);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.HasPayAtCanteenFeature());
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.HasAllergyAlertFeature());
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("disabled", !ctx.form.valid);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx.IsEdit);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.IsEdit);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.CanArchive());
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.errorAPI);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_9__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControlName, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormArrayName, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatError, _angular_material_select__WEBPACK_IMPORTED_MODULE_14__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_15__.MatOption, _angular_material_input__WEBPACK_IMPORTED_MODULE_16__.MatInput, _angular_material_button__WEBPACK_IMPORTED_MODULE_17__.MatButton, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_18__.MatCheckbox, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__.NavBackButtonComponent, _shared_components_colour_picker_colour_picker_component__WEBPACK_IMPORTED_MODULE_6__.ColourPickerComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.goBackButton[_ngcontent-%COMP%] {\n  cursor: pointer;\n  margin-top: 20px;\n  margin-left: 20px;\n}\n\n.wrapper[_ngcontent-%COMP%] {\n  margin-left: 10px;\n  margin-right: 10px;\n}\n\n.bodyForm[_ngcontent-%COMP%] {\n  background-color: white;\n  padding-bottom: 15px;\n}\n\n.archiveButton[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.allergyCheckBox[_ngcontent-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWFuYWdlLWNoaWxkcmVuL2NvbXBvbmVudHMvYWRkLWNoaWxkL2FkZC1jaGlsZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFkQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBaUJGOztBQWRBO0VBQ0UsaUJBQUE7RUFDQSxrQkFBQTtBQWlCRjs7QUFkQTtFQUNFLHVCQUFBO0VBQ0Esb0JBQUE7QUFpQkY7O0FBZEE7RUFDRSxXQUFBO0FBaUJGOztBQWRBO0VBQ0UsY0FBQTtBQWlCRiIsInNvdXJjZXNDb250ZW50IjpbIiRicmVha3BvaW50LXNtOiA1NzZweDtcbiRicmVha3BvaW50LW1kOiA3NjdweDtcbiRicmVha3BvaW50LWxnOiA5OTJweDtcbiRicmVha3BvaW50LXhsOiAxMjAwcHg7XG5cbi5tb2JpbGUge1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1heC13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuLy8gTk9URSBDdXJyZW50bHkgdGFibGV0IGFuZCBtb2JpbGUgaXMgdGhlIHNhbWUuIENoYW5nZSB0byAkYnJlYWtwb2ludC1sZyBsYXRlciBpZiB3ZSBnZXQgYSBwcm9wZXIgdGFibGV0IGRlc2lnbi5cbi5kZXNrdG9wIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtaW4td2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbiIsIkBpbXBvcnQgJy4uLy4uLy4uLy4uL3N0eWxlcy9jYXNobGVzcy1icmVha3BvaW50cy5zY3NzJztcblxuLmdvQmFja0J1dHRvbiB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgbWFyZ2luLXRvcDogMjBweDtcbiAgbWFyZ2luLWxlZnQ6IDIwcHg7XG59XG5cbi53cmFwcGVyIHtcbiAgbWFyZ2luLWxlZnQ6IDEwcHg7XG4gIG1hcmdpbi1yaWdodDogMTBweDtcbn1cblxuLmJvZHlGb3JtIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIHBhZGRpbmctYm90dG9tOiAxNXB4O1xufVxuXG4uYXJjaGl2ZUJ1dHRvbiB7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uYWxsZXJneUNoZWNrQm94IHtcbiAgZGlzcGxheTogYmxvY2s7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 83077:
/*!*****************************************************!*\
  !*** ./src/app/manage-children/components/index.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddChildComponent: () => (/* reexport safe */ _add_child_add_child_component__WEBPACK_IMPORTED_MODULE_0__.AddChildComponent),
/* harmony export */   ListChildrenComponent: () => (/* reexport safe */ _list_children_list_children_component__WEBPACK_IMPORTED_MODULE_1__.ListChildrenComponent)
/* harmony export */ });
/* harmony import */ var _add_child_add_child_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-child/add-child.component */ 40760);
/* harmony import */ var _list_children_list_children_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./list-children/list-children.component */ 46051);



/***/ }),

/***/ 46051:
/*!*************************************************************************************!*\
  !*** ./src/app/manage-children/components/list-children/list-children.component.ts ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ListChildrenComponent: () => (/* binding */ ListChildrenComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _states_children_children_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../states/children/children.actions */ 21391);
/* harmony import */ var _states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../states/children/children.selectors */ 54186);
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/animations */ 47172);
/* harmony import */ var ng_animate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ng-animate */ 77975);
/* harmony import */ var src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/states/user/user.selectors */ 92290);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_warning_warning_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared/components/warning/warning.component */ 42689);
// Ngrx



//Animations









function ListChildrenComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 4)(1, "div", 5)(2, "p", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "My Children");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 5)(5, "p", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, "My Children");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 8)(8, "button", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "Add Child");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "button", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](11, "Add Child");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("@bounce", ctx_r0.bounce);
  }
}
function ListChildrenComponent_div_2_ng_container_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ListChildrenComponent_div_2_ng_container_1_div_1_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r9);
      const student_r7 = restoredCtx.$implicit;
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r8.EditChildClick(student_r7));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "img", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "div", 16)(3, "div", 17)(4, "h4", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const student_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](student_r7.FirstName);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"]("", student_r7.ClassName, ", ", student_r7.SchoolName, "");
  }
}
function ListChildrenComponent_div_2_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ListChildrenComponent_div_2_ng_container_1_div_1_Template, 8, 3, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r3.listStudents);
  }
}
const _c0 = function () {
  return ["/family/home"];
};
function ListChildrenComponent_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 16)(1, "div", 17)(2, "p", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " You're now ready to make an order. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "a", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5, "Go to Orders");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](1, _c0));
  }
}
function ListChildrenComponent_div_2_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 16)(1, "div", 17)(2, "h3", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Add your child to begin using Spriggy Schools!");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
}
function ListChildrenComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ListChildrenComponent_div_2_ng_container_1_Template, 2, 1, "ng-container", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, ListChildrenComponent_div_2_div_2_Template, 7, 2, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, ListChildrenComponent_div_2_div_3_Template, 4, 0, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("@bounce", ctx_r1.bounce);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.ShowListStudents());
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r1.addedFirstChild);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r1.ShowListStudents() && !ctx_r1.deactivatedUser);
  }
}
function ListChildrenComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 16)(1, "div", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "app-warning", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
class ListChildrenComponent {
  constructor(store, router, spinnerService, userService, studentService, debounceService) {
    this.store = store;
    this.router = router;
    this.spinnerService = spinnerService;
    this.userService = userService;
    this.studentService = studentService;
    this.debounceService = debounceService;
    this.listStudents = [];
    this.addedFirstChild = false;
    this.deactivatedUser = false;
    this.UpdateIntercomDebounce = this.debounceService.callDebounce(this.updateIntercom, 350, false, true);
  }
  ngOnInit() {
    this.childrenListSubscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_6__.select)(_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__.children)).subscribe(state => {
      this.spinnerService.start();
      this.listStudents = state.list;
      this.UpdateIntercomDebounce();
      // Set text to explain what to do after adding the first student
      if (!this.listStudents || this.listStudents.length < 1) {
        this.studentService.SetNoStudent(true);
      } else {
        if (this.studentService.GetNoStudent()) {
          this.addedFirstChild = true;
          this.studentService.SetNoStudent(false);
        }
      }
      this.spinnerService.stop();
    });
    this.connectedUserSubscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_6__.select)(src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_2__.connectedUser)).subscribe(user => {
      this.deactivatedUser = !user.IsActive;
    });
  }
  ngOnDestroy() {
    if (this.childrenListSubscription) {
      this.childrenListSubscription.unsubscribe();
    }
    if (this.connectedUserSubscription) {
      this.connectedUserSubscription.unsubscribe();
    }
  }
  updateIntercom() {
    // get schools info into Intercom
    if (this.listStudents) {
      const schoolNameString = this.listStudents.map(s => s.SchoolName).toString();
      const schoolIdArray = this.listStudents.map(s => s.SchoolId.toString());
      this.userService.UpdateUser(schoolNameString, schoolIdArray);
    }
  }
  ShowListStudents() {
    if (!this.listStudents) {
      return false;
    } else {
      return this.listStudents.length > 0;
    }
  }
  EditChildClick(student) {
    this.store.dispatch((0,_states_children_children_actions__WEBPACK_IMPORTED_MODULE_0__.SetSelectedChild)({
      child: student
    }));
    this.router.navigate(['family/children/edit']);
  }
  static {
    this.ɵfac = function ListChildrenComponent_Factory(t) {
      return new (t || ListChildrenComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_6__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.StudentService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.DebounceService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: ListChildrenComponent,
      selectors: [["manage-children-list-children"]],
      decls: 4,
      vars: 3,
      consts: [[1, "container-fluid", "divWrapper"], ["class", "row headerContainer", 4, "ngIf"], ["class", "row cardDefaultParent", 4, "ngIf"], ["class", "row", 4, "ngIf"], [1, "row", "headerContainer"], [1, "col-4"], [1, "headerTextLeft"], [1, "headerTextCentre"], [1, "col-4", "buttonContainer"], ["type", "button", "routerLink", "./add", 1, "PrimaryButton"], ["type", "button", "routerLink", "./add", 1, "buttonAddMobile"], [1, "row", "cardDefaultParent"], [4, "ngIf"], ["class", "row col-12 cardChild", 3, "click", 4, "ngFor", "ngForOf"], [1, "row", "col-12", "cardChild", 3, "click"], ["src", "assets/icons/child-blank.svg", 1, "child-image"], [1, "row"], [1, "col-12"], [1, "childName"], [1, "firstChild"], [3, "routerLink"], [1, "noChild"], ["title", "Your account is deactivated", "description", "Please get in touch with us if you would like to reopen your account."]],
      template: function ListChildrenComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ListChildrenComponent_div_1_Template, 12, 1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, ListChildrenComponent_div_2_Template, 4, 4, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, ListChildrenComponent_div_3_Template, 3, 0, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.deactivatedUser);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.deactivatedUser);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.deactivatedUser);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink, _shared_components_warning_warning_component__WEBPACK_IMPORTED_MODULE_4__.WarningComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.divWrapper[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  margin-left: 0px;\n  margin-right: 0px;\n  margin-bottom: 0px;\n}\n@media (min-width: 767px) {\n  .divWrapper[_ngcontent-%COMP%] {\n    padding-left: 37px;\n    padding-right: 37px;\n  }\n}\n\nbutton[_ngcontent-%COMP%] {\n  border: none;\n  background-color: #f2f2f2;\n}\n\n.headerContainer[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100px;\n}\n\n.headerTextLeft[_ngcontent-%COMP%] {\n  color: #333b44;\n  font-size: 32px;\n  font-family: \"bariol_regular\";\n  text-align: start;\n  padding: 0px;\n  visibility: hidden;\n}\n@media (min-width: 767px) {\n  .headerTextLeft[_ngcontent-%COMP%] {\n    visibility: visible;\n  }\n}\n\n.headerTextCentre[_ngcontent-%COMP%] {\n  color: #333b44;\n  font-size: 20px;\n  font-family: \"bariol_bold\";\n  text-align: center;\n  padding: 0px;\n  visibility: visible;\n}\n@media (min-width: 767px) {\n  .headerTextCentre[_ngcontent-%COMP%] {\n    visibility: hidden;\n  }\n}\n\n.buttonContainer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.buttonAddMobile[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  margin-bottom: 20px;\n  right: 0px;\n  padding: 0px;\n  margin: 0px;\n  font-size: 20px;\n  color: #ff7a00;\n  font-family: \"bariol_bold\";\n  display: block;\n}\n@media (min-width: 767px) {\n  .buttonAddMobile[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n\n.PrimaryButton[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .PrimaryButton[_ngcontent-%COMP%] {\n    display: block;\n    right: 0;\n    max-width: 138px;\n  }\n}\n\n.child-image[_ngcontent-%COMP%] {\n  height: 64px;\n  width: 64px;\n  margin-left: 15px;\n  margin-top: 23px;\n  margin-bottom: 23px;\n  margin-right: 23px;\n}\n\n.childInfoContainer[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.cardChild[_ngcontent-%COMP%] {\n  border-bottom-style: solid;\n  border-bottom-width: 1px;\n  border-bottom-color: #dddddd;\n  cursor: pointer;\n}\n\n.childName[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n  font-size: 20px;\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.noChild[_ngcontent-%COMP%] {\n  padding-left: 23px;\n}\n\n.firstChild[_ngcontent-%COMP%] {\n  padding: 23px;\n  font-size: 18px;\n  line-height: 24px;\n}\n.firstChild[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  text-decoration: none;\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.trigger)('bounce', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)('* => *', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.useAnimation)(ng_animate__WEBPACK_IMPORTED_MODULE_10__.fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: {
            timing: 0.5,
            delay: 0
          }
        }))])]
      }
    });
  }
}

/***/ }),

/***/ 10474:
/*!*******************************************************************!*\
  !*** ./src/app/manage-children/manage-children-routing.module.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageChildrenRoutingModule: () => (/* binding */ ManageChildrenRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 83077);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../sharedServices */ 2902);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);

// Components

// Services



const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.ListChildrenComponent,
  resolve: {
    students: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListStudentsResolver
  }
}, {
  path: 'add',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AddChildComponent,
  resolve: {
    schools: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListSchoolsResolver
  }
}, {
  path: 'edit',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AddChildComponent,
  resolve: {
    schools: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListSchoolsResolver
  }
}];
class ManageChildrenRoutingModule {
  static {
    this.ɵfac = function ManageChildrenRoutingModule_Factory(t) {
      return new (t || ManageChildrenRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: ManageChildrenRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](ManageChildrenRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 23635:
/*!***********************************************************!*\
  !*** ./src/app/manage-children/manage-children.module.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageChildrenModule: () => (/* binding */ ManageChildrenModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _ngrx_effects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ngrx/effects */ 70347);
/* harmony import */ var _states_children_children_reducer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../states/children/children.reducer */ 33980);
/* harmony import */ var _states_children_children_effects__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../states/children/children.effects */ 47366);
/* harmony import */ var _manage_children_routing_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./manage-children-routing.module */ 10474);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ 83077);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);


// google material






// modules

// Ngrx


// reducers

// Effects






class ManageChildrenModule {
  static {
    this.ɵfac = function ManageChildrenModule_Factory(t) {
      return new (t || ManageChildrenModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineNgModule"]({
      type: ManageChildrenModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.ReactiveFormsModule, _manage_children_routing_module__WEBPACK_IMPORTED_MODULE_3__.ManageChildrenRoutingModule, _ngrx_store__WEBPACK_IMPORTED_MODULE_8__.StoreModule.forFeature(_states_children_children_reducer__WEBPACK_IMPORTED_MODULE_1__.childrenFeatureKey, _states_children_children_reducer__WEBPACK_IMPORTED_MODULE_1__.childrenReducer), _ngrx_effects__WEBPACK_IMPORTED_MODULE_9__.EffectsModule.forFeature([_states_children_children_effects__WEBPACK_IMPORTED_MODULE_2__.ChildrenEffects]),
      // material
      _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelectModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_13__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_14__.MatButtonModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__.MatCheckboxModule,
      // other
      _shared_shared_module__WEBPACK_IMPORTED_MODULE_0__.SharedModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsetNgModuleScope"](ManageChildrenModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_4__.ListChildrenComponent, _components__WEBPACK_IMPORTED_MODULE_4__.AddChildComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.ReactiveFormsModule, _manage_children_routing_module__WEBPACK_IMPORTED_MODULE_3__.ManageChildrenRoutingModule, _ngrx_store__WEBPACK_IMPORTED_MODULE_8__.StoreFeatureModule, _ngrx_effects__WEBPACK_IMPORTED_MODULE_9__.EffectsFeatureModule,
    // material
    _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_11__.MatSelectModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_13__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_14__.MatButtonModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__.MatCheckboxModule,
    // other
    _shared_shared_module__WEBPACK_IMPORTED_MODULE_0__.SharedModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_manage-children_manage-children_module_ts.js.map