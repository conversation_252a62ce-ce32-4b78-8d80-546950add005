"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_Core_request-immediate-data-flush_js"],{

/***/ 62893:
/*!******************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Core/request-immediate-data-flush.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   requestImmediateDataFlush: () => (/* binding */ requestImmediateDataFlush)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);

function requestImmediateDataFlush(t) {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return;
  const r = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].cr();
  r && r.requestImmediateDataFlush(t);
}

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_Core_request-immediate-data-flush_js.js.map