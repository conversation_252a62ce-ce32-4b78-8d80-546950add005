using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Braintree;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Schools.BLL.Helpers;
using Schools.BLL.Classes.Billing;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Exceptions;
using Schools.BLL.ThirdParty.General;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using Schools.DAL.Enums;
using Schools.DAL.Entities;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PayController : ControllerBase
    {
        public IBraintreeConfiguration braintreeConfig = new BraintreeConfiguration();

        public static readonly TransactionStatus[] transactionSuccessStatuses = {
            TransactionStatus.AUTHORIZED,
            TransactionStatus.AUTHORIZING,
            TransactionStatus.SETTLED,
            TransactionStatus.SETTLING,
            TransactionStatus.SETTLEMENT_CONFIRMED,
            TransactionStatus.SETTLEMENT_PENDING,
            TransactionStatus.SUBMITTED_FOR_SETTLEMENT
        };

        private readonly ITelemetryService telemetryService;
        private readonly IDBHelper dbHelper;
        private readonly IScoreApiClient scoreApiClient;
        private readonly IUserService userService;
        private readonly IPaymentService paymentService;
        private readonly IPaymentService2 _paymentService2;
        private readonly ICanteenService canteenService;
        private readonly IPaymentValidator paymentValidator;
        private readonly IStripeService _stripeService;
        private readonly ILogger<PayController> _logger;

        public PayController(ITelemetryService telemetryService, IDBHelper dbHelper, IScoreApiClient scoreApiClient,
                            IUserService userService, IPaymentService paymentService, IPaymentService2 paymentService2, ICanteenService canteenService,
                            IPaymentValidator paymentValidator, IStripeService stripeService, ILogger<PayController> logger)
        {
            this.telemetryService = telemetryService;
            this.dbHelper = dbHelper;
            this.scoreApiClient = scoreApiClient;
            this.userService = userService;
            this.paymentService = paymentService;
            _paymentService2 = paymentService2;
            this.canteenService = canteenService;
            this.paymentValidator = paymentValidator;
            _stripeService = stripeService;
            _logger = logger;
        }

        /// <summary>
        /// Gets a new token to initiate checkout
        /// </summary>
        /// <returns></returns>
        /// This endpoint will work for ONLY SCore customers
        [Route("GetNewToken/{externalUserId}")]
        [Route("GetNewToken/{externalUserId}/{braintreeFormat}")]
        [Route("GetNewTokenMobile/{externalUserId}")]
        [Route("GetNewTokenMobile/{externalUserId}/{braintreeFormat}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetNewToken(string externalUserId, bool braintreeFormat = false) //customerId = UserExternalId
        {
            await paymentValidator.ValidateGetNewTokenRequest();
            return await GetNewToken2(decimal.Zero, braintreeFormat);
        }

        /// <summary>
        /// Gets a new token to initiate checkout
        /// </summary>
        /// <returns></returns>
        /// This endpoint will work for BOTH SCore and Stripe customers
        [Route("GetNewToken2/{amount}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetNewToken2(decimal amount, bool fullTokenResponse = true) //customerId = UserExternalId
        {
            var res = await _paymentService2.GetNewToken(amount);
            if (res == null)
            {
                this.telemetryService.TrackTrace("GetNewToken2: response is null");
            }
            else if (String.IsNullOrEmpty(res.token))
            {
                this.telemetryService.TrackTrace("GetNewToken2: token is empty or null");
            }

            if (fullTokenResponse)
            {
                return new JsonResult(res);
            }

            return new JsonResult(res.token);
        }

        /// <summary>
        /// Top Up the balance of the user
        /// </summary>
        [Route("TopupAmount")]
        [Route("TopupAmountMobile")]
        [HttpPost]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> TopupAmount([FromBody] TopupRequest request)
        {
            await this.paymentValidator.ValidateRequest(request);

            // user audit
            this.telemetryService.TrackTrace("topup_request",
                            new Dictionary<string, object>() {
                                {"request", request}
                            });

            var response = await _paymentService2.TopUp(request);

            if (!response.IsSuccess.GetValueOrDefault(false))
            {
                // user audit
                this.telemetryService.TrackEvent("topup_request_failed",
                                    new Dictionary<string, object>() {
                                        {"response", response}
                                    });
            }

            return new JsonResult(response);
        }

        [AllowAnonymous]
        [Route("ReceiveStripeEvents")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ReceiveStripeEvents()
        {
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
            var signatureHeader = Request.Headers["Stripe-Signature"];

            try
            {
                await _stripeService.ProcessEvent(json, signatureHeader);
                return Ok();
            }
            catch (Exception e)
            {
                telemetryService.TrackTrace($"Exception while processing Stripe Event: {e.Message}");
                _logger.LogDebug($"Exception while processing Stripe Event: {e.Message}");
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Top up the account and pay an order in the same time.
        /// 
        /// This is used by the cashless-process-order logic app. 
        /// 
        /// This needs anonymous access - there is no firebase token, but there 
        /// should be an API secret in one of the request headers
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("MakePayment")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> MakePayment([FromBody] PaymentRequest req)
        {
            this.telemetryService.TrackTrace("Make_Payment_Controller_Request",
                                new Dictionary<string, string>() {
                                    {"request", JsonConvert.SerializeObject(req)}
                                });

            // get canteen external user Id
            if (req.canteenId > 0)
            {
                CanteenEntity canteen = await this.canteenService.GetCanteenById(req.canteenId);

                if (string.IsNullOrEmpty(req.toExternalCustomerId) && !string.IsNullOrEmpty(req.fromExternalCustomerId))
                {
                    req.toExternalCustomerId = canteen.ExternalUserId;
                }
                else if (!string.IsNullOrEmpty(req.toExternalCustomerId) && string.IsNullOrEmpty(req.fromExternalCustomerId))
                {
                    req.fromExternalCustomerId = canteen.ExternalUserId;
                }
            }

            var res = await _paymentService2.MakePayment(req);

            return new JsonResult(res);
        }

        #region Test methods for Stripe Integration - should be removed later

        [CheckUserRole(UserRole.Admin)]
        [Route("CreateAndConfirmPaymentIntent")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAndConfirmPaymentIntent([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.CreateAndConfirmPaymentIntent(req.fromExternalCustomerId, (long)req.chargeAmount, req.toExternalCustomerId);

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("GetPaymentIntentToken")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetPaymentIntentToken([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.GetToken(req.fromExternalCustomerId, (long)req.topupAmount);

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("CreateCustomer")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateCustomer([FromBody] CreateCustomerRequest req)
        {
            var res = await _stripeService.CreateCustomer(req.reference);

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("GetPaymentMethods1")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetPaymentMethods1([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.GetPaymentMethods(req.fromExternalCustomerId);

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("AddPaymentMethod1")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddPaymentMethod1([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.AddPaymentMethod();

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("AttachPaymentMethod")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AttachPaymentMethod([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.AttachPaymentMethod(req.fromExternalCustomerId, req.toExternalCustomerId);

            return new JsonResult(res);
        }

        [CheckUserRole(UserRole.Admin)]
        [Route("DeletePaymentMethod")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeletePaymentMethod([FromBody] PaymentRequest req)
        {
            var res = await _stripeService.DeletePaymentMethod(req.toExternalCustomerId);

            return new JsonResult(res);
        }

        #endregion

        [Route("GetBalance")]
        [Route("GetBalanceMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent, UserRole.Merchant, UserRole.Admin)]
        public async Task<IActionResult> GetBalance([FromBody] GetCustomerRequest request)
        {
            await this.paymentValidator.ValidateRequest(request?.reference);

            var response = await _paymentService2.UpdateUserBalance(request);

            return new JsonResult(response);
        }

        [Route("GetPaymentMethods/{externalUserId}")]
        [Route("GetPaymentMethodsMobile/{externalUserId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> GetPaymentMethods(string externalUserId)
        {
            return await GetPaymentMethods();
        }

        [Route("GetPaymentMethods")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> GetPaymentMethods()
        {
            var res = await _paymentService2.GetPaymentMethods();

            return new JsonResult(res);
        }

        [Route("AddPaymentMethod")]
        [Route("AddPaymentMethodMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> AddPaymentMethod([FromBody] AddPaymentMethod request)
        {
            await paymentValidator.ValidateAddPaymentMethodRequest();
            await this.paymentValidator.ValidateRequest(request?.externalUserId);

            var res = await this.scoreApiClient.AddPaymentMethods(request);

            return new JsonResult(res);
        }

        [Route("DeletePaymentMethod/{custId}/{methodId}")]
        [Route("DeletePaymentMethodMobile/{custId}/{methodId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        [Obsolete]
        public async Task<IActionResult> DeletePaymentMethod(string custId, string methodId)
        {
            return await DeletePaymentMethod(methodId);
        }

        [Route("DeletePaymentMethod/{methodId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Parent)]
        public async Task<IActionResult> DeletePaymentMethod(string methodId)
        {
            var res = await _paymentService2.DeletePaymentMethod(methodId);

            this.telemetryService.TrackTrace("delete_payment",
                                new Dictionary<string, string>() {
                                    {"response", JsonConvert.SerializeObject(res)}
                                });

            return new JsonResult(res);
        }

        [Route("{paymentId}/CheckBalance")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> CheckBalanceFromPayment(int paymentId)
        {
            // check balance
            CheckBalanceResponse res = await this.paymentService.CheckBalanceFromPayment(paymentId);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Link orders to transactions
        /// 
        /// This needs anonymous access - there is no firebase token, but there 
        /// should be an API secret in one of the request headers
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("OrdersPaymentLink")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> OrdersPaymentLink([FromBody] OrdersPaymentLinkRequest request)
        {
            await this.paymentService.OrdersPaymentLink(request);
            return new OkResult();
        }
    }
}
