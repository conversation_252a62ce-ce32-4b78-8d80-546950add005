####
#
# Do not edit this worklow, managed by -> https://github.com/piggymoney/github-central-workflows
# Located -> https://github.com/piggymoney/github-central-workflows/blob/main/workflows/python-pre-upgrade-direct-main-deply/build.yaml
####

name: Build Docker Image

on:
  workflow_dispatch:
  push:
    branches:
      - '**'
    paths:
      - '**'
      - '!kustomize/**'
      - '!.github/workflows/lint-kustomize.yaml'
    
permissions:
  actions: write
  id-token: write
  contents: read

# Environment variables are found within -> ./.github/variables/docker-config.env

jobs:
  # lint:
  #   runs-on: prod-runner
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4
  #       with:
  #         fetch-depth: 0
      
  #     - name: Super-Linter
  #       uses: super-linter/super-linter@v6.3.0
  #       env:
  #         # To report GitHub Actions status checks
  #         GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  #         VALIDATE_CSHARP: true
  #         LOG_LEVEL: ERROR

  test-code:
    runs-on: prod-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env

      
      - uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.0.x'
      
      - name: Configure AWS Credentials for Dev
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.DEV_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Dev
        run: |
          aws secretsmanager get-secret-value --secret-id 'dev/schools/schools-api/appsettings' --query SecretString --output text > ./appsettings.dev.json
      
      - name: Configure AWS Credentials for Test
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.TEST_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Test
        run: |
          aws secretsmanager get-secret-value --secret-id 'test/schools/schools-api/appsettings' --query SecretString --output text > ./appsettings.test.json
      
      - name: Configure AWS Credentials for Prod
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.PROD_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Prod
        run: |
          aws secretsmanager get-secret-value --secret-id 'prod/schools/schools-api/appsettings' --query SecretString --output text > ./appsettings.prod.json
      
      # we want to run the tests both in the Dockerfile to test the environment and unit tests here to check where it fails easily via github gui
      - name: Run unit tests
        run: |
          dotnet test cashlessApi.sln
      
      - name: Run Snyk to check for vulnerabilities
        run: |
          export SNYK_TOKEN=${{ secrets.SNYK_TOKEN }}
          snyk monitor --all-projects

  build:
    runs-on: ${{ matrix.runner }}
    strategy:
      matrix:
        platform: [amd64, arm64]
        include:
          - platform: amd64
            runner: prod-runner
          - platform: arm64
            runner: prod-runner-arm
    timeout-minutes: 15
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # Needed otherwise get error in buildx
      - run: |
          docker context create builders
      
      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3
        with:
          endpoint: builders
          driver-opts: image=public.ecr.aws/vend/moby/buildkit:buildx-stable-1

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env
      
      - uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.0.x'

      - name: Configure Repo Environment Variables
        run: |  
          export SHORT_COMMIT=$(git log -1 --pretty=format:%h)
          echo SHORT_COMMIT=$SHORT_COMMIT >> $GITHUB_ENV
          export REPO_NAME=$ECR_REPO_PREFIX$SERVICE
          echo REPO_NAME=$REPO_NAME >> $GITHUB_ENV
      
      - name: Configure AWS Credentials for Dev
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.DEV_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Dev
        run: |
          aws secretsmanager get-secret-value --secret-id 'dev/schools/schools-api/appsettings' --query SecretString --output text > ./Cashless.API/appsettings.dev.json
      
      - name: Configure AWS Credentials for Test
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.TEST_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Test
        run: |
          aws secretsmanager get-secret-value --secret-id 'test/schools/schools-api/appsettings' --query SecretString --output text > ./Cashless.API/appsettings.test.json
      
      - name: Configure AWS Credentials for Prod
        # Needed to get base image for when we start moving to shared account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.PROD_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Get config Prod
        run: |
          aws secretsmanager get-secret-value --secret-id 'prod/schools/schools-api/appsettings' --query SecretString --output text > ./Cashless.API/appsettings.prod.json
          
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DEVOPS_DOCKERHUB_USER }}
          password: ${{secrets.DEVOPS_DOCKERHUB_TOKEN }}
      
      - name: Setup Buildx
        run: |
          docker context create builder
          docker buildx create builder --use --driver docker-container --driver-opt 'image=public.ecr.aws/vend/moby/buildkit:buildx-stable-1' --buildkitd-flags '--allow-insecure-entitlement security.insecure --allow-insecure-entitlement network.host'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ECR_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Cache Docker layers
        id: cache
        uses: runs-on/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ env.SERVICE }}-${{ matrix.platform }}-${{ github.sha }}
          restore-keys: |
              ${{ runner.os }}-buildx-${{ env.SERVICE }}-${{ matrix.platform }}
        env:
          RUNS_ON_S3_BUCKET_CACHE: ${{ env.S3_GITHUB_CACHE }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2      
          
      - name: Build Docker image
        run: |
          docker buildx build --platform=linux/${{ matrix.platform }} \
            --output type=docker,dest=/tmp/${{ env.SERVICE}}-${{ matrix.platform }}.tar \
            -t ${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-${{ matrix.platform }} \
            --cache-from type=local,src=/tmp/.buildx-cache \
            --cache-to type=local,dest=/tmp/.buildx-cache \
            --build-arg DD_VERSION=${{env.SHORT_COMMIT}} \
            --build-arg DD_GIT_COMMIT_SHA=${{env.DD_GIT_COMMIT_SHA}} \
            --build-arg DD_GIT_REPOSITORY_URL=${{env.DD_GIT_REPOSITORY_URL}} \
            . # This is required

      - name: Upload artifact
        run: |
          aws s3 cp /tmp/${{ env.SERVICE}}-${{ matrix.platform }}.tar s3://$S3_GITHUB_UPLOAD/$GITHUB_REPOSITORY/$GITHUB_RUN_NUMBER/${{ env.SERVICE}}-${{ matrix.platform }}.tar
  

  test-image:
    runs-on: prod-runner-small
    #runs-on: ${{ matrix.runner }}
    needs: [build]
    timeout-minutes: 10
    # strategy:
    #   matrix:
    #     platform: [amd64, arm64]
    #     include:
    #       - platform: amd64
    #         runner: prod-runner-small
    #       # Only need to test 1
    #       - platform: arm64
    #         runner: prod-runner-small-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env
      
      - name: Configure Repo Environment Variables
        run: |  
          export SERVICE_APP=${{ env.SERVICE }}
          export SHORT_COMMIT=$(git log -1 --pretty=format:%h)
          echo SHORT_COMMIT=$SHORT_COMMIT >> $GITHUB_ENV
          export REPO_NAME=$ECR_REPO_PREFIX$SERVICE_APP
          echo REPO_NAME=$REPO_NAME >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ECR_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download artifact
        run: |
          aws s3 cp s3://$S3_GITHUB_UPLOAD/$GITHUB_REPOSITORY/$GITHUB_RUN_NUMBER/${{ env.SERVICE }}-amd64.tar  /tmp/${{ env.SERVICE }}-amd64.tar
      
      - name: Load Docker image
        run: |
          docker load --input /tmp/${{ env.SERVICE }}-amd64.tar
          docker image ls -a
  
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ env.REPO_NAME }}:${{ env.SHORT_COMMIT }}-amd64'
          format: 'table'
          severity: 'CRITICAL'
          # If there is no fix, ignore
          ignore-unfixed: true
          # Change exit code to 1 to fail
          exit-code: ${{ startsWith(github.event.label.name, 'critical_patch') && 0 || 1 }}
          scanners: ${{ github.ref_name == 'main' && 'vuln,secret' || 'vuln'}}

      - name: Lint image with Hadolint
        run: docker run -e HADOLINT_FAILURE_THRESHOLD=error --rm -i ghcr.io/hadolint/hadolint < Dockerfile
          
  push-image:
    runs-on: ${{ matrix.runner }}
    if: github.ref_name == 'master'
    needs: [test-code, test-image]
    timeout-minutes: 10
    strategy:
      matrix:
        platform: [amd64, arm64]
        include:
          - platform: amd64
            runner: prod-runner-small
          - platform: arm64
            runner: prod-runner-small-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env
      
      - name: Configure Repo Environment Variables
        run: |  
          export SERVICE_APP=${{ env.SERVICE }}
          export SHORT_COMMIT=$(git log -1 --pretty=format:%h)
          echo SHORT_COMMIT=$SHORT_COMMIT >> $GITHUB_ENV
          export REPO_NAME=$ECR_REPO_PREFIX$SERVICE_APP
          echo REPO_NAME=$REPO_NAME >> $GITHUB_ENV
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ECR_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Download artifact
        run: |
          aws s3 cp s3://$S3_GITHUB_UPLOAD/$GITHUB_REPOSITORY/$GITHUB_RUN_NUMBER/${{ env.SERVICE }}-${{ matrix.platform }}.tar  /tmp/${{ env.SERVICE }}-${{ matrix.platform }}.tar
    
      - name: Load Docker image
        run: |
          docker load --input /tmp/${{ env.SERVICE }}-${{ matrix.platform }}.tar
          docker image ls -a
        
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Docker Push Commit ID
        run: |
          AWS_ACCOUNT_NUMBER=$(aws sts get-caller-identity --query "Account" --output text)
          docker image ls
          docker tag ${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-${{ matrix.platform }} $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-${{ matrix.platform }}
          docker image ls
          docker push $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-${{ matrix.platform }}

  push-manifest:
    runs-on: prod-runner-small
    needs: [push-image]
    if: github.ref_name == 'master'
    timeout-minutes: 10
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
    
      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env

      - name: Configure Repo Environment Variables
        run: |
          export SERVICE_APP=${{ env.SERVICE }}
          export SHORT_COMMIT=$(git log -1 --pretty=format:%h)
          echo SHORT_COMMIT=$SHORT_COMMIT >> $GITHUB_ENV
          export REPO_NAME=$ECR_REPO_PREFIX$SERVICE_APP
          echo REPO_NAME=$REPO_NAME >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ECR_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Update image with commitid
        run: |
          AWS_ACCOUNT_NUMBER=$(aws sts get-caller-identity --query "Account" --output text)
          for TAG in $SHORT_COMMIT; do
            # Manifiest to create multi build image
            docker manifest create \
              $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:$TAG \
                -a $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-arm64 \
                -a $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:${{env.SHORT_COMMIT}}-amd64
            docker manifest push $AWS_ACCOUNT_NUMBER.dkr.ecr.ap-southeast-2.amazonaws.com/${{env.REPO_NAME}}:$TAG
          done