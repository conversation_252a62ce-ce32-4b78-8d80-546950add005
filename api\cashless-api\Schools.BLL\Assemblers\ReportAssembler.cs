﻿using System;
using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes.Orders.Reports;
using Schools.BLL.Constants;
using Schools.BLL.Helpers;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class ReportAssembler
{
    public static SaleReportDto Convert_SaleReportResult_To_SaleReportDto(IEnumerable<SaleReportResult> rows)
    {
        SaleReportDto report = new();
        List<CategorySaleReportDto> categories = new();

        if (rows != null)
        {
            foreach (var row in rows)
            {
                var index = categories.FindIndex(x => x.CatName == row.CategoryName);

                // options prices is not included in the item price
                var priceWithOption = OrderHelper.GetItemPriceWithSelectedOptions(row.Price, row.Qty, row.SelectedOptions);

                // calcul again GST value if option price was added and if item GstValue is more than 0
                var gstValue = (row.Price != priceWithOption && row.GstValue > 0) ? priceWithOption / OrderConstants.GstPercentage : row.GstValue;

                SalesReportItemDto item = new()
                {
                    Name = row.ItemName,
                    Quantity = row.Qty,
                    SelectedOptions = String.Empty,
                    CanteenStatus = row.CanteenStatus,
                    Price = priceWithOption,
                    GstValue = gstValue,
                    PriceExclGst = gstValue > 0 ? priceWithOption - gstValue : decimal.Zero
                };

                item.SelectedOptions = ItemOptionHelper.GetItemSelectedOptions(row.SelectedOptions);

                if (index >= 0)
                {
                    categories[index].TotalGST += item.GstValue;
                    categories[index].TotalPriceExclGST += item.PriceExclGst;
                    categories[index].TotalPriceIncGST += item.Price;
                    categories[index].Sales.Add(item);
                }
                else
                {
                    CategorySaleReportDto cat = new()
                    {
                        CatName = row.CategoryName,
                        TotalGST = item.GstValue,
                        TotalPriceExclGST = item.PriceExclGst,
                        TotalPriceIncGST = item.Price,
                        Sales = new List<SalesReportItemDto>
                        {
                            item
                        }
                    };

                    categories.Add(cat);
                }
            }

            report.Categories = categories;
            report.TotalGST = categories.Sum(s => s.TotalGST);
            report.TotalPriceExclGST = categories.Sum(s => s.TotalPriceExclGST);
            report.TotalPriceIncGST = categories.Sum(s => s.TotalPriceIncGST);
        }

        return report;
    }

    public static IEnumerable<ClassReportSchoolDto> Convert_ClassReportResult_To_ClassReportDto(IEnumerable<ClassOrderReportResult> rows)
    {
        List<ClassReportSchoolDto> report = new();

        if (rows != null)
        {
            foreach (var row in rows)
            {
                // search class
                var rsIndex = report.FindIndex(f => f.Class == row.Class);

                if (rsIndex <= -1)
                {
                    ClassReportSchoolDto rs = new()
                    {
                        Class = row.Class,
                        School = row.School,
                        Orders = new()
                    };

                    report.Add(rs);
                    rsIndex = report.FindIndex(f => f.Class == row.Class);
                }

                // search order
                var roIndex = report[rsIndex].Orders.FindIndex(f => f.OrderId == row.OrderId);

                if (roIndex <= -1)
                {
                    ClassReportOrderDto ro = new()
                    {
                        OrderId = row.OrderId,
                        StudentName = row.StudentName,
                        MenuType = row.MenuType,
                        RunNumber = row.RunNumber,
                        CanteenStatus = row.CanteenStatus,
                        Items = new()
                    };

                    report[rsIndex].Orders.Add(ro);
                    roIndex = report[rsIndex].Orders.FindIndex(f => f.OrderId == row.OrderId);
                }

                // add item
                ClassReportItemDto item = new()
                {
                    Name = row.Name,
                    Quantity = row.Quantity,
                    SelectedOptions = ItemOptionHelper.GetItemSelectedOptions(row.SelectedOptions)
                };

                report[rsIndex].Orders[roIndex].Items.Add(item);
            }
        }

        return report;
    }

    /// <summary>
    /// Convert orders to a report
    /// </summary>
    public static IEnumerable<Category> DefaultReportAssembler(IEnumerable<OrderAssembler> orders)
    {
        List<Category> report = new();

        if (orders != null)
        {
            foreach (var order in orders)
            {
                var index = report.FindIndex(x => x.CatName == order.CategoryName);

                ReportMenuItem item = new ReportMenuItem();
                item.Name = order.ItemName;
                item.Quantity = order.Qty;
                item.Options = new List<Option>();
                item.RunNumbers = order.RunNumbers;
                item.StudentIds = order.StudentIds;
                item.Price = order.Price;
                item.GstValue = order.GstValue;
                item.PriceExclGst = order.PriceExclGst;
                item.CanteenStatus = order.CanteenStatus;
                item.MenuItemId = order.MenuItemId;

                if (order.Options != null)
                {
                    foreach (var option in order.Options)
                    {
                        Option itemOption = new Option();
                        itemOption.Name = option.Name;
                        itemOption.SelectedOption = option.SelectedOption;
                        itemOption.SelectedOptionList = option.SelectedOptionList;

                        if (option.SubOptions != null)
                        {
                            itemOption.SubOptions = option.SubOptions;
                        }

                        item.Options.Add(itemOption);
                    }
                }

                if (index >= 0)
                {
                    report[index].item.Add(item);
                }
                else
                {
                    Category cat = new Category();
                    cat.CatName = order.CategoryName;
                    cat.item = new List<MenuItem>();
                    cat.item.Add(item);

                    report.Add(cat);
                }
            }
        }

        return report;
    }
}