apiVersion: v1
data:
  ASPNETCORE_ENVIRONMENT: test
  KMS_MASTER_KEY_ARN: arn:aws:kms:ap-southeast-2:846982525963:key/cbdccfb1-956d-4650-aeca-5f59c5ad2d9b
  AWS_S3_ASSETS_BUCKET_NAME: test-schools-assets
  AWS_ASSETS_READ_URL: https://assets.test.spriggyschools.com.au/
  MOBILE_APP_WARNING_VERSION: "7.3.3.318"
  MOBILE_APP_BLOCK_VERSION: "7.3.3.318"
  # Datadog variables https://docs.datadoghq.com/tracing/trace_collection/automatic_instrumentation/dd_libraries/dotnet-core/?tab=linux
  DD_LOGS_INJECTION: 'true'
  DD_PROFILING_ENABLED: 'true'
  DD_SERVICE: 'schools-api'
  DD_DBM_PROPAGATION_MODE: full
  IS_STRIPE_GATEWAY_ENABLED : 'true'
  STRIPE_VERSION: '2024-04-10'
  BALANCE_REFRESH_INTERVALS_IN_MILLIS: '1000,1000,2000,2000'
  FREQUENTLY_ORDERED_ITEMS_PAST_MONTHS: '12'
  FREQUENTLY_ORDERED_ITEMS_FREQUENCY: '3'
  FREQUENTLY_ORDERED_ITEMS_NUMBER_TO_SHOW: '5'
  FREQUENTLY_ORDERED_ITEMS_SCHOOLIDS: '52243,52245'
kind: ConfigMap
metadata:
  name: schools-api-config
  namespace: default
