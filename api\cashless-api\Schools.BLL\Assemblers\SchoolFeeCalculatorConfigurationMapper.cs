using System.Collections.Generic;
using Schools.BLL.Classes.Fees;
using Schools.BLL.Exceptions;
using Schools.DAL.Enums;
using Schools.DAL.Extensions.Entities;

namespace Schools.BLL.Assemblers;

public static class SchoolFeeCalculatorConfigurationMapper
{
    public static IEnumerable<SchoolFeeCalculatorConfigResponse> MapToSchoolFeeCalculatorsConfigResponses(IEnumerable<SchoolFeeCalculatorConfigurationsEntity> entities)
    {
        if (entities == null)
        {
            throw new FeeCalculatorConfigurationException(message: "Fee Calculator Configurations must be provided");
        }

        var response = new List<SchoolFeeCalculatorConfigResponse>();

        foreach (var e in entities)
        {
            response.Add(MapToSchoolFeeCalculatorConfigResponse(e));
        }

        return response;
    }

    public static SchoolFeeCalculatorConfigResponse MapToSchoolFeeCalculatorConfigResponse(SchoolFeeCalculatorConfigurationsEntity entity)
    {
        if (entity == null)
        {
            throw new FeeCalculatorConfigurationException(message: "Fee Calculator Configuration must be provided");
        }

        return new SchoolFeeCalculatorConfigResponse()
        {
            FeeCalculatorId = entity.FeeCalculatorId,
            FeeCalculatorName = entity.FeeCalculator?.Name,
            CalculatorOrderType = entity.FeeCalculator?.CalculatorOrderType ?? OrderType.Unknown,
            EndDate = entity.EndDate,
            IsActive = entity.IsActive,
            SchoolCalculatorConfigurationId = entity.SchoolCalculatorConfigurationId,
            SchoolId = entity.SchoolId,
            MerchantId = entity.MerchantId,
            StartDate = entity.StartDate
        };
    }
}
