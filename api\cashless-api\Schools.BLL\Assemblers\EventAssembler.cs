﻿using System;
using System.Collections.Generic;
using Schools.BLL.Classes;
using Schools.BLL.Constants;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class EventAssembler
{
    public static SchoolEventDto Convert_EventEntity_To_EventDto(SchoolEventEntity request)
    {
        SchoolEventDto dto = new()
        {
            SchoolEventId = request.SchoolEventId,
            SchoolId = request.SchoolId,
            IsActive = request.IsActive,
            Name = request.Name,
            Description = request.Description,
            EventDate = request.EventDate.ToString(DateTimeConstants.DefaultDateFormat),
            CutOffDate = request.CutOffDate.ToString(DateTimeConstants.DefaultDateFormat),
            SpecificClasses = request.SpecificClasses,
            WeeksPreOrder = request.WeeksPreOrder,
            MenuId = Convert.ToInt32(request.MenuId),
            MerchantId = request.MerchantId,
            ImageUrl = request.ImageUrl,
            EventTemplateId = request.EventTemplateId,
            WaiveEventOrderFee = request.WaiveEventOrderFee
        };

        return dto;
    }

    public static List<SchoolEventDto> Convert_ListEventEntity_To_ListEventDto(List<SchoolEventEntity> request)
    {
        List<SchoolEventDto> dto = new();

        if (request != null && request.Count > 0)
        {
            request.ForEach(e =>
            {
                dto.Add(Convert_EventEntity_To_EventDto(e));
            });
        }

        return dto;
    }
}
