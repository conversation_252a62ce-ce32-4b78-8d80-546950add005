﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Orders
{
    public class PlaceOrderResponse
    {
        public int OrderId { get; set; }
        public string Error { get; set; }
        public int StudentId { get; set; }
        public int? OrderStatusId { get; set; }
        public string OrderDate { get; set; }
        public string MenuType { get; set; }
    }

    public class CreateOrdersSummary
    {
        public List<CreateOrdersInfo> CreateOrdersInfo;
        public decimal TotalAmount { get; set; }
        public decimal Balance { get; set; }
        public bool TopUpRequired => TotalAmount > Balance;
    }

    public class CreateOrdersInfo
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; }
        public decimal Price { get; set; }
        public decimal Fee { get; set; }
        public string MenuType { get; set; }
        public string MenuFriendlyName { get; set; }
        public DateTime OrderDate { get; set; }
    }

    public class EditOrderSummary
    {
        public int OrderId { get; set; }
        public decimal Price { get; set; }
        public decimal Fee { get; set; }
        public decimal PreviousPrice { get; set; }
        public decimal PriceDiff => Price - PreviousPrice;
        public decimal Balance { get; set; }
        public bool TopUpRequired => PriceDiff > Math.Max(0, Balance);
    }
}
