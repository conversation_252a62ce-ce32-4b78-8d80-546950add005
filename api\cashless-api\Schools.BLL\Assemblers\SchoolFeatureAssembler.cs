﻿using System;
using System.Collections.Generic;
using Schools.BLL.Classes.SchoolFeature;
using Schools.BLL.Exceptions;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public interface ISchoolFeatureAssembler
{
    SchoolFeatureDto ConvertSchoolFeatureEntityToSchoolFeatureDto(SchoolFeatureEntity entity);
    List<SchoolFeatureDto> ConvertListSchoolFeatureEntityToListSchoolFeatureDto(IEnumerable<SchoolFeatureEntity> entities);
    FeatureTypeEnum ConvertOptionNameToType(string optionName);
    string ConvertTypeToOptionName(FeatureTypeEnum type);
}

public class SchoolFeatureAssembler : ISchoolFeatureAssembler
{
    public SchoolFeatureAssembler()
    {
    }

    /// <summary>
    /// Convert a single school feature entity to an dto
    /// </summary>
    public SchoolFeatureDto ConvertSchoolFeatureEntityToSchoolFeatureDto(SchoolFeatureEntity entity)
    {
        SchoolFeatureDto dto = new()
        {
            IsActive = entity.IsActive,
            Description = entity.OptionDescription
        };

        dto.Type = this.ConvertOptionNameToType(entity.OptionName);

        return dto;
    }

    /// <summary>
    /// Convert a school feature entity list to an dto list
    /// </summary>
    public List<SchoolFeatureDto> ConvertListSchoolFeatureEntityToListSchoolFeatureDto(IEnumerable<SchoolFeatureEntity> entities)
    {
        List<SchoolFeatureDto> dto = new();

        if (entities == null)
        {
            return dto;
        }

        foreach (var e in entities)
        {
            // convert each entities to a dto
            dto.Add(this.ConvertSchoolFeatureEntityToSchoolFeatureDto(e));
        }

        return dto;
    }

    /// <summary>
    /// Convert the option name to the type enum
    /// </summary>
    public FeatureTypeEnum ConvertOptionNameToType(string optionName)
    {
        var type = FeatureTypeEnum.PayAtCanteen;

        switch (optionName)
        {
            case SchoolFeatureNameConstant.PayAtCanteen:
                type = FeatureTypeEnum.PayAtCanteen;
                break;

            case SchoolFeatureNameConstant.Allergies:
                type = FeatureTypeEnum.Allergies;
                break;

            case SchoolFeatureNameConstant.Uniform:
                type = FeatureTypeEnum.Uniform;
                break;
        }

        return type;
    }

    /// <summary>
    /// Convert the feature type to option name
    /// </summary>
    public string ConvertTypeToOptionName(FeatureTypeEnum type)
    {
        string optionName = String.Empty;

        switch (type)
        {
            case FeatureTypeEnum.PayAtCanteen:
                optionName = SchoolFeatureNameConstant.PayAtCanteen;
                break;

            case FeatureTypeEnum.Allergies:
                optionName = SchoolFeatureNameConstant.Allergies;
                break;

            case FeatureTypeEnum.Uniform:
                optionName = SchoolFeatureNameConstant.Uniform;
                break;

            default:
                throw new SchoolFeatureException("Unknown feature type");
        }

        return optionName;
    }
}
