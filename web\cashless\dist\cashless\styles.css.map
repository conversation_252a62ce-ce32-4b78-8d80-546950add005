{"version": 3, "file": "styles.css", "mappings": ";;;AAAA,YAAY,eAAe,CAAC,iBAAiB,CAAC,wBAAwB,uBAAuB,CAAC,iCAAiC,gBAAgB,CAAC,oBAAoB,iBAAiB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,2DAA2D,CAAC,0BAA0B,CAAC,8CAA8C,YAAY,CAAC,qBAAqB,QAAQ,CAAC,kBAAkB,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,MAAM,CAAC,+BAA+B,SAAS,CAAC,OAAO,CAAC,mDAAmD,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,uBAAuB,cAAc,CAAC,YAAY,CAAC,6BAA6B,YAAY,CAAC,4BAA4B,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,iBAAiB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,YAAY,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,CAAC,sBAAsB,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,yCAAyC,CAAC,yDAAyD,CAAC,SAAS,CAAC,mDAAmD,SAAS,CAAC,6EAA6E,UAAU,CAAC,2BAA2B,0BAA0B,CAAC,kCAAkC,mDAAmD,CAAC,iBAAiB,CAAC,SAAS,CAAC,+DAA+D,SAAS,CAAC,kBAAkB,CAAC,qCAAqC,eAAe,CAAC,6CAA6C,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,aAAa,CAAC,cAAc,CAAC,wBAAwB,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,+BAA+B,WAAW,CAAC,yCAAyC,wBAAwB,CAAC,iCAAiC,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,iDAAiD,wBAAwB,CAAC,iCAAiC,CAAC,mBAAmB,CAAC,yCAAyC,IAAI,CAAC,CAAC,uCAAuC,IAAI,CAAC,CAAC,oDAAoD,8CAA8C,CAAC,0DAA0D,4CAA4C,CAAC,qBAAqB,iBAAiB,CAAC,6BAA6B,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,gDAAgD,CAAC,uJAAuJ,CAAC,2DAA2D,CAAC,mCAAmC,UAAU,CAAC,0BAA0B,oCAAoC,CAAC,yBAAyB,iBAAiB,CAAC,iCAAiC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,oDAAoD,CAAC,mKAAmK,CAAC,+DAA+D,CAAC,uCAAuC,UAAU,CAAC,0BAA0B,wCAAwC,CAAC,oBAAoB,+BAA+B,CAAC,KAAK,oDAAoD,CAAC,iDAAiD,CAAC,wDAAwD,CAAC,wDAAwD,CAAC,2DAA2D,CAAC,YAAY,oDAAoD,CAAC,UAAU,oDAAoD,CAAC,KAAK,mDAAmD,CAAC,0BAA0B,qBAAqB,CAAC,uDAAuD,aAAa,CAAC,gKAAgK,aAAa,CAAC,4IAA4I,kBAAkB,CAAC,0JAA0J,aAAa,CAAC,sIAAsI,aAAa,CAAC,kHAAkH,kBAAkB,CAAC,gIAAgI,aAAa,CAAC,8JAA8J,aAAa,CAAC,0IAA0I,kBAAkB,CAAC,wJAAwJ,aAAa,CAAC,0JAA0J,aAAa,CAAC,sIAAsI,kBAAkB,CAAC,oJAAoJ,aAAa,CAAC,gMAAgM,aAAa,CAAC,4KAA4K,kBAAkB,CAAC,oBAAoB,wBAAwB,CAAC,qBAAqB,CAAC,+DAA+D,uHAAuH,CAAC,+DAA+D,wHAAwH,CAAC,+DAA+D,wHAAwH,CAAC,+DAA+D,wHAAwH,CAAC,+DAA+D,yHAAyH,CAAC,+DAA+D,yHAAyH,CAAC,+DAA+D,0HAA0H,CAAC,+DAA+D,0HAA0H,CAAC,+DAA+D,0HAA0H,CAAC,+DAA+D,0HAA0H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,2HAA2H,CAAC,iEAAiE,4HAA4H,CAAC,iEAAiE,4HAA4H,CAAC,iEAAiE,4HAA4H,CAAC,iEAAiE,4HAA4H,CAAC,iEAAiE,6HAA6H,CAAC,iEAAiE,6HAA6H,CAAC,iEAAiE,6HAA6H,CAAC,iEAAiE,6HAA6H,CAAC,iEAAiE,6HAA6H,CAAC,yBAAyB,YAAY,CAAC,KAAK,+CAA+C,CAAC,wCAAwC,CAAC,iCAAiC,CAAC,0CAA0C,CAAC,kCAAkC,CAAC,KAAK,iDAAiD,CAAC,0CAA0C,CAAC,mCAAmC,CAAC,4CAA4C,CAAC,oCAAoC,CAAC,cAAc,yCAAyC,CAAC,qJAAqJ,CAAC,yCAAyC,CAAC,qDAAqD,CAAC,oJAAoJ,CAAC,kDAAkD,CAAC,cAAc,6CAA6C,CAAC,sCAAsC,CAAC,+BAA+B,CAAC,uCAAuC,CAAC,gCAAgC,CAAC,gDAAgD,CAAC,yCAAyC,CAAC,kCAAkC,CAAC,gDAAgD,CAAC,mCAAmC,CAAC,sBAAsB,oDAAoD,CAAC,yDAAyD,CAAC,yCAAyC,CAAC,wDAAwD,wCAAwC,CAAC,gFAAgF,CAAC,8BAA8B,wDAAwD,6BAA6B,CAAC,CAAC,qEAAqE,wDAAwD,8BAA8B,CAAC,kVAAkV,CAAC,CAAC,uDAAuD,wCAAwC,CAAC,gFAAgF,CAAC,iCAAiC,oDAAoD,CAAC,0DAA0D,CAAC,yCAAyC,CAAC,mEAAmE,yCAAyC,CAAC,iFAAiF,CAAC,8BAA8B,mEAAmE,6BAA6B,CAAC,CAAC,qEAAqE,mEAAmE,8BAA8B,CAAC,mVAAmV,CAAC,CAAC,kEAAkE,yCAAyC,CAAC,iFAAiF,CAAC,+BAA+B,oDAAoD,CAAC,yDAAyD,CAAC,yCAAyC,CAAC,iEAAiE,wCAAwC,CAAC,gFAAgF,CAAC,8BAA8B,iEAAiE,6BAA6B,CAAC,CAAC,qEAAqE,iEAAiE,8BAA8B,CAAC,kVAAkV,CAAC,CAAC,gEAAgE,wCAAwC,CAAC,gFAAgF,CAAC,iBAAiB,2CAA2C,CAAC,8CAA8C,CAAC,iBAAiB,2DAA2D,CAAC,6CAA6C,CAAC,8CAA8C,CAAC,2DAA2D,CAAC,KAAK,2CAA2C,CAAC,4DAA4D,CAAC,sEAAsE,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,2DAA2D,CAAC,qEAAqE,CAAC,4DAA4D,CAAC,qEAAqE,CAAC,uEAAuE,CAAC,4DAA4D,CAAC,sDAAsD,CAAC,iDAAiD,CAAC,kEAAkE,CAAC,2EAA2E,CAAC,wEAAwE,CAAC,4DAA4D,CAAC,kEAAkE,CAAC,kEAAkE,CAAC,6CAA6C,CAAC,qDAAqD,CAAC,wEAAwE,CAAC,6DAA6D,CAAC,uEAAuE,CAAC,8DAA8D,CAAC,uEAAuE,CAAC,yEAAyE,CAAC,mDAAmD,CAAC,8DAA8D,CAAC,wDAAwD,CAAC,2DAA2D,CAAC,oEAAoE,CAAC,iEAAiE,CAAC,2DAA2D,CAAC,2DAA2D,CAAC,qDAAqD,CAAC,0EAA0E,CAAC,0BAA0B,qCAAqC,CAAC,+EAA+E,iCAAiC,CAAC,kCAAkC,CAAC,qDAAqD,CAAC,4DAA4D,CAAC,mDAAmD,CAAC,4DAA4D,CAAC,uDAAuD,CAAC,kCAAkC,gCAAgC,CAAC,4DAA4D,WAAW,CAAC,kEAAkE,WAAW,CAAC,4EAA4E,qBAAqB,CAAC,oGAAoG,yBAAyB,CAAC,mGAAmG,0BAA0B,CAAC,iGAAiG,yBAAyB,CAAC,oGAAoG,qBAAqB,CAAC,+BAA+B,2CAA2C,CAAC,4DAA4D,CAAC,uEAAuE,CAAC,6CAA6C,CAAC,qDAAqD,CAAC,yEAAyE,CAAC,6BAA6B,2CAA2C,CAAC,4DAA4D,CAAC,sEAAsE,CAAC,6CAA6C,CAAC,qDAAqD,CAAC,wEAAwE,CAAC,+IAA+I,mCAAmC,CAAC,yJAAyJ,gBAAgB,CAAC,oCAAoC,CAAC,0BAA0B,eAAe,CAAC,6EAA6E,QAAQ,CAAC,qHAAqH,uHAAuH,CAAC,mDAAmD,CAAC,+EAA+E,gBAAgB,CAAC,mBAAmB,CAAC,qFAAqF,gBAAgB,CAAC,kBAAkB,CAAC,kHAAkH,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,0DAA0D,CAAC,4CAA4C,CAAC,qDAAqD,CAAC,6CAA6C,CAAC,4DAA4D,CAAC,8CAA8C,CAAC,uDAAuD,CAAC,+CAA+C,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,yCAAyC,CAAC,kDAAkD,CAAC,0CAA0C,CAAC,wDAAwD,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,yCAAyC,CAAC,uDAAuD,CAAC,0CAA0C,CAAC,KAAK,yCAAyC,CAAC,2DAA2D,CAAC,4DAA4D,CAAC,sDAAsD,CAAC,oDAAoD,CAAC,qDAAqD,CAAC,wDAAwD,CAAC,wDAAwD,CAAC,oCAAoC,yCAAyC,CAAC,2DAA2D,CAAC,4DAA4D,CAAC,sDAAsD,CAAC,oDAAoD,CAAC,qDAAqD,CAAC,yDAAyD,CAAC,wDAAwD,CAAC,kCAAkC,yCAAyC,CAAC,2DAA2D,CAAC,4DAA4D,CAAC,sDAAsD,CAAC,oDAAoD,CAAC,qDAAqD,CAAC,wDAAwD,CAAC,wDAAwD,CAAC,KAAK,iDAAiD,CAAC,0CAA0C,CAAC,mCAAmC,CAAC,4CAA4C,CAAC,oCAAoC,CAAC,KAAK,yCAAyC,CAAC,0BAA0B,kCAAkC,CAAC,8CAA8C,CAAC,qDAAqD,CAAC,0BAA0B,4CAA4C,CAAC,qCAAqC,CAAC,8BAA8B,CAAC,+BAA+B,CAAC,sCAAsC,CAAC,oDAAoD,CAAC,6CAA6C,CAAC,sCAAsC,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,uBAAuB,4CAA4C,CAAC,2CAA2C,CAAC,oDAAoD,CAAC,wCAAwC,CAAC,yCAAyC,CAAC,mCAAmC,CAAC,uCAAuC,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,kEAAkE,CAAC,yDAAyD,CAAC,qHAAqH,0CAA0C,CAAC,2CAA2C,CAAC,oDAAoD,CAAC,wCAAwC,CAAC,yCAAyC,CAAC,iCAAiC,CAAC,qCAAqC,CAAC,8CAA8C,CAAC,8CAA8C,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,mHAAmH,0CAA0C,CAAC,2CAA2C,CAAC,oDAAoD,CAAC,wCAAwC,CAAC,yCAAyC,CAAC,iCAAiC,CAAC,qCAAqC,CAAC,8CAA8C,CAAC,8CAA8C,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,+GAA+G,0CAA0C,CAAC,2CAA2C,CAAC,oDAAoD,CAAC,wCAAwC,CAAC,yCAAyC,CAAC,iCAAiC,CAAC,qCAAqC,CAAC,8CAA8C,CAAC,8CAA8C,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,oCAAoC,gCAAgC,CAAC,uBAAuB,6CAA6C,CAAC,sCAAsC,CAAC,+BAA+B,CAAC,6CAA6C,CAAC,gCAAgC,CAAC,sBAAsB,qDAAqD,CAAC,0CAA0C,CAAC,qDAAqD,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,kDAAkD,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,iDAAiD,CAAC,yCAAyC,CAAC,mDAAmD,CAAC,8CAA8C,CAAC,kDAAkD,CAAC,qDAAqD,CAAC,gDAAgD,CAAC,oDAAoD,CAAC,gEAAgE,CAAC,kJAAkJ,CAAC,sCAAsC,CAAC,0JAA0J,CAAC,qCAAqC,CAAC,kDAAkD,CAAC,uDAAuD,CAAC,iDAAiD,CAAC,4CAA4C,CAAC,kDAAkD,CAAC,uDAAuD,CAAC,iDAAiD,CAAC,uCAAuC,CAAC,oDAAoD,CAAC,yDAAyD,CAAC,mDAAmD,CAAC,2CAA2C,CAAC,sCAAsC,sEAAsE,CAAC,kDAAkD,qBAAqB,CAAC,iCAAiC,qDAAqD,CAAC,0CAA0C,CAAC,qDAAqD,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,kDAAkD,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,iDAAiD,CAAC,yCAAyC,CAAC,+BAA+B,qDAAqD,CAAC,0CAA0C,CAAC,qDAAqD,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,kDAAkD,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,iDAAiD,CAAC,yCAAyC,CAAC,sBAAsB,kCAAkC,CAAC,sBAAsB,qDAAqD,CAAC,uCAAuC,CAAC,qDAAqD,CAAC,8CAA8C,CAAC,wCAAwC,CAAC,sCAAsC,iCAAiC,CAAC,kCAAkC,CAAC,8BAA8B,CAAC,0GAA0G,CAAC,kBAAkB,CAAC,yDAAyD,CAAC,mBAAmB,CAAC,4DAA4D,CAAC,eAAe,CAAC,wDAAwD,CAAC,6BAA6B,CAAC,yEAAyE,CAAC,uBAAuB,CAAC,4EAAoE,CAApE,oEAAoE,CAAC,sBAAsB,CAAC,kEAAkE,CAAC,sCAAsC,sEAAsE,CAAC,kCAAkC,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,6BAA6B,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,iCAAiC,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,6BAA6B,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,+BAA+B,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,6BAA6B,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,iCAAiC,iCAAiC,CAAC,sCAAsC,iCAAiC,CAAC,kCAAkC,CAAC,0GAA0G,CAAC,qDAAqD,CAAC,yDAAyD,CAAC,wDAAwD,CAAC,yEAAyE,CAAC,4EAAoE,CAApE,oEAAoE,CAAC,+DAA+D,CAAC,gBAAgB,wCAAwC,CAAC,yCAAyC,CAAC,uCAAuC,CAAC,6CAA6C,CAAC,+CAA+C,CAAC,0DAA0D,CAAC,6CAA6C,CAAC,4BAA4B,iCAAiC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,yCAAyC,CAAC,wDAAwD,CAAC,6DAA6D,CAAC,sCAAsC,CAAC,4DAA4D,CAAC,2DAA2D,CAAC,2BAA2B,iCAAiC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,yCAAyC,CAAC,wDAAwD,CAAC,6DAA6D,CAAC,sCAAsC,CAAC,6DAA6D,CAAC,4DAA4D,CAAC,yBAAyB,iCAAiC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,yCAAyC,CAAC,wDAAwD,CAAC,6DAA6D,CAAC,sCAAsC,CAAC,4DAA4D,CAAC,2DAA2D,CAAC,gBAAgB,qDAAqD,CAAC,uCAAuC,CAAC,8CAA8C,CAAC,qDAAqD,CAAC,wCAAwC,CAAC,KAAK,oDAAoD,CAAC,8CAA8C,CAAC,2DAA2D,CAAC,2DAA2D,CAAC,gCAAgC,CAAC,KAAK,kDAAkD,CAAC,oCAAoC,CAAC,6CAA6C,CAAC,2CAA2C,CAAC,qCAAqC,CAAC,mBAAmB,yDAAyD,CAAC,8DAA8D,CAAC,2DAA2D,CAAC,uEAAuE,CAAC,4DAA4D,CAAC,qEAAqE,CAAC,oDAAoD,CAAC,sDAAsD,CAAC,uDAAuD,CAAC,+DAA+D,CAAC,iEAAiE,CAAC,kEAAkE,CAAC,+DAA+D,CAAC,kDAAkD,CAAC,mDAAmD,CAAC,kDAAkD,CAAC,mDAAmD,CAAC,0CAA0C,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,kEAAkE,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,8DAA8D,6CAA6C,CAAC,+CAA+C,CAAC,+CAA+C,CAAC,qDAAqD,CAAC,6DAA6D,CAAC,6CAA6C,CAAC,6CAA6C,CAAC,uCAAuC,CAAC,+CAA+C,CAAC,qBAAqB,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,gCAAgC,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,8BAA8B,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uLAAuL,aAAa,CAAC,2OAA2O,aAAa,CAAC,sMAAsM,SAAS,CAAC,mBAAmB,mDAAmD,CAAC,mDAAmD,CAAC,qDAAqD,CAAC,yPAAyP,WAAW,CAAC,4PAA4P,WAAW,CAAC,mBAAmB,uDAAuD,CAAC,gDAAgD,CAAC,yCAAyC,CAAC,kDAAkD,CAAC,0CAA0C,CAAC,4DAA4D,CAAC,qDAAqD,CAAC,8CAA8C,CAAC,4DAA4D,CAAC,+CAA+C,CAAC,qEAAqE,CAAC,8DAA8D,CAAC,uDAAuD,CAAC,qEAAqE,CAAC,wDAAwD,CAAC,2BAA2B,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,wBAAwB,CAAC,KAAK,wDAAwD,CAAC,gDAAgD,CAAC,sDAAsD,CAAC,uDAAuD,CAAC,KAAK,mCAAmC,CAAC,6CAA6C,eAAe,CAAC,gGAAgG,QAAQ,CAAC,wIAAwI,uHAAuH,CAAC,mDAAmD,CAAC,kGAAkG,eAAe,CAAC,kBAAkB,CAAC,wGAAwG,eAAe,CAAC,kBAAkB,CAAC,qIAAqI,eAAe,CAAC,kBAAkB,CAAC,sGAAsG,YAAY,CAAC,KAAK,sDAAsD,CAAC,+CAA+C,CAAC,wCAAwC,CAAC,sDAAsD,CAAC,yCAAyC,CAAC,6CAA6C,CAAC,wCAAwC,kDAAkD,CAAC,0DAA0D,CAAC,2CAA2C,CAAC,6DAA6D,CAAC,gDAAgD,CAAC,4CAA4C,CAAC,8CAA8C,CAAC,mEAAmE,CAAC,mEAAmE,CAAC,sDAAsD,CAAC,sDAAsD,CAAC,qDAAqD,CAAC,qDAAqD,CAAC,8DAA8D,kDAAkD,CAAC,0DAA0D,CAAC,2CAA2C,CAAC,6DAA6D,CAAC,gDAAgD,CAAC,4CAA4C,CAAC,8CAA8C,CAAC,mEAAmE,CAAC,mEAAmE,CAAC,sDAAsD,CAAC,sDAAsD,CAAC,qDAAqD,CAAC,qDAAqD,CAAC,0DAA0D,kDAAkD,CAAC,0DAA0D,CAAC,2CAA2C,CAAC,6DAA6D,CAAC,gDAAgD,CAAC,4CAA4C,CAAC,8CAA8C,CAAC,mEAAmE,CAAC,mEAAmE,CAAC,sDAAsD,CAAC,sDAAsD,CAAC,qDAAqD,CAAC,qDAAqD,CAAC,sFAAsF,yDAAyD,CAAC,uDAAuD,CAAC,oFAAoF,yDAAyD,CAAC,uDAAuD,CAAC,gFAAgF,yDAAyD,CAAC,uDAAuD,CAAC,oBAAoB,oDAAoD,CAAC,oBAAoB,mDAAmD,CAAC,qCAAqC,CAAC,mDAAmD,CAAC,4CAA4C,CAAC,sCAAsC,CAAC,KAAK,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,8BAA8B,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,2BAA2B,+DAA+D,CAAC,iEAAiE,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,gDAAgD,CAAC,0CAA0C,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,kDAAkD,CAAC,wDAAwD,CAAC,gEAAgE,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,uDAAuD,CAAC,uDAAuD,CAAC,yDAAyD,CAAC,kCAAkC,sEAAsE,CAAC,kDAAkD,qBAAqB,CAAC,KAAK,oCAAoC,CAAC,kCAAkC,iCAAiC,CAAC,kCAAkC,CAAC,0GAA0G,CAAC,qDAAqD,CAAC,yDAAyD,CAAC,wDAAwD,CAAC,yEAAyE,CAAC,4EAAoE,CAApE,oEAAoE,CAAC,+DAA+D,CAAC,6BAA6B,uCAAuC,CAAC,4BAA4B,0CAA0C,CAAC,2BAA2B,0CAA0C,CAAC,yBAAyB,0CAA0C,CAAC,oCAAoC,+DAA+D,CAAC,sDAAsD,CAAC,wCAAwC,wCAAwC,CAAC,yCAAyC,CAAC,uCAAuC,2CAA2C,CAAC,yCAAyC,CAAC,sCAAsC,2CAA2C,CAAC,yCAAyC,CAAC,oCAAoC,2CAA2C,CAAC,yCAAyC,CAAC,+CAA+C,gEAAgE,CAAC,iEAAiE,CAAC,uDAAuD,CAAC,wDAAwD,CAAC,oCAAoC,2CAA2C,CAAC,4CAA4C,CAAC,mCAAmC,8CAA8C,CAAC,4CAA4C,CAAC,kCAAkC,8CAA8C,CAAC,4CAA4C,CAAC,gCAAgC,8CAA8C,CAAC,4CAA4C,CAAC,2CAA2C,mEAAmE,CAAC,oEAAoE,CAAC,0DAA0D,CAAC,2DAA2D,CAAC,4CAA4C,CAAC,yBAAyB,uDAAuD,CAAC,sCAAsC,2CAA2C,CAAC,qCAAqC,8CAA8C,CAAC,oCAAoC,8CAA8C,CAAC,kCAAkC,8CAA8C,CAAC,6CAA6C,0DAA0D,CAAC,mEAAmE,CAAC,uDAAuD,CAAC,gEAAgE,CAAC,yCAAyC,8CAA8C,CAAC,iDAAiD,CAAC,yIAAyI,WAAW,CAAC,4UAA4U,WAAW,CAAC,2IAA2I,WAAW,CAAC,iEAAiE,iDAAiD,CAAC,qDAAqD,CAAC,+DAA+D,iDAAiD,CAAC,sDAAsD,CAAC,2DAA2D,iDAAiD,CAAC,qDAAqD,CAAC,kDAAkD,8CAA8C,CAAC,iDAAiD,CAAC,kJAAkJ,WAAW,CAAC,8VAA8V,WAAW,CAAC,oJAAoJ,WAAW,CAAC,0EAA0E,8CAA8C,CAAC,uDAAuD,CAAC,wEAAwE,8CAA8C,CAAC,uDAAuD,CAAC,oEAAoE,8CAA8C,CAAC,uDAAuD,CAAC,2KAA2K,WAAW,CAAC,YAAY,iCAAiC,CAAC,kCAAkC,CAAC,2GAA2G,CAAC,sDAAsD,CAAC,0DAA0D,CAAC,yDAAyD,CAAC,0EAA0E,CAAC,0EAAkE,CAAlE,kEAAkE,CAAC,gEAAgE,CAAC,qBAAqB,8CAA8C,CAAC,iDAAiD,CAAC,oCAAoC,CAAC,8CAA8C,CAAC,iDAAiD,CAAC,qEAAqE,WAAW,CAAC,uKAAuK,WAAW,CAAC,sEAAsE,WAAW,CAAC,iCAAiC,iDAAiD,CAAC,oDAAoD,CAAC,gCAAgC,iDAAiD,CAAC,qDAAqD,CAAC,8BAA8B,iDAAiD,CAAC,oDAAoD,CAAC,iCAAiC,oCAAoC,CAAC,iDAAiD,CAAC,qDAAqD,CAAC,gCAAgC,oCAAoC,CAAC,iDAAiD,CAAC,sDAAsD,CAAC,8BAA8B,oCAAoC,CAAC,iDAAiD,CAAC,qDAAqD,CAAC,yCAAyC,gDAAgD,CAAC,yDAAyD,CAAC,yCAAyC,uCAAuC,CAAC,6CAA6C,CAAC,8CAA8C,CAAC,YAAY,CAAC,+BAA+B,8CAA8C,CAAC,iDAAiD,CAAC,+HAA+H,WAAW,CAAC,wTAAwT,WAAW,CAAC,iIAAiI,WAAW,CAAC,uDAAuD,8CAA8C,CAAC,uDAAuD,CAAC,qDAAqD,8CAA8C,CAAC,uDAAuD,CAAC,iDAAiD,8CAA8C,CAAC,uDAAuD,CAAC,uEAAuE,6CAA6C,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,yDAAyD,+BAA+B,CAAC,0BAA0B,CAAC,yBAAyB,CAAC,uDAAuD,iCAAiC,CAAC,0BAA0B,CAAC,yBAAyB,CAAC,qDAAqD,iCAAiC,CAAC,0BAA0B,CAAC,yBAAyB,CAAC,iDAAiD,iCAAiC,CAAC,0BAA0B,CAAC,yBAAyB,CAAC,mBAAmB,iCAAiC,CAAC,kCAAkC,CAAC,2GAA2G,CAAC,sDAAsD,CAAC,0DAA0D,CAAC,yDAAyD,CAAC,0EAA0E,CAAC,0EAAkE,CAAlE,kEAAkE,CAAC,gEAAgE,CAAC,sBAAsB,qDAAqD,CAAC,uCAAuC,CAAC,qDAAqD,CAAC,wCAAwC,CAAC,6BAA6B,sCAAsC,CAAC,8DAA8D,CAAC,oCAAoC,CAAC,6BAA6B,sDAAsD,CAAC,+CAA+C,CAAC,wCAAwC,CAAC,yCAAyC,CAAC,KAAK,kCAAkC,CAAC,qDAAqD,CAAC,yDAAyD,CAAC,sDAAsD,CAAC,KAAK,wCAAwC,CAAC,wCAAwC,CAAC,0CAA0C,CAAC,KAAK,mDAAmD,CAAC,4CAA4C,CAAC,qCAAqC,CAAC,sCAAsC,CAAC,mDAAmD,CAAC,uDAAuD,CAAC,gDAAgD,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,uDAAuD,CAAC,0DAA0D,CAAC,mDAAmD,CAAC,4CAA4C,CAAC,6CAA6C,CAAC,0DAA0D,CAAC,0BAA0B,sDAAsD,CAAC,qCAAqC,sDAAsD,CAAC,mCAAmC,sDAAsD,CAAC,WAAW,iBAAiB,CAAC,qBAAqB,gBAAgB,CAAC,mBAAmB,iBAAiB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,oBAAoB,CAAC,eAAe,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,kDAAkD,CAAC,iCAAiC,CAAC,8BAA8B,CAAC,0DAA0D,CAAC,cAAc,CAAC,0CAA0C,CAAC,eAAe,CAAC,6CAA6C,CAAC,6CAA6C,iBAAiB,CAAC,eAAe,CAAC,uCAAuC,iEAAiE,CAAC,gDAAgD,CAAC,qCAAqC,YAAY,CAAC,mFAAmF,eAAe,CAAC,oCAAoC,cAAc,CAAC,oCAAoC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,aAAa,CAAC,oDAAoD,CAAC,oDAAoD,QAAQ,CAAC,oDAAoD,WAAW,CAAC,qDAAqD,UAAU,CAAC,+DAA+D,SAAS,CAAC,WAAW,CAAC,oDAAoD,WAAW,CAAC,8DAA8D,UAAU,CAAC,UAAU,CAAC,uEAAuE,SAAS,CAAC,iFAAiF,SAAS,CAAC,UAAU,CAAC,sEAAsE,UAAU,CAAC,gFAAgF,UAAU,CAAC,SAAS,CAAC,qCAAqC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,qDAAqD,SAAS,CAAC,qDAAqD,YAAY,CAAC,sDAAsD,UAAU,CAAC,gEAAgE,SAAS,CAAC,WAAW,CAAC,qDAAqD,WAAW,CAAC,+DAA+D,UAAU,CAAC,UAAU,CAAC,wEAAwE,UAAU,CAAC,kFAAkF,SAAS,CAAC,WAAW,CAAC,uEAAuE,WAAW,CAAC,iFAAiF,UAAU,CAAC,UAAU,CAAC,oCAAoC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,qDAAqD,CAAC,oDAAoD,SAAS,CAAC,oDAAoD,YAAY,CAAC,qDAAqD,UAAU,CAAC,+DAA+D,SAAS,CAAC,WAAW,CAAC,oDAAoD,WAAW,CAAC,8DAA8D,UAAU,CAAC,UAAU,CAAC,uEAAuE,UAAU,CAAC,iFAAiF,SAAS,CAAC,WAAW,CAAC,sEAAsE,WAAW,CAAC,gFAAgF,UAAU,CAAC,UAAU,CAAC,KAAK,oCAAoC,CAAC,4BAA4B,CAAC,mDAAmD,CAAC,yDAAyD,CAAC,kBAAkB,oCAAoC,CAAC,4BAA4B,CAAC,gBAAgB,oCAAoC,CAAC,4BAA4B,CAAC,KAAK,wCAAwC,CAAC,0BAA0B,CAAC,2BAA2B,CAAC,oCAAoC,CAAC,qCAAqC,CAAC,KAAK,2DAA2D,CAAC,mDAAmD,CAAC,KAAK,yDAAyD,CAAC,kDAAkD,CAAC,2CAA2C,CAAC,yDAAyD,CAAC,4CAA4C,CAAC,KAAK,yDAAyD,CAAC,gEAAgE,CAAC,wEAAwE,CAAC,kEAAkE,CAAC,wEAAwE,CAAC,kEAAkE,CAAC,2EAA2E,CAAC,2DAA2D,CAAC,mDAAmD,CAAC,oDAAoD,CAAC,oEAAoE,CAAC,0EAA0E,CAAC,0EAA0E,CAAC,kEAAkE,CAAC,mFAAmF,CAAC,6EAA6E,CAAC,kDAAkD,CAAC,KAAK,wCAAwC,CAAC,KAAK,uDAAuD,CAAC,yDAAyD,CAAC,KAAK,8DAA8D,CAAC,sEAAsE,CAAC,8FAA8F,CAAC,uEAAuE,CAAC,kFAAkF,CAAC,kFAAkF,CAAC,uDAAuD,CAAC,qFAAqF,CAAC,gGAAgG,CAAC,8EAA8E,CAAC,uFAAuF,CAAC,sDAAsD,CAAC,mEAAmE,CAAC,sEAAsE,CAAC,0EAA0E,CAAC,kEAAkE,CAAC,+DAA+D,CAAC,sEAAsE,CAAC,qFAAqF,CAAC,6DAA6D,CAAC,wDAAwD,CAAC,4EAA4E,CAAC,8EAA8E,CAAC,gEAAgE,CAAC,+EAA+E,CAAC,0EAA0E,CAAC,0DAA0D,CAAC,kEAAkE,CAAC,mCAAmC,8DAA8D,CAAC,sEAAsE,CAAC,+FAA+F,CAAC,uEAAuE,CAAC,mFAAmF,CAAC,mFAAmF,CAAC,sFAAsF,CAAC,gGAAgG,CAAC,8EAA8E,CAAC,uFAAuF,CAAC,iCAAiC,8DAA8D,CAAC,sEAAsE,CAAC,8FAA8F,CAAC,uEAAuE,CAAC,kFAAkF,CAAC,kFAAkF,CAAC,qFAAqF,CAAC,gGAAgG,CAAC,8EAA8E,CAAC,uFAAuF,CAAC,yCAAyC,uDAAuD,CAAC,uCAAuC,uDAAuD,CAAC,gEAAgE,uCAAuC,CAAC,6CAA6C,CAAC,8CAA8C,CAAC,WAAW,CAAC,6FAA6F,YAAY,CAAC,KAAK,sDAAsD,CAAC,wCAAwC,CAAC,mDAAmD,CAAC,oDAAoD,CAAC,sDAAsD,CAAC,uDAAuD,CAAC,+CAA+C,CAAC,gDAAgD,CAAC,KAAK,uCAAuC,CAAC,KAAK,gDAAgD,CAAC,wDAAwD,CAAC,yDAAyD,CAAC,kEAAkE,CAAC,kEAAkE,CAAC,oEAAoE,CAAC,qDAAqD,CAAC,4DAA4D,CAAC,0DAA0D,CAAC,KAAK,kDAAkD,CAAC,iDAAiD,CAAC,KAAK,mDAAmD,CAAC,qCAAqC,CAAC,sCAAsC,CAAC,+CAA+C,CAAC,4CAA4C,CAAC,sDAAsD,CAAC,+CAA+C,CAAC,wCAAwC,CAAC,sDAAsD,CAAC,yCAAyC,CAAC,KAAK,kDAAkD,CAAC,oDAAoD,CAAC,kDAAkD,CAAC,oDAAoD,CAAC,KAAK,wBAAwB,CAAC,sBAAsB,wBAAwB,CAAC,qBAAqB,wBAAwB,CAAC,mBAAmB,wBAAwB,CAAC,KAAK,yDAAyD,CAAC,8CAA8C,CAAC,sDAAsD,CAAC,8CAA8C,CAAC,oDAAoD,CAAC,4CAA4C,CAAC,KAAK,gDAAgD,CAAC,iEAAiE,CAAC,+DAA+D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,mCAAmC,CAAC,4CAA4C,CAAC,gEAAgE,CAAC,gEAAgE,CAAC,yDAAyD,CAAC,kEAAkE,CAAC,wEAAwE,CAAC,yDAAyD,CAAC,8DAA8D,CAAC,8DAA8D,CAAC,kEAAkE,CAAC,iCAAiC,gDAAgD,CAAC,iEAAiE,CAAC,+DAA+D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,+BAA+B,gDAAgD,CAAC,iEAAiE,CAAC,+DAA+D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,6DAA6D,CAAC,2DAA2D,CAAC,KAAK,gCAAgC,CAAC,KAAK,oDAAoD,CAAC,uDAAuD,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,qDAAqD,CAAC,wDAAwD,CAAC,yDAAyD,CAAC,uBAAuB,aAAa,CAAC,KAAK,mDAAmD,CAAC,sDAAsD,CAAC,yBAAyB,gDAAgD,CAAC,wCAAwC,CAAC,wBAAwB,gDAAgD,CAAC,wCAAwC,CAAC,sBAAsB,gDAAgD,CAAC,wCAAwC,CAAC,KAAK,kCAAkC,CAAC,gCAAgC,CAAC,KAAK,gDAAgD,CAAC,yCAAyC,CAAC,kCAAkC,CAAC,0CAA0C,CAAC,mCAAmC,CAAC,UAAU,eAAe,CAAC,qCAAqC,qBAAqB,CAAC,eAAe,eAAe,CAAC,UAAU,8BAA8B,CAAC,qCAAqC,eAAe,CAAC,cAAc,CAAC,mGAAmG,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,qBAAqB,CAAC,eAAe,CAAC,mGAAmG,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,sBAAsB,CAAC,eAAe,CAAC,mGAAmG,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,wBAAwB,CAAC,eAAe,CAAC,2FAA2F,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,uBAAuB,CAAC,eAAe,CAAC,mDAAmD,kDAAkD,CAAC,eAAe,CAAC,mDAAmD,kDAAkD,CAAC,eAAe,CAAC,kGAAkG,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,4BAA4B,CAAC,4FAA4F,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,4BAA4B,CAAC,sGAAsG,eAAe,CAAC,gFAAgF,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,4BAA4B,CAAC,gDAAgD,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,0BAA0B,CAAC,eAAe,CAAC,gDAAgD,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,6BAA6B,CAAC,eAAe,CAAC,gDAAgD,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,qBAAqB,CAAC,eAAe,CAAC,gDAAgD,cAAc,CAAC,eAAe,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,4BAA4B,CAAC,eAAe,C;;;;ACAholF;AAEA;AACA;EACE;EACA;EACA;EACA;EACA;AACF;AAEA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAF;;AAGA;AACA;EACE;AAAF;;AAGA;EACE;EACA;EAGA;EAGA;EACA;EACA;AAFF;;AC7CA;;;;;EAAA;AAOA;EACE;EACA;AD+CF;;AC5CA;;;EAGE;AD+CF;;AEzDE;ECAA;EACA;EACA;EACA;EACA;AH6DF;AIVI;EFvDF;ICWI,gBEgMiB;ELtIrB;AACF;AIfI;EFvDF;ICWI,gBEgMiB;ELjIrB;AACF;AIpBI;EFvDF;ICWI,gBEgMiB;EL5HrB;AACF;AIzBI;EFvDF;ICWI,iBEgMiB;ELvHrB;AACF;;AEzEE;ECZA;EACA;EACA;EACA;EACA;EDUE;AFgFJ;;AEvEE;ECNA;EACA;EACA;EACA;AHiFF;;AExEE;EACE;EACA;AF2EJ;AEzEI;;EAEE;EACA;AF2EN;;AMzGE;;;;;;EACE;EACA;EACA;EACA;ANiHJ;;AM9FM;EACE;EACA;EACA;ANiGR;;AM/FM;EACE;EACA;EACA;ANkGR;;AM9FQ;EHPN;EAIA;AHsGF;;AMnGQ;EHPN;EAIA;AH2GF;;AMxGQ;EHPN;EAIA;AHgHF;;AM7GQ;EHPN;EAIA;AHqHF;;AMlHQ;EHPN;EAIA;AH0HF;;AMvHQ;EHPN;EAIA;AH+HF;;AM5HQ;EHPN;EAIA;AHoIF;;AMjIQ;EHPN;EAIA;AHyIF;;AMtIQ;EHPN;EAIA;AH8IF;;AM3IQ;EHPN;EAIA;AHmJF;;AMhJQ;EHPN;EAIA;AHwJF;;AMrJQ;EHPN;EAIA;AH6JF;;AMrJM;EACE;ANwJR;;AMrJM;EACE;ANwJR;;AMpJQ;EACE,QAFS;ANyJnB;;AMxJQ;EACE,QAFS;AN6JnB;;AM5JQ;EACE,QAFS;ANiKnB;;AMhKQ;EACE,QAFS;ANqKnB;;AMpKQ;EACE,QAFS;ANyKnB;;AMxKQ;EACE,QAFS;AN6KnB;;AM5KQ;EACE,QAFS;ANiLnB;;AMhLQ;EACE,QAFS;ANqLnB;;AMpLQ;EACE,QAFS;ANyLnB;;AMxLQ;EACE,QAFS;AN6LnB;;AM5LQ;EACE,SAFS;ANiMnB;;AMhMQ;EACE,SAFS;ANqMnB;;AMpMQ;EACE,SAFS;ANyMnB;;AM/LU;EHrBR;AHwNF;;AMnMU;EHrBR;AH4NF;;AMvMU;EHrBR;AHgOF;;AM3MU;EHrBR;AHoOF;;AM/MU;EHrBR;AHwOF;;AMnNU;EHrBR;AH4OF;;AMvNU;EHrBR;AHgPF;;AM3NU;EHrBR;AHoPF;;AM/NU;EHrBR;AHwPF;;AMnOU;EHrBR;AH4PF;;AMvOU;EHrBR;AHgQF;;AIpPI;EE1BE;IACE;IACA;IACA;ENkRN;EMhRI;IACE;IACA;IACA;ENkRN;EM9QM;IHPN;IAIA;EHqRA;EMlRM;IHPN;IAIA;EHyRA;EMtRM;IHPN;IAIA;EH6RA;EM1RM;IHPN;IAIA;EHiSA;EM9RM;IHPN;IAIA;EHqSA;EMlSM;IHPN;IAIA;EHySA;EMtSM;IHPN;IAIA;EH6SA;EM1SM;IHPN;IAIA;EHiTA;EM9SM;IHPN;IAIA;EHqTA;EMlTM;IHPN;IAIA;EHyTA;EMtTM;IHPN;IAIA;EH6TA;EM1TM;IHPN;IAIA;EHiUA;EMzTI;IACE;EN2TN;EMxTI;IACE;EN0TN;EMtTM;IACE,QAFS;EN0TjB;EMzTM;IACE,QAFS;EN6TjB;EM5TM;IACE,QAFS;ENgUjB;EM/TM;IACE,QAFS;ENmUjB;EMlUM;IACE,QAFS;ENsUjB;EMrUM;IACE,QAFS;ENyUjB;EMxUM;IACE,QAFS;EN4UjB;EM3UM;IACE,QAFS;EN+UjB;EM9UM;IACE,QAFS;ENkVjB;EMjVM;IACE,QAFS;ENqVjB;EMpVM;IACE,SAFS;ENwVjB;EMvVM;IACE,SAFS;EN2VjB;EM1VM;IACE,SAFS;EN8VjB;EMpVQ;IHrBR;EH4WA;EMvVQ;IHrBR;EH+WA;EM1VQ;IHrBR;EHkXA;EM7VQ;IHrBR;EHqXA;EMhWQ;IHrBR;EHwXA;EMnWQ;IHrBR;EH2XA;EMtWQ;IHrBR;EH8XA;EMzWQ;IHrBR;EHiYA;EM5WQ;IHrBR;EHoYA;EM/WQ;IHrBR;EHuYA;EMlXQ;IHrBR;EH0YA;EMrXQ;IHrBR;EH6YA;AACF;AIlYI;EE1BE;IACE;IACA;IACA;EN+ZN;EM7ZI;IACE;IACA;IACA;EN+ZN;EM3ZM;IHPN;IAIA;EHkaA;EM/ZM;IHPN;IAIA;EHsaA;EMnaM;IHPN;IAIA;EH0aA;EMvaM;IHPN;IAIA;EH8aA;EM3aM;IHPN;IAIA;EHkbA;EM/aM;IHPN;IAIA;EHsbA;EMnbM;IHPN;IAIA;EH0bA;EMvbM;IHPN;IAIA;EH8bA;EM3bM;IHPN;IAIA;EHkcA;EM/bM;IHPN;IAIA;EHscA;EMncM;IHPN;IAIA;EH0cA;EMvcM;IHPN;IAIA;EH8cA;EMtcI;IACE;ENwcN;EMrcI;IACE;ENucN;EMncM;IACE,QAFS;ENucjB;EMtcM;IACE,QAFS;EN0cjB;EMzcM;IACE,QAFS;EN6cjB;EM5cM;IACE,QAFS;ENgdjB;EM/cM;IACE,QAFS;ENmdjB;EMldM;IACE,QAFS;ENsdjB;EMrdM;IACE,QAFS;ENydjB;EMxdM;IACE,QAFS;EN4djB;EM3dM;IACE,QAFS;EN+djB;EM9dM;IACE,QAFS;ENkejB;EMjeM;IACE,SAFS;ENqejB;EMpeM;IACE,SAFS;ENwejB;EMveM;IACE,SAFS;EN2ejB;EMjeQ;IHrBR;EHyfA;EMpeQ;IHrBR;EH4fA;EMveQ;IHrBR;EH+fA;EM1eQ;IHrBR;EHkgBA;EM7eQ;IHrBR;EHqgBA;EMhfQ;IHrBR;EHwgBA;EMnfQ;IHrBR;EH2gBA;EMtfQ;IHrBR;EH8gBA;EMzfQ;IHrBR;EHihBA;EM5fQ;IHrBR;EHohBA;EM/fQ;IHrBR;EHuhBA;EMlgBQ;IHrBR;EH0hBA;AACF;AI/gBI;EE1BE;IACE;IACA;IACA;EN4iBN;EM1iBI;IACE;IACA;IACA;EN4iBN;EMxiBM;IHPN;IAIA;EH+iBA;EM5iBM;IHPN;IAIA;EHmjBA;EMhjBM;IHPN;IAIA;EHujBA;EMpjBM;IHPN;IAIA;EH2jBA;EMxjBM;IHPN;IAIA;EH+jBA;EM5jBM;IHPN;IAIA;EHmkBA;EMhkBM;IHPN;IAIA;EHukBA;EMpkBM;IHPN;IAIA;EH2kBA;EMxkBM;IHPN;IAIA;EH+kBA;EM5kBM;IHPN;IAIA;EHmlBA;EMhlBM;IHPN;IAIA;EHulBA;EMplBM;IHPN;IAIA;EH2lBA;EMnlBI;IACE;ENqlBN;EMllBI;IACE;ENolBN;EMhlBM;IACE,QAFS;ENolBjB;EMnlBM;IACE,QAFS;ENulBjB;EMtlBM;IACE,QAFS;EN0lBjB;EMzlBM;IACE,QAFS;EN6lBjB;EM5lBM;IACE,QAFS;ENgmBjB;EM/lBM;IACE,QAFS;ENmmBjB;EMlmBM;IACE,QAFS;ENsmBjB;EMrmBM;IACE,QAFS;ENymBjB;EMxmBM;IACE,QAFS;EN4mBjB;EM3mBM;IACE,QAFS;EN+mBjB;EM9mBM;IACE,SAFS;ENknBjB;EMjnBM;IACE,SAFS;ENqnBjB;EMpnBM;IACE,SAFS;ENwnBjB;EM9mBQ;IHrBR;EHsoBA;EMjnBQ;IHrBR;EHyoBA;EMpnBQ;IHrBR;EH4oBA;EMvnBQ;IHrBR;EH+oBA;EM1nBQ;IHrBR;EHkpBA;EM7nBQ;IHrBR;EHqpBA;EMhoBQ;IHrBR;EHwpBA;EMnoBQ;IHrBR;EH2pBA;EMtoBQ;IHrBR;EH8pBA;EMzoBQ;IHrBR;EHiqBA;EM5oBQ;IHrBR;EHoqBA;EM/oBQ;IHrBR;EHuqBA;AACF;AI5pBI;EE1BE;IACE;IACA;IACA;ENyrBN;EMvrBI;IACE;IACA;IACA;ENyrBN;EMrrBM;IHPN;IAIA;EH4rBA;EMzrBM;IHPN;IAIA;EHgsBA;EM7rBM;IHPN;IAIA;EHosBA;EMjsBM;IHPN;IAIA;EHwsBA;EMrsBM;IHPN;IAIA;EH4sBA;EMzsBM;IHPN;IAIA;EHgtBA;EM7sBM;IHPN;IAIA;EHotBA;EMjtBM;IHPN;IAIA;EHwtBA;EMrtBM;IHPN;IAIA;EH4tBA;EMztBM;IHPN;IAIA;EHguBA;EM7tBM;IHPN;IAIA;EHouBA;EMjuBM;IHPN;IAIA;EHwuBA;EMhuBI;IACE;ENkuBN;EM/tBI;IACE;ENiuBN;EM7tBM;IACE,QAFS;ENiuBjB;EMhuBM;IACE,QAFS;ENouBjB;EMnuBM;IACE,QAFS;ENuuBjB;EMtuBM;IACE,QAFS;EN0uBjB;EMzuBM;IACE,QAFS;EN6uBjB;EM5uBM;IACE,QAFS;ENgvBjB;EM/uBM;IACE,QAFS;ENmvBjB;EMlvBM;IACE,QAFS;ENsvBjB;EMrvBM;IACE,QAFS;ENyvBjB;EMxvBM;IACE,QAFS;EN4vBjB;EM3vBM;IACE,SAFS;EN+vBjB;EM9vBM;IACE,SAFS;ENkwBjB;EMjwBM;IACE,SAFS;ENqwBjB;EM3vBQ;IHrBR;EHmxBA;EM9vBQ;IHrBR;EHsxBA;EMjwBQ;IHrBR;EHyxBA;EMpwBQ;IHrBR;EH4xBA;EMvwBQ;IHrBR;EH+xBA;EM1wBQ;IHrBR;EHkyBA;EM7wBQ;IHrBR;EHqyBA;EMhxBQ;IHrBR;EHwyBA;EMnxBQ;IHrBR;EH2yBA;EMtxBQ;IHrBR;EH8yBA;EMzxBQ;IHrBR;EHizBA;EM5xBQ;IHrBR;EHozBA;AACF;AO11BM;EACE;AP41BR;;AO71BM;EACE;APg2BR;;AOj2BM;EACE;APo2BR;;AOr2BM;EACE;APw2BR;;AOz2BM;EACE;AP42BR;;AO72BM;EACE;APg3BR;;AOj3BM;EACE;APo3BR;;AOr3BM;EACE;APw3BR;;AOz3BM;EACE;AP43BR;;AI50BI;EGjDE;IACE;EPi4BN;EOl4BI;IACE;EPo4BN;EOr4BI;IACE;EPu4BN;EOx4BI;IACE;EP04BN;EO34BI;IACE;EP64BN;EO94BI;IACE;EPg5BN;EOj5BI;IACE;EPm5BN;EOp5BI;IACE;EPs5BN;EOv5BI;IACE;EPy5BN;AACF;AI12BI;EGjDE;IACE;EP85BN;EO/5BI;IACE;EPi6BN;EOl6BI;IACE;EPo6BN;EOr6BI;IACE;EPu6BN;EOx6BI;IACE;EP06BN;EO36BI;IACE;EP66BN;EO96BI;IACE;EPg7BN;EOj7BI;IACE;EPm7BN;EOp7BI;IACE;EPs7BN;AACF;AIv4BI;EGjDE;IACE;EP27BN;EO57BI;IACE;EP87BN;EO/7BI;IACE;EPi8BN;EOl8BI;IACE;EPo8BN;EOr8BI;IACE;EPu8BN;EOx8BI;IACE;EP08BN;EO38BI;IACE;EP68BN;EO98BI;IACE;EPg9BN;EOj9BI;IACE;EPm9BN;AACF;AIp6BI;EGjDE;IACE;EPw9BN;EOz9BI;IACE;EP29BN;EO59BI;IACE;EP89BN;EO/9BI;IACE;EPi+BN;EOl+BI;IACE;EPo+BN;EOr+BI;IACE;EPu+BN;EOx+BI;IACE;EP0+BN;EO3+BI;IACE;EP6+BN;EO9+BI;IACE;EPg/BN;AACF;AOv+BA;EAEI;IACE;EPw+BJ;EOz+BE;IACE;EP2+BJ;EO5+BE;IACE;EP8+BJ;EO/+BE;IACE;EPi/BJ;EOl/BE;IACE;EPo/BJ;EOr/BE;IACE;EPu/BJ;EOx/BE;IACE;EP0/BJ;EO3/BE;IACE;EP6/BJ;EO9/BE;IACE;EPggCJ;AACF;AQhhCI;EACE;ARkhCN;;AQhhCI;EACE;ARmhCN;;AQjhCI;EACE;ARohCN;;AQlhCI;EACE;ARqhCN;;AQlhCI;EACE;ARqhCN;;AQnhCI;EACE;ARshCN;;AQphCI;EACE;ARuhCN;;AQrhCI;EACE;ARwhCN;;AQthCI;EACE;ARyhCN;;AQvhCI;EACE;AR0hCN;;AQxhCI;EACE;AR2hCN;;AQzhCI;EACE;AR4hCN;;AQzhCI;EACE;AR4hCN;;AQ1hCI;EACE;AR6hCN;;AQ3hCI;EACE;AR8hCN;;AQ5hCI;EACE;AR+hCN;;AQ7hCI;EACE;ARgiCN;;AQ7hCI;EACE;ARgiCN;;AQ9hCI;EACE;ARiiCN;;AQ/hCI;EACE;ARkiCN;;AQhiCI;EACE;ARmiCN;;AQjiCI;EACE;ARoiCN;;AQjiCI;EACE;ARoiCN;;AQliCI;EACE;ARqiCN;;AQniCI;EACE;ARsiCN;;AQpiCI;EACE;ARuiCN;;AQriCI;EACE;ARwiCN;;AQtiCI;EACE;ARyiCN;;AQtiCI;EACE;ARyiCN;;AQviCI;EACE;AR0iCN;;AQxiCI;EACE;AR2iCN;;AQziCI;EACE;AR4iCN;;AQ1iCI;EACE;AR6iCN;;AQ3iCI;EACE;AR8iCN;;AIrmCI;EIlDA;IACE;ER2pCJ;EQzpCE;IACE;ER2pCJ;EQzpCE;IACE;ER2pCJ;EQzpCE;IACE;ER2pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQxpCE;IACE;ER0pCJ;EQvpCE;IACE;ERypCJ;EQvpCE;IACE;ERypCJ;EQvpCE;IACE;ERypCJ;EQvpCE;IACE;ERypCJ;EQvpCE;IACE;ERypCJ;EQtpCE;IACE;ERwpCJ;EQtpCE;IACE;ERwpCJ;EQtpCE;IACE;ERwpCJ;EQtpCE;IACE;ERwpCJ;EQtpCE;IACE;ERwpCJ;EQrpCE;IACE;ERupCJ;EQrpCE;IACE;ERupCJ;EQrpCE;IACE;ERupCJ;EQrpCE;IACE;ERupCJ;EQrpCE;IACE;ERupCJ;EQrpCE;IACE;ERupCJ;EQppCE;IACE;ERspCJ;EQppCE;IACE;ERspCJ;EQppCE;IACE;ERspCJ;EQppCE;IACE;ERspCJ;EQppCE;IACE;ERspCJ;EQppCE;IACE;ERspCJ;AACF;AI9sCI;EIlDA;IACE;ERmwCJ;EQjwCE;IACE;ERmwCJ;EQjwCE;IACE;ERmwCJ;EQjwCE;IACE;ERmwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQhwCE;IACE;ERkwCJ;EQ/vCE;IACE;ERiwCJ;EQ/vCE;IACE;ERiwCJ;EQ/vCE;IACE;ERiwCJ;EQ/vCE;IACE;ERiwCJ;EQ/vCE;IACE;ERiwCJ;EQ9vCE;IACE;ERgwCJ;EQ9vCE;IACE;ERgwCJ;EQ9vCE;IACE;ERgwCJ;EQ9vCE;IACE;ERgwCJ;EQ9vCE;IACE;ERgwCJ;EQ7vCE;IACE;ER+vCJ;EQ7vCE;IACE;ER+vCJ;EQ7vCE;IACE;ER+vCJ;EQ7vCE;IACE;ER+vCJ;EQ7vCE;IACE;ER+vCJ;EQ7vCE;IACE;ER+vCJ;EQ5vCE;IACE;ER8vCJ;EQ5vCE;IACE;ER8vCJ;EQ5vCE;IACE;ER8vCJ;EQ5vCE;IACE;ER8vCJ;EQ5vCE;IACE;ER8vCJ;EQ5vCE;IACE;ER8vCJ;AACF;AItzCI;EIlDA;IACE;ER22CJ;EQz2CE;IACE;ER22CJ;EQz2CE;IACE;ER22CJ;EQz2CE;IACE;ER22CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQx2CE;IACE;ER02CJ;EQv2CE;IACE;ERy2CJ;EQv2CE;IACE;ERy2CJ;EQv2CE;IACE;ERy2CJ;EQv2CE;IACE;ERy2CJ;EQv2CE;IACE;ERy2CJ;EQt2CE;IACE;ERw2CJ;EQt2CE;IACE;ERw2CJ;EQt2CE;IACE;ERw2CJ;EQt2CE;IACE;ERw2CJ;EQt2CE;IACE;ERw2CJ;EQr2CE;IACE;ERu2CJ;EQr2CE;IACE;ERu2CJ;EQr2CE;IACE;ERu2CJ;EQr2CE;IACE;ERu2CJ;EQr2CE;IACE;ERu2CJ;EQr2CE;IACE;ERu2CJ;EQp2CE;IACE;ERs2CJ;EQp2CE;IACE;ERs2CJ;EQp2CE;IACE;ERs2CJ;EQp2CE;IACE;ERs2CJ;EQp2CE;IACE;ERs2CJ;EQp2CE;IACE;ERs2CJ;AACF;AI95CI;EIlDA;IACE;ERm9CJ;EQj9CE;IACE;ERm9CJ;EQj9CE;IACE;ERm9CJ;EQj9CE;IACE;ERm9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQh9CE;IACE;ERk9CJ;EQ/8CE;IACE;ERi9CJ;EQ/8CE;IACE;ERi9CJ;EQ/8CE;IACE;ERi9CJ;EQ/8CE;IACE;ERi9CJ;EQ/8CE;IACE;ERi9CJ;EQ98CE;IACE;ERg9CJ;EQ98CE;IACE;ERg9CJ;EQ98CE;IACE;ERg9CJ;EQ98CE;IACE;ERg9CJ;EQ98CE;IACE;ERg9CJ;EQ78CE;IACE;ER+8CJ;EQ78CE;IACE;ER+8CJ;EQ78CE;IACE;ER+8CJ;EQ78CE;IACE;ER+8CJ;EQ78CE;IACE;ER+8CJ;EQ78CE;IACE;ER+8CJ;EQ58CE;IACE;ER88CJ;EQ58CE;IACE;ER88CJ;EQ58CE;IACE;ER88CJ;EQ58CE;IACE;ER88CJ;EQ58CE;IACE;ER88CJ;EQ58CE;IACE;ER88CJ;AACF;ASxjDQ;EACE;AT0jDV;;ASxjDQ;;EAEE;AT2jDV;;ASzjDQ;;EAEE;AT4jDV;;AS1jDQ;;EAEE;AT6jDV;;AS3jDQ;;EAEE;AT8jDV;;AS/kDQ;EACE;ATklDV;;AShlDQ;;EAEE;ATmlDV;;ASjlDQ;;EAEE;ATolDV;;ASllDQ;;EAEE;ATqlDV;;ASnlDQ;;EAEE;ATslDV;;ASvmDQ;EACE;AT0mDV;;ASxmDQ;;EAEE;AT2mDV;;ASzmDQ;;EAEE;AT4mDV;;AS1mDQ;;EAEE;AT6mDV;;AS3mDQ;;EAEE;AT8mDV;;AS/nDQ;EACE;ATkoDV;;AShoDQ;;EAEE;ATmoDV;;ASjoDQ;;EAEE;ATooDV;;ASloDQ;;EAEE;ATqoDV;;ASnoDQ;;EAEE;ATsoDV;;ASvpDQ;EACE;AT0pDV;;ASxpDQ;;EAEE;AT2pDV;;ASzpDQ;;EAEE;AT4pDV;;AS1pDQ;;EAEE;AT6pDV;;AS3pDQ;;EAEE;AT8pDV;;AS/qDQ;EACE;ATkrDV;;AShrDQ;;EAEE;ATmrDV;;ASjrDQ;;EAEE;ATorDV;;ASlrDQ;;EAEE;ATqrDV;;ASnrDQ;;EAEE;ATsrDV;;ASvsDQ;EACE;AT0sDV;;ASxsDQ;;EAEE;AT2sDV;;ASzsDQ;;EAEE;AT4sDV;;AS1sDQ;;EAEE;AT6sDV;;AS3sDQ;;EAEE;AT8sDV;;AS/tDQ;EACE;ATkuDV;;AShuDQ;;EAEE;ATmuDV;;ASjuDQ;;EAEE;ATouDV;;ASluDQ;;EAEE;ATquDV;;ASnuDQ;;EAEE;ATsuDV;;ASvvDQ;EACE;AT0vDV;;ASxvDQ;;EAEE;AT2vDV;;ASzvDQ;;EAEE;AT4vDV;;AS1vDQ;;EAEE;AT6vDV;;AS3vDQ;;EAEE;AT8vDV;;AS/wDQ;EACE;ATkxDV;;AShxDQ;;EAEE;ATmxDV;;ASjxDQ;;EAEE;AToxDV;;ASlxDQ;;EAEE;ATqxDV;;ASnxDQ;;EAEE;ATsxDV;;ASvyDQ;EACE;AT0yDV;;ASxyDQ;;EAEE;AT2yDV;;ASzyDQ;;EAEE;AT4yDV;;AS1yDQ;;EAEE;AT6yDV;;AS3yDQ;;EAEE;AT8yDV;;AS/zDQ;EACE;ATk0DV;;ASh0DQ;;EAEE;ATm0DV;;ASj0DQ;;EAEE;ATo0DV;;ASl0DQ;;EAEE;ATq0DV;;ASn0DQ;;EAEE;ATs0DV;;AS9zDQ;EACE;ATi0DV;;AS/zDQ;;EAEE;ATk0DV;;ASh0DQ;;EAEE;ATm0DV;;ASj0DQ;;EAEE;ATo0DV;;ASl0DQ;;EAEE;ATq0DV;;ASt1DQ;EACE;ATy1DV;;ASv1DQ;;EAEE;AT01DV;;ASx1DQ;;EAEE;AT21DV;;ASz1DQ;;EAEE;AT41DV;;AS11DQ;;EAEE;AT61DV;;AS92DQ;EACE;ATi3DV;;AS/2DQ;;EAEE;ATk3DV;;ASh3DQ;;EAEE;ATm3DV;;ASj3DQ;;EAEE;ATo3DV;;ASl3DQ;;EAEE;ATq3DV;;ASt4DQ;EACE;ATy4DV;;ASv4DQ;;EAEE;AT04DV;;ASx4DQ;;EAEE;AT24DV;;ASz4DQ;;EAEE;AT44DV;;AS14DQ;;EAEE;AT64DV;;AS95DQ;EACE;ATi6DV;;AS/5DQ;;EAEE;ATk6DV;;ASh6DQ;;EAEE;ATm6DV;;ASj6DQ;;EAEE;ATo6DV;;ASl6DQ;;EAEE;ATq6DV;;AS/5DI;EACE;ATk6DN;;ASh6DI;;EAEE;ATm6DN;;ASj6DI;;EAEE;ATo6DN;;ASl6DI;;EAEE;ATq6DN;;ASn6DI;;EAEE;ATs6DN;;AIr7DI;EKlDI;IACE;ET2+DR;ESz+DM;;IAEE;ET2+DR;ESz+DM;;IAEE;ET2+DR;ESz+DM;;IAEE;ET2+DR;ESz+DM;;IAEE;ET2+DR;ES5/DM;IACE;ET8/DR;ES5/DM;;IAEE;ET8/DR;ES5/DM;;IAEE;ET8/DR;ES5/DM;;IAEE;ET8/DR;ES5/DM;;IAEE;ET8/DR;ES/gEM;IACE;ETihER;ES/gEM;;IAEE;ETihER;ES/gEM;;IAEE;ETihER;ES/gEM;;IAEE;ETihER;ES/gEM;;IAEE;ETihER;ESliEM;IACE;EToiER;ESliEM;;IAEE;EToiER;ESliEM;;IAEE;EToiER;ESliEM;;IAEE;EToiER;ESliEM;;IAEE;EToiER;ESrjEM;IACE;ETujER;ESrjEM;;IAEE;ETujER;ESrjEM;;IAEE;ETujER;ESrjEM;;IAEE;ETujER;ESrjEM;;IAEE;ETujER;ESxkEM;IACE;ET0kER;ESxkEM;;IAEE;ET0kER;ESxkEM;;IAEE;ET0kER;ESxkEM;;IAEE;ET0kER;ESxkEM;;IAEE;ET0kER;ES3lEM;IACE;ET6lER;ES3lEM;;IAEE;ET6lER;ES3lEM;;IAEE;ET6lER;ES3lEM;;IAEE;ET6lER;ES3lEM;;IAEE;ET6lER;ES9mEM;IACE;ETgnER;ES9mEM;;IAEE;ETgnER;ES9mEM;;IAEE;ETgnER;ES9mEM;;IAEE;ETgnER;ES9mEM;;IAEE;ETgnER;ESjoEM;IACE;ETmoER;ESjoEM;;IAEE;ETmoER;ESjoEM;;IAEE;ETmoER;ESjoEM;;IAEE;ETmoER;ESjoEM;;IAEE;ETmoER;ESppEM;IACE;ETspER;ESppEM;;IAEE;ETspER;ESppEM;;IAEE;ETspER;ESppEM;;IAEE;ETspER;ESppEM;;IAEE;ETspER;ESvqEM;IACE;ETyqER;ESvqEM;;IAEE;ETyqER;ESvqEM;;IAEE;ETyqER;ESvqEM;;IAEE;ETyqER;ESvqEM;;IAEE;ETyqER;ES1rEM;IACE;ET4rER;ES1rEM;;IAEE;ET4rER;ES1rEM;;IAEE;ET4rER;ES1rEM;;IAEE;ET4rER;ES1rEM;;IAEE;ET4rER;ESprEM;IACE;ETsrER;ESprEM;;IAEE;ETsrER;ESprEM;;IAEE;ETsrER;ESprEM;;IAEE;ETsrER;ESprEM;;IAEE;ETsrER;ESvsEM;IACE;ETysER;ESvsEM;;IAEE;ETysER;ESvsEM;;IAEE;ETysER;ESvsEM;;IAEE;ETysER;ESvsEM;;IAEE;ETysER;ES1tEM;IACE;ET4tER;ES1tEM;;IAEE;ET4tER;ES1tEM;;IAEE;ET4tER;ES1tEM;;IAEE;ET4tER;ES1tEM;;IAEE;ET4tER;ES7uEM;IACE;ET+uER;ES7uEM;;IAEE;ET+uER;ES7uEM;;IAEE;ET+uER;ES7uEM;;IAEE;ET+uER;ES7uEM;;IAEE;ET+uER;EShwEM;IACE;ETkwER;EShwEM;;IAEE;ETkwER;EShwEM;;IAEE;ETkwER;EShwEM;;IAEE;ETkwER;EShwEM;;IAEE;ETkwER;ES5vEE;IACE;ET8vEJ;ES5vEE;;IAEE;ET8vEJ;ES5vEE;;IAEE;ET8vEJ;ES5vEE;;IAEE;ET8vEJ;ES5vEE;;IAEE;ET8vEJ;AACF;AI9wEI;EKlDI;IACE;ETm0ER;ESj0EM;;IAEE;ETm0ER;ESj0EM;;IAEE;ETm0ER;ESj0EM;;IAEE;ETm0ER;ESj0EM;;IAEE;ETm0ER;ESp1EM;IACE;ETs1ER;ESp1EM;;IAEE;ETs1ER;ESp1EM;;IAEE;ETs1ER;ESp1EM;;IAEE;ETs1ER;ESp1EM;;IAEE;ETs1ER;ESv2EM;IACE;ETy2ER;ESv2EM;;IAEE;ETy2ER;ESv2EM;;IAEE;ETy2ER;ESv2EM;;IAEE;ETy2ER;ESv2EM;;IAEE;ETy2ER;ES13EM;IACE;ET43ER;ES13EM;;IAEE;ET43ER;ES13EM;;IAEE;ET43ER;ES13EM;;IAEE;ET43ER;ES13EM;;IAEE;ET43ER;ES74EM;IACE;ET+4ER;ES74EM;;IAEE;ET+4ER;ES74EM;;IAEE;ET+4ER;ES74EM;;IAEE;ET+4ER;ES74EM;;IAEE;ET+4ER;ESh6EM;IACE;ETk6ER;ESh6EM;;IAEE;ETk6ER;ESh6EM;;IAEE;ETk6ER;ESh6EM;;IAEE;ETk6ER;ESh6EM;;IAEE;ETk6ER;ESn7EM;IACE;ETq7ER;ESn7EM;;IAEE;ETq7ER;ESn7EM;;IAEE;ETq7ER;ESn7EM;;IAEE;ETq7ER;ESn7EM;;IAEE;ETq7ER;ESt8EM;IACE;ETw8ER;ESt8EM;;IAEE;ETw8ER;ESt8EM;;IAEE;ETw8ER;ESt8EM;;IAEE;ETw8ER;ESt8EM;;IAEE;ETw8ER;ESz9EM;IACE;ET29ER;ESz9EM;;IAEE;ET29ER;ESz9EM;;IAEE;ET29ER;ESz9EM;;IAEE;ET29ER;ESz9EM;;IAEE;ET29ER;ES5+EM;IACE;ET8+ER;ES5+EM;;IAEE;ET8+ER;ES5+EM;;IAEE;ET8+ER;ES5+EM;;IAEE;ET8+ER;ES5+EM;;IAEE;ET8+ER;ES//EM;IACE;ETigFR;ES//EM;;IAEE;ETigFR;ES//EM;;IAEE;ETigFR;ES//EM;;IAEE;ETigFR;ES//EM;;IAEE;ETigFR;ESlhFM;IACE;ETohFR;ESlhFM;;IAEE;ETohFR;ESlhFM;;IAEE;ETohFR;ESlhFM;;IAEE;ETohFR;ESlhFM;;IAEE;ETohFR;ES5gFM;IACE;ET8gFR;ES5gFM;;IAEE;ET8gFR;ES5gFM;;IAEE;ET8gFR;ES5gFM;;IAEE;ET8gFR;ES5gFM;;IAEE;ET8gFR;ES/hFM;IACE;ETiiFR;ES/hFM;;IAEE;ETiiFR;ES/hFM;;IAEE;ETiiFR;ES/hFM;;IAEE;ETiiFR;ES/hFM;;IAEE;ETiiFR;ESljFM;IACE;ETojFR;ESljFM;;IAEE;ETojFR;ESljFM;;IAEE;ETojFR;ESljFM;;IAEE;ETojFR;ESljFM;;IAEE;ETojFR;ESrkFM;IACE;ETukFR;ESrkFM;;IAEE;ETukFR;ESrkFM;;IAEE;ETukFR;ESrkFM;;IAEE;ETukFR;ESrkFM;;IAEE;ETukFR;ESxlFM;IACE;ET0lFR;ESxlFM;;IAEE;ET0lFR;ESxlFM;;IAEE;ET0lFR;ESxlFM;;IAEE;ET0lFR;ESxlFM;;IAEE;ET0lFR;ESplFE;IACE;ETslFJ;ESplFE;;IAEE;ETslFJ;ESplFE;;IAEE;ETslFJ;ESplFE;;IAEE;ETslFJ;ESplFE;;IAEE;ETslFJ;AACF;AItmFI;EKlDI;IACE;ET2pFR;ESzpFM;;IAEE;ET2pFR;ESzpFM;;IAEE;ET2pFR;ESzpFM;;IAEE;ET2pFR;ESzpFM;;IAEE;ET2pFR;ES5qFM;IACE;ET8qFR;ES5qFM;;IAEE;ET8qFR;ES5qFM;;IAEE;ET8qFR;ES5qFM;;IAEE;ET8qFR;ES5qFM;;IAEE;ET8qFR;ES/rFM;IACE;ETisFR;ES/rFM;;IAEE;ETisFR;ES/rFM;;IAEE;ETisFR;ES/rFM;;IAEE;ETisFR;ES/rFM;;IAEE;ETisFR;ESltFM;IACE;ETotFR;ESltFM;;IAEE;ETotFR;ESltFM;;IAEE;ETotFR;ESltFM;;IAEE;ETotFR;ESltFM;;IAEE;ETotFR;ESruFM;IACE;ETuuFR;ESruFM;;IAEE;ETuuFR;ESruFM;;IAEE;ETuuFR;ESruFM;;IAEE;ETuuFR;ESruFM;;IAEE;ETuuFR;ESxvFM;IACE;ET0vFR;ESxvFM;;IAEE;ET0vFR;ESxvFM;;IAEE;ET0vFR;ESxvFM;;IAEE;ET0vFR;ESxvFM;;IAEE;ET0vFR;ES3wFM;IACE;ET6wFR;ES3wFM;;IAEE;ET6wFR;ES3wFM;;IAEE;ET6wFR;ES3wFM;;IAEE;ET6wFR;ES3wFM;;IAEE;ET6wFR;ES9xFM;IACE;ETgyFR;ES9xFM;;IAEE;ETgyFR;ES9xFM;;IAEE;ETgyFR;ES9xFM;;IAEE;ETgyFR;ES9xFM;;IAEE;ETgyFR;ESjzFM;IACE;ETmzFR;ESjzFM;;IAEE;ETmzFR;ESjzFM;;IAEE;ETmzFR;ESjzFM;;IAEE;ETmzFR;ESjzFM;;IAEE;ETmzFR;ESp0FM;IACE;ETs0FR;ESp0FM;;IAEE;ETs0FR;ESp0FM;;IAEE;ETs0FR;ESp0FM;;IAEE;ETs0FR;ESp0FM;;IAEE;ETs0FR;ESv1FM;IACE;ETy1FR;ESv1FM;;IAEE;ETy1FR;ESv1FM;;IAEE;ETy1FR;ESv1FM;;IAEE;ETy1FR;ESv1FM;;IAEE;ETy1FR;ES12FM;IACE;ET42FR;ES12FM;;IAEE;ET42FR;ES12FM;;IAEE;ET42FR;ES12FM;;IAEE;ET42FR;ES12FM;;IAEE;ET42FR;ESp2FM;IACE;ETs2FR;ESp2FM;;IAEE;ETs2FR;ESp2FM;;IAEE;ETs2FR;ESp2FM;;IAEE;ETs2FR;ESp2FM;;IAEE;ETs2FR;ESv3FM;IACE;ETy3FR;ESv3FM;;IAEE;ETy3FR;ESv3FM;;IAEE;ETy3FR;ESv3FM;;IAEE;ETy3FR;ESv3FM;;IAEE;ETy3FR;ES14FM;IACE;ET44FR;ES14FM;;IAEE;ET44FR;ES14FM;;IAEE;ET44FR;ES14FM;;IAEE;ET44FR;ES14FM;;IAEE;ET44FR;ES75FM;IACE;ET+5FR;ES75FM;;IAEE;ET+5FR;ES75FM;;IAEE;ET+5FR;ES75FM;;IAEE;ET+5FR;ES75FM;;IAEE;ET+5FR;ESh7FM;IACE;ETk7FR;ESh7FM;;IAEE;ETk7FR;ESh7FM;;IAEE;ETk7FR;ESh7FM;;IAEE;ETk7FR;ESh7FM;;IAEE;ETk7FR;ES56FE;IACE;ET86FJ;ES56FE;;IAEE;ET86FJ;ES56FE;;IAEE;ET86FJ;ES56FE;;IAEE;ET86FJ;ES56FE;;IAEE;ET86FJ;AACF;AI97FI;EKlDI;IACE;ETm/FR;ESj/FM;;IAEE;ETm/FR;ESj/FM;;IAEE;ETm/FR;ESj/FM;;IAEE;ETm/FR;ESj/FM;;IAEE;ETm/FR;ESpgGM;IACE;ETsgGR;ESpgGM;;IAEE;ETsgGR;ESpgGM;;IAEE;ETsgGR;ESpgGM;;IAEE;ETsgGR;ESpgGM;;IAEE;ETsgGR;ESvhGM;IACE;ETyhGR;ESvhGM;;IAEE;ETyhGR;ESvhGM;;IAEE;ETyhGR;ESvhGM;;IAEE;ETyhGR;ESvhGM;;IAEE;ETyhGR;ES1iGM;IACE;ET4iGR;ES1iGM;;IAEE;ET4iGR;ES1iGM;;IAEE;ET4iGR;ES1iGM;;IAEE;ET4iGR;ES1iGM;;IAEE;ET4iGR;ES7jGM;IACE;ET+jGR;ES7jGM;;IAEE;ET+jGR;ES7jGM;;IAEE;ET+jGR;ES7jGM;;IAEE;ET+jGR;ES7jGM;;IAEE;ET+jGR;EShlGM;IACE;ETklGR;EShlGM;;IAEE;ETklGR;EShlGM;;IAEE;ETklGR;EShlGM;;IAEE;ETklGR;EShlGM;;IAEE;ETklGR;ESnmGM;IACE;ETqmGR;ESnmGM;;IAEE;ETqmGR;ESnmGM;;IAEE;ETqmGR;ESnmGM;;IAEE;ETqmGR;ESnmGM;;IAEE;ETqmGR;EStnGM;IACE;ETwnGR;EStnGM;;IAEE;ETwnGR;EStnGM;;IAEE;ETwnGR;EStnGM;;IAEE;ETwnGR;EStnGM;;IAEE;ETwnGR;ESzoGM;IACE;ET2oGR;ESzoGM;;IAEE;ET2oGR;ESzoGM;;IAEE;ET2oGR;ESzoGM;;IAEE;ET2oGR;ESzoGM;;IAEE;ET2oGR;ES5pGM;IACE;ET8pGR;ES5pGM;;IAEE;ET8pGR;ES5pGM;;IAEE;ET8pGR;ES5pGM;;IAEE;ET8pGR;ES5pGM;;IAEE;ET8pGR;ES/qGM;IACE;ETirGR;ES/qGM;;IAEE;ETirGR;ES/qGM;;IAEE;ETirGR;ES/qGM;;IAEE;ETirGR;ES/qGM;;IAEE;ETirGR;ESlsGM;IACE;ETosGR;ESlsGM;;IAEE;ETosGR;ESlsGM;;IAEE;ETosGR;ESlsGM;;IAEE;ETosGR;ESlsGM;;IAEE;ETosGR;ES5rGM;IACE;ET8rGR;ES5rGM;;IAEE;ET8rGR;ES5rGM;;IAEE;ET8rGR;ES5rGM;;IAEE;ET8rGR;ES5rGM;;IAEE;ET8rGR;ES/sGM;IACE;ETitGR;ES/sGM;;IAEE;ETitGR;ES/sGM;;IAEE;ETitGR;ES/sGM;;IAEE;ETitGR;ES/sGM;;IAEE;ETitGR;ESluGM;IACE;ETouGR;ESluGM;;IAEE;ETouGR;ESluGM;;IAEE;ETouGR;ESluGM;;IAEE;ETouGR;ESluGM;;IAEE;ETouGR;ESrvGM;IACE;ETuvGR;ESrvGM;;IAEE;ETuvGR;ESrvGM;;IAEE;ETuvGR;ESrvGM;;IAEE;ETuvGR;ESrvGM;;IAEE;ETuvGR;ESxwGM;IACE;ET0wGR;ESxwGM;;IAEE;ET0wGR;ESxwGM;;IAEE;ET0wGR;ESxwGM;;IAEE;ET0wGR;ESxwGM;;IAEE;ET0wGR;ESpwGE;IACE;ETswGJ;ESpwGE;;IAEE;ETswGJ;ESpwGE;;IAEE;ETswGJ;ESpwGE;;IAEE;ETswGJ;ESpwGE;;IAEE;ETswGJ;AACF;AU70GA;EACE;AV+0GF;AU90GE;EAFF;IAGI;EVi1GF;AACF;;AU90GA;EACE;AVi1GF;AUh1GE;EAFF;IAGI;EVm1GF;AACF;;AWn2GA;EACE;EACA;EACA;AXs2GF;AWn2GA;EACE;EACA;EACA;AXq2GF;AWl2GA;EACE;EACA;EACA;AXo2GF;AWj2GA;EACE;EACA;EACA;AXm2GF;AUn3GA;EACE;AVq3GF;AUp3GE;EAFF;IAGI;EVu3GF;AACF;;AUp3GA;EACE;AVu3GF;AUt3GE;EAFF;IAGI;EVy3GF;AACF;;AYz4GA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AZ44GF;;AYz4GA;AAEA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AZ24GF;;AYx4GA;EACE;EACA;EACA;AZ24GF;;AYx4GA;;;;EAAA;AAKA;;EAEE;EAGA;AZ24GF;;AYz4GA;EACE;EACA;EACA;AZ44GF;;AY14GA;EACE;AZ64GF;;AY34GA;EACE;EACA;EACA;EACA;AZ84GF;;AY54GA;EACE;EACA;AZ+4GF;;AY74GA;EACE;EACA;EACA;EACA;EACA;EACA;EAIA;AZg5GF;;AY94GA;EACE;EACA;AZi5GF;;AY/4GA;EACE;EACA;EACA;AZk5GF;;AYh5GA;EACE;EACA;AZm5GF;;AYj5GA;EACE;EACA;EACA;AZo5GF;;AYl5GA;EACE;EACA;AZq5GF;;AYn5GA;EACE;EACA;EACA;AZs5GF;;AYp5GA;;GAAA;AA6CA;EACE;IAIE;EZu5GF;EYr5GA;IAIE;EZu5GF;EYr5GA;IAIE;EZu5GF;AACF;AA5/GA;;;;;;;;;;;;;;;;;EAiBE;AA8/GF;;AA3/GA;EACE;AA8/GF;;AA3/GA;EACE;AA8/GF;;AA1/GE;EACE;EACA;AA6/GJ;;AAz/GA;AACA;EACE;AA4/GF;;AAz/GA;EACE;AA4/GF;;AAz/GA;EACE,yBalEO;Ab8jHT;;AAr/GA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EAIA;AAq/GF;AAx/GE;EARF;IASI;EA2/GF;AACF;;AAv/GA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EAIA;AAu/GF;AA1/GE;EARF;IASI;EA6/GF;AACF;;AAr/GA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAw/GF;AAt/GE;EACE;EACA;AAw/GJ;AAr/GE;EACE;EACA;EACA;EACA;AAu/GJ;;AAp/GA;EACE;EACA;AAu/GF;;AAp/GA;EACE;EACA,yBa5IO;Eb6IP;EACA;EACA;EACA;AAu/GF;;AAp/GA;EACE;EACA;EACA;AAu/GF;;AAj/GA;EACE,catLW;EbuLX;EACA;EACA;EACA;EACA;AAo/GF;AAl/GE;EARF;IASI;EAq/GF;AACF;;AAj/GA;EACE;EACA;EACA;EACA;EAEA;EAKA;EAKA;EAOA;AAq+GF;AAr/GE;EACE;AAu/GJ;AAn/GE;EACE;AAq/GJ;AAh/GI;EADF;IAEI,mBa7LG;EbgrHP;AACF;AA/+GE;EACE;EACA,mBa5LM;Ab6qHV;;AA5+GA;EACE;EACA;AA++GF;;AAz+GA;EACE;EACA;EACA;EACA;EACA;EACA;EAGA;AA4+GF;AA1+GE;EAGE;EACA;AA4+GJ;AA1+GE;EACE;EACA,canPO;EboPP;EACA;EACA;AA4+GJ;;AAx+GA;EACE;EACA;EACA;EACA;AA2+GF;;AAx+GA;EACE;AA2+GF;;AAx+GA;EACE;AA2+GF;;AAx+GA;EACE;EACA;EACA;AA2+GF;;AAx+GA;EACE;EACA;EACA,ca5RW;Eb6RX;AA2+GF;AAz+GE;EACE;EACA,cavRO;EbwRP;AA2+GJ;;AAt+GA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AAy+GF;AAv+GE;EACE;AAy+GJ;AAt+GE;EACE;EACA;AAw+GJ,C", "sources": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./src/styles.scss", "./src/styles/bootstrap/bootstrap-grid.scss", "./src/styles/bootstrap/_grid.scss", "./src/styles/bootstrap/mixins/_grid.scss", "./src/styles/bootstrap/mixins/_breakpoints.scss", "./src/styles/bootstrap/_variables.scss", "./src/styles/bootstrap/mixins/_grid-framework.scss", "./src/styles/bootstrap/utilities/_display.scss", "./src/styles/bootstrap/utilities/_flex.scss", "./src/styles/bootstrap/utilities/_spacing.scss", "./src/styles/cashless-breakpoints.scss", "./src/styles/cashless-font.scss", "./src/styles/spinner.scss", "./src/styles/cashless-theme.scss"], "sourcesContent": [".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0)}.cdk-high-contrast-active .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\"\"}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus::before{content:\"\"}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-ripple-element{background-color:rgba(0,0,0,.1)}html{--mat-option-selected-state-label-text-color:#3f51b5;--mat-option-label-text-color:rgba(0, 0, 0, 0.87);--mat-option-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-option-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-option-selected-state-layer-color:rgba(0, 0, 0, 0.04)}.mat-accent{--mat-option-selected-state-label-text-color:#ff4081}.mat-warn{--mat-option-selected-state-label-text-color:#f44336}html{--mat-optgroup-label-text-color:rgba(0, 0, 0, 0.87)}.mat-pseudo-checkbox-full{color:rgba(0,0,0,.54)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{color:#b0b0b0}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#3f51b5}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#3f51b5}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#ff4081}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#ff4081}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#ff4081}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#ff4081}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#f44336}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#f44336}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#b0b0b0}.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#b0b0b0}.mat-app-background{background-color:#fafafa;color:rgba(0,0,0,.87)}.mat-elevation-z0,.mat-mdc-elevation-specific.mat-elevation-z0{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z1,.mat-mdc-elevation-specific.mat-elevation-z1{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z2,.mat-mdc-elevation-specific.mat-elevation-z2{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z3,.mat-mdc-elevation-specific.mat-elevation-z3{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z4,.mat-mdc-elevation-specific.mat-elevation-z4{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z5,.mat-mdc-elevation-specific.mat-elevation-z5{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z6,.mat-mdc-elevation-specific.mat-elevation-z6{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z7,.mat-mdc-elevation-specific.mat-elevation-z7{box-shadow:0px 4px 5px -2px rgba(0, 0, 0, 0.2), 0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12)}.mat-elevation-z8,.mat-mdc-elevation-specific.mat-elevation-z8{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z9,.mat-mdc-elevation-specific.mat-elevation-z9{box-shadow:0px 5px 6px -3px rgba(0, 0, 0, 0.2), 0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z10,.mat-mdc-elevation-specific.mat-elevation-z10{box-shadow:0px 6px 6px -3px rgba(0, 0, 0, 0.2), 0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z11,.mat-mdc-elevation-specific.mat-elevation-z11{box-shadow:0px 6px 7px -4px rgba(0, 0, 0, 0.2), 0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z12,.mat-mdc-elevation-specific.mat-elevation-z12{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z13,.mat-mdc-elevation-specific.mat-elevation-z13{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z14,.mat-mdc-elevation-specific.mat-elevation-z14{box-shadow:0px 7px 9px -4px rgba(0, 0, 0, 0.2), 0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z15,.mat-mdc-elevation-specific.mat-elevation-z15{box-shadow:0px 8px 9px -5px rgba(0, 0, 0, 0.2), 0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z16,.mat-mdc-elevation-specific.mat-elevation-z16{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z17,.mat-mdc-elevation-specific.mat-elevation-z17{box-shadow:0px 8px 11px -5px rgba(0, 0, 0, 0.2), 0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z18,.mat-mdc-elevation-specific.mat-elevation-z18{box-shadow:0px 9px 11px -5px rgba(0, 0, 0, 0.2), 0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z19,.mat-mdc-elevation-specific.mat-elevation-z19{box-shadow:0px 9px 12px -6px rgba(0, 0, 0, 0.2), 0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z20,.mat-mdc-elevation-specific.mat-elevation-z20{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z21,.mat-mdc-elevation-specific.mat-elevation-z21{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z22,.mat-mdc-elevation-specific.mat-elevation-z22{box-shadow:0px 10px 14px -6px rgba(0, 0, 0, 0.2), 0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z23,.mat-mdc-elevation-specific.mat-elevation-z23{box-shadow:0px 11px 14px -7px rgba(0, 0, 0, 0.2), 0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12)}.mat-elevation-z24,.mat-mdc-elevation-specific.mat-elevation-z24{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-theme-loaded-marker{display:none}html{--mat-option-label-text-font:Roboto, sans-serif;--mat-option-label-text-line-height:24px;--mat-option-label-text-size:16px;--mat-option-label-text-tracking:0.03125em;--mat-option-label-text-weight:400}html{--mat-optgroup-label-text-font:Roboto, sans-serif;--mat-optgroup-label-text-line-height:24px;--mat-optgroup-label-text-size:16px;--mat-optgroup-label-text-tracking:0.03125em;--mat-optgroup-label-text-weight:400}.mat-mdc-card{--mdc-elevated-card-container-color:white;--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-color:white;--mdc-outlined-card-outline-color:rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-card-subtitle-text-color:rgba(0, 0, 0, 0.54)}.mat-mdc-card{--mat-card-title-text-font:Roboto, sans-serif;--mat-card-title-text-line-height:32px;--mat-card-title-text-size:20px;--mat-card-title-text-tracking:0.0125em;--mat-card-title-text-weight:500;--mat-card-subtitle-text-font:Roboto, sans-serif;--mat-card-subtitle-text-line-height:22px;--mat-card-subtitle-text-size:14px;--mat-card-subtitle-text-tracking:0.0071428571em;--mat-card-subtitle-text-weight:500}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-color:#3f51b5;--mdc-linear-progress-track-color:rgba(63, 81, 181, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:rgba(63, 81, 181, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(63, 81, 181, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(63, 81, 181, 0.25)'/%3E%3C/svg%3E\")}}.mat-mdc-progress-bar .mdc-linear-progress__buffer-bar{background-color:rgba(63, 81, 181, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(63, 81, 181, 0.25))}.mat-mdc-progress-bar.mat-accent{--mdc-linear-progress-active-indicator-color:#ff4081;--mdc-linear-progress-track-color:rgba(255, 64, 129, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:rgba(255, 64, 129, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(255, 64, 129, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(255, 64, 129, 0.25)'/%3E%3C/svg%3E\")}}.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-bar{background-color:rgba(255, 64, 129, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(255, 64, 129, 0.25))}.mat-mdc-progress-bar.mat-warn{--mdc-linear-progress-active-indicator-color:#f44336;--mdc-linear-progress-track-color:rgba(244, 67, 54, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:rgba(244, 67, 54, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(244, 67, 54, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E\")}}.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-bar{background-color:rgba(244, 67, 54, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(244, 67, 54, 0.25))}.mat-mdc-tooltip{--mdc-plain-tooltip-container-color:#616161;--mdc-plain-tooltip-supporting-text-color:#fff}.mat-mdc-tooltip{--mdc-plain-tooltip-supporting-text-font:Roboto, sans-serif;--mdc-plain-tooltip-supporting-text-size:12px;--mdc-plain-tooltip-supporting-text-weight:400;--mdc-plain-tooltip-supporting-text-tracking:0.0333333333em}html{--mdc-filled-text-field-caret-color:#3f51b5;--mdc-filled-text-field-focus-active-indicator-color:#3f51b5;--mdc-filled-text-field-focus-label-text-color:rgba(63, 81, 181, 0.87);--mdc-filled-text-field-container-color:whitesmoke;--mdc-filled-text-field-disabled-container-color:#fafafa;--mdc-filled-text-field-label-text-color:rgba(0, 0, 0, 0.6);--mdc-filled-text-field-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-text-field-input-text-color:rgba(0, 0, 0, 0.87);--mdc-filled-text-field-disabled-input-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-text-field-input-text-placeholder-color:rgba(0, 0, 0, 0.6);--mdc-filled-text-field-error-focus-label-text-color:#f44336;--mdc-filled-text-field-error-label-text-color:#f44336;--mdc-filled-text-field-error-caret-color:#f44336;--mdc-filled-text-field-active-indicator-color:rgba(0, 0, 0, 0.42);--mdc-filled-text-field-disabled-active-indicator-color:rgba(0, 0, 0, 0.06);--mdc-filled-text-field-hover-active-indicator-color:rgba(0, 0, 0, 0.87);--mdc-filled-text-field-error-active-indicator-color:#f44336;--mdc-filled-text-field-error-focus-active-indicator-color:#f44336;--mdc-filled-text-field-error-hover-active-indicator-color:#f44336;--mdc-outlined-text-field-caret-color:#3f51b5;--mdc-outlined-text-field-focus-outline-color:#3f51b5;--mdc-outlined-text-field-focus-label-text-color:rgba(63, 81, 181, 0.87);--mdc-outlined-text-field-label-text-color:rgba(0, 0, 0, 0.6);--mdc-outlined-text-field-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-input-text-color:rgba(0, 0, 0, 0.87);--mdc-outlined-text-field-disabled-input-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-input-text-placeholder-color:rgba(0, 0, 0, 0.6);--mdc-outlined-text-field-error-caret-color:#f44336;--mdc-outlined-text-field-error-focus-label-text-color:#f44336;--mdc-outlined-text-field-error-label-text-color:#f44336;--mdc-outlined-text-field-outline-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-disabled-outline-color:rgba(0, 0, 0, 0.06);--mdc-outlined-text-field-hover-outline-color:rgba(0, 0, 0, 0.87);--mdc-outlined-text-field-error-focus-outline-color:#f44336;--mdc-outlined-text-field-error-hover-outline-color:#f44336;--mdc-outlined-text-field-error-outline-color:#f44336;--mat-form-field-disabled-input-text-placeholder-color:rgba(0, 0, 0, 0.38)}.mat-mdc-form-field-error{color:var(--mdc-theme-error, #f44336)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{background-color:rgba(0,0,0,.87)}.mat-mdc-form-field:hover .mat-mdc-form-field-focus-overlay{opacity:.04}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:.12}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{color:rgba(0,0,0,.54)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-primary .mat-mdc-form-field-infix::after{color:rgba(63,81,181,.87)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-accent .mat-mdc-form-field-infix::after{color:rgba(255,64,129,.87)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-warn .mat-mdc-form-field-infix::after{color:rgba(244,67,54,.87)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:rgba(0,0,0,.38)}.mat-mdc-form-field.mat-accent{--mdc-filled-text-field-caret-color:#ff4081;--mdc-filled-text-field-focus-active-indicator-color:#ff4081;--mdc-filled-text-field-focus-label-text-color:rgba(255, 64, 129, 0.87);--mdc-outlined-text-field-caret-color:#ff4081;--mdc-outlined-text-field-focus-outline-color:#ff4081;--mdc-outlined-text-field-focus-label-text-color:rgba(255, 64, 129, 0.87)}.mat-mdc-form-field.mat-warn{--mdc-filled-text-field-caret-color:#f44336;--mdc-filled-text-field-focus-active-indicator-color:#f44336;--mdc-filled-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87);--mdc-outlined-text-field-caret-color:#f44336;--mdc-outlined-text-field-focus-outline-color:#f44336;--mdc-outlined-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87)}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:56px}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:28px}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY( -34.75px) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{padding-top:16px;padding-bottom:16px}.mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-form-field-infix{padding-top:24px;padding-bottom:8px}.mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix{padding-top:16px;padding-bottom:16px}html{--mdc-filled-text-field-label-text-font:Roboto, sans-serif;--mdc-filled-text-field-label-text-size:16px;--mdc-filled-text-field-label-text-tracking:0.03125em;--mdc-filled-text-field-label-text-weight:400;--mdc-outlined-text-field-label-text-font:Roboto, sans-serif;--mdc-outlined-text-field-label-text-size:16px;--mdc-outlined-text-field-label-text-tracking:0.03125em;--mdc-outlined-text-field-label-text-weight:400;--mat-form-field-container-text-font:Roboto, sans-serif;--mat-form-field-container-text-line-height:24px;--mat-form-field-container-text-size:16px;--mat-form-field-container-text-tracking:0.03125em;--mat-form-field-container-text-weight:400;--mat-form-field-outlined-label-text-populated-size:16px;--mat-form-field-subscript-text-font:Roboto, sans-serif;--mat-form-field-subscript-text-line-height:20px;--mat-form-field-subscript-text-size:12px;--mat-form-field-subscript-text-tracking:0.0333333333em;--mat-form-field-subscript-text-weight:400}html{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(63, 81, 181, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-accent{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(255, 64, 129, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-warn{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(244, 67, 54, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html{--mat-select-trigger-text-font:Roboto, sans-serif;--mat-select-trigger-text-line-height:24px;--mat-select-trigger-text-size:16px;--mat-select-trigger-text-tracking:0.03125em;--mat-select-trigger-text-weight:400}html{--mat-autocomplete-background-color:white}.mat-mdc-dialog-container{--mdc-dialog-container-color:white;--mdc-dialog-subhead-color:rgba(0, 0, 0, 0.87);--mdc-dialog-supporting-text-color:rgba(0, 0, 0, 0.6)}.mat-mdc-dialog-container{--mdc-dialog-subhead-font:Roboto, sans-serif;--mdc-dialog-subhead-line-height:32px;--mdc-dialog-subhead-size:20px;--mdc-dialog-subhead-weight:500;--mdc-dialog-subhead-tracking:0.0125em;--mdc-dialog-supporting-text-font:Roboto, sans-serif;--mdc-dialog-supporting-text-line-height:24px;--mdc-dialog-supporting-text-size:16px;--mdc-dialog-supporting-text-weight:400;--mdc-dialog-supporting-text-tracking:0.03125em}.mat-mdc-standard-chip{--mdc-chip-disabled-label-text-color:#212121;--mdc-chip-elevated-container-color:#e0e0e0;--mdc-chip-elevated-disabled-container-color:#e0e0e0;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:#212121;--mdc-chip-with-icon-icon-color:#212121;--mdc-chip-with-icon-disabled-icon-color:#212121;--mdc-chip-with-icon-selected-icon-color:#212121;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:#212121;--mdc-chip-with-trailing-icon-trailing-icon-color:#212121}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#3f51b5;--mdc-chip-elevated-disabled-container-color:#3f51b5;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-accent,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-accent{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#ff4081;--mdc-chip-elevated-disabled-container-color:#ff4081;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-warn,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-warn{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#f44336;--mdc-chip-elevated-disabled-container-color:#f44336;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-chip.mat-mdc-standard-chip{--mdc-chip-container-height:32px}.mat-mdc-standard-chip{--mdc-chip-label-text-font:Roboto, sans-serif;--mdc-chip-label-text-line-height:20px;--mdc-chip-label-text-size:14px;--mdc-chip-label-text-tracking:0.0178571429em;--mdc-chip-label-text-weight:400}.mat-mdc-slide-toggle{--mdc-switch-selected-focus-state-layer-color:#3949ab;--mdc-switch-selected-handle-color:#3949ab;--mdc-switch-selected-hover-state-layer-color:#3949ab;--mdc-switch-selected-pressed-state-layer-color:#3949ab;--mdc-switch-selected-focus-handle-color:#1a237e;--mdc-switch-selected-hover-handle-color:#1a237e;--mdc-switch-selected-pressed-handle-color:#1a237e;--mdc-switch-selected-focus-track-color:#7986cb;--mdc-switch-selected-hover-track-color:#7986cb;--mdc-switch-selected-pressed-track-color:#7986cb;--mdc-switch-selected-track-color:#7986cb;--mdc-switch-disabled-selected-handle-color:#424242;--mdc-switch-disabled-selected-icon-color:#fff;--mdc-switch-disabled-selected-track-color:#424242;--mdc-switch-disabled-unselected-handle-color:#424242;--mdc-switch-disabled-unselected-icon-color:#fff;--mdc-switch-disabled-unselected-track-color:#424242;--mdc-switch-handle-surface-color:var(--mdc-theme-surface, #fff);--mdc-switch-handle-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-switch-handle-shadow-color:black;--mdc-switch-disabled-handle-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mdc-switch-selected-icon-color:#fff;--mdc-switch-unselected-focus-handle-color:#212121;--mdc-switch-unselected-focus-state-layer-color:#424242;--mdc-switch-unselected-focus-track-color:#e0e0e0;--mdc-switch-unselected-handle-color:#616161;--mdc-switch-unselected-hover-handle-color:#212121;--mdc-switch-unselected-hover-state-layer-color:#424242;--mdc-switch-unselected-hover-track-color:#e0e0e0;--mdc-switch-unselected-icon-color:#fff;--mdc-switch-unselected-pressed-handle-color:#212121;--mdc-switch-unselected-pressed-state-layer-color:#424242;--mdc-switch-unselected-pressed-track-color:#e0e0e0;--mdc-switch-unselected-track-color:#e0e0e0}.mat-mdc-slide-toggle .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:rgba(0,0,0,.38)}.mat-mdc-slide-toggle.mat-accent{--mdc-switch-selected-focus-state-layer-color:#d81b60;--mdc-switch-selected-handle-color:#d81b60;--mdc-switch-selected-hover-state-layer-color:#d81b60;--mdc-switch-selected-pressed-state-layer-color:#d81b60;--mdc-switch-selected-focus-handle-color:#880e4f;--mdc-switch-selected-hover-handle-color:#880e4f;--mdc-switch-selected-pressed-handle-color:#880e4f;--mdc-switch-selected-focus-track-color:#f06292;--mdc-switch-selected-hover-track-color:#f06292;--mdc-switch-selected-pressed-track-color:#f06292;--mdc-switch-selected-track-color:#f06292}.mat-mdc-slide-toggle.mat-warn{--mdc-switch-selected-focus-state-layer-color:#e53935;--mdc-switch-selected-handle-color:#e53935;--mdc-switch-selected-hover-state-layer-color:#e53935;--mdc-switch-selected-pressed-state-layer-color:#e53935;--mdc-switch-selected-focus-handle-color:#b71c1c;--mdc-switch-selected-hover-handle-color:#b71c1c;--mdc-switch-selected-pressed-handle-color:#b71c1c;--mdc-switch-selected-focus-track-color:#e57373;--mdc-switch-selected-hover-track-color:#e57373;--mdc-switch-selected-pressed-track-color:#e57373;--mdc-switch-selected-track-color:#e57373}.mat-mdc-slide-toggle{--mdc-switch-state-layer-size:48px}.mat-mdc-slide-toggle{--mat-slide-toggle-label-text-font:Roboto, sans-serif;--mat-slide-toggle-label-text-size:14px;--mat-slide-toggle-label-text-tracking:0.0178571429em;--mat-slide-toggle-label-text-line-height:20px;--mat-slide-toggle-label-text-weight:400}.mat-mdc-slide-toggle .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit)}.mat-mdc-radio-button .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-radio-button.mat-primary{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#3f51b5;--mdc-radio-selected-hover-icon-color:#3f51b5;--mdc-radio-selected-icon-color:#3f51b5;--mdc-radio-selected-pressed-icon-color:#3f51b5;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#3f51b5;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button.mat-accent{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#ff4081;--mdc-radio-selected-hover-icon-color:#ff4081;--mdc-radio-selected-icon-color:#ff4081;--mdc-radio-selected-pressed-icon-color:#ff4081;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#ff4081;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button.mat-warn{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#f44336;--mdc-radio-selected-hover-icon-color:#f44336;--mdc-radio-selected-icon-color:#f44336;--mdc-radio-selected-pressed-icon-color:#f44336;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#f44336;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button .mdc-radio{--mdc-radio-state-layer-size:40px}.mat-mdc-radio-button .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-body2-font-size, 14px);line-height:var(--mdc-typography-body2-line-height, 20px);font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:var(--mdc-typography-body2-text-transform, none)}.mat-mdc-slider{--mdc-slider-label-container-color:black;--mdc-slider-label-label-text-color:white;--mdc-slider-disabled-handle-color:#000;--mdc-slider-disabled-active-track-color:#000;--mdc-slider-disabled-inactive-track-color:#000;--mdc-slider-with-tick-marks-disabled-container-color:#000;--mat-mdc-slider-value-indicator-opacity: 0.6}.mat-mdc-slider.mat-primary{--mdc-slider-handle-color:#3f51b5;--mdc-slider-focus-handle-color:#3f51b5;--mdc-slider-hover-handle-color:#3f51b5;--mdc-slider-active-track-color:#3f51b5;--mdc-slider-inactive-track-color:#3f51b5;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#3f51b5;--mat-mdc-slider-ripple-color: #3f51b5;--mat-mdc-slider-hover-ripple-color: rgba(63, 81, 181, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(63, 81, 181, 0.2)}.mat-mdc-slider.mat-accent{--mdc-slider-handle-color:#ff4081;--mdc-slider-focus-handle-color:#ff4081;--mdc-slider-hover-handle-color:#ff4081;--mdc-slider-active-track-color:#ff4081;--mdc-slider-inactive-track-color:#ff4081;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#ff4081;--mat-mdc-slider-ripple-color: #ff4081;--mat-mdc-slider-hover-ripple-color: rgba(255, 64, 129, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(255, 64, 129, 0.2)}.mat-mdc-slider.mat-warn{--mdc-slider-handle-color:#f44336;--mdc-slider-focus-handle-color:#f44336;--mdc-slider-hover-handle-color:#f44336;--mdc-slider-active-track-color:#f44336;--mdc-slider-inactive-track-color:#f44336;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#f44336;--mat-mdc-slider-ripple-color: #f44336;--mat-mdc-slider-hover-ripple-color: rgba(244, 67, 54, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(244, 67, 54, 0.2)}.mat-mdc-slider{--mdc-slider-label-label-text-font:Roboto, sans-serif;--mdc-slider-label-label-text-size:14px;--mdc-slider-label-label-text-line-height:22px;--mdc-slider-label-label-text-tracking:0.0071428571em;--mdc-slider-label-label-text-weight:500}html{--mat-menu-item-label-text-color:rgba(0, 0, 0, 0.87);--mat-menu-item-icon-color:rgba(0, 0, 0, 0.87);--mat-menu-item-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-menu-item-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-menu-container-color:white}html{--mat-menu-item-label-text-font:Roboto, sans-serif;--mat-menu-item-label-text-size:16px;--mat-menu-item-label-text-tracking:0.03125em;--mat-menu-item-label-text-line-height:24px;--mat-menu-item-label-text-weight:400}.mat-mdc-list-base{--mdc-list-list-item-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-supporting-text-color:rgba(0, 0, 0, 0.54);--mdc-list-list-item-leading-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-trailing-supporting-text-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-selected-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-disabled-label-text-color:black;--mdc-list-list-item-disabled-leading-icon-color:black;--mdc-list-list-item-disabled-trailing-icon-color:black;--mdc-list-list-item-hover-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-hover-leading-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-hover-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-focus-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-hover-state-layer-color:black;--mdc-list-list-item-hover-state-layer-opacity:0.04;--mdc-list-list-item-focus-state-layer-color:black;--mdc-list-list-item-focus-state-layer-opacity:0.12}.mdc-list-item__start,.mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#3f51b5;--mdc-radio-selected-hover-icon-color:#3f51b5;--mdc-radio-selected-icon-color:#3f51b5;--mdc-radio-selected-pressed-icon-color:#3f51b5}.mat-accent .mdc-list-item__start,.mat-accent .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#ff4081;--mdc-radio-selected-hover-icon-color:#ff4081;--mdc-radio-selected-icon-color:#ff4081;--mdc-radio-selected-pressed-icon-color:#ff4081}.mat-warn .mdc-list-item__start,.mat-warn .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#f44336;--mdc-radio-selected-hover-icon-color:#f44336;--mdc-radio-selected-icon-color:#f44336;--mdc-radio-selected-pressed-icon-color:#f44336}.mat-mdc-list-option{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#3f51b5;--mdc-checkbox-selected-hover-icon-color:#3f51b5;--mdc-checkbox-selected-icon-color:#3f51b5;--mdc-checkbox-selected-pressed-icon-color:#3f51b5;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#3f51b5;--mdc-checkbox-selected-hover-state-layer-color:#3f51b5;--mdc-checkbox-selected-pressed-state-layer-color:#3f51b5;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-option.mat-accent{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#ff4081;--mdc-checkbox-selected-hover-icon-color:#ff4081;--mdc-checkbox-selected-icon-color:#ff4081;--mdc-checkbox-selected-pressed-icon-color:#ff4081;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#ff4081;--mdc-checkbox-selected-hover-state-layer-color:#ff4081;--mdc-checkbox-selected-pressed-state-layer-color:#ff4081;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-option.mat-warn{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#f44336;--mdc-checkbox-selected-hover-icon-color:#f44336;--mdc-checkbox-selected-icon-color:#f44336;--mdc-checkbox-selected-pressed-icon-color:#f44336;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#f44336;--mdc-checkbox-selected-hover-state-layer-color:#f44336;--mdc-checkbox-selected-pressed-state-layer-color:#f44336;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__primary-text{color:#3f51b5}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected.mdc-list-item--with-leading-icon .mdc-list-item__start,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated.mdc-list-item--with-leading-icon .mdc-list-item__start{color:#3f51b5}.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__content,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mat-mdc-list-base{--mdc-list-list-item-one-line-container-height:48px;--mdc-list-list-item-two-line-container-height:64px;--mdc-list-list-item-three-line-container-height:88px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line{height:56px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines{height:72px}.mat-mdc-list-base{--mdc-list-list-item-label-text-font:Roboto, sans-serif;--mdc-list-list-item-label-text-line-height:24px;--mdc-list-list-item-label-text-size:16px;--mdc-list-list-item-label-text-tracking:0.03125em;--mdc-list-list-item-label-text-weight:400;--mdc-list-list-item-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-supporting-text-line-height:20px;--mdc-list-list-item-supporting-text-size:14px;--mdc-list-list-item-supporting-text-tracking:0.0178571429em;--mdc-list-list-item-supporting-text-weight:400;--mdc-list-list-item-trailing-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-trailing-supporting-text-line-height:20px;--mdc-list-list-item-trailing-supporting-text-size:12px;--mdc-list-list-item-trailing-supporting-text-tracking:0.0333333333em;--mdc-list-list-item-trailing-supporting-text-weight:400}.mdc-list-group__subheader{font-size:16px;font-weight:400;line-height:28px;font-family:Roboto, sans-serif;letter-spacing:.009375em}html{--mat-paginator-container-text-color:rgba(0, 0, 0, 0.87);--mat-paginator-container-background-color:white;--mat-paginator-enabled-icon-color:rgba(0, 0, 0, 0.54);--mat-paginator-disabled-icon-color:rgba(0, 0, 0, 0.12)}html{--mat-paginator-container-size:56px}.mat-mdc-paginator .mat-mdc-form-field-infix{min-height:40px}.mat-mdc-paginator .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:20px}.mat-mdc-paginator .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY( -26.75px) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-paginator .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-floating-label{display:none}html{--mat-paginator-container-text-font:Roboto, sans-serif;--mat-paginator-container-text-line-height:20px;--mat-paginator-container-text-size:12px;--mat-paginator-container-text-tracking:0.0333333333em;--mat-paginator-container-text-weight:400;--mat-paginator-select-trigger-text-size:12px}.mat-mdc-tab-group,.mat-mdc-tab-nav-bar{--mdc-tab-indicator-active-indicator-color:#3f51b5;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#3f51b5;--mat-tab-header-active-ripple-color:#3f51b5;--mat-tab-header-inactive-ripple-color:#3f51b5;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#3f51b5;--mat-tab-header-active-hover-label-text-color:#3f51b5;--mat-tab-header-active-focus-indicator-color:#3f51b5;--mat-tab-header-active-hover-indicator-color:#3f51b5}.mat-mdc-tab-group.mat-accent,.mat-mdc-tab-nav-bar.mat-accent{--mdc-tab-indicator-active-indicator-color:#ff4081;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#ff4081;--mat-tab-header-active-ripple-color:#ff4081;--mat-tab-header-inactive-ripple-color:#ff4081;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#ff4081;--mat-tab-header-active-hover-label-text-color:#ff4081;--mat-tab-header-active-focus-indicator-color:#ff4081;--mat-tab-header-active-hover-indicator-color:#ff4081}.mat-mdc-tab-group.mat-warn,.mat-mdc-tab-nav-bar.mat-warn{--mdc-tab-indicator-active-indicator-color:#f44336;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#f44336;--mat-tab-header-active-ripple-color:#f44336;--mat-tab-header-inactive-ripple-color:#f44336;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#f44336;--mat-tab-header-active-hover-label-text-color:#f44336;--mat-tab-header-active-focus-indicator-color:#f44336;--mat-tab-header-active-hover-indicator-color:#f44336}.mat-mdc-tab-group.mat-background-primary,.mat-mdc-tab-nav-bar.mat-background-primary{--mat-tab-header-with-background-background-color:#3f51b5;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-group.mat-background-accent,.mat-mdc-tab-nav-bar.mat-background-accent{--mat-tab-header-with-background-background-color:#ff4081;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-group.mat-background-warn,.mat-mdc-tab-nav-bar.mat-background-warn{--mat-tab-header-with-background-background-color:#f44336;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-header{--mdc-secondary-navigation-tab-container-height:48px}.mat-mdc-tab-header{--mat-tab-header-label-text-font:Roboto, sans-serif;--mat-tab-header-label-text-size:14px;--mat-tab-header-label-text-tracking:0.0892857143em;--mat-tab-header-label-text-line-height:36px;--mat-tab-header-label-text-weight:500}html{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#ff4081;--mdc-checkbox-selected-hover-icon-color:#ff4081;--mdc-checkbox-selected-icon-color:#ff4081;--mdc-checkbox-selected-pressed-icon-color:#ff4081;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#ff4081;--mdc-checkbox-selected-hover-state-layer-color:#ff4081;--mdc-checkbox-selected-pressed-state-layer-color:#ff4081;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox.mat-primary{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#3f51b5;--mdc-checkbox-selected-hover-icon-color:#3f51b5;--mdc-checkbox-selected-icon-color:#3f51b5;--mdc-checkbox-selected-pressed-icon-color:#3f51b5;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#3f51b5;--mdc-checkbox-selected-hover-state-layer-color:#3f51b5;--mdc-checkbox-selected-pressed-state-layer-color:#3f51b5;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox.mat-warn{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#f44336;--mdc-checkbox-selected-hover-icon-color:#f44336;--mdc-checkbox-selected-icon-color:#f44336;--mdc-checkbox-selected-pressed-icon-color:#f44336;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#f44336;--mdc-checkbox-selected-hover-state-layer-color:#f44336;--mdc-checkbox-selected-pressed-state-layer-color:#f44336;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{color:rgba(0,0,0,.38)}html{--mdc-checkbox-state-layer-size:40px}.mat-mdc-checkbox .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-body2-font-size, 14px);line-height:var(--mdc-typography-body2-line-height, 20px);font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:var(--mdc-typography-body2-text-transform, none)}.mat-mdc-button.mat-unthemed{--mdc-text-button-label-text-color:#000}.mat-mdc-button.mat-primary{--mdc-text-button-label-text-color:#3f51b5}.mat-mdc-button.mat-accent{--mdc-text-button-label-text-color:#ff4081}.mat-mdc-button.mat-warn{--mdc-text-button-label-text-color:#f44336}.mat-mdc-button[disabled][disabled]{--mdc-text-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-text-button-label-text-color:rgba(0, 0, 0, 0.38)}.mat-mdc-unelevated-button.mat-unthemed{--mdc-filled-button-container-color:#fff;--mdc-filled-button-label-text-color:#000}.mat-mdc-unelevated-button.mat-primary{--mdc-filled-button-container-color:#3f51b5;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button.mat-accent{--mdc-filled-button-container-color:#ff4081;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button.mat-warn{--mdc-filled-button-container-color:#f44336;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button[disabled][disabled]{--mdc-filled-button-disabled-container-color:rgba(0, 0, 0, 0.12);--mdc-filled-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-button-container-color:rgba(0, 0, 0, 0.12);--mdc-filled-button-label-text-color:rgba(0, 0, 0, 0.38)}.mat-mdc-raised-button.mat-unthemed{--mdc-protected-button-container-color:#fff;--mdc-protected-button-label-text-color:#000}.mat-mdc-raised-button.mat-primary{--mdc-protected-button-container-color:#3f51b5;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button.mat-accent{--mdc-protected-button-container-color:#ff4081;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button.mat-warn{--mdc-protected-button-container-color:#f44336;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button[disabled][disabled]{--mdc-protected-button-disabled-container-color:rgba(0, 0, 0, 0.12);--mdc-protected-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-protected-button-container-color:rgba(0, 0, 0, 0.12);--mdc-protected-button-label-text-color:rgba(0, 0, 0, 0.38);--mdc-protected-button-container-elevation:0}.mat-mdc-outlined-button{--mdc-outlined-button-outline-color:rgba(0, 0, 0, 0.12)}.mat-mdc-outlined-button.mat-unthemed{--mdc-outlined-button-label-text-color:#000}.mat-mdc-outlined-button.mat-primary{--mdc-outlined-button-label-text-color:#3f51b5}.mat-mdc-outlined-button.mat-accent{--mdc-outlined-button-label-text-color:#ff4081}.mat-mdc-outlined-button.mat-warn{--mdc-outlined-button-label-text-color:#f44336}.mat-mdc-outlined-button[disabled][disabled]{--mdc-outlined-button-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-button-outline-color:rgba(0, 0, 0, 0.12);--mdc-outlined-button-disabled-outline-color:rgba(0, 0, 0, 0.12)}.mat-mdc-button,.mat-mdc-outlined-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-button.mat-primary,.mat-mdc-outlined-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #3f51b5;--mat-mdc-button-ripple-color: rgba(63, 81, 181, 0.1)}.mat-mdc-button.mat-accent,.mat-mdc-outlined-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #ff4081;--mat-mdc-button-ripple-color: rgba(255, 64, 129, 0.1)}.mat-mdc-button.mat-warn,.mat-mdc-outlined-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #f44336;--mat-mdc-button-ripple-color: rgba(244, 67, 54, 0.1)}.mat-mdc-raised-button,.mat-mdc-unelevated-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-raised-button.mat-primary,.mat-mdc-unelevated-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-raised-button.mat-accent,.mat-mdc-unelevated-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-raised-button.mat-warn,.mat-mdc-unelevated-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-button.mat-mdc-button-base,.mat-mdc-raised-button.mat-mdc-button-base,.mat-mdc-unelevated-button.mat-mdc-button-base,.mat-mdc-outlined-button.mat-mdc-button-base{height:36px}.mdc-button{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-button-font-size, 14px);line-height:var(--mdc-typography-button-line-height, 36px);font-weight:var(--mdc-typography-button-font-weight, 500);letter-spacing:var(--mdc-typography-button-letter-spacing, 0.0892857143em);text-decoration:var(--mdc-typography-button-text-decoration, none);text-transform:var(--mdc-typography-button-text-transform, none)}.mat-mdc-icon-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1);--mdc-icon-button-icon-color:inherit;--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-icon-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-icon-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-icon-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #6200ee;--mat-mdc-button-ripple-color: rgba(98, 0, 238, 0.1)}.mat-mdc-icon-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #018786;--mat-mdc-button-ripple-color: rgba(1, 135, 134, 0.1)}.mat-mdc-icon-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #b00020;--mat-mdc-button-ripple-color: rgba(176, 0, 32, 0.1)}.mat-mdc-icon-button.mat-primary{--mdc-icon-button-icon-color:#3f51b5;--mat-mdc-button-persistent-ripple-color: #3f51b5;--mat-mdc-button-ripple-color: rgba(63, 81, 181, 0.1)}.mat-mdc-icon-button.mat-accent{--mdc-icon-button-icon-color:#ff4081;--mat-mdc-button-persistent-ripple-color: #ff4081;--mat-mdc-button-ripple-color: rgba(255, 64, 129, 0.1)}.mat-mdc-icon-button.mat-warn{--mdc-icon-button-icon-color:#f44336;--mat-mdc-button-persistent-ripple-color: #f44336;--mat-mdc-button-ripple-color: rgba(244, 67, 54, 0.1)}.mat-mdc-icon-button[disabled][disabled]{--mdc-icon-button-icon-color:rgba(0, 0, 0, 0.38);--mdc-icon-button-disabled-icon-color:rgba(0, 0, 0, 0.38)}.mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size:48px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:12px}.mat-mdc-fab,.mat-mdc-mini-fab{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-fab:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-fab.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-fab:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-fab.mat-primary,.mat-mdc-mini-fab.mat-primary{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab.mat-accent,.mat-mdc-mini-fab.mat-accent{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab.mat-warn,.mat-mdc-mini-fab.mat-warn{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab[disabled][disabled],.mat-mdc-mini-fab[disabled][disabled]{--mdc-fab-container-color:rgba(0, 0, 0, 0.12);--mdc-fab-icon-color:rgba(0, 0, 0, 0.38);--mat-mdc-fab-color: rgba(0, 0, 0, 0.38)}.mat-mdc-fab.mat-unthemed,.mat-mdc-mini-fab.mat-unthemed{--mdc-fab-container-color:white;--mdc-fab-icon-color:black;--mat-mdc-fab-color: #000}.mat-mdc-fab.mat-primary,.mat-mdc-mini-fab.mat-primary{--mdc-fab-container-color:#3f51b5;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mat-mdc-fab.mat-accent,.mat-mdc-mini-fab.mat-accent{--mdc-fab-container-color:#ff4081;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mat-mdc-fab.mat-warn,.mat-mdc-mini-fab.mat-warn{--mdc-fab-container-color:#f44336;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mdc-fab--extended{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-button-font-size, 14px);line-height:var(--mdc-typography-button-line-height, 36px);font-weight:var(--mdc-typography-button-font-weight, 500);letter-spacing:var(--mdc-typography-button-letter-spacing, 0.0892857143em);text-decoration:var(--mdc-typography-button-text-decoration, none);text-transform:var(--mdc-typography-button-text-transform, none)}.mat-mdc-extended-fab{--mdc-extended-fab-label-text-font:Roboto, sans-serif;--mdc-extended-fab-label-text-size:14px;--mdc-extended-fab-label-text-tracking:0.0892857143em;--mdc-extended-fab-label-text-weight:500}.mat-mdc-snack-bar-container{--mdc-snackbar-container-color:#333333;--mdc-snackbar-supporting-text-color:rgba(255, 255, 255, 0.87);--mat-snack-bar-button-color:#ff4081}.mat-mdc-snack-bar-container{--mdc-snackbar-supporting-text-font:Roboto, sans-serif;--mdc-snackbar-supporting-text-line-height:20px;--mdc-snackbar-supporting-text-size:14px;--mdc-snackbar-supporting-text-weight:400}html{--mat-table-background-color:white;--mat-table-header-headline-color:rgba(0, 0, 0, 0.87);--mat-table-row-item-label-text-color:rgba(0, 0, 0, 0.87);--mat-table-row-item-outline-color:rgba(0, 0, 0, 0.12)}html{--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px}html{--mat-table-header-headline-font:Roboto, sans-serif;--mat-table-header-headline-line-height:22px;--mat-table-header-headline-size:14px;--mat-table-header-headline-weight:500;--mat-table-header-headline-tracking:0.0071428571em;--mat-table-row-item-label-text-font:Roboto, sans-serif;--mat-table-row-item-label-text-line-height:20px;--mat-table-row-item-label-text-size:14px;--mat-table-row-item-label-text-weight:400;--mat-table-row-item-label-text-tracking:0.0178571429em;--mat-table-footer-supporting-text-font:Roboto, sans-serif;--mat-table-footer-supporting-text-line-height:20px;--mat-table-footer-supporting-text-size:14px;--mat-table-footer-supporting-text-weight:400;--mat-table-footer-supporting-text-tracking:0.0178571429em}.mat-mdc-progress-spinner{--mdc-circular-progress-active-indicator-color:#3f51b5}.mat-mdc-progress-spinner.mat-accent{--mdc-circular-progress-active-indicator-color:#ff4081}.mat-mdc-progress-spinner.mat-warn{--mdc-circular-progress-active-indicator-color:#f44336}.mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;border-radius:50%;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:Roboto, sans-serif;font-family:var(--mat-badge-text-font, Roboto, sans-serif);font-size:12px;font-size:var(--mat-badge-text-size, 12px);font-weight:600;font-weight:var(--mat-badge-text-weight, 600)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:16px;height:16px;line-height:16px;font-size:9px;font-size:var(--mat-badge-small-size-text-size, 9px)}.mat-badge-small.mat-badge-above .mat-badge-content{top:-8px}.mat-badge-small.mat-badge-below .mat-badge-content{bottom:-8px}.mat-badge-small.mat-badge-before .mat-badge-content{left:-16px}[dir=rtl] .mat-badge-small.mat-badge-before .mat-badge-content{left:auto;right:-16px}.mat-badge-small.mat-badge-after .mat-badge-content{right:-16px}[dir=rtl] .mat-badge-small.mat-badge-after .mat-badge-content{right:auto;left:-16px}.mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-8px}.mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-8px}.mat-badge-medium .mat-badge-content{width:22px;height:22px;line-height:22px}.mat-badge-medium.mat-badge-above .mat-badge-content{top:-11px}.mat-badge-medium.mat-badge-below .mat-badge-content{bottom:-11px}.mat-badge-medium.mat-badge-before .mat-badge-content{left:-22px}[dir=rtl] .mat-badge-medium.mat-badge-before .mat-badge-content{left:auto;right:-22px}.mat-badge-medium.mat-badge-after .mat-badge-content{right:-22px}[dir=rtl] .mat-badge-medium.mat-badge-after .mat-badge-content{right:auto;left:-22px}.mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-11px}.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-11px}.mat-badge-large .mat-badge-content{width:28px;height:28px;line-height:28px;font-size:24px;font-size:var(--mat-badge-large-size-text-size, 24px)}.mat-badge-large.mat-badge-above .mat-badge-content{top:-14px}.mat-badge-large.mat-badge-below .mat-badge-content{bottom:-14px}.mat-badge-large.mat-badge-before .mat-badge-content{left:-28px}[dir=rtl] .mat-badge-large.mat-badge-before .mat-badge-content{left:auto;right:-28px}.mat-badge-large.mat-badge-after .mat-badge-content{right:-28px}[dir=rtl] .mat-badge-large.mat-badge-after .mat-badge-content{right:auto;left:-28px}.mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-14px}.mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-14px}html{--mat-badge-background-color:#3f51b5;--mat-badge-text-color:white;--mat-badge-disabled-state-background-color:#b9b9b9;--mat-badge-disabled-state-text-color:rgba(0, 0, 0, 0.38)}.mat-badge-accent{--mat-badge-background-color:#ff4081;--mat-badge-text-color:white}.mat-badge-warn{--mat-badge-background-color:#f44336;--mat-badge-text-color:white}html{--mat-badge-text-font:Roboto, sans-serif;--mat-badge-text-size:12px;--mat-badge-text-weight:600;--mat-badge-small-size-text-size:9px;--mat-badge-large-size-text-size:24px}html{--mat-bottom-sheet-container-text-color:rgba(0, 0, 0, 0.87);--mat-bottom-sheet-container-background-color:white}html{--mat-bottom-sheet-container-text-font:Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height:20px;--mat-bottom-sheet-container-text-size:14px;--mat-bottom-sheet-container-text-tracking:0.0178571429em;--mat-bottom-sheet-container-text-weight:400}html{--mat-legacy-button-toggle-text-color:rgba(0, 0, 0, 0.38);--mat-legacy-button-toggle-state-layer-color:rgba(0, 0, 0, 0.12);--mat-legacy-button-toggle-selected-state-text-color:rgba(0, 0, 0, 0.54);--mat-legacy-button-toggle-selected-state-background-color:#e0e0e0;--mat-legacy-button-toggle-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-legacy-button-toggle-disabled-state-background-color:#eeeeee;--mat-legacy-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-standard-button-toggle-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-background-color:white;--mat-standard-button-toggle-state-layer-color:black;--mat-standard-button-toggle-selected-state-background-color:#e0e0e0;--mat-standard-button-toggle-selected-state-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-standard-button-toggle-disabled-state-background-color:white;--mat-standard-button-toggle-disabled-selected-state-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-standard-button-toggle-divider-color:#e0e0e0}html{--mat-standard-button-toggle-height:48px}html{--mat-legacy-button-toggle-text-font:Roboto, sans-serif;--mat-standard-button-toggle-text-font:Roboto, sans-serif}html{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#3f51b5;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(63, 81, 181, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(63, 81, 181, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(63, 81, 181, 0.3);--mat-datepicker-toggle-active-state-icon-color:#3f51b5;--mat-datepicker-calendar-date-in-range-state-background-color:rgba(63, 81, 181, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e;--mat-datepicker-toggle-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-body-label-text-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-period-button-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-navigation-button-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-header-divider-color:rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-text-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-outline-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-date-today-disabled-state-outline-color:rgba(0, 0, 0, 0.18);--mat-datepicker-calendar-date-text-color:rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-outline-color:transparent;--mat-datepicker-calendar-date-disabled-state-text-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-date-preview-state-outline-color:rgba(0, 0, 0, 0.24);--mat-datepicker-range-input-separator-color:rgba(0, 0, 0, 0.87);--mat-datepicker-range-input-disabled-state-separator-color:rgba(0, 0, 0, 0.38);--mat-datepicker-range-input-disabled-state-text-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-container-background-color:white;--mat-datepicker-calendar-container-text-color:rgba(0, 0, 0, 0.87)}.mat-datepicker-content.mat-accent{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#ff4081;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(255, 64, 129, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(255, 64, 129, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(255, 64, 129, 0.3);--mat-datepicker-calendar-date-in-range-state-background-color:rgba(255, 64, 129, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e}.mat-datepicker-content.mat-warn{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#f44336;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(244, 67, 54, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(244, 67, 54, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(244, 67, 54, 0.3);--mat-datepicker-calendar-date-in-range-state-background-color:rgba(244, 67, 54, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e}.mat-datepicker-toggle-active.mat-accent{--mat-datepicker-toggle-active-state-icon-color:#ff4081}.mat-datepicker-toggle-active.mat-warn{--mat-datepicker-toggle-active-state-icon-color:#f44336}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size:40px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:8px}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base .mat-mdc-button-touch-target{display:none}html{--mat-datepicker-calendar-text-font:Roboto, sans-serif;--mat-datepicker-calendar-text-size:13px;--mat-datepicker-calendar-body-label-text-size:14px;--mat-datepicker-calendar-body-label-text-weight:500;--mat-datepicker-calendar-period-button-text-size:14px;--mat-datepicker-calendar-period-button-text-weight:500;--mat-datepicker-calendar-header-text-size:11px;--mat-datepicker-calendar-header-text-weight:400}html{--mat-divider-color:rgba(0, 0, 0, 0.12)}html{--mat-expansion-container-background-color:white;--mat-expansion-container-text-color:rgba(0, 0, 0, 0.87);--mat-expansion-actions-divider-color:rgba(0, 0, 0, 0.12);--mat-expansion-header-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-expansion-header-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-expansion-header-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-expansion-header-text-color:rgba(0, 0, 0, 0.87);--mat-expansion-header-description-color:rgba(0, 0, 0, 0.54);--mat-expansion-header-indicator-color:rgba(0, 0, 0, 0.54)}html{--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px}html{--mat-expansion-header-text-font:Roboto, sans-serif;--mat-expansion-header-text-size:14px;--mat-expansion-header-text-weight:500;--mat-expansion-header-text-line-height:inherit;--mat-expansion-header-text-tracking:inherit;--mat-expansion-container-text-font:Roboto, sans-serif;--mat-expansion-container-text-line-height:20px;--mat-expansion-container-text-size:14px;--mat-expansion-container-text-tracking:0.0178571429em;--mat-expansion-container-text-weight:400}html{--mat-grid-list-tile-header-primary-text-size:14px;--mat-grid-list-tile-header-secondary-text-size:12px;--mat-grid-list-tile-footer-primary-text-size:14px;--mat-grid-list-tile-footer-secondary-text-size:12px}html{--mat-icon-color:inherit}.mat-icon.mat-primary{--mat-icon-color:#3f51b5}.mat-icon.mat-accent{--mat-icon-color:#ff4081}.mat-icon.mat-warn{--mat-icon-color:#f44336}html{--mat-sidenav-container-divider-color:rgba(0, 0, 0, 0.12);--mat-sidenav-container-background-color:white;--mat-sidenav-container-text-color:rgba(0, 0, 0, 0.87);--mat-sidenav-content-background-color:#fafafa;--mat-sidenav-content-text-color:rgba(0, 0, 0, 0.87);--mat-sidenav-scrim-color:rgba(0, 0, 0, 0.6)}html{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#3f51b5;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#3f51b5;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#3f51b5;--mat-stepper-header-edit-state-icon-foreground-color:white;--mat-stepper-container-color:white;--mat-stepper-line-color:rgba(0, 0, 0, 0.12);--mat-stepper-header-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-stepper-header-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-stepper-header-label-text-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-optional-label-text-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-selected-state-label-text-color:rgba(0, 0, 0, 0.87);--mat-stepper-header-error-state-label-text-color:#f44336;--mat-stepper-header-icon-background-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-error-state-icon-foreground-color:#f44336;--mat-stepper-header-error-state-icon-background-color:transparent}html .mat-step-header.mat-accent{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#ff4081;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#ff4081;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#ff4081;--mat-stepper-header-edit-state-icon-foreground-color:white}html .mat-step-header.mat-warn{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#f44336;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#f44336;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#f44336;--mat-stepper-header-edit-state-icon-foreground-color:white}html{--mat-stepper-header-height:72px}html{--mat-stepper-container-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-size:14px;--mat-stepper-header-label-text-weight:400;--mat-stepper-header-error-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-weight:400}.mat-sort-header-arrow{color:#757575}html{--mat-toolbar-container-background-color:whitesmoke;--mat-toolbar-container-text-color:rgba(0, 0, 0, 0.87)}.mat-toolbar.mat-primary{--mat-toolbar-container-background-color:#3f51b5;--mat-toolbar-container-text-color:white}.mat-toolbar.mat-accent{--mat-toolbar-container-background-color:#ff4081;--mat-toolbar-container-text-color:white}.mat-toolbar.mat-warn{--mat-toolbar-container-background-color:#f44336;--mat-toolbar-container-text-color:white}html{--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px}html{--mat-toolbar-title-text-font:Roboto, sans-serif;--mat-toolbar-title-text-line-height:32px;--mat-toolbar-title-text-size:20px;--mat-toolbar-title-text-tracking:0.0125em;--mat-toolbar-title-text-weight:500}.mat-tree{background:#fff}.mat-tree-node,.mat-nested-tree-node{color:rgba(0,0,0,.87)}.mat-tree-node{min-height:48px}.mat-tree{font-family:Roboto, sans-serif}.mat-tree-node,.mat-nested-tree-node{font-weight:400;font-size:14px}.mat-h1,.mat-headline-5,.mat-typography .mat-h1,.mat-typography .mat-headline-5,.mat-typography h1{font-size:24px;font-weight:400;line-height:32px;font-family:Roboto, sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2,.mat-headline-6,.mat-typography .mat-h2,.mat-typography .mat-headline-6,.mat-typography h2{font-size:20px;font-weight:500;line-height:32px;font-family:Roboto, sans-serif;letter-spacing:.0125em;margin:0 0 16px}.mat-h3,.mat-subtitle-1,.mat-typography .mat-h3,.mat-typography .mat-subtitle-1,.mat-typography h3{font-size:16px;font-weight:400;line-height:28px;font-family:Roboto, sans-serif;letter-spacing:.009375em;margin:0 0 16px}.mat-h4,.mat-body-1,.mat-typography .mat-h4,.mat-typography .mat-body-1,.mat-typography h4{font-size:16px;font-weight:400;line-height:24px;font-family:Roboto, sans-serif;letter-spacing:.03125em;margin:0 0 16px}.mat-h5,.mat-typography .mat-h5,.mat-typography h5{font:400 calc(14px * 0.83)/20px Roboto, sans-serif;margin:0 0 12px}.mat-h6,.mat-typography .mat-h6,.mat-typography h6{font:400 calc(14px * 0.67)/20px Roboto, sans-serif;margin:0 0 12px}.mat-body-strong,.mat-subtitle-2,.mat-typography .mat-body-strong,.mat-typography .mat-subtitle-2{font-size:14px;font-weight:500;line-height:22px;font-family:Roboto, sans-serif;letter-spacing:.0071428571em}.mat-body,.mat-body-2,.mat-typography .mat-body,.mat-typography .mat-body-2,.mat-typography{font-size:14px;font-weight:400;line-height:20px;font-family:Roboto, sans-serif;letter-spacing:.0178571429em}.mat-body p,.mat-body-2 p,.mat-typography .mat-body p,.mat-typography .mat-body-2 p,.mat-typography p{margin:0 0 12px}.mat-small,.mat-caption,.mat-typography .mat-small,.mat-typography .mat-caption{font-size:12px;font-weight:400;line-height:20px;font-family:Roboto, sans-serif;letter-spacing:.0333333333em}.mat-headline-1,.mat-typography .mat-headline-1{font-size:96px;font-weight:300;line-height:96px;font-family:Roboto, sans-serif;letter-spacing:-0.015625em;margin:0 0 56px}.mat-headline-2,.mat-typography .mat-headline-2{font-size:60px;font-weight:300;line-height:60px;font-family:Roboto, sans-serif;letter-spacing:-.0083333333em;margin:0 0 64px}.mat-headline-3,.mat-typography .mat-headline-3{font-size:48px;font-weight:400;line-height:50px;font-family:Roboto, sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-headline-4,.mat-typography .mat-headline-4{font-size:34px;font-weight:400;line-height:40px;font-family:Roboto, sans-serif;letter-spacing:.0073529412em;margin:0 0 64px}", "/* You can add global styles to this file, and also import other style files */\n\n/* Font optimization for better loading performance */\n@font-face {\n  font-family: 'Material Icons';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap; /* Ensures text remains visible during font load */\n  src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');\n}\n\n/* Ensure Material Icons load properly with fallback */\n.material-icons {\n  font-family: 'Material Icons';\n  font-weight: normal;\n  font-style: normal;\n  font-size: 24px;\n  line-height: 1;\n  letter-spacing: normal;\n  text-transform: none;\n  display: inline-block;\n  white-space: nowrap;\n  word-wrap: normal;\n  direction: ltr;\n  font-feature-settings: 'liga';\n  -webkit-font-feature-settings: 'liga';\n  -webkit-font-smoothing: antialiased;\n  text-rendering: optimizeLegibility;\n  -moz-osx-font-smoothing: grayscale;\n  font-display: swap;\n}\n\n/* Angular Material icon compatibility */\n.mat-icon {\n  font-display: swap;\n}\n\nbody {\n  padding: 0px;\n  margin: 0px;\n\n  // disable text selection to be more \"app like\"\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n}\n\n// Bootstrap 4\n@import './styles/bootstrap/bootstrap-grid.scss';\n\n// Material theme\n@import '@angular/material/prebuilt-themes/indigo-pink.css';\n\n// Custom Spriggy theme.\n@import './styles/cashless-theme.scss';\n@import './styles/cashless-font.scss';\n@import './styles/cashless-breakpoints.scss';\n@import './styles/spinner.scss';\n\n// usefull to desactivate the mobile zoom in forms\ninput[type='color'],\ninput[type='date'],\ninput[type='datetime'],\ninput[type='datetime-local'],\ninput[type='email'],\ninput[type='month'],\ninput[type='number'],\ninput[type='password'],\ninput[type='search'],\ninput[type='tel'],\ninput[type='text'],\ninput[type='time'],\ninput[type='url'],\ninput[type='week'],\nselect,\noption,\ntextarea {\n  font-size: 16px;\n}\n\nselect option {\n  font-size: 16px;\n}\n\n.cashlessForm > * {\n  width: 100%;\n}\n\n.cashlessLink {\n  a {\n    text-decoration: none;\n    cursor: pointer;\n  }\n}\n\n/* default font */\n* {\n  font-family: 'bariol_regular', Arial, Helvetica, sans-serif;\n}\n\n.noPadding {\n  padding: 0;\n}\n\n.mat-drawer-content {\n  background-color: $grey-4;\n}\n\n/////////////////////////////////////////////////\n// Card\n////////////////////////////////////////////////\n\n.cardDefaultParent {\n  background-color: white;\n  padding: 0px;\n  margin-bottom: 20px;\n  border: 1px solid #dddddd;\n  border-radius: 0;\n  box-sizing: border-box;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n  @media (min-width: $breakpoint-md) {\n    border-radius: 12px;\n  }\n  height: auto;\n}\n\n.cardDefaultCanteen {\n  background-color: white;\n  padding: 20px;\n  margin-bottom: 20px;\n  border: 1px solid #dddddd;\n  border-radius: 0;\n  box-sizing: border-box;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n  @media (min-width: $breakpoint-md) {\n    border-radius: 12px;\n  }\n  height: auto;\n}\n\n/////////////////////////////////////////////////\n// Buttons\n/////////////////////////////////////////////////\n\n.PrimaryButton {\n  background: linear-gradient(96.62deg, $orange-1 0%, $orange-2 100%);\n  border-radius: 8px;\n  border-style: none;\n  color: white;\n  cursor: pointer;\n  font-family: 'bariol_bold';\n  font-size: 22px;\n  margin-top: auto; // Default: vertically centre the button. Overide as neccessary.\n  margin-bottom: auto; // Default: vertically centre the button. Overide as neccessary.\n  height: 56px; // Default: 56px height. Overide as neccessary.\n  width: 100%; // Default: Fill the entire width. Overide as neccessary.\n  outline: none;\n\n  &.smaller {\n    height: 48px;\n    border-radius: 10px;\n  }\n\n  &.action {\n    width: 60px;\n    height: 35px;\n    font-size: 16px;\n    padding: 10px;\n  }\n}\n.PrimaryButton:disabled {\n  color: rgba(43, 45, 37, 0.5);\n  font-family: 'bariol_regular';\n}\n\n.SecondaryButton {\n  width: 100%;\n  background-color: $grey-2;\n  color: black;\n  margin-top: 10px;\n  border-radius: 8px;\n  cursor: pointer;\n}\n\n.WarnLink {\n  width: 100%;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n/////////////////////////////////////////////////\n// family\n/////////////////////////////////////////////////\n.titleBlock {\n  color: $charcoal-1;\n  font-family: 'bariol_bold';\n  font-size: 18px;\n  margin-bottom: 6px;\n  margin-top: 32px;\n  padding-top: 0px;\n\n  @media (min-width: $breakpoint-md) {\n    margin-top: 16px;\n  }\n}\n\n// Horizontal scrolling\n.scrolling-horizontal-wrapper {\n  overflow: hidden;\n  overflow-x: auto;\n  overflow-y: hidden;\n  white-space: nowrap;\n\n  /* width */\n  &::-webkit-scrollbar {\n    height: 4px;\n  }\n\n  /* Track */\n  &::-webkit-scrollbar-track {\n    background: #f1f1f1;\n  }\n\n  /* Handle */\n  &::-webkit-scrollbar-thumb {\n    @media (min-width: $breakpoint-md) {\n      background: $grey-2;\n    }\n  }\n\n  /* Handle on hover */\n  &::-webkit-scrollbar-thumb:hover {\n    cursor: pointer;\n    background: $grey-10;\n  }\n}\n\n// Spinner\n.spinnerBlock {\n  padding-top: 10px;\n  padding-bottom: 10px;\n}\n\n/////////////////////////////////////////////////\n// Modal\n/////////////////////////////////////////////////\n.modalClose {\n  height: 56px;\n  width: 56px;\n  background-color: white;\n  border: 1px solid $grey-2;\n  border-radius: 50%;\n  padding: 7px;\n  -webkit-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);\n  -moz-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);\n  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);\n\n  &.noBackground {\n    -webkit-box-shadow: none;\n    -moz-box-shadow: none;\n    box-shadow: none;\n    border: none;\n  }\n  & mat-icon {\n    font-size: 40px;\n    color: $orange-3;\n    cursor: pointer;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.spinnerDialog {\n  width: 100px;\n  height: 300px;\n  margin: auto;\n  padding-top: 100px;\n}\n\n.paddingLine {\n  padding: 5px;\n}\n\n.headerContainer {\n  height: 60px;\n}\n\n.titleDialog {\n  text-align: center;\n  font-size: 24px;\n  margin-bottom: 100px;\n}\n\n.feeInformationLink {\n  margin-bottom: 0;\n  margin-top: 30px;\n  color: $charcoal-1;\n  font-size: 14px;\n\n  & a {\n    cursor: pointer;\n    color: $orange-3;\n    text-decoration: underline;\n  }\n}\n\n// Section\n.section {\n  background-color: white;\n  border-radius: 12px;\n  margin-left: 12px;\n  margin-right: 12px;\n  margin-top: 12px;\n  margin-bottom: 12px;\n  padding-top: 12px;\n\n  &.noTopMargin {\n    margin-top: 0;\n  }\n\n  &.noSideMargin {\n    margin-left: 0;\n    margin-right: 0;\n  }\n}\n", "/*!\n * Bootstrap Grid v4.3.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 The Bootstrap Authors\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\nhtml {\n  box-sizing: border-box;\n  -ms-overflow-style: scrollbar;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\n@import 'functions';\n@import 'variables';\n\n@import 'mixins/breakpoints';\n@import 'mixins/grid-framework';\n@import 'mixins/grid';\n\n@import 'grid';\n@import 'utilities/display';\n@import 'utilities/flex';\n@import 'utilities/spacing';\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  .container {\n    @include make-container();\n    @include make-container-max-widths();\n  }\n}\n\n// Fluid container\n//\n// Utilizes the mixin meant for fixed width containers, but with 100% width for\n// fluid, full width layouts.\n\n@if $enable-grid-classes {\n  .container-fluid {\n    @include make-container();\n    background-color: #f2f2f2;\n  }\n}\n\n// Row\n//\n// Rows contain and clear the floats of your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n  }\n\n  // Remove the negative margin from default .row, then the horizontal padding\n  // from all immediate children columns (to prevent runaway style inheritance).\n  .no-gutters {\n    margin-right: 0;\n    margin-left: 0;\n\n    > .col,\n    > [class*='col-'] {\n      padding-right: 0;\n      padding-left: 0;\n    }\n  }\n}\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: calc($gutter / 2);\n  padding-left: calc($gutter / 2);\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: calc($gutter / -2);\n  margin-left: calc($gutter / -2);\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: calc($gutter / 2);\n  padding-left: calc($gutter / 2);\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage(calc($size / $columns));\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage(calc($size / $columns));\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: calc($size / $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - 0.02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, '', '-#{$name}');\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white: #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black: #000 !default;\n\n$grays: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    '100': $gray-100,\n    '200': $gray-200,\n    '300': $gray-300,\n    '400': $gray-400,\n    '500': $gray-500,\n    '600': $gray-600,\n    '700': $gray-700,\n    '800': $gray-800,\n    '900': $gray-900,\n  ),\n  $grays\n);\n\n$blue: #007bff !default;\n$indigo: #6610f2 !default;\n$purple: #6f42c1 !default;\n$pink: #e83e8c !default;\n$red: #dc3545 !default;\n$orange: #fd7e14 !default;\n$yellow: #ffc107 !default;\n$green: #28a745 !default;\n$teal: #20c997 !default;\n$cyan: #17a2b8 !default;\n\n$colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    'blue': $blue,\n    'indigo': $indigo,\n    'purple': $purple,\n    'pink': $pink,\n    'red': $red,\n    'orange': $orange,\n    'yellow': $yellow,\n    'green': $green,\n    'teal': $teal,\n    'cyan': $cyan,\n    'white': $white,\n    'gray': $gray-600,\n    'gray-dark': $gray-800,\n  ),\n  $colors\n);\n\n$primary: $blue !default;\n$secondary: $gray-600 !default;\n$success: $green !default;\n$info: $cyan !default;\n$warning: $yellow !default;\n$danger: $red !default;\n$light: $gray-100 !default;\n$dark: $gray-800 !default;\n\n$theme-colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    'primary': $primary,\n    'secondary': $secondary,\n    'success': $success,\n    'info': $info,\n    'warning': $warning,\n    'danger': $danger,\n    'light': $light,\n    'dark': $dark,\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval: 8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: $gray-900 !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret: true !default;\n$enable-rounded: true !default;\n$enable-shadows: false !default;\n$enable-gradients: false !default;\n$enable-transitions: true !default;\n$enable-prefers-reduced-motion-media-query: true !default;\n$enable-hover-media-query: false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes: true !default;\n$enable-pointer-cursor-for-buttons: true !default;\n$enable-print-styles: true !default;\n$enable-responsive-font-sizes: false !default;\n$enable-validation-icons: true !default;\n$enable-deprecation-messages: true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: (\n      $spacer * 0.25,\n    ),\n    2: (\n      $spacer * 0.5,\n    ),\n    3: $spacer,\n    4: (\n      $spacer * 1.5,\n    ),\n    5: (\n      $spacer * 3,\n    ),\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto,\n  ),\n  $sizes\n);\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg: $white !default;\n$body-color: $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color: theme-color('primary') !default;\n$link-decoration: none !default;\n$link-hover-color: darken($link-color, 15%) !default;\n$link-hover-decoration: underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem !default;\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n) !default;\n\n@include _assert-ascending($grid-breakpoints, '$grid-breakpoints');\n@include _assert-starts-at-zero($grid-breakpoints, '$grid-breakpoints');\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n) !default;\n\n@include _assert-ascending($container-max-widths, '$container-max-widths');\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns: 12 !default;\n$grid-gutter-width: 30px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg: 1.5 !default;\n$line-height-sm: 1.5 !default;\n\n$border-width: 1px !default;\n$border-color: $gray-300 !default;\n\n$border-radius: 0.25rem !default;\n$border-radius-lg: 0.3rem !default;\n$border-radius-sm: 0.2rem !default;\n\n$rounded-pill: 50rem !default;\n\n$box-shadow-sm: 0 0.125rem 0.25rem rgba($black, 0.075) !default;\n$box-shadow: 0 0.5rem 1rem rgba($black, 0.15) !default;\n$box-shadow-lg: 0 1rem 3rem rgba($black, 0.175) !default;\n\n$component-active-color: $white !default;\n$component-active-bg: theme-color('primary') !default;\n\n$caret-width: 0.3em !default;\n$caret-vertical-align: $caret-width * 0.85 !default;\n$caret-spacing: $caret-width * 0.85 !default;\n\n$transition-base: all 0.2s ease-in-out !default;\n$transition-fade: opacity 0.15s linear !default;\n$transition-collapse: height 0.35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$embed-responsive-aspect-ratios: join(((21 9), (16 9), (4 3), (1 1)), $embed-responsive-aspect-ratios);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !default;\n$font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !default;\n$font-family-base: $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg: $font-size-base * 1.25 !default;\n$font-size-sm: $font-size-base * 0.875 !default;\n\n$font-weight-lighter: lighter !default;\n$font-weight-light: 300 !default;\n$font-weight-normal: 400 !default;\n$font-weight-bold: 700 !default;\n$font-weight-bolder: bolder !default;\n\n$font-weight-base: $font-weight-normal !default;\n$line-height-base: 1.5 !default;\n\n$h1-font-size: $font-size-base * 2.5 !default;\n$h2-font-size: $font-size-base * 2 !default;\n$h3-font-size: $font-size-base * 1.75 !default;\n$h4-font-size: $font-size-base * 1.5 !default;\n$h5-font-size: $font-size-base * 1.25 !default;\n$h6-font-size: $font-size-base !default;\n\n$headings-margin-bottom: calc($spacer / 2) !default;\n$headings-font-family: null !default;\n$headings-font-weight: 500 !default;\n$headings-line-height: 1.2 !default;\n$headings-color: null !default;\n\n$display1-size: 6rem !default;\n$display2-size: 5.5rem !default;\n$display3-size: 4.5rem !default;\n$display4-size: 3.5rem !default;\n\n$display1-weight: 300 !default;\n$display2-weight: 300 !default;\n$display3-weight: 300 !default;\n$display4-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n\n$lead-font-size: $font-size-base * 1.25 !default;\n$lead-font-weight: 300 !default;\n\n$small-font-size: 80% !default;\n\n$text-muted: $gray-600 !default;\n\n$blockquote-small-color: $gray-600 !default;\n$blockquote-small-font-size: $small-font-size !default;\n$blockquote-font-size: $font-size-base * 1.25 !default;\n\n$hr-border-color: rgba($black, 0.1) !default;\n$hr-border-width: $border-width !default;\n\n$mark-padding: 0.2em !default;\n\n$dt-font-weight: $font-weight-bold !default;\n\n$kbd-box-shadow: inset 0 -0.1rem 0 rgba($black, 0.25) !default;\n$nested-kbd-font-weight: $font-weight-bold !default;\n\n$list-inline-padding: 0.5rem !default;\n\n$mark-bg: #fcf8e3 !default;\n\n$hr-margin-y: $spacer !default;\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding: 0.75rem !default;\n$table-cell-padding-sm: 0.3rem !default;\n\n$table-color: $body-color !default;\n$table-bg: null !default;\n$table-accent-bg: rgba($black, 0.05) !default;\n$table-hover-color: $table-color !default;\n$table-hover-bg: rgba($black, 0.075) !default;\n$table-active-bg: $table-hover-bg !default;\n\n$table-border-width: $border-width !default;\n$table-border-color: $border-color !default;\n\n$table-head-bg: $gray-200 !default;\n$table-head-color: $gray-700 !default;\n\n$table-dark-color: $white !default;\n$table-dark-bg: $gray-800 !default;\n$table-dark-accent-bg: rgba($white, 0.05) !default;\n$table-dark-hover-color: $table-dark-color !default;\n$table-dark-hover-bg: rgba($white, 0.075) !default;\n$table-dark-border-color: lighten($table-dark-bg, 7.5%) !default;\n$table-dark-color: $white !default;\n\n$table-striped-order: odd !default;\n\n$table-caption-color: $text-muted !default;\n\n$table-bg-level: -9 !default;\n$table-border-level: -6 !default;\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y: 0.375rem !default;\n$input-btn-padding-x: 0.75rem !default;\n$input-btn-font-family: null !default;\n$input-btn-font-size: $font-size-base !default;\n$input-btn-line-height: $line-height-base !default;\n\n$input-btn-focus-width: 0.2rem !default;\n$input-btn-focus-color: rgba($component-active-bg, 0.25) !default;\n$input-btn-focus-box-shadow: 0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm: 0.25rem !default;\n$input-btn-padding-x-sm: 0.5rem !default;\n$input-btn-font-size-sm: $font-size-sm !default;\n$input-btn-line-height-sm: $line-height-sm !default;\n\n$input-btn-padding-y-lg: 0.5rem !default;\n$input-btn-padding-x-lg: 1rem !default;\n$input-btn-font-size-lg: $font-size-lg !default;\n$input-btn-line-height-lg: $line-height-lg !default;\n\n$input-btn-border-width: $border-width !default;\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y: $input-btn-padding-y !default;\n$btn-padding-x: $input-btn-padding-x !default;\n$btn-font-family: $input-btn-font-family !default;\n$btn-font-size: $input-btn-font-size !default;\n$btn-line-height: $input-btn-line-height !default;\n\n$btn-padding-y-sm: $input-btn-padding-y-sm !default;\n$btn-padding-x-sm: $input-btn-padding-x-sm !default;\n$btn-font-size-sm: $input-btn-font-size-sm !default;\n$btn-line-height-sm: $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg: $input-btn-padding-y-lg !default;\n$btn-padding-x-lg: $input-btn-padding-x-lg !default;\n$btn-font-size-lg: $input-btn-font-size-lg !default;\n$btn-line-height-lg: $input-btn-line-height-lg !default;\n\n$btn-border-width: $input-btn-border-width !default;\n\n$btn-font-weight: $font-weight-normal !default;\n$btn-box-shadow: inset 0 1px 0 rgba($white, 0.15), 0 1px 1px rgba($black, 0.075) !default;\n$btn-focus-width: $input-btn-focus-width !default;\n$btn-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity: 0.65 !default;\n$btn-active-box-shadow: inset 0 3px 5px rgba($black, 0.125) !default;\n\n$btn-link-disabled-color: $gray-600 !default;\n\n$btn-block-spacing-y: 0.5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius: $border-radius !default;\n$btn-border-radius-lg: $border-radius-lg !default;\n$btn-border-radius-sm: $border-radius-sm !default;\n\n$btn-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n  box-shadow 0.15s ease-in-out !default;\n\n// Forms\n\n$label-margin-bottom: 0.5rem !default;\n\n$input-padding-y: $input-btn-padding-y !default;\n$input-padding-x: $input-btn-padding-x !default;\n$input-font-family: $input-btn-font-family !default;\n$input-font-size: $input-btn-font-size !default;\n$input-font-weight: $font-weight-base !default;\n$input-line-height: $input-btn-line-height !default;\n\n$input-padding-y-sm: $input-btn-padding-y-sm !default;\n$input-padding-x-sm: $input-btn-padding-x-sm !default;\n$input-font-size-sm: $input-btn-font-size-sm !default;\n$input-line-height-sm: $input-btn-line-height-sm !default;\n\n$input-padding-y-lg: $input-btn-padding-y-lg !default;\n$input-padding-x-lg: $input-btn-padding-x-lg !default;\n$input-font-size-lg: $input-btn-font-size-lg !default;\n$input-line-height-lg: $input-btn-line-height-lg !default;\n\n$input-bg: $white !default;\n$input-disabled-bg: $gray-200 !default;\n\n$input-color: $gray-700 !default;\n$input-border-color: $gray-400 !default;\n$input-border-width: $input-btn-border-width !default;\n$input-box-shadow: inset 0 1px 1px rgba($black, 0.075) !default;\n\n$input-border-radius: $border-radius !default;\n$input-border-radius-lg: $border-radius-lg !default;\n$input-border-radius-sm: $border-radius-sm !default;\n\n$input-focus-bg: $input-bg !default;\n$input-focus-border-color: lighten($component-active-bg, 25%) !default;\n$input-focus-color: $input-color !default;\n$input-focus-width: $input-btn-focus-width !default;\n$input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color: $gray-600 !default;\n$input-plaintext-color: $body-color !default;\n\n$input-height-border: $input-border-width * 2 !default;\n\n$input-height-inner: calc(#{$input-line-height * 1em} + #{$input-padding-y * 2}) !default;\n$input-height-inner-half: calc(#{$input-line-height * 0.5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter: calc(#{$input-line-height * 0.25em} + #{calc($input-padding-y / 2)}) !default;\n\n$input-height: calc(#{$input-line-height * 1em} + #{$input-padding-y * 2} + #{$input-height-border}) !default;\n$input-height-sm: calc(\n  #{$input-line-height-sm * 1em} + #{$input-btn-padding-y-sm * 2} + #{$input-height-border}\n) !default;\n$input-height-lg: calc(\n  #{$input-line-height-lg * 1em} + #{$input-btn-padding-y-lg * 2} + #{$input-height-border}\n) !default;\n\n$input-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !default;\n\n$form-text-margin-top: 0.25rem !default;\n\n$form-check-input-gutter: 1.25rem !default;\n$form-check-input-margin-y: 0.3rem !default;\n$form-check-input-margin-x: 0.25rem !default;\n\n$form-check-inline-margin-x: 0.75rem !default;\n$form-check-inline-input-margin-x: 0.3125rem !default;\n\n$form-grid-gutter-width: 10px !default;\n$form-group-margin-bottom: 1rem !default;\n\n$input-group-addon-color: $input-color !default;\n$input-group-addon-bg: $gray-200 !default;\n$input-group-addon-border-color: $input-border-color !default;\n\n$custom-forms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n  box-shadow 0.15s ease-in-out !default;\n\n$custom-control-gutter: 0.5rem !default;\n$custom-control-spacer-x: 1rem !default;\n\n$custom-control-indicator-size: 1rem !default;\n$custom-control-indicator-bg: $input-bg !default;\n\n$custom-control-indicator-bg-size: 50% 50% !default;\n$custom-control-indicator-box-shadow: $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-indicator-disabled-bg: $input-disabled-bg !default;\n$custom-control-label-disabled-color: $gray-600 !default;\n\n$custom-control-indicator-checked-color: $component-active-color !default;\n$custom-control-indicator-checked-bg: $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg: rgba(theme-color('primary'), 0.5) !default;\n$custom-control-indicator-checked-box-shadow: none !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow: $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color: $input-focus-border-color !default;\n\n$custom-control-indicator-active-color: $component-active-color !default;\n$custom-control-indicator-active-bg: lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow: none !default;\n$custom-control-indicator-active-border-color: $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius: $border-radius !default;\n$custom-checkbox-indicator-icon-checked: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n\n$custom-checkbox-indicator-indeterminate-bg: $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius: 50% !default;\n$custom-radio-indicator-icon-checked: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n\n$custom-switch-width: $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius: calc($custom-control-indicator-size / 2) !default;\n$custom-switch-indicator-size: calc(\n  #{$custom-control-indicator-size} - #{$custom-control-indicator-border-width * 4}\n) !default;\n\n$custom-select-padding-y: $input-padding-y !default;\n$custom-select-padding-x: $input-padding-x !default;\n$custom-select-font-family: $input-font-family !default;\n$custom-select-font-size: $input-font-size !default;\n$custom-select-height: $input-height !default;\n$custom-select-indicator-padding: 1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight: $input-font-weight !default;\n$custom-select-line-height: $input-line-height !default;\n$custom-select-color: $input-color !default;\n$custom-select-disabled-color: $gray-600 !default;\n$custom-select-bg: $input-bg !default;\n$custom-select-disabled-bg: $gray-200 !default;\n$custom-select-bg-size: 8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color: $gray-800 !default;\n$custom-select-indicator: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$custom-select-background: $custom-select-indicator no-repeat right $custom-select-padding-x center /\n  $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: calc(\n  (1em + #{2 * $custom-select-padding-y}) * 3 / 4 + #{$custom-select-padding-x +\n    $custom-select-indicator-padding}\n) !default;\n$custom-select-feedback-icon-position: center right\n  ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size: $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width: $input-border-width !default;\n$custom-select-border-color: $input-border-color !default;\n$custom-select-border-radius: $border-radius !default;\n$custom-select-box-shadow: inset 0 1px 2px rgba($black, 0.075) !default;\n\n$custom-select-focus-border-color: $input-focus-border-color !default;\n$custom-select-focus-width: $input-focus-width !default;\n$custom-select-focus-box-shadow: 0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm: $input-padding-y-sm !default;\n$custom-select-padding-x-sm: $input-padding-x-sm !default;\n$custom-select-font-size-sm: $input-font-size-sm !default;\n$custom-select-height-sm: $input-height-sm !default;\n\n$custom-select-padding-y-lg: $input-padding-y-lg !default;\n$custom-select-padding-x-lg: $input-padding-x-lg !default;\n$custom-select-font-size-lg: $input-font-size-lg !default;\n$custom-select-height-lg: $input-height-lg !default;\n\n$custom-range-track-width: 100% !default;\n$custom-range-track-height: 0.5rem !default;\n$custom-range-track-cursor: pointer !default;\n$custom-range-track-bg: $gray-300 !default;\n$custom-range-track-border-radius: 1rem !default;\n$custom-range-track-box-shadow: inset 0 0.25rem 0.25rem rgba($black, 0.1) !default;\n\n$custom-range-thumb-width: 1rem !default;\n$custom-range-thumb-height: $custom-range-thumb-width !default;\n$custom-range-thumb-bg: $component-active-bg !default;\n$custom-range-thumb-border: 0 !default;\n$custom-range-thumb-border-radius: 1rem !default;\n$custom-range-thumb-box-shadow: 0 0.1rem 0.25rem rgba($black, 0.1) !default;\n$custom-range-thumb-focus-box-shadow: 0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width: $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg: lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg: $gray-500 !default;\n\n$custom-file-height: $input-height !default;\n$custom-file-height-inner: $input-height-inner !default;\n$custom-file-focus-border-color: $input-focus-border-color !default;\n$custom-file-focus-box-shadow: $input-focus-box-shadow !default;\n$custom-file-disabled-bg: $input-disabled-bg !default;\n\n$custom-file-padding-y: $input-padding-y !default;\n$custom-file-padding-x: $input-padding-x !default;\n$custom-file-line-height: $input-line-height !default;\n$custom-file-font-family: $input-font-family !default;\n$custom-file-font-weight: $input-font-weight !default;\n$custom-file-color: $input-color !default;\n$custom-file-bg: $input-bg !default;\n$custom-file-border-width: $input-border-width !default;\n$custom-file-border-color: $input-border-color !default;\n$custom-file-border-radius: $input-border-radius !default;\n$custom-file-box-shadow: $input-box-shadow !default;\n$custom-file-button-color: $custom-file-color !default;\n$custom-file-button-bg: $input-group-addon-bg !default;\n$custom-file-text: (\n  en: 'Browse',\n) !default;\n\n// Form validation\n\n$form-feedback-margin-top: $form-text-margin-top !default;\n$form-feedback-font-size: $small-font-size !default;\n$form-feedback-valid-color: theme-color('success') !default;\n$form-feedback-invalid-color: theme-color('danger') !default;\n\n$form-feedback-icon-valid-color: $form-feedback-valid-color !default;\n$form-feedback-icon-valid: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$form-feedback-icon-invalid-color: $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$form-feedback-icon-invalid-color}' viewBox='-2 -2 7 7'%3e%3cpath stroke='#{$form-feedback-icon-invalid-color}' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\"),\n  '#',\n  '%23'\n) !default;\n\n$form-validation-states: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    'valid': (\n      'color': $form-feedback-valid-color,\n      'icon': $form-feedback-icon-valid,\n    ),\n    'invalid': (\n      'color': $form-feedback-invalid-color,\n      'icon': $form-feedback-icon-invalid,\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown: 1000 !default;\n$zindex-sticky: 1020 !default;\n$zindex-fixed: 1030 !default;\n$zindex-modal-backdrop: 1040 !default;\n$zindex-modal: 1050 !default;\n$zindex-popover: 1060 !default;\n$zindex-tooltip: 1070 !default;\n\n// Navs\n\n$nav-link-padding-y: 0.5rem !default;\n$nav-link-padding-x: 1rem !default;\n$nav-link-disabled-color: $gray-600 !default;\n\n$nav-tabs-border-color: $gray-300 !default;\n$nav-tabs-border-width: $border-width !default;\n$nav-tabs-border-radius: $border-radius !default;\n$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color: $gray-700 !default;\n$nav-tabs-link-active-bg: $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius: $border-radius !default;\n$nav-pills-link-active-color: $component-active-color !default;\n$nav-pills-link-active-bg: $component-active-bg !default;\n\n$nav-divider-color: $gray-200 !default;\n$nav-divider-margin-y: calc($spacer / 2) !default;\n\n// Navbar\n\n$navbar-padding-y: calc($spacer / 2) !default;\n$navbar-padding-x: $spacer !default;\n\n$navbar-nav-link-padding-x: 0.5rem !default;\n\n$navbar-brand-font-size: $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height: $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height: $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y: calc(($nav-link-height - $navbar-brand-height) / 2) !default;\n\n$navbar-toggler-padding-y: 0.25rem !default;\n$navbar-toggler-padding-x: 0.75rem !default;\n$navbar-toggler-font-size: $font-size-lg !default;\n$navbar-toggler-border-radius: $btn-border-radius !default;\n\n$navbar-dark-color: rgba($white, 0.5) !default;\n$navbar-dark-hover-color: rgba($white, 0.75) !default;\n$navbar-dark-active-color: $white !default;\n$navbar-dark-disabled-color: rgba($white, 0.25) !default;\n$navbar-dark-toggler-icon-bg: str-replace(\n  url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$navbar-dark-toggler-border-color: rgba($white, 0.1) !default;\n\n$navbar-light-color: rgba($black, 0.5) !default;\n$navbar-light-hover-color: rgba($black, 0.7) !default;\n$navbar-light-active-color: rgba($black, 0.9) !default;\n$navbar-light-disabled-color: rgba($black, 0.3) !default;\n$navbar-light-toggler-icon-bg: str-replace(\n  url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$navbar-light-toggler-border-color: rgba($black, 0.1) !default;\n\n$navbar-light-brand-color: $navbar-light-active-color !default;\n$navbar-light-brand-hover-color: $navbar-light-active-color !default;\n$navbar-dark-brand-color: $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color: $navbar-dark-active-color !default;\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width: 10rem !default;\n$dropdown-padding-y: 0.5rem !default;\n$dropdown-spacer: 0.125rem !default;\n$dropdown-font-size: $font-size-base !default;\n$dropdown-color: $body-color !default;\n$dropdown-bg: $white !default;\n$dropdown-border-color: rgba($black, 0.15) !default;\n$dropdown-border-radius: $border-radius !default;\n$dropdown-border-width: $border-width !default;\n$dropdown-inner-border-radius: calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default;\n$dropdown-divider-bg: $gray-200 !default;\n$dropdown-divider-margin-y: $nav-divider-margin-y !default;\n$dropdown-box-shadow: 0 0.5rem 1rem rgba($black, 0.175) !default;\n\n$dropdown-link-color: $gray-900 !default;\n$dropdown-link-hover-color: darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg: $gray-100 !default;\n\n$dropdown-link-active-color: $component-active-color !default;\n$dropdown-link-active-bg: $component-active-bg !default;\n\n$dropdown-link-disabled-color: $gray-600 !default;\n\n$dropdown-item-padding-y: 0.25rem !default;\n$dropdown-item-padding-x: 1.5rem !default;\n\n$dropdown-header-color: $gray-600 !default;\n\n// Pagination\n\n$pagination-padding-y: 0.5rem !default;\n$pagination-padding-x: 0.75rem !default;\n$pagination-padding-y-sm: 0.25rem !default;\n$pagination-padding-x-sm: 0.5rem !default;\n$pagination-padding-y-lg: 0.75rem !default;\n$pagination-padding-x-lg: 1.5rem !default;\n$pagination-line-height: 1.25 !default;\n\n$pagination-color: $link-color !default;\n$pagination-bg: $white !default;\n$pagination-border-width: $border-width !default;\n$pagination-border-color: $gray-300 !default;\n\n$pagination-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$pagination-focus-outline: 0 !default;\n\n$pagination-hover-color: $link-hover-color !default;\n$pagination-hover-bg: $gray-200 !default;\n$pagination-hover-border-color: $gray-300 !default;\n\n$pagination-active-color: $component-active-color !default;\n$pagination-active-bg: $component-active-bg !default;\n$pagination-active-border-color: $pagination-active-bg !default;\n\n$pagination-disabled-color: $gray-600 !default;\n$pagination-disabled-bg: $white !default;\n$pagination-disabled-border-color: $gray-300 !default;\n\n// Jumbotron\n\n$jumbotron-padding: 2rem !default;\n$jumbotron-color: null !default;\n$jumbotron-bg: $gray-200 !default;\n\n// Cards\n\n$card-spacer-y: 0.75rem !default;\n$card-spacer-x: 1.25rem !default;\n$card-border-width: $border-width !default;\n$card-border-radius: $border-radius !default;\n$card-border-color: rgba($black, 0.125) !default;\n$card-inner-border-radius: calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg: rgba($black, 0.03) !default;\n$card-cap-color: null !default;\n$card-color: null !default;\n$card-bg: $white !default;\n\n$card-img-overlay-padding: 1.25rem !default;\n\n$card-group-margin: calc($grid-gutter-width / 2) !default;\n$card-deck-margin: $card-group-margin !default;\n\n$card-columns-count: 3 !default;\n$card-columns-gap: 1.25rem !default;\n$card-columns-margin: $card-spacer-y !default;\n\n// Tooltips\n\n$tooltip-font-size: $font-size-sm !default;\n$tooltip-max-width: 200px !default;\n$tooltip-color: $white !default;\n$tooltip-bg: $black !default;\n$tooltip-border-radius: $border-radius !default;\n$tooltip-opacity: 0.9 !default;\n$tooltip-padding-y: 0.25rem !default;\n$tooltip-padding-x: 0.5rem !default;\n$tooltip-margin: 0 !default;\n\n$tooltip-arrow-width: 0.8rem !default;\n$tooltip-arrow-height: 0.4rem !default;\n$tooltip-arrow-color: $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y: $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x: $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size: $tooltip-font-size !default;\n$form-feedback-tooltip-line-height: $line-height-base !default;\n$form-feedback-tooltip-opacity: $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size: $font-size-sm !default;\n$popover-bg: $white !default;\n$popover-max-width: 276px !default;\n$popover-border-width: $border-width !default;\n$popover-border-color: rgba($black, 0.2) !default;\n$popover-border-radius: $border-radius-lg !default;\n$popover-box-shadow: 0 0.25rem 0.5rem rgba($black, 0.2) !default;\n\n$popover-header-bg: darken($popover-bg, 3%) !default;\n$popover-header-color: $headings-color !default;\n$popover-header-padding-y: 0.5rem !default;\n$popover-header-padding-x: 0.75rem !default;\n\n$popover-body-color: $body-color !default;\n$popover-body-padding-y: $popover-header-padding-y !default;\n$popover-body-padding-x: $popover-header-padding-x !default;\n\n$popover-arrow-width: 1rem !default;\n$popover-arrow-height: 0.5rem !default;\n$popover-arrow-color: $popover-bg !default;\n\n$popover-arrow-outer-color: fade-in($popover-border-color, 0.05) !default;\n\n// Toasts\n\n$toast-max-width: 350px !default;\n$toast-padding-x: 0.75rem !default;\n$toast-padding-y: 0.25rem !default;\n$toast-font-size: 0.875rem !default;\n$toast-color: null !default;\n$toast-background-color: rgba($white, 0.85) !default;\n$toast-border-width: 1px !default;\n$toast-border-color: rgba(0, 0, 0, 0.1) !default;\n$toast-border-radius: 0.25rem !default;\n$toast-box-shadow: 0 0.25rem 0.75rem rgba($black, 0.1) !default;\n\n$toast-header-color: $gray-600 !default;\n$toast-header-background-color: rgba($white, 0.85) !default;\n$toast-header-border-color: rgba(0, 0, 0, 0.05) !default;\n\n// Badges\n\n$badge-font-size: 75% !default;\n$badge-font-weight: $font-weight-bold !default;\n$badge-padding-y: 0.25em !default;\n$badge-padding-x: 0.4em !default;\n$badge-border-radius: $border-radius !default;\n\n$badge-transition: $btn-transition !default;\n$badge-focus-width: $input-btn-focus-width !default;\n\n$badge-pill-padding-x: 0.6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius: 10rem !default;\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding: 1rem !default;\n\n$modal-dialog-margin: 0.5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height: $line-height-base !default;\n\n$modal-content-color: null !default;\n$modal-content-bg: $white !default;\n$modal-content-border-color: rgba($black, 0.2) !default;\n$modal-content-border-width: $border-width !default;\n$modal-content-border-radius: $border-radius-lg !default;\n$modal-content-box-shadow-xs: 0 0.25rem 0.5rem rgba($black, 0.5) !default;\n$modal-content-box-shadow-sm-up: 0 0.5rem 1rem rgba($black, 0.5) !default;\n\n$modal-backdrop-bg: $black !default;\n$modal-backdrop-opacity: 0.5 !default;\n$modal-header-border-color: $border-color !default;\n$modal-footer-border-color: $modal-header-border-color !default;\n$modal-header-border-width: $modal-content-border-width !default;\n$modal-footer-border-width: $modal-header-border-width !default;\n$modal-header-padding-y: 1rem !default;\n$modal-header-padding-x: 1rem !default;\n$modal-header-padding: $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl: 1140px !default;\n$modal-lg: 800px !default;\n$modal-md: 500px !default;\n$modal-sm: 300px !default;\n\n$modal-fade-transform: translate(0, -50px) !default;\n$modal-show-transform: none !default;\n$modal-transition: transform 0.3s ease-out !default;\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y: 0.75rem !default;\n$alert-padding-x: 1.25rem !default;\n$alert-margin-bottom: 1rem !default;\n$alert-border-radius: $border-radius !default;\n$alert-link-font-weight: $font-weight-bold !default;\n$alert-border-width: $border-width !default;\n\n$alert-bg-level: -10 !default;\n$alert-border-level: -9 !default;\n$alert-color-level: 6 !default;\n\n// Progress bars\n\n$progress-height: 1rem !default;\n$progress-font-size: $font-size-base * 0.75 !default;\n$progress-bg: $gray-200 !default;\n$progress-border-radius: $border-radius !default;\n$progress-box-shadow: inset 0 0.1rem 0.1rem rgba($black, 0.1) !default;\n$progress-bar-color: $white !default;\n$progress-bar-bg: theme-color('primary') !default;\n$progress-bar-animation-timing: 1s linear infinite !default;\n$progress-bar-transition: width 0.6s ease !default;\n\n// List group\n\n$list-group-color: null !default;\n$list-group-bg: $white !default;\n$list-group-border-color: rgba($black, 0.125) !default;\n$list-group-border-width: $border-width !default;\n$list-group-border-radius: $border-radius !default;\n\n$list-group-item-padding-y: 0.75rem !default;\n$list-group-item-padding-x: 1.25rem !default;\n\n$list-group-hover-bg: $gray-100 !default;\n$list-group-active-color: $component-active-color !default;\n$list-group-active-bg: $component-active-bg !default;\n$list-group-active-border-color: $list-group-active-bg !default;\n\n$list-group-disabled-color: $gray-600 !default;\n$list-group-disabled-bg: $list-group-bg !default;\n\n$list-group-action-color: $gray-700 !default;\n$list-group-action-hover-color: $list-group-action-color !default;\n\n$list-group-action-active-color: $body-color !default;\n$list-group-action-active-bg: $gray-200 !default;\n\n// Image thumbnails\n\n$thumbnail-padding: 0.25rem !default;\n$thumbnail-bg: $body-bg !default;\n$thumbnail-border-width: $border-width !default;\n$thumbnail-border-color: $gray-300 !default;\n$thumbnail-border-radius: $border-radius !default;\n$thumbnail-box-shadow: 0 1px 2px rgba($black, 0.075) !default;\n\n// Figures\n\n$figure-caption-font-size: 90% !default;\n$figure-caption-color: $gray-600 !default;\n\n// Breadcrumbs\n\n$breadcrumb-padding-y: 0.75rem !default;\n$breadcrumb-padding-x: 1rem !default;\n$breadcrumb-item-padding: 0.5rem !default;\n\n$breadcrumb-margin-bottom: 1rem !default;\n\n$breadcrumb-bg: $gray-200 !default;\n$breadcrumb-divider-color: $gray-600 !default;\n$breadcrumb-active-color: $gray-600 !default;\n$breadcrumb-divider: quote('/') !default;\n\n$breadcrumb-border-radius: $border-radius !default;\n\n// Carousel\n\n$carousel-control-color: $white !default;\n$carousel-control-width: 15% !default;\n$carousel-control-opacity: 0.5 !default;\n$carousel-control-hover-opacity: 0.9 !default;\n$carousel-control-transition: opacity 0.15s ease !default;\n\n$carousel-indicator-width: 30px !default;\n$carousel-indicator-height: 3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer: 3px !default;\n$carousel-indicator-active-bg: $white !default;\n$carousel-indicator-transition: opacity 0.6s ease !default;\n\n$carousel-caption-width: 70% !default;\n$carousel-caption-color: $white !default;\n\n$carousel-control-icon-width: 20px !default;\n\n$carousel-control-prev-icon-bg: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n$carousel-control-next-icon-bg: str-replace(\n  url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e\"),\n  '#',\n  '%23'\n) !default;\n\n$carousel-transition-duration: 0.6s !default;\n$carousel-transition: transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n// Spinners\n\n$spinner-width: 2rem !default;\n$spinner-height: $spinner-width !default;\n$spinner-border-width: 0.25em !default;\n\n$spinner-width-sm: 1rem !default;\n$spinner-height-sm: $spinner-width-sm !default;\n$spinner-border-width-sm: 0.2em !default;\n\n// Close\n\n$close-font-size: $font-size-base * 1.5 !default;\n$close-font-weight: $font-weight-bold !default;\n$close-color: $black !default;\n$close-text-shadow: 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size: 87.5% !default;\n$code-color: $pink !default;\n\n$kbd-padding-y: 0.2rem !default;\n$kbd-padding-x: 0.4rem !default;\n$kbd-font-size: $code-font-size !default;\n$kbd-color: $white !default;\n$kbd-bg: $gray-900 !default;\n\n$pre-color: $gray-900 !default;\n$pre-scrollable-max-height: 340px !default;\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n\n// Printing\n\n$print-page-size: a3 !default;\n$print-body-min-width: map-get($grid-breakpoints, 'lg') !default;\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns(\n  $columns: $grid-columns,\n  $gutter: $grid-gutter-width,\n  $breakpoints: $grid-breakpoints\n) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    padding-right: calc($gutter / 2);\n    padding-left: calc($gutter / 2);\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    // Allow columns to stretch full width below their breakpoints\n    @for $i from 1 through $columns {\n      .col#{$infix}-#{$i} {\n        @extend %grid-column;\n      }\n    }\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n      .col#{$infix}-auto {\n        flex: 0 0 auto;\n        width: auto;\n        max-width: 100%; // Reset earlier grid tiers\n      }\n\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @include make-col($i, $columns);\n        }\n      }\n\n      .order#{$infix}-first {\n        order: -1;\n      }\n\n      .order#{$infix}-last {\n        order: $columns + 1;\n      }\n\n      @for $i from 0 through $columns {\n        .order#{$infix}-#{$i} {\n          order: $i;\n        }\n      }\n\n      // `$columns - 1` because offsetting by the width of an entire row isn't possible\n      @for $i from 0 through ($columns - 1) {\n        @if not($infix == '' and $i == 0) {\n          // Avoid emitting useless .offset-0\n          .offset#{$infix}-#{$i} {\n            @include make-col-offset($i, $columns);\n          }\n        }\n      }\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Utilities for common `display` values\n//\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $value in $displays {\n      .d#{$infix}-#{$value} {\n        display: $value !important;\n      }\n    }\n  }\n}\n\n//\n// Utilities for toggling `display` in print\n//\n\n@media print {\n  @each $value in $displays {\n    .d-print-#{$value} {\n      display: $value !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Flex variation\n//\n// Custom styles for additional flex alignment options.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .flex#{$infix}-row {\n      flex-direction: row !important;\n    }\n    .flex#{$infix}-column {\n      flex-direction: column !important;\n    }\n    .flex#{$infix}-row-reverse {\n      flex-direction: row-reverse !important;\n    }\n    .flex#{$infix}-column-reverse {\n      flex-direction: column-reverse !important;\n    }\n\n    .flex#{$infix}-wrap {\n      flex-wrap: wrap !important;\n    }\n    .flex#{$infix}-nowrap {\n      flex-wrap: nowrap !important;\n    }\n    .flex#{$infix}-wrap-reverse {\n      flex-wrap: wrap-reverse !important;\n    }\n    .flex#{$infix}-fill {\n      flex: 1 1 auto !important;\n    }\n    .flex#{$infix}-grow-0 {\n      flex-grow: 0 !important;\n    }\n    .flex#{$infix}-grow-1 {\n      flex-grow: 1 !important;\n    }\n    .flex#{$infix}-shrink-0 {\n      flex-shrink: 0 !important;\n    }\n    .flex#{$infix}-shrink-1 {\n      flex-shrink: 1 !important;\n    }\n\n    .justify-content#{$infix}-start {\n      justify-content: flex-start !important;\n    }\n    .justify-content#{$infix}-end {\n      justify-content: flex-end !important;\n    }\n    .justify-content#{$infix}-center {\n      justify-content: center !important;\n    }\n    .justify-content#{$infix}-between {\n      justify-content: space-between !important;\n    }\n    .justify-content#{$infix}-around {\n      justify-content: space-around !important;\n    }\n\n    .align-items#{$infix}-start {\n      align-items: flex-start !important;\n    }\n    .align-items#{$infix}-end {\n      align-items: flex-end !important;\n    }\n    .align-items#{$infix}-center {\n      align-items: center !important;\n    }\n    .align-items#{$infix}-baseline {\n      align-items: baseline !important;\n    }\n    .align-items#{$infix}-stretch {\n      align-items: stretch !important;\n    }\n\n    .align-content#{$infix}-start {\n      align-content: flex-start !important;\n    }\n    .align-content#{$infix}-end {\n      align-content: flex-end !important;\n    }\n    .align-content#{$infix}-center {\n      align-content: center !important;\n    }\n    .align-content#{$infix}-between {\n      align-content: space-between !important;\n    }\n    .align-content#{$infix}-around {\n      align-content: space-around !important;\n    }\n    .align-content#{$infix}-stretch {\n      align-content: stretch !important;\n    }\n\n    .align-self#{$infix}-auto {\n      align-self: auto !important;\n    }\n    .align-self#{$infix}-start {\n      align-self: flex-start !important;\n    }\n    .align-self#{$infix}-end {\n      align-self: flex-end !important;\n    }\n    .align-self#{$infix}-center {\n      align-self: center !important;\n    }\n    .align-self#{$infix}-baseline {\n      align-self: baseline !important;\n    }\n    .align-self#{$infix}-stretch {\n      align-self: stretch !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Mar<PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} {\n          #{$prop}: $length !important;\n        }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} {\n          margin: -$length !important;\n        }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto {\n      margin: auto !important;\n    }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n    }\n  }\n}\n", "$breakpoint-sm: 576px;\n$breakpoint-md: 767px;\n$breakpoint-lg: 992px;\n$breakpoint-xl: 1200px;\n\n.mobile {\n  display: none;\n  @media (max-width: $breakpoint-md) {\n    display: block;\n  }\n}\n// NOTE Currently tablet and mobile is the same. Change to $breakpoint-lg later if we get a proper tablet design.\n.desktop {\n  display: none;\n  @media (min-width: $breakpoint-md) {\n    display: block;\n  }\n}\n", "@font-face {\r\n  font-family: 'bariol_regular';\r\n  font-display: swap;\r\n  src: url('../assets/fonts/bariol_regular-webfont.woff') format('woff');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'bariol_bold';\r\n  font-display: swap;\r\n  src: url('../assets/fonts/bariol_bold-webfont.woff') format('woff');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'bariol_light';\r\n  font-display: swap;\r\n  src: url('../assets/fonts/bariol_light-webfont.woff') format('woff');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'bariol_thin';\r\n  font-display: swap;\r\n  src: url('../assets/fonts/bariol_thin-webfont.woff') format('woff');\r\n}\r\n", ".loading {\n  position: fixed;\n  z-index: 999;\n  height: 64px;\n  width: 64px;\n  overflow: visible;\n  margin: auto;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n}\n\n/* Transparent Overlay */\n\n.loading:before {\n  content: '';\n  display: block;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(243, 243, 243, 0.7);\n}\n\n.splashSpinner {\n  width: 100px;\n  margin: auto;\n  margin-top: 300px;\n}\n\n/*!\n * Load Awesome v1.1.0 (http://github.danielcardoso.net/load-awesome/)\n * Copyright 2015 <PERSON> <@DanielCardoso>\n * Licensed under MIT\n */\n.la-ball-clip-rotate,\n.la-ball-clip-rotate > div {\n  position: relative;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.la-ball-clip-rotate {\n  display: block;\n  font-size: 0;\n  color: #3f51b5;\n}\n.la-ball-clip-rotate.la-dark {\n  color: #333;\n}\n.la-ball-clip-rotate > div {\n  display: inline-block;\n  float: none;\n  background-color: currentColor;\n  border: 0 solid currentColor;\n}\n.la-ball-clip-rotate {\n  width: 32px;\n  height: 32px;\n}\n.la-ball-clip-rotate > div {\n  width: 32px;\n  height: 32px;\n  background: transparent;\n  border-width: 2px;\n  border-bottom-color: transparent;\n  border-radius: 100%;\n  -webkit-animation: ball-clip-rotate 0.75s linear infinite;\n  -moz-animation: ball-clip-rotate 0.75s linear infinite;\n  -o-animation: ball-clip-rotate 0.75s linear infinite;\n  animation: ball-clip-rotate 0.75s linear infinite;\n}\n.la-ball-clip-rotate.la-sm {\n  width: 16px;\n  height: 16px;\n}\n.la-ball-clip-rotate.la-sm > div {\n  width: 16px;\n  height: 16px;\n  border-width: 1px;\n}\n.la-ball-clip-rotate.la-2x {\n  width: 64px;\n  height: 64px;\n}\n.la-ball-clip-rotate.la-2x > div {\n  width: 64px;\n  height: 64px;\n  border-width: 4px;\n}\n.la-ball-clip-rotate.la-3x {\n  width: 96px;\n  height: 96px;\n}\n.la-ball-clip-rotate.la-3x > div {\n  width: 96px;\n  height: 96px;\n  border-width: 6px;\n}\n/*\n  * Animation\n  */\n@-webkit-keyframes ball-clip-rotate {\n  0% {\n    -webkit-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  50% {\n    -webkit-transform: rotate(180deg);\n    transform: rotate(180deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-moz-keyframes ball-clip-rotate {\n  0% {\n    -moz-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  50% {\n    -moz-transform: rotate(180deg);\n    transform: rotate(180deg);\n  }\n  100% {\n    -moz-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-o-keyframes ball-clip-rotate {\n  0% {\n    -o-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  50% {\n    -o-transform: rotate(180deg);\n    transform: rotate(180deg);\n  }\n  100% {\n    -o-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@keyframes ball-clip-rotate {\n  0% {\n    -webkit-transform: rotate(0deg);\n    -moz-transform: rotate(0deg);\n    -o-transform: rotate(0deg);\n    transform: rotate(0deg);\n  }\n  50% {\n    -webkit-transform: rotate(180deg);\n    -moz-transform: rotate(180deg);\n    -o-transform: rotate(180deg);\n    transform: rotate(180deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    -moz-transform: rotate(360deg);\n    -o-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n", "@import 'cashless-breakpoints';\n\n// Primary colours\n\n$blue-1: #1ea3ce;\n$blue-2: #4754b0;\n$blue-3: #144cdc;\n\n$red-1: #f14762;\n$red-2: #ffebeb;\n$red-3: #c04545;\n$red-4: #ffcfcc;\n\n$purple-1: #7f3db3;\n$navy-1: #1c4270;\n$charcoal-1: #333b44;\n\n$green-1: #00ba6b;\n$green-2: #d9f5e9;\n$green-3: #006f49;\n$green-4: #e3f5ef;\n$green-5: #dffff0;\n\n// Orange\n$orange-1: #ff9e00;\n$orange-3: #ff7a00;\n$orange-2: #ff4b17;\n$orange-4: #ffe2c7;\n$orange-5: #fff2e6;\n$orange-6: #ff8000;\n$orange-7: #ffead6;\n$orange-8: #fef0e0;\n$orange-9: #fff0e0;\n$orange-10: #f36600;\n$orange-11: #ffe3bb;\n$mobile-dark-orange: #D95B00;\n$mobile-light-orange: #FFEAD6;\n\n// grey\n$grey-1: #88949f;\n$grey-2: #e0e1e2;\n$grey-3: #dddddd;\n$grey-4: #f2f2f2;\n$grey-5: #333b44;\n$grey-6: #e5e5e5;\n$grey-7: #b9b9c8;\n$grey-8: #878787;\n$grey-9: #e0e0e0;\n$grey-10: #bdbdbd;\n$grey-11: #828282;\n$grey-12: #1b1f39;\n$grey-13: #b8b8b8;\n$grey-14: #272c50;\n$grey-15: #f6f5f3;\n$grey-16: #faf9f8;\n$grey-17: #6b6c89;\n\n// Secondary colours\n$blue-secondary-1: rgba(255, 243, 219, 1);\n"], "names": [], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}