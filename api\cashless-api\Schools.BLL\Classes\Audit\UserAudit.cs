﻿using System;
using System.Collections.Generic;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Audit;

/// <summary>
/// User audit Types Emum
/// </summary>
public enum TypeUserAuditLogEnum
{
    Order,
    Child,
    Account,
    Payment
}

/// <summary>
/// Child SubType Enum
/// </summary>
public enum ChildSubTypeLogEnum
{
    Add,
    Edit,
    Archive
}

/// <summary>
/// Account SubType Enum
/// </summary>
public enum AccountSubTypeLogEnum
{
    Register,
    Login,
    Delete,
    Edit,
    EditRole
}

/// <summary>
/// Base User Audit class
/// </summary>
public abstract class UserAuditLog : BaseAuditLog
{
    public long? TargetUserId { get; set; }
    public long? OrderId { get; set; }

    public UserAuditLog(TypeUserAuditLogEnum type) : base()
    {
        this.LogType = type.ToString();
    }
}

/// <summary>
/// Payment log
/// </summary>
public class PaymentAuditLog : BaseAuditLog
{
    public long PaymentFromUserId { get; set; }
    public long PaymentToUserId { get; set; }
    public decimal PaymentAmount { get; set; }
    public long? OrderId { get; set; }

    public PaymentAuditLog(PaymentTypeEnum paymentType) : base()
    {
        this.LogType = TypeUserAuditLogEnum.Payment.ToString();
        this.LogSubType = paymentType.ToString();
    }
}

/// <summary>
/// Child log
/// </summary>
public class ChildAuditLog : UserAuditLog
{
    public ChildAuditLog(ChildSubTypeLogEnum type) : base(TypeUserAuditLogEnum.Child)
    {
        this.LogSubType = type.ToString();
    }
}

/// <summary>
/// Account log
/// </summary>
public class AccountAuditLog : UserAuditLog
{
    public AccountAuditLog(AccountSubTypeLogEnum type) : base(TypeUserAuditLogEnum.Account)
    {
        this.LogSubType = type.ToString();
    }
}