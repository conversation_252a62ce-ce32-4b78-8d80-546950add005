<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<rect x="8" y="6" width="56" height="56" rx="28" fill="white"/>
<rect x="8.5" y="6.5" width="55" height="55" rx="27.5" stroke="#DDDDDD"/>
</g>
<path d="M26 43.9994L45.9999 24.3843" stroke="#FF7A00" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26 24.0001L45.9999 43.6152" stroke="#FF7A00" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_d" x="0" y="0" width="72" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
