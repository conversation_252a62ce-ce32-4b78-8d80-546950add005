﻿using System;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers
{
    /// <summary>
    /// Sample exception
    /// </summary>
    public class SampleException : Exception
    {
        public SampleException() : base() { }
        public SampleException(string message) : base(message) { }
    }

    /// <summary>
    /// Controller for handling error. This is not included in Swagger docs
    /// </summary>
    [AllowAnonymous]
    [Route("api/[controller]")]
    [ApiController]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class ErrorController : Controller
    {
        private readonly IUserService userService;
        private readonly ITelemetryService telemetryService;
        private readonly ILogger<ErrorController> logger;

        public ErrorController(IUserService userService, ITelemetryService telemetryService, ILogger<ErrorController> logger)
        {
            this.userService = userService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <summary>
        /// Throw an exception to simulate application errors and and test 
        /// the exception processing chain  
        /// </summary>
        [Route("ThrowError")]
        public IActionResult ThrowError()
        {
            logger.LogDebug("Throwing sample exception!");

            throw new SampleException($"This is a sample exception thrown at {DateTime.Now}");
        }

        /// <summary>
        /// Check the user's role is admin or merchant
        /// </summary>
        [Authorize]
        [Route("CheckRole")]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> CheckRole()
        {
            var user = await this.userService.GetCurrentUser();

            logger.LogDebug("Current user {User} and their role checks out!", user);

            return new OkResult();
        }

        /// <summary>
        /// Error handling middleware registered in the startup class:             
        ///     app.UseExceptionHandler("/api/Error/HandleError");
        ///     
        /// This has limitations in that it may not have the full context
        /// of the request and controller where the error occured
        /// </summary>
        [Route("HandleError")]
        [Obsolete("Not used due to limitations around fetching request parameters & controller info. Please use one of the filters instead")]
        public IActionResult HandleError()
        {
            // Fetch the error encountered
            var exceptionHandler = HttpContext.Features.Get<IExceptionHandlerFeature>()!;
            var exception = exceptionHandler?.Error;
            var path = exceptionHandler?.Path;

            // Get the request parameters. Note the request path returns 
            // the URI to the error handler!
            //
            // The remaining attributes such as query string and method
            // appear to be passed to the error handler.
            var ip = HttpContext.GetRequestIp();
            var userId = HttpContext.User?.Identity.GetId();
            var method = HttpContext.Request.Method;
            var queryString = HttpContext.Request.QueryString;

            logger.LogError("Error {Method} {Path} - User: {UserId} | IP: {IP} | Exception: {Exception}",
                            method, path + queryString, userId, ip, exception);

            // Log exception in Application Insights
            if (exception != null)
            {
                telemetryService.TrackException(exception);
            }

            // Return a HTTP 500 error
            return StatusCode(500);
        }
    }
}
