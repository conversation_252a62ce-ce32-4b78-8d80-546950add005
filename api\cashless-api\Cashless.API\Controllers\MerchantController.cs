﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Helpers;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Canteens;
using Schools.BLL.Classes.Invoices;
using Schools.BLL.Exceptions;
using Schools.DAL.Entities;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [CheckUserRole(UserRole.Admin)]
    [ApiController]
    public class MerchantController : ControllerBase
    {
        private readonly IUserService userService;
        private readonly ICanteenService canteenService;
        private readonly ISchoolService schoolService;
        private readonly ISchoolMerchantService schoolMerchantService;
        private readonly IMerchantValidator merchantValidator;
        private readonly IInvoiceService invoiceService;

        public MerchantController(IUserService userService, ICanteenService canteenService,
                                    ISchoolService schoolService, ISchoolMerchantService schoolMerchantService,
                                    IMerchantValidator merchantValidator, IInvoiceService invoiceService)
        {
            this.userService = userService;
            this.canteenService = canteenService;
            this.schoolService = schoolService;
            this.schoolMerchantService = schoolMerchantService;
            this.merchantValidator = merchantValidator;
            this.invoiceService = invoiceService;
        }

        /// <summary>
        /// Get all merchants
        /// </summary>
        /// <returns></returns>
        [Route("")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetAll()
        {
            // get merchants
            List<GetAllMerchantResponse> res = await this.canteenService.GetAllMerchants();

            if (res == null)
            {
                return new NotFoundResult();
            }

            return new OkObjectResult(res);
        }

        [Route("")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> CreateMerchant([FromBody] CreateMerchantRequest request)
        {
            long canteenId = await this.canteenService.CreateMerchant(request);

            return new OkObjectResult(canteenId);
        }

        [Route("MoveMerchantFromStripeToSCore/{userId}")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> MoveMerchantFromStripeToSCore(int userId)
        {
            string message = await userService.MoveMerchantFromStripeToSCore(userId);

            return new OkObjectResult(message);
        }

        [Route("MoveParentFromStripeToSCore/{userId}")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> MoveParentFromStripeToSCore(int userId)
        {
            string message = await userService.MoveParentFromStripeToSCore(userId);

            return new OkObjectResult(message);
        }

        /// <summary>
        /// Return the list of users with a canteen Role or a parent role without children
        /// </summary>
        /// <param name="search"></param>
        /// <returns></returns>
        [Route("SearchUserForCreation/{search}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> SearchUserForCreation(string search)
        {
            List<User> res = await this.userService.GetCanteenUsersOrParentWithoutChild(search);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get merchant by id
        /// </summary>
        /// <param name="canteenId"></param>
        /// <returns></returns>
        [Route("{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(statusCode: 500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> Get(int canteenId)
        {
            await this.merchantValidator.ValidateAccessToCanteen(canteenId);

            // get merchant
            GetMerchantResponse res = await this.canteenService.GetMerchantById(canteenId);

            if (res == null)
            {
                return new NotFoundResult();
            }

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Update merchant details
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("{canteenId}")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> EditMerchant(int canteenId, [FromBody] UpdateMerchantDetailsRequest request)
        {
            // update merchant details
            await this.canteenService.UpdateMerchantDetails(canteenId, request);

            return new OkResult();
        }

        /// <summary>
        /// Edit the owner
        /// </summary>
        [Route("{canteenId}/Contact")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> EditMerchantContact(int canteenId, [FromBody] UpdateMerchantOwnerRequest request)
        {
            // update owner
            await this.canteenService.UpdateMerchantOwner(canteenId, request);

            return new OkResult();
        }

        /// <summary>
        /// Update merchant fee information
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("{canteenId}/Fee")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> Update(int canteenId, [FromBody] UpdateMerchantFeeRequest request)
        {
            // check request
            if (String.IsNullOrEmpty(request.MerchantFeeStartDate) || request.MerchantFee == null ||
                request.ParentFeeCoveredByMerchants == null || request.MerchantFee < 0 || request.ParentFeeCoveredByMerchants < 0)
            {
                throw new ValidationException("Invalid request");
            }

            // get merchant
            GetMerchantResponse res = await this.canteenService.GetMerchantById(canteenId);

            if (res == null)
            {
                return new NotFoundResult();
            }
            else
            {
                // update fee information
                await this.canteenService.UpdateMerchantFee(canteenId, request);
            }

            return new OkResult();
        }

        /// <summary>
        /// Return the list of schools that can be link to the given merchant
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="search"></param>
        /// <returns></returns>
        [Route("{canteenId}/schools/unlinked/{search}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> SearchSchoolForMerchant(int canteenId, string search)
        {
            // get schools unlinked list
            List<School> res = await this.canteenService.SearchSchoolsForMerchant(canteenId, search);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Return the schools currently linked to the given merchant
        /// </summary>
        [Route("{canteenId}/schools")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> MerchantSchools(int canteenId)
        {
            // Get schools associated with this merchant. This will return 
            // signed, active and churned schools
            BillingStatusEnum[] billingStatuses = { BillingStatusEnum.Active, BillingStatusEnum.Churned };
            IEnumerable<SchoolWithBillingSettingsDto> res = await this.schoolService.GetSchoolsForMerchant(canteenId, billingStatuses);

            return new OkObjectResult(res);
        }

        [Route("{canteenId}/schools/active")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> MerchantActiveSchools(int canteenId)
        {
            // Get schools associated with this merchant. This will return 
            // signed and active schools
            BillingStatusEnum[] billingStatuses = { BillingStatusEnum.Active };
            IEnumerable<SchoolWithBillingSettingsDto> res = await this.schoolService.GetSchoolsForMerchant(canteenId, billingStatuses);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// link the school to the merchant
        /// </summary>
        [Route("{canteenId}/schools/{schoolId}/link")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> LinkSchoolToMerchant(int canteenId, int schoolId)
        {
            await this.canteenService.LinkSchoolToMerchant(schoolId, canteenId);

            return new OkResult();
        }

        /// <summary>
        /// unlink the school from the merchant.
        /// Also delete the existing permission
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="schoolId"></param>
        /// <returns></returns>
        [Route("{canteenId}/schools/{schoolId}/unlink")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> UnlinkSchoolFromMerchant(int canteenId, int schoolId)
        {
            await this.canteenService.UnlinkSchoolFromMerchant(schoolId, canteenId);

            return new OkResult();
        }

        /// <summary>
        /// Update School - Merchant settings
        /// </summary>
        [Route("{canteenId}/schools/{schoolId}/update")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> UpdateSchoolMerchantSettings(UpsertSchoolBillingDto request)
        {
            await this.canteenService.UpdateSchoolBillingSettings(request);

            return new OkResult();
        }

        /// <summary>
        /// Update School - Merchant settings
        /// </summary>
        [Route("{SchoolCanteenId}/update/status/{status}")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> UpdateSchoolMerchantStatus([FromRoute] long SchoolCanteenId, [FromRoute] InternalSchoolMerchantStatusEnum status)
        {
            await this.canteenService.UpdateSchoolBillingInternalStatus(SchoolCanteenId, status);

            return new OkResult();
        }

        /// <summary>
        /// Get the canteen users linked to the given canteenId
        /// </summary>
        /// <param name="canteenId"></param>
        /// <returns></returns>
        [Route("{canteenId}/users")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantUsers(int canteenId)
        {
            // get schools unlinked list
            IEnumerable<CanteenUser> res = await this.userService.GetCanteenUsers(canteenId);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get the users that we can add to the canteen
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="search"></param>
        /// <returns></returns>
        [Route("{canteenId}/users/search/{search}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantUsersSearch(int canteenId, string search)
        {
            // get schools unlinked list
            List<User> res = await this.userService.SearchUsersAvailableForCanteen(canteenId, search);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Add user permission to the canteen and schools
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("{canteenId}/users")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantAddUser(int canteenId, [FromBody] UpsertMerchantUserRequest request)
        {
            // add canteen user
            await this.canteenService.AddMerchantUser(canteenId, request);

            return new OkResult();
        }

        /// <summary>
        /// Get the user permission for the given canteen Id
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Route("{canteenId}/users/{userId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantGetUser(int canteenId, int userId)
        {
            // get user with permission
            CanteenUserWithPermission res = await this.canteenService.GetMerchantUserWithPermissions(userId, canteenId);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Edit User permission on the canteen and schools
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="userId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("{canteenId}/users/{userId}")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantEditUser(int canteenId, int userId, [FromBody] UpsertMerchantUserRequest request)
        {
            //check request
            if (userId != request.UserId) throw new ValidationException("userId", "Invalid request");

            // edit canteen user
            await this.canteenService.EditMerchantUser(canteenId, request);

            return new OkResult();
        }

        /// <summary>
        /// Remove User permission on the canteen and schools
        /// </summary>
        /// <param name="canteenId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Route("{canteenId}/users/{userId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(roles: UserRole.Admin)]
        public async Task<IActionResult> MerchantRemoveUser(int canteenId, int userId)
        {
            // remove canteen user permissions
            await this.canteenService.RemoveMerchantUser(canteenId, userId);

            return new OkResult();
        }

        /// <summary>
        /// Export the invoicing information for a given period in CSV format
        /// </summary>
        [Route("invoice/export")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> ExportInvoiceData(InvoiceExportRequestDto request)
        {
            // Fetch the records for the given period
            var records = await this.GetInvoiceData(request);

            // Write the data out in CSV format
            var csvConfig = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                Delimiter = ",",
                Encoding = Encoding.UTF8,
            };

            var filename = GetFilename((InvoiceExportTypeEnum)request.ExportType);

            // Gotcha - Using a "using" statement will dispose of this stream
            //          before the client is finished fetching the data
            var stream = new MemoryStream();

            using (var writer = new StreamWriter(stream, leaveOpen: true))
            {
                using (var csv = new CsvHelper.CsvWriter(writer, csvConfig))
                {
                    if (request.ExportType.Value == InvoiceExportTypeEnum.Invoice)
                    {
                        csv.Context.RegisterClassMap<InvoiceRecordDtoMap>();
                    }
                    else if (request.ExportType.Value == InvoiceExportTypeEnum.Settlement)
                    {
                        csv.Context.RegisterClassMap<SettlementRecordDtoMap>();
                    }
                    else if (request.ExportType.Value == InvoiceExportTypeEnum.Revenue)
                    {
                        csv.Context.RegisterClassMap<RevenueRecordDtoMap>();
                    }

                    csv.WriteRecords(records);
                }
            }

            // Reset the stream
            stream.Seek(0, SeekOrigin.Begin);

            return File(stream, "application/octet-stream", filename);
        }

        [Route("invoice")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetInvoices()
        {
            var res = await this.invoiceService.GetActiveInvoices();
            return new OkObjectResult(res);
        }

        [Route("invoice/generate")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GenerateInvoiceData(InvoiceExportRequestDto request)
        {
            await this.invoiceService.GenerateInvoice(request);
            return new OkResult();
        }

        [Route("invoice/exportv2")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> ExportInvoiceFile(InvoiceFileRequestDto request)
        {
            // Fetch the records for the given period
            var records = await this.invoiceService.GetInvoiceResults(request);

            // Write the data out in CSV format
            var csvConfig = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                Delimiter = ",",
                Encoding = Encoding.UTF8,
            };

            var filename = GetFilename(request.ExportType);

            // Gotcha - Using a "using" statement will dispose of this stream
            //          before the client is finished fetching the data
            var stream = new MemoryStream();

            using (var writer = new StreamWriter(stream, leaveOpen: true))
            {
                using (var csv = new CsvHelper.CsvWriter(writer, csvConfig))
                {
                    if (request.ExportType == InvoiceExportTypeEnum.Invoice)
                    {
                        csv.Context.RegisterClassMap<InvoiceRecordDtoMap>();
                    }
                    else if (request.ExportType == InvoiceExportTypeEnum.Settlement)
                    {
                        csv.Context.RegisterClassMap<SettlementRecordDtoMap>();
                    }
                    else if (request.ExportType == InvoiceExportTypeEnum.Revenue)
                    {
                        csv.Context.RegisterClassMap<RevenueRecordDtoMap>();
                    }

                    csv.WriteRecords(records);
                }
            }

            // Reset the stream
            stream.Seek(0, SeekOrigin.Begin);

            return File(stream, "application/octet-stream", filename);
        }

        /// <summary>
        /// Return the name of the export
        /// </summary>
        private string GetFilename(InvoiceExportTypeEnum exportType)
        {
            if (exportType == InvoiceExportTypeEnum.Invoice)
            {
                return "invoice-export.csv";
            }
            else if (exportType == InvoiceExportTypeEnum.Settlement)
            {
                return "settlement-export.csv";
            }

            return "revenue-export.csv";
        }

        /// <summary>
        /// Fetch settlement or invoice records
        /// </summary>
        private async Task<IEnumerable<object>> GetInvoiceData(InvoiceExportRequestDto request)
        {
            if (request.ExportType == InvoiceExportTypeEnum.Invoice)
            {
                return await this.schoolMerchantService.GetInvoiceData<InvoiceRecordDto>(request);
            }
            else if (request.ExportType == InvoiceExportTypeEnum.Settlement)
            {
                return await this.schoolMerchantService.GetInvoiceData<SettlementRecordDto>(request);
            }

            return await this.schoolMerchantService.GetInvoiceData<RevenueRecordDto>(request);
        }
    }
}
