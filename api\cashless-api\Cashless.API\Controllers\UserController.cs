using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders;
using Schools.BLL.Classes.Users;
using Schools.BLL.Exceptions;
using Schools.BLL.Helpers;
using Schools.DAL.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class UserController : ControllerBase
{
    private readonly ITelemetryService telemetryService;
    private readonly IDBHelper dbHelper;
    private readonly IScoreApiClient scoreApiClient;
    private readonly IUserService userService;
    private readonly IPaymentService2 _paymentService2;
    private readonly IUnitOfWork unitOfWork;
    private readonly IFirebaseService firebaseService;
    private readonly ISendGridService sendGridService;
    private readonly ICanteenService canteenService;
    private readonly ISchoolService schoolService;
    private readonly IUserValidator userValidator;
    private readonly ILogger<UserController> logger;

    public UserController(ITelemetryService telemetryService, IDBHelper dbHelper, IScoreApiClient scoreApiClient, IUserService userService,
                        IPaymentService2 paymentService2, IFirebaseService firebaseService, ISendGridService sendGridService, ICanteenService canteenService,
                        ISchoolService schoolService, IUserValidator userValidator, IUnitOfWork unitOfWork, ILogger<UserController> logger)
    {
        this.telemetryService = telemetryService;
        this.dbHelper = dbHelper;
        this.scoreApiClient = scoreApiClient;
        this.userService = userService;
        _paymentService2 = paymentService2;
        this.firebaseService = firebaseService;
        this.sendGridService = sendGridService;
        this.canteenService = canteenService;
        this.schoolService = schoolService;
        this.userValidator = userValidator;
        this.unitOfWork = unitOfWork;
        this.logger = logger;
    }

    [Route("GetUsersList/{schooldId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [Obsolete("No longer used by the web portal")]
    public IActionResult GetUsersList(string schooldId)
    {
        throw new DeprecatedApiException();
    }

    [Route("GetUsersDetails/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> GetUsersDetails(int userId)
    {
        User user = await this.unitOfWork.UserRepository.GetUserDetailsByUserId(userId);

        await this.userValidator.ValidateAccessToUser(user);

        return new OkObjectResult(user);
    }

    [Route("GetUserDetails/")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetUsersDetails()
    {
        // only returns details of currently authenticated user
        User user = await this.userService.GetCurrentUser();

        return new OkObjectResult(user);
    }

    /// <summary>
    /// This API call is used in the registration flow on the Web app. The app calls the 
    /// Firebase SDK to create a new account, and then calls this method to create the User 
    /// in the Schools platform.
    /// 
    /// This is also used to create and update Students. All new users start as parents. 
    /// </summary>
    [Route("UpsertUser")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRoleAndAllowNewUser(UserRole.Parent, UserRole.Admin)]
    // Remove calls to Upsert and use Create and Update endpoints instead
    [Obsolete]
    public async Task<IActionResult> UpsertUser([FromBody] User user)
    {
        User result = null;

        // Check userId incase duplicating creating customers
        if (user.UserId <= 0)
        {
            var userRequest = new CreateSchoolsUserRequest()
            {
                Allergies = user.Allergies,
                AllowCanteenToOrder = user.AllowCanteenToOrder,
                ClassId = user.ClassId,
                DateOfBirth = user.DateOfBirth,
                Email = user.Email,
                FavouriteColour = user.FavouriteColour,
                FirebaseUserId = user.FirebaseUserId,
                FirstName = user.FirstName,
                Lastname = user.Lastname,
                IsActive = user.IsActive,
                Mobile = user.Mobile,
                ParentId = user.ParentId,
                Role = Utils.StringToUserRole(user.Role),
                SchoolId = user.SchoolId,
                SpriggyUserId = user.SpriggyUserId
            };

            await this.userValidator.ValidateRequest(userRequest);

            result = await this.userService.CreateUser(userRequest);
        }
        else
        {
            var userRequest = new UpdateSchoolsUserRequest()
            {
                Allergies = user.Allergies,
                AllowCanteenToOrder = user.AllowCanteenToOrder,
                ClassId = user.ClassId,
                DateOfBirth = user.DateOfBirth,
                Email = user.Email,
                FavouriteColour = user.FavouriteColour,
                FirstName = user.FirstName,
                Lastname = user.Lastname,
                IsActive = user.IsActive,
                Mobile = user.Mobile,
                ParentId = user.ParentId,
                Role = Utils.StringToUserRole(user.Role),
                SchoolId = user.SchoolId,
                UserId = user.UserId
            };

            await this.userValidator.ValidateRequest(userRequest);

            result = await this.userService.UpdateUser(userRequest);
        }

        return new OkObjectResult(result);
    }

    /// <summary>
    /// Create a new User
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Route("")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRoleAndAllowNewUser(UserRole.Parent, UserRole.Admin)]
    public async Task<IActionResult> CreateUser(CreateSchoolsUserRequest request)
    {
        await this.userValidator.ValidateRequest(request);

        User result = await this.userService.CreateUser(request);

        return new OkObjectResult(result);
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Route("")]
    [HttpPut]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRoleAndAllowNewUser(UserRole.Parent, UserRole.Admin)]
    public async Task<IActionResult> UpdateUser(UpdateSchoolsUserRequest request)
    {
        await this.userValidator.ValidateRequest(request);

        User result = await this.userService.UpdateUser(request);

        return new OkObjectResult(result);
    }

    [Route("UpdateStudentClass")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> UpdateStudentClass([FromBody] User user)
    {
        // To Do: Replace request with StudentId and ClassId only -- no need for entire user object
        if (user == null || user.UserId < 1)
        {
            throw new ValidationException("Invalid request");
        }

        await this.userValidator.ValidateAccessToStudent(user.UserId);

        // Check if the class is Active
        if (user.UserId > 0 && user.ClassId > 0)
        {
            SchoolClass schoolClass = await this.schoolService.GetClassById(Convert.ToInt32(user.ClassId));

            user.NeedToUpdateClass = !schoolClass.IsActive;
        }

        await this.unitOfWork.UserRepository.UpdateUserClass(user);

        return new OkResult();
    }

    [Route("UpsertUserAudit")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [Obsolete("No longer used by the web portal")]
    public IActionResult UpsertUserAudit([FromBody] UserAuditRequest request)
    {
        throw new DeprecatedApiException();
    }

    /// <summary>
    /// Rebuild: Upsert student
    /// </summary>
    [Route("UpsertStudent")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [Obsolete("No longer used by the web portal")]
    public IActionResult UpsertStudent([FromBody] Student request)
    {
        throw new DeprecatedApiException();
    }

    [Route("RefundUser")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> RefundUser([FromBody] RefundUserRequest request)
    {
        CanteenEntity canteen = await this.canteenService.GetCanteenBySchoolIdAndType(request.SchoolId);
        User user = await this.userService.GetUserById(request.UserId);

        RefundResponse res = await _paymentService2.RefundUser(canteen, user, request.Amount, request.Message);

        if (res.UpdatedBalance == null)
        {
            throw new PaymentException("Refund failed. Balance returned was null");
        }

        return new OkObjectResult(res);
    }

    [Route("TransferUserBalance")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> TransferUserBalance([FromBody] TransferUserBalanceRequest request)
    {
        User fromUser = await this.userService.GetUserById(request.FromUserId);
        User toUser = await this.userService.GetUserById(request.ToUserId);

        this.userValidator.CheckTransferUserBalance(request, fromUser, toUser);

        TransferUserBalanceResponse res = await _paymentService2.TransferUserBalance(fromUser, toUser, request.Amount, request.Message);

        if (!String.IsNullOrEmpty(res.ErrorMessage))
        {
            throw new PaymentException(res.ErrorMessage);
        }

        return new OkObjectResult(res);
    }

    [Route("GetUsersWithFilters")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> GetUsersWithFilters([FromBody] ArrayFilter filter)
    {
        ListUsersResponseOld response = new();

        User user = await this.userService.GetCurrentUser();

        if (user.IsMerchant)
        {
            response.Users = await this.unitOfWork.UserRepository.GetAllStudentWithFilters(filter);
        }
        else
        {
            response.Users = await this.unitOfWork.UserRepository.GetAllUsersWithFilter(filter);
        }

        await this.userValidator.ValidateAccessToUsers(response.Users);

        return new OkObjectResult(response);
    }

    /// <summary>
    /// Retrieves list of canteen users by canteen ID
    /// </summary>
    /// <param name="canteenId">ID of the canteen</param>
    /// <returns></returns>
    [HttpGet("GetUsersByCanteen/{canteenId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> GetUsersByCanteen([FromRoute] long canteenId)
    {
        if (canteenId <= 0)
        {
            throw new ValidationException("Invalid request");
        }

        await this.userValidator.ValidateAccessToCanteen(canteenId);

        var listCanteenUsersResponse = new ListCanteenUsersResponse();

        listCanteenUsersResponse.CanteenUsers = await this.userService.GetCanteenUsers(canteenId);

        return new OkObjectResult(listCanteenUsersResponse);
    }

    [Route("ArchiveParentAccount")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> ArchiveParentAccount([FromBody] ArchiveUserRequest request)
    {
        // Transfer the balance to Refund Account
        var res = await _paymentService2.RefundAccountBalance(request.UserId);

        // Close parent account
        await this.userService.ArchiveUser(request.UserId, request.Reason);

        // Close children accounts
        List<EditStudentDto> children = await this.userService.GetStudentsByParentId(request.UserId);

        if (children != null && children.Count > 0)
        {
            children.ForEach(s =>
            {
                this.userService.ArchiveUser(s.UserId, "");
            });
        }

        var response = new ArchiveUserResponse()
        {
            BalanceRefunded = res.PaymentDetails.IsPaymentSuccess,
            Balance = res.AccountBalance.ToString(),
        };

        return new OkObjectResult(response);
    }

    [HttpPut("UpdateCanteenUserSettings")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Merchant)]
    public async Task<IActionResult> UpdateCanteenUserSettings([FromBody] CanteenUserSettingsRequest request)
    {
        // validate access to canteen
        this.userValidator.ValidateRequest(request);
        await this.userValidator.ValidateCurrentAndRequestedUserAtCanteen((long)request.CanteenId, request.UserId);

        // update permissions
        await this.canteenService.UpdateMerchantUserPermission(request);

        return new OkResult();
    }

    [Route("UserResetPasswordEmail")]
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> UserResetPasswordEmail([FromBody] ResetPasswordRequest request)
    {
        this.userValidator.ValidateRequest(request);

        await this.sendGridService.SendPasswordResetEmail(request.Email);

        return new OkResult();
    }

    #region

    /// <summary>
    /// Get user details. Only used in Web apps this is used in the auth flow only. 
    /// The User fetching their own details
    /// </summary>
    [Route("GetUser")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetCurrentUser([FromBody] UserRequest request)
    {
        await this.userValidator.ValidateRequest(request);

        User user = await this.userService.GetCurrentUserWithHMAC(false);

        return new OkObjectResult(user);
    }

    /// <summary>
    /// Rebuild: Edit user profile 
    /// </summary>
    [Route("UserUpdateProfile")]
    [Route("UserUpdateProfileMobile")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> UserUpdateProfileMobile([FromBody] UserUpdateProfile request)
    {
        // Check the logged on user is the user referenced in the request
        await this.userValidator.ValidateRequest(request);

        this.telemetryService.TrackTrace("UserUpdateProfile",
                            new Dictionary<string, object>() {
                                    {"request", request}
                            });

        await this.unitOfWork.UserRepository.UpsertProfile(request);

        return new OkObjectResult(request);
    }

    /// <summary>
    /// Check if the current parent should use the React Native build
    /// </summary>
    private async Task<bool> UseReactNativeBuild()
    {
        return true;
    }

    [Route("ShowReactNativeBuildMobile")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Parent)]
    public async Task<IActionResult> ShowReactNativeBuild()
    {
        var res = await UseReactNativeBuild();

        return new OkObjectResult(res);
    }

    /// <summary>
    /// This API call is used in the registration flow on the Mobile app. The app calls the 
    /// Firebase SDK to create a new account, and then calls this method to create the User 
    /// in the Schools platform.
    /// 
    /// All new users start as parents. 
    /// Below endpoint will create User including SCore Customer
    /// </summary>
    [Route("RegisterUser")]
    [Route("RegisterUserMobile")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [AllowAnonymous]
    [Obsolete]
    public async Task<IActionResult> RegisterUser([FromBody] CreateUserRequest request)
    {
        return await RegisterUser2(request);
    }

    // Below endpoint will create User including Stripe Customer
    [Route("RegisterUser2")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [AllowAnonymous]
    public async Task<IActionResult> RegisterUser2([FromBody] CreateUserRequest request)
    {
        // No auth checks to be done, this is a new user. Check the 
        // user is not already in the system?
        await this.userValidator.ValidateRequest(request);

        // Register the new User in Firebase, SCore/Stripe and Schools DB
        var result = await userService.RegisterUser2(request);

        return new OkObjectResult(result);
    }

    /// <summary>
    /// Update a student.. Not the same as UpsertStudent
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Route("UpsertStudentMobile")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Parent)]
    public async Task<IActionResult> UpsertStudentMobile([FromBody] Student request)
    {
        // Check the Student being updated belongs to the current Parent user
        await this.userValidator.ValidateRequest(request);

        // Log the request
        this.telemetryService.TrackTrace("upsert_student",
                            new Dictionary<string, object>() {
                                    {"request", request}
                            });

        // Check if the class needs to be updated
        SchoolClass schoolClass = await this.schoolService.GetClassById(Convert.ToInt32(request.ClassId));
        request.NeedToUpdateClass = !schoolClass.IsActive;

        // Update the student
        var studentToUpsert = JsonConvert.SerializeObject(request);
        var response = await this.unitOfWork.UserRepository.UpsertStudent(request);

        // Log the response
        this.telemetryService.TrackTrace("upsert_student",
                            new Dictionary<string, object>() {
                                    {"response", response}
                            });

        // Send back the updated student (TODO improve later)
        if (!String.IsNullOrEmpty(response))
        {
            request.UserId = Convert.ToInt32(response);
        }

        return new OkObjectResult(request);
    }

    #endregion

    /// <summary>
    /// Fetch the Account ID for a given user
    /// </summary>
    [Route("GetSpriggyAccountId/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> GetSpriggyAccountId(int userId)
    {
        var user = await this.userService.GetUserById(userId);
        var spriggyAccountId = await this.userService.GetSpriggyAccountId(user);

        return new OkObjectResult(new
        {
            SpriggyAccountId = spriggyAccountId,
        });
    }

    /// <summary>
    /// Update the Spriggy Account ID for a given User
    /// </summary>
    [Route("UpdateSpriggyAccountId/{userId}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> UpdateSpriggyAccountId(int userId)
    {
        var user = await this.userService.GetUserById(userId);
        var spriggyAccountId = await this.userService.SetSpriggyAccountId(user);

        return new OkObjectResult(new
        {
            SpriggyAccountId = spriggyAccountId,
        });
    }

    /// <summary>
    /// Get a given User's custom claims in Firebase
    /// </summary>
    [Route("GetCustomClaims/{userId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> GetCustomClaims(int userId)
    {
        var user = await this.userService.GetUserById(userId);
        var claims = await this.firebaseService.GetUserCustomClaims(user);

        return new OkObjectResult(claims);
    }

    /// <summary>
    /// Update a given User's custom claims in Firebase
    /// </summary>
    [Route("UpdateCustomClaims/{userId}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> UpdateCustomClaims(int userId)
    {
        var user = await this.userService.GetUserById(userId);
        await this.firebaseService.UpdateUserCustomClaims(user);
        var claims = await this.firebaseService.GetUserCustomClaims(user);

        return new OkObjectResult(claims);
    }

    [CheckUserRole(UserRole.Admin)]
    [Route("PopulateSpriggyAccounts/{maxUsers}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> PopulateSpriggyAccounts(int maxUsers)
    {
        //await userService.ProcessUsersAccountId(maxUsers);
        var order = new Order();
        var orderException = new OrderException("TEST order error");

        this.logger.LogError(orderException, "VOILA", order);

        return new OkResult();
    }

    #region Stripe

    [CheckUserRole(UserRole.Admin)]
    [Route("PrepareStripeCustomersForExistingUsers/{maxUsers}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> PrepareStripeCustomersForExistingUsers(int maxUsers)
    {
        var res = await userService.PrepareStripeCustomersForExistingUsers(maxUsers);

        return new JsonResult(res);
    }

    #endregion
}
