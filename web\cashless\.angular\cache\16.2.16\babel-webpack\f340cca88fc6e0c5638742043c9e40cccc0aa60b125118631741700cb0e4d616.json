{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n// Routing\nimport { PosRoutingModule } from './pos-routing.module';\n// Shared Modules\nimport { SharedModule } from '../shared/shared.module';\nimport { SharedToolsModule } from '../shared-tools/shared-tools.module';\nimport { SchoolsFormModule } from '../schools-form/schools-form.module';\nimport { SchoolsButtonModule } from '../schools-button/schools-button.module';\nimport { SchoolsCommonModule } from '../schools-common/schools-common.module';\nimport { ManageOrderModule } from '../manage-order/manage-order.module';\nimport { PaymentModule } from '../payment/payment.module';\n// Standalone Components\nimport { CategoryTileComponent } from '../manage-order/components/category-tile/category-tile.component';\n// Standalone Pipes\nimport { AbsoluteMoneyValuePipe } from '../sharedPipes/absolute-money-value.pipe';\nimport { MoneyButtonDisplayPipe } from '../sharedPipes/money-button-display.pipe';\nimport { PlaceOrderButtonTextPipe } from '../sharedPipes/place-order-button-text.pipe';\n// Components\nimport { PosComponent, PosTabComponent, StudentSearchDropdownComponent, PosCategoryTileComponent, PosProductItemComponent, DialogPosProductItem, PosPlaceOrderDialogComponent, PosOrdersPlacedComponent } from './components';\nimport * as i0 from \"@angular/core\";\nexport class PosModule {\n  static {\n    this.ɵfac = function PosModule_Factory(t) {\n      return new (t || PosModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PosModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, PosRoutingModule,\n      // Angular Material\n      MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatAutocompleteModule, MatIconModule, MatProgressSpinnerModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatMenuModule, MatExpansionModule, MatBottomSheetModule, MatCheckboxModule, MatTooltipModule,\n      // Shared Modules\n      SharedModule, SharedToolsModule, SchoolsFormModule, SchoolsButtonModule, SchoolsCommonModule, ManageOrderModule, PaymentModule,\n      // Standalone Components\n      CategoryTileComponent, PosCategoryTileComponent]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PosModule, {\n    declarations: [PosComponent, PosTabComponent, StudentSearchDropdownComponent, PosProductItemComponent, DialogPosProductItem, PosPlaceOrderDialogComponent, PosOrdersPlacedComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, PosRoutingModule,\n    // Angular Material\n    MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatAutocompleteModule, MatIconModule, MatProgressSpinnerModule, MatTableModule, MatPaginatorModule, MatSortModule, MatDialogModule, MatMenuModule, MatExpansionModule, MatBottomSheetModule, MatCheckboxModule, MatTooltipModule,\n    // Shared Modules\n    SharedModule, SharedToolsModule, SchoolsFormModule, SchoolsButtonModule, SchoolsCommonModule, ManageOrderModule, PaymentModule,\n    // Standalone Components\n    CategoryTileComponent, PosCategoryTileComponent,\n    // Standalone Pipes\n    AbsoluteMoneyValuePipe, MoneyButtonDisplayPipe, PlaceOrderButtonTextPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatAutocompleteModule", "MatIconModule", "MatProgressSpinnerModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatDialogModule", "MatMenuModule", "MatExpansionModule", "MatBottomSheetModule", "MatCheckboxModule", "MatTooltipModule", "PosRoutingModule", "SharedModule", "SharedToolsModule", "SchoolsFormModule", "SchoolsButtonModule", "SchoolsCommonModule", "ManageOrderModule", "PaymentModule", "CategoryTileComponent", "AbsoluteMoneyValuePipe", "MoneyButtonDisplayPipe", "PlaceOrderButtonTextPipe", "PosComponent", "PosTabComponent", "StudentSearchDropdownComponent", "PosCategoryTileComponent", "PosProductItemComponent", "DialogPosProductItem", "PosPlaceOrderDialogComponent", "PosOrdersPlacedComponent", "PosModule", "declarations", "imports"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\pos.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatBottomSheetModule } from '@angular/material/bottom-sheet';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTooltipModule } from '@angular/material/tooltip';\n\n// Routing\nimport { PosRoutingModule } from './pos-routing.module';\n\n// Shared Modules\nimport { SharedModule } from '../shared/shared.module';\nimport { SharedToolsModule } from '../shared-tools/shared-tools.module';\nimport { SchoolsFormModule } from '../schools-form/schools-form.module';\nimport { SchoolsButtonModule } from '../schools-button/schools-button.module';\nimport { SchoolsCommonModule } from '../schools-common/schools-common.module';\nimport { ManageOrderModule } from '../manage-order/manage-order.module';\nimport { PaymentModule } from '../payment/payment.module';\n\n// Standalone Components\nimport { CategoryTileComponent } from '../manage-order/components/category-tile/category-tile.component';\n\n// Standalone Pipes\nimport { AbsoluteMoneyValuePipe } from '../sharedPipes/absolute-money-value.pipe';\nimport { MoneyButtonDisplayPipe } from '../sharedPipes/money-button-display.pipe';\nimport { PlaceOrderButtonTextPipe } from '../sharedPipes/place-order-button-text.pipe';\n\n// Components\nimport {\n  PosComponent,\n  PosTabComponent,\n  StudentSearchDropdownComponent,\n  PosCategoryTileComponent,\n  PosProductItemComponent,\n  DialogPosProductItem,\n  PosPlaceOrderDialogComponent,\n  PosOrdersPlacedComponent,\n  GuestPaymentDialogComponent,\n} from './components';\n\n@NgModule({\n  declarations: [\n    PosComponent,\n    PosTabComponent,\n    StudentSearchDropdownComponent,\n    PosProductItemComponent,\n    DialogPosProductItem,\n    PosPlaceOrderDialogComponent,\n    PosOrdersPlacedComponent,\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    PosRoutingModule,\n    \n    // Angular Material\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatAutocompleteModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatDialogModule,\n    MatMenuModule,\n    MatExpansionModule,\n    MatBottomSheetModule,\n    MatCheckboxModule,\n    MatTooltipModule,\n    \n    // Shared Modules\n    SharedModule,\n    SharedToolsModule,\n    SchoolsFormModule,\n    SchoolsButtonModule,\n    SchoolsCommonModule,\n    ManageOrderModule,\n    PaymentModule,\n\n    // Standalone Components\n    CategoryTileComponent,\n    PosCategoryTileComponent,\n\n    // Standalone Pipes\n    AbsoluteMoneyValuePipe,\n    MoneyButtonDisplayPipe,\n    PlaceOrderButtonTextPipe,\n  ],\n})\nexport class PosModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD;AACA,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,aAAa,QAAQ,2BAA2B;AAEzD;AACA,SAASC,qBAAqB,QAAQ,kEAAkE;AAExG;AACA,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,wBAAwB,QAAQ,6CAA6C;AAEtF;AACA,SACEC,YAAY,EACZC,eAAe,EACfC,8BAA8B,EAC9BC,wBAAwB,EACxBC,uBAAuB,EACvBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,wBAAwB,QAEnB,cAAc;;AAyDrB,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA;IAAS;EAAA;;;gBA5ClBzC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXmB,gBAAgB;MAEhB;MACAlB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,qBAAqB,EACrBC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,oBAAoB,EACpBC,iBAAiB,EACjBC,gBAAgB;MAEhB;MACAE,YAAY,EACZC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmB,EACnBC,mBAAmB,EACnBC,iBAAiB,EACjBC,aAAa;MAEb;MACAC,qBAAqB,EACrBO,wBAAwB;IAAA;EAAA;;;2EAQfK,SAAS;IAAAC,YAAA,GArDlBT,YAAY,EACZC,eAAe,EACfC,8BAA8B,EAC9BE,uBAAuB,EACvBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,wBAAwB;IAAAG,OAAA,GAGxB3C,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXmB,gBAAgB;IAEhB;IACAlB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,qBAAqB,EACrBC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,oBAAoB,EACpBC,iBAAiB,EACjBC,gBAAgB;IAEhB;IACAE,YAAY,EACZC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmB,EACnBC,mBAAmB,EACnBC,iBAAiB,EACjBC,aAAa;IAEb;IACAC,qBAAqB,EACrBO,wBAAwB;IAExB;IACAN,sBAAsB,EACtBC,sBAAsB,EACtBC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}