using System;
using System.Collections.Generic;
using Schools.BLL.Helpers;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Orders;

public class OrderShorterDto
{
    public long OrderId { get; set; }
    public long SchoolId { get; set; }
    public long StudentId { get; set; }
    public DateTime OrderDate { get; set; }
    public int OrderStatusId { get; set; }
    public decimal Price { get; set; }
    public decimal OrderFee { get; set; }
    public string MenuType { get; set; }
    public long MenuId { get; set; }
}