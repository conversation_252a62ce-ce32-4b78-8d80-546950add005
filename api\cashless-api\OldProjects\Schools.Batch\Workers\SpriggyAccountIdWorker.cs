using System;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Schools.Batch.Helpers;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Interfaces;

namespace Schools.Batch.Workers;

/// <summary>
/// Batch service to update User's SpriggyAccountIdId if missing
/// </summary>
public class SpriggyAccountIdWorker : BackgroundService
{
    // Parameters
    private const string SettingGroup = "SpriggyAccountId";
    private const string SettingName = "UserId";

    // Services
    private readonly IHost _host;
    private readonly IConfigService _configService;
    private readonly IWorkerHelper _workerHelper;
    private readonly IUserRepository _userRepository;
    private readonly IUserService _userService;
    private readonly ILogger<SpriggyAccountIdWorker> _logger;

    // Properties
    private int BatchSize;
    private int NumRecords;

    public SpriggyAccountIdWorker(IHost host, IConfigService configService, IWorkerHelper workerHelper, IUserRepository userRepository,
                        IUserService userService, ILogger<SpriggyAccountIdWorker> logger)
    {
        _host = host;
        _configService = configService;
        _workerHelper = workerHelper;
        _userRepository = userRepository;
        _userService = userService;
        _logger = logger;

        // Load parameters from config
        BatchSize = _configService.GetInt("Workers:SpriggyAccountId:BatchSize");
        NumRecords = _configService.GetInt("Workers:SpriggyAccountId:NumRecords");

        // Init other components
        _workerHelper.Init(SettingGroup, SettingName);
    }

    /// <summary>
    /// Do the work required and stop the console app once done
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await ProcessUsers();

        await _host.StopAsync();
    }

    /// <summary>
    /// Look for all Users in the Schools DB and update their Spriggy Account ID from Spriggy Core
    /// </summary>
    private async Task ProcessUsers()
    {
        var stopwatch = Stopwatch.StartNew();
        var processed = 0;
        var success = 0;

        // Fetch the ID of the last updated User from tblSettings
        var minUserId = await _workerHelper.GetLastUserId();

        // Fetch all Users with ID > last updated User that are still missing Spriggy Account ID
        var users = await _userRepository.GetParentUsersWithoutSpriggyAccountIdFromId(minUserId, NumRecords);

        _logger.LogInformation("Processing started - Users: {Users}", users.Count());

        // For each User fetched:
        //  1. Update the User's Spriggy Account ID from Spriggy Core
        //  2. Update the last Updated User ID in tblSettings
        foreach (var user in users)
        {
            processed++;

            if (await UpdateSpriggyAccountId(user))
            {
                success++;
            }

            if (processed % BatchSize == 0)
            {
                _logger.LogInformation("Processing update - Success: {Success}, Failure: {Failure}, Total: {Processed}, Duration: {Duration:0.00}s",
                                            success, _workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);

                await _workerHelper.SetLastUserId(user.UserId);
            }
        }

        if (users.Any())
        {
            await _workerHelper.SetLastUserId(users.Last().UserId);
        }

        await _workerHelper.ShowErrors();

        _logger.LogInformation("Processing completed - Success: {Success}, Failure: {Failure}, Total: {Processed}, Duration: {Duration:0.00}s",
                                    success, _workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);
    }

    /// <summary>
    /// Update the Spriggy Core Account ID for the given User
    /// </summary>
    private async Task<bool> UpdateSpriggyAccountId(User user)
    {
        try
        {
            await _userService.SetSpriggyAccountId(user);

            _logger.LogDebug("Updated Spriggy Account ID for User: {User}", user);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update Spriggy Account ID for User: {User}", user);

            _workerHelper.AddError(user, ex);

            return false;
        }
    }
}
