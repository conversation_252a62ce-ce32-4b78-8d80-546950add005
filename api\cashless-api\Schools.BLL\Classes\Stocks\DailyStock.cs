﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Stocks
{
    public class DailyStockRequest
    {
        [JsonProperty(PropertyName = "MenuItemId")]
        public int MenuItemId { get; set; }

        [JsonProperty(PropertyName = "Date")]
        public string Date { get; set; }
    }

    public class StockOrder : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "DailyStockOrderId")]
        public int? DailyStockOrderId { get; set; }

        [JsonProperty(PropertyName = "DailyStockId")]
        public int? DailyStockId { get; set; }

        [JsonProperty(PropertyName = "StockId")]
        public int? StockId { get; set; }

        [JsonProperty(PropertyName = "OrderId")]
        public int OrderId { get; set; }

        [JsonProperty(PropertyName = "MenuItemId")]
        public int MenuItemId { get; set; }

        [JsonProperty(PropertyName = "quantityStockOrdered")]
        public int quantityStockOrdered { get; set; }

        [JsonProperty(PropertyName = "IsCancelled")]
        public bool? IsCancelled { get; set; }
    }

    public class DailyStockAvailability
    {
        public int StockId { get; set; }
        public int QuantityOrdered { get; set; }
        public DateTime DateOrdered { get; set; }
        public string ItemName { get; set; }
    }
}
