using Schools.DAL.Enums;

namespace Schools.BLL.Classes
{

    /// <summary>
    /// Perform pagination and filtering on orders. Filter by:
    ///     1. Menu Type
    ///     2. Order Status
    /// </summary>
    public class OrderFilter
    {
        public int PageIndex { get; set; }
        public int NumberRows { get; set; }
        public string MenuType { get; set; }
        public OrderStatusEnum OrderStatus { get; set; }
    }
}
