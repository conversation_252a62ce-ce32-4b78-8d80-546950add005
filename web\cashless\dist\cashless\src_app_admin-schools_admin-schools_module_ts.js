"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_admin-schools_admin-schools_module_ts"],{

/***/ 86118:
/*!***************************************************************!*\
  !*** ./src/app/admin-schools/admin-schools-routing.module.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminSchoolsRoutingModule: () => (/* binding */ AdminSchoolsRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 52195);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../sharedServices */ 2902);
/* harmony import */ var _resolvers_school_merchants_resolver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolvers/school-merchants.resolver */ 98122);
/* harmony import */ var _resolvers_list_features_resolver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolvers/list-features.resolver */ 1268);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);

// components

// resolvers





// routes
const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminListSchoolsComponent,
  resolve: {
    schools: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListSchoolsAdminResolver
  }
}, {
  path: 'add',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminDetailSchoolComponent,
  resolve: {
    canteens: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListCanteensForAdminResolver,
    states: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListStatesResolver
  }
}, {
  path: ':id',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.SchoolNavBarComponent,
  children: [{
    path: '',
    pathMatch: 'full',
    redirectTo: 'details'
  }, {
    path: 'details',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminDetailSchoolComponent,
    resolve: {
      school: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.SchoolResolver,
      states: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListStatesResolver
    }
  }, {
    path: 'classes',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminSchoolClassesComponent,
    resolve: {
      classes: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListClassesResolver
    }
  }, {
    path: 'features',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminSchoolFeaturesComponent,
    resolve: {
      features: _resolvers_list_features_resolver__WEBPACK_IMPORTED_MODULE_3__.ListFeaturesResolver
    }
  }, {
    path: 'events',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminEventManagementComponent,
    resolve: {
      merchants: _resolvers_school_merchants_resolver__WEBPACK_IMPORTED_MODULE_2__.SchoolMerchantsResolver
    }
  }]
}];
class AdminSchoolsRoutingModule {
  static {
    this.ɵfac = function AdminSchoolsRoutingModule_Factory(t) {
      return new (t || AdminSchoolsRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineNgModule"]({
      type: AdminSchoolsRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsetNgModuleScope"](AdminSchoolsRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
  });
})();

/***/ }),

/***/ 57583:
/*!*******************************************************!*\
  !*** ./src/app/admin-schools/admin-schools.module.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminSchoolsModule: () => (/* binding */ AdminSchoolsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../schools-events/schools-events.module */ 42199);
/* harmony import */ var _admin_schools_routing_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./admin-schools-routing.module */ 86118);
/* harmony import */ var _account_account_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../account/account.module */ 90359);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared-tools/shared-tools.module */ 23879);
/* harmony import */ var _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../schools-common/schools-common.module */ 53943);
/* harmony import */ var _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../schools-form/schools-form.module */ 97162);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/material/datepicker */ 61977);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/radio */ 53804);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/paginator */ 24624);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components */ 52195);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);


// modules








// material















class AdminSchoolsModule {
  static {
    this.ɵfac = function AdminSchoolsModule_Factory(t) {
      return new (t || AdminSchoolsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineNgModule"]({
      type: AdminSchoolsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _admin_schools_routing_module__WEBPACK_IMPORTED_MODULE_1__.AdminSchoolsRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.ReactiveFormsModule, _account_account_module__WEBPACK_IMPORTED_MODULE_2__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_3__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_4__.SharedToolsModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsCommonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsFormModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_7__.SchoolsButtonModule,
      // material
      _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__.MatIconModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__.MatCheckboxModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_14__.MatSortModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_15__.MatFormFieldModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_16__.MatInputModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_17__.MatCardModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_18__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_19__.MatDatepickerModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_20__.MatRadioModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_21__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_22__.MatTableModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__.MatPaginatorModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_24__.MatButtonModule, _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_0__.SchoolsEventsModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsetNgModuleScope"](AdminSchoolsModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_8__.SchoolNavBarComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AdminSchoolClassesComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AddSchoolClassComponent, _components__WEBPACK_IMPORTED_MODULE_8__.ListClassesComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AdminListSchoolsComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AdminDetailSchoolComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AdminEventManagementComponent, _components__WEBPACK_IMPORTED_MODULE_8__.AdminSchoolFeaturesComponent, _components__WEBPACK_IMPORTED_MODULE_8__.StringArrayFormatPipe],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _admin_schools_routing_module__WEBPACK_IMPORTED_MODULE_1__.AdminSchoolsRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.ReactiveFormsModule, _account_account_module__WEBPACK_IMPORTED_MODULE_2__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_3__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_4__.SharedToolsModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsCommonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsFormModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_7__.SchoolsButtonModule,
    // material
    _angular_material_icon__WEBPACK_IMPORTED_MODULE_12__.MatIconModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__.MatCheckboxModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_14__.MatSortModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_15__.MatFormFieldModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_16__.MatInputModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_17__.MatCardModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_18__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_19__.MatDatepickerModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_20__.MatRadioModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_21__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_22__.MatTableModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__.MatPaginatorModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_24__.MatButtonModule, _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_0__.SchoolsEventsModule]
  });
})();

/***/ }),

/***/ 27637:
/*!*****************************************************************************************!*\
  !*** ./src/app/admin-schools/components/add-school-class/add-school-class.component.ts ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddSchoolClassComponent: () => (/* binding */ AddSchoolClassComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/shared/components/dialog-confirm/dialog-confirm.component */ 26645);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/button */ 84175);














function AddSchoolClassComponent_mat_error_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r0.getErrorMessageName());
  }
}
function AddSchoolClassComponent_mat_option_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "mat-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const val_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", val_r5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](val_r5);
  }
}
function AddSchoolClassComponent_mat_error_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r2.WriteError());
  }
}
function AddSchoolClassComponent_button_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function AddSchoolClassComponent_button_26_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r6.CancelForm());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Cancel");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function AddSchoolClassComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 15)(1, "button", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function AddSchoolClassComponent_div_27_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r8.ArchiveClicked());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Archive");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
class AddSchoolClassComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(schoolClassService, adminService, spinnerService, dialog) {
    super();
    this.schoolClassService = schoolClassService;
    this.adminService = adminService;
    this.spinnerService = spinnerService;
    this.dialog = dialog;
    this.loadSchoolClasses = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.isEdit = false;
    this.classValues = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
  }
  ngOnInit() {
    this.schoolId = this.adminService.GetSchoolId();
    this.createForm(new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolClass());
    this.editClassSubscription = this.adminService.classEditRequestEvent$.subscribe(schoolClass => {
      this.createForm(schoolClass, true);
    });
  }
  ngOnDestroy() {
    if (this.editClassSubscription) {
      this.editClassSubscription.unsubscribe();
    }
  }
  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get name() {
    return this.form.get('name');
  }
  getErrorMessageName() {
    return this.form.get('name').hasError('required') ? 'You must enter a value' : '';
  }
  createForm(schoolClass, isEdit = false) {
    this.isEdit = isEdit;
    this.textSubmit = this._GetTextSubmit(schoolClass.Name);
    let yearGroup = [];
    if (schoolClass.YearGroup) {
      //remove any commas from the end of string
      let yearGroupString = schoolClass?.YearGroup.slice(-1) === ',' ? schoolClass.YearGroup.slice(0, -1) : schoolClass.YearGroup;
      // transform string into array
      yearGroup = yearGroupString.split(',');
    }
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      id: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(schoolClass.ClassId),
      schoolId: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.schoolId),
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(schoolClass.Name, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required]),
      teacher: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(schoolClass.Teacher),
      isActive: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(schoolClass.IsActive != null ? schoolClass.IsActive : true),
      yearGroup: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(yearGroup),
      sortOrder: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(schoolClass.SortOrder)
    });
  }
  ////////////////////////////////////////
  // Add school
  ////////////////////////////////////////
  onSubmit() {
    this.spinnerService.start();
    let data = this.convertObject();
    if (data.ClassId) {
      this.updateClass(data);
    } else {
      this.addClass(data);
    }
  }
  addClass(data) {
    this.schoolClassService.CreateClassApi(data).subscribe({
      next: response => {
        this.initClassForm();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  updateClass(data) {
    this.schoolClassService.UpdateClassApi(data).subscribe({
      next: response => {
        this.initClassForm();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  initClassForm() {
    this.loadSchoolClasses.emit();
    this.createForm(new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolClass());
  }
  convertObject() {
    let schoolClass = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolClass();
    schoolClass.ClassId = this.form.get('id').value;
    schoolClass.SchoolId = this.form.get('schoolId').value;
    schoolClass.Name = this.form.get('name').value;
    schoolClass.Teacher = this.form.get('teacher').value;
    schoolClass.IsActive = Boolean(this.form.get('isActive').value);
    schoolClass.SortOrder = this.form.get('sortOrder').value;
    let yearGroupResult = '';
    if (this.yearGroup.value?.length > 1) {
      // convert year group array into string
      this.yearGroup.value.forEach(el => {
        yearGroupResult += el + ',';
      });
    } else if (this.yearGroup.value?.length === 1) {
      yearGroupResult = this.yearGroup.value.toString();
    }
    schoolClass.YearGroup = yearGroupResult;
    return schoolClass;
  }
  CancelForm() {
    this.createForm(new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolClass());
  }
  get yearGroup() {
    return this.form.get('yearGroup');
  }
  ////////////////////////////////////////
  // Get Text
  ////////////////////////////////////////
  _GetTextSubmit(className) {
    if (className != null && className != '') {
      this.showButtonCancel = true;
      return 'Edit';
    } else {
      this.showButtonCancel = false;
      return 'Add';
    }
  }
  ArchiveClicked() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = 'Archive Class';
    data.Text = "Archiving this class will inactivate the class permanently and can not be undone. Parents will be instructed to change their child's class. Proceed?";
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';
    const dialogRef = this.dialog.open(src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_0__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed();
      }
    });
  }
  ArchiveClickConfirmed() {
    this.spinnerService.start();
    let archiveClass = this.convertObject();
    archiveClass.IsArchived = true;
    this.schoolClassService.ArchiveClassApi(archiveClass).subscribe({
      next: response => {
        this.loadSchoolClasses.emit();
        this.createForm(new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolClass());
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  static {
    this.ɵfac = function AddSchoolClassComponent_Factory(t) {
      return new (t || AddSchoolClassComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolClassesService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.AdminService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: AddSchoolClassComponent,
      selectors: [["admin-add-school-class"]],
      inputs: {
        onCancel: "onCancel"
      },
      outputs: {
        loadSchoolClasses: "loadSchoolClasses"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 28,
      vars: 9,
      consts: [[1, "cashlessForm", 3, "formGroup", "ngSubmit"], ["appearance", "outline"], ["matInput", "", "placeholder", "Name", "formControlName", "name", "type", "text", "required", ""], [4, "ngIf"], ["matInput", "", "placeholder", "Teacher", "formControlName", "teacher", "type", "text"], ["multiple", "", 3, "formControl"], [3, "value", 4, "ngFor", "ngForOf"], ["matInput", "", "placeholder", "Display Order", "formControlName", "sortOrder", "type", "number", "step", "1"], [1, "row", "buttonsWrapper"], [1, "col-6"], ["mat-flat-button", "", "color", "primary", "type", "submit", 3, "disabled"], ["mat-flat-button", "", "type", "button", 3, "click", 4, "ngIf"], ["class", "col-6 archiveDiv", 4, "ngIf"], [3, "value"], ["mat-flat-button", "", "type", "button", 3, "click"], [1, "col-6", "archiveDiv"], ["mat-flat-button", "", "type", "button", 1, "archiveButton", 3, "click"]],
      template: function AddSchoolClassComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Add / Edit Class");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "form", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngSubmit", function AddSchoolClassComponent_Template_form_ngSubmit_2_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "mat-form-field", 1)(4, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Class name");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "input", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, AddSchoolClassComponent_mat_error_7_Template, 2, 1, "mat-error", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "mat-form-field", 1)(9, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10, "Teacher");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](11, "input", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "mat-form-field", 1)(13, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](14, "Select year group");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](15, "mat-select", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](16, AddSchoolClassComponent_mat_option_16_Template, 2, 2, "mat-option", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "mat-form-field", 1)(18, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](19, "Display Order");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "input", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, AddSchoolClassComponent_mat_error_21_Template, 2, 1, "mat-error", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "div", 8)(23, "div", 9)(24, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](26, AddSchoolClassComponent_button_26_Template, 2, 0, "button", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](27, AddSchoolClassComponent_div_27_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.name.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formControl", ctx.yearGroup);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.classValues);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.errorAPI);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", !ctx.form.valid);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx.textSubmit);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showButtonCancel);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isEdit);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatError, _angular_material_input__WEBPACK_IMPORTED_MODULE_8__.MatInput, _angular_material_select__WEBPACK_IMPORTED_MODULE_9__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_10__.MatOption, _angular_material_button__WEBPACK_IMPORTED_MODULE_11__.MatButton],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.buttonsWrapper[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-top: 20px;\n}\n\n.archiveDiv[_ngcontent-%COMP%] {\n  text-align: right;\n}\n\n.archiveButton[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 16px;\n  padding-right: 22px;\n  padding-left: 22px;\n  width: 90px;\n  height: 44px;\n  text-align: center;\n  background-color: #e0e0e0;\n  color: red;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 90425:
/*!***********************************************************************************************!*\
  !*** ./src/app/admin-schools/components/admin-detail-school/admin-detail-school.component.ts ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminDetailSchoolComponent: () => (/* binding */ AdminDetailSchoolComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components/ */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/core */ 74646);

// Models














function AdminDetailSchoolComponent_mat_option_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-option", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const state_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", state_r3.StateId);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](state_r3.ShortName);
  }
}
function AdminDetailSchoolComponent_div_13_mat_option_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-option", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const canteen_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", canteen_r5.CanteenId);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](canteen_r5.CanteenName);
  }
}
function AdminDetailSchoolComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "mat-form-field", 7)(2, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "Merchant");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "mat-select", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](5, AdminDetailSchoolComponent_div_13_mat_option_5_Template, 2, 2, "mat-option", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r1.canteens);
  }
}
function AdminDetailSchoolComponent_div_28_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r2.WriteError());
  }
}
class AdminDetailSchoolComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(activatedRoute, spinnerService, schoolService, location, dialog) {
    super();
    this.activatedRoute = activatedRoute;
    this.spinnerService = spinnerService;
    this.schoolService = schoolService;
    this.location = location;
    this.dialog = dialog;
    this.listStates = [];
    this.showError = false;
    this.pricingEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.PricingModelEnum;
  }
  ngOnInit() {
    this.school = this.activatedRoute.snapshot.data['school'];
    this.listStates = this.activatedRoute.snapshot.data['states'];
    this.canteens = this.activatedRoute.snapshot.data['canteens'];
    this._createForm();
  }
  ///////////////////////////////////
  // Form
  ///////////////////////////////////
  _createForm() {
    let canteenId = null;
    if (!this.school) {
      this.setSchoollDefaultValues();
      canteenId = this.canteens[0].CanteenId;
    }
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroup({
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.Name, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.maxLength(200)]),
      active: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.IsActive),
      marketingFree: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.IsMarketingFree),
      canteen: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(canteenId),
      state: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.StateId),
      deactivatedFilters: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.DeactivatedFilters),
      totalStudentEst: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.school.TotalStudentEst, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.min(0))
    });
  }
  setSchoollDefaultValues() {
    this.school = {
      SchoolId: null,
      Name: '',
      StateId: this.listStates[0].StateId,
      DeactivatedFilters: '',
      IsActive: true,
      IsMarketingFree: false,
      TotalStudentEst: 0
    };
  }
  get name() {
    return this.form.get('name');
  }
  get canteen() {
    return this.form.get('canteen');
  }
  get active() {
    return this.form.get('active');
  }
  get marketingFree() {
    return this.form.get('marketingFree');
  }
  get deactivatedFilters() {
    return this.form.get('deactivatedFilters');
  }
  get state() {
    return this.form.get('state');
  }
  get totalStudentEst() {
    return this.form.get('totalStudentEst');
  }
  onSubmit() {
    if (this.school.SchoolId) {
      this.confirmSubmit();
      return;
    }
    this.areYouSurePopup();
  }
  confirmSubmit() {
    this.spinnerService.start();
    this._convertToItemObject();
    if (this.school.SchoolId > 0) {
      this.editSchoolAPICall();
    } else {
      this.createSchoolAPICall();
    }
  }
  editSchoolAPICall() {
    this.schoolService.EditApi(this.school).subscribe({
      next: () => {
        this.spinnerService.stop();
        this.successPopup('School has been successfully updated.');
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup('We were unable to update this school.');
        this.handleErrorFromService(error);
      }
    });
  }
  createSchoolAPICall() {
    this.schoolService.CreateApi(this.school, this.canteen.value).subscribe({
      next: response => {
        this.school.SchoolId = response;
        this.spinnerService.stop();
        let dialogRef = this.successPopup('School has been successfully created.');
        dialogRef.afterClosed().subscribe(result => {
          this.location.back();
        });
      },
      error: error => {
        this.spinnerService.stop();
        let errorMessage = typeof error?.errors?.Name[0] === 'string' ? error?.errors?.Name[0] : 'We were unable to create this school.';
        this.SomethingWentWrongPopup(errorMessage);
      }
    });
  }
  /** Put the values of the form into the MenuItem */
  _convertToItemObject() {
    this.school.Name = this.name.value;
    this.school.IsActive = this.active.value;
    this.school.DeactivatedFilters = this.deactivatedFilters.value;
    this.school.IsMarketingFree = this.marketingFree.value;
    this.school.StateId = this.state.value;
    this.school.TotalStudentEst = this.totalStudentEst.value || 0;
  }
  ///////////////////////////////////
  // view functions
  ///////////////////////////////////
  getErrorMessageName() {
    return 'Please enter a valid input';
  }
  GetTextSubmitButton() {
    return this.school.SchoolId ? 'Save' : 'Add';
  }
  ///////////////////////////////////
  // Pop ups
  ///////////////////////////////////
  successPopup(text) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = text;
    data.ConfirmButton = 'Okay';
    return this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
  }
  SomethingWentWrongPopup(text) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = text;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmSubmit();
      }
    });
  }
  areYouSurePopup() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = 'Are you sure you want to create a new school?';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, Create';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmSubmit();
      }
    });
  }
  static {
    this.ɵfac = function AdminDetailSchoolComponent_Factory(t) {
      return new (t || AdminDetailSchoolComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_7__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_8__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AdminDetailSchoolComponent,
      selectors: [["app-admin-detail-school"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 29,
      vars: 8,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-12", "col-sm-12", "col-lg-6"], [1, "cardDefaultCanteen"], [1, "cashlessForm", "input-container", 3, "formGroup", "ngSubmit"], ["placeholder", "School name", "formControlName", "name", 3, "error"], ["placeholder", "Number of Students", "formControlName", "totalStudentEst", "step", "1", "min", "0", 3, "error"], ["appearance", "outline"], ["placeholder", "State", "formControlName", "state"], [3, "value", 4, "ngFor", "ngForOf"], [4, "ngIf"], ["matInput", "", "placeholder", "Deactivated filters", "type", "text", "formControlName", "deactivatedFilters"], [1, "pb-3"], ["formControlName", "active"], ["formControlName", "marketingFree"], ["type", "submit", 1, "PrimaryButton", "submitButton", 3, "disabled"], [3, "value"], ["placeholder", "Operator", "formControlName", "canteen"]],
      template: function AdminDetailSchoolComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "form", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngSubmit", function AdminDetailSchoolComponent_Template_form_ngSubmit_4_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "input-text", 5)(6, "input-text", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "div")(8, "mat-form-field", 7)(9, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10, "State");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "mat-select", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](12, AdminDetailSchoolComponent_mat_option_12_Template, 2, 2, "mat-option", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](13, AdminDetailSchoolComponent_div_13_Template, 6, 1, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "div")(15, "mat-form-field", 7)(16, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](17, "Deactivated Filters");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](18, "input", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "div", 12)(20, "mat-checkbox", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](21, "Is Active");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "div", 12)(23, "mat-checkbox", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](24, "Hide Marketing - Requires General Manager approval to select ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "div")(26, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](28, AdminDetailSchoolComponent_div_28_Template, 3, 1, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("error", ctx.name.invalid ? ctx.getErrorMessageName() : null);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("error", ctx.totalStudentEst.invalid ? ctx.getErrorMessageName() : null);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx.listStates);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.school.SchoolId || ctx.school.SchoolId == 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", !ctx.form.valid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx.GetTextSubmitButton(), " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.errorAPI);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControlName, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_3__.InputTextComponent, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_9__.MatCheckbox, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatError, _angular_material_input__WEBPACK_IMPORTED_MODULE_11__.MatInput, _angular_material_select__WEBPACK_IMPORTED_MODULE_12__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_13__.MatOption],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.input-container[_ngcontent-%COMP%] {\n  max-width: 400px;\n}\n.input-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.submitButton[_ngcontent-%COMP%] {\n  width: 55px;\n  height: 35px;\n  font-size: 16px;\n  padding: 10px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tc2Nob29scy9jb21wb25lbnRzL2FkbWluLWRldGFpbC1zY2hvb2wvYWRtaW4tZGV0YWlsLXNjaG9vbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFkQTtFQUNFLGdCQUFBO0FBaUJGO0FBZkU7RUFDRSxXQUFBO0FBaUJKOztBQWJBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtBQWdCRiIsInNvdXJjZXNDb250ZW50IjpbIiRicmVha3BvaW50LXNtOiA1NzZweDtcbiRicmVha3BvaW50LW1kOiA3NjdweDtcbiRicmVha3BvaW50LWxnOiA5OTJweDtcbiRicmVha3BvaW50LXhsOiAxMjAwcHg7XG5cbi5tb2JpbGUge1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1heC13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuLy8gTk9URSBDdXJyZW50bHkgdGFibGV0IGFuZCBtb2JpbGUgaXMgdGhlIHNhbWUuIENoYW5nZSB0byAkYnJlYWtwb2ludC1sZyBsYXRlciBpZiB3ZSBnZXQgYSBwcm9wZXIgdGFibGV0IGRlc2lnbi5cbi5kZXNrdG9wIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtaW4td2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbiIsIkBpbXBvcnQgJy4uLy4uLy4uLy4uL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzJztcblxuLmlucHV0LWNvbnRhaW5lciB7XG4gIG1heC13aWR0aDogNDAwcHg7XG5cbiAgbWF0LWZvcm0tZmllbGQge1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG59XG5cbi5zdWJtaXRCdXR0b24ge1xuICB3aWR0aDogNTVweDtcbiAgaGVpZ2h0OiAzNXB4O1xuICBmb250LXNpemU6IDE2cHg7XG4gIHBhZGRpbmc6IDEwcHg7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 85187:
/*!*****************************************************************************************************!*\
  !*** ./src/app/admin-schools/components/admin-event-management/admin-event-management.component.ts ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminEventManagementComponent: () => (/* binding */ AdminEventManagementComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_events_components_event_management_event_management_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-events/components/event-management/event-management.component */ 37819);




function AdminEventManagementComponent_event_management_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "event-management", 1);
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("schoolId", ctx_r0.schoolId)("merchants", ctx_r0.merchants);
  }
}
class AdminEventManagementComponent {
  constructor(route) {
    this.schoolId = route.snapshot.parent.params['id'];
    this.merchants = route.snapshot.data['merchants'];
  }
  ngOnInit() {}
  static {
    this.ɵfac = function AdminEventManagementComponent_Factory(t) {
      return new (t || AdminEventManagementComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_2__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: AdminEventManagementComponent,
      selectors: [["admin-event-management"]],
      decls: 1,
      vars: 1,
      consts: [[3, "schoolId", "merchants", 4, "ngIf"], [3, "schoolId", "merchants"]],
      template: function AdminEventManagementComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, AdminEventManagementComponent_event_management_0_Template, 1, 2, "event-management", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.schoolId);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf, _schools_events_components_event_management_event_management_component__WEBPACK_IMPORTED_MODULE_0__.EventManagementComponent],
      encapsulation: 2
    });
  }
}

/***/ }),

/***/ 91783:
/*!*********************************************************************************************!*\
  !*** ./src/app/admin-schools/components/admin-list-schools/admin-list-schools.component.ts ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminListSchoolsComponent: () => (/* binding */ AdminListSchoolsComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-common/components/search-panel/search-panel.component */ 43913);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/paginator */ 24624);
//models








function AdminListSchoolsComponent_th_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "School Id");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a1) {
  return ["./", a1];
};
function AdminListSchoolsComponent_td_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r10 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](2, _c0, element_r10.SchoolId));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", element_r10.SchoolId, " ");
  }
}
function AdminListSchoolsComponent_th_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function AdminListSchoolsComponent_td_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r11.Name);
  }
}
function AdminListSchoolsComponent_th_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "th", 19);
  }
}
function AdminListSchoolsComponent_td_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 22)(1, "mat-icon", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "chevron_right");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function AdminListSchoolsComponent_th_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Deactivated Filters");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function AdminListSchoolsComponent_td_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r13 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r13.DeactivatedFilters);
  }
}
function AdminListSchoolsComponent_tr_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 24);
  }
}
function AdminListSchoolsComponent_tr_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 25);
  }
  if (rf & 2) {
    const row_r14 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](1, _c0, row_r14.SchoolId));
  }
}
const _c1 = function () {
  return ["./add"];
};
const _c2 = function () {
  return [25, 50, 100];
};
const _columns = ['id', 'name', 'deactivatedFilters', 'options'];
class AdminListSchoolsComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BasePaginatorComponent {
  constructor(route, router, spinnerService, adminService, schoolService) {
    super(_columns);
    this.route = route;
    this.router = router;
    this.spinnerService = spinnerService;
    this.adminService = adminService;
    this.schoolService = schoolService;
  }
  ngOnInit() {
    this.route.data.subscribe(data => {
      // get data from resolver
      let tempRes = data['schools'];
      this._ProcessResponseSchools(tempRes.schoolList);
      // get current filters
      this.listfilters = this.adminService.GetSchoolFilters();
      if (!this.listfilters) {
        this.listfilters = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ArrayFilter();
      }
    });
  }
  pageChange(event) {
    // Update filter
    this.basePageChange(event);
    // send request
    this._requestSchools();
  }
  clearFilter() {
    this.clearFiltersAndResults();
    // send request
    this._requestSchools();
  }
  fetchData(searchInput) {
    this.listfilters.Filter = searchInput;
    this._requestSchools();
  }
  /** Call the school service to get the school data  */
  _requestSchools() {
    // start spinner
    this.spinnerService.start();
    // save current filters
    this.adminService.SetSchoolFilters(this.listfilters);
    this.schoolService.GetSchoolsWithFilterAPI(this.listfilters).subscribe({
      next: res => {
        this._ProcessResponseSchools(res.schoolList);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  /** Process the list of users to be used in the component */
  _ProcessResponseSchools(response) {
    if (response) {
      this.listObjects = response;
      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
      } else {
        this.totalRows = 0;
      }
    } else {
      this.listObjects = [];
      this.totalRows = 0;
    }
    this.dataSource.data = this.listObjects;
    //Stop spinner
    this.spinnerService.stop();
  }
  ShowCutOffTime(time) {
    let compare = new Date('0001-01-01');
    return new Date(time) > compare;
  }
  static {
    this.ɵfac = function AdminListSchoolsComponent_Factory(t) {
      return new (t || AdminListSchoolsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.AdminService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SchoolService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: AdminListSchoolsComponent,
      selectors: [["app-admin-list-schools"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 28,
      vars: 11,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-12"], ["placeholder", "Filter", 3, "searchInput", "triggerSearch", "triggerClear"], [1, "col-12", "col-md-6", "col-lg-5", "mt-3"], [3, "routerLink"], [1, "col-12", "schoolsArray"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "accountTable", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 3, "routerLink", 4, "matCellDef"], ["matColumnDef", "name"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "options", "stickyEnd", ""], ["mat-cell", "", "style", "text-align: right", 4, "matCellDef"], ["matColumnDef", "deactivatedFilters"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "tableLine", 3, "routerLink", 4, "matRowDef", "matRowDefColumns"], [3, "pageSize", "pageSizeOptions", "length", "pageIndex", "page"], ["mat-header-cell", ""], ["mat-cell", "", 3, "routerLink"], ["mat-cell", ""], ["mat-cell", "", 2, "text-align", "right"], [1, "actionTableau"], ["mat-header-row", ""], ["mat-row", "", 1, "tableLine", 3, "routerLink"]],
      template: function AdminListSchoolsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "search-panel", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("triggerSearch", function AdminListSchoolsComponent_Template_search_panel_triggerSearch_5_listener($event) {
            return ctx.fetchData($event);
          })("triggerClear", function AdminListSchoolsComponent_Template_search_panel_triggerClear_5_listener() {
            return ctx.clearFilter();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 1)(7, "div", 4)(8, "a", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, "Create new school");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 1)(11, "div", 6)(12, "table", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](13, 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, AdminListSchoolsComponent_th_14_Template, 2, 0, "th", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](15, AdminListSchoolsComponent_td_15_Template, 2, 4, "td", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](16, 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](17, AdminListSchoolsComponent_th_17_Template, 2, 0, "th", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](18, AdminListSchoolsComponent_td_18_Template, 2, 1, "td", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](19, 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, AdminListSchoolsComponent_th_20_Template, 1, 0, "th", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, AdminListSchoolsComponent_td_21_Template, 3, 0, "td", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](22, 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](23, AdminListSchoolsComponent_th_23_Template, 2, 0, "th", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](24, AdminListSchoolsComponent_td_24_Template, 2, 1, "td", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, AdminListSchoolsComponent_tr_25_Template, 1, 0, "tr", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](26, AdminListSchoolsComponent_tr_26_Template, 1, 3, "tr", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](27, "mat-paginator", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("page", function AdminListSchoolsComponent_Template_mat_paginator_page_27_listener($event) {
            return ctx.pageChange($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("searchInput", ctx.listfilters.Filter);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction0"](9, _c1));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("dataSource", ctx.dataSource);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matHeaderRowDef", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("pageSize", 25)("pageSizeOptions", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction0"](10, _c2))("length", ctx.totalRows)("pageIndex", ctx.listfilters.PageIndex);
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterLink, _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_2__.SearchPanelComponent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__.MatIcon, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_6__.MatRow, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_7__.MatPaginator],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nh3[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.filterField[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.accountTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.actionTableau[_ngcontent-%COMP%] {\n  cursor: pointer;\n  margin-left: 5px;\n  margin-right: 5px;\n}\n\n.schoolsArray[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  padding-bottom: 80px;\n}\n\n.tableLine[_ngcontent-%COMP%]:hover {\n  background-color: #fff2e6;\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 47037:
/*!*************************************************************************************************!*\
  !*** ./src/app/admin-schools/components/admin-school-classes/admin-school-classes.component.ts ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminSchoolClassesComponent: () => (/* binding */ AdminSchoolClassesComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../add-school-class/add-school-class.component */ 27637);
/* harmony import */ var _list_classes_list_classes_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../list-classes/list-classes.component */ 36693);








function AdminSchoolClassesComponent_admin_list_classes_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "admin-list-classes");
  }
}
class AdminSchoolClassesComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(route, router, spinnerService, adminService, schoolClassesService) {
    super();
    this.route = route;
    this.router = router;
    this.spinnerService = spinnerService;
    this.adminService = adminService;
    this.schoolClassesService = schoolClassesService;
  }
  ngOnInit() {
    let listTemp = this.route.snapshot.data['classes'];
    //get schoolid from url
    this.schoolId = this.route.snapshot.parent.params['id'];
    if (listTemp) {
      this.adminService.SetListClasses(listTemp.Classes);
    }
    this.isListLoaded = true;
    //Stop spinner
    this.spinnerService.stop();
  }
  /**
   * This function is called each time a class is updated via
   * the add-school-class component
   */
  loadSchoolClasses() {
    this.spinnerService.start();
    this.schoolClassesService.GetClassesBySchoolAPI(this.schoolId, true).subscribe({
      next: response => {
        this.adminService.SetListClasses(response.Classes);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  GoBack() {
    this.spinnerService.start();
    this.router.navigate(['./admin/schools']);
  }
  static {
    this.ɵfac = function AdminSchoolClassesComponent_Factory(t) {
      return new (t || AdminSchoolClassesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.AdminService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SchoolClassesService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AdminSchoolClassesComponent,
      selectors: [["admin-school-classes"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 10,
      vars: 1,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-9"], [4, "ngIf"], [1, "col-3"], ["appearance", "outlined"], [1, "row", "justify-content-center"], [1, "col-12"], [3, "loadSchoolClasses"]],
      template: function AdminSchoolClassesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](3, AdminSchoolClassesComponent_admin_list_classes_3_Template, 1, 0, "admin-list-classes", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 4)(5, "mat-card", 5)(6, "mat-card-content")(7, "div", 6)(8, "div", 7)(9, "admin-add-school-class", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("loadSchoolClasses", function AdminSchoolClassesComponent_Template_admin_add_school_class_loadSchoolClasses_9_listener() {
            return ctx.loadSchoolClasses();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.isListLoaded);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_7__.MatCardContent, _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_2__.AddSchoolClassComponent, _list_classes_list_classes_component__WEBPACK_IMPORTED_MODULE_3__.ListClassesComponent],
      styles: [".goBackButton[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tc2Nob29scy9jb21wb25lbnRzL2FkbWluLXNjaG9vbC1jbGFzc2VzL2FkbWluLXNjaG9vbC1jbGFzc2VzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmdvQmFja0J1dHRvbiB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 4197:
/*!***************************************************************************************************!*\
  !*** ./src/app/admin-schools/components/admin-school-features/admin-school-features.component.ts ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminSchoolFeaturesComponent: () => (/* binding */ AdminSchoolFeaturesComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-form/components */ 46282);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-common/components/school-panel/school-panel.component */ 74153);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);












function AdminSchoolFeaturesComponent_span_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " (No Uniform menu available for this School).");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
class AdminSchoolFeaturesComponent extends src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__.BaseFormComponent {
  constructor(route, schoolFeatureService) {
    super();
    this.route = route;
    this.schoolFeatureService = schoolFeatureService;
    this.uniformShopAvailable = false;
  }
  ngOnInit() {
    // create form
    this._CreateForm();
    // get school Id from the route
    const routeSnapshot = this.route.snapshot;
    this.schoolId = routeSnapshot.params['id'];
    if (this.schoolId == null || this.schoolId == undefined) {
      this.schoolId = routeSnapshot.parent.params['id'];
    }
    // get form values from the data received
    this._initScreen(this.route.snapshot.data['features']);
  }
  /**
   * Create form
   */
  _CreateForm() {
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroup({
      allergy: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(false),
      payAtCanteen: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(false),
      uniform: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(false),
      uniformDescription: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl('')
    });
    this.uniform.valueChanges.subscribe(event => {
      if (event) {
        this.uniformDescription.disable();
      } else {
        this.uniformDescription.enable();
      }
    });
  }
  get allergy() {
    return this.formGroup.get('allergy');
  }
  get payAtCanteen() {
    return this.formGroup.get('payAtCanteen');
  }
  get uniform() {
    return this.formGroup.get('uniform');
  }
  get uniformDescription() {
    return this.formGroup.get('uniformDescription');
  }
  /**
   *
   * @param data data received from the resolver
   */
  _initScreen(data) {
    if (data) {
      // manage uniform shop availability
      this.uniformShopAvailable = data.uniformFeatureAvailable;
      if (data.uniformFeatureAvailable) {
        this.uniform.enable();
      } else {
        this.uniform.disable();
      }
      // manage features check
      if (data.features) {
        const payAtCanteen = data.features.find(x => x.type == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.PayAtCanteen);
        this.payAtCanteen.setValue(payAtCanteen ? payAtCanteen.isActive : false);
        const allergy = data.features.find(x => x.type == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.Allergies);
        this.allergy.setValue(allergy ? allergy.isActive : false);
        const uniform = data.features.find(x => x.type == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.Uniform);
        this.uniform.setValue(uniform ? uniform.isActive : false);
        this.uniformDescription.setValue(uniform?.description ? uniform.description : '');
        if (this.uniform.value) {
          this.uniformDescription.disable();
        } else {
          this.uniformDescription.enable();
        }
      }
    }
  }
  SaveFeatures() {
    if (this.formGroup.valid) {
      let request = [];
      // allergy
      let allergyFeature = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeature();
      allergyFeature.type = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.Allergies;
      allergyFeature.isActive = this.allergy.value;
      request.push(allergyFeature);
      // pay at canteen
      let paycanteenFeature = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeature();
      paycanteenFeature.type = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.PayAtCanteen;
      paycanteenFeature.isActive = this.payAtCanteen.value;
      request.push(paycanteenFeature);
      // pay at canteen
      let uniformFeature = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeature();
      uniformFeature.type = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolFeatureTypeEnum.Uniform;
      uniformFeature.isActive = this.uniform.value;
      uniformFeature.description = this.uniformDescription.value;
      request.push(uniformFeature);
      // call API
      this.schoolFeatureService.CreateFeatureApi(+this.schoolId, request).subscribe({
        next: res => {
          window.location.reload();
        },
        error: error => {
          this.ErrorModal('Saving features was unsuccessful', error);
        }
      });
    }
  }
  static {
    this.ɵfac = function AdminSchoolFeaturesComponent_Factory(t) {
      return new (t || AdminSchoolFeaturesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolFeatureService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: AdminSchoolFeaturesComponent,
      selectors: [["admin-school-features"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵInheritDefinitionFeature"]],
      decls: 21,
      vars: 3,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-12", "col-md-6"], [3, "formGroup"], [1, "col-12", "mt-3"], ["formControlName", "allergy"], ["formControlName", "payAtCanteen"], ["formControlName", "uniform"], ["class", "noUniformMerchant", 4, "ngIf"], [1, "col-12", "col-md-6", "mt-2"], ["placeholder", "Custom message to parents when uniform shop disable (optional)", "formControlName", "uniformDescription", "multiline", "true"], [1, "row", "mt-3", "mb-2"], [1, "col-4"], ["text", "Save", 3, "buttonStyle", "onPress"], [1, "noUniformMerchant"]],
      template: function AdminSchoolFeaturesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "school-panel")(4, "form", 3)(5, "div", 1)(6, "div", 4)(7, "mat-checkbox", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Allergies alert");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "div", 4)(10, "mat-checkbox", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](11, "Walk up orders");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "div", 4)(13, "mat-checkbox", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14, "Uniform shop");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, AdminSchoolFeaturesComponent_span_15_Template, 2, 0, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](17, "input-text", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "div", 11)(19, "div", 12)(20, "basic-button", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("onPress", function AdminSchoolFeaturesComponent_Template_basic_button_onPress_20_listener() {
            return ctx.SaveFeatures();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.formGroup);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.uniformShopAvailable);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("buttonStyle", 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControlName, _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_3__.SchoolPanelComponent, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_4__.InputTextComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_5__.BasicButtonComponent, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_10__.MatCheckbox],
      styles: [".noUniformMerchant[_ngcontent-%COMP%] {\n  margin-left: 15px;\n  font-size: 14px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tc2Nob29scy9jb21wb25lbnRzL2FkbWluLXNjaG9vbC1mZWF0dXJlcy9hZG1pbi1zY2hvb2wtZmVhdHVyZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLGVBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5ub1VuaWZvcm1NZXJjaGFudCB7XG4gIG1hcmdpbi1sZWZ0OiAxNXB4O1xuICBmb250LXNpemU6IDE0cHg7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 52195:
/*!***************************************************!*\
  !*** ./src/app/admin-schools/components/index.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddSchoolClassComponent: () => (/* reexport safe */ _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_0__.AddSchoolClassComponent),
/* harmony export */   AdminDetailSchoolComponent: () => (/* reexport safe */ _admin_detail_school_admin_detail_school_component__WEBPACK_IMPORTED_MODULE_1__.AdminDetailSchoolComponent),
/* harmony export */   AdminEventManagementComponent: () => (/* reexport safe */ _admin_event_management_admin_event_management_component__WEBPACK_IMPORTED_MODULE_7__.AdminEventManagementComponent),
/* harmony export */   AdminListSchoolsComponent: () => (/* reexport safe */ _admin_list_schools_admin_list_schools_component__WEBPACK_IMPORTED_MODULE_6__.AdminListSchoolsComponent),
/* harmony export */   AdminSchoolClassesComponent: () => (/* reexport safe */ _admin_school_classes_admin_school_classes_component__WEBPACK_IMPORTED_MODULE_2__.AdminSchoolClassesComponent),
/* harmony export */   AdminSchoolFeaturesComponent: () => (/* reexport safe */ _admin_school_features_admin_school_features_component__WEBPACK_IMPORTED_MODULE_8__.AdminSchoolFeaturesComponent),
/* harmony export */   ListClassesComponent: () => (/* reexport safe */ _list_classes_list_classes_component__WEBPACK_IMPORTED_MODULE_3__.ListClassesComponent),
/* harmony export */   SchoolNavBarComponent: () => (/* reexport safe */ _school_nav_bar_school_nav_bar_component__WEBPACK_IMPORTED_MODULE_5__.SchoolNavBarComponent),
/* harmony export */   StringArrayFormatPipe: () => (/* reexport safe */ _pipes_string_array_pipe__WEBPACK_IMPORTED_MODULE_4__.StringArrayFormatPipe)
/* harmony export */ });
/* harmony import */ var _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-school-class/add-school-class.component */ 27637);
/* harmony import */ var _admin_detail_school_admin_detail_school_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./admin-detail-school/admin-detail-school.component */ 90425);
/* harmony import */ var _admin_school_classes_admin_school_classes_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./admin-school-classes/admin-school-classes.component */ 47037);
/* harmony import */ var _list_classes_list_classes_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./list-classes/list-classes.component */ 36693);
/* harmony import */ var _pipes_string_array_pipe__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pipes/string-array.pipe */ 24693);
/* harmony import */ var _school_nav_bar_school_nav_bar_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./school-nav-bar/school-nav-bar.component */ 13627);
/* harmony import */ var _admin_list_schools_admin_list_schools_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./admin-list-schools/admin-list-schools.component */ 91783);
/* harmony import */ var _admin_event_management_admin_event_management_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./admin-event-management/admin-event-management.component */ 85187);
/* harmony import */ var _admin_school_features_admin_school_features_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./admin-school-features/admin-school-features.component */ 4197);










/***/ }),

/***/ 36693:
/*!*********************************************************************************!*\
  !*** ./src/app/admin-schools/components/list-classes/list-classes.component.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ListClassesComponent: () => (/* binding */ ListClassesComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _pipes_string_array_pipe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../pipes/string-array.pipe */ 24693);






function ListClassesComponent_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "th", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "No.");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function ListClassesComponent_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r12 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](element_r12.ClassId);
  }
}
function ListClassesComponent_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "th", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function ListClassesComponent_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r13 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](element_r13.Name);
  }
}
function ListClassesComponent_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "th", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Teacher");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function ListClassesComponent_td_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r14 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](element_r14.Teacher);
  }
}
function ListClassesComponent_th_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "th", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Year Group");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function ListClassesComponent_td_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](2, "stringArrayFormat");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r15 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind1"](2, 1, element_r15.YearGroup));
  }
}
function ListClassesComponent_th_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "th", 10);
  }
}
function ListClassesComponent_td_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "td", 11)(1, "mat-icon", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function ListClassesComponent_td_15_Template_mat_icon_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r18);
      const element_r16 = restoredCtx.$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r17.EditSchool(element_r16));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "edit");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function ListClassesComponent_tr_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "tr", 13);
  }
}
function ListClassesComponent_tr_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "tr", 14);
  }
}
class ListClassesComponent {
  constructor(adminService) {
    this.adminService = adminService;
    this.displayedColumns = ['id', 'name', 'teacher', 'yearGroup', 'options'];
    this.dataSource = new _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatTableDataSource();
  }
  ngOnInit() {
    this.RefreshTable(this.adminService.GetListClasses());
    this.addClassSbuscription = this.adminService.classesListUpdatedEvent$.subscribe(listClasses => {
      // Refresh table
      this.RefreshTable(listClasses);
    });
  }
  ngOnDestroy() {
    this.addClassSbuscription.unsubscribe();
  }
  EditSchool(schoolClass) {
    this.adminService.EditClassRequest(schoolClass);
  }
  RefreshTable(listClasses) {
    this.listClasses = listClasses;
    this.dataSource.data = this.listClasses;
  }
  static {
    this.ɵfac = function ListClassesComponent_Factory(t) {
      return new (t || ListClassesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_0__.AdminService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: ListClassesComponent,
      selectors: [["admin-list-classes"]],
      decls: 18,
      vars: 3,
      consts: [["mat-table", "", 1, "mat-elevation-z8", "tableau", "classesTable", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "name"], ["matColumnDef", "teacher"], ["matColumnDef", "yearGroup"], ["matColumnDef", "options", "stickyEnd", ""], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], ["matTooltip", "Edit", 1, "actionTableau", 3, "click"], ["mat-header-row", ""], ["mat-row", ""]],
      template: function ListClassesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "table", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](1, 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, ListClassesComponent_th_2_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, ListClassesComponent_td_3_Template, 2, 1, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](4, 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, ListClassesComponent_th_5_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, ListClassesComponent_td_6_Template, 2, 1, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](7, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](8, ListClassesComponent_th_8_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](9, ListClassesComponent_td_9_Template, 2, 1, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](10, 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](11, ListClassesComponent_th_11_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](12, ListClassesComponent_td_12_Template, 3, 3, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](13, 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](14, ListClassesComponent_th_14_Template, 1, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](15, ListClassesComponent_td_15_Template, 3, 0, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](16, ListClassesComponent_tr_16_Template, 1, 0, "tr", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](17, ListClassesComponent_tr_17_Template, 1, 0, "tr", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("dataSource", ctx.dataSource);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("matHeaderRowDef", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
        }
      },
      dependencies: [_angular_material_icon__WEBPACK_IMPORTED_MODULE_4__.MatIcon, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatRow, _pipes_string_array_pipe__WEBPACK_IMPORTED_MODULE_1__.StringArrayFormatPipe],
      styles: [".classesTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.actionTableau[_ngcontent-%COMP%] {\n  cursor: pointer;\n  margin-left: 5px;\n  margin-right: 5px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tc2Nob29scy9jb21wb25lbnRzL2xpc3QtY2xhc3Nlcy9saXN0LWNsYXNzZXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0FBQ0Y7O0FBRUE7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmNsYXNzZXNUYWJsZSB7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uYWN0aW9uVGFibGVhdSB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgbWFyZ2luLWxlZnQ6IDVweDtcbiAgbWFyZ2luLXJpZ2h0OiA1cHg7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 13627:
/*!*************************************************************************************!*\
  !*** ./src/app/admin-schools/components/school-nav-bar/school-nav-bar.component.ts ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolNavBarComponent: () => (/* binding */ SchoolNavBarComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);



const _c0 = function () {
  return ["details"];
};
const _c1 = function () {
  return ["classes"];
};
const _c2 = function () {
  return ["features"];
};
const _c3 = function () {
  return ["events"];
};
class SchoolNavBarComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function SchoolNavBarComponent_Factory(t) {
      return new (t || SchoolNavBarComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: SchoolNavBarComponent,
      selectors: [["app-school-nav-bar"]],
      decls: 17,
      vars: 8,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-6", "col-sm-3"], ["routerLink", "/admin/schools", "text", "Go back"], [1, "col-12"], [1, "tabReport"], ["routerLinkActive", "active", 3, "routerLink"]],
      template: function SchoolNavBarComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "nav-back-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 1)(5, "div", 4)(6, "ul", 5)(7, "li", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, "School Details");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "li", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "Classes");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "li", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](12, "Features");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](13, "li", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](14, "Events");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](16, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction0"](4, _c0));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction0"](5, _c1));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction0"](6, _c2));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction0"](7, _c3));
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterLinkActive, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_0__.NavBackButtonComponent],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.tabReport[_ngcontent-%COMP%] {\n  padding: 0;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  margin: auto;\n  font-size: 18px;\n  font-weight: bold;\n}\n.tabReport[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-left: 30px;\n  padding-bottom: 5px;\n  text-align: center;\n  cursor: pointer;\n}\n.tabReport[_ngcontent-%COMP%]   li.active[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  border-bottom: 2px solid #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 24693:
/*!**********************************************************!*\
  !*** ./src/app/admin-schools/pipes/string-array.pipe.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StringArrayFormatPipe: () => (/* binding */ StringArrayFormatPipe)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);

/**
 * transform an array that is formatted into a string split by commas to
 * normal sentence structure (add spaces after the commas)
 */
class StringArrayFormatPipe {
  constructor() {
    this.characterToModify = /,/g;
    this.characterToReplace = ', ';
  }
  transform(value) {
    if (!value || value.length === 1) {
      return value;
    }
    //add a space after each comma
    let formattedVal = value.replace(this.characterToModify, this.characterToReplace);
    //remove white space and remove last comma
    return formattedVal.trim().slice(0, -1);
  }
  static {
    this.ɵfac = function StringArrayFormatPipe_Factory(t) {
      return new (t || StringArrayFormatPipe)();
    };
  }
  static {
    this.ɵpipe = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefinePipe"]({
      name: "stringArrayFormat",
      type: StringArrayFormatPipe,
      pure: true
    });
  }
}

/***/ }),

/***/ 1268:
/*!*******************************************************************!*\
  !*** ./src/app/admin-schools/resolvers/list-features.resolver.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ListFeaturesResolver: () => (/* binding */ ListFeaturesResolver)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedServices */ 2902);

// service

const ListFeaturesResolver = route => {
  const schoolFeatureService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureService);
  let id = route.params['id'];
  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }
  return schoolFeatureService.GetFeaturesBySchoolApi(id);
};

/***/ }),

/***/ 98122:
/*!**********************************************************************!*\
  !*** ./src/app/admin-schools/resolvers/school-merchants.resolver.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolMerchantsResolver: () => (/* binding */ SchoolMerchantsResolver)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedServices */ 2902);

// services

const SchoolMerchantsResolver = route => {
  const schoolService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__.SchoolService);
  let id = route.params['id'];
  if (id == null || id == undefined) {
    id = route.parent.params['id'];
  }
  return schoolService.GetMerchantsPerSchoolAPI(id);
};

/***/ })

}]);
//# sourceMappingURL=src_app_admin-schools_admin-schools_module_ts.js.map