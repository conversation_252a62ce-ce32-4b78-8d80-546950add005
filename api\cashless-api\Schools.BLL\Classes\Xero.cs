﻿using System;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class XeroItemResponse
    {
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

        [JsonProperty(PropertyName = "Status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "Items")]
        public List<XeroItem> Items { get; set; }
    }

    public class XeroItem
    {
        [JsonProperty(PropertyName = "ItemID")]
        public string ItemID { get; set; }

        [JsonProperty(PropertyName = "Code")]
        public string Code { get; set; }

        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        [JsonProperty(PropertyName = "QuantityOnHand")]
        public float? QuantityOnHand { get; set; }
    }
}
