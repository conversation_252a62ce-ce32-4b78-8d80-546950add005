﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Schools.BLL.Classes.Menus;
using Schools.BLL.Classes.Notices;
using Schools.BLL.Classes.Orders;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Screens;

public class HomeScreenDto
{
    public IEnumerable<StudentEventDto> Events { get; set; }
    public IEnumerable<MenuDto> Menus { get; set; }
    public IEnumerable<OrderShorterDto> Orders { get; set; }
    public IEnumerable<DateSchool> SchoolDates { get; set; }
    public IEnumerable<Notice> Notices { get; set; }
    public bool HasUniformMenu { get; set; }
    public SupportedVersionsResponse SupportedAppVersions { get; set; }
}

public class HomeScreenRefreshDto
{
    public IEnumerable<OrderShorterDto> Orders { get; set; }
    public IEnumerable<DateSchool> SchoolDates { get; set; }
    public IEnumerable<StudentEventDto> Events { get; set; }
}

public class HomeScreenRefreshRequest : OrderHistoryStudentRequest
{
    public bool? LoadEvents;
}