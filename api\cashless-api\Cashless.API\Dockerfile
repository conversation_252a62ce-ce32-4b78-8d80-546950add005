#####################################################
# Test the solution but build the API project only  #
#####################################################
FROM mcr.microsoft.com/dotnet/sdk:6.0-alpine AS build
WORKDIR /app
COPY . .
RUN dotnet restore
RUN dotnet test cashlessApi.sln
RUN dotnet publish Cashless.API --output /dist --verbosity normal --no-restore

#####################################################
# Create the runtime to run the API                 #
#####################################################
FROM mcr.microsoft.com/dotnet/aspnet:6.0-alpine AS runtime
WORKDIR /app

# Install culture prerequisities for SQL Server health checks
RUN apk add --no-cache \
    icu-data-full \
    icu-libs

# Enable all cultures. This is set to true in base alpine image
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

COPY --from=build /dist /app
ENTRYPOINT [ "dotnet", "/app/Cashless.APIs.dll" ]
