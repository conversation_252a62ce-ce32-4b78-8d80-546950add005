﻿using System;
using Schools.BLL.Classes;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;
namespace Schools.BLL.Assemblers;

public interface IAddressAssembler
{
    AddressEntity ConvertAddressFormDtoToAddressEntity(AddressDto request);
    AddressEntity MergeAddressFormDtoIntoAddressEntity(AddressDto request, AddressEntity entity);
}

public class AddressAssembler : IAddressAssembler
{
    /// <summary>
    /// Convert from AddressFormDto to AddressEntity
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public AddressEntity ConvertAddressFormDtoToAddressEntity(AddressDto request)
    {
        AddressEntity entity = new()
        {
            AddressId = request.AddressId,
            City = request.City,
            StreetName = request.StreetName,
            StreetNumber = request.StreetNumber,
            PostCode = request.PostCode,
            StateId = request.StateId
        };

        return entity;
    }

    /// <summary>
    /// Merge DTO updates into the entity
    /// </summary>
    /// <param name="request"></param>
    /// <param name="entity"></param>
    /// <returns></returns>
    public AddressEntity MergeAddressFormDtoIntoAddressEntity(AddressDto request, AddressEntity entity)
    {
        entity.City = request.City;
        entity.StreetName = request.StreetName;
        entity.StreetNumber = request.StreetNumber;
        entity.PostCode = request.PostCode;
        entity.StateId = request.StateId;

        return entity;
    }
}
