﻿using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class SchoolTerm : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "TermId")]
        public long TermId { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "Name")]
        [MaxLength(50)]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "StartDate")]
        public DateTime StartDate { get; set; }

        [JsonProperty(PropertyName = "EndDate")]
        public DateTime EndDate { get; set; }
    }
}
