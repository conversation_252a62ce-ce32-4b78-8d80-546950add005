using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface ISchoolClassValidator
{
    Task ValidateUserAccessToSchool(long schoolId);
    String ValidateClassUpdateRequest(SchoolClassUpdateRequest request);
    String ValidateClassCreateRequest(SchoolClassCreateRequest request);
}

public class SchoolClassValidator : ISchoolClassValidator
{
    private readonly IUserService _userService;
    private readonly ICanteenService _canteenService;
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<SchoolClassValidator> _logger;
    public SchoolClassValidator(IUserService userService, ICanteenService canteenService, IAuthenticationValidator authenticationValidator, ILogger<SchoolClassValidator> logger)
    {
        _logger = logger;
        _authenticationValidator = authenticationValidator;
        _userService = userService;
        _canteenService = canteenService;
    }

    public async Task ValidateUserAccessToSchool(long schoolId)
    {
        await _authenticationValidator.ValidateAccessToSchool(schoolId);
    }

    public String ValidateClassUpdateRequest(SchoolClassUpdateRequest request)
    {
        if (request == null)
        {
            throw new ValidationException("schoolClass", "Invalid request");
        }

        if (String.IsNullOrEmpty(request.Name))
        {
            throw new ValidationException("Name", "Name must be supplied");
        }

        return null;
    }

    public String ValidateClassCreateRequest(SchoolClassCreateRequest request)
    {
        if (request == null)
        {
            throw new ValidationException("schoolClass", "Invalid request");
        }

        if (String.IsNullOrEmpty(request.Name))
        {
            throw new ValidationException("Name", "Name must be supplied");
        }

        return null;
    }
}