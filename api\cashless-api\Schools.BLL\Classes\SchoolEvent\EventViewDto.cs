using System;
using System.Collections.Generic;

namespace Schools.BLL.Classes;

public class EventViewDto
{
    public SchoolEventDto Event { get; set; }
    public List<EventItemsViewDto> Items { get; set; }
}

public class EventItemsViewDto
{
    public long MenuItemId { get; set; }
    public string ImageUrl { get; set; }
    public string Name { get; set; }
    public decimal Price { get; set; }
    public int? Stock { get; set; }
    public List<EventItemsOptionViewDto> Options { get; set; }
}

public class EventItemsOptionViewDto
{
    public long OptionId { get; set; }
    public string Name { get; set; }
    public List<EventItemsOPtionChoiceViewDto> Choices { get; set; }
}

public class EventItemsOPtionChoiceViewDto
{
    public string Name { get; set; }
    public decimal? Cost { get; set; }
}
