using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.BLL.Classes.Users;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    public class ListUsersResponse : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Users")]
        public List<EditStudentDto> Users { get; set; }
    }

    public class ListUsersResponseOld : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Users")]
        public List<User> Users { get; set; }
    }
}
