using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes
{
    public class ListOrders : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Orders")]
        public List<Order> Orders { get; set; }

        [JsonProperty(PropertyName = "Menus")]
        public List<Menu> Menus { get; set; }

        [JsonProperty(PropertyName = "Dates")]
        public List<DateSchool> Dates { get; set; }
    }
}
