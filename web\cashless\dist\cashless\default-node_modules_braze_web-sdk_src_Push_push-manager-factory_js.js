"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["default-node_modules_braze_web-sdk_src_Push_push-manager-factory_js"],{

/***/ 80297:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Push/push-manager-factory.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _push_manager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./push-manager.js */ 23726);


const na = {
  t: !1,
  i: null,
  m: () => (na.o(), na.i || (na.i = new _push_manager_js__WEBPACK_IMPORTED_MODULE_0__["default"](_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].br(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].Ma(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].te(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].Ys(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS._a), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS.ka), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].tr(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS.qa), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS.Aa), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].l())), na.i),
  o: () => {
    na.t || (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].g(na), na.t = !0);
  },
  destroy: () => {
    na.i = null, na.t = !1;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (na);

/***/ }),

/***/ 23726:
/*!**************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Push/push-manager.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ea)
/* harmony export */ });
/* harmony import */ var _util_code_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/code-utils.js */ 92133);
/* harmony import */ var _models_push_token_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../models/push-token.js */ 62766);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../managers/storage-manager.js */ 57309);
/* harmony import */ var _User_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../User/index.js */ 7646);
/* harmony import */ var _util_window_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/window-utils.js */ 71848);
/* harmony import */ var _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/push-utils.js */ 4794);
/* harmony import */ var _util_error_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/error-utils.js */ 9304);








class ea {
  constructor(i, t, e, s, r, n, o, u, h, a) {
    this.hn = i, this.cn = t, this.fn = e, this.dn = r, this.bn = n, this.wt = o, this.yn = u, this.gn = h, this.u = a, this.hn = i, this.cn = t, this.fn = e, this.wn = s + "/safari/" + t, this.dn = r || "/service-worker.js", this.bn = n, this.wt = o, this.yn = u || !1, this.gn = h || !1, this.u = a, this.vn = _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].kn(), this.Pn = _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].Dn();
  }
  Sn() {
    return this.gn;
  }
  An(i, t, e, s, n) {
    i.unsubscribe().then(i => {
      i ? this.jn(t, e, s, n) : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Failed to unsubscribe device from push."), "function" == typeof n && n(!1));
    }).catch(i => {
      _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Push unsubscription error: " + i), "function" == typeof n && n(!1);
    });
  }
  Un(i, t, e) {
    const s = (i => {
      if ("string" == typeof i) return i;
      if (0 !== i.endpoint.indexOf("https://android.googleapis.com/gcm/send")) return i.endpoint;
      let t = i.endpoint;
      const e = i;
      return e.Wn && -1 === i.endpoint.indexOf(e.Wn) && (t = i.endpoint + "/" + e.Wn), t;
    })(i);
    let r = null,
      n = null;
    const o = i;
    if (null != o.getKey) try {
      const i = Array.from(new Uint8Array(o.getKey("p256dh"))),
        t = Array.from(new Uint8Array(o.getKey("auth")));
      r = btoa(String.fromCharCode.apply(null, i)), n = btoa(String.fromCharCode.apply(null, t));
    } catch (i) {
      if ("invalid arguments" !== (0,_util_error_utils_js__WEBPACK_IMPORTED_MODULE_2__.getErrorMessage)(i)) throw i;
    }
    const u = (i => {
      let t;
      return i.options && (t = i.options.applicationServerKey) && t.byteLength && t.byteLength > 0 ? btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(t)))).replace(/\+/g, "-").replace(/\//g, "_") : null;
    })(o);
    this.hn && this.hn._n(s, t, r, n, u), s && "function" == typeof e && e(s, r, n);
  }
  xn() {
    this.hn && this.hn.Nn(!0);
  }
  Tn(i, t) {
    this.hn && this.hn.Nn(!1), _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(i), "function" == typeof t && t(!1);
  }
  zn(i, t, e, s) {
    var n;
    if ("default" === t.permission) try {
      window.safari.pushNotification.requestPermission(this.wn, i, {
        api_key: this.cn,
        device_id: (null === (n = this.fn) || void 0 === n ? void 0 : n.ce().id) || ""
      }, t => {
        "granted" === t.permission && this.hn && this.hn.setPushNotificationSubscriptionType(_User_index_js__WEBPACK_IMPORTED_MODULE_3__["default"].NotificationSubscriptionTypes.OPTED_IN), this.zn(i, t, e, s);
      });
    } catch (i) {
      this.Tn("Could not request permission for push: " + i, s);
    } else "denied" === t.permission ? this.Tn("The user has blocked notifications from this site, or Safari push is not configured in the Braze dashboard.", s) : "granted" === t.permission && (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device successfully subscribed to push."), this.Un(t.deviceToken, new Date(), e));
  }
  requestPermission(i, t, e) {
    const s = s => {
      switch (s) {
        case "granted":
          return void ("function" == typeof i && i());
        case "default":
          return void ("function" == typeof t && t());
        case "denied":
          return void ("function" == typeof e && e());
        default:
          _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Received unexpected permission result " + s);
      }
    };
    let n = !1;
    const o = window.Notification.requestPermission(i => {
      n && s(i);
    });
    o ? o.then(i => {
      s(i);
    }) : n = !0;
  }
  jn(i, t, e, s) {
    const n = {
      userVisibleOnly: !0
    };
    null != t && (n.applicationServerKey = t), i.pushManager.subscribe(n).then(i => {
      _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device successfully subscribed to push."), this.Un(i, new Date(), e);
    }).catch(i => {
      _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushBlocked() ? (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Permission for push notifications was denied."), "function" == typeof s && s(!1)) : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Push subscription failed: " + i), "function" == typeof s && s(!0));
    });
  }
  In() {
    return this.yn ? navigator.serviceWorker.getRegistration(this.dn) : navigator.serviceWorker.register(this.dn).then(() => navigator.serviceWorker.ready.then(i => (i && "function" == typeof i.update && i.update().catch(i => {
      _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("ServiceWorker update failed: " + i);
    }), i)));
  }
  Vn(i) {
    this.yn || (i.unregister(), _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Service worker successfully unregistered."));
  }
  subscribe(t, e) {
    if (!_utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushSupported()) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(ea.qn), void ("function" == typeof e && e(!1));
    if (this.vn) {
      if (!this.yn && null != window.location) {
        let i = this.dn;
        -1 === i.indexOf(window.location.host) && (i = window.location.host + i), -1 === i.indexOf(window.location.protocol) && (i = window.location.protocol + "//" + i);
        const t = i.substr(0, i.lastIndexOf("/") + 1);
        if (0 !== _util_window_utils_js__WEBPACK_IMPORTED_MODULE_4__.WindowUtils.Cn().indexOf(t)) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Cannot subscribe to push from a path higher than the service worker location (tried to subscribe from " + window.location.pathname + " but service worker is at " + i + ")"), void ("function" == typeof e && e(!0));
      }
      if (_utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushBlocked()) return void this.Tn("Notifications from this site are blocked. This may be a temporary embargo or a permanent denial.", e);
      if (this.wt && !this.wt.En() && 0 === this.wt.li()) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Waiting for VAPID key from server config before subscribing to push."), void this.wt.Rn(() => {
        this.subscribe(t, e);
      });
      const s = () => {
          _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Permission for push notifications was denied."), "function" == typeof e && e(!1);
        },
        n = () => {
          let i = "Permission for push notifications was ignored.";
          _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushBlocked() && (i += " The browser has automatically blocked further permission requests for a period (probably 1 week)."), _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(i), "function" == typeof e && e(!0);
        },
        o = _utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushPermissionGranted(),
        u = () => {
          !o && this.hn && this.hn.setPushNotificationSubscriptionType(_User_index_js__WEBPACK_IMPORTED_MODULE_3__["default"].NotificationSubscriptionTypes.OPTED_IN), this.In().then(s => {
            if (null == s) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("No service worker registration. Set the `manageServiceWorkerExternally` initialization option to false or ensure that your service worker is registered before calling registerPush."), void ("function" == typeof e && e(!0));
            s.pushManager.getSubscription().then(n => {
              let o = null;
              if (this.wt && null != this.wt.En() && (o = _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].On.Fn(this.wt.En())), n) {
                let u,
                  h = null,
                  a = null;
                if (this.u && (u = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.k.Bn)), u && !(0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_6__.isArray)(u)) {
                  let i;
                  try {
                    i = _models_push_token_js__WEBPACK_IMPORTED_MODULE_7__["default"].Yn(u).Mn;
                  } catch (t) {
                    i = null;
                  }
                  null == i || isNaN(i.getTime()) || 0 === i.getTime() || (h = i, a = new Date(h), a.setMonth(h.getMonth() + 6));
                }
                null != o && n.options && n.options.applicationServerKey && n.options.applicationServerKey.byteLength && n.options.applicationServerKey.byteLength > 0 && !(0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_6__.isEqual)(o, new Uint8Array(n.options.applicationServerKey)) ? (n.options.applicationServerKey.byteLength > 12 ? _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device was already subscribed to push using a different VAPID provider, creating new subscription.") : _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Attempting to upgrade a gcm_sender_id-based push registration to VAPID - depending on the browser this may or may not result in the same gcm_sender_id-based subscription."), this.An(n, s, o, t, e)) : n.expirationTime && new Date(n.expirationTime).valueOf() <= new Date().valueOf() ? (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Push subscription is expired, creating new subscription."), this.An(n, s, o, t, e)) : u && (0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_6__.isArray)(u) ? this.An(n, s, o, t, e) : null == a ? (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("No push subscription creation date found, creating new subscription."), this.An(n, s, o, t, e)) : a.valueOf() <= new Date().valueOf() ? (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Push subscription older than 6 months, creating new subscription."), this.An(n, s, o, t, e)) : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device already subscribed to push, sending existing subscription to backend."), this.Un(n, h, t));
              } else this.jn(s, o, t, e);
            }).catch(i => {
              _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Error checking current push subscriptions: " + i);
            });
          }).catch(i => {
            _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("ServiceWorker registration failed: " + i);
          });
        };
      this.requestPermission(u, n, s);
    } else if (this.Pn) {
      if (null == this.bn || "" === this.bn) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("You must supply the safariWebsitePushId initialization option in order to use registerPush on Safari"), void ("function" == typeof e && e(!0));
      const i = window.safari.pushNotification.permission(this.bn);
      this.zn(this.bn, i, t, e);
    }
  }
  unsubscribe(i, t) {
    if (!_utils_push_utils_js__WEBPACK_IMPORTED_MODULE_0__["default"].isPushSupported()) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(ea.qn), void ("function" == typeof t && t());
    this.vn ? navigator.serviceWorker.getRegistration().then(e => {
      e ? e.pushManager.getSubscription().then(s => {
        s && (this.xn(), s.unsubscribe().then(s => {
          s ? (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device successfully unsubscribed from push."), "function" == typeof i && i()) : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Failed to unsubscribe device from push."), "function" == typeof t && t()), this.Vn(e);
        }).catch(i => {
          _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Push unsubscription error: " + i), "function" == typeof t && t();
        }));
      }).catch(i => {
        _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error("Error unsubscribing from push: " + i), "function" == typeof t && t();
      }) : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device already unsubscribed from push."), "function" == typeof i && i());
    }) : this.Pn && (this.xn(), _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info("Device unsubscribed from push."), "function" == typeof i && i());
  }
}
ea.qn = "Push notifications are not supported in this browser.";

/***/ })

}]);
//# sourceMappingURL=default-node_modules_braze_web-sdk_src_Push_push-manager-factory_js.js.map