using System;
using System.ComponentModel.DataAnnotations;

using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;
namespace Schools.BLL.Classes.Notices
{
    public class UpdateNoticeStatusRequest
    {
        [JsonProperty(PropertyName = "NoticeId")]
        [Required]
        [Range(1, long.MaxValue)]
        public long NoticeId { get; set; }

        [JsonProperty(PropertyName = "NoticeStatus")]
        public NoticeStatus NoticeStatus { get; set; }

        [JsonProperty(PropertyName = "ValidationDescription")]
        public string ValidationDescription { get; set; }
    }
}
