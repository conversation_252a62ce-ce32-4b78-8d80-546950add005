﻿using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders;
using Schools.BLL.Classes.Orders.Events;
using Schools.BLL.Exceptions;
using Schools.BLL.Helpers;
using Schools.DAL.Entities;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Schools.Orders.Services;

public class OrderStatusService : SchoolsApiClient, IOrderStatusService
{
    private const string CreateErrorUrl = "OrderStatus/Error/";

    private readonly ILogger<OrderService> _logger;

    public OrderStatusService(ILogger<OrderService> logger, IHttpClientFactory httpClientFactory, IConfiguration config, ILogger<SchoolsApiClient> baseLogger) : base(httpClientFactory, config, baseLogger)
    {
        _logger = logger;
    }

    public async Task SaveErrorOrderCreatedDetails(ConsumeContext<Fault<OrdersCreated>> context)
    {
        await GetErrorDetails(context.Message.Message.Orders, context.SourceAddress?.AbsolutePath, context.Message.Exceptions);
    }

    public async Task SaveErrorOrderEditedDetails(ConsumeContext<Fault<OrdersEdited>> context)
    {
        await GetErrorDetails(new List<OrderPublishInfo>() { context.Message.Message.Order }, context.SourceAddress?.AbsolutePath, context.Message.Exceptions);
    }

    private async Task GetErrorDetails(IEnumerable<OrderPublishInfo> orders, string sourcePath, ExceptionInfo[] exceptions)
    {
        var exception = exceptions?.First();

        // Use early exit instead of nested ifs
        if (exception == null || string.IsNullOrEmpty(sourcePath))
        {
            return;
        }

        foreach (var order in orders)
        {
            var error = new CreateOrderErrorDto
            {
                CorrelationId = order.CorrelationId,
                Message = exception.Message + exception.InnerException != null ?
                                $"; {exception.InnerException.Message}" : string.Empty,
                Details = $"Data => {JsonSerializer.Serialize(exception.StackTrace)};",
            };

            if (sourcePath.Contains("Payment"))
            {
                error.Type = OrderStatusProcessStepEnum.Payment;
            }
            else if (sourcePath.Contains("Stock"))
            {
                error.Type = OrderStatusProcessStepEnum.Stock;
            }
            else
            {
                error.Type = OrderStatusProcessStepEnum.Notification;
            }

            await SaveError(error);
        }
    }

    private async Task SaveError(CreateOrderErrorDto request)
    {
        if (request == null)
        {
            throw new OrderFunctionException("No CreateOrderError provided");
        }

        using (var client = await GetHttpClient(typeof(FailedOrdersRequest).Name))
        {
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, ContentTypeJson);

            var response = await client.PostAsync(CreateErrorUrl, content);
        }
    }
}