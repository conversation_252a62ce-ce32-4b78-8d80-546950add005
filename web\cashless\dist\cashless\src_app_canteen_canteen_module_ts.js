"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_canteen_canteen_module_ts"],{

/***/ 16162:
/*!***************************************************!*\
  !*** ./src/app/canteen/canteen-routing.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenRoutingModule: () => (/* binding */ CanteenRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_merchant_navbar_merchant_navbar_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/components/merchant-navbar/merchant-navbar.component */ 83433);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components */ 94608);
/* harmony import */ var _shared_tools_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared-tools/components */ 17009);
/* harmony import */ var _sharedServices_canteen_menu_editor_guard_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../sharedServices/canteen/menu-editor-guard.service */ 2225);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../sharedServices */ 2902);
/* harmony import */ var _sharedServices_canteen_sales_report_guard_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../sharedServices/canteen/sales-report-guard.service */ 15531);
/* harmony import */ var _event_management_event_management_routes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../event-management/event-management.routes */ 92307);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);

// components


// Shared tools components


// Services





const routes = [{
  path: '',
  component: _shared_components_merchant_navbar_merchant_navbar_component__WEBPACK_IMPORTED_MODULE_0__.MerchantNavbarComponent,
  children: [{
    path: '',
    pathMatch: 'full',
    redirectTo: 'home'
  }, {
    path: 'home',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.CanteenComponent,
    resolve: {
      canteens: _sharedServices__WEBPACK_IMPORTED_MODULE_4__.ListCanteensResolver
    }
  }, {
    path: 'order',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_manage-order_manage-order_module_ts"), __webpack_require__.e("common")]).then(__webpack_require__.bind(__webpack_require__, /*! ../manage-order/manage-order.module */ 32261)).then(m => m.ManageOrderModule)
  }, {
    path: 'settings',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_schools-events_schools-events_module_ts"), __webpack_require__.e("src_app_canteen-settings_canteen-settings_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ../canteen-settings/canteen-settings.module */ 11953)).then(m => m.CanteenSettingsModule)
  }, {
    path: 'reports',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_reports_reports_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ../reports/reports.module */ 31943)).then(m => m.ReportsModule)
  }, {
    path: 'students',
    component: _shared_tools_components__WEBPACK_IMPORTED_MODULE_2__.ListAccountComponent,
    resolve: {
      canteens: _sharedServices__WEBPACK_IMPORTED_MODULE_4__.ListCanteensResolver
    }
  }, {
    path: 'students/details/:id',
    component: _shared_tools_components__WEBPACK_IMPORTED_MODULE_2__.UserDetailsPageComponent,
    resolve: {
      user: _sharedServices__WEBPACK_IMPORTED_MODULE_4__.UserDetailsResolver
    }
  }, {
    path: 'labels',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.LabelPrintComponent
  }, {
    path: 'notice',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.NoticeBoardComponent
  }, {
    path: 'notice/announcements',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.AnnouncementsComponent
  }, {
    path: 'notice/articles',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.ArticlesComponent
  }, {
    path: 'editor',
    loadChildren: () => Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../menu-editor/menu-editor.module */ 92327)).then(m => m.MenuEditorModule),
    canActivateChild: [_sharedServices_canteen_menu_editor_guard_service__WEBPACK_IMPORTED_MODULE_3__.MenuEditorGuardService]
  }, {
    path: 'events',
    loadChildren: () => _event_management_event_management_routes__WEBPACK_IMPORTED_MODULE_6__.eventManagementRoutes,
    resolve: {
      merchants: _sharedServices__WEBPACK_IMPORTED_MODULE_4__.ListCanteensResolver
    }
  }, {
    path: 'pos',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_manage-order_manage-order_module_ts"), __webpack_require__.e("common"), __webpack_require__.e("src_app_pos_pos_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ../pos/pos.module */ 98375)).then(m => m.PosModule)
  }]
}, {
  path: 'iosPrint/:displaySchoolName',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.IosLabelsPrintingComponent
}];
class CanteenRoutingModule {
  static {
    this.ɵfac = function CanteenRoutingModule_Factory(t) {
      return new (t || CanteenRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineNgModule"]({
      type: CanteenRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjector"]({
      providers: [_sharedServices_canteen_menu_editor_guard_service__WEBPACK_IMPORTED_MODULE_3__.MenuEditorGuardService, _sharedServices_canteen_sales_report_guard_service__WEBPACK_IMPORTED_MODULE_5__.SalesReportGuardService],
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsetNgModuleScope"](CanteenRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule]
  });
})();

/***/ }),

/***/ 15499:
/*!*******************************************!*\
  !*** ./src/app/canteen/canteen.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenModule: () => (/* binding */ CanteenModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var ngx_print__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ngx-print */ 33133);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/datepicker */ 61977);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/paginator */ 24624);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/radio */ 53804);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @angular/material/menu */ 31034);
/* harmony import */ var _canteen_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canteen-routing.module */ 16162);
/* harmony import */ var _account_account_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../account/account.module */ 90359);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-tools/shared-tools.module */ 23879);
/* harmony import */ var _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../schools-form/schools-form.module */ 97162);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components */ 94608);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);



// google material





















// Components


class CanteenModule {
  static {
    this.ɵfac = function CanteenModule_Factory(t) {
      return new (t || CanteenModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineNgModule"]({
      type: CanteenModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjector"]({
      providers: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.DatePipe],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, ngx_print__WEBPACK_IMPORTED_MODULE_10__.NgxPrintModule, _canteen_routing_module__WEBPACK_IMPORTED_MODULE_0__.CanteenRoutingModule, _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsButtonModule,
      // material
      _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatFormFieldModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__.MatRadioModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__.MatCheckboxModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_14__.MatExpansionModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_15__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_16__.MatTableModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_18__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_19__.MatButtonModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_20__.MatSortModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_21__.MatPaginatorModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_22__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_23__.MatDatepickerModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_24__.MatTooltipModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_25__.MatMenuModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsFormModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_24__.MatTooltipModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsetNgModuleScope"](CanteenModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_6__.CanteenComponent, _components__WEBPACK_IMPORTED_MODULE_6__.LabelPrintComponent, _components__WEBPACK_IMPORTED_MODULE_6__.BlockPrintComponent, _components__WEBPACK_IMPORTED_MODULE_6__.IosLabelsPrintingComponent, _components__WEBPACK_IMPORTED_MODULE_6__.NoticeBoardComponent, _components__WEBPACK_IMPORTED_MODULE_6__.AnnouncementsComponent, _components__WEBPACK_IMPORTED_MODULE_6__.ArticlesComponent, _components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderFilterComponent, _components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderSchoolFilterComponent, _components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderTypeFilterComponent, _components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderCategoryFilterComponent, _components__WEBPACK_IMPORTED_MODULE_6__.A4PrintFormComponent, _components__WEBPACK_IMPORTED_MODULE_6__.NoticeHeaderComponent, _components__WEBPACK_IMPORTED_MODULE_6__.NoticeHeaderComponent, _components__WEBPACK_IMPORTED_MODULE_6__.NoticeTableComponent, _components__WEBPACK_IMPORTED_MODULE_6__.UniformOrderStatusPickerComponent, _components__WEBPACK_IMPORTED_MODULE_6__.SelectedOrderComponent, _components__WEBPACK_IMPORTED_MODULE_6__.LabelTemplateComponent, _components__WEBPACK_IMPORTED_MODULE_6__.LabelTemplateIosComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, ngx_print__WEBPACK_IMPORTED_MODULE_10__.NgxPrintModule, _canteen_routing_module__WEBPACK_IMPORTED_MODULE_0__.CanteenRoutingModule, _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsButtonModule,
    // material
    _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatFormFieldModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_12__.MatRadioModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__.MatCheckboxModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_14__.MatExpansionModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_15__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_16__.MatTableModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_18__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_19__.MatButtonModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_20__.MatSortModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_21__.MatPaginatorModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_22__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_23__.MatDatepickerModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_24__.MatTooltipModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_25__.MatMenuModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsFormModule],
    exports: [_components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderTypeFilterComponent, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_24__.MatTooltipModule, _components__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderCategoryFilterComponent]
  });
})();

/***/ }),

/***/ 122:
/*!*****************************************************************************!*\
  !*** ./src/app/canteen/components/a4-print-form/a4-print-form.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A4PrintFormComponent: () => (/* binding */ A4PrintFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/core */ 74646);











function A4PrintFormComponent_form_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "form", 2)(1, "div", 3)(2, "div", 4)(3, "mat-form-field", 5)(4, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, "Settings");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "mat-select", 6)(7, "mat-option", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8, " Chrome Default ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "mat-option", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, " Chrome No Margins ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](11, "div", 4)(12, "mat-form-field", 8)(13, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "Start line");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "mat-select", 9)(16, "mat-option", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](17, "1");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "mat-option", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "2");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](20, "mat-option", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](21, "3");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](22, "mat-option", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](23, "4");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](24, "mat-option", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, "5");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "mat-option", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, "6");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "mat-option", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29, "7");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](30, "div", 4)(31, "mat-form-field", 8)(32, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](33, "Start column");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](34, "mat-select", 17)(35, "mat-option", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36, "1");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "mat-option", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](38, "2");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](39, "mat-option", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](40, "3");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](41, "div", 18)(42, "mat-checkbox", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](43, "Display School Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx_r0.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx_r0.printSettings.Default);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx_r0.printSettings.Custom);
  }
}
class A4PrintFormComponent {
  constructor(canteenService) {
    this.canteenService = canteenService;
    this.generate = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.updateCustomValue = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.sliceLabels = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.updateDisplaySchool = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.printSettings = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.A4PrintSettings;
  }
  ngOnInit() {
    this._createForm();
  }
  _createForm() {
    let settings = 'default';
    this.updateCustomValue.emit(false);
    let savedSetting = this.canteenService.GetPrintingSetting();
    let schoolNameSetting = this.canteenService.GetSchoolNamesOnA4Labels() === 'true';
    this.updateDisplaySchool.emit(schoolNameSetting);
    if (savedSetting && savedSetting != settings) {
      settings = savedSetting;
      this.updateCustomValue.emit(false);
    }
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({
      line: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl('1', [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required]),
      column: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl('1', [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required]),
      settings: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(settings),
      displaySchoolName: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(schoolNameSetting)
    });
    this.sliceLabels.emit();
    this.line.valueChanges.subscribe(val => {
      this.generateLabels();
    });
    this.column.valueChanges.subscribe(val => {
      this.generateLabels();
    });
    this.settings.valueChanges.subscribe(val => {
      this.updateCustomValue.emit(Boolean(val === 'custom'));
      this.canteenService.SetPrintingSetting(val);
    });
    this.displaySchoolName.valueChanges.subscribe(val => {
      this.updateDisplaySchool.emit(val);
      this.canteenService.setSchoolNamesOnA4Labels(val);
    });
  }
  generateLabels() {
    const data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.A4PrintPositions();
    data.Line = this.line.value;
    data.Column = this.column.value;
    this.generate.emit(data);
  }
  get line() {
    return this.form.get('line');
  }
  get column() {
    return this.form.get('column');
  }
  get settings() {
    return this.form.get('settings');
  }
  get displaySchoolName() {
    return this.form.get('displaySchoolName');
  }
  static {
    this.ɵfac = function A4PrintFormComponent_Factory(t) {
      return new (t || A4PrintFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.CanteenService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: A4PrintFormComponent,
      selectors: [["a4-print-form"]],
      outputs: {
        generate: "generate",
        updateCustomValue: "updateCustomValue",
        sliceLabels: "sliceLabels",
        updateDisplaySchool: "updateDisplaySchool"
      },
      decls: 2,
      vars: 1,
      consts: [[1, "container-fluid"], [3, "formGroup", 4, "ngIf"], [3, "formGroup"], [1, "row"], [1, "col-12", "col-sm-4", "col-md-2"], ["appearance", "outline", 1, "fullWidthInput"], ["formControlName", "settings"], [3, "value"], ["appearance", "outline"], ["formControlName", "line", "required", ""], ["value", "1"], ["value", "2"], ["value", "3"], ["value", "4"], ["value", "5"], ["value", "6"], ["value", "7"], ["formControlName", "column", "required", ""], [1, "col-12", "col-sm-4", "col-md-2", "d-flex", "align-items-center"], ["formControlName", "displaySchoolName"]],
      template: function A4PrintFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, A4PrintFormComponent_form_1_Template, 44, 3, "form", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.form);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_3__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControlName, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_5__.MatLabel, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_6__.MatCheckbox, _angular_material_select__WEBPACK_IMPORTED_MODULE_7__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_8__.MatOption],
      styles: ["h4[_ngcontent-%COMP%] {\n  width: 100%;\n  text-align: right;\n}\n\nmat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.fullWidthInput[_ngcontent-%COMP%] {\n  width: 100%;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi9jb21wb25lbnRzL2E0LXByaW50LWZvcm0vYTQtcHJpbnQtZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFdBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUNBO0VBQ0UsV0FBQTtBQUVGOztBQUNBO0VBQ0UsV0FBQTtBQUVGIiwic291cmNlc0NvbnRlbnQiOlsiaDQge1xuICB3aWR0aDogMTAwJTtcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XG59XG5tYXQtZm9ybS1maWVsZCB7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uZnVsbFdpZHRoSW5wdXQge1xuICB3aWR0aDogMTAwJTtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 24422:
/*!*****************************************************************************!*\
  !*** ./src/app/canteen/components/announcements/announcements.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AnnouncementsComponent: () => (/* binding */ AnnouncementsComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/datepicker */ 61977);
/* harmony import */ var _schools_form_components_form_buttons_form_buttons_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/form-buttons/form-buttons.component */ 85274);
/* harmony import */ var _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../notice-header/notice-header.component */ 84334);
/* harmony import */ var _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../notice-table/notice-table.component */ 7750);

//Models















function AnnouncementsComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 25)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "Validation explanation");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](ctx_r2.selectedNotice.ValidationDescription);
  }
}
const _c0 = function (a0) {
  return {
    invisible: a0
  };
};
class AnnouncementsComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.NoticeBoardComponent {
  constructor(_location, spinnerService, noticeService, userService, dialog) {
    super(noticeService, spinnerService, userService, dialog);
    this._location = _location;
    this.spinnerService = spinnerService;
    this.noticeService = noticeService;
    this.userService = userService;
    this.dialog = dialog;
    this.date = new Date();
  }
  ngOnInit() {
    this.currentNoticeType = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.NoticeType.Announcement;
    this.CreateForm(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Notice());
  }
  GoBackClick() {
    this._location.back();
  }
  openEditForm(announcement) {
    this.selectedNotice = announcement;
    this.CreateForm(announcement);
    this.showNoticeForm = true;
  }
  CreateForm(announcement) {
    const filteredAnnouncements = this.noticeData && this.noticeData.filter(announcement => announcement.IsActive == true) || [];
    const formDate = announcement.EndDate ? (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.ConvertToUniversalDateFormat)(announcement.EndDate) : null;
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
      title: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(announcement.Title || '', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.minLength(3)]),
      description: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(announcement.Description || ''),
      endDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(formDate),
      isActive: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(this.isAnnouncementActive(announcement))
    });
  }
  isAnnouncementActive(announcement) {
    const newAnnouncment = !announcement.NoticeId;
    return announcement?.IsActive || newAnnouncment;
  }
  AddNotice() {
    this.showNoticeForm = true;
    this.selectedNotice = null;
    this.CreateForm(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Notice());
  }
  IsSubmitButtonActive() {
    return !this.form.valid;
  }
  static {
    this.ɵfac = function AnnouncementsComponent_Factory(t) {
      return new (t || AnnouncementsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_9__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.NoticeService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: AnnouncementsComponent,
      selectors: [["app-announcements"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"]],
      decls: 38,
      vars: 12,
      consts: [[1, "col-12"], [1, "col-12", "col-md-8"], ["text", "Go Back", 1, "backButton", 3, "navBack"], ["title", "Announcement", 3, "schoolChanged", "merchantChanged", "openForm"], [1, "col-12", "row", "infoBlock"], ["type", "NoticeType.Announcement", 3, "tableData", "selectRow"], [1, "col-12", "col-md-4", 3, "ngClass"], [1, "cardWrapper"], [1, "crossIconWrapper"], [1, "closeBtn", 3, "click"], ["src", "assets/icons/cross.svg"], [1, "cashlessForm", 3, "formGroup"], ["appearance", "outline"], ["maxlength", "50", "matInput", "", "placeholder", "Enter title of announcement", "formControlName", "title", "type", "text"], ["maxlength", "200", "rows", "1", "matInput", "", "placeholder", "Add a description", "formControlName", "description", "type", "text", "rows", "3", 1, "description"], ["description", ""], ["align", "end"], ["matInput", "", "formControlName", "endDate", "readonly", "", 3, "matDatepicker", "min"], ["matIconSuffix", "", 3, "for"], ["picker1", ""], ["formControlName", "isActive"], [1, "checkboxLabel"], [1, "separator"], ["class", "validationExplanation", 4, "ngIf"], [3, "disableSaveButton", "showDeleteButton", "saveEvent", "cancelEvent", "deleteEvent"], [1, "validationExplanation"]],
      template: function AnnouncementsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "nav-back-button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("navBack", function AnnouncementsComponent_Template_nav_back_button_navBack_2_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "div", 0)(4, "notice-header", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("schoolChanged", function AnnouncementsComponent_Template_notice_header_schoolChanged_4_listener($event) {
            return ctx.OnSchoolSelect($event);
          })("merchantChanged", function AnnouncementsComponent_Template_notice_header_merchantChanged_4_listener($event) {
            return ctx.OnMerchantChanged($event);
          })("openForm", function AnnouncementsComponent_Template_notice_header_openForm_4_listener() {
            return ctx.AddNotice();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "div", 4)(6, "div", 1)(7, "notice-table", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("selectRow", function AnnouncementsComponent_Template_notice_table_selectRow_7_listener($event) {
            return ctx.openEditForm($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](8, "div", 6)(9, "div", 7)(10, "div", 8)(11, "a", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function AnnouncementsComponent_Template_a_click_11_listener() {
            return ctx.hideForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](12, "img", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](13, "form", 11)(14, "mat-form-field", 12)(15, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](16, "Title");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](17, "input", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](18, "mat-form-field", 12)(19, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](20, "Description (optional)");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](21, "textarea", 14, 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](23, "mat-hint", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](24);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](25, "mat-form-field", 12)(26, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](27, "End date (optional)");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](28, "input", 17)(29, "mat-datepicker-toggle", 18)(30, "mat-datepicker", null, 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](32, "mat-checkbox", 20)(33, "p", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](34, "Active");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](36, AnnouncementsComponent_div_36_Template, 5, 1, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](37, "form-buttons", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("saveEvent", function AnnouncementsComponent_Template_form_buttons_saveEvent_37_listener() {
            return ctx.saveNotice();
          })("cancelEvent", function AnnouncementsComponent_Template_form_buttons_cancelEvent_37_listener() {
            return ctx.hideForm();
          })("deleteEvent", function AnnouncementsComponent_Template_form_buttons_deleteEvent_37_listener() {
            return ctx.deleteNoticeCheck();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          const _r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵreference"](22);
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵreference"](31);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("tableData", ctx.noticeData);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpureFunction1"](10, _c0, !ctx.showNoticeForm));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate1"]("", _r0.value.length, " / 200");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("matDatepicker", _r1)("min", ctx.date);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("for", _r1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.selectedNotice && ctx.selectedNotice.Status == ctx.NoticeStatusEnum.Refused);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("disableSaveButton", ctx.IsSubmitButtonActive())("showDeleteButton", ctx.showDeleteButton());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.MaxLengthValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatHint, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatSuffix, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_12__.MatCheckbox, _angular_material_input__WEBPACK_IMPORTED_MODULE_13__.MatInput, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_14__.MatDatepicker, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_14__.MatDatepickerInput, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_14__.MatDatepickerToggle, _schools_form_components_form_buttons_form_buttons_component__WEBPACK_IMPORTED_MODULE_4__.FormButtonsComponent, _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_5__.NoticeHeaderComponent, _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_6__.NoticeTableComponent],
      styles: [".backButton[_ngcontent-%COMP%] {\n  color: orange;\n  font-size: 14px;\n}\n\n.cardWrapper[_ngcontent-%COMP%] {\n  background-color: white;\n  border-radius: 12px;\n  padding-top: 20px;\n  padding-bottom: 23px;\n  padding-right: 15px;\n  padding-left: 20px;\n}\n\n.crossIconWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-right: 2px;\n  cursor: pointer;\n}\n\n.invisible[_ngcontent-%COMP%] {\n  display: none;\n}\n\n.inputTitle[_ngcontent-%COMP%]:last-of-type {\n  margin-bottom: 0;\n}\n\n.image[_ngcontent-%COMP%] {\n  width: 28px;\n}\n\n.separator[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #dadada;\n  height: 1px;\n  margin-top: 10px;\n}\n\n.infoBlock[_ngcontent-%COMP%] {\n  padding-bottom: 30px;\n}\n\n.tableElement[_ngcontent-%COMP%] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  padding-right: 10px;\n  white-space: pre-line;\n  vertical-align: top;\n}\n\n.closeBtn[_ngcontent-%COMP%] {\n  padding: 5px;\n}\n\n.validationExplanation[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-bottom: 0;\n  font-weight: bold;\n}\n.validationExplanation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: 50px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 62990:
/*!*******************************************************************!*\
  !*** ./src/app/canteen/components/articles/articles.component.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ArticlesComponent: () => (/* binding */ ArticlesComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _schools_form_components_form_buttons_form_buttons_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-form/components/form-buttons/form-buttons.component */ 85274);
/* harmony import */ var _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../notice-header/notice-header.component */ 84334);
/* harmony import */ var _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../notice-table/notice-table.component */ 7750);

// Models












function ArticlesComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 21)(1, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "Validation explanation");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r1.selectedNotice.ValidationDescription);
  }
}
const _c0 = function (a0) {
  return {
    invisible: a0
  };
};
class ArticlesComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.NoticeBoardComponent {
  constructor(_location, spinnerService, noticeService, userService, dialog) {
    super(noticeService, spinnerService, userService, dialog);
    this._location = _location;
    this.spinnerService = spinnerService;
    this.noticeService = noticeService;
    this.userService = userService;
    this.dialog = dialog;
    this.displayedColumns = ['title', 'body', 'IsActive', 'Status'];
  }
  ngOnInit() {
    this.currentNoticeType = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.NoticeType.Article;
    this.CreateForm(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Notice());
  }
  GoBackClick() {
    this._location.back();
  }
  CreateForm(article) {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroup({
      title: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(article.Title || ''),
      description: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(article.Description || ''),
      endDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(article.EndDate),
      isActive: new _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControl(article.IsActive === false ? false : true)
    });
  }
  AddNotice() {
    this.showNoticeForm = true;
    this.selectedNotice = null;
    this.CreateForm(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Notice());
  }
  IsSubmitButtonActive() {
    return !this.form.get('title').value || !this.form.get('description').value;
  }
  openEditForm(article) {
    this.selectedNotice = article;
    this.CreateForm(article);
    this.showNoticeForm = true;
  }
  get bodyFill() {
    return this.form.get('description').value ? true : false;
  }
  autoGrowTextZone(e) {
    if (!this.bodyFill) {
      e.target.style.height = '300px';
      return;
    }
    e.target.style.height = e.target.scrollHeight + 'px';
  }
  static {
    this.ɵfac = function ArticlesComponent_Factory(t) {
      return new (t || ArticlesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.NoticeService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: ArticlesComponent,
      selectors: [["app-articles"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵInheritDefinitionFeature"]],
      decls: 28,
      vars: 9,
      consts: [[1, "col-12"], [1, "col-12", "col-md-8"], ["text", "Go Back", 1, "backButton", 3, "navBack"], ["title", "Article", 3, "schoolChanged", "merchantChanged", "openForm"], [1, "col-12", "row", "infoBlock"], ["type", "NoticeType.Article", 3, "tableData", "selectRow"], [1, "col-12", "col-md-4", 3, "ngClass"], [1, "cardWrapper"], [1, "cardWrapperPadding"], [1, "crossIconWrapper"], [1, "closeBtn", 3, "click"], ["src", "assets/icons/cross.svg"], [1, "cashlessForm", 3, "formGroup"], ["appearance", "outline"], ["maxlength", "50", "matInput", "", "placeholder", "Enter title of article", "formControlName", "title", "type", "text"], ["maxlength", "1000", "rows", "1", "matInput", "", "placeholder", "Add article content", "formControlName", "description", "type", "text", "rows", "5", 1, "description"], ["description", ""], ["align", "end"], [1, "separator", "bottom"], ["class", "validationExplanation", 4, "ngIf"], [3, "disableSaveButton", "showDeleteButton", "saveEvent", "cancelEvent", "deleteEvent"], [1, "validationExplanation"]],
      template: function ArticlesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "nav-back-button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("navBack", function ArticlesComponent_Template_nav_back_button_navBack_2_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "notice-header", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("schoolChanged", function ArticlesComponent_Template_notice_header_schoolChanged_3_listener($event) {
            return ctx.OnSchoolSelect($event);
          })("merchantChanged", function ArticlesComponent_Template_notice_header_merchantChanged_3_listener($event) {
            return ctx.OnMerchantChanged($event);
          })("openForm", function ArticlesComponent_Template_notice_header_openForm_3_listener() {
            return ctx.AddNotice();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 4)(5, "div", 1)(6, "notice-table", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("selectRow", function ArticlesComponent_Template_notice_table_selectRow_6_listener($event) {
            return ctx.openEditForm($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "div", 6)(8, "div", 7)(9, "div", 8)(10, "div", 9)(11, "a", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ArticlesComponent_Template_a_click_11_listener() {
            return ctx.hideForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "img", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "form", 12)(14, "mat-form-field", 13)(15, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](16, "Title");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](17, "input", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "mat-form-field", 13)(19, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](20, "Body");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](21, "textarea", 15, 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "mat-hint", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](25, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](26, ArticlesComponent_div_26_Template, 5, 1, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](27, "form-buttons", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("saveEvent", function ArticlesComponent_Template_form_buttons_saveEvent_27_listener() {
            return ctx.saveNotice();
          })("cancelEvent", function ArticlesComponent_Template_form_buttons_cancelEvent_27_listener() {
            return ctx.hideForm();
          })("deleteEvent", function ArticlesComponent_Template_form_buttons_deleteEvent_27_listener() {
            return ctx.deleteNoticeCheck();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          const _r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("tableData", ctx.noticeData);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](7, _c0, !ctx.showNoticeForm));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"]("", _r0.value.length, " / 1000");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.selectedNotice && ctx.selectedNotice.Status == ctx.NoticeStatusEnum.Refused);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disableSaveButton", ctx.IsSubmitButtonActive())("showDeleteButton", ctx.showDeleteButton());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.MaxLengthValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__.NavBackButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatHint, _angular_material_input__WEBPACK_IMPORTED_MODULE_11__.MatInput, _schools_form_components_form_buttons_form_buttons_component__WEBPACK_IMPORTED_MODULE_3__.FormButtonsComponent, _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_4__.NoticeHeaderComponent, _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_5__.NoticeTableComponent],
      styles: [".backButton[_ngcontent-%COMP%] {\n  color: orange;\n  font-size: 14px;\n}\n\n.checkBox[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.inActiveCheckbox[_ngcontent-%COMP%] {\n  transition: border-color 90ms cubic-bezier(0, 0, 0.2, 0.1);\n  border-width: 2px;\n  border-style: solid;\n  border-color: #ff7a00;\n  width: 16px;\n  height: 16px;\n  border-radius: 2px;\n}\n\n.cardWrapper[_ngcontent-%COMP%] {\n  background-color: white;\n  border-radius: 12px;\n  padding-top: 20px;\n  padding-bottom: 23px;\n  padding-right: 15px;\n  padding-left: 20px;\n}\n\n.crossIconWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-right: 2px;\n  cursor: pointer;\n}\n\n.invisible[_ngcontent-%COMP%] {\n  display: none;\n}\n\n.inputTitle[_ngcontent-%COMP%]:last-of-type {\n  margin-bottom: 0;\n}\n\n.checkboxLabel[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.image[_ngcontent-%COMP%] {\n  width: 28px;\n}\n\n.separator[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #dadada;\n  height: 1px;\n  margin-top: 10px;\n}\n\n.infoBlock[_ngcontent-%COMP%] {\n  padding-bottom: 30px;\n}\n\n.tableElement[_ngcontent-%COMP%] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  padding-right: 10px;\n  white-space: pre-line;\n  vertical-align: top;\n}\n\n.closeBtn[_ngcontent-%COMP%] {\n  padding: 5px;\n}\n\n.validationExplanation[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-bottom: 0;\n  font-weight: bold;\n}\n.validationExplanation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: 50px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 18302:
/*!*************************************************************************!*\
  !*** ./src/app/canteen/components/block-print/block-print.component.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BlockPrintComponent: () => (/* binding */ BlockPrintComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var ngx_device_detector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ngx-device-detector */ 50565);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var ngx_print__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ngx-print */ 33133);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _a4_print_form_a4_print_form_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../a4-print-form/a4-print-form.component */ 122);
/* harmony import */ var _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../label-template/label-template.component */ 29294);
// Models










function BlockPrintComponent_button_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " Print ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", !ctx_r0.listLabels)("useExistingCss", true);
  }
}
function BlockPrintComponent_button_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function BlockPrintComponent_button_6_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r3.ClickTabletPrint());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " Print ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", !ctx_r1.listLabels);
  }
}
const _c0 = function (a0) {
  return {
    custom: a0
  };
};
function BlockPrintComponent_div_9_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](1, _c0, ctx_r6.custom));
  }
}
function BlockPrintComponent_div_9_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "label-template", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const label_r9 = ctx.$implicit;
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](3, _c0, ctx_r7.custom));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("label", label_r9)("displaySchoolName", ctx_r7.displaySchoolName);
  }
}
function BlockPrintComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](1, BlockPrintComponent_div_9_div_1_Template, 2, 3, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](2, BlockPrintComponent_div_9_div_2_Template, 2, 5, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const page_r5 = ctx.$implicit;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](3, _c0, ctx_r2.custom));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", page_r5.fakeLabels);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", page_r5.listLabels);
  }
}
class BlockPrintComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(canteenService, deviceService, router) {
    super();
    this.canteenService = canteenService;
    this.deviceService = deviceService;
    this.router = router;
    this._fakeLabels = [];
    this.a4PageLabel = [];
    this.custom = true;
    this.displaySchoolName = false;
    this.SAFARI_BROWSER = 'Safari';
  }
  ngOnInit() {}
  IsDesktop() {
    return this.deviceService.isDesktop();
  }
  ClickTabletPrint() {
    if (this.deviceService.browser == this.SAFARI_BROWSER) {
      this.canteenService.SetLabels(this.a4PageLabel);
      this.router.navigate([`/canteen/iosPrint/${this.displaySchoolName}`]);
    } else {
      this.AndroidPrinting();
    }
  }
  AndroidPrinting() {
    let printContents = document.getElementById('labelsToPrint').innerHTML;
    let popupWin = window.open('', '_blank', 'height=100%,width=auto');
    popupWin.document.open();
    popupWin.document.write(`
      <html>
        <head>
          <title>Print tab</title>
        
          <style>
          @page{
            margin-left: 4mm;
            margin-right: 1mm;
            margin-top: 7mm;
            margin-bottom: 1mm;
            }
          .pageA4{
            width: 295mm;
            padding-top:8mm;
            margin-bottom: 6mm;
        }
    
    .labelsContainer{
        margin: 30px;
    }
    
    
    .colLabel{
        padding-top: 2mm;
        padding-left: 6mm;
        padding-right: 2mm;
        padding-bottom: 2mm;
        width: 87mm;//86mm;
        height: 51mm;
        margin-bottom:2mm;
        margin-right:2mm;
        display: inline-flex;
    
        // border-width: 1px;
        // border-color: green;
        // border-style: solid;
        // border-radius: 8px;
    
    
        font-family: 'bariol_regular';
        font-size: 16px;
    
        &.custom{
            width: 61mm;
            height: 35mm;
            margin-left: 3MM;
            font-size: 13px;
    
            & li{
                font-size: 15px;
            }
        }
    }
    
    .importantText{
        //font-size: 18px;
        font-weight: bold;
    }
    
    .className{
        //font-size: 18px;
        font-family: 'bariol_regular';
        margin-top:4mm;
        margin-left:2mm;
    }
    
    .classNamePage1{
        //font-size: 18px;
        font-family: 'bariol_regular';
        margin-top:2mm;
        margin-left:2mm;
    }
    
    .subTitle{
        font-family: 'bariol_regular';
        margin-top:0px;
        margin-left:2mm;
        font-size: 15px;
    }
    
    .classNameBold{
        //font-size: 18px;
        font-family: 'bariol_bold';
        margin-top:2mm;
    
        &.totalItems{
          display: inline-block;
          margin-top: 2px;
        }
    }
    .orderNo{
        //font-size: 18px;
        font-family: 'bariol_regular';
    }
    
    ul{
        font-family: 'bariol_regular';
        margin-top: 2px;
        margin-bottom: 2px;
        padding-left: 20px;
    
        & li{
            font-size: 19px;
        }
    }
    
    h4{
        width: 100%;
        text-align: right;
    }
    mat-form-field{
        width: 70px;
    }
    
    
    .fullWidthInput{
        width: 100%;
    }

          </style>
        </head>
        <body onload="window.print()">${printContents}</body>
      </html>`);
  }
  generateLabels(settings) {
    this._generateFakeLabels(settings.Line, settings.Column);
  }
  sliceLabels() {
    this._SliceLabelsA4();
  }
  updateCustomValue(val) {
    this.custom = val;
  }
  updateDisplaySchoolValue(val) {
    this.displaySchoolName = val;
  }
  _generateFakeLabels(line, column) {
    let numberFakeLabels = 0; //(this.line.value * 3) + this.column.value;
    let numberLine = 0;
    let numberColumn = 0;
    if (line != 1) {
      numberLine = line - 1;
    }
    if (column != 1) {
      numberColumn = column - 1;
    }
    numberFakeLabels = numberLine * 3 + numberColumn;
    this._fakeLabels = [];
    for (let index = 0; index < numberFakeLabels; index++) {
      this._fakeLabels.push(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.LabelV2());
    }
    this._SliceLabelsA4();
  }
  _SliceLabelsA4() {
    this.a4PageLabel = [];
    this.a4PageLabel[0] = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.A4PageLabel();
    this.a4PageLabel[0].fakeLabels = [] = this._fakeLabels;
    let sliceStart = 0;
    let indexPage = 0;
    // sort listLabels into local blocks
    while (sliceStart < this.listLabels.length) {
      if (!this.a4PageLabel[indexPage]) {
        this.a4PageLabel[indexPage] = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.A4PageLabel();
      }
      let labelPage = this.a4PageLabel[indexPage].GetRemainingLabels();
      this.a4PageLabel[indexPage].listLabels = this.listLabels.slice(sliceStart, sliceStart + labelPage);
      sliceStart += labelPage;
      indexPage++;
    }
  }
  static {
    this.ɵfac = function BlockPrintComponent_Factory(t) {
      return new (t || BlockPrintComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.CanteenService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](ngx_device_detector__WEBPACK_IMPORTED_MODULE_5__.DeviceDetectorService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: BlockPrintComponent,
      selectors: [["block-print"]],
      inputs: {
        listLabels: "listLabels"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 10,
      vars: 3,
      consts: [[1, "container-fluid"], [3, "generate", "sliceLabels", "updateCustomValue", "updateDisplaySchool"], [1, "labelsContainer"], [1, "row"], [1, "col-6"], ["mat-flat-button", "", "color", "primary", "type", "button", "printSectionId", "labelsToPrint", "ngxPrint", "", 3, "disabled", "useExistingCss", 4, "ngIf"], ["mat-flat-button", "", "color", "primary", "type", "button", 3, "disabled", "click", 4, "ngIf"], ["id", "labelsToPrint"], ["class", "pageA4", 3, "ngClass", 4, "ngFor", "ngForOf"], ["mat-flat-button", "", "color", "primary", "type", "button", "printSectionId", "labelsToPrint", "ngxPrint", "", 3, "disabled", "useExistingCss"], ["mat-flat-button", "", "color", "primary", "type", "button", 3, "disabled", "click"], [1, "pageA4", 3, "ngClass"], ["class", "colLabel", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "colLabel", 3, "ngClass"], [3, "label", "displaySchoolName"]],
      template: function BlockPrintComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "a4-print-form", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("generate", function BlockPrintComponent_Template_a4_print_form_generate_1_listener($event) {
            return ctx.generateLabels($event);
          })("sliceLabels", function BlockPrintComponent_Template_a4_print_form_sliceLabels_1_listener() {
            return ctx.sliceLabels();
          })("updateCustomValue", function BlockPrintComponent_Template_a4_print_form_updateCustomValue_1_listener($event) {
            return ctx.updateCustomValue($event);
          })("updateDisplaySchool", function BlockPrintComponent_Template_a4_print_form_updateDisplaySchool_1_listener($event) {
            return ctx.updateDisplaySchoolValue($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "div", 2)(3, "div", 3)(4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](5, BlockPrintComponent_button_5_Template, 2, 2, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, BlockPrintComponent_button_6_Template, 2, 1, "button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](7, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](9, BlockPrintComponent_div_9_Template, 3, 5, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.IsDesktop());
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.IsDesktop());
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx.a4PageLabel);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, ngx_print__WEBPACK_IMPORTED_MODULE_8__.NgxPrintDirective, _angular_material_button__WEBPACK_IMPORTED_MODULE_9__.MatButton, _a4_print_form_a4_print_form_component__WEBPACK_IMPORTED_MODULE_2__.A4PrintFormComponent, _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_3__.LabelTemplateComponent],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.pageA4[_ngcontent-%COMP%] {\n  width: 245mm;\n  padding-top: 8mm;\n  margin-bottom: 6mm;\n}\n.pageA4.custom[_ngcontent-%COMP%] {\n  width: 213mm;\n  padding-top: 13.5mm;\n  margin-bottom: 13.5mm;\n}\n\n.colLabel[_ngcontent-%COMP%] {\n  padding-top: 2mm;\n  padding-left: 6mm;\n  padding-right: 2mm;\n  padding-bottom: 2mm;\n  width: 81mm;\n  height: 47.5mm;\n  margin-bottom: 2mm;\n  display: inline-flex;\n  font-family: \"bariol_regular\";\n  font-size: 16px;\n}\n.colLabel.custom[_ngcontent-%COMP%] {\n  width: 61mm;\n  height: 35mm;\n  margin-left: 3mm;\n  font-size: 13px;\n}\n.colLabel.custom[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  font-size: 15px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 93070:
/*!*************************************************************************************************************!*\
  !*** ./src/app/canteen/components/canteen-order-category-filter/canteen-order-category-filter.component.ts ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenOrderCategoryFilterComponent: () => (/* binding */ CanteenOrderCategoryFilterComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/expansion */ 19322);





function CanteenOrderCategoryFilterComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 6)(1, "mat-checkbox", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("change", function CanteenOrderCategoryFilterComponent_div_9_Template_mat_checkbox_change_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.CheckboxChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const cat_r1 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("name", cat_r1.MenuCategoryId)("checked", ctx_r0.IsChecked(cat_r1.MenuCategoryId));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](cat_r1.CategoryName);
  }
}
const _c0 = function (a0) {
  return {
    active: a0
  };
};
class CanteenOrderCategoryFilterComponent {
  constructor() {
    this.rowExpanded = false;
    this.categoriesSelected = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
    this.selectedValueCategories = [];
  }
  ngOnChanges(changes) {
    for (const propName in changes) {
      switch (propName) {
        case 'categories':
          this._prepareList();
          break;
        default:
          break;
      }
    }
  }
  /**
   * Prepare the school list to display
   */
  _prepareList() {
    if (this.categories && this.categories.length > 0) {
      this.selectedValueCategories = [];
      this.categories.forEach(s => {
        this.selectedValueCategories.push(s.MenuCategoryId);
      });
      // if there is a preferred school, use it.
      let savedValue = JSON.parse(localStorage.getItem('prefCategoryId'));
      if (savedValue != null && savedValue.length > 0) {
        let index = this.selectedValueCategories.find(i => i == savedValue[0]);
        if (index > -1) {
          this.selectedValueCategories = savedValue;
        }
      } else {
        this._SaveCategories();
      }
      // trigger selected categories
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }
  /**
   * Check if the given categoryId is selected
   * @param categoryId
   * @returns
   */
  IsChecked(categoryId) {
    return this.selectedValueCategories.findIndex(s => s == categoryId) > -1;
  }
  /**
   * Checkbox value changed
   * @param event Checkbox event
   */
  CheckboxChanged(event) {
    const schoolId = +event.source.name;
    if (event.checked) {
      this.selectedValueCategories.push(schoolId);
    } else {
      let index = this.selectedValueCategories.findIndex(i => i == schoolId);
      if (index > -1) {
        this.selectedValueCategories.splice(index, 1);
      }
    }
    // save categories selection
    this._SaveCategories();
    // trigger selected categories
    this.categoriesSelected.emit(this.selectedValueCategories);
  }
  /**
   * Save the categories list in localStorage
   */
  _SaveCategories() {
    localStorage.setItem('prefCategoryId', JSON.stringify(this.selectedValueCategories));
  }
  /**
   * Check if all categories are selected
   * @returns
   */
  IsAllSelected() {
    return this.selectedValueCategories.length == this.categories.length;
  }
  /**
   * Check if the selected categories list is empty
   * @returns
   */
  IsNoCategoriessSelected() {
    return this.selectedValueCategories.length == 0;
  }
  /**
   * Select all the categories available
   */
  SelectAll() {
    if (!this.IsAllSelected()) {
      this.selectedValueCategories = [];
      this.categories.forEach(s => {
        this.selectedValueCategories.push(s.MenuCategoryId);
      });
      this._SaveCategories();
      // trigger selected categories
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }
  /**
   * Clear selected list
   */
  Clear() {
    if (!this.IsNoCategoriessSelected()) {
      this.selectedValueCategories = [];
      this.categoriesSelected.emit(this.selectedValueCategories);
    }
  }
  static {
    this.ɵfac = function CanteenOrderCategoryFilterComponent_Factory(t) {
      return new (t || CanteenOrderCategoryFilterComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: CanteenOrderCategoryFilterComponent,
      selectors: [["canteen-order-category-filter"]],
      inputs: {
        categories: "categories",
        rowExpanded: "rowExpanded"
      },
      outputs: {
        categoriesSelected: "categoriesSelected"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵNgOnChangesFeature"]],
      decls: 17,
      vars: 10,
      consts: [[3, "expanded"], [1, "row"], ["class", "col-6 col-md-3", 4, "ngFor", "ngForOf"], [1, "col-12", "col-md-6"], [1, "blockAction"], [3, "ngClass", "click"], [1, "col-6", "col-md-3"], [3, "name", "checked", "change"]],
      template: function CanteenOrderCategoryFilterComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "mat-accordion")(1, "mat-expansion-panel", 0)(2, "mat-expansion-panel-header")(3, "mat-panel-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4, " Categories ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "mat-panel-description")(6, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](9, CanteenOrderCategoryFilterComponent_div_9_Template, 3, 3, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "div", 1)(11, "div", 3)(12, "div", 4)(13, "a", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function CanteenOrderCategoryFilterComponent_Template_a_click_13_listener() {
            return ctx.Clear();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](14, "Clear");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](15, "a", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function CanteenOrderCategoryFilterComponent_Template_a_click_15_listener() {
            return ctx.SelectAll();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16, "Select All");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("expanded", ctx.rowExpanded);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate2"]("", ctx.selectedValueCategories.length, "/", ctx.categories.length, "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.categories);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpureFunction1"](6, _c0, !ctx.IsNoCategoriessSelected()));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpureFunction1"](8, _c0, !ctx.IsAllSelected()));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__.MatCheckbox, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatAccordion, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanel, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelHeader, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelTitle, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelDescription],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nmat-expansion-panel-header[_ngcontent-%COMP%] {\n  height: 45px !important;\n}\n\nmat-panel-title[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-family: Arial, Helvetica, sans-serif;\n  align-items: center;\n}\n\nmat-panel-description[_ngcontent-%COMP%] {\n  justify-content: right;\n}\nmat-panel-description[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n  font-family: Arial, Helvetica, sans-serif;\n  background-color: #e5e5e5;\n  border-radius: 8px;\n  color: black;\n  padding: 8px 5px 8px 5px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.blockAction[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\n.blockAction[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-right: 14px;\n  color: #b9b9c8;\n}\n.blockAction[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\n  cursor: pointer;\n  color: #ff4b17;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 16972:
/*!*******************************************************************************************!*\
  !*** ./src/app/canteen/components/canteen-order-filter/canteen-order-filter.component.ts ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenOrderFilterComponent: () => (/* binding */ CanteenOrderFilterComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_tools_components_canteen_select_list_canteen_select_list_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared-tools/components/canteen-select-list/canteen-select-list.component */ 4007);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);
/* harmony import */ var _canteen_order_school_filter_canteen_order_school_filter_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../canteen-order-school-filter/canteen-order-school-filter.component */ 76854);
/* harmony import */ var _canteen_order_type_filter_canteen_order_type_filter_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../canteen-order-type-filter/canteen-order-type-filter.component */ 87014);
/* harmony import */ var _canteen_order_category_filter_canteen_order_category_filter_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../canteen-order-category-filter/canteen-order-category-filter.component */ 93070);



//ngrx


// models
















function CanteenOrderFilterComponent_form_0_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](1, "input-date", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
}
function CanteenOrderFilterComponent_form_0_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 3)(1, "mat-form-field", 15)(2, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](3, "Print Status");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "mat-select", 16)(5, "mat-option", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](6, "All");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](7, "mat-option", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](8, "Printed Only");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](9, "mat-option", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](10, "Unprinted Only");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()()();
  }
}
function CanteenOrderFilterComponent_form_0_div_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 2)(1, "div", 9)(2, "canteen-order-school-filter", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("schoolsSelected", function CanteenOrderFilterComponent_form_0_div_16_Template_canteen_order_school_filter_schoolsSelected_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r6.SchoolSelectionChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("schools", ctx_r3.selectedCanteen.Schools);
  }
}
function CanteenOrderFilterComponent_form_0_canteen_order_type_filter_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "canteen-order-type-filter", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("formGroupChange", function CanteenOrderFilterComponent_form_0_canteen_order_type_filter_19_Template_canteen_order_type_filter_formGroupChange_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r8.filterForm = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("merchantType", ctx_r4.selectedCanteen.CanteenType)("formGroup", ctx_r4.filterForm);
  }
}
function CanteenOrderFilterComponent_form_0_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 11)(1, "div", 9)(2, "canteen-order-category-filter", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("categoriesSelected", function CanteenOrderFilterComponent_form_0_div_20_Template_canteen_order_category_filter_categoriesSelected_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r11);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r10.CategoriesSelectionChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("categories", ctx_r5.listCategories);
  }
}
function CanteenOrderFilterComponent_form_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "form", 1)(1, "div", 2)(2, "div", 3)(3, "canteen-select-list", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("selectedChanged", function CanteenOrderFilterComponent_form_0_Template_canteen_select_list_selectedChanged_3_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r12.CanteenChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](5, CanteenOrderFilterComponent_form_0_div_5_Template, 2, 0, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](6, CanteenOrderFilterComponent_form_0_div_6_Template, 11, 0, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](7, "div", 3)(8, "mat-form-field", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](9, "input", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](10, "mat-icon", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("click", function CanteenOrderFilterComponent_form_0_Template_mat_icon_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r14.SearchClicked());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](11, "search");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](12, "div", 2)(13, "div", 9)(14, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](15, "Filters");
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](16, CanteenOrderFilterComponent_form_0_div_16_Template, 3, 1, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](17, "div", 11)(18, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](19, CanteenOrderFilterComponent_form_0_canteen_order_type_filter_19_Template, 1, 2, "canteen-order-type-filter", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](20, CanteenOrderFilterComponent_form_0_div_20_Template, 3, 1, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("formGroup", ctx_r0.filterForm);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r0.isUniformCanteen);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", !ctx_r0.isUniformCanteen);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r0.selectedCanteen);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r0.selectedCanteen);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r0.showCategoriesList && ctx_r0.listCategories);
  }
}
class CanteenOrderFilterComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.BaseComponent {
  constructor(canteenService, store, debounceService) {
    super();
    this.canteenService = canteenService;
    this.store = store;
    this.debounceService = debounceService;
    this.filtersChanged = new _angular_core__WEBPACK_IMPORTED_MODULE_9__.EventEmitter();
    this.settingsChanged = new _angular_core__WEBPACK_IMPORTED_MODULE_9__.EventEmitter();
    this.isUniformCanteen = false;
    this.isEventCanteen = false;
    this.showCategoriesList = false;
    this.triggerFiltersChanged = this.debounceService.callDebounce(this._prepareRequest, 100, false, true);
  }
  ngOnInit() {
    this.canteenSubscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_10__.select)(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__.menuCategories)).subscribe(menuCategories => {
      this.listCategories = menuCategories;
    });
    // get saved filters
    this.canteenFilters = this.canteenService.GetFilters();
    // create form
    this._createForm();
  }
  ngOnDestroy() {
    if (this.canteenSubscription) {
      this.canteenSubscription.unsubscribe();
    }
  }
  get date() {
    return this.filterForm.get('date');
  }
  get recess() {
    return this.filterForm.get('recess');
  }
  get lunch() {
    return this.filterForm.get('lunch');
  }
  get event() {
    return this.filterForm.get('event');
  }
  get printed() {
    return this.filterForm.get('printed');
  }
  get uniNew() {
    return this.filterForm.get('uniNew');
  }
  get uniProcessing() {
    return this.filterForm.get('uniProcessing');
  }
  get uniReady() {
    return this.filterForm.get('uniReady');
  }
  get uniCompleted() {
    return this.filterForm.get('uniCompleted');
  }
  get search() {
    return this.filterForm.get('search');
  }
  /**
   * Setup the filters form and listen to events
   */
  _createForm() {
    this.filterForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormGroup({
      search: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(''),
      recess: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.Recess),
      lunch: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.Lunch),
      event: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.Event),
      printed: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.Printed),
      date: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.Date, [_angular_forms__WEBPACK_IMPORTED_MODULE_11__.Validators.required]),
      uniNew: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.UniNew),
      uniProcessing: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.UniProcessing),
      uniReady: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.UniReady),
      uniCompleted: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl(this.canteenFilters.UniCompleted)
    });
    this.date.valueChanges.subscribe(val => {
      this.triggerFiltersChanged();
    });
    this.recess.valueChanges.subscribe(val => {
      this.canteenFilters.Recess = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.lunch.valueChanges.subscribe(val => {
      this.canteenFilters.Lunch = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.event.valueChanges.subscribe(val => {
      this.canteenFilters.Event = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.printed.valueChanges.subscribe(val => {
      this.canteenFilters.Printed = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.uniNew.valueChanges.subscribe(val => {
      this.canteenFilters.UniNew = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.uniProcessing.valueChanges.subscribe(val => {
      this.canteenFilters.UniProcessing = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.uniReady.valueChanges.subscribe(val => {
      this.canteenFilters.UniReady = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
    this.uniCompleted.valueChanges.subscribe(val => {
      this.canteenFilters.UniCompleted = val;
      this.canteenService.SetFilters(this.canteenFilters);
      this.triggerFiltersChanged();
    });
  }
  /**
   * Manage the change of the selected canteen
   * @param canteen selected canteen
   */
  CanteenChanged(canteen) {
    this.selectedCanteen = canteen;
    this.isUniformCanteen = this.selectedCanteen.CanteenType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MerchantTypeEnum.Uniform;
    this.isEventCanteen = this.selectedCanteen.CanteenType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MerchantTypeEnum.Event;
    this.labelPrintChoice = this.selectedCanteen.Schools[0].LabelPrintChoice; // TODO => change the logic to take the First selected school
    this.showCategoriesList = this.labelPrintChoice == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.LabelPrintChoiceEnum.Item;
    // export settings to the main screen
    const settings = {
      LabelPrintChoice: this.labelPrintChoice,
      UsePrintingApp: this.selectedCanteen.Schools[0].UsePrintingApp,
      IsUniformCanteen: this.isUniformCanteen
    };
    this.settingsChanged.emit(settings);
  }
  /**
   * Search value
   */
  SearchClicked() {
    this.triggerFiltersChanged();
  }
  /**
   * Handled event when schools selection change
   * @param schools list of schools
   */
  SchoolSelectionChanged(schools) {
    this.selectedSchools = schools;
    if (this.selectedCanteen.CanteenType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MerchantTypeEnum.Event) {
      this.event.setValue(true);
      this.canteenFilters.Event = true;
      this.canteenService.SetFilters(this.canteenFilters);
    }
    if (this.labelPrintChoice == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.LabelPrintChoiceEnum.Order || this.labelPrintChoice == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.LabelPrintChoiceEnum.Item && this.listCategories.length > 0) {
      this.triggerFiltersChanged();
    }
  }
  /**
   * Handled event when categories selection change
   * @param categories list of categories
   */
  CategoriesSelectionChanged(categories) {
    this.selectedCategories = categories;
    this.triggerFiltersChanged();
  }
  _prepareRequest() {
    // prepare request
    let request = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CanteenOrderRequest();
    this.selectedSchools.forEach(n => {
      if (!request.SchoolIds) {
        request.SchoolIds = n.toString();
      } else {
        request.SchoolIds += ',' + n;
      }
    });
    // filter per selected categories (print 1 item per label only)
    if (this.selectedCategories) {
      this.selectedCategories.forEach(n => {
        if (!request.CategoryIds) {
          request.CategoryIds = n.toString();
        } else {
          request.CategoryIds += ',' + n;
        }
      });
    }
    request.Date = moment__WEBPACK_IMPORTED_MODULE_0__(this.date.value).format('YYYY-MM-DD');
    request.CanteenStatus = '';
    request.OrderType = '';
    request.LabelPrintChoice = this.labelPrintChoice;
    request.Filter = this.search.value;
    request.MerchantId = this.selectedCanteen.CanteenId;
    if (this.isUniformCanteen) {
      request.OrderType += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MenuTypeEnum.Uniform + ',';
      if (this.uniNew.value) {
        request.CanteenStatus += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CanteenStatusEnum.New + ',';
      }
      if (this.uniProcessing.value) {
        request.CanteenStatus += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CanteenStatusEnum.Processing + ',';
      }
      if (this.uniReady.value) {
        request.CanteenStatus += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CanteenStatusEnum.Ready + ',';
      }
      if (this.uniCompleted.value) {
        request.CanteenStatus += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CanteenStatusEnum.Completed + ',';
      }
    } else {
      if (this.recess.value && !this.isEventCanteen) {
        request.OrderType += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MenuTypeEnum.Recess + ',';
      }
      if (this.lunch.value && !this.isEventCanteen) {
        request.OrderType += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MenuTypeEnum.Lunch + ',';
      }
      if (this.event.value) {
        request.OrderType += src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.MenuTypeEnum.Event + ',';
      }
      if (this.printed.value) {
        if (this.printed.value == 'all') {
          request.Printed == null;
        } else if (this.printed.value == 'printed') {
          request.Printed = true;
        } else {
          request.Printed = false;
        }
      }
    }
    // trigger event
    this.filtersChanged.emit(request);
  }
  static {
    this.ɵfac = function CanteenOrderFilterComponent_Factory(t) {
      return new (t || CanteenOrderFilterComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.CanteenService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_10__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.DebounceService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineComponent"]({
      type: CanteenOrderFilterComponent,
      selectors: [["canteen-order-filter"]],
      outputs: {
        filtersChanged: "filtersChanged",
        settingsChanged: "settingsChanged"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵInheritDefinitionFeature"]],
      decls: 1,
      vars: 1,
      consts: [[3, "formGroup", 4, "ngIf"], [3, "formGroup"], [1, "row"], [1, "col-12", "col-md-3", "col-lg-2"], [3, "selectedChanged"], ["class", "col-12 col-md-3 col-lg-2", 4, "ngIf"], ["appearance", "outline", 1, "searchBox"], ["matInput", "", "formControlName", "search", "placeholder", "Search", "autocomplete", "off"], ["matSuffix", "", "aria-hidden", "false", 3, "click"], [1, "col-12"], ["class", "row", 4, "ngIf"], [1, "row", "mt-3"], [3, "merchantType", "formGroup", "formGroupChange", 4, "ngIf"], ["class", "row mt-3", 4, "ngIf"], ["placeholder", "Date", "formControlName", "date"], ["appearance", "outline"], ["formControlName", "printed"], ["value", "all"], ["value", "printed"], ["value", "unprinted"], [3, "schools", "schoolsSelected"], [3, "merchantType", "formGroup", "formGroupChange"], [3, "categories", "categoriesSelected"]],
      template: function CanteenOrderFilterComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](0, CanteenOrderFilterComponent_form_0_Template, 21, 6, "form", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.filterForm);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_11__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_11__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControlName, _shared_tools_components_canteen_select_list_canteen_select_list_component__WEBPACK_IMPORTED_MODULE_4__.CanteenSelectListComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatSuffix, _angular_material_select__WEBPACK_IMPORTED_MODULE_14__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_15__.MatOption, _angular_material_icon__WEBPACK_IMPORTED_MODULE_16__.MatIcon, _angular_material_input__WEBPACK_IMPORTED_MODULE_17__.MatInput, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_5__.InputDateComponent, _canteen_order_school_filter_canteen_order_school_filter_component__WEBPACK_IMPORTED_MODULE_6__.CanteenOrderSchoolFilterComponent, _canteen_order_type_filter_canteen_order_type_filter_component__WEBPACK_IMPORTED_MODULE_7__.CanteenOrderTypeFilterComponent, _canteen_order_category_filter_canteen_order_category_filter_component__WEBPACK_IMPORTED_MODULE_8__.CanteenOrderCategoryFilterComponent],
      styles: ["h4[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n  margin-left: 15px;\n}\n\nmat-icon[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n\nmat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi9jb21wb25lbnRzL2NhbnRlZW4tb3JkZXItZmlsdGVyL2NhbnRlZW4tb3JkZXItZmlsdGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsbUJBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsZUFBQTtBQUNGOztBQUVBO0VBQ0UsV0FBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiaDQge1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICBtYXJnaW4tbGVmdDogMTVweDtcbn1cblxubWF0LWljb24ge1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG5cbm1hdC1mb3JtLWZpZWxkIHtcbiAgd2lkdGg6IDEwMCU7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 76854:
/*!*********************************************************************************************************!*\
  !*** ./src/app/canteen/components/canteen-order-school-filter/canteen-order-school-filter.component.ts ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenOrderSchoolFilterComponent: () => (/* binding */ CanteenOrderSchoolFilterComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/expansion */ 19322);





function CanteenOrderSchoolFilterComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 6)(1, "mat-checkbox", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("change", function CanteenOrderSchoolFilterComponent_div_9_Template_mat_checkbox_change_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r2.CheckboxChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const school_r1 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("name", school_r1.SchoolId)("checked", ctx_r0.IsChecked(school_r1.SchoolId));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](school_r1.Name);
  }
}
const _c0 = function (a0) {
  return {
    active: a0
  };
};
class CanteenOrderSchoolFilterComponent {
  constructor() {
    this.schoolsSelected = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
    this.selectedValueSchools = [];
  }
  ngOnChanges(changes) {
    for (const propName in changes) {
      switch (propName) {
        case 'schools':
          this._prepareList();
          break;
        default:
          break;
      }
    }
  }
  /**
   * Prepare the school list to display
   */
  _prepareList() {
    if (this.schools && this.schools.length > 0) {
      //this.usePrintingApp = this.listSchools[0].UsePrintingApp;
      this.selectedValueSchools = [];
      this.schools.forEach(s => {
        this.selectedValueSchools.push(s.SchoolId);
      });
      // if there is a preferred school, use it.
      let savedValue = JSON.parse(localStorage.getItem('prefSchoolId'));
      if (savedValue != null && savedValue.length > 0) {
        let index = this.selectedValueSchools.find(i => i == savedValue[0]);
        if (index > -1) {
          this.selectedValueSchools = savedValue;
        }
      } else {
        this._SaveSchools();
      }
      // trigger selected schools
      this.schoolsSelected.emit(this.selectedValueSchools);
    }
  }
  /**
   * Check if the given schoolId is selected
   * @param schoolId
   * @returns
   */
  IsChecked(schoolId) {
    return this.selectedValueSchools.findIndex(s => s == schoolId) > -1;
  }
  /**
   * Checkbox value changed
   * @param event Checkbox event
   */
  CheckboxChanged(event) {
    const schoolId = +event.source.name;
    if (event.checked) {
      this.selectedValueSchools.push(schoolId);
    } else {
      let index = this.selectedValueSchools.findIndex(i => i == schoolId);
      if (index > -1) {
        this.selectedValueSchools.splice(index, 1);
      }
    }
    // save school selection
    this._SaveSchools();
    // trigger selected schools
    this.schoolsSelected.emit(this.selectedValueSchools);
  }
  /**
   * Save the schools list in localStorage
   * @param schools
   */
  _SaveSchools() {
    localStorage.setItem('prefSchoolId', JSON.stringify(this.selectedValueSchools));
  }
  /**
   * Check if all schools are selected
   * @returns
   */
  IsAllSelected() {
    return this.selectedValueSchools.length == this.schools.length;
  }
  /**
   * Check if the selected schools list is empty
   * @returns
   */
  IsNoSchoolsSelected() {
    return this.selectedValueSchools.length == 0;
  }
  /**
   * Select all the schools available
   */
  SelectAll() {
    if (!this.IsAllSelected()) {
      this.selectedValueSchools = [];
      this.schools.forEach(s => {
        this.selectedValueSchools.push(s.SchoolId);
      });
      this._SaveSchools();
      // trigger selected schools
      this.schoolsSelected.emit(this.selectedValueSchools);
    }
  }
  /**
   * Clear selected list
   */
  Clear() {
    if (!this.IsNoSchoolsSelected()) {
      this.selectedValueSchools = [];
    }
  }
  static {
    this.ɵfac = function CanteenOrderSchoolFilterComponent_Factory(t) {
      return new (t || CanteenOrderSchoolFilterComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: CanteenOrderSchoolFilterComponent,
      selectors: [["canteen-order-school-filter"]],
      inputs: {
        schools: "schools"
      },
      outputs: {
        schoolsSelected: "schoolsSelected"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵNgOnChangesFeature"]],
      decls: 17,
      vars: 10,
      consts: [[3, "expanded"], [1, "row"], ["class", "col-6 col-md-3", 4, "ngFor", "ngForOf"], [1, "col-12", "col-md-6"], [1, "blockAction"], [3, "ngClass", "click"], [1, "col-6", "col-md-3"], [3, "name", "checked", "change"]],
      template: function CanteenOrderSchoolFilterComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "mat-accordion")(1, "mat-expansion-panel", 0)(2, "mat-expansion-panel-header")(3, "mat-panel-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4, " Schools ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "mat-panel-description")(6, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](8, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](9, CanteenOrderSchoolFilterComponent_div_9_Template, 3, 3, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](10, "div", 1)(11, "div", 3)(12, "div", 4)(13, "a", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function CanteenOrderSchoolFilterComponent_Template_a_click_13_listener() {
            return ctx.Clear();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](14, "Clear");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](15, "a", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function CanteenOrderSchoolFilterComponent_Template_a_click_15_listener() {
            return ctx.SelectAll();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](16, "Select All");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("expanded", false);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate2"]("", ctx.selectedValueSchools.length, "/", ctx.schools.length, "");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx.schools);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpureFunction1"](6, _c0, !ctx.IsNoSchoolsSelected()));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpureFunction1"](8, _c0, !ctx.IsAllSelected()));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__.MatCheckbox, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatAccordion, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanel, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelHeader, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelTitle, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_3__.MatExpansionPanelDescription],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nmat-expansion-panel-header[_ngcontent-%COMP%] {\n  height: 45px !important;\n}\n\nmat-panel-title[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-family: Arial, Helvetica, sans-serif;\n  align-items: center;\n}\n\nmat-panel-description[_ngcontent-%COMP%] {\n  justify-content: right;\n}\nmat-panel-description[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n  font-family: Arial, Helvetica, sans-serif;\n  background-color: #e5e5e5;\n  border-radius: 8px;\n  color: black;\n  padding: 8px 5px 8px 5px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.blockAction[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\n.blockAction[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-right: 14px;\n  color: #b9b9c8;\n}\n.blockAction[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\n  cursor: pointer;\n  color: #ff4b17;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 87014:
/*!*****************************************************************************************************!*\
  !*** ./src/app/canteen/components/canteen-order-type-filter/canteen-order-type-filter.component.ts ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenOrderTypeFilterComponent: () => (/* binding */ CanteenOrderTypeFilterComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
/* harmony import */ var _shared_components_menu_merchant_menu_name_pipe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/menu/merchant-menu-name.pipe */ 66825);

// models








function CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 6)(1, "mat-checkbox", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "merchantMenuName");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](3, 1, ctx_r4.MenuTypeEnum.Recess));
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 6)(1, "mat-checkbox", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "merchantMenuName");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](3, 1, ctx_r5.MenuTypeEnum.Lunch));
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_1_Template, 4, 3, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_2_Template, 4, 3, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 6)(4, "mat-checkbox", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Event");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r1.isEventMerchant);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r1.isEventMerchant);
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("(", ctx_r6.statusTotals.New, ")");
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("(", ctx_r7.statusTotals.Processing, ")");
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("(", ctx_r8.statusTotals.Ready, ")");
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("(", ctx_r9.statusTotals.Completed, ")");
  }
}
function CanteenOrderTypeFilterComponent_div_5_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 6)(1, "mat-checkbox", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "New ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_3_Template, 2, 1, "span", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 6)(5, "mat-checkbox", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Processing ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_7_Template, 2, 1, "span", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "div", 6)(9, "mat-checkbox", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10, "Ready ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_11_Template, 2, 1, "span", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "div", 6)(13, "mat-checkbox", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](14, "Completed ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](15, CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_15_Template, 2, 1, "span", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r3.statusTotals && ctx_r3.uniNew.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r3.statusTotals && ctx_r3.uniProcessing.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r3.statusTotals && ctx_r3.uniReady.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r3.statusTotals && ctx_r3.uniCompleted.value);
  }
}
function CanteenOrderTypeFilterComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, CanteenOrderTypeFilterComponent_div_5_ng_container_1_Template, 6, 2, "ng-container", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, CanteenOrderTypeFilterComponent_div_5_ng_template_2_Template, 16, 4, "ng-template", null, 4, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](3);
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx_r0.getForm());
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r0.isUniformShop)("ngIfElse", _r2);
  }
}
const _c0 = function (a0) {
  return {
    "mat-elevation-z0": a0
  };
};
class CanteenOrderTypeFilterComponent {
  constructor(orderStatusService) {
    this.orderStatusService = orderStatusService;
    this.MenuTypeEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MenuTypeEnum;
  }
  ngOnInit() {
    if (!this.formGroup) {
      this._createForm();
    }
  }
  ngOnChanges() {
    this.isUniformShop = this.merchantType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Uniform;
    this.isEventMerchant = this.merchantType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Event;
    this.formTitle = this.isUniformShop ? 'Order Status' : 'Order Type';
  }
  getForm() {
    if (!this.formGroup) {
      return this.filterForm;
    }
    return this.formGroup;
  }
  /**
   * Setup the filters form and listen to events
   */
  _createForm() {
    this.filterForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      uniNew: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(true),
      uniProcessing: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(true),
      uniReady: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(true),
      uniCompleted: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(true)
    });
    this.filterForm.valueChanges.subscribe(x => {
      let activeCanteenStatuses = [];
      if (this.uniProcessing.value) {
        activeCanteenStatuses.push(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CanteenStatusEnum.Processing);
      }
      if (this.uniReady.value) {
        activeCanteenStatuses.push(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CanteenStatusEnum.Ready);
      }
      if (this.uniCompleted.value) {
        activeCanteenStatuses.push(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CanteenStatusEnum.Completed);
      }
      if (this.uniNew.value) {
        activeCanteenStatuses.push(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CanteenStatusEnum.New);
      }
      //save orderStatus values to subscription
      this.orderStatusService.setOrderStatus(activeCanteenStatuses);
    });
  }
  get uniProcessing() {
    return this.filterForm.get('uniProcessing');
  }
  get uniReady() {
    return this.filterForm.get('uniReady');
  }
  get uniCompleted() {
    return this.filterForm.get('uniCompleted');
  }
  get uniNew() {
    return this.filterForm.get('uniNew');
  }
  static {
    this.ɵfac = function CanteenOrderTypeFilterComponent_Factory(t) {
      return new (t || CanteenOrderTypeFilterComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.OrderStatusService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: CanteenOrderTypeFilterComponent,
      selectors: [["canteen-order-type-filter"]],
      inputs: {
        merchantType: "merchantType",
        formGroup: "formGroup",
        forReports: "forReports",
        statusTotals: "statusTotals"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵNgOnChangesFeature"]],
      decls: 6,
      vars: 6,
      consts: [[3, "expanded", "ngClass"], ["class", "row", 3, "formGroup", 4, "ngIf"], [1, "row", 3, "formGroup"], [4, "ngIf", "ngIfElse"], ["uniformFilters", ""], ["class", "col-6 col-md-2 col-lg-1", 4, "ngIf"], [1, "col-6", "col-md-2", "col-lg-1"], ["formControlName", "event"], ["formControlName", "recess"], ["formControlName", "lunch"], ["formControlName", "uniNew"], [4, "ngIf"], ["formControlName", "uniProcessing"], ["formControlName", "uniReady"], ["formControlName", "uniCompleted"]],
      template: function CanteenOrderTypeFilterComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "mat-accordion")(1, "mat-expansion-panel", 0)(2, "mat-expansion-panel-header")(3, "mat-panel-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, CanteenOrderTypeFilterComponent_div_5_Template, 4, 3, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("expanded", true)("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c0, ctx.forReports));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx.formTitle, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.formGroup || ctx.filterForm);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_6__.MatCheckbox, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatAccordion, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanel, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanelHeader, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanelTitle, _shared_components_menu_merchant_menu_name_pipe__WEBPACK_IMPORTED_MODULE_2__.MerchantMenuNamePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nmat-expansion-panel-header[_ngcontent-%COMP%] {\n  height: 45px !important;\n}\n\nmat-panel-title[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-family: Arial, Helvetica, sans-serif;\n  align-items: center;\n}\n\nmat-panel-description[_ngcontent-%COMP%] {\n  justify-content: right;\n}\nmat-panel-description[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n  font-family: Arial, Helvetica, sans-serif;\n  background-color: #e5e5e5;\n  border-radius: 8px;\n  color: black;\n  padding: 8px 5px 8px 5px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.blockAction[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\n.blockAction[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-right: 14px;\n  color: #b9b9c8;\n}\n.blockAction[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\n  cursor: pointer;\n  color: #ff4b17;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 36490:
/*!*****************************************************************!*\
  !*** ./src/app/canteen/components/canteen/canteen.component.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenComponent: () => (/* binding */ CanteenComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_cdk_collections__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/cdk/collections */ 37989);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/paginator */ 24624);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _canteen_order_filter_canteen_order_filter_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../canteen-order-filter/canteen-order-filter.component */ 16972);
/* harmony import */ var _uniform_order_status_picker_uniform_order_status_picker_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../uniform-order-status-picker/uniform-order-status-picker.component */ 46118);
/* harmony import */ var _selected_order_selected_order_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../selected-order/selected-order.component */ 90500);
/* harmony import */ var _shared_components_menu_merchant_menu_name_pipe__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../shared/components/menu/merchant-menu-name.pipe */ 66825);



















function CanteenComponent_h4_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "h4", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate1"](" Selected (", ctx_r0.selection.selected.length, ") ");
  }
}
function CanteenComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div")(1, "uniform-order-status-picker", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("clearRowSelection", function CanteenComponent_div_12_Template_uniform_order_status_picker_clearRowSelection_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r20.clearRowSelection());
    })("loadTableData", function CanteenComponent_div_12_Template_uniform_order_status_picker_loadTableData_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r21);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r22.LoadTableData());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("selectedOrders", ctx_r1.selection.selected);
  }
}
function CanteenComponent_th_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 37)(1, "mat-checkbox", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("change", function CanteenComponent_th_20_Template_mat_checkbox_change_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"]($event ? ctx_r23.masterToggle() : null);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("checked", ctx_r2.selection.hasValue() && ctx_r2.isAllSelected())("indeterminate", ctx_r2.selection.hasValue() && !ctx_r2.isAllSelected())("aria-label", ctx_r2.checkboxLabel());
  }
}
function CanteenComponent_td_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r27 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39)(1, "mat-checkbox", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function CanteenComponent_td_21_Template_mat_checkbox_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r27);
      const row_r25 = restoredCtx.$implicit;
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r26.selection.toggle(row_r25));
    })("change", function CanteenComponent_td_21_Template_mat_checkbox_change_1_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r27);
      const row_r25 = restoredCtx.$implicit;
      const ctx_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"]($event ? ctx_r28.selection.toggle(row_r25) : null);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const row_r25 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("checked", ctx_r3.selection.isSelected(row_r25))("aria-label", ctx_r3.checkboxLabel(row_r25));
  }
}
function CanteenComponent_th_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "No.");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r29 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r29.OrderId);
  }
}
function CanteenComponent_th_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "Status");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r30 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r30.CanteenStatus);
  }
}
function CanteenComponent_th_29_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "School");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_30_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r31 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r31.SchoolName);
  }
}
function CanteenComponent_th_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "Order Type");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipe"](2, "merchantMenuName");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r32 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipeBind1"](2, 1, element_r32.MenuType));
  }
}
function CanteenComponent_th_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "Student");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39)(1, "a", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r33 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpropertyInterpolate1"]("routerLink", "./../students/details/", element_r33.StudentId, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r33.StudentName);
  }
}
function CanteenComponent_th_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, "Class");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
}
function CanteenComponent_td_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r34 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r34.ClassName);
  }
}
function CanteenComponent_th_41_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "th", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1, " Order Date ");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("disabled", !ctx_r16.isUniformCanteen);
  }
}
function CanteenComponent_td_42_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipe"](2, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipe"](3, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r35 = ctx.$implicit;
    const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate1"](" ", ctx_r17.isUniformCanteen ? _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipeBind2"](2, 1, element_r35.OrderDate, "EEEE d MMMM y") : _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpipeBind2"](3, 4, element_r35.OrderDate, "EEEE d MMMM"), " ");
  }
}
function CanteenComponent_tr_43_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](0, "tr", 44);
  }
}
const _c0 = function (a0) {
  return {
    selectedRow: a0
  };
};
function CanteenComponent_tr_44_Template(rf, ctx) {
  if (rf & 1) {
    const _r38 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "tr", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function CanteenComponent_tr_44_Template_tr_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r38);
      const row_r36 = restoredCtx.$implicit;
      const ctx_r37 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r37.RowClick(row_r36));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const row_r36 = ctx.$implicit;
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](1, _c0, ctx_r19.isRowSelected(row_r36)));
  }
}
const _c1 = function () {
  return [25, 50, 100];
};
const displayedColumns = ['OrderId', 'SchoolName', 'MenuType', 'StudentName', 'ClassName', 'OrderDate'];
const uniformColumns = ['select', 'OrderId', 'CanteenStatus', 'StudentName', 'ClassName', 'OrderDate'];
class CanteenComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BasePaginatorComponent {
  constructor(router, orderAPIService, spinnerService, canteenService, printingApiService) {
    super(displayedColumns);
    this.router = router;
    this.orderAPIService = orderAPIService;
    this.spinnerService = spinnerService;
    this.canteenService = canteenService;
    this.printingApiService = printingApiService;
    this.dataSource = new _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatTableDataSource();
    this.listOrders = [];
    this.itemsSelectedOrder = [];
    this.isUniformCanteen = false;
    this.CanteenStatusEnum = _sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenStatusEnum;
    this.canteenListVisible = true;
    this.listSchools = [];
    /////////////////////////////////////////////////////////
    // Table functions
    //////////////////////////////////////////////////////////
    this.selection = new _angular_cdk_collections__WEBPACK_IMPORTED_MODULE_10__.SelectionModel(true, []);
  }
  ngOnInit() {
    // get saved filters
    this.canteenFilters = this.canteenService.GetFilters();
    if (!this.canteenFilters) {
      this.canteenFilters = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenFilters();
      this.canteenFilters.Date = moment__WEBPACK_IMPORTED_MODULE_0__().toDate();
      this.canteenFilters.Event = false;
      this.canteenFilters.Recess = true;
      this.canteenFilters.Lunch = true;
      this.canteenFilters.Printed = 'all';
      this.canteenFilters.UniNew = true;
      this.canteenFilters.UniProcessing = true;
      this.canteenFilters.UniReady = true;
      this.canteenFilters.UniCompleted = false;
      this.canteenService.SetFilters(this.canteenFilters);
    }
  }
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
  FiltersChanged(request) {
    this.ordersRequest = request;
    this.LoadTableData();
    this.clearRowSelection();
  }
  clearRowSelection() {
    this.selection.clear();
    this.selectedOrder = null;
    this.itemsSelectedOrder = null;
  }
  getOrdersRequest(getAll) {
    this.ordersRequest.NumberRows = this.listfilters.NumberRows;
    this.ordersRequest.PageIndex = this.listfilters.PageIndex;
    this.ordersRequest.SortBy = this.listfilters.SortBy;
    this.ordersRequest.SortDirection = this.listfilters.SortDirection;
    this.ordersRequest.GetAll = getAll;
    if (!this.ordersRequest.NumberRows || this.ordersRequest.NumberRows == undefined || this.ordersRequest.NumberRows == 0) {
      this.ordersRequest.NumberRows = 25;
    }
  }
  LoadTableData() {
    this.getOrdersRequest(false);
    this.spinnerService.start();
    this.orderAPIService.GetOrdersBySchoolAndMerchantAPI(this.ordersRequest).subscribe({
      next: response => {
        this.RefreshTable(response.Orders);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  RefreshTable(orders) {
    this.listOrders = orders;
    this.itemsSelectedOrder = [];
    this.selectedOrder = null;
    if (!this.listOrders || this.listOrders.length == 0) {
      this.dataSource.data = [];
      this.totalRows = 0;
    } else {
      this.dataSource.data = this.listOrders;
      this.totalRows = this.listOrders[0].TotalRows;
    }
  }
  get newStatus() {
    return this.statusForm.get('newStatus');
  }
  SettingsChanged(event) {
    this.usePrintingApp = event.UsePrintingApp;
    this.labelPrintChoice = event.LabelPrintChoice;
    this.isUniformCanteen = event.IsUniformCanteen;
    if (this.isUniformCanteen) {
      this.displayedColumns = uniformColumns;
    } else {
      this.displayedColumns = displayedColumns;
    }
  }
  GetItemOptions(item) {
    let optionsText = '(';
    //item stores modifers in Options
    if (item.Options && item.Options.length > 0) {
      item.Options.forEach(opt => {
        // item has selected options
        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {
          opt.SelectedOptionList.forEach(selectedOpt => {
            optionsText += selectedOpt + ', ';
          });
        }
      });
    }
    //item stores modifiers in SelectedOptions
    if (item.SelectedOptions && item.SelectedOptions.length > 0) {
      item.SelectedOptions.forEach(opt => {
        optionsText += opt.OptionName + ', ';
      });
    }
    optionsText = optionsText.slice(0, -2);
    optionsText += ')';
    if (optionsText.length <= 1) {
      optionsText = '';
    }
    return optionsText;
  }
  ShowButtonPrintLabels() {
    if (!this.usePrintingApp) {
      return true;
    } else {
      return !this.isUniformCanteen;
    }
  }
  EnableMarkAsFulfilledButton() {
    if (this.selectedOrder) {
      if (this.selectedOrder.IsFulFilled) {
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    let numRows = 0;
    if (this.dataSource.data) {
      numRows = this.dataSource.data.length;
    }
    return numSelected === numRows;
  }
  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected() ? this.selection.clear() : this.dataSource.data.forEach(row => this.selection.select(row));
  }
  /** The label for the checkbox on the passed row */
  checkboxLabel(row) {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row`;
  }
  RowClick(row) {
    if (this.selectedOrder && this.selectedOrder.OrderId === row.OrderId && this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId) {
      this.selectedOrder = null;
      this.itemsSelectedOrder = null;
    } else {
      this.selectedOrder = row;
      this.itemsSelectedOrder = row.Items;
    }
    //row selection for uniforms
    this.selection.toggle(row);
    if (this.isUniformCanteen && this.selection.selected.length === 1) {
      this.selectedOrder = this.selection.selected[0];
      this.itemsSelectedOrder = this.selection.selected[0].Items;
    }
  }
  PageChange(event) {
    this.clearRowSelection();
    // Update filter
    this.basePageChange(event);
    this.LoadTableData();
  }
  SortChange(event) {
    this.listfilters.PageIndex = 0;
    this.listfilters.SortBy = event.active;
    this.listfilters.SortDirection = event.direction;
    this.LoadTableData();
  }
  isRowSelected(row) {
    if (this.isUniformCanteen) {
      //multi selected rows
      return this.selection.isSelected(row);
    } else {
      return this.selectedOrder && this.selectedOrder.OrderId === row.OrderId && this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId;
    }
  }
  showSelectedOrder() {
    //show selected order canteen
    if (!this.isUniformCanteen && this.selectedOrder) {
      return true;
      //show selected order uniform
    } else {
      return Boolean(this.isUniformCanteen && this.selection.selected.length == 1 && this.selectedOrder);
    }
  }
  /////////////////////////////////////////////////////////
  // Printing functions
  //////////////////////////////////////////////////////////
  PrintAllLabels() {
    this.getOrdersRequest(true);
    this.spinnerService.start();
    if (this.labelPrintChoice === _sharedModels__WEBPACK_IMPORTED_MODULE_1__.LabelPrintChoiceEnum.Order) {
      this.printAllOrders();
      return;
    }
    this.printAllItems();
  }
  printAllOrders() {
    this.printingApiService.SetOrdersToPrintAPI(this.ordersRequest).subscribe({
      next: response => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  printAllItems() {
    this.printingApiService.SetItemsToPrintAPI(this.ordersRequest).subscribe({
      next: response => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  PrintSelectedOrder() {
    this.spinnerService.start();
    if (this.labelPrintChoice == _sharedModels__WEBPACK_IMPORTED_MODULE_1__.LabelPrintChoiceEnum.Item) {
      this.printOneOrderByItem();
    } else {
      this.printOneOrderByOrder();
    }
  }
  printOneOrderByOrder() {
    this.printingApiService.SetOrderToPrintByOrderIdAPI(this.selectedOrder.OrderId).subscribe({
      next: response => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  printOneOrderByItem() {
    if (!this.selectedOrder.Items || !this.selectedOrder.Items.length) {
      return;
    }
    const itemId = this.selectedOrder.Items[0].MenuItemId;
    this.printingApiService.SetItemToPrintByItemIdAPI(this.selectedOrder.OrderId, itemId).subscribe({
      next: response => {
        if (response?.guid) {
          this.handlePrintResponse(response.guid);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  handlePrintResponse(guid) {
    if (this.usePrintingApp) {
      this._triggerThermalPrinter(guid);
    } else {
      this._triggerA4Printer(guid);
    }
  }
  _triggerThermalPrinter(guid) {
    let canteenPrinter = document.getElementById('canteenPrinter');
    canteenPrinter.href = 'print://' + guid;
    canteenPrinter.click();
  }
  _triggerA4Printer(guid) {
    this.printingApiService.SetPrintingGuid(guid);
    this.router.navigate(['canteen/labels']);
  }
  static {
    this.ɵfac = function CanteenComponent_Factory(t) {
      return new (t || CanteenComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_11__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.OrderApiService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.CanteenService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.PrintingApiService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineComponent"]({
      type: CanteenComponent,
      selectors: [["app-canteen"]],
      viewQuery: function CanteenComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵviewQuery"](_angular_material_sort__WEBPACK_IMPORTED_MODULE_12__.MatSort, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵloadQuery"]()) && (ctx.sort = _t.first);
        }
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵInheritDefinitionFeature"]],
      decls: 50,
      vars: 16,
      consts: [[1, "container-fluid", "pt-4"], [3, "filtersChanged", "settingsChanged"], [1, "row", "mt-4"], [1, "col-sm-7", "d-flex", "justify-content-between", "align-items-end"], [1, "d-flex", "justify-content-between", "align-items-end"], [2, "margin", "0"], [2, "font-weight", "400"], ["style", "margin: 0", "class", "labelCount", 4, "ngIf"], [1, "d-flex"], [4, "ngIf"], ["text", "Print Labels", 3, "buttonStyle", "onPress"], ["id", "canteenPrinter", 2, "color", "white"], [1, "row", "mt-2"], [1, "col-sm-7", "pb-3"], [1, "table-container", "mat-elevation-z6"], ["mat-table", "", "matSort", "", "matSortActive", "ClassName", "matSortDisableClear", "", "matSortDirection", "desc", 1, "mat-elevation-z8", "tableau", 3, "dataSource", "matSortChange"], ["matColumnDef", "select"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "OrderId"], ["mat-header-cell", "", "mat-sort-header", "", "disableClear", "", 4, "matHeaderCellDef"], ["matColumnDef", "CanteenStatus"], ["matColumnDef", "SchoolName"], ["matColumnDef", "MenuType"], ["matColumnDef", "StudentName"], ["matColumnDef", "ClassName"], ["matColumnDef", "OrderDate"], ["mat-header-cell", "", "mat-sort-header", "", 3, "disabled", 4, "matHeaderCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef", "matHeaderRowDefSticky"], ["mat-row", "", 3, "ngClass", "click", 4, "matRowDef", "matRowDefColumns"], [3, "pageSize", "pageSizeOptions", "length", "pageIndex", "page"], [1, "col-sm-5", "pb-3"], [1, "row"], [1, "col-12"], [3, "order", "showOrder", "itemsSelectedOrder", "printOrder"], [1, "labelCount", 2, "margin", "0"], [3, "selectedOrders", "clearRowSelection", "loadTableData"], ["mat-header-cell", ""], [3, "checked", "indeterminate", "aria-label", "change"], ["mat-cell", ""], [3, "checked", "aria-label", "click", "change"], ["mat-header-cell", "", "mat-sort-header", "", "disableClear", ""], ["matTooltip", "View Child", 1, "student-link", 3, "routerLink"], ["mat-header-cell", "", "mat-sort-header", "", 3, "disabled"], ["mat-header-row", ""], ["mat-row", "", 3, "ngClass", "click"]],
      template: function CanteenComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div", 0)(1, "canteen-order-filter", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("filtersChanged", function CanteenComponent_Template_canteen_order_filter_filtersChanged_1_listener($event) {
            return ctx.FiltersChanged($event);
          })("settingsChanged", function CanteenComponent_Template_canteen_order_filter_settingsChanged_1_listener($event) {
            return ctx.SettingsChanged($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "h3", 5)(6, "strong");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](7, "Labels");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](8, "span", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](10, CanteenComponent_h4_10_Template, 2, 1, "h4", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](11, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](12, CanteenComponent_div_12_Template, 2, 1, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](13, "basic-button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("onPress", function CanteenComponent_Template_basic_button_onPress_13_listener() {
            return ctx.PrintAllLabels();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](14, "a", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](15, "div", 12)(16, "div", 13)(17, "div", 14)(18, "table", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("matSortChange", function CanteenComponent_Template_table_matSortChange_18_listener($event) {
            return ctx.SortChange($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](19, 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](20, CanteenComponent_th_20_Template, 2, 3, "th", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](21, CanteenComponent_td_21_Template, 2, 2, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](22, 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](23, CanteenComponent_th_23_Template, 2, 0, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](24, CanteenComponent_td_24_Template, 2, 1, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](25, 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](26, CanteenComponent_th_26_Template, 2, 0, "th", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](27, CanteenComponent_td_27_Template, 2, 1, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](28, 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](29, CanteenComponent_th_29_Template, 2, 0, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](30, CanteenComponent_td_30_Template, 2, 1, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](31, 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](32, CanteenComponent_th_32_Template, 2, 0, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](33, CanteenComponent_td_33_Template, 3, 3, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](34, 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](35, CanteenComponent_th_35_Template, 2, 0, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](36, CanteenComponent_td_36_Template, 3, 2, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](37, 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](38, CanteenComponent_th_38_Template, 2, 0, "th", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](39, CanteenComponent_td_39_Template, 2, 1, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](40, 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](41, CanteenComponent_th_41_Template, 2, 1, "th", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](42, CanteenComponent_td_42_Template, 4, 7, "td", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](43, CanteenComponent_tr_43_Template, 1, 0, "tr", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](44, CanteenComponent_tr_44_Template, 1, 3, "tr", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](45, "mat-paginator", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("page", function CanteenComponent_Template_mat_paginator_page_45_listener($event) {
            return ctx.PageChange($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](46, "div", 31)(47, "div", 32)(48, "div", 33)(49, "selected-order", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("printOrder", function CanteenComponent_Template_selected_order_printOrder_49_listener() {
            return ctx.PrintSelectedOrder();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate1"](" (", ctx.totalRows, ")");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngIf", ctx.isUniformCanteen);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngIf", ctx.isUniformCanteen);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("buttonStyle", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("dataSource", ctx.dataSource);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("matHeaderRowDef", ctx.displayedColumns)("matHeaderRowDefSticky", true);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("pageSize", 25)("pageSizeOptions", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction0"](15, _c1))("length", ctx.totalRows)("pageIndex", ctx.listfilters.PageIndex);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("order", ctx.selectedOrder)("showOrder", ctx.showSelectedOrder())("itemsSelectedOrder", ctx.itemsSelectedOrder);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_11__.RouterLink, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_3__.BasicButtonComponent, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_14__.MatCheckbox, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatRow, _angular_material_sort__WEBPACK_IMPORTED_MODULE_12__.MatSort, _angular_material_sort__WEBPACK_IMPORTED_MODULE_12__.MatSortHeader, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_15__.MatPaginator, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_16__.MatTooltip, _canteen_order_filter_canteen_order_filter_component__WEBPACK_IMPORTED_MODULE_4__.CanteenOrderFilterComponent, _uniform_order_status_picker_uniform_order_status_picker_component__WEBPACK_IMPORTED_MODULE_5__.UniformOrderStatusPickerComponent, _selected_order_selected_order_component__WEBPACK_IMPORTED_MODULE_6__.SelectedOrderComponent, _angular_common__WEBPACK_IMPORTED_MODULE_13__.DatePipe, _shared_components_menu_merchant_menu_name_pipe__WEBPACK_IMPORTED_MODULE_7__.MerchantMenuNamePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.table-container[_ngcontent-%COMP%] {\n  padding: 0;\n  width: 100%;\n  max-width: 100%;\n  overflow-x: auto;\n}\n\n.tableau[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.titleFilters[_ngcontent-%COMP%] {\n  font-size: 18px;\n  margin-bottom: 0;\n  color: #ff4b17;\n}\n\n.filterDiv[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-left: 20px;\n}\n.filterDiv[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  display: inline-block;\n  width: 150px;\n  height: 30px;\n  vertical-align: middle;\n  font-size: 18px;\n  margin-left: 20px;\n}\n.filterDiv[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  width: 200px;\n  display: inline-block;\n}\n\n.updateStatusDiv[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #ff4b17;\n  margin-top: 5px;\n  font-size: 22px;\n}\n.updateStatusDiv[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 120px;\n  height: 35px;\n  font-size: 18px;\n  margin-left: 40px;\n}\n\n.singleOrderPrinting[_ngcontent-%COMP%] {\n  height: 40px;\n  width: 200px;\n  margin-top: 30px;\n}\n\n.selectAllLink[_ngcontent-%COMP%] {\n  padding-bottom: 5px;\n}\n.selectAllLink[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #ff4b17;\n  cursor: pointer;\n}\n\n.selectedRow[_ngcontent-%COMP%] {\n  background-color: #ffe2c7;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]:hover {\n  background-color: #fff0e0;\n}\n\n.student-link[_ngcontent-%COMP%] {\n  color: #4754b0;\n  font-weight: 700;\n}\n\n.print-btn[_ngcontent-%COMP%] {\n  background-color: #272c50;\n  color: #ffffff;\n  font-size: 16px;\n  text-align: center;\n  font-weight: 700;\n  padding: 5px 10px;\n  margin: 0;\n  line-height: 16px;\n}\n\n.labelCount[_ngcontent-%COMP%] {\n  padding-left: 15px;\n  color: #6b6c89;\n  font-size: 16px;\n}\n\n.status-menu-btn[_ngcontent-%COMP%] {\n  line-height: 16px;\n  font-size: 16px;\n  padding: 0 5px;\n  margin: 0;\n}\n\n\n\n.table-container[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background {\n  background-color: #272c50;\n}\n\n\n\n.table-container[_ngcontent-%COMP%]     .mat-mdc-checkbox.mat-accent.mat-checkbox-indeterminate .mat-checkbox-background {\n  background-color: #272c50;\n}\n\n\n\n.table-container[_ngcontent-%COMP%]    .mat-mdc-checkbox .mat-checkbox-frame {\n  border-color: #272c50;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 94608:
/*!*********************************************!*\
  !*** ./src/app/canteen/components/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A4PrintFormComponent: () => (/* reexport safe */ _a4_print_form_a4_print_form_component__WEBPACK_IMPORTED_MODULE_11__.A4PrintFormComponent),
/* harmony export */   AnnouncementsComponent: () => (/* reexport safe */ _announcements_announcements_component__WEBPACK_IMPORTED_MODULE_7__.AnnouncementsComponent),
/* harmony export */   ArticlesComponent: () => (/* reexport safe */ _articles_articles_component__WEBPACK_IMPORTED_MODULE_10__.ArticlesComponent),
/* harmony export */   BlockPrintComponent: () => (/* reexport safe */ _block_print_block_print_component__WEBPACK_IMPORTED_MODULE_5__.BlockPrintComponent),
/* harmony export */   CanteenComponent: () => (/* reexport safe */ _canteen_canteen_component__WEBPACK_IMPORTED_MODULE_0__.CanteenComponent),
/* harmony export */   CanteenOrderCategoryFilterComponent: () => (/* reexport safe */ _canteen_order_category_filter_canteen_order_category_filter_component__WEBPACK_IMPORTED_MODULE_8__.CanteenOrderCategoryFilterComponent),
/* harmony export */   CanteenOrderFilterComponent: () => (/* reexport safe */ _canteen_order_filter_canteen_order_filter_component__WEBPACK_IMPORTED_MODULE_3__.CanteenOrderFilterComponent),
/* harmony export */   CanteenOrderSchoolFilterComponent: () => (/* reexport safe */ _canteen_order_school_filter_canteen_order_school_filter_component__WEBPACK_IMPORTED_MODULE_9__.CanteenOrderSchoolFilterComponent),
/* harmony export */   CanteenOrderTypeFilterComponent: () => (/* reexport safe */ _canteen_order_type_filter_canteen_order_type_filter_component__WEBPACK_IMPORTED_MODULE_4__.CanteenOrderTypeFilterComponent),
/* harmony export */   IosLabelsPrintingComponent: () => (/* reexport safe */ _ios_labels_printing_ios_labels_printing_component__WEBPACK_IMPORTED_MODULE_2__.IosLabelsPrintingComponent),
/* harmony export */   LabelPrintComponent: () => (/* reexport safe */ _label_print_label_print_component__WEBPACK_IMPORTED_MODULE_1__.LabelPrintComponent),
/* harmony export */   LabelTemplateComponent: () => (/* reexport safe */ _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_16__.LabelTemplateComponent),
/* harmony export */   LabelTemplateIosComponent: () => (/* reexport safe */ _label_template_ios_label_template_ios_component__WEBPACK_IMPORTED_MODULE_17__.LabelTemplateIosComponent),
/* harmony export */   NoticeBoardComponent: () => (/* reexport safe */ _notice_board_notice_board_component__WEBPACK_IMPORTED_MODULE_6__.NoticeBoardComponent),
/* harmony export */   NoticeHeaderComponent: () => (/* reexport safe */ _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_12__.NoticeHeaderComponent),
/* harmony export */   NoticeTableComponent: () => (/* reexport safe */ _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_13__.NoticeTableComponent),
/* harmony export */   SelectedOrderComponent: () => (/* reexport safe */ _selected_order_selected_order_component__WEBPACK_IMPORTED_MODULE_15__.SelectedOrderComponent),
/* harmony export */   UniformOrderStatusPickerComponent: () => (/* reexport safe */ _uniform_order_status_picker_uniform_order_status_picker_component__WEBPACK_IMPORTED_MODULE_14__.UniformOrderStatusPickerComponent)
/* harmony export */ });
/* harmony import */ var _canteen_canteen_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canteen/canteen.component */ 36490);
/* harmony import */ var _label_print_label_print_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./label-print/label-print.component */ 88642);
/* harmony import */ var _ios_labels_printing_ios_labels_printing_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ios-labels-printing/ios-labels-printing.component */ 93734);
/* harmony import */ var _canteen_order_filter_canteen_order_filter_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./canteen-order-filter/canteen-order-filter.component */ 16972);
/* harmony import */ var _canteen_order_type_filter_canteen_order_type_filter_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./canteen-order-type-filter/canteen-order-type-filter.component */ 87014);
/* harmony import */ var _block_print_block_print_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block-print/block-print.component */ 18302);
/* harmony import */ var _notice_board_notice_board_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notice-board/notice-board.component */ 21990);
/* harmony import */ var _announcements_announcements_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./announcements/announcements.component */ 24422);
/* harmony import */ var _canteen_order_category_filter_canteen_order_category_filter_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./canteen-order-category-filter/canteen-order-category-filter.component */ 93070);
/* harmony import */ var _canteen_order_school_filter_canteen_order_school_filter_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./canteen-order-school-filter/canteen-order-school-filter.component */ 76854);
/* harmony import */ var _articles_articles_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./articles/articles.component */ 62990);
/* harmony import */ var _a4_print_form_a4_print_form_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./a4-print-form/a4-print-form.component */ 122);
/* harmony import */ var _notice_header_notice_header_component__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./notice-header/notice-header.component */ 84334);
/* harmony import */ var _notice_table_notice_table_component__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./notice-table/notice-table.component */ 7750);
/* harmony import */ var _uniform_order_status_picker_uniform_order_status_picker_component__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./uniform-order-status-picker/uniform-order-status-picker.component */ 46118);
/* harmony import */ var _selected_order_selected_order_component__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./selected-order/selected-order.component */ 90500);
/* harmony import */ var _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./label-template/label-template.component */ 29294);
/* harmony import */ var _label_template_ios_label_template_ios_component__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./label-template-ios/label-template-ios.component */ 68030);



















/***/ }),

/***/ 93734:
/*!*****************************************************************************************!*\
  !*** ./src/app/canteen/components/ios-labels-printing/ios-labels-printing.component.ts ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IosLabelsPrintingComponent: () => (/* binding */ IosLabelsPrintingComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../label-template/label-template.component */ 29294);





function IosLabelsPrintingComponent_div_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function IosLabelsPrintingComponent_div_1_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "label-template", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const label_r5 = ctx.$implicit;
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("label", label_r5)("displaySchoolName", ctx_r3.displaySchoolName);
  }
}
function IosLabelsPrintingComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, IosLabelsPrintingComponent_div_1_div_1_Template, 2, 0, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, IosLabelsPrintingComponent_div_1_div_2_Template, 2, 2, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const page_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", page_r1.fakeLabels);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", page_r1.listLabels);
  }
}
class IosLabelsPrintingComponent {
  constructor(canteenService, route) {
    this.canteenService = canteenService;
    this.route = route;
    this.pagesLabels = [];
    this.custom = false;
    this.displaySchoolName = false;
  }
  ngOnInit() {
    this.displaySchoolName = this.route.snapshot.params['displaySchoolName'] === 'true';
    this.pagesLabels = this.canteenService.GetLabels();
  }
  static {
    this.ɵfac = function IosLabelsPrintingComponent_Factory(t) {
      return new (t || IosLabelsPrintingComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__.CanteenService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: IosLabelsPrintingComponent,
      selectors: [["app-ios-labels-printing"]],
      decls: 2,
      vars: 1,
      consts: [["id", "htmlData", 1, "background", 2, "width", "215mm"], ["class", "pageA4", 4, "ngFor", "ngForOf"], [1, "pageA4"], ["class", "colLabel", 4, "ngFor", "ngForOf"], [1, "colLabel"], [3, "label", "displaySchoolName"]],
      template: function IosLabelsPrintingComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, IosLabelsPrintingComponent_div_1_Template, 3, 2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.pagesLabels);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _label_template_label_template_component__WEBPACK_IMPORTED_MODULE_1__.LabelTemplateComponent],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.background[_ngcontent-%COMP%] {\n  background-color: white;\n  height: 100vh;\n}\n\n.pageA4[_ngcontent-%COMP%] {\n  width: 215mm;\n  background-color: white;\n  margin: 0mm;\n}\n\n.colLabel[_ngcontent-%COMP%] {\n  padding-top: 4mm;\n  padding-left: 4mm;\n  padding-right: 3mm;\n  padding-bottom: 2mm;\n  width: 69mm;\n  height: 45mm;\n  margin-left: 1mm;\n  margin-right: 1mm;\n  margin-bottom: 0mm;\n  display: inline-flex;\n  font-family: \"bariol_regular\";\n  border: white solid 1px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 88642:
/*!*************************************************************************!*\
  !*** ./src/app/canteen/components/label-print/label-print.component.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LabelPrintComponent: () => (/* binding */ LabelPrintComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _block_print_block_print_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../block-print/block-print.component */ 18302);
// Models






function LabelPrintComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 3)(2, "div", 0)(3, "div", 1)(4, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Labels");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "block-print", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("listLabels", ctx_r0.listLabels);
  }
}
function LabelPrintComponent_ng_container_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 3)(2, "div", 0)(3, "div", 1)(4, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "No Labels");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
}
class LabelPrintComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(router, printingApiService, spinnerService) {
    super();
    this.router = router;
    this.printingApiService = printingApiService;
    this.spinnerService = spinnerService;
    this.listLabels = [];
  }
  ngOnInit() {
    // get the orders to print
    this.spinnerService.start();
    const guid = this.printingApiService.GetPrintingGuid();
    this.printingApiService.GetOrdersToPrintByGuidAPI(guid).subscribe({
      next: response => {
        this.listLabels = response.Labels;
        if (!this.listLabels || this.listLabels.length == 0) {
          this.router.navigate(['./canteen']);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.handleErrorFromService(error);
        this.spinnerService.stop();
      }
    });
  }
  static {
    this.ɵfac = function LabelPrintComponent_Factory(t) {
      return new (t || LabelPrintComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.PrintingApiService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: LabelPrintComponent,
      selectors: [["app-label-print"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 4,
      vars: 2,
      consts: [[1, "row"], [1, "col-12"], [4, "ngIf"], [1, "container-fluid"], [3, "listLabels"]],
      template: function LabelPrintComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, LabelPrintComponent_ng_container_2_Template, 7, 1, "ng-container", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, LabelPrintComponent_ng_container_3_Template, 6, 0, "ng-container", 2);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.listLabels && ctx.listLabels.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.listLabels || ctx.listLabels.length == 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _block_print_block_print_component__WEBPACK_IMPORTED_MODULE_2__.BlockPrintComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.tabLabels[_ngcontent-%COMP%] {\n  width: 400px;\n  padding: 0;\n  padding-top: 30px;\n  padding-bottom: 15px;\n  margin: auto;\n  font-size: 18px;\n  font-weight: bold;\n}\n.tabLabels[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-left: 30px;\n  padding-bottom: 5px;\n  text-align: center;\n  color: #ff7a00;\n  cursor: pointer;\n}\n.tabLabels[_ngcontent-%COMP%]   li.active[_ngcontent-%COMP%] {\n  border-bottom: 2px solid #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 68030:
/*!***************************************************************************************!*\
  !*** ./src/app/canteen/components/label-template-ios/label-template-ios.component.ts ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LabelTemplateIosComponent: () => (/* binding */ LabelTemplateIosComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);


function LabelTemplateIosComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div")(1, "span", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r0.GetPrintValue("Title", ctx_r0.label));
  }
}
function LabelTemplateIosComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div")(1, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("Allergies: ", ctx_r1.GetPrintValue("Allergies", ctx_r1.label), "");
  }
}
function LabelTemplateIosComponent_ul_6_li_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "li", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", item_r4, " ");
  }
}
function LabelTemplateIosComponent_ul_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ul");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](1, LabelTemplateIosComponent_ul_6_li_1_Template, 2, 1, "li", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx_r2.label.Items);
  }
}
class LabelTemplateIosComponent {
  constructor() {}
  ngOnInit() {}
  GetPrintValue(printField, label) {
    var pos = label.Positions.find(x => x.Name == printField);
    if (pos) {
      return pos.Value;
    } else {
      return '';
    }
  }
  ShowAllergies(label) {
    var pos = label.Positions.find(x => x.Name == 'Allergies');
    if (pos) {
      return !Boolean(pos.Value == null || pos.Value == '');
    } else {
      return false;
    }
  }
  static {
    this.ɵfac = function LabelTemplateIosComponent_Factory(t) {
      return new (t || LabelTemplateIosComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: LabelTemplateIosComponent,
      selectors: [["label-template-ios"]],
      inputs: {
        label: "label",
        displaySchoolName: "displaySchoolName"
      },
      decls: 13,
      vars: 7,
      consts: [[4, "ngIf"], [1, "subTitle"], [1, "footer", "labelNumber"], [1, "footer", "footerLeft"], [1, "footer", "footerRight"], [1, "title"], [1, "allergies"], ["class", "items", 4, "ngFor", "ngForOf"], [1, "items"]],
      template: function LabelTemplateIosComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerStart"](0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](1, LabelTemplateIosComponent_div_1_Template, 3, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div")(3, "span", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](5, LabelTemplateIosComponent_div_5_Template, 3, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](6, LabelTemplateIosComponent_ul_6_Template, 2, 1, "ul", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "span", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "span", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "span", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.displaySchoolName);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("SubTitle", ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.ShowAllergies(ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.label.Items);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.label.LabelNumber);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("FooterLeft", ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("FooterRight", ctx.label));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf],
      styles: ["ul[_ngcontent-%COMP%] {\n  font-family: \"bariol_regular\";\n  margin-top: 0px;\n  margin-bottom: 0px;\n  padding-left: 20px;\n}\nul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  font-size: 16px;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 14px;\n  line-height: 14px;\n  font-family: \"bariol_regular\";\n  margin-top: 1mm;\n  margin-left: 2mm;\n}\n\n.subTitle[_ngcontent-%COMP%] {\n  font-size: 14px;\n  line-height: 14px;\n  font-family: \"bariol_bold\";\n  margin-left: 2mm;\n}\n\n.allergies[_ngcontent-%COMP%] {\n  font-size: 16px;\n  line-height: 16px;\n  font-family: \"bariol_regular\";\n  margin-left: 2mm;\n  text-decoration: underline;\n}\n\n.items[_ngcontent-%COMP%] {\n  margin-top: 0mm;\n  margin-bottom: 0mm;\n  font-size: 16px;\n  line-height: 16px;\n}\n\n.footer[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin-top: 0px;\n  display: inline-block;\n}\n\n.labelNumber[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.footerLeft[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n  margin-left: 0mm;\n  text-transform: uppercase;\n}\n\n.footerRight[_ngcontent-%COMP%] {\n  font-family: \"bariol_regular\";\n  margin-left: 0mm;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi9jb21wb25lbnRzL2xhYmVsLXRlbXBsYXRlLWlvcy9sYWJlbC10ZW1wbGF0ZS1pb3MuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSw2QkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FBQ0Y7QUFDRTtFQUNFLGVBQUE7QUFDSjs7QUFHQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBQUY7O0FBRUE7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSwwQkFBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBQ0E7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtFQUNBLGdCQUFBO0VBQ0EsMEJBQUE7QUFFRjs7QUFDQTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtBQUVGOztBQUNBO0VBQ0UsZUFBQTtFQUNBLGVBQUE7RUFDQSxxQkFBQTtBQUVGOztBQUNBO0VBQ0UsMEJBQUE7QUFFRjs7QUFDQTtFQUNFLDBCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQUVGOztBQUNBO0VBQ0UsNkJBQUE7RUFDQSxnQkFBQTtBQUVGIiwic291cmNlc0NvbnRlbnQiOlsidWwge1xuICBmb250LWZhbWlseTogJ2JhcmlvbF9yZWd1bGFyJztcbiAgbWFyZ2luLXRvcDogMHB4O1xuICBtYXJnaW4tYm90dG9tOiAwcHg7XG4gIHBhZGRpbmctbGVmdDogMjBweDtcblxuICAmIGxpIHtcbiAgICBmb250LXNpemU6IDE2cHg7XG4gIH1cbn1cblxuLnRpdGxlIHtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBsaW5lLWhlaWdodDogMTRweDtcbiAgZm9udC1mYW1pbHk6ICdiYXJpb2xfcmVndWxhcic7XG4gIG1hcmdpbi10b3A6IDFtbTtcbiAgbWFyZ2luLWxlZnQ6IDJtbTtcbn1cbi5zdWJUaXRsZSB7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgbGluZS1oZWlnaHQ6IDE0cHg7XG4gIGZvbnQtZmFtaWx5OiAnYmFyaW9sX2JvbGQnO1xuICBtYXJnaW4tbGVmdDogMm1tO1xufVxuLmFsbGVyZ2llcyB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDE2cHg7XG4gIGZvbnQtZmFtaWx5OiAnYmFyaW9sX3JlZ3VsYXInO1xuICBtYXJnaW4tbGVmdDogMm1tO1xuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbn1cblxuLml0ZW1zIHtcbiAgbWFyZ2luLXRvcDogMG1tO1xuICBtYXJnaW4tYm90dG9tOiAwbW07XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDE2cHg7XG59XG5cbi5mb290ZXIge1xuICBmb250LXNpemU6IDE2cHg7XG4gIG1hcmdpbi10b3A6IDBweDtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xufVxuXG4ubGFiZWxOdW1iZXIge1xuICBmb250LWZhbWlseTogJ2JhcmlvbF9ib2xkJztcbn1cblxuLmZvb3RlckxlZnQge1xuICBmb250LWZhbWlseTogJ2JhcmlvbF9ib2xkJztcbiAgbWFyZ2luLWxlZnQ6IDBtbTtcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbn1cblxuLmZvb3RlclJpZ2h0IHtcbiAgZm9udC1mYW1pbHk6ICdiYXJpb2xfcmVndWxhcic7XG4gIG1hcmdpbi1sZWZ0OiAwbW07XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 29294:
/*!*******************************************************************************!*\
  !*** ./src/app/canteen/components/label-template/label-template.component.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LabelTemplateComponent: () => (/* binding */ LabelTemplateComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);


function LabelTemplateComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div")(1, "span", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r0.GetPrintValue("Title", ctx_r0.label));
  }
}
function LabelTemplateComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div")(1, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("Allergies: ", ctx_r1.GetPrintValue("Allergies", ctx_r1.label), "");
  }
}
function LabelTemplateComponent_ul_6_li_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "li", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" ", item_r4, " ");
  }
}
function LabelTemplateComponent_ul_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "ul");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](1, LabelTemplateComponent_ul_6_li_1_Template, 2, 1, "li", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx_r2.label.Items);
  }
}
class LabelTemplateComponent {
  constructor() {}
  ngOnInit() {}
  GetPrintValue(printField, label) {
    var pos = label.Positions.find(x => x.Name == printField);
    if (pos) {
      return pos.Value;
    } else {
      return '';
    }
  }
  ShowAllergies(label) {
    var pos = label.Positions.find(x => x.Name == 'Allergies');
    if (pos) {
      return !Boolean(pos.Value == null || pos.Value == '');
    } else {
      return false;
    }
  }
  static {
    this.ɵfac = function LabelTemplateComponent_Factory(t) {
      return new (t || LabelTemplateComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: LabelTemplateComponent,
      selectors: [["label-template"]],
      inputs: {
        label: "label",
        displaySchoolName: "displaySchoolName"
      },
      decls: 13,
      vars: 7,
      consts: [[4, "ngIf"], [1, "subTitle"], [1, "footer", "labelNumber"], [1, "footer", "footerLeft"], [1, "footer", "footerRight"], [1, "title"], [1, "allergies"], ["class", "items", 4, "ngFor", "ngForOf"], [1, "items"]],
      template: function LabelTemplateComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerStart"](0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](1, LabelTemplateComponent_div_1_Template, 3, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "div")(3, "span", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](5, LabelTemplateComponent_div_5_Template, 3, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](6, LabelTemplateComponent_ul_6_Template, 2, 1, "ul", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "span", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](9, "span", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "span", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.displaySchoolName);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("SubTitle", ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.ShowAllergies(ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.label.Items);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.label.LabelNumber);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("FooterLeft", ctx.label));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.GetPrintValue("FooterRight", ctx.label));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf],
      styles: ["ul[_ngcontent-%COMP%] {\n  font-family: \"bariol_regular\";\n  margin-top: 0px;\n  margin-bottom: 0px;\n  padding-left: 20px;\n}\nul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 16px;\n  line-height: 16px;\n  font-family: \"bariol_regular\";\n  margin-top: 1mm;\n  margin-left: 2mm;\n}\n\n.subTitle[_ngcontent-%COMP%] {\n  font-size: 16px;\n  line-height: 16px;\n  font-family: \"bariol_bold\";\n  margin-left: 2mm;\n}\n\n.allergies[_ngcontent-%COMP%] {\n  font-size: 18px;\n  line-height: 18px;\n  font-family: \"bariol_regular\";\n  margin-left: 2mm;\n  text-decoration: underline;\n}\n\n.items[_ngcontent-%COMP%] {\n  margin-top: 0mm;\n  margin-bottom: 0mm;\n  font-size: 18px;\n  line-height: 18px;\n}\n\n.footer[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin-top: 0px;\n  display: inline-block;\n}\n\n.labelNumber[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.footerLeft[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n  margin-left: 8mm;\n  text-transform: uppercase;\n}\n\n.footerRight[_ngcontent-%COMP%] {\n  font-family: \"bariol_regular\";\n  margin-left: 8mm;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi9jb21wb25lbnRzL2xhYmVsLXRlbXBsYXRlL2xhYmVsLXRlbXBsYXRlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsNkJBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQUNGO0FBQ0U7RUFDRSxlQUFBO0FBQ0o7O0FBR0E7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQUFGOztBQUVBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsMEJBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUNBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtFQUNBLDBCQUFBO0FBRUY7O0FBQ0E7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7QUFFRjs7QUFDQTtFQUNFLGVBQUE7RUFDQSxlQUFBO0VBQ0EscUJBQUE7QUFFRjs7QUFDQTtFQUNFLDBCQUFBO0FBRUY7O0FBQ0E7RUFDRSwwQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7QUFFRjs7QUFDQTtFQUNFLDZCQUFBO0VBQ0EsZ0JBQUE7QUFFRiIsInNvdXJjZXNDb250ZW50IjpbInVsIHtcbiAgZm9udC1mYW1pbHk6ICdiYXJpb2xfcmVndWxhcic7XG4gIG1hcmdpbi10b3A6IDBweDtcbiAgbWFyZ2luLWJvdHRvbTogMHB4O1xuICBwYWRkaW5nLWxlZnQ6IDIwcHg7XG5cbiAgJiBsaSB7XG4gICAgZm9udC1zaXplOiAxOHB4O1xuICB9XG59XG5cbi50aXRsZSB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDE2cHg7XG4gIGZvbnQtZmFtaWx5OiAnYmFyaW9sX3JlZ3VsYXInO1xuICBtYXJnaW4tdG9wOiAxbW07XG4gIG1hcmdpbi1sZWZ0OiAybW07XG59XG4uc3ViVGl0bGUge1xuICBmb250LXNpemU6IDE2cHg7XG4gIGxpbmUtaGVpZ2h0OiAxNnB4O1xuICBmb250LWZhbWlseTogJ2JhcmlvbF9ib2xkJztcbiAgbWFyZ2luLWxlZnQ6IDJtbTtcbn1cbi5hbGxlcmdpZXMge1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAxOHB4O1xuICBmb250LWZhbWlseTogJ2JhcmlvbF9yZWd1bGFyJztcbiAgbWFyZ2luLWxlZnQ6IDJtbTtcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG59XG5cbi5pdGVtcyB7XG4gIG1hcmdpbi10b3A6IDBtbTtcbiAgbWFyZ2luLWJvdHRvbTogMG1tO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAxOHB4O1xufVxuXG4uZm9vdGVyIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBtYXJnaW4tdG9wOiAwcHg7XG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbn1cblxuLmxhYmVsTnVtYmVyIHtcbiAgZm9udC1mYW1pbHk6ICdiYXJpb2xfYm9sZCc7XG59XG5cbi5mb290ZXJMZWZ0IHtcbiAgZm9udC1mYW1pbHk6ICdiYXJpb2xfYm9sZCc7XG4gIG1hcmdpbi1sZWZ0OiA4bW07XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG59XG5cbi5mb290ZXJSaWdodCB7XG4gIGZvbnQtZmFtaWx5OiAnYmFyaW9sX3JlZ3VsYXInO1xuICBtYXJnaW4tbGVmdDogOG1tO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 21990:
/*!***************************************************************************!*\
  !*** ./src/app/canteen/components/notice-board/notice-board.component.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NoticeBoardComponent: () => (/* binding */ NoticeBoardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../shared-tools/components/settings-row/settings-row.component */ 7429);


class NoticeBoardComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function NoticeBoardComponent_Factory(t) {
      return new (t || NoticeBoardComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: NoticeBoardComponent,
      selectors: [["app-notice-board"]],
      decls: 5,
      vars: 0,
      consts: [[1, "col-8", "settingsWrapper"], ["text", "Announcements", "route", "/canteen/notice/announcements"], ["src", "assets/icons/announcements.svg"], ["text", "Articles", "route", "/canteen/notice/articles", "lastRow", "true"], ["src", "assets/icons/article.svg"]],
      template: function NoticeBoardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "settings-row", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "img", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "settings-row", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](4, "img", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
      },
      dependencies: [_shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_0__.SettingsRowComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.settingsWrapper[_ngcontent-%COMP%] {\n  margin: 16px 20px 0 20px;\n  border-radius: 12px;\n  overflow: hidden;\n  padding: 0;\n}\n\nimg[_ngcontent-%COMP%] {\n  width: 22px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi9jb21wb25lbnRzL25vdGljZS1ib2FyZC9ub3RpY2UtYm9hcmQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS0E7RUFDRSxhQUFBO0FDSkY7QURLRTtFQUZGO0lBR0ksY0FBQTtFQ0ZGO0FBQ0Y7O0FES0E7RUFDRSxhQUFBO0FDRkY7QURHRTtFQUZGO0lBR0ksY0FBQTtFQ0FGO0FBQ0Y7O0FBZEE7RUFDRSx3QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxVQUFBO0FBaUJGOztBQWRBO0VBQ0UsV0FBQTtBQWlCRiIsInNvdXJjZXNDb250ZW50IjpbIiRicmVha3BvaW50LXNtOiA1NzZweDtcbiRicmVha3BvaW50LW1kOiA3NjdweDtcbiRicmVha3BvaW50LWxnOiA5OTJweDtcbiRicmVha3BvaW50LXhsOiAxMjAwcHg7XG5cbi5tb2JpbGUge1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1heC13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuLy8gTk9URSBDdXJyZW50bHkgdGFibGV0IGFuZCBtb2JpbGUgaXMgdGhlIHNhbWUuIENoYW5nZSB0byAkYnJlYWtwb2ludC1sZyBsYXRlciBpZiB3ZSBnZXQgYSBwcm9wZXIgdGFibGV0IGRlc2lnbi5cbi5kZXNrdG9wIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtaW4td2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbiIsIkBpbXBvcnQgJy4uLy4uLy4uLy4uL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzJztcblxuLnNldHRpbmdzV3JhcHBlciB7XG4gIG1hcmdpbjogMTZweCAyMHB4IDAgMjBweDtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgcGFkZGluZzogMDtcbn1cblxuaW1nIHtcbiAgd2lkdGg6IDIycHg7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 84334:
/*!*****************************************************************************!*\
  !*** ./src/app/canteen/components/notice-header/notice-header.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NoticeHeaderComponent: () => (/* binding */ NoticeHeaderComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../shared-tools/components/merchant-school-picker/merchant-school-picker.component */ 38237);



class NoticeHeaderComponent {
  constructor() {
    this.schoolChanged = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this.merchantChanged = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this.openForm = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
  }
  ngOnInit() {}
  OnSchoolSelect(event) {
    this.schoolChanged.emit(event);
  }
  OnMerchantChange(event) {
    this.merchantChanged.emit(event);
  }
  openNoticeForm() {
    this.openForm.emit();
  }
  static {
    this.ɵfac = function NoticeHeaderComponent_Factory(t) {
      return new (t || NoticeHeaderComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: NoticeHeaderComponent,
      selectors: [["notice-header"]],
      inputs: {
        title: "title"
      },
      outputs: {
        schoolChanged: "schoolChanged",
        merchantChanged: "merchantChanged",
        openForm: "openForm"
      },
      decls: 12,
      vars: 3,
      consts: [[1, "titleWrapper"], ["sizes", "24", "src", "assets/icons/announcements.svg"], [1, "titleMain"], [3, "schoolChanged", "merchantChanged"], [1, "titleDescriptionWrapper"], [1, "titleDescription"], [1, "titleDescriptionButton", 3, "click"], [1, "titleDescriptionButtonText"], ["sizes", "24", "src", "assets/icons/plus.svg"]],
      template: function NoticeHeaderComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "img", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "merchant-school-picker", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("schoolChanged", function NoticeHeaderComponent_Template_merchant_school_picker_schoolChanged_4_listener($event) {
            return ctx.OnSchoolSelect($event);
          })("merchantChanged", function NoticeHeaderComponent_Template_merchant_school_picker_merchantChanged_4_listener($event) {
            return ctx.OnMerchantChange($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 4)(6, "h3", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "a", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function NoticeHeaderComponent_Template_a_click_8_listener() {
            return ctx.openNoticeForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "p", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](11, "img", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", ctx.title, "s");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", ctx.title, " List");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("New ", ctx.title, "");
        }
      },
      dependencies: [_shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_0__.MerchantSchoolPickerComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.titleWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 15px;\n  margin-left: 15px;\n  align-items: center;\n}\n\n.titleMain[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  margin: 0;\n  margin-left: 8px;\n}\n\n.titleDescription[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 20px;\n}\n\n.titleDescriptionWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 18px 20px;\n}\n\n.titleDescriptionButton[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n}\n\n.titleDescriptionButtonText[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  text-align: right;\n  margin: 0;\n  margin-right: 9px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 7750:
/*!***************************************************************************!*\
  !*** ./src/app/canteen/components/notice-table/notice-table.component.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NoticeTableComponent: () => (/* binding */ NoticeTableComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/table */ 77697);

//Models




function NoticeTableComponent_table_0_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "th", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Title");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](element_r11.Title);
  }
}
function NoticeTableComponent_table_0_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Body/Description");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a0) {
  return {
    noDescription: a0
  };
};
function NoticeTableComponent_table_0_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r12 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](2, _c0, !element_r12.Description.length));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", element_r12.Description || "No description", " ");
  }
}
function NoticeTableComponent_table_0_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "th", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Active");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_9_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "div", 21);
  }
}
function NoticeTableComponent_table_0_td_9_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "img", 22);
  }
}
function NoticeTableComponent_table_0_td_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, NoticeTableComponent_table_0_td_9_div_1_Template, 1, 0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, NoticeTableComponent_table_0_td_9_img_2_Template, 1, 0, "img", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r13 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !element_r13.IsActive);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", element_r13.IsActive);
  }
}
function NoticeTableComponent_table_0_th_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "th", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Status");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_12_span_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Waiting for validation");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_12_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Not valid");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_12_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Approved");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_td_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, NoticeTableComponent_table_0_td_12_span_1_Template, 2, 0, "span", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, NoticeTableComponent_table_0_td_12_span_2_Template, 2, 0, "span", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, NoticeTableComponent_table_0_td_12_span_3_Template, 2, 0, "span", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r16 = ctx.$implicit;
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", element_r16.Status == ctx_r8.NoticeStatusEnum.WaitingValidation);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", element_r16.Status == ctx_r8.NoticeStatusEnum.Refused);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", element_r16.Status == ctx_r8.NoticeStatusEnum.Validated);
  }
}
function NoticeTableComponent_table_0_tr_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "tr", 29);
  }
}
function NoticeTableComponent_table_0_tr_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "tr", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function NoticeTableComponent_table_0_tr_14_Template_tr_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r22);
      const row_r20 = restoredCtx.$implicit;
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r21.noticeRowClick(row_r20));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function NoticeTableComponent_table_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "table", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](1, 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, NoticeTableComponent_table_0_th_2_Template, 2, 0, "th", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, NoticeTableComponent_table_0_td_3_Template, 2, 1, "td", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](4, 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](5, NoticeTableComponent_table_0_th_5_Template, 2, 0, "th", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, NoticeTableComponent_table_0_td_6_Template, 2, 4, "td", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](7, 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](8, NoticeTableComponent_table_0_th_8_Template, 2, 0, "th", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](9, NoticeTableComponent_table_0_td_9_Template, 3, 2, "td", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](10, 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](11, NoticeTableComponent_table_0_th_11_Template, 2, 0, "th", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](12, NoticeTableComponent_table_0_td_12_Template, 4, 3, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](13, NoticeTableComponent_table_0_tr_13_Template, 1, 0, "tr", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](14, NoticeTableComponent_table_0_tr_14_Template, 1, 0, "tr", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("dataSource", ctx_r0.tableData);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("matHeaderRowDef", ctx_r0.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("matRowDefColumns", ctx_r0.displayedColumns);
  }
}
class NoticeTableComponent {
  constructor() {
    this.selectRow = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this.displayedColumns = ['title', 'body', 'IsActive', 'Status'];
    this.NoticeStatusEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.NoticeStatusEnum;
  }
  ngOnInit() {}
  noticeRowClick(event) {
    this.selectRow.emit(event);
  }
  static {
    this.ɵfac = function NoticeTableComponent_Factory(t) {
      return new (t || NoticeTableComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: NoticeTableComponent,
      selectors: [["notice-table"]],
      inputs: {
        tableData: "tableData",
        type: "type"
      },
      outputs: {
        selectRow: "selectRow"
      },
      decls: 1,
      vars: 1,
      consts: [["mat-table", "", "class", "mat-elevation-z8 tableau table", 3, "dataSource", 4, "ngIf"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "table", 3, "dataSource"], ["matColumnDef", "title"], ["mat-header-cell", "", "class", "title", 4, "matHeaderCellDef"], ["mat-cell", "", "class", "tableElement", 4, "matCellDef"], ["matColumnDef", "body"], ["mat-header-cell", "", "class", "titleCenter", 4, "matHeaderCellDef"], ["mat-cell", "", "class", "tableElement", 3, "ngClass", 4, "matCellDef"], ["matColumnDef", "IsActive"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "Status"], ["mat-cell", "", "style", "text-align: center", 4, "matCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "rowElement", 3, "click", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", "", 1, "title"], ["mat-cell", "", 1, "tableElement"], ["mat-header-cell", "", 1, "titleCenter"], ["mat-cell", "", 1, "tableElement", 3, "ngClass"], ["mat-cell", ""], ["class", "inActiveCheckbox", 4, "ngIf"], ["sizes", "24", "src", "assets/icons/checkBox.svg", "class", "checkBox", 4, "ngIf"], [1, "inActiveCheckbox"], ["sizes", "24", "src", "assets/icons/checkBox.svg", 1, "checkBox"], ["mat-cell", "", 2, "text-align", "center"], [4, "ngIf"], ["style", "color: red", 4, "ngIf"], ["style", "color: green", 4, "ngIf"], [2, "color", "red"], [2, "color", "green"], ["mat-header-row", ""], ["mat-row", "", 1, "rowElement", 3, "click"]],
      template: function NoticeTableComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, NoticeTableComponent_table_0_Template, 15, 3, "table", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.tableData);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_3__.MatRow],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mat-elevation-z8[_ngcontent-%COMP%] {\n  box-shadow: 0 0 #000000;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-style: normal;\n  font-weight: normal;\n  font-size: 14px;\n  line-height: 15px;\n  color: #828282;\n  width: 30%;\n}\n\n.table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-radius: 12px;\n  overflow: hidden;\n  margin-bottom: 30px;\n}\n\n.tableElement[_ngcontent-%COMP%] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  padding-right: 30px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  max-width: 0px;\n}\n\n.titleCenter[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-style: normal;\n  font-weight: normal;\n  font-size: 14px;\n  line-height: 15px;\n  color: #828282;\n  width: 65%;\n}\n\n.rowElement[_ngcontent-%COMP%] {\n  height: 61px;\n}\n\n.rowElement[_ngcontent-%COMP%]:hover {\n  background-color: #fff0e0;\n  cursor: pointer;\n}\n\n.checkBox[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 90500:
/*!*******************************************************************************!*\
  !*** ./src/app/canteen/components/selected-order/selected-order.component.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SelectedOrderComponent: () => (/* binding */ SelectedOrderComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);




function SelectedOrderComponent_basic_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "basic-button", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("onPress", function SelectedOrderComponent_basic_button_4_Template_basic_button_onPress_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r2.printButtonPressed());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("buttonStyle", 1);
  }
}
function SelectedOrderComponent_div_5_span_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" - (", ctx_r4.order.Allergies, ")");
  }
}
function SelectedOrderComponent_div_5_li_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r7 = ctx.$implicit;
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate3"](" ", item_r7.Quantity, " x ", item_r7.Name, " ", ctx_r5.GetItemOptions(item_r7), " ");
  }
}
function SelectedOrderComponent_div_5_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("Order ID: ", ctx_r6.order.LocalRunNumber, "");
  }
}
function SelectedOrderComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div")(1, "div", 5)(2, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 5)(5, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](7, SelectedOrderComponent_div_5_span_7_Template, 2, 1, "span", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](10, "ul");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](11, SelectedOrderComponent_div_5_li_11_Template, 2, 3, "li", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](12, SelectedOrderComponent_div_5_div_12_Template, 2, 1, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r1.order.SchoolName);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r1.order.ClassName);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r1.order.Allergies);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r1.order.StudentName);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r1.itemsSelectedOrder);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r1.order);
  }
}
class SelectedOrderComponent {
  constructor() {
    this.printOrder = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
  }
  ngOnInit() {}
  GetItemOptions(item) {
    let optionsText = '(';
    //item stores modifiers in Options
    if (item.Options && item.Options.length > 0) {
      item.Options.forEach(opt => {
        // item has selected options
        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {
          opt.SelectedOptionList.forEach(selectedOpt => {
            optionsText += selectedOpt + ', ';
          });
        }
      });
    }
    //item stores modifiers in SelectedOptions
    if (item.SelectedOptions && item.SelectedOptions.length > 0) {
      item.SelectedOptions.forEach(opt => {
        optionsText += opt.OptionName + ', ';
      });
    }
    optionsText = optionsText.slice(0, -2);
    optionsText += ')';
    if (optionsText.length <= 1) {
      optionsText = '';
    }
    return optionsText;
  }
  printButtonPressed() {
    this.printOrder.emit();
  }
  static {
    this.ɵfac = function SelectedOrderComponent_Factory(t) {
      return new (t || SelectedOrderComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: SelectedOrderComponent,
      selectors: [["selected-order"]],
      inputs: {
        order: "order",
        itemsSelectedOrder: "itemsSelectedOrder",
        showOrder: "showOrder"
      },
      outputs: {
        printOrder: "printOrder"
      },
      decls: 6,
      vars: 2,
      consts: [[1, "divOrderDetails", "mat-elevation-z2"], [1, "header"], ["text", "Print this order", 3, "buttonStyle", "onPress", 4, "ngIf"], [4, "ngIf"], ["text", "Print this order", 3, "buttonStyle", "onPress"], [1, "dataline"], [4, "ngFor", "ngForOf"]],
      template: function SelectedOrderComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Selected Order");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](4, SelectedOrderComponent_basic_button_4_Template, 1, 1, "basic-button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](5, SelectedOrderComponent_div_5_Template, 13, 6, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.showOrder);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.showOrder);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_0__.BasicButtonComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.divOrderDetails[_ngcontent-%COMP%] {\n  min-height: 300px;\n  height: -moz-fit-content;\n  height: fit-content;\n  width: 100%;\n  background-color: #ffffff;\n  padding: 10px;\n}\n.divOrderDetails[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #ff4b17;\n  margin-top: 5px;\n  font-size: 22px;\n}\n.divOrderDetails[_ngcontent-%COMP%]   .dataline[_ngcontent-%COMP%] {\n  margin-bottom: 5px;\n  font-size: 10px;\n}\n.divOrderDetails[_ngcontent-%COMP%]   .dataline[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: bold;\n}\n.divOrderDetails[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  margin-top: 10px;\n  margin-bottom: 10px;\n  padding-left: 15px;\n}\n\n.header[_ngcontent-%COMP%] {\n  display: flex;\n  width: 100%;\n  justify-content: space-between;\n}\n.header[_ngcontent-%COMP%]   .printButton[_ngcontent-%COMP%] {\n  outline: none;\n  border: none;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 700;\n  border-radius: 8px;\n  box-shadow: 0px 1px #e0e0e0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: #272c50;\n}\n.header[_ngcontent-%COMP%]   .printButton[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  color: #ffffff;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 46118:
/*!*********************************************************************************************************!*\
  !*** ./src/app/canteen/components/uniform-order-status-picker/uniform-order-status-picker.component.ts ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UniformOrderStatusPickerComponent: () => (/* binding */ UniformOrderStatusPickerComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/menu */ 31034);










function UniformOrderStatusPickerComponent_button_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function UniformOrderStatusPickerComponent_button_6_Template_button_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r4);
      const status_r2 = restoredCtx.$implicit;
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r3.showDialog(status_r2.value));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const status_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", status_r2.value, " ");
  }
}
class UniformOrderStatusPickerComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(spinnerService, orderAPIService, dialog) {
    super();
    this.spinnerService = spinnerService;
    this.orderAPIService = orderAPIService;
    this.dialog = dialog;
    this.loadTableData = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.clearRowSelection = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.CanteenStatusEnum = _sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenStatusEnum;
  }
  showDialog(newStatus) {
    if (this.completedOrdersSelected()) {
      this.showWarningDialog();
      return;
    }
    this.showUpdateStatusDialog(newStatus);
  }
  completedOrdersSelected() {
    return this.selectedOrders.some(order => order.CanteenStatus === _sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenStatusEnum.Completed);
  }
  showWarningDialog() {
    const data = this.getWarningDialogData();
    this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
  }
  showUpdateStatusDialog(newStatus) {
    const orderCountText = this.selectedOrders.length === 1 ? '1 order' : `${this.selectedOrders.length} orders`;
    const data = this.getUpdateDialogData(orderCountText, newStatus);
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.updateOrderStatus(newStatus);
      }
    });
  }
  updateOrderStatus(newStatus) {
    this.spinnerService.start();
    const selectedOrderIds = this.selectedOrders.map(el => el.OrderId);
    this.orderAPIService.UpdateListCanteenStatusOrder(selectedOrderIds, newStatus).subscribe({
      next: response => {
        // Give time for orders table in the replica db to update before refreshing orders
        setTimeout(() => {
          this.loadTableData.emit();
        }, 1000);
        this.clearRowSelection.emit();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  getWarningDialogData() {
    const data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ResultDialogData();
    data.TitleLine1 = 'Completed orders cannot have their status updated';
    data.TextLine2 = 'Please unselect completed orders to continue';
    data.ConfirmButton = 'Ok';
    return data;
  }
  getUpdateDialogData(orderCount, newStatus) {
    const data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ResultDialogData();
    data.TitleLine1 = 'Are you sure you want to update the order status? ';
    data.TextLine2 = this.getDialogMessage(orderCount, newStatus);
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, change status';
    return data;
  }
  getDialogMessage(orderCount, newStatus) {
    return newStatus === _sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenStatusEnum.Completed ? `Once an order's status is updated to "${_sharedModels__WEBPACK_IMPORTED_MODULE_1__.CanteenStatusEnum.Completed}" the status cannot be changed again` : `${orderCount} will be updated to “${newStatus}”`;
  }
  static {
    this.ɵfac = function UniformOrderStatusPickerComponent_Factory(t) {
      return new (t || UniformOrderStatusPickerComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.OrderApiService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_4__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: UniformOrderStatusPickerComponent,
      selectors: [["uniform-order-status-picker"]],
      inputs: {
        selectedOrders: "selectedOrders"
      },
      outputs: {
        loadTableData: "loadTableData",
        clearRowSelection: "clearRowSelection"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 8,
      vars: 5,
      consts: [["mat-button", "", 1, "status-menu-btn", 3, "disabled", "matMenuTriggerFor"], ["menu", "matMenu"], ["mat-menu-item", "", "class", "menu-status-picker", 3, "click", 4, "ngFor", "ngForOf"], ["mat-menu-item", "", 1, "menu-status-picker", 3, "click"]],
      template: function UniformOrderStatusPickerComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, " Order Status ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "mat-icon");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "arrow_drop_down");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "mat-menu", null, 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, UniformOrderStatusPickerComponent_button_6_Template, 2, 1, "button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](7, "keyvalue");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", !ctx.selectedOrders.length)("matMenuTriggerFor", _r0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](7, 3, ctx.CanteenStatusEnum));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIcon, _angular_material_button__WEBPACK_IMPORTED_MODULE_7__.MatButton, _angular_material_menu__WEBPACK_IMPORTED_MODULE_8__.MatMenu, _angular_material_menu__WEBPACK_IMPORTED_MODULE_8__.MatMenuItem, _angular_material_menu__WEBPACK_IMPORTED_MODULE_8__.MatMenuTrigger, _angular_common__WEBPACK_IMPORTED_MODULE_5__.KeyValuePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.menu-status-picker[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n}\n\n.completed-menu-item[_ngcontent-%COMP%] {\n  background-color: #e5e5e5;\n  color: #4754b0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2225:
/*!*********************************************************************!*\
  !*** ./src/app/sharedServices/canteen/menu-editor-guard.service.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MenuEditorGuardService: () => (/* binding */ MenuEditorGuardService)
/* harmony export */ });
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 59452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs/operators */ 36647);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);






class MenuEditorGuardService {
  constructor(store, router) {
    this.store = store;
    this.router = router;
  }
  canLoad() {
    return this.store.select(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.selectedCanteen).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.switchMap)(selectedCanteen => {
      if (!selectedCanteen.IsMenuEditorAvailable) {
        this.router.navigate(['/canteen']);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(false);
      } else {
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(true);
      }
    }));
  }
  canActivateChild() {
    return this.store.select(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.selectedCanteen).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.switchMap)(selectedCanteen => {
      if (!selectedCanteen.IsMenuEditorAvailable) {
        this.router.navigate(['/canteen']);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(false);
      } else {
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(true);
      }
    }));
  }
  static {
    this.ɵfac = function MenuEditorGuardService_Factory(t) {
      return new (t || MenuEditorGuardService)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_4__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: MenuEditorGuardService,
      factory: MenuEditorGuardService.ɵfac
    });
  }
}

/***/ }),

/***/ 15531:
/*!**********************************************************************!*\
  !*** ./src/app/sharedServices/canteen/sales-report-guard.service.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SalesReportGuardService: () => (/* binding */ SalesReportGuardService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 59452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs/operators */ 36647);
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
// Services






class SalesReportGuardService {
  constructor(store, router) {
    this.store = store;
    this.router = router;
  }
  canLoad() {
    return this.store.select(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.selectedCanteen).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.switchMap)(selectedCanteen => {
      if (!selectedCanteen.IsSaleReportsAvailable) {
        this.router.navigate(['/canteen']);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(false);
      } else {
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(true);
      }
    }));
  }
  canActivateChild() {
    return this.store.select(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.selectedCanteen).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_1__.switchMap)(selectedCanteen => {
      if (!selectedCanteen.IsSaleReportsAvailable) {
        this.router.navigate(['/canteen']);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(false);
      } else {
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.of)(true);
      }
    }));
  }
  static {
    this.ɵfac = function SalesReportGuardService_Factory(t) {
      return new (t || SalesReportGuardService)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_4__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: SalesReportGuardService,
      factory: SalesReportGuardService.ɵfac
    });
  }
}

/***/ }),

/***/ 83433:
/*!********************************************************************************!*\
  !*** ./src/app/shared/components/merchant-navbar/merchant-navbar.component.ts ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantNavbarComponent: () => (/* binding */ MerchantNavbarComponent)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../states/canteen/canteen.selectors */ 80974);
/* harmony import */ var _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/toolbar */ 39552);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/menu */ 31034);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var src_app_sharedServices_brazeTimer_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices/brazeTimer.service */ 17730);


// ngrx

















function MerchantNavbarComponent_mat_toolbar_0_button_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 8)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const data_r4 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("routerLink", data_r4.Link);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](data_r4.Name);
  }
}
function MerchantNavbarComponent_mat_toolbar_0_li_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "li")(1, "a", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "span", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const data_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("routerLink", data_r5.Link);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](data_r5.Name);
  }
}
function MerchantNavbarComponent_mat_toolbar_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-toolbar", 1)(1, "button", 2)(2, "mat-icon");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "more_vert");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "mat-menu", null, 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, MerchantNavbarComponent_mat_toolbar_0_button_6_Template, 3, 2, "button", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "ul", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](8, MerchantNavbarComponent_mat_toolbar_0_li_8_Template, 4, 2, "li", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](9, "span", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](5);
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("matMenuTriggerFor", _r1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r0.navDataToShow);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r0.navDataToShow);
  }
}
class MerchantNavbarComponent {
  constructor(router, userService, spinnerService, store, brazeTimerService, route) {
    this.router = router;
    this.userService = userService;
    this.spinnerService = spinnerService;
    this.store = store;
    this.brazeTimerService = brazeTimerService;
    this.route = route;
    this.isUniformCanteen = false;
    this.isMenuEditorRole = false;
    this.isEventManegementRole = false;
    this.refreshCount = 0;
    this.navDataToShow = [];
    this.navDataOptions = [{
      Name: 'Orders',
      Link: '/canteen/home'
    }, {
      Name: 'Reports',
      Link: '/canteen/reports'
    }, {
      Name: 'Students',
      Link: '/canteen/students'
    }, {
      Name: 'Menu Editor',
      Link: '/canteen/editor'
    }, {
      Name: 'Noticeboard',
      Link: '/canteen/notice'
    }, {
      Name: 'Events',
      Link: '/canteen/events'
    }, {
      Name: 'Settings',
      Link: '/canteen/settings'
    }, {
      Name: 'POS',
      Link: '/canteen/pos'
    }];
    this.routeName = this.router.url;
  }
  ngOnInit() {
    // get view type paramter from query params
    this.initializeFromRoute();
    this.spinnerService.stop();
    this.userService.IdentifyUser();
    this.subscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_5__.select)(_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__.canteenStateSelector)).subscribe(state => {
      this.isUniformCanteen = state?.selected && state.selected.CanteenType == _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Uniform;
      this.isMenuEditorRole = state?.selected && state.selected.IsMenuEditorAvailable;
      this.isEventManegementRole = state?.selected && state.selected.IsEventManagementAvailable;
      this.generateNavDataToShow();
    });
    this.router.events.subscribe(event => {
      if (event instanceof _angular_router__WEBPACK_IMPORTED_MODULE_6__.NavigationStart) {
        this.routeName = event.url;
      }
    });
    this.brazeTimerService.setUpBrazeInAppMessageTimer(_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Roles.Canteen);
  }
  initializeFromRoute() {
    this.route.queryParams.subscribe(params => {
      const viewParam = params['viewType'];
      this.viewType = viewParam === 'student' ? 'student' : 'merchant';
    });
  }
  generateNavDataToShow() {
    this.navDataToShow = [];
    this.navDataOptions.forEach(navOption => {
      if (navOption.Name === 'Menu Editor' && !this.isMenuEditorRole) {
        return;
      }
      if (navOption.Name === 'Events' && (!this.isEventManegementRole || this.isUniformCanteen)) {
        return;
      }
      if (navOption.Name === 'Reports') {
        navOption.Link = this.isUniformCanteen ? '/canteen/reports/uniformitems' : '/canteen/reports';
      }
      this.navDataToShow.push(navOption);
    });
  }
  ngOnDestroy() {
    this.subscription?.unsubscribe();
    this.brazeTimerService.unsubscribeFromBrazeTimer();
  }
  SignOut() {
    this.userService.logout();
  }
  static {
    this.ɵfac = function MerchantNavbarComponent_Factory(t) {
      return new (t || MerchantNavbarComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_5__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices_brazeTimer_service__WEBPACK_IMPORTED_MODULE_3__.BrazeTimerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: MerchantNavbarComponent,
      selectors: [["merchant-navbar"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 1,
      consts: [["color", "primary", "role", "heading", "class", "cashless-toolbar", 4, "ngIf"], ["color", "primary", "role", "heading", 1, "cashless-toolbar"], ["mat-icon-button", "", 1, "d-block", "d-lg-none", 3, "matMenuTriggerFor"], ["menuNav", "matMenu"], ["mat-menu-item", "", 3, "routerLink", 4, "ngFor", "ngForOf"], [1, "d-none", "d-lg-block"], [4, "ngFor", "ngForOf"], [1, "example-fill-remaining-space"], ["mat-menu-item", "", 3, "routerLink"], ["routerLinkActive", "activeLink", 3, "routerLink"], [1, "tab-selected"]],
      template: function MerchantNavbarComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](0, MerchantNavbarComponent_mat_toolbar_0_Template, 10, 3, "mat-toolbar", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.viewType === "merchant");
        }
      },
      dependencies: [_angular_material_toolbar__WEBPACK_IMPORTED_MODULE_7__.MatToolbarModule, _angular_material_toolbar__WEBPACK_IMPORTED_MODULE_7__.MatToolbar, _angular_material_button__WEBPACK_IMPORTED_MODULE_8__.MatButtonModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_8__.MatIconButton, _angular_material_menu__WEBPACK_IMPORTED_MODULE_9__.MatMenuModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_9__.MatMenu, _angular_material_menu__WEBPACK_IMPORTED_MODULE_9__.MatMenuItem, _angular_material_menu__WEBPACK_IMPORTED_MODULE_9__.MatMenuTrigger, _angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__.MatIconModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__.MatIcon, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterLinkActive],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.cashless-toolbar[_ngcontent-%COMP%] {\n  position: sticky;\n  position: -webkit-sticky; \n\n  top: 0; \n\n  z-index: 1000; \n\n  background-color: #ffffff;\n  color: #333b44;\n}\n\n.example-fill-remaining-space[_ngcontent-%COMP%] {\n  \n\n\n  flex: 1 1 auto;\n}\n\nul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: inline-block;\n  margin-right: 20px;\n  color: #333b44;\n  vertical-align: middle;\n  cursor: pointer;\n}\n\nul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  text-decoration: none;\n  font-family: \"bariol_bold\";\n  margin-right: 20px;\n  color: #333b44;\n}\n\n.activeLink[_ngcontent-%COMP%] {\n  border-bottom: 2px #000 solid;\n}\n\nimg[_ngcontent-%COMP%] {\n  vertical-align: middle;\n  height: 24px;\n  width: 24px;\n  margin-right: 10px;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvbWVyY2hhbnQtbmF2YmFyL21lcmNoYW50LW5hdmJhci5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFiQTtFQUNFLGdCQUFBO0VBQ0Esd0JBQUEsRUFBQSx5QkFBQTtFQUNBLE1BQUEsRUFBQSx5Q0FBQTtFQUNBLGFBQUEsRUFBQSwrREFBQTtFQUNBLHlCQUFBO0VBQ0EsY0NrQ087QURsQlQ7O0FBYkE7RUFDRTtxREFBQTtFQUVBLGNBQUE7QUFnQkY7O0FBYkE7RUFDRSxxQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0NzQk87RURyQlAsc0JBQUE7RUFDQSxlQUFBO0FBZ0JGOztBQWJBO0VBQ0UscUJBQUE7RUFDQSwwQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0NhTztBREdUOztBQWJBO0VBQ0UsNkJBQUE7QUFnQkY7O0FBYkE7RUFDRSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBZ0JGIiwic291cmNlc0NvbnRlbnQiOlsiJGJyZWFrcG9pbnQtc206IDU3NnB4O1xuJGJyZWFrcG9pbnQtbWQ6IDc2N3B4O1xuJGJyZWFrcG9pbnQtbGc6IDk5MnB4O1xuJGJyZWFrcG9pbnQteGw6IDEyMDBweDtcblxuLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWF4LXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4vLyBOT1RFIEN1cnJlbnRseSB0YWJsZXQgYW5kIG1vYmlsZSBpcyB0aGUgc2FtZS4gQ2hhbmdlIHRvICRicmVha3BvaW50LWxnIGxhdGVyIGlmIHdlIGdldCBhIHByb3BlciB0YWJsZXQgZGVzaWduLlxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuIiwiQGltcG9ydCAnLi4vLi4vLi4vLi4vc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MnO1xuQGltcG9ydCAnLi4vLi4vLi4vLi4vc3R5bGVzL3NjaG9vbHMtdGhlbWUuc2Nzcyc7XG5cbi5jYXNobGVzcy10b29sYmFyIHtcbiAgcG9zaXRpb246IHN0aWNreTtcbiAgcG9zaXRpb246IC13ZWJraXQtc3RpY2t5OyAvKiBGb3IgbWFjT1MvaU9TIFNhZmFyaSAqL1xuICB0b3A6IDA7IC8qIFNldHMgdGhlIHN0aWNreSB0b29sYmFyIHRvIGJlIG9uIHRvcCAqL1xuICB6LWluZGV4OiAxMDAwOyAvKiBFbnN1cmUgdGhhdCB5b3VyIGFwcCdzIGNvbnRlbnQgZG9lc24ndCBvdmVybGFwIHRoZSB0b29sYmFyICovXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XG4gIGNvbG9yOiAkZ3JleS01O1xufVxuXG4uZXhhbXBsZS1maWxsLXJlbWFpbmluZy1zcGFjZSB7XG4gIC8qIFRoaXMgZmlsbHMgdGhlIHJlbWFpbmluZyBzcGFjZSwgYnkgdXNpbmcgZmxleGJveC4gXG4gICAgICAgRXZlcnkgdG9vbGJhciByb3cgdXNlcyBhIGZsZXhib3ggcm93IGxheW91dC4gKi9cbiAgZmxleDogMSAxIGF1dG87XG59XG5cbnVsIGxpIHtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICBtYXJnaW4tcmlnaHQ6IDIwcHg7XG4gIGNvbG9yOiAkZ3JleS01O1xuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG5cbnVsIGxpIGEge1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIGZvbnQtZmFtaWx5OiAnYmFyaW9sX2JvbGQnO1xuICBtYXJnaW4tcmlnaHQ6IDIwcHg7XG4gIGNvbG9yOiAkZ3JleS01O1xufVxuXG4uYWN0aXZlTGluayB7XG4gIGJvcmRlci1ib3R0b206IDJweCAjMDAwIHNvbGlkO1xufVxuXG5pbWcge1xuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xuICBoZWlnaHQ6IDI0cHg7XG4gIHdpZHRoOiAyNHB4O1xuICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gIG1hcmdpbi10b3A6IGF1dG87XG4gIG1hcmdpbi1ib3R0b206IGF1dG87XG59XG4iLCJAaW1wb3J0ICdjYXNobGVzcy1icmVha3BvaW50cyc7XG5cbi8vIFByaW1hcnkgY29sb3Vyc1xuXG4kYmx1ZS0xOiAjMWVhM2NlO1xuJGJsdWUtMjogIzQ3NTRiMDtcbiRibHVlLTM6ICMxNDRjZGM7XG5cbiRyZWQtMTogI2YxNDc2MjtcbiRyZWQtMjogI2ZmZWJlYjtcbiRyZWQtMzogI2MwNDU0NTtcbiRyZWQtNDogI2ZmY2ZjYztcblxuJHB1cnBsZS0xOiAjN2YzZGIzO1xuJG5hdnktMTogIzFjNDI3MDtcbiRjaGFyY29hbC0xOiAjMzMzYjQ0O1xuXG4kZ3JlZW4tMTogIzAwYmE2YjtcbiRncmVlbi0yOiAjZDlmNWU5O1xuJGdyZWVuLTM6ICMwMDZmNDk7XG4kZ3JlZW4tNDogI2UzZjVlZjtcbiRncmVlbi01OiAjZGZmZmYwO1xuXG4vLyBPcmFuZ2VcbiRvcmFuZ2UtMTogI2ZmOWUwMDtcbiRvcmFuZ2UtMzogI2ZmN2EwMDtcbiRvcmFuZ2UtMjogI2ZmNGIxNztcbiRvcmFuZ2UtNDogI2ZmZTJjNztcbiRvcmFuZ2UtNTogI2ZmZjJlNjtcbiRvcmFuZ2UtNjogI2ZmODAwMDtcbiRvcmFuZ2UtNzogI2ZmZWFkNjtcbiRvcmFuZ2UtODogI2ZlZjBlMDtcbiRvcmFuZ2UtOTogI2ZmZjBlMDtcbiRvcmFuZ2UtMTA6ICNmMzY2MDA7XG4kb3JhbmdlLTExOiAjZmZlM2JiO1xuJG1vYmlsZS1kYXJrLW9yYW5nZTogI0Q5NUIwMDtcbiRtb2JpbGUtbGlnaHQtb3JhbmdlOiAjRkZFQUQ2O1xuXG4vLyBncmV5XG4kZ3JleS0xOiAjODg5NDlmO1xuJGdyZXktMjogI2UwZTFlMjtcbiRncmV5LTM6ICNkZGRkZGQ7XG4kZ3JleS00OiAjZjJmMmYyO1xuJGdyZXktNTogIzMzM2I0NDtcbiRncmV5LTY6ICNlNWU1ZTU7XG4kZ3JleS03OiAjYjliOWM4O1xuJGdyZXktODogIzg3ODc4NztcbiRncmV5LTk6ICNlMGUwZTA7XG4kZ3JleS0xMDogI2JkYmRiZDtcbiRncmV5LTExOiAjODI4MjgyO1xuJGdyZXktMTI6ICMxYjFmMzk7XG4kZ3JleS0xMzogI2I4YjhiODtcbiRncmV5LTE0OiAjMjcyYzUwO1xuJGdyZXktMTU6ICNmNmY1ZjM7XG4kZ3JleS0xNjogI2ZhZjlmODtcbiRncmV5LTE3OiAjNmI2Yzg5O1xuXG4vLyBTZWNvbmRhcnkgY29sb3Vyc1xuJGJsdWUtc2Vjb25kYXJ5LTE6IHJnYmEoMjU1LCAyNDMsIDIxOSwgMSk7XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 33133:
/*!*******************************************************!*\
  !*** ./node_modules/ngx-print/fesm2022/ngx-print.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NgxPrintDirective: () => (/* binding */ NgxPrintDirective),
/* harmony export */   NgxPrintModule: () => (/* binding */ NgxPrintModule),
/* harmony export */   NgxPrintService: () => (/* binding */ NgxPrintService),
/* harmony export */   PrintOptions: () => (/* binding */ PrintOptions)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);


class PrintBase {
  _printStyle = [];
  _styleSheetFile = '';
  //#region Getters and Setters
  /**
   * Sets the print styles based on the provided values.
   *
   * @param {Object} values - Key-value pairs representing print styles.
   * @protected
   */
  setPrintStyle(values) {
    this._printStyle = [];
    for (let key in values) {
      if (values.hasOwnProperty(key)) {
        this._printStyle.push((key + JSON.stringify(values[key])).replace(/['"]+/g, ''));
      }
    }
  }
  /**
   *
   *
   * @returns the string that create the stylesheet which will be injected
   * later within <style></style> tag.
   *
   * -join/replace to transform an array objects to css-styled string
   */
  returnStyleValues() {
    return `<style> ${this._printStyle.join(' ').replace(/,/g, ';')} </style>`;
  }
  /**
  * @returns string which contains the link tags containing the css which will
  * be injected later within <head></head> tag.
  *
  */
  returnStyleSheetLinkTags() {
    return this._styleSheetFile;
  }
  /**
   * Sets the style sheet file based on the provided CSS list.
   *
   * @param {string} cssList - CSS file or list of CSS files.
   * @protected
   */
  setStyleSheetFile(cssList) {
    let linkTagFn = function (cssFileName) {
      return `<link rel="stylesheet" type="text/css" href="${cssFileName}">`;
    };
    if (cssList.indexOf(',') !== -1) {
      const valueArr = cssList.split(',');
      this._styleSheetFile = valueArr.map(val => linkTagFn(val)).join('');
    } else {
      this._styleSheetFile = linkTagFn(cssList);
    }
  }
  //#endregion
  //#region Private methods used by PrintBase
  /**
   * Updates the default values for input elements.
   *
   * @param {HTMLCollectionOf<HTMLInputElement>} elements - Collection of input elements.
   * @private
   */
  updateInputDefaults(elements) {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      element['defaultValue'] = element.value;
      if (element['checked']) element['defaultChecked'] = true;
    }
  }
  /**
   * Updates the default values for select elements.
   *
   * @param {HTMLCollectionOf<HTMLSelectElement>} elements - Collection of select elements.
   * @private
   */
  updateSelectDefaults(elements) {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const selectedIdx = element.selectedIndex;
      const selectedOption = element.options[selectedIdx];
      selectedOption.defaultSelected = true;
    }
  }
  /**
   * Updates the default values for textarea elements.
   *
   * @param {HTMLCollectionOf<HTMLTextAreaElement>} elements - Collection of textarea elements.
   * @private
   */
  updateTextAreaDefaults(elements) {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      element['defaultValue'] = element.value;
    }
  }
  /**
   * Converts a canvas element to an image and returns its HTML string.
   *
   * @param {HTMLCanvasElement} element - The canvas element to convert.
   * @returns {string} - HTML string of the image.
   * @private
   */
  canvasToImageHtml(element) {
    const dataUrl = element.toDataURL();
    return `<img src="${dataUrl}" style="max-width: 100%;">`;
  }
  /**
   * Includes canvas contents in the print section via img tags.
   *
   * @param {HTMLCollectionOf<HTMLCanvasElement>} elements - Collection of canvas elements.
   * @private
   */
  updateCanvasToImage(elements) {
    for (let i = 0; i < elements.length; i++) {
      const element = this.canvasToImageHtml(elements[i]);
      elements[i].insertAdjacentHTML('afterend', element);
      elements[i].remove();
    }
  }
  /**
   * Retrieves the HTML content of a specified printing section.
   *
   * @param {string} printSectionId - Id of the printing section.
   * @returns {string | null} - HTML content of the printing section, or null if not found.
   * @private
   */
  getHtmlContents(printSectionId) {
    const printContents = document.getElementById(printSectionId);
    if (!printContents) return null;
    const inputEls = printContents.getElementsByTagName('input');
    const selectEls = printContents.getElementsByTagName('select');
    const textAreaEls = printContents.getElementsByTagName('textarea');
    const canvasEls = printContents.getElementsByTagName('canvas');
    this.updateInputDefaults(inputEls);
    this.updateSelectDefaults(selectEls);
    this.updateTextAreaDefaults(textAreaEls);
    this.updateCanvasToImage(canvasEls);
    return printContents.innerHTML;
  }
  /**
   * Retrieves the HTML content of elements with the specified tag.
   *
   * @param {keyof HTMLElementTagNameMap} tag - HTML tag name.
   * @returns {string} - Concatenated outerHTML of elements with the specified tag.
   * @private
   */
  getElementTag(tag) {
    const html = [];
    const elements = document.getElementsByTagName(tag);
    for (let index = 0; index < elements.length; index++) {
      html.push(elements[index].outerHTML);
    }
    return html.join('\r\n');
  }
  //#endregion
  /**
   * Prints the specified content using the provided print options.
   *
   * @param {PrintOptions} printOptions - Options for printing.
   * @public
   */
  print(printOptions) {
    let styles = '',
      links = '',
      popOut = 'top=0,left=0,height=auto,width=auto';
    const baseTag = this.getElementTag('base');
    if (printOptions.useExistingCss) {
      styles = this.getElementTag('style');
      links = this.getElementTag('link');
    }
    // If the openNewTab option is set to true, then set the popOut option to an empty string. 
    // This will cause the print dialog to open in a new tab.
    if (printOptions.openNewTab) {
      popOut = '';
    }
    const printContents = this.getHtmlContents(printOptions.printSectionId);
    if (!printContents) {
      // Handle the case where the specified print section is not found.
      console.error(`Print section with id ${printOptions.printSectionId} not found.`);
      return;
    }
    const popupWin = window.open("", "_blank", popOut);
    if (!popupWin) {
      // the popup window could not be opened.
      console.error('Could not open print window.');
      return;
    }
    popupWin.document.open();
    popupWin.document.write(`
          <html>
            <head>
              <title>${printOptions.printTitle ? printOptions.printTitle : ""}</title>
              ${baseTag}
              ${this.returnStyleValues()}
              ${this.returnStyleSheetLinkTags()}
              ${styles}
              ${links}
            </head>
            <body ${printOptions.bodyClass ? `class="${printOptions.bodyClass}"` : ''}>
              ${printContents}
              <script defer>
                function triggerPrint(event) {
                  window.removeEventListener('load', triggerPrint, false);
                  ${printOptions.previewOnly ? '' : `setTimeout(function() {
                    closeWindow(window.print());
                  }, ${printOptions.printDelay});`}
                }
                function closeWindow(){
                  ${printOptions.closeWindow ? 'window.close();' : ''}
                }
                window.addEventListener('load', triggerPrint, false);
              </script>
            </body>
          </html>`);
    popupWin.document.close();
  }
  static ɵfac = function PrintBase_Factory(t) {
    return new (t || PrintBase)();
  };
  static ɵprov = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjectable"]({
    token: PrintBase,
    factory: PrintBase.ɵfac,
    providedIn: 'root'
  });
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵsetClassMetadata"](PrintBase, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Injectable,
    args: [{
      providedIn: 'root'
    }]
  }], null, null);
})();

/**
 * Service for handling printing functionality in Angular applications.
 * Extends the base printing class (PrintBase).
 *
 * @export
 * @class NgxPrintService
 * @extends {PrintBase}
 */
class NgxPrintService extends PrintBase {
  /**
   * Initiates the printing process using the provided print options.
   *
   * @param {PrintOptions} printOptions - Options for configuring the printing process.
   * @memberof NgxPrintService
   * @returns {void}
   */
  print(printOptions) {
    // Call the print method in the parent class
    super.print(printOptions);
  }
  /**
   * Sets the print style for the printing process.
   *
   * @param {{ [key: string]: { [key: string]: string } }} values - A dictionary representing the print styles.
   * @memberof NgxPrintService
   * @setter
   */
  set printStyle(values) {
    super.setPrintStyle(values);
  }
  /**
   * Sets the stylesheet file for the printing process.
   *
   * @param {string} cssList - A string representing the path to the stylesheet file.
   * @memberof NgxPrintService
   * @setter
   */
  set styleSheetFile(cssList) {
    super.setStyleSheetFile(cssList);
  }
  static ɵfac = /* @__PURE__ */function () {
    let ɵNgxPrintService_BaseFactory;
    return function NgxPrintService_Factory(t) {
      return (ɵNgxPrintService_BaseFactory || (ɵNgxPrintService_BaseFactory = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetInheritedFactory"](NgxPrintService)))(t || NgxPrintService);
    };
  }();
  static ɵprov = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjectable"]({
    token: NgxPrintService,
    factory: NgxPrintService.ɵfac,
    providedIn: "root"
  });
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵsetClassMetadata"](NgxPrintService, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
class PrintOptions {
  printSectionId = null;
  printTitle = null;
  useExistingCss = false;
  bodyClass = '';
  openNewTab = false;
  previewOnly = false;
  closeWindow = true;
  printDelay = 0;
  constructor(options) {
    if (options) {
      Object.assign(this, options);
    }
  }
}
class NgxPrintDirective extends PrintBase {
  printOptions = new PrintOptions();
  /**
   * Prevents the print dialog from opening on the window
   *
   * @memberof NgxPrintDirective
   */
  set previewOnly(value) {
    this.printOptions = {
      ...this.printOptions,
      previewOnly: value
    };
  }
  /**
   *
   *
   * @memberof NgxPrintDirective
   */
  set printSectionId(value) {
    this.printOptions = {
      ...this.printOptions,
      printSectionId: value
    };
  }
  /**
   *
   *
   * @memberof NgxPrintDirective
   */
  set printTitle(value) {
    this.printOptions = {
      ...this.printOptions,
      printTitle: value
    };
  }
  /**
   *
   *
   * @memberof NgxPrintDirective
   */
  set useExistingCss(value) {
    this.printOptions = {
      ...this.printOptions,
      useExistingCss: value
    };
  }
  /**
   * A delay in milliseconds to force the print dialog to wait before opened. Default: 0
   *
   * @memberof NgxPrintDirective
   */
  set printDelay(value) {
    this.printOptions = {
      ...this.printOptions,
      printDelay: value
    };
  }
  /**
   * Whether to close the window after print() returns.
   *
   */
  set closeWindow(value) {
    this.printOptions = {
      ...this.printOptions,
      closeWindow: value
    };
  }
  /**
   * Class attribute to apply to the body element.
   *
   */
  set bodyClass(value) {
    this.printOptions = {
      ...this.printOptions,
      bodyClass: value
    };
  }
  /**
   * Whether to open a new window or default to new window.
   *
   */
  set openNewTab(value) {
    this.printOptions = {
      ...this.printOptions,
      openNewTab: value
    };
  }
  /**
   *
   *
   * @memberof NgxPrintDirective
   */
  set printStyle(values) {
    super.setPrintStyle(values);
  }
  /**
   * @memberof NgxPrintDirective
   * @param cssList
   */
  set styleSheetFile(cssList) {
    super.setStyleSheetFile(cssList);
  }
  /**
   *
   *
   * @memberof NgxPrintDirective
   */
  print() {
    super.print(this.printOptions);
  }
  static ɵfac = /* @__PURE__ */function () {
    let ɵNgxPrintDirective_BaseFactory;
    return function NgxPrintDirective_Factory(t) {
      return (ɵNgxPrintDirective_BaseFactory || (ɵNgxPrintDirective_BaseFactory = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetInheritedFactory"](NgxPrintDirective)))(t || NgxPrintDirective);
    };
  }();
  static ɵdir = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineDirective"]({
    type: NgxPrintDirective,
    selectors: [["button", "ngxPrint", ""]],
    hostBindings: function NgxPrintDirective_HostBindings(rf, ctx) {
      if (rf & 1) {
        _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function NgxPrintDirective_click_HostBindingHandler() {
          return ctx.print();
        });
      }
    },
    inputs: {
      previewOnly: "previewOnly",
      printSectionId: "printSectionId",
      printTitle: "printTitle",
      useExistingCss: "useExistingCss",
      printDelay: "printDelay",
      closeWindow: "closeWindow",
      bodyClass: "bodyClass",
      openNewTab: "openNewTab",
      printStyle: "printStyle",
      styleSheetFile: "styleSheetFile"
    },
    standalone: true,
    features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵInheritDefinitionFeature"]]
  });
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵsetClassMetadata"](NgxPrintDirective, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Directive,
    args: [{
      selector: "button[ngxPrint]",
      standalone: true
    }]
  }], null, {
    previewOnly: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    printSectionId: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    printTitle: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    useExistingCss: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    printDelay: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    closeWindow: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    bodyClass: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    openNewTab: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    printStyle: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    styleSheetFile: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.Input
    }],
    print: [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.HostListener,
      args: ['click']
    }]
  });
})();
class NgxPrintModule {
  static ɵfac = function NgxPrintModule_Factory(t) {
    return new (t || NgxPrintModule)();
  };
  static ɵmod = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineNgModule"]({
    type: NgxPrintModule
  });
  static ɵinj = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjector"]({});
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵsetClassMetadata"](NgxPrintModule, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_0__.NgModule,
    args: [{
      imports: [NgxPrintDirective],
      exports: [NgxPrintDirective]
    }]
  }], null, null);
})();

/*
 * Public API Surface of ngx-print
 */

/**
 * Generated bundle index. Do not edit.
 */



/***/ })

}]);
//# sourceMappingURL=src_app_canteen_canteen_module_ts.js.map