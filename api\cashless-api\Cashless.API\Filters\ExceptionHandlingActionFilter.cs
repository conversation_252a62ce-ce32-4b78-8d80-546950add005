// using Cashless.APIs.Extensions;
// using Schools.BLL.Extensions;
// using Schools.BLL.Services;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.AspNetCore.Mvc.Filters;
// using Microsoft.Extensions.Logging;
// using Schools.BLL.Services.Interfaces;

// namespace Cashless.APIs.Filters;

// /// <summary>
// /// Custom middleware to handle exceptions. This is implmented as a 
// /// filter and thus care must be taken as to where it is added in the
// /// request chain. It needs to be added to the startup class as per: 
// /// https://docs.microsoft.com/en-us/aspnet/core/web-api/handle-errors?view=aspnetcore-6.0#use-exceptions-to-modify-the-response
// /// </summary>
// public class ExceptionHandlingActionFilter : IActionFilter, IOrderedFilter
// {
//     private readonly ITelemetryService telemetryService;
//     private readonly ILogger<ExceptionHandlingActionFilter> logger;

//     public ExceptionHandlingActionFilter(ITelemetryService telemetryService, ILogger<ExceptionHandlingActionFilter> logger)
//     {
//         this.telemetryService = telemetryService;
//         this.logger = logger;
//     }

//     /// <summary>
//     /// Ensure this filter runs first and that other filters can run after it
//     /// </summary>
//     public int Order => int.MaxValue - 10;

//     public void OnActionExecuting(ActionExecutingContext context) { }

//     /// <summary>
//     /// Handle an exception in the request chain and return a HTTP 500 response
//     /// </summary>
//     public void OnActionExecuted(ActionExecutedContext context)
//     {
//         if (context.Exception == null)
//         {
//             return;
//         }

//         var httpContext = context.HttpContext;
//         var HttpRequest = httpContext.Request;
//         var exception = context.Exception;
//         var userId = httpContext.User?.Identity.GetId();
//         var ip = httpContext.GetRequestIp();
//         var method = httpContext.Request.Method;
//         var path = httpContext.Request.Path;
//         var queryString = httpContext.Request.QueryString;
//         var controller = context.RouteData.Values["controller"];
//         var action = context.RouteData.Values["action"];

//         logger.LogError("Error {Method} {Path} - User: {UserId} - IP: {IP} - Controller: {Controller} - Action: {Action} - Exception: {Exception}",
//                         method, path + queryString, userId, ip, controller, action, exception);

//         // Log exception in Application Insights
//         if (exception != null)
//         {
//             telemetryService.TrackException(exception);
//         }

//         // Create a 500 response to return
//         context.Result = new StatusCodeResult(500);

//         // Stop the exception handling
//         context.ExceptionHandled = true;
//     }
// }
