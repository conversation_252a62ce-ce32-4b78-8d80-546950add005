using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface IStudentValidator
{
    Task ValidateAccessToParent(int parentId);
    Task ValidateAccessToStudent(int studentId);
}

/// <summary>
/// Validator to check various User API calls
/// </summary>
public class StudentValidator : IStudentValidator
{
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<StudentValidator> _logger;

    public StudentValidator(IAuthenticationValidator authenticationValidator, ILogger<StudentValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _logger = logger;
    }

    public async Task ValidateAccessToParent(int parentId)
    {
        await _authenticationValidator.ValidateAccessForCurrentUser(parentId);
    }

    public async Task ValidateAccessToStudent(int studentId)
    {
        await _authenticationValidator.ValidateAccessToStudent(studentId);
    }
}