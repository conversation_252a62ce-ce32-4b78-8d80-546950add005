{"version": "2.0.0", "tasks": [{"label": "build-all", "command": "dotnet", "type": "process", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$tsc"}, {"label": "build-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Cashless.API/Cashless.APIs.csproj"], "problemMatcher": "$tsc"}, {"label": "publish-api", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Cashless.API/Cashless.APIs.csproj"], "problemMatcher": "$tsc"}, {"label": "watch-api", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/Cashless.API/Cashless.APIs.csproj"], "problemMatcher": "$tsc"}, {"label": "build-batch", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Schools.Batch/Schools.Batch.csproj"], "problemMatcher": "$tsc"}, {"label": "publish-batch", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Schools.Batch/Schools.Batch.csproj"], "problemMatcher": "$tsc"}, {"label": "watch-batch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/Schools.Batch/Schools.Batch.csproj"], "problemMatcher": "$tsc"}, {"label": "build-fees", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Fees.API/Fees.API.csproj"], "problemMatcher": "$tsc"}, {"label": "publish-fees", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Fees.API/Fees.API.csproj"], "problemMatcher": "$tsc"}, {"label": "watch-fees", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/Fees.API/Fees.API.csproj"], "problemMatcher": "$tsc"}, {"label": "clean-functions", "command": "dotnet", "args": ["clean", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}/Schools.Orders"}}, {"label": "build-functions", "command": "dotnet", "args": ["build", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "dependsOn": "clean-functions", "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}/Schools.Orders"}}, {"label": "clean-release-functions", "command": "dotnet", "args": ["clean", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}/Schools.Orders"}}, {"label": "publish-functions", "command": "dotnet", "args": ["publish", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "type": "process", "dependsOn": "clean-release-functions", "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}/Schools.Orders"}}, {"label": "run-functions", "type": "func", "dependsOn": "build-functions", "options": {"cwd": "${workspaceFolder}/Schools.Orders/bin/Debug/net8.0"}, "command": "host start", "isBackground": true, "problemMatcher": "$func-dotnet-watch"}]}