using System.Threading.Tasks;
using Schools.BLL.Extensions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Cashless.APIs.Attributes;

/// <summary>
/// Action filter attribute to log request body
/// </summary>
public class LogRequestBodyAttribute : ActionFilterAttribute
{
    /// <summary>
    /// Log the body of the request if one was found
    /// </summary>
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var logger = context.HttpContext.RequestServices.GetService<ILogger<LogRequestBodyAttribute>>();
        var body = await context.HttpContext.Request.GetRawBodyAsync();

        logger.LogDebug("Processing request with payload: {Body}", body);

        await base.OnActionExecutionAsync(context, next);
    }
}
