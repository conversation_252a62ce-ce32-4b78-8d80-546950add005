"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_Push_unregister-push_js"],{

/***/ 44979:
/*!*****************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Push/unregister-push.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   unregisterPush: () => (/* binding */ unregisterPush)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _push_manager_factory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./push-manager-factory.js */ 80297);


function unregisterPush(r, n) {
  if (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return _push_manager_factory_js__WEBPACK_IMPORTED_MODULE_1__["default"].m().unsubscribe(r, n);
}

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_Push_unregister-push_js.js.map