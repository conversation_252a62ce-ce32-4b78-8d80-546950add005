using System.Threading.Tasks;
using Schools.BLL.Classes.Payments;

namespace Schools.BLL.Services.Interfaces;

/// <summary>
/// Service interface for handling guest user payments
/// </summary>
public interface IGuestPaymentService
{
    /// <summary>
    /// Process a guest payment with card details
    /// </summary>
    /// <param name="request">Guest payment request</param>
    /// <returns>Payment response</returns>
    Task<GuestPaymentResponse> ProcessGuestPayment(GuestPaymentRequest request);

    /// <summary>
    /// Validate guest card details
    /// </summary>
    /// <param name="request">Card validation request</param>
    /// <returns>Validation response</returns>
    Task<GuestCardValidationResponse> ValidateGuestCard(GuestCardValidationRequest request);

    /// <summary>
    /// Get payment status for a guest order
    /// </summary>
    /// <param name="request">Payment status request</param>
    /// <returns>Payment status response</returns>
    Task<GuestPaymentStatusResponse> GetGuestPaymentStatus(GuestPaymentStatusRequest request);

    /// <summary>
    /// Validate guest user permissions for payment
    /// </summary>
    /// <param name="guestUserId">Guest user ID</param>
    /// <param name="canteenId">Canteen ID</param>
    /// <returns>True if guest can make payments</returns>
    Task<bool> ValidateGuestPermissions(int guestUserId, int canteenId);
}
