﻿using Newtonsoft.Json;

namespace Schools.BLL.Classes.Menus
{
    public class FoodBreakSettingsUpdateRequest
    {
        [JsonProperty(PropertyName = "MenuId")]
        public int? MenuId { get; set; }

        [JsonProperty(PropertyName = "FriendlyName")]
        public string FriendlyName { get; set; }

        [JsonProperty(PropertyName = "CutOffTime")]
        public string CutOffTime { get; set; }

        [JsonProperty(PropertyName = "BreakTime")]
        public string BreakTime { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public string IsActive { get; set; }
    }
}
