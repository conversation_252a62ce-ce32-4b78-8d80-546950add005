// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using Schools.BLL.Classes.Orders.Events;
// using Schools.BLL.Exceptions;
// using Microsoft.Extensions.Logging;
// using IStockUpdateService = Cashless.Common.Interfaces.IOrderService;

// namespace Schools.Orders.Services;

// public class StockService : IStocksService
// {
//     private readonly ILogger<StockService> _logger;
//     private readonly IStockUpdateService _stockUpdateService;

//     public StockService(ILogger<StockService> logger, IStockUpdateService stockUpdateService)
//     {
//         _logger = logger;
//         _stockUpdateService = stockUpdateService;
//     }

//     public async Task OrderUpdateStocks(IEnumerable<OrderPublishInfo> orders)
//     {
//         if (orders == null || !orders.Any())
//         {
//             return;
//         }

//         foreach (var o in orders)
//         {
//             if (o == null || o.OrderId < 1)
//             {
//                 throw new OrderFunctionException("Invalid order id");
//             }

//             await _stockUpdateService.UpdateStocks(o.OrderId);
//         } 
//     }
// }