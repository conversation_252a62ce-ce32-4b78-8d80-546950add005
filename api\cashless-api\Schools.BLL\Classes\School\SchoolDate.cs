﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class SchoolDate : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "SchoolDateId")]
        public long SchoolDateId { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "StartDate")]
        public DateTime StartDate { get; set; }

        [JsonProperty(PropertyName = "EndDate")]
        public DateTime EndDate { get; set; }
    }
}
