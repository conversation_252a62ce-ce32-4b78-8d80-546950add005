using System.Text;
using System.Text.Json;

namespace Schools.Orders.Common;

public static class Util
{
    public static byte[] GetBytesFromMessageRequest(string message)
    {
        var sbMessage = JsonSerializer.Deserialize<MessageEnvelope>(message, new JsonSerializerOptions() { PropertyNameCaseInsensitive = true });
        return Encoding.UTF8.GetBytes(sbMessage.message.ToJsonString());
    }
}