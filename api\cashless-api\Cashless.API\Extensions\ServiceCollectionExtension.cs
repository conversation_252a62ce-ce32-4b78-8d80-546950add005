using System;
using System.Net.Http;
using Schools.BLL.Services;
using Schools.BLL.Services.Calculators;
using Cashless.APIs.Validators;
using Cashless.APIs.Validators.Fees;
using Schools.BLL.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using Polly.Bulkhead;
using Polly.Contrib.WaitAndRetry;
using Polly.Extensions.Http;
// using INotifyService = Schools.Orders.Services.INotifyService;
// using NotifyService = Schools.Orders.Services.NotifyService;
using Schools.BLL.Validators;

namespace Cashless.APIs.Extensions;

public static class ServiceCollectionExtension
{
        /// <summary>
        /// How long HttpMessageHandler objects will live for in the pool of 
        /// HttpMessageHandler objects (in minutes)
        /// 
        /// https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#httpclient-lifetimes
        /// </summary>
        public const int HttpMessageHandlerLifetime = 60;

        /// <summary>
        /// Use Polly to create a policy to retry HTTP requests after a transient 
        /// failure (HTTP Status = 408 or >= 500)
        /// 
        /// NOTE - We will try this on read transactions for the time being
        /// 
        /// https://learn.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/implement-http-call-retries-exponential-backoff-polly
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int medianFirstRetryDelay, int retryCount)
        {
                // Add jitter to the retry intervals to avoid drowning
                // downstream systems with retries
                var delay = Backoff.DecorrelatedJitterBackoffV2(
                                        medianFirstRetryDelay: TimeSpan.FromSeconds(medianFirstRetryDelay),
                                        retryCount: retryCount);

                return HttpPolicyExtensions
                        .HandleTransientHttpError()
                        .WaitAndRetryAsync(delay);
        }

        /// <summary>
        /// Use bulkhead to reduce the number of concurrent requests being sent
        /// to a given API. This throws an exception once the queue of requests
        /// is full
        /// </summary>
        public static AsyncBulkheadPolicy<HttpResponseMessage> GetBulkheadPolicy(int capacity, int queueLength)
        {
                // TODO - Add a Lambda function to respond once queue is full
                return Policy.BulkheadAsync<HttpResponseMessage>(capacity, queueLength);
        }

        /// <summary>
        /// Register the various services with the DI container
        /// </summary>
        public static IServiceCollection AddApiServices(this IServiceCollection services)
        {
                // Register named HttpClient for Spriggy Core API and other APIs and include any 
                // common headers and other policies by passing a configuration Action
                //
                // https://docs.microsoft.com/en-us/aspnet/core/fundamentals/http-requests?view=aspnetcore-6.0#named-clients
                services.AddHttpClient(ScoreApiClient.HttpClientName, ScoreApiClient.ConfigureHttpClient)
                        .SetHandlerLifetime(TimeSpan.FromMinutes(HttpMessageHandlerLifetime));
                services.AddHttpClient(LogicAppService.HttpClientName)
                        .SetHandlerLifetime(TimeSpan.FromMinutes(HttpMessageHandlerLifetime));

                // Combine retry and bulkhead policies
                var policy = Policy.WrapAsync(GetRetryPolicy(ScoreApiClient.MedianFirstRetryDelay, ScoreApiClient.RetryCount),
                                        GetBulkheadPolicy(ScoreApiClient.Capacity, ScoreApiClient.QueueLength));
                services.AddHttpClient(ScoreApiClient.HttpClientNameWithRetryPolicy, ScoreApiClient.ConfigureHttpClient)
                        .SetHandlerLifetime(TimeSpan.FromMinutes(HttpMessageHandlerLifetime))
                        .AddPolicyHandler(policy);

                // Register different services
                // services.AddScoped<ISecurityService, SecurityService>();
                // services.AddScoped<IExceptionHandlerService, ExceptionHandlerService>();
                // services.AddScoped<IUserService, UserService>();
                // services.AddTransient<INotifyService, NotifyService>();
                // services.AddTransient<IPaymentService, PaymentService>();
                // services.AddTransient<IPaymentService2, PaymentService2>();
                // services.AddTransient<IOrderService, OrderService>();
                // services.AddTransient<Schools.Orders.Services.IOrderService, Schools.Orders.Services.OrderService>();
                // services.AddTransient<IOrderStatusService, OrderStatusService>();
                // services.AddTransient<IBillingService, BillingService>();
                // services.AddTransient<ISchoolService, SchoolService>();
                // services.AddTransient<IStocksService, StocksService>();
                // services.AddTransient<Schools.Orders.Services.IStocksService, Schools.Orders.Services.StockService>();
                // services.AddTransient<ICanteenService, CanteenService>();
                // services.AddTransient<ICategoryService, CategoryService>();
                // services.AddTransient<ISchoolClassService, SchoolClassService>();
                // services.AddTransient<IEditorService, EditorService>();
                // services.AddTransient<IMenuService, MenuService>();
                // services.AddTransient<INotificationService, NotificationService>();
                // services.AddTransient<ISchoolEventService, EventService>();
                // services.AddTransient<INoticeService, NoticeService>();
                // services.AddTransient<IPrintingService, PrintingService>();
                // services.AddTransient<IFirebaseService, FirebaseService>();
                // services.AddTransient<IXeroService, XeroService>();
                // services.AddTransient<IFileStorageService, AWSS3StorageService>();
                // services.AddScoped<ISendGridService, SendGridService>();
                // services.AddTransient<IAddressAssembler, AddressAssembler>();
                // services.AddTransient<IAddressService, AddressService>();
                // services.AddScoped<ISendGridService, SendGridService>();
                // services.AddScoped<ILogicAppService, LogicAppService>();
                // services.AddScoped<IAppVersionService, AppVersionService>();
                // services.AddScoped<ITelemetryService, TelemetryService>();
                // services.AddScoped<IScoreApiClient, ScoreApiClient>();
                // services.AddScoped<IStripeService, StripeService>();
                // services.AddScoped<ISchoolMerchantService, SchoolMerchantService>();
                // services.AddScoped<ISchoolFeatureService, SchoolFeatureService>();
                // services.AddTransient<ISchoolFeatureAssembler, SchoolFeatureAssembler>();
                // services.AddTransient<IAuditService, AuditService>();
                // services.AddTransient<IAuditCommonService, AuditCommonService>();
                // services.AddTransient<ICurrentUserHelper, CurrentUserHelper>();
                // services.AddTransient<ITransactionHistoryService, TransactionHistoryService>();
                // services.AddScoped<ISupportedVersionsService, SupportedVersionsService>();
                // services.AddScoped<IPaymentResultProcessor, PaymentResultProcessor>();
                // services.AddScoped<IReconciliationService, ReconciliationService>();
                // services.AddScoped<IFeatureFlagService, FeatureFlagService>();
                // services.AddScoped<IInvoiceService, InvoiceService>();

                // Validation factory depending on context
                // services.AddScoped<SchoolEventValidatorFactory>();
                // services.AddTransient<SchoolEventAdminValidator>()
                //         .AddScoped<ISchoolEventServiceValidator, SchoolEventAdminValidator>(v => v.GetService<SchoolEventAdminValidator>());
                // services.AddTransient<SchoolEventMerchantValidator>()
                //         .AddScoped<ISchoolEventServiceValidator, SchoolEventMerchantValidator>(v => v.GetService<SchoolEventMerchantValidator>());

                // Validators as services
                // services.AddScoped<IAuthenticationValidator, AuthenticationValidator>();
                services.AddScoped<IReconciliationValidator, ReconciliationValidator>();
                services.AddScoped<IBillingValidator, BillingValidator>();
                // services.AddScoped<IEditorValidator, EditorValidator>();
                services.AddScoped<IMenuValidator, MenuValidator>();
                services.AddScoped<IMerchantValidator, MerchantValidator>();
                // services.AddScoped<INoticeValidator, NoticeValidator>();

                // services.AddScoped<IOrderServiceValidator, OrderServiceValidator>();
                // services.AddScoped<IPaymentValidator, PaymentValidator>();
                services.AddScoped<IPrintingValidator, PrintingValidator>();
                services.AddScoped<ISchoolClassValidator, SchoolClassValidator>();
                services.AddScoped<ISchoolEventValidator, SchoolEventValidator>();
                services.AddScoped<ISchoolValidator, SchoolValidator>();
                services.AddScoped<IStudentValidator, StudentValidator>();
                services.AddScoped<IUserValidator, UserValidator>();

                return services;
        }
}