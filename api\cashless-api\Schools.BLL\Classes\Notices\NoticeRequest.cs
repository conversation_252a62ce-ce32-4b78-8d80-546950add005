﻿using System;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Notices
{
    public class NoticeRequest
    {
        public long NoticeId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public NoticeTypes NoticeType { get; set; }
        public string Status { get; set; }
        public string ValidationDescription { get; set; }
        public long SchoolId { get; set; }
        public long MerchantId { get; set; }
        public int UserId { get; set; }
    }
}
