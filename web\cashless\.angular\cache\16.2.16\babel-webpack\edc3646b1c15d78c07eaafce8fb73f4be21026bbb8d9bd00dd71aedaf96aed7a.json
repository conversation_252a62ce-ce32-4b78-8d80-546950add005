{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as _ from 'lodash';\nimport { ClearDayDetail } from 'src/app/states/family/family.actions';\nimport { clearAll } from 'src/app/states/shoppingCart/shopping-cart.actions';\n// Models\nimport { BaseComponent } from 'src/app/sharedModels';\n// Utilities\nimport { formatDateToUniversal } from 'src/app/utility';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@ngrx/store\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/sharedServices\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../services/pos-communication.service\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/expansion\";\nimport * as i12 from \"../pos-orders-placed/pos-orders-placed.component\";\nfunction PosPlaceOrderDialogComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"mat-spinner\", 8);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Calculating order total...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_mat_accordion_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const order_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", order_r12.studentName, \" - \", order_r12.menuFriendlyName, \" - \", i0.ɵɵpipeBind2(2, 4, order_r12.orderDate, \"EE dd/LL\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 7, order_r12.price));\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fee_r14 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Order Fee (\", fee_r14.name, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", i0.ɵɵpipeBind1(4, 2, fee_r14.fee), \"\");\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 25)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\", 26);\n    i0.ɵɵtext(4, \"Order Fees: (\");\n    i0.ɵɵelementStart(5, \"span\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \")\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 27);\n    i0.ɵɵtemplate(10, PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_div_10_Template, 5, 4, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"expanded\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 3, ctx_r11.totalFees));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.feesToDisplay);\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_mat_accordion_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-accordion\")(1, \"mat-expansion-panel\", 25)(2, \"mat-expansion-panel-header\")(3, \"mat-panel-title\")(4, \"span\", 26);\n    i0.ɵɵtext(5, \"Orders: (\");\n    i0.ɵɵelementStart(6, \"span\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \")\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 27);\n    i0.ɵɵtemplate(11, PosPlaceOrderDialogComponent_div_7_mat_accordion_3_div_11_Template, 6, 9, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_Template, 11, 5, \"mat-expansion-panel\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"expanded\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 4, ctx_r4.createOrderSummary.totalAmount));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.createOrderSummary.createOrdersInfo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalFees > 0);\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵtext(2, \"Previous order: \");\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtext(7, \"New order: \");\n    i0.ɵɵelementStart(8, \"span\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 34);\n    i0.ɵɵtext(12, \"Difference: \");\n    i0.ɵɵelementStart(13, \"span\", 18);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, ctx_r5.editOrderSummary.previousPrice));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 5, ctx_r5.editOrderSummary.price));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 7, ctx_r5.editOrderSummary.priceDiff));\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"mat-error\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.errorMessage);\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 36);\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.getConfirmButtonText());\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Processing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h2\", 10);\n    i0.ɵɵtext(2, \"Order Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PosPlaceOrderDialogComponent_div_7_mat_accordion_3_Template, 13, 6, \"mat-accordion\", 11);\n    i0.ɵɵtemplate(4, PosPlaceOrderDialogComponent_div_7_div_4_Template, 16, 9, \"div\", 12);\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"span\", 14);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 15);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 16)(12, \"div\", 17);\n    i0.ɵɵtext(13, \" Wallet Balance: \");\n    i0.ɵɵelementStart(14, \"span\", 18);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19);\n    i0.ɵɵtext(18, \" We'll deduct the total orders amount from your wallet balance. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, PosPlaceOrderDialogComponent_div_7_div_19_Template, 3, 1, \"div\", 20);\n    i0.ɵɵelementStart(20, \"div\", 21)(21, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function PosPlaceOrderDialogComponent_div_7_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.closeDialog());\n    });\n    i0.ɵɵtext(22, \" Go back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PosPlaceOrderDialogComponent_div_7_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.confirmOrder());\n    });\n    i0.ɵɵtemplate(24, PosPlaceOrderDialogComponent_div_7_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 24);\n    i0.ɵɵtemplate(25, PosPlaceOrderDialogComponent_div_7_span_25_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(26, PosPlaceOrderDialogComponent_div_7_span_26_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.createOrderSummary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editOrderSummary);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getTextTotalOrder(), \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 10, ctx_r1.totalPrice));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 12, ctx_r1.accountBalance));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.canteenOrAdminInsufficientWalletBalance || ctx_r1.buttonLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.buttonLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.buttonLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.buttonLoading);\n  }\n}\nfunction PosPlaceOrderDialogComponent_pos_orders_placed_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"pos-orders-placed\", 37);\n    i0.ɵɵlistener(\"goToOrders\", function PosPlaceOrderDialogComponent_pos_orders_placed_8_Template_pos_orders_placed_goToOrders_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.GotToOrders());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosPlaceOrderDialogComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"h2\", 10);\n    i0.ɵɵtext(2, \"Processing your order...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7);\n    i0.ɵɵelement(4, \"mat-spinner\", 8);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please wait while we process your order...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class PosPlaceOrderDialogComponent extends BaseComponent {\n  constructor(dialogRef, data, dialog, store, router, orderApiService, userService, location, payService, adminService, appInsightsService, posCommunicationService) {\n    super();\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.dialog = dialog;\n    this.store = store;\n    this.router = router;\n    this.orderApiService = orderApiService;\n    this.userService = userService;\n    this.location = location;\n    this.payService = payService;\n    this.adminService = adminService;\n    this.appInsightsService = appInsightsService;\n    this.posCommunicationService = posCommunicationService;\n    this.errorMessage = null;\n    this.orderPlaced = false;\n    this.isProcessing = false;\n    this.isTopUp = false;\n    this.sufficientWalletBalance = false;\n    this.canteenOrAdminInsufficientWalletBalance = false;\n    this.buttonLoading = false;\n    this.totalFees = 0;\n    this.createOrderSummary = null;\n    this.editOrderSummary = null;\n    this.feesToDisplay = [];\n    this.summaryLoading = false;\n    // POS-specific properties\n    this.paymentMethod = 'spriggy';\n    this.isPosOrder = true;\n    this.placedOrderId = null;\n    this.viewType = 'merchant';\n    // Error Messages\n    this.insufficientFundsError = 'Sorry, this order cannot be completed due to insufficient funds in the user wallet.';\n    this.cannotRetrieveFundsError = 'We are having an issue retrieving your balance, please contact support team.';\n    this.outOfStockError = 'Sorry, one or more items in your order are out of stock and could not be processed.';\n    this.guestUserOrderError = 'Guest users should use the card payment option. Please close this dialog and use \"Pay with Card\".';\n  }\n  ngOnInit() {\n    this.initializeComponent();\n  }\n  ngOnDestroy() {\n    this.subscriptionBalance$?.unsubscribe();\n  }\n  initializeComponent() {\n    // Initialize from dialog data\n    this.orders = this.data.groupedCarts;\n    this.editOrderId = this.data.editOrderId;\n    this.paymentMethod = 'spriggy'; // Only Spriggy payment is supported\n    this.viewType = this.data.viewType || 'merchant';\n    this.guid = this.data.guid;\n    this.selectedStudent = this.data.selectedStudent;\n    this.selectedMenuType = this.data.selectedMenuType;\n    this.selectedOrderDate = this.data.selectedOrderDate;\n    // Validate POS-specific requirements\n    if (!this.validatePosRequirements()) {\n      return;\n    }\n    // Get order summary\n    if (this.editOrderId) {\n      this.getEditOrderSummaryAPI();\n    } else {\n      this.getOrderSummaryAPI();\n    }\n    // Get balance using the same logic as the original canteen system\n    this.getUpdatedWalletBalance();\n  }\n  getUpdatedWalletBalance() {\n    if (this.userService.IsCanteenOrAdmin()) {\n      this.adminOrMerchantGetParentBalance();\n      return;\n    }\n    this.getParentBalance();\n  }\n  adminOrMerchantGetParentBalance() {\n    const parent = this.adminService.GetParent();\n    this.accountBalance = +parent.SpriggyBalance;\n  }\n  validatePosRequirements() {\n    if (!this.selectedStudent) {\n      this.errorMessage = 'Please select a student before placing an order.';\n      return false;\n    }\n    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {\n      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';\n      return false;\n    }\n    // Guest users are now allowed in POS but will use different payment flow\n    // No need to block them here - payment method validation happens later\n    return true;\n  }\n  isGuestUser() {\n    return this.selectedStudent?.IsGuest === true;\n  }\n  getParentBalance() {\n    this.payService.UpdateBalance();\n    this.subscriptionBalance$ = this.payService.SubscribeBalanceUpdate().subscribe({\n      next: response => {\n        this.accountBalance = response;\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.isProcessing = false;\n      }\n    });\n  }\n  GetEditSummaryRequest(carts) {\n    const orderData = carts.map(cartItem => {\n      return this.getOrderItemSummary(cartItem);\n    });\n    return {\n      OrderId: this.editOrderId,\n      Items: orderData[0]\n    };\n  }\n  getOrderSummaryRequest(carts) {\n    const orderData = carts.map(cartItem => {\n      const orderSummary = {\n        OrderId: 0,\n        StudentId: cartItem[0].studentId,\n        OrderDate: formatDateToUniversal(cartItem[0].date),\n        MenuId: cartItem[0].menuId,\n        Items: this.getOrderItemSummary(cartItem)\n      };\n      console.log(`[POS] Creating order summary with MenuId: ${orderSummary.MenuId} for student: ${orderSummary.StudentId}`);\n      return orderSummary;\n    });\n    const request = {\n      Orders: orderData\n    };\n    console.log(`[POS] Order summary request:`, request);\n    return request;\n  }\n  getOrderItemSummary(order) {\n    return order.map(orderItem => ({\n      MenuItemId: orderItem.menuItemId,\n      MenuItemOptionIds: this.getOrderItemOptionsSummary(orderItem.selectedOptions),\n      Quantity: orderItem.quantity\n    }));\n  }\n  getOrderItemOptionsSummary(selectedOptions) {\n    if (selectedOptions?.length === 0) {\n      return [];\n    }\n    return selectedOptions.map(option => option.menuItemOptionId);\n  }\n  getSelectedOptionIds(selectedOptions) {\n    return selectedOptions.map(option => option.menuItemOptionId);\n  }\n  getOrderSummaryAPI() {\n    this.summaryLoading = true;\n    const request = this.getOrderSummaryRequest(this.orders);\n    this.orderApiService.getOrderSummary(request).subscribe({\n      next: res => {\n        this.createOrderSummary = res;\n        this.totalFees = this.createOrderSummary.createOrdersInfo.reduce((prev, next) => prev + next.fee, 0);\n        this.totalPrice = this.createOrderSummary.totalAmount + this.totalFees;\n        // Populate fees to display\n        this.feesToDisplay = this.groupFeesByStudent(this.createOrderSummary.createOrdersInfo);\n        this.summaryLoading = false;\n        this.confirmSufficientUserBalance();\n      },\n      error: error => {\n        this.handleOrderSummaryApiError(error);\n      }\n    });\n  }\n  getEditOrderSummaryAPI() {\n    this.summaryLoading = true;\n    const request = this.GetEditSummaryRequest(this.orders);\n    this.orderApiService.getEditOrderSummary(request).subscribe({\n      next: res => {\n        this.editOrderSummary = res;\n        this.summaryLoading = false;\n        this.totalPrice = this.editOrderSummary.price;\n      },\n      error: error => {\n        this.handleOrderSummaryApiError(error);\n      }\n    });\n  }\n  /**\r\n   * For by Admin/Canteen creating orders\r\n   * Check if user balance is enough to complete payment after order fee is added\r\n   */\n  confirmSufficientUserBalance() {\n    if (!this.userService.IsCanteenOrAdmin() || this.editOrderId) {\n      return;\n    }\n    this.canteenOrAdminInsufficientWalletBalance = this.accountBalance < this.totalPrice;\n    this.errorMessage = this.canteenOrAdminInsufficientWalletBalance ? this.insufficientFundsError : null;\n  }\n  handleOrderSummaryApiError(error) {\n    this.closeDialog(true);\n    this.handleErrorFromService(error);\n  }\n  closeDialog(error = false) {\n    return this.isTopUp ? this.closeTopUp() : this.dialogRef.close(error);\n  }\n  closeTopUp() {\n    this.isTopUp = false;\n  }\n  TopUpAmountChanged(newAmount) {\n    this.topUpAmount = newAmount;\n  }\n  TopUpClick() {\n    this.isTopUp = true;\n  }\n  GotToOrders() {\n    this.dialogRef.close({\n      success: true,\n      orderId: this.placedOrderId\n    });\n  }\n  //////////////////////////////////////////////////\n  // Place order\n  //////////////////////////////////////////////////\n  confirmOrder() {\n    this.appInsightsService.TrackEvent('ClickPlaceOrderPOS', {\n      Orders: JSON.stringify(this.orders),\n      PaymentMethod: this.paymentMethod,\n      ViewType: this.viewType,\n      StudentId: this.selectedStudent?.UserId\n    });\n    this.buttonLoading = true;\n    this.isProcessing = true;\n    this.errorMessage = null;\n    // Additional POS validation\n    if (!this.validateOrderBeforePlacement()) {\n      this.buttonLoading = false;\n      this.isProcessing = false;\n      return;\n    }\n    if (this.editOrderId) {\n      this.placeEditedOrder();\n    } else {\n      this.placeNewOrder();\n    }\n  }\n  validateOrderBeforePlacement() {\n    // Validate student selection\n    if (!this.selectedStudent) {\n      this.errorMessage = 'Please select a student before placing an order.';\n      return false;\n    }\n    // Validate cart\n    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {\n      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';\n      return false;\n    }\n    // Guest users should not reach this dialog - they should use the guest payment dialog instead\n    if (this.isGuestUser()) {\n      this.errorMessage = 'Guest users should use the card payment option. Please close this dialog and use \"Pay with Card\".';\n      return false;\n    }\n    // Validate wallet balance for Spriggy payments (non-guest users only)\n    if (this.paymentMethod === 'spriggy' && !this.sufficientWalletBalance && !this.userService.IsCanteenOrAdmin()) {\n      this.errorMessage = this.insufficientFundsError;\n      return false;\n    }\n    return true;\n  }\n  placeNewOrder() {\n    const request = this.getCreateOrdersRequest(this.orders);\n    // TODO: Temporarily disabled API call due to API issues\n    // Uncomment the following block when API is working:\n    /*\r\n    this.orderApiService.CreateOrders(request).subscribe({\r\n      next: res => {\r\n        this.placedOrderId = res.OrderId;\r\n        this.orderSuccessApiResponse();\r\n      },\r\n      error: error => {\r\n        this.orderErrorApiResponse(error);\r\n      },\r\n    });\r\n    */\n    // TEMPORARY: Mock success flow for testing\n    console.log('[POS] Mock order placement - Request:', request);\n    // Simulate API delay\n    setTimeout(() => {\n      // Generate a mock order ID\n      this.placedOrderId = Math.floor(Math.random() * 100000) + 1000;\n      console.log('[POS] Mock order placed successfully with ID:', this.placedOrderId);\n      // Execute the same success flow as real API\n      this.orderSuccessApiResponse();\n    }, 1500); // 1.5 second delay to simulate API call\n  }\n\n  placeEditedOrder() {\n    const orderId = this.editOrderId;\n    const request = {\n      OrderId: orderId,\n      Items: this.processOrderItems(this.orders[0])\n    };\n    // TODO: Temporarily disabled API call due to API issues\n    // Uncomment the following block when API is working:\n    /*\r\n    this.orderApiService.EditOrder(request).subscribe({\r\n      next: res => {\r\n        this.placedOrderId = res.OrderId;\r\n        this.orderSuccessApiResponse();\r\n      },\r\n      error: error => {\r\n        this.orderErrorApiResponse(error);\r\n      },\r\n    });\r\n    */\n    // TEMPORARY: Mock success flow for testing\n    console.log('[POS] Mock order edit - Request:', request);\n    // Simulate API delay\n    setTimeout(() => {\n      // Use the existing order ID for edits\n      this.placedOrderId = orderId;\n      console.log('[POS] Mock order edited successfully with ID:', this.placedOrderId);\n      // Execute the same success flow as real API\n      this.orderSuccessApiResponse();\n    }, 1500); // 1.5 second delay to simulate API call\n  }\n\n  orderSuccessApiResponse() {\n    console.log('[POS] Order success - executing post-order actions...');\n    // Update UI state to show order completion\n    this.orderPlaced = true;\n    console.log('[POS] ✓ Order placed state updated');\n    // Clear the shopping cart\n    this._clearCart();\n    console.log('[POS] ✓ Shopping cart cleared');\n    // Clear day detail state\n    this.store.dispatch(ClearDayDetail());\n    console.log('[POS] ✓ Day detail state cleared');\n    // Reset loading states\n    this.isProcessing = false;\n    this.buttonLoading = false;\n    console.log('[POS] ✓ Loading states reset');\n    // Send cross-tab communication for POS to update student view\n    this.sendOrderPlacedMessage();\n    console.log('[POS] ✓ Cross-tab communication sent');\n    console.log('[POS] All post-order actions completed successfully');\n  }\n  orderErrorApiResponse(error) {\n    this.handleErrorFromService(error);\n    this.orderPlaced = false;\n    this.errorMessage = this.WriteError();\n    this.isProcessing = false;\n    this.buttonLoading = false;\n  }\n  needToTopUp() {\n    return this.paymentMethod === 'spriggy' && !this.isGuestUser() && this.accountBalance < this.totalPrice && !this.userService.IsCanteenOrAdmin();\n  }\n  _clearCart() {\n    this.store.dispatch(clearAll());\n  }\n  sendOrderPlacedMessage() {\n    if (this.viewType === 'merchant' && this.placedOrderId && this.guid) {\n      const payload = {\n        orderId: this.placedOrderId,\n        studentId: this.selectedStudent.UserId,\n        studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,\n        menuType: this.selectedMenuType,\n        orderDate: this.selectedOrderDate.toISOString(),\n        totalAmount: this.totalPrice,\n        itemCount: this.orders[0].length,\n        paymentMethod: this.paymentMethod\n      };\n      console.log('[POS] Sending cross-tab order placed message:', payload);\n      this.posCommunicationService.sendOrderPlaced(payload, this.guid);\n      console.log('[POS] Cross-tab message sent to student view with GUID:', this.guid);\n    } else {\n      console.log('[POS] Cross-tab message not sent - conditions not met:', {\n        viewType: this.viewType,\n        placedOrderId: this.placedOrderId,\n        guid: this.guid,\n        hasSelectedStudent: !!this.selectedStudent\n      });\n    }\n  }\n  ///////////////////////\n  // PLACE ORDER REQUEST\n  ///////////////////////\n  getEditOrderRequest(cartItems, orderId) {\n    const groupedCartItems = this.groupCartItems(cartItems);\n    return {\n      OrderId: orderId,\n      Items: this.processOrderItems(groupedCartItems[0])\n    };\n  }\n  getCreateOrdersRequest(cartItems) {\n    const groupedCartItems = this.groupCartItems(cartItems);\n    const ordersRequestList = groupedCartItems.map(item => {\n      return this.processOrders(item);\n    });\n    return {\n      Orders: ordersRequestList\n    };\n  }\n  groupCartItems(cartData) {\n    return Object.values(cartData).map(cartItems => {\n      return cartItems;\n    });\n  }\n  processOrderItems(cartItems) {\n    return cartItems.map(item => {\n      return {\n        MenuItemId: item.menuItemId,\n        Quantity: item.quantity,\n        MenuItemOptionIds: this.getSelectedOptionIds(item.selectedOptions)\n      };\n    });\n  }\n  processOrders(cartItems) {\n    const itemList = this.processOrderItems(cartItems);\n    const firstCartItem = cartItems[0];\n    return {\n      StudentId: firstCartItem.studentId,\n      OrderDate: formatDateToUniversal(firstCartItem.date),\n      MenuId: firstCartItem.menuId,\n      Items: _.clone(itemList)\n    };\n  }\n  // Payment method helpers\n  getPaymentMethodDisplayName() {\n    switch (this.paymentMethod) {\n      case 'spriggy':\n        return 'Spriggy Card / Wallet';\n      case 'stripe':\n        return 'Stripe';\n      case 'cash':\n        return 'Cash';\n      case 'applepay':\n        return 'Apple Pay';\n      case 'visa':\n        return 'Visa';\n      default:\n        return 'Unknown Payment Method';\n    }\n  }\n  isSpriggyPayment() {\n    return this.paymentMethod === 'spriggy';\n  }\n  showWalletBalance() {\n    return this.isSpriggyPayment() && !this.isGuestUser();\n  }\n  // Additional methods to match canteen dialog functionality\n  getTextTotalOrder() {\n    return this.editOrderId ? 'New order' : 'Total';\n  }\n  getConfirmButtonText() {\n    const action = this.editOrderId > 0 ? 'Confirm Changes' : 'Confirm Order';\n    const formattedPrice = new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.totalPrice || 0);\n    return `${action} (${formattedPrice})`;\n  }\n  isRefund() {\n    return this.editOrderSummary && this.editOrderSummary.priceDiff < 0;\n  }\n  topUpMinimumAmount() {\n    return this.totalPrice - this.accountBalance;\n  }\n  // Group fees by student (simplified for POS)\n  groupFeesByStudent(createOrdersInfo) {\n    // For POS, we typically have one student, so return simplified fee structure\n    if (!createOrdersInfo || createOrdersInfo.length === 0) {\n      return [];\n    }\n    return createOrdersInfo.map(order => ({\n      name: order.studentName,\n      fee: order.fee || 0\n    }));\n  }\n  static {\n    this.ɵfac = function PosPlaceOrderDialogComponent_Factory(t) {\n      return new (t || PosPlaceOrderDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.Store), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.OrderApiService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i4.PayService), i0.ɵɵdirectiveInject(i4.AdminService), i0.ɵɵdirectiveInject(i4.CashlessAppInsightsService), i0.ɵɵdirectiveInject(i6.PosCommunicationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PosPlaceOrderDialogComponent,\n      selectors: [[\"pos-place-order-dialog\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 10,\n      vars: 4,\n      consts: [[1, \"pos-order-dialog\"], [1, \"close-button-container\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"order-content\", 4, \"ngIf\"], [3, \"goToOrders\", 4, \"ngIf\"], [\"class\", \"processing-state\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"40\"], [1, \"order-content\"], [1, \"dialog-title\"], [4, \"ngIf\"], [\"class\", \"edit-summary\", 4, \"ngIf\"], [1, \"total-section\"], [1, \"total-label\"], [1, \"total-amount\"], [1, \"wallet-section\"], [1, \"wallet-balance\"], [1, \"amount-highlight\"], [1, \"wallet-description\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"mat-stroked-button\", \"\", 1, \"go-back-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"confirm-btn\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [3, \"expanded\"], [1, \"panel-title\"], [1, \"panel-content\"], [\"class\", \"order-item\", 4, \"ngFor\", \"ngForOf\"], [3, \"expanded\", 4, \"ngIf\"], [1, \"order-item\"], [\"class\", \"fee-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"fee-item\"], [1, \"edit-summary\"], [1, \"summary-line\"], [1, \"error-message\"], [\"diameter\", \"20\"], [3, \"goToOrders\"], [1, \"processing-state\"]],\n      template: function PosPlaceOrderDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-dialog-content\")(1, \"div\", 0)(2, \"div\", 1)(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function PosPlaceOrderDialogComponent_Template_button_click_3_listener() {\n            return ctx.closeDialog();\n          });\n          i0.ɵɵelementStart(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"close\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, PosPlaceOrderDialogComponent_div_6_Template, 4, 0, \"div\", 3);\n          i0.ɵɵtemplate(7, PosPlaceOrderDialogComponent_div_7_Template, 27, 14, \"div\", 4);\n          i0.ɵɵtemplate(8, PosPlaceOrderDialogComponent_pos_orders_placed_8_Template, 1, 0, \"pos-orders-placed\", 5);\n          i0.ɵɵtemplate(9, PosPlaceOrderDialogComponent_div_9_Template, 7, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.summaryLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.summaryLoading && !ctx.orderPlaced && !ctx.isProcessing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.orderPlaced);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i7.MatButton, i7.MatIconButton, i8.MatError, i9.MatIcon, i10.MatProgressSpinner, i1.MatDialogContent, i11.MatAccordion, i11.MatExpansionPanel, i11.MatExpansionPanelHeader, i11.MatExpansionPanelTitle, i12.PosOrdersPlacedComponent, i5.CurrencyPipe, i5.DatePipe],\n      styles: [\"@font-face {\\n  font-family: \\\"bariol_regular\\\";\\n  font-display: swap;\\n  src: url('bariol_regular-webfont.woff') format(\\\"woff\\\");\\n}\\n@font-face {\\n  font-family: \\\"bariol_bold\\\";\\n  font-display: swap;\\n  src: url('bariol_bold-webfont.woff') format(\\\"woff\\\");\\n}\\n@font-face {\\n  font-family: \\\"bariol_light\\\";\\n  font-display: swap;\\n  src: url('bariol_light-webfont.woff') format(\\\"woff\\\");\\n}\\n@font-face {\\n  font-family: \\\"bariol_thin\\\";\\n  font-display: swap;\\n  src: url('bariol_thin-webfont.woff') format(\\\"woff\\\");\\n}\\n.mobile[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 767px) {\\n  .mobile[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.desktop[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (min-width: 767px) {\\n  .desktop[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.mobile[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 767px) {\\n  .mobile[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.desktop[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (min-width: 767px) {\\n  .desktop[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.pos-order-dialog[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n  min-width: 400px;\\n  padding: 0;\\n  position: relative;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  z-index: 10;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 107, 53, 0.1);\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  color: #666;\\n  font-size: 14px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%] {\\n  padding: 20px 24px 24px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 20px 0;\\n  padding-top: 20px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%] {\\n  box-shadow: none;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 4px;\\n  margin-bottom: 8px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  height: auto;\\n  min-height: 48px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]   .panel-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]   .panel-title[_ngcontent-%COMP%]   .amount-highlight[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 700;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  padding: 0 16px 16px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%], .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .fee-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 0;\\n  font-size: 14px;\\n  color: #666;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]:not(:last-child), .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .fee-item[_ngcontent-%COMP%]:not(:last-child) {\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .amount-highlight[_ngcontent-%COMP%], .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   mat-accordion[_ngcontent-%COMP%]   mat-expansion-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .fee-item[_ngcontent-%COMP%]   .amount-highlight[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 600;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .edit-summary[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .edit-summary[_ngcontent-%COMP%]   .summary-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .edit-summary[_ngcontent-%COMP%]   .summary-line[_ngcontent-%COMP%]   .amount-highlight[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 700;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .total-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 0;\\n  margin: 20px 0;\\n  border-top: 2px solid #e0e0e0;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .total-section[_ngcontent-%COMP%]   .total-label[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .total-section[_ngcontent-%COMP%]   .total-amount[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ff6b35;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .wallet-section[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .wallet-section[_ngcontent-%COMP%]   .wallet-balance[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .wallet-section[_ngcontent-%COMP%]   .wallet-balance[_ngcontent-%COMP%]   .amount-highlight[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n  font-weight: 700;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .wallet-section[_ngcontent-%COMP%]   .wallet-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  padding: 12px;\\n  background-color: #ffebee;\\n  border-left: 4px solid #f44336;\\n  border-radius: 4px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   mat-error[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #d32f2f;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 12px;\\n  margin-top: 24px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .go-back-btn[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n  height: 40px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #666;\\n  border-color: #ddd;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .go-back-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #ccc;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  height: 40px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  background-color: #ff6b35;\\n  color: white;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #e55a2b;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #ccc;\\n  color: #999;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .processing-state[_ngcontent-%COMP%] {\\n  padding: 20px 24px;\\n}\\n.pos-order-dialog[_ngcontent-%COMP%]   .processing-state[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 20px 0;\\n}\\n\\n@media (max-width: 600px) {\\n  .pos-order-dialog[_ngcontent-%COMP%] {\\n    min-width: 90vw;\\n    max-width: 90vw;\\n  }\\n  .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%] {\\n    padding: 16px 20px 20px;\\n  }\\n  .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .go-back-btn[_ngcontent-%COMP%], .pos-order-dialog[_ngcontent-%COMP%]   .order-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "_", "ClearDayDetail", "clearAll", "BaseComponent", "formatDateToUniversal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate3", "order_r12", "studentName", "menuFriendlyName", "ɵɵpipeBind2", "orderDate", "ɵɵtextInterpolate", "ɵɵpipeBind1", "price", "ɵɵtextInterpolate1", "fee_r14", "name", "fee", "ɵɵtemplate", "PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_div_10_Template", "ɵɵproperty", "ctx_r11", "totalFees", "feesToDisplay", "PosPlaceOrderDialogComponent_div_7_mat_accordion_3_div_11_Template", "PosPlaceOrderDialogComponent_div_7_mat_accordion_3_mat_expansion_panel_12_Template", "ctx_r4", "createOrderSummary", "totalAmount", "createOrdersInfo", "ctx_r5", "editOrder<PERSON><PERSON><PERSON>y", "previousPrice", "priceDiff", "ctx_r6", "errorMessage", "ctx_r8", "getConfirmButtonText", "PosPlaceOrderDialogComponent_div_7_mat_accordion_3_Template", "PosPlaceOrderDialogComponent_div_7_div_4_Template", "PosPlaceOrderDialogComponent_div_7_div_19_Template", "ɵɵlistener", "PosPlaceOrderDialogComponent_div_7_Template_button_click_21_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "closeDialog", "PosPlaceOrderDialogComponent_div_7_Template_button_click_23_listener", "ctx_r17", "confirmOrder", "PosPlaceOrderDialogComponent_div_7_mat_spinner_24_Template", "PosPlaceOrderDialogComponent_div_7_span_25_Template", "PosPlaceOrderDialogComponent_div_7_span_26_Template", "ctx_r1", "getTextTotalOrder", "totalPrice", "accountBalance", "canteenOrAdminInsufficientWalletBalance", "buttonLoading", "PosPlaceOrderDialogComponent_pos_orders_placed_8_Template_pos_orders_placed_goToOrders_0_listener", "_r19", "ctx_r18", "GotToOrders", "PosPlaceOrderDialogComponent", "constructor", "dialogRef", "data", "dialog", "store", "router", "orderApiService", "userService", "location", "payService", "adminService", "appInsightsService", "posCommunicationService", "orderPlaced", "isProcessing", "isTopUp", "sufficientWalletBalance", "summaryLoading", "paymentMethod", "isPosOrder", "placedOrderId", "viewType", "insufficientFundsError", "cannotRetrieveFundsError", "outOfStockError", "guest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "initializeComponent", "ngOnDestroy", "subscriptionBalance$", "unsubscribe", "orders", "groupedCarts", "editOrderId", "guid", "selectedStudent", "selectedMenuType", "selectedOrderDate", "validatePosRequirements", "getEditOrderSummaryAPI", "getOrderSummaryAPI", "getUpdatedWalletBalance", "IsCanteenOrAdmin", "adminOrMerchantGetParentBalance", "getParentBalance", "parent", "GetParent", "SpriggyBalance", "length", "isGuestUser", "IsGuest", "UpdateBalance", "SubscribeBalanceUpdate", "subscribe", "next", "response", "error", "handleErrorFromService", "GetEditSummaryRequest", "carts", "orderData", "map", "cartItem", "getOrderItemSummary", "OrderId", "Items", "getOrderSummaryRequest", "orderSummary", "StudentId", "studentId", "OrderDate", "date", "MenuId", "menuId", "console", "log", "request", "Orders", "order", "orderItem", "MenuItemId", "menuItemId", "MenuItemOptionIds", "getOrderItemOptionsSummary", "selectedOptions", "Quantity", "quantity", "option", "menuItemOptionId", "getSelectedOptionIds", "getOrderSummary", "res", "reduce", "prev", "groupFeesByStudent", "confirmSufficientUserBalance", "handleOrderSummaryApiError", "getEditOrderSummary", "closeTopUp", "close", "TopUpAmountChanged", "newAmount", "topUpAmount", "TopUpClick", "success", "orderId", "TrackEvent", "JSON", "stringify", "PaymentMethod", "ViewType", "UserId", "validateOrderBeforePlacement", "placeEditedOrder", "placeNewOrder", "getCreateOrdersRequest", "setTimeout", "Math", "floor", "random", "orderSuccessApiResponse", "processOrderItems", "_clearCart", "dispatch", "sendOrderPlacedMessage", "orderErrorApiResponse", "WriteError", "needToTopUp", "payload", "FirstName", "Lastname", "menuType", "toISOString", "itemCount", "sendOrderPlaced", "hasSelectedStudent", "getEditOrderRequest", "cartItems", "groupedCartItems", "groupCartItems", "ordersRequestList", "item", "processOrders", "cartData", "Object", "values", "itemList", "firstCartItem", "clone", "getPaymentMethodDisplayName", "isSpriggyPayment", "showWalletBalance", "action", "formattedPrice", "Intl", "NumberFormat", "style", "currency", "format", "isRefund", "topUpMinimumAmount", "ɵɵdirectiveInject", "i1", "MatDialogRef", "MatDialog", "i2", "Store", "i3", "Router", "i4", "OrderApiService", "UserService", "i5", "Location", "PayService", "AdminService", "CashlessAppInsightsService", "i6", "PosCommunicationService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "PosPlaceOrderDialogComponent_Template", "rf", "ctx", "PosPlaceOrderDialogComponent_Template_button_click_3_listener", "PosPlaceOrderDialogComponent_div_6_Template", "PosPlaceOrderDialogComponent_div_7_Template", "PosPlaceOrderDialogComponent_pos_orders_placed_8_Template", "PosPlaceOrderDialogComponent_div_9_Template"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\pos-place-order-dialog\\pos-place-order-dialog.component.ts", "D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\pos-place-order-dialog\\pos-place-order-dialog.component.html"], "sourcesContent": ["/*\n * TEMPORARY CHANGES FOR API TESTING:\n *\n * The following API calls have been temporarily disabled due to API issues:\n * - this.orderApiService.CreateOrders() in placeNewOrder() method (line ~394)\n * - this.orderApiService.EditOrder() in placeEditedOrder() method (line ~423)\n *\n * Instead, mock success flows have been implemented that:\n * 1. Simulate API delay with setTimeout (1.5 seconds)\n * 2. Generate mock order IDs\n * 3. Execute all the same post-order actions:\n *    - Clear shopping cart\n *    - Update UI state to show order completion\n *    - Trigger cross-tab communication to update student view\n *    - Clear day detail state\n *    - Reset loading states\n *\n * TO RESTORE NORMAL FUNCTIONALITY:\n * - Uncomment the API calls in placeNewOrder() and placeEditedOrder()\n * - Remove the setTimeout mock implementations\n * - Remove this comment block\n */\n\nimport { Component, OnInit, OnDestroy, Inject } from '@angular/core';\nimport { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { Location } from '@angular/common';\nimport { Store } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport * as _ from 'lodash';\n\n// States\nimport { FamilyState } from 'src/app/states';\nimport { ClearDayDetail } from 'src/app/states/family/family.actions';\nimport { clearAll } from 'src/app/states/shoppingCart/shopping-cart.actions';\n\n// Models\nimport {\n  UserCashless,\n  MenuItem,\n  FeeRequest,\n  FeeToShow,\n  CreateOrderRequest,\n  CreateOrderInfo,\n  BaseComponent,\n  OrderItemSummary,\n  EditOrderRequest,\n  CreateOrdersSummaryRequest,\n  CreateOrdersSummary,\n  CreateEditSummary,\n  CreateEditSummaryRequest,\n  CartOption,\n  OrdersSummary,\n  CartItem,\n} from 'src/app/sharedModels';\n\n// Services\nimport {\n  OrderApiService,\n  UserService,\n  CashlessAppInsightsService,\n  PayService,\n  AdminService,\n} from 'src/app/sharedServices';\n\n// POS Services\nimport { PosCommunicationService } from '../../services/pos-communication.service';\n\n// Utilities\nimport { formatDateToUniversal } from 'src/app/utility';\n\n// POS Models\nimport { OrderPlacedPayload } from '../../models/pos-messages.interface';\n\nexport interface PosPlaceOrderDialogData {\n  editOrderId: number;\n  groupedCarts: CartItem[][];\n  // POS-specific properties (optional)\n  viewType?: 'merchant' | 'student';\n  guid?: string;\n  selectedStudent?: any;\n  selectedMenuType?: string;\n  selectedOrderDate?: Date;\n}\n\n@Component({\n  selector: 'pos-place-order-dialog',\n  templateUrl: './pos-place-order-dialog.component.html',\n  styleUrls: ['./pos-place-order-dialog.component.scss'],\n})\nexport class PosPlaceOrderDialogComponent extends BaseComponent implements OnInit, OnDestroy {\n  topUpAmount: number;\n  errorMessage: string = null;\n  orderPlaced: boolean = false;\n  isProcessing: boolean = false;\n  isTopUp: boolean = false;\n  orders: CartItem[][];\n  sufficientWalletBalance: boolean = false;\n  cartItems: MenuItem[];\n  connectedUser: UserCashless;\n  canteenOrAdminInsufficientWalletBalance: boolean = false;\n  buttonLoading: boolean = false;\n  editOrderId: number;\n  accountBalance: number;\n\n  totalPrice: number;\n  totalFees: number = 0;\n  createOrderSummary: CreateOrdersSummary = null;\n  editOrderSummary: CreateEditSummary = null;\n\n  fees: FeeRequest[];\n  feesToDisplay: FeeToShow[] = [];\n  summaryLoading: boolean = false;\n\n  // POS-specific properties\n  paymentMethod: string = 'spriggy';\n  isPosOrder: boolean = true;\n  placedOrderId: number = null;\n  viewType: 'merchant' | 'student' = 'merchant';\n  guid: string;\n  selectedStudent: any;\n  selectedMenuType: string;\n  selectedOrderDate: Date;\n\n  private subscriptionBalance$: Subscription;\n\n  // Error Messages\n  insufficientFundsError =\n    'Sorry, this order cannot be completed due to insufficient funds in the user wallet.';\n  cannotRetrieveFundsError = 'We are having an issue retrieving your balance, please contact support team.';\n  outOfStockError = 'Sorry, one or more items in your order are out of stock and could not be processed.';\n  guestUserOrderError = 'Guest users should use the card payment option. Please close this dialog and use \"Pay with Card\".';\n\n  constructor(\n    public dialogRef: MatDialogRef<PosPlaceOrderDialogComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: PosPlaceOrderDialogData,\n    public dialog: MatDialog,\n    private store: Store<{ family: FamilyState }>,\n    private router: Router,\n    private orderApiService: OrderApiService,\n    private userService: UserService,\n    private location: Location,\n    private payService: PayService,\n    private adminService: AdminService,\n    private appInsightsService: CashlessAppInsightsService,\n    private posCommunicationService: PosCommunicationService\n  ) {\n    super();\n  }\n\n  ngOnInit(): void {\n    this.initializeComponent();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptionBalance$?.unsubscribe();\n  }\n\n  private initializeComponent(): void {\n    // Initialize from dialog data\n    this.orders = this.data.groupedCarts;\n    this.editOrderId = this.data.editOrderId;\n    this.paymentMethod = 'spriggy'; // Only Spriggy payment is supported\n    this.viewType = this.data.viewType || 'merchant';\n    this.guid = this.data.guid;\n    this.selectedStudent = this.data.selectedStudent;\n    this.selectedMenuType = this.data.selectedMenuType;\n    this.selectedOrderDate = this.data.selectedOrderDate;\n\n    // Validate POS-specific requirements\n    if (!this.validatePosRequirements()) {\n      return;\n    }\n\n    // Get order summary\n    if (this.editOrderId) {\n      this.getEditOrderSummaryAPI();\n    } else {\n      this.getOrderSummaryAPI();\n    }\n\n    // Get balance using the same logic as the original canteen system\n    this.getUpdatedWalletBalance();\n  }\n\n  getUpdatedWalletBalance(): void {\n    if (this.userService.IsCanteenOrAdmin()) {\n      this.adminOrMerchantGetParentBalance();\n      return;\n    }\n    this.getParentBalance();\n  }\n\n  adminOrMerchantGetParentBalance(): void {\n    const parent = this.adminService.GetParent();\n    this.accountBalance = +parent.SpriggyBalance;\n  }\n\n  private validatePosRequirements(): boolean {\n    if (!this.selectedStudent) {\n      this.errorMessage = 'Please select a student before placing an order.';\n      return false;\n    }\n\n    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {\n      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';\n      return false;\n    }\n\n    // Guest users are now allowed in POS but will use different payment flow\n    // No need to block them here - payment method validation happens later\n\n    return true;\n  }\n\n  private isGuestUser(): boolean {\n    return this.selectedStudent?.IsGuest === true;\n  }\n\n  getParentBalance(): void {\n    this.payService.UpdateBalance();\n    this.subscriptionBalance$ = this.payService.SubscribeBalanceUpdate().subscribe({\n      next: (response: number) => {\n        this.accountBalance = response;\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.isProcessing = false;\n      },\n    });\n  }\n\n  GetEditSummaryRequest(carts: CartItem[][]): CreateEditSummaryRequest {\n    const orderData = carts.map((cartItem: CartItem[]) => {\n      return this.getOrderItemSummary(cartItem);\n    });\n\n    return { OrderId: this.editOrderId, Items: orderData[0] };\n  }\n\n  getOrderSummaryRequest(carts: CartItem[][]): CreateOrdersSummaryRequest {\n    const orderData: OrdersSummary[] = carts.map((cartItem: CartItem[]) => {\n      const orderSummary = {\n        OrderId: 0,\n        StudentId: cartItem[0].studentId,\n        OrderDate: formatDateToUniversal(cartItem[0].date),\n        MenuId: cartItem[0].menuId,\n        Items: this.getOrderItemSummary(cartItem),\n      };\n\n      console.log(`[POS] Creating order summary with MenuId: ${orderSummary.MenuId} for student: ${orderSummary.StudentId}`);\n      return orderSummary;\n    });\n\n    const request = { Orders: orderData };\n    console.log(`[POS] Order summary request:`, request);\n    return request;\n  }\n\n  getOrderItemSummary(order: CartItem[]): OrderItemSummary[] {\n    return order.map(orderItem => ({\n      MenuItemId: orderItem.menuItemId,\n      MenuItemOptionIds: this.getOrderItemOptionsSummary(orderItem.selectedOptions),\n      Quantity: orderItem.quantity,\n    }));\n  }\n\n  getOrderItemOptionsSummary(selectedOptions: CartOption[]): number[] {\n    if (selectedOptions?.length === 0) {\n      return [];\n    }\n    return selectedOptions.map(option => option.menuItemOptionId);\n  }\n\n  getSelectedOptionIds(selectedOptions: CartOption[]): number[] {\n    return selectedOptions.map(option => option.menuItemOptionId);\n  }\n\n  getOrderSummaryAPI(): void {\n    this.summaryLoading = true;\n    const request = this.getOrderSummaryRequest(this.orders);\n\n    this.orderApiService.getOrderSummary(request).subscribe({\n      next: (res: CreateOrdersSummary) => {\n        this.createOrderSummary = res;\n        this.totalFees = this.createOrderSummary.createOrdersInfo.reduce((prev, next) => prev + next.fee, 0);\n        this.totalPrice = this.createOrderSummary.totalAmount + this.totalFees;\n\n        // Populate fees to display\n        this.feesToDisplay = this.groupFeesByStudent(this.createOrderSummary.createOrdersInfo);\n\n        this.summaryLoading = false;\n        this.confirmSufficientUserBalance();\n      },\n      error: error => {\n        this.handleOrderSummaryApiError(error);\n      },\n    });\n  }\n\n  getEditOrderSummaryAPI(): void {\n    this.summaryLoading = true;\n    const request = this.GetEditSummaryRequest(this.orders);\n\n    this.orderApiService.getEditOrderSummary(request).subscribe({\n      next: (res: CreateEditSummary) => {\n        this.editOrderSummary = res;\n        this.summaryLoading = false;\n        this.totalPrice = this.editOrderSummary.price;\n      },\n      error: error => {\n        this.handleOrderSummaryApiError(error);\n      },\n    });\n  }\n\n  /**\n   * For by Admin/Canteen creating orders\n   * Check if user balance is enough to complete payment after order fee is added\n   */\n  confirmSufficientUserBalance(): void {\n    if (!this.userService.IsCanteenOrAdmin() || this.editOrderId) {\n      return;\n    }\n    this.canteenOrAdminInsufficientWalletBalance = this.accountBalance < this.totalPrice;\n    this.errorMessage = this.canteenOrAdminInsufficientWalletBalance ? this.insufficientFundsError : null;\n  }\n\n  handleOrderSummaryApiError(error: any): void {\n    this.closeDialog(true);\n    this.handleErrorFromService(error);\n  }\n\n  closeDialog(error: boolean = false): void {\n    return this.isTopUp ? this.closeTopUp() : this.dialogRef.close(error);\n  }\n\n  closeTopUp(): void {\n    this.isTopUp = false;\n  }\n\n  TopUpAmountChanged(newAmount: number): void {\n    this.topUpAmount = newAmount;\n  }\n\n  TopUpClick(): void {\n    this.isTopUp = true;\n  }\n\n  GotToOrders(): void {\n    this.dialogRef.close({ success: true, orderId: this.placedOrderId });\n  }\n\n  //////////////////////////////////////////////////\n  // Place order\n  //////////////////////////////////////////////////\n\n  confirmOrder(): void {\n    this.appInsightsService.TrackEvent('ClickPlaceOrderPOS', {\n      Orders: JSON.stringify(this.orders),\n      PaymentMethod: this.paymentMethod,\n      ViewType: this.viewType,\n      StudentId: this.selectedStudent?.UserId,\n    });\n\n    this.buttonLoading = true;\n    this.isProcessing = true;\n    this.errorMessage = null;\n\n    // Additional POS validation\n    if (!this.validateOrderBeforePlacement()) {\n      this.buttonLoading = false;\n      this.isProcessing = false;\n      return;\n    }\n\n    if (this.editOrderId) {\n      this.placeEditedOrder();\n    } else {\n      this.placeNewOrder();\n    }\n  }\n\n  private validateOrderBeforePlacement(): boolean {\n    // Validate student selection\n    if (!this.selectedStudent) {\n      this.errorMessage = 'Please select a student before placing an order.';\n      return false;\n    }\n\n    // Validate cart\n    if (!this.orders || this.orders.length === 0 || this.orders[0].length === 0) {\n      this.errorMessage = 'Your cart is empty. Please add items before placing an order.';\n      return false;\n    }\n\n    // Guest users should not reach this dialog - they should use the guest payment dialog instead\n    if (this.isGuestUser()) {\n      this.errorMessage = 'Guest users should use the card payment option. Please close this dialog and use \"Pay with Card\".';\n      return false;\n    }\n\n    // Validate wallet balance for Spriggy payments (non-guest users only)\n    if (this.paymentMethod === 'spriggy' && !this.sufficientWalletBalance && !this.userService.IsCanteenOrAdmin()) {\n      this.errorMessage = this.insufficientFundsError;\n      return false;\n    }\n\n    return true;\n  }\n\n  placeNewOrder(): void {\n    const request: CreateOrderRequest = this.getCreateOrdersRequest(this.orders);\n\n    // TODO: Temporarily disabled API call due to API issues\n    // Uncomment the following block when API is working:\n    /*\n    this.orderApiService.CreateOrders(request).subscribe({\n      next: res => {\n        this.placedOrderId = res.OrderId;\n        this.orderSuccessApiResponse();\n      },\n      error: error => {\n        this.orderErrorApiResponse(error);\n      },\n    });\n    */\n\n    // TEMPORARY: Mock success flow for testing\n    console.log('[POS] Mock order placement - Request:', request);\n\n    // Simulate API delay\n    setTimeout(() => {\n      // Generate a mock order ID\n      this.placedOrderId = Math.floor(Math.random() * 100000) + 1000;\n      console.log('[POS] Mock order placed successfully with ID:', this.placedOrderId);\n\n      // Execute the same success flow as real API\n      this.orderSuccessApiResponse();\n    }, 1500); // 1.5 second delay to simulate API call\n  }\n\n  placeEditedOrder(): void {\n    const orderId = this.editOrderId;\n    const request: EditOrderRequest = { OrderId: orderId, Items: this.processOrderItems(this.orders[0]) };\n\n    // TODO: Temporarily disabled API call due to API issues\n    // Uncomment the following block when API is working:\n    /*\n    this.orderApiService.EditOrder(request).subscribe({\n      next: res => {\n        this.placedOrderId = res.OrderId;\n        this.orderSuccessApiResponse();\n      },\n      error: error => {\n        this.orderErrorApiResponse(error);\n      },\n    });\n    */\n\n    // TEMPORARY: Mock success flow for testing\n    console.log('[POS] Mock order edit - Request:', request);\n\n    // Simulate API delay\n    setTimeout(() => {\n      // Use the existing order ID for edits\n      this.placedOrderId = orderId;\n      console.log('[POS] Mock order edited successfully with ID:', this.placedOrderId);\n\n      // Execute the same success flow as real API\n      this.orderSuccessApiResponse();\n    }, 1500); // 1.5 second delay to simulate API call\n  }\n\n  orderSuccessApiResponse(): void {\n    console.log('[POS] Order success - executing post-order actions...');\n\n    // Update UI state to show order completion\n    this.orderPlaced = true;\n    console.log('[POS] ✓ Order placed state updated');\n\n    // Clear the shopping cart\n    this._clearCart();\n    console.log('[POS] ✓ Shopping cart cleared');\n\n    // Clear day detail state\n    this.store.dispatch(ClearDayDetail());\n    console.log('[POS] ✓ Day detail state cleared');\n\n    // Reset loading states\n    this.isProcessing = false;\n    this.buttonLoading = false;\n    console.log('[POS] ✓ Loading states reset');\n\n    // Send cross-tab communication for POS to update student view\n    this.sendOrderPlacedMessage();\n    console.log('[POS] ✓ Cross-tab communication sent');\n\n    console.log('[POS] All post-order actions completed successfully');\n  }\n\n  orderErrorApiResponse(error: any): void {\n    this.handleErrorFromService(error);\n    this.orderPlaced = false;\n    this.errorMessage = this.WriteError();\n    this.isProcessing = false;\n    this.buttonLoading = false;\n  }\n\n  needToTopUp(): boolean {\n    return this.paymentMethod === 'spriggy' &&\n           !this.isGuestUser() &&\n           this.accountBalance < this.totalPrice &&\n           !this.userService.IsCanteenOrAdmin();\n  }\n\n  private _clearCart(): void {\n    this.store.dispatch(clearAll());\n  }\n\n  private sendOrderPlacedMessage(): void {\n    if (this.viewType === 'merchant' && this.placedOrderId && this.guid) {\n      const payload: OrderPlacedPayload = {\n        orderId: this.placedOrderId,\n        studentId: this.selectedStudent.UserId,\n        studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,\n        menuType: this.selectedMenuType,\n        orderDate: this.selectedOrderDate.toISOString(),\n        totalAmount: this.totalPrice,\n        itemCount: this.orders[0].length,\n        paymentMethod: this.paymentMethod\n      };\n\n      console.log('[POS] Sending cross-tab order placed message:', payload);\n      this.posCommunicationService.sendOrderPlaced(payload, this.guid);\n      console.log('[POS] Cross-tab message sent to student view with GUID:', this.guid);\n    } else {\n      console.log('[POS] Cross-tab message not sent - conditions not met:', {\n        viewType: this.viewType,\n        placedOrderId: this.placedOrderId,\n        guid: this.guid,\n        hasSelectedStudent: !!this.selectedStudent\n      });\n    }\n  }\n\n  ///////////////////////\n  // PLACE ORDER REQUEST\n  ///////////////////////\n\n  getEditOrderRequest(cartItems: CartItem[][], orderId: number): EditOrderRequest {\n    const groupedCartItems = this.groupCartItems(cartItems);\n    return { OrderId: orderId, Items: this.processOrderItems(groupedCartItems[0]) };\n  }\n\n  getCreateOrdersRequest(cartItems: CartItem[][]): CreateOrderRequest {\n    const groupedCartItems = this.groupCartItems(cartItems);\n    const ordersRequestList = groupedCartItems.map(item => {\n      return this.processOrders(item);\n    });\n\n    return { Orders: ordersRequestList };\n  }\n\n  groupCartItems(cartData: CartItem[][]): CartItem[][] {\n    return Object.values(cartData).map((cartItems: CartItem[]) => {\n      return cartItems;\n    });\n  }\n\n  processOrderItems(cartItems: CartItem[]): OrderItemSummary[] {\n    return cartItems.map((item: CartItem) => {\n      return {\n        MenuItemId: item.menuItemId,\n        Quantity: item.quantity,\n        MenuItemOptionIds: this.getSelectedOptionIds(item.selectedOptions),\n      };\n    });\n  }\n\n  processOrders(cartItems: CartItem[]): CreateOrderInfo {\n    const itemList = this.processOrderItems(cartItems);\n\n    const firstCartItem = cartItems[0];\n    return {\n      StudentId: firstCartItem.studentId,\n      OrderDate: formatDateToUniversal(firstCartItem.date),\n      MenuId: firstCartItem.menuId,\n      Items: _.clone(itemList),\n    };\n  }\n\n  // Payment method helpers\n  getPaymentMethodDisplayName(): string {\n    switch (this.paymentMethod) {\n      case 'spriggy':\n        return 'Spriggy Card / Wallet';\n      case 'stripe':\n        return 'Stripe';\n      case 'cash':\n        return 'Cash';\n      case 'applepay':\n        return 'Apple Pay';\n      case 'visa':\n        return 'Visa';\n      default:\n        return 'Unknown Payment Method';\n    }\n  }\n\n  isSpriggyPayment(): boolean {\n    return this.paymentMethod === 'spriggy';\n  }\n\n  showWalletBalance(): boolean {\n    return this.isSpriggyPayment() && !this.isGuestUser();\n  }\n\n  // Additional methods to match canteen dialog functionality\n  getTextTotalOrder(): string {\n    return this.editOrderId ? 'New order' : 'Total';\n  }\n\n  getConfirmButtonText(): string {\n    const action = this.editOrderId > 0 ? 'Confirm Changes' : 'Confirm Order';\n    const formattedPrice = new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.totalPrice || 0);\n    return `${action} (${formattedPrice})`;\n  }\n\n  isRefund(): boolean {\n    return this.editOrderSummary && this.editOrderSummary.priceDiff < 0;\n  }\n\n  topUpMinimumAmount(): number {\n    return this.totalPrice - this.accountBalance;\n  }\n\n  // Group fees by student (simplified for POS)\n  groupFeesByStudent(createOrdersInfo: any[]): any[] {\n    // For POS, we typically have one student, so return simplified fee structure\n    if (!createOrdersInfo || createOrdersInfo.length === 0) {\n      return [];\n    }\n\n    return createOrdersInfo.map(order => ({\n      name: order.studentName,\n      fee: order.fee || 0\n    }));\n  }\n}\n", "<mat-dialog-content>\n  <div class=\"pos-order-dialog\">\n    <!-- Close Button -->\n    <div class=\"close-button-container\">\n      <button mat-icon-button class=\"close-button\" (click)=\"closeDialog()\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n\n    <!-- Loading State -->\n    <div *ngIf=\"summaryLoading\" class=\"loading-state\">\n      <mat-spinner diameter=\"40\"></mat-spinner>\n      <p>Calculating order total...</p>\n    </div>\n\n    <!-- Order Details -->\n    <div *ngIf=\"!summaryLoading && !orderPlaced && !isProcessing\" class=\"order-content\">\n      <!-- Header -->\n      <h2 class=\"dialog-title\">Order Details</h2>\n\n      <!-- Order Summary Expansion Panel -->\n      <mat-accordion *ngIf=\"createOrderSummary\">\n        <mat-expansion-panel [expanded]=\"true\">\n          <mat-expansion-panel-header>\n            <mat-panel-title>\n              <span class=\"panel-title\">Orders: (<span class=\"amount-highlight\">{{ createOrderSummary.totalAmount | currency }}</span>)</span>\n            </mat-panel-title>\n          </mat-expansion-panel-header>\n          <div class=\"panel-content\">\n            <div *ngFor=\"let order of createOrderSummary.createOrdersInfo\" class=\"order-item\">\n              {{ order.studentName }} - {{ order.menuFriendlyName }} - {{ order.orderDate | date : 'EE dd/LL' }}:\n              <span class=\"amount-highlight\">{{ order.price | currency }}</span>\n            </div>\n          </div>\n        </mat-expansion-panel>\n\n        <mat-expansion-panel *ngIf=\"totalFees > 0\" [expanded]=\"true\">\n          <mat-expansion-panel-header>\n            <mat-panel-title>\n              <span class=\"panel-title\">Order Fees: (<span class=\"amount-highlight\">{{ totalFees | currency }}</span>)</span>\n            </mat-panel-title>\n          </mat-expansion-panel-header>\n          <div class=\"panel-content\">\n            <div *ngFor=\"let fee of feesToDisplay\" class=\"fee-item\">\n              Order Fee ({{ fee.name }})\n              <span class=\"amount-highlight\">+{{ fee.fee | currency }}</span>\n            </div>\n          </div>\n        </mat-expansion-panel>\n      </mat-accordion>\n\n      <!-- Edit Order Summary -->\n      <div *ngIf=\"editOrderSummary\" class=\"edit-summary\">\n        <div class=\"summary-line\">Previous order: <span class=\"amount-highlight\">{{ editOrderSummary.previousPrice | currency }}</span></div>\n        <div class=\"summary-line\">New order: <span class=\"amount-highlight\">{{ editOrderSummary.price | currency }}</span></div>\n        <div class=\"summary-line\">Difference: <span class=\"amount-highlight\">{{ editOrderSummary.priceDiff | currency }}</span></div>\n      </div>\n\n      <!-- Total -->\n      <div class=\"total-section\">\n        <span class=\"total-label\">{{ getTextTotalOrder() }}:</span>\n        <span class=\"total-amount\">{{ totalPrice | currency }}</span>\n      </div>\n\n      <!-- Wallet Balance -->\n      <div class=\"wallet-section\">\n        <div class=\"wallet-balance\">\n          Wallet Balance: <span class=\"amount-highlight\">{{ accountBalance | currency }}</span>\n        </div>\n        <div class=\"wallet-description\">\n          We'll deduct the total orders amount from your wallet balance.\n        </div>\n      </div>\n\n      <!-- Error Message -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\">\n        <mat-error>{{ errorMessage }}</mat-error>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"action-buttons\">\n        <button mat-stroked-button class=\"go-back-btn\" (click)=\"closeDialog()\">\n          Go back\n        </button>\n        <button\n          mat-raised-button\n          color=\"primary\"\n          class=\"confirm-btn\"\n          (click)=\"confirmOrder()\"\n          [disabled]=\"canteenOrAdminInsufficientWalletBalance || buttonLoading\">\n          <mat-spinner *ngIf=\"buttonLoading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!buttonLoading\">{{ getConfirmButtonText() }}</span>\n          <span *ngIf=\"buttonLoading\">Processing...</span>\n        </button>\n      </div>\n    </div>\n\n    <!-- Order Placed Success -->\n    <pos-orders-placed *ngIf=\"orderPlaced\" (goToOrders)=\"GotToOrders()\"></pos-orders-placed>\n\n    <!-- Processing State -->\n    <div *ngIf=\"isProcessing\" class=\"processing-state\">\n      <h2 class=\"dialog-title\">Processing your order...</h2>\n      <div class=\"loading-state\">\n        <mat-spinner diameter=\"40\"></mat-spinner>\n        <p>Please wait while we process your order...</p>\n      </div>\n    </div>\n  </div>\n</mat-dialog-content>\n"], "mappings": "AAwBA,SAAuBA,eAAe,QAAmB,0BAA0B;AAKnF,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAI3B,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,QAAQ,QAAQ,mDAAmD;AAE5E;AACA,SAOEC,aAAa,QAUR,sBAAsB;AAc7B;AACA,SAASC,qBAAqB,QAAQ,iBAAiB;;;;;;;;;;;;;;;;IC3DnDC,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAiB3BJ,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAG,MAAA,GACA;;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADlEJ,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAM,kBAAA,MAAAC,SAAA,CAAAC,WAAA,SAAAD,SAAA,CAAAE,gBAAA,SAAAT,EAAA,CAAAU,WAAA,OAAAH,SAAA,CAAAI,SAAA,oBACA;IAA+BX,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,OAAAN,SAAA,CAAAO,KAAA,EAA4B;;;;;IAY7Dd,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAG,MAAA,GACA;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAAyB;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAD/DJ,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAe,kBAAA,iBAAAC,OAAA,CAAAC,IAAA,OACA;IAA+BjB,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAe,kBAAA,MAAAf,EAAA,CAAAa,WAAA,OAAAG,OAAA,CAAAE,GAAA,MAAyB;;;;;IAT9DlB,EAAA,CAAAC,cAAA,8BAA6D;IAG7BD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA0B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAAAJ,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGnHJ,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAAC,yFAAA,kBAGM;IACRpB,EAAA,CAAAI,YAAA,EAAM;;;;IAXmCJ,EAAA,CAAAqB,UAAA,kBAAiB;IAGgBrB,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,OAAAS,OAAA,CAAAC,SAAA,EAA0B;IAI7EvB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAqB,UAAA,YAAAC,OAAA,CAAAE,aAAA,CAAgB;;;;;IAtB3CxB,EAAA,CAAAC,cAAA,oBAA0C;IAIRD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA+C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAAAJ,EAAA,CAAAG,MAAA,QAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGpIJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAAM,kEAAA,kBAGM;IACRzB,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAmB,UAAA,KAAAO,kFAAA,mCAYsB;IACxB1B,EAAA,CAAAI,YAAA,EAAgB;;;;IA3BOJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAqB,UAAA,kBAAiB;IAGkCrB,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,OAAAc,MAAA,CAAAC,kBAAA,CAAAC,WAAA,EAA+C;IAI5F7B,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAqB,UAAA,YAAAM,MAAA,CAAAC,kBAAA,CAAAE,gBAAA,CAAsC;IAO3C9B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAqB,UAAA,SAAAM,MAAA,CAAAJ,SAAA,KAAmB;;;;;IAgB3CvB,EAAA,CAAAC,cAAA,cAAmD;IACvBD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA+C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/HJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAAuC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClHJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAA2C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAF9CJ,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,OAAAkB,MAAA,CAAAC,gBAAA,CAAAC,aAAA,EAA+C;IACpDjC,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,QAAAkB,MAAA,CAAAC,gBAAA,CAAAlB,KAAA,EAAuC;IACtCd,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,QAAAkB,MAAA,CAAAC,gBAAA,CAAAE,SAAA,EAA2C;;;;;IAoBlHlC,EAAA,CAAAC,cAAA,cAAgD;IACnCD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAY;;;;IAA9BJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAAuB,MAAA,CAAAC,YAAA,CAAkB;;;;;IAc3BpC,EAAA,CAAAE,SAAA,sBAA+D;;;;;IAC/DF,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAC,oBAAA,GAA4B;;;;;IACzDtC,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IA5EtDJ,EAAA,CAAAC,cAAA,aAAoF;IAEzDD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAG3CJ,EAAA,CAAAmB,UAAA,IAAAoB,2DAAA,6BA4BgB;IAGhBvC,EAAA,CAAAmB,UAAA,IAAAqB,iDAAA,mBAIM;IAGNxC,EAAA,CAAAC,cAAA,cAA2B;IACCD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA2B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAI/DJ,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAG,MAAA,yBAAgB;IAAAH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAA+B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEvFJ,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAG,MAAA,wEACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAmB,UAAA,KAAAsB,kDAAA,kBAEM;IAGNzC,EAAA,CAAAC,cAAA,eAA4B;IACqBD,EAAA,CAAA0C,UAAA,mBAAAC,qEAAA;MAAA3C,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAA+C,aAAA;MAAA,OAAS/C,EAAA,CAAAgD,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpEjD,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAKwE;IADtED,EAAA,CAAA0C,UAAA,mBAAAQ,qEAAA;MAAAlD,EAAA,CAAA4C,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAnD,EAAA,CAAA+C,aAAA;MAAA,OAAS/C,EAAA,CAAAgD,WAAA,CAAAG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExBpD,EAAA,CAAAmB,UAAA,KAAAkC,0DAAA,0BAA+D;IAC/DrD,EAAA,CAAAmB,UAAA,KAAAmC,mDAAA,mBAAgE;IAChEtD,EAAA,CAAAmB,UAAA,KAAAoC,mDAAA,mBAAgD;IAClDvD,EAAA,CAAAI,YAAA,EAAS;;;;IAxEKJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAqB,UAAA,SAAAmC,MAAA,CAAA5B,kBAAA,CAAwB;IA+BlC5B,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAqB,UAAA,SAAAmC,MAAA,CAAAxB,gBAAA,CAAsB;IAQAhC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAe,kBAAA,KAAAyC,MAAA,CAAAC,iBAAA,QAA0B;IACzBzD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,SAAA2C,MAAA,CAAAE,UAAA,EAA2B;IAML1D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,SAAA2C,MAAA,CAAAG,cAAA,EAA+B;IAQ5E3D,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAqB,UAAA,SAAAmC,MAAA,CAAApB,YAAA,CAAkB;IAcpBpC,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAqB,UAAA,aAAAmC,MAAA,CAAAI,uCAAA,IAAAJ,MAAA,CAAAK,aAAA,CAAqE;IACvD7D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAqB,UAAA,SAAAmC,MAAA,CAAAK,aAAA,CAAmB;IAC1B7D,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAqB,UAAA,UAAAmC,MAAA,CAAAK,aAAA,CAAoB;IACpB7D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAqB,UAAA,SAAAmC,MAAA,CAAAK,aAAA,CAAmB;;;;;;IAMhC7D,EAAA,CAAAC,cAAA,4BAAoE;IAA7BD,EAAA,CAAA0C,UAAA,wBAAAoB,kGAAA;MAAA9D,EAAA,CAAA4C,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAA+C,aAAA;MAAA,OAAc/C,EAAA,CAAAgD,WAAA,CAAAgB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAACjE,EAAA,CAAAI,YAAA,EAAoB;;;;;IAGxFJ,EAAA,CAAAC,cAAA,cAAmD;IACxBD,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtDJ,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADfzD,OAAM,MAAO8D,4BAA6B,SAAQpE,aAAa;EA2C7DqE,YACSC,SAAqD,EAC5BC,IAA6B,EACtDC,MAAiB,EAChBC,KAAqC,EACrCC,MAAc,EACdC,eAAgC,EAChCC,WAAwB,EACxBC,QAAkB,EAClBC,UAAsB,EACtBC,YAA0B,EAC1BC,kBAA8C,EAC9CC,uBAAgD;IAExD,KAAK,EAAE;IAbA,KAAAX,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,uBAAuB,GAAvBA,uBAAuB;IArDjC,KAAA3C,YAAY,GAAW,IAAI;IAC3B,KAAA4C,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,OAAO,GAAY,KAAK;IAExB,KAAAC,uBAAuB,GAAY,KAAK;IAGxC,KAAAvB,uCAAuC,GAAY,KAAK;IACxD,KAAAC,aAAa,GAAY,KAAK;IAK9B,KAAAtC,SAAS,GAAW,CAAC;IACrB,KAAAK,kBAAkB,GAAwB,IAAI;IAC9C,KAAAI,gBAAgB,GAAsB,IAAI;IAG1C,KAAAR,aAAa,GAAgB,EAAE;IAC/B,KAAA4D,cAAc,GAAY,KAAK;IAE/B;IACA,KAAAC,aAAa,GAAW,SAAS;IACjC,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAC,aAAa,GAAW,IAAI;IAC5B,KAAAC,QAAQ,GAA2B,UAAU;IAQ7C;IACA,KAAAC,sBAAsB,GACpB,qFAAqF;IACvF,KAAAC,wBAAwB,GAAG,8EAA8E;IACzG,KAAAC,eAAe,GAAG,qFAAqF;IACvG,KAAAC,mBAAmB,GAAG,mGAAmG;EAiBzH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,oBAAoB,EAAEC,WAAW,EAAE;EAC1C;EAEQH,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACI,MAAM,GAAG,IAAI,CAAC7B,IAAI,CAAC8B,YAAY;IACpC,IAAI,CAACC,WAAW,GAAG,IAAI,CAAC/B,IAAI,CAAC+B,WAAW;IACxC,IAAI,CAACf,aAAa,GAAG,SAAS,CAAC,CAAC;IAChC,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACnB,IAAI,CAACmB,QAAQ,IAAI,UAAU;IAChD,IAAI,CAACa,IAAI,GAAG,IAAI,CAAChC,IAAI,CAACgC,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACjC,IAAI,CAACiC,eAAe;IAChD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAClC,IAAI,CAACkC,gBAAgB;IAClD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACnC,IAAI,CAACmC,iBAAiB;IAEpD;IACA,IAAI,CAAC,IAAI,CAACC,uBAAuB,EAAE,EAAE;MACnC;;IAGF;IACA,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,IAAI,CAACM,sBAAsB,EAAE;KAC9B,MAAM;MACL,IAAI,CAACC,kBAAkB,EAAE;;IAG3B;IACA,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAAClC,WAAW,CAACmC,gBAAgB,EAAE,EAAE;MACvC,IAAI,CAACC,+BAA+B,EAAE;MACtC;;IAEF,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,+BAA+BA,CAAA;IAC7B,MAAME,MAAM,GAAG,IAAI,CAACnC,YAAY,CAACoC,SAAS,EAAE;IAC5C,IAAI,CAACtD,cAAc,GAAG,CAACqD,MAAM,CAACE,cAAc;EAC9C;EAEQT,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACH,eAAe,EAAE;MACzB,IAAI,CAAClE,YAAY,GAAG,kDAAkD;MACtE,OAAO,KAAK;;IAGd,IAAI,CAAC,IAAI,CAAC8D,MAAM,IAAI,IAAI,CAACA,MAAM,CAACiB,MAAM,KAAK,CAAC,IAAI,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAACiB,MAAM,KAAK,CAAC,EAAE;MAC3E,IAAI,CAAC/E,YAAY,GAAG,+DAA+D;MACnF,OAAO,KAAK;;IAGd;IACA;IAEA,OAAO,IAAI;EACb;EAEQgF,WAAWA,CAAA;IACjB,OAAO,IAAI,CAACd,eAAe,EAAEe,OAAO,KAAK,IAAI;EAC/C;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACnC,UAAU,CAAC0C,aAAa,EAAE;IAC/B,IAAI,CAACtB,oBAAoB,GAAG,IAAI,CAACpB,UAAU,CAAC2C,sBAAsB,EAAE,CAACC,SAAS,CAAC;MAC7EC,IAAI,EAAGC,QAAgB,IAAI;QACzB,IAAI,CAAC/D,cAAc,GAAG+D,QAAQ;MAChC,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC1C,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEA4C,qBAAqBA,CAACC,KAAmB;IACvC,MAAMC,SAAS,GAAGD,KAAK,CAACE,GAAG,CAAEC,QAAoB,IAAI;MACnD,OAAO,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAAC;IAC3C,CAAC,CAAC;IAEF,OAAO;MAAEE,OAAO,EAAE,IAAI,CAAC/B,WAAW;MAAEgC,KAAK,EAAEL,SAAS,CAAC,CAAC;IAAC,CAAE;EAC3D;EAEAM,sBAAsBA,CAACP,KAAmB;IACxC,MAAMC,SAAS,GAAoBD,KAAK,CAACE,GAAG,CAAEC,QAAoB,IAAI;MACpE,MAAMK,YAAY,GAAG;QACnBH,OAAO,EAAE,CAAC;QACVI,SAAS,EAAEN,QAAQ,CAAC,CAAC,CAAC,CAACO,SAAS;QAChCC,SAAS,EAAE1I,qBAAqB,CAACkI,QAAQ,CAAC,CAAC,CAAC,CAACS,IAAI,CAAC;QAClDC,MAAM,EAAEV,QAAQ,CAAC,CAAC,CAAC,CAACW,MAAM;QAC1BR,KAAK,EAAE,IAAI,CAACF,mBAAmB,CAACD,QAAQ;OACzC;MAEDY,OAAO,CAACC,GAAG,CAAC,6CAA6CR,YAAY,CAACK,MAAM,iBAAiBL,YAAY,CAACC,SAAS,EAAE,CAAC;MACtH,OAAOD,YAAY;IACrB,CAAC,CAAC;IAEF,MAAMS,OAAO,GAAG;MAAEC,MAAM,EAAEjB;IAAS,CAAE;IACrCc,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,OAAO,CAAC;IACpD,OAAOA,OAAO;EAChB;EAEAb,mBAAmBA,CAACe,KAAiB;IACnC,OAAOA,KAAK,CAACjB,GAAG,CAACkB,SAAS,KAAK;MAC7BC,UAAU,EAAED,SAAS,CAACE,UAAU;MAChCC,iBAAiB,EAAE,IAAI,CAACC,0BAA0B,CAACJ,SAAS,CAACK,eAAe,CAAC;MAC7EC,QAAQ,EAAEN,SAAS,CAACO;KACrB,CAAC,CAAC;EACL;EAEAH,0BAA0BA,CAACC,eAA6B;IACtD,IAAIA,eAAe,EAAEpC,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,EAAE;;IAEX,OAAOoC,eAAe,CAACvB,GAAG,CAAC0B,MAAM,IAAIA,MAAM,CAACC,gBAAgB,CAAC;EAC/D;EAEAC,oBAAoBA,CAACL,eAA6B;IAChD,OAAOA,eAAe,CAACvB,GAAG,CAAC0B,MAAM,IAAIA,MAAM,CAACC,gBAAgB,CAAC;EAC/D;EAEAhD,kBAAkBA,CAAA;IAChB,IAAI,CAACvB,cAAc,GAAG,IAAI;IAC1B,MAAM2D,OAAO,GAAG,IAAI,CAACV,sBAAsB,CAAC,IAAI,CAACnC,MAAM,CAAC;IAExD,IAAI,CAACzB,eAAe,CAACoF,eAAe,CAACd,OAAO,CAAC,CAACvB,SAAS,CAAC;MACtDC,IAAI,EAAGqC,GAAwB,IAAI;QACjC,IAAI,CAAClI,kBAAkB,GAAGkI,GAAG;QAC7B,IAAI,CAACvI,SAAS,GAAG,IAAI,CAACK,kBAAkB,CAACE,gBAAgB,CAACiI,MAAM,CAAC,CAACC,IAAI,EAAEvC,IAAI,KAAKuC,IAAI,GAAGvC,IAAI,CAACvG,GAAG,EAAE,CAAC,CAAC;QACpG,IAAI,CAACwC,UAAU,GAAG,IAAI,CAAC9B,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACN,SAAS;QAEtE;QACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACyI,kBAAkB,CAAC,IAAI,CAACrI,kBAAkB,CAACE,gBAAgB,CAAC;QAEtF,IAAI,CAACsD,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC8E,4BAA4B,EAAE;MACrC,CAAC;MACDvC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACwC,0BAA0B,CAACxC,KAAK,CAAC;MACxC;KACD,CAAC;EACJ;EAEAjB,sBAAsBA,CAAA;IACpB,IAAI,CAACtB,cAAc,GAAG,IAAI;IAC1B,MAAM2D,OAAO,GAAG,IAAI,CAAClB,qBAAqB,CAAC,IAAI,CAAC3B,MAAM,CAAC;IAEvD,IAAI,CAACzB,eAAe,CAAC2F,mBAAmB,CAACrB,OAAO,CAAC,CAACvB,SAAS,CAAC;MAC1DC,IAAI,EAAGqC,GAAsB,IAAI;QAC/B,IAAI,CAAC9H,gBAAgB,GAAG8H,GAAG;QAC3B,IAAI,CAAC1E,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAAC1B,gBAAgB,CAAClB,KAAK;MAC/C,CAAC;MACD6G,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACwC,0BAA0B,CAACxC,KAAK,CAAC;MACxC;KACD,CAAC;EACJ;EAEA;;;;EAIAuC,4BAA4BA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACxF,WAAW,CAACmC,gBAAgB,EAAE,IAAI,IAAI,CAACT,WAAW,EAAE;MAC5D;;IAEF,IAAI,CAACxC,uCAAuC,GAAG,IAAI,CAACD,cAAc,GAAG,IAAI,CAACD,UAAU;IACpF,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACwB,uCAAuC,GAAG,IAAI,CAAC6B,sBAAsB,GAAG,IAAI;EACvG;EAEA0E,0BAA0BA,CAACxC,KAAU;IACnC,IAAI,CAAC1E,WAAW,CAAC,IAAI,CAAC;IACtB,IAAI,CAAC2E,sBAAsB,CAACD,KAAK,CAAC;EACpC;EAEA1E,WAAWA,CAAC0E,KAAA,GAAiB,KAAK;IAChC,OAAO,IAAI,CAACzC,OAAO,GAAG,IAAI,CAACmF,UAAU,EAAE,GAAG,IAAI,CAACjG,SAAS,CAACkG,KAAK,CAAC3C,KAAK,CAAC;EACvE;EAEA0C,UAAUA,CAAA;IACR,IAAI,CAACnF,OAAO,GAAG,KAAK;EACtB;EAEAqF,kBAAkBA,CAACC,SAAiB;IAClC,IAAI,CAACC,WAAW,GAAGD,SAAS;EAC9B;EAEAE,UAAUA,CAAA;IACR,IAAI,CAACxF,OAAO,GAAG,IAAI;EACrB;EAEAjB,WAAWA,CAAA;IACT,IAAI,CAACG,SAAS,CAACkG,KAAK,CAAC;MAAEK,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI,CAACrF;IAAa,CAAE,CAAC;EACtE;EAEA;EACA;EACA;EAEAnC,YAAYA,CAAA;IACV,IAAI,CAAC0B,kBAAkB,CAAC+F,UAAU,CAAC,oBAAoB,EAAE;MACvD7B,MAAM,EAAE8B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7E,MAAM,CAAC;MACnC8E,aAAa,EAAE,IAAI,CAAC3F,aAAa;MACjC4F,QAAQ,EAAE,IAAI,CAACzF,QAAQ;MACvB+C,SAAS,EAAE,IAAI,CAACjC,eAAe,EAAE4E;KAClC,CAAC;IAEF,IAAI,CAACrH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACoB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC7C,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAAC,IAAI,CAAC+I,4BAA4B,EAAE,EAAE;MACxC,IAAI,CAACtH,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACoB,YAAY,GAAG,KAAK;MACzB;;IAGF,IAAI,IAAI,CAACmB,WAAW,EAAE;MACpB,IAAI,CAACgF,gBAAgB,EAAE;KACxB,MAAM;MACL,IAAI,CAACC,aAAa,EAAE;;EAExB;EAEQF,4BAA4BA,CAAA;IAClC;IACA,IAAI,CAAC,IAAI,CAAC7E,eAAe,EAAE;MACzB,IAAI,CAAClE,YAAY,GAAG,kDAAkD;MACtE,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC,IAAI,CAAC8D,MAAM,IAAI,IAAI,CAACA,MAAM,CAACiB,MAAM,KAAK,CAAC,IAAI,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAACiB,MAAM,KAAK,CAAC,EAAE;MAC3E,IAAI,CAAC/E,YAAY,GAAG,+DAA+D;MACnF,OAAO,KAAK;;IAGd;IACA,IAAI,IAAI,CAACgF,WAAW,EAAE,EAAE;MACtB,IAAI,CAAChF,YAAY,GAAG,mGAAmG;MACvH,OAAO,KAAK;;IAGd;IACA,IAAI,IAAI,CAACiD,aAAa,KAAK,SAAS,IAAI,CAAC,IAAI,CAACF,uBAAuB,IAAI,CAAC,IAAI,CAACT,WAAW,CAACmC,gBAAgB,EAAE,EAAE;MAC7G,IAAI,CAACzE,YAAY,GAAG,IAAI,CAACqD,sBAAsB;MAC/C,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA4F,aAAaA,CAAA;IACX,MAAMtC,OAAO,GAAuB,IAAI,CAACuC,sBAAsB,CAAC,IAAI,CAACpF,MAAM,CAAC;IAE5E;IACA;IACA;;;;;;;;;;;IAYA;IACA2C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,OAAO,CAAC;IAE7D;IACAwC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAChG,aAAa,GAAGiG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI;MAC9D7C,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAACvD,aAAa,CAAC;MAEhF;MACA,IAAI,CAACoG,uBAAuB,EAAE;IAChC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;;EAEAP,gBAAgBA,CAAA;IACd,MAAMR,OAAO,GAAG,IAAI,CAACxE,WAAW;IAChC,MAAM2C,OAAO,GAAqB;MAAEZ,OAAO,EAAEyC,OAAO;MAAExC,KAAK,EAAE,IAAI,CAACwD,iBAAiB,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAAC,CAAC;IAAC,CAAE;IAErG;IACA;IACA;;;;;;;;;;;IAYA;IACA2C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,OAAO,CAAC;IAExD;IACAwC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAChG,aAAa,GAAGqF,OAAO;MAC5B/B,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAACvD,aAAa,CAAC;MAEhF;MACA,IAAI,CAACoG,uBAAuB,EAAE;IAChC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;;EAEAA,uBAAuBA,CAAA;IACrB9C,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IAEpE;IACA,IAAI,CAAC9D,WAAW,GAAG,IAAI;IACvB6D,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAAC+C,UAAU,EAAE;IACjBhD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAE5C;IACA,IAAI,CAACvE,KAAK,CAACuH,QAAQ,CAAClM,cAAc,EAAE,CAAC;IACrCiJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C;IACA,IAAI,CAAC7D,YAAY,GAAG,KAAK;IACzB,IAAI,CAACpB,aAAa,GAAG,KAAK;IAC1BgF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C;IACA,IAAI,CAACiD,sBAAsB,EAAE;IAC7BlD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IAEnDD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EACpE;EAEAkD,qBAAqBA,CAACrE,KAAU;IAC9B,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;IAClC,IAAI,CAAC3C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC5C,YAAY,GAAG,IAAI,CAAC6J,UAAU,EAAE;IACrC,IAAI,CAAChH,YAAY,GAAG,KAAK;IACzB,IAAI,CAACpB,aAAa,GAAG,KAAK;EAC5B;EAEAqI,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC7G,aAAa,KAAK,SAAS,IAChC,CAAC,IAAI,CAAC+B,WAAW,EAAE,IACnB,IAAI,CAACzD,cAAc,GAAG,IAAI,CAACD,UAAU,IACrC,CAAC,IAAI,CAACgB,WAAW,CAACmC,gBAAgB,EAAE;EAC7C;EAEQgF,UAAUA,CAAA;IAChB,IAAI,CAACtH,KAAK,CAACuH,QAAQ,CAACjM,QAAQ,EAAE,CAAC;EACjC;EAEQkM,sBAAsBA,CAAA;IAC5B,IAAI,IAAI,CAACvG,QAAQ,KAAK,UAAU,IAAI,IAAI,CAACD,aAAa,IAAI,IAAI,CAACc,IAAI,EAAE;MACnE,MAAM8F,OAAO,GAAuB;QAClCvB,OAAO,EAAE,IAAI,CAACrF,aAAa;QAC3BiD,SAAS,EAAE,IAAI,CAAClC,eAAe,CAAC4E,MAAM;QACtC1K,WAAW,EAAE,GAAG,IAAI,CAAC8F,eAAe,CAAC8F,SAAS,IAAI,IAAI,CAAC9F,eAAe,CAAC+F,QAAQ,EAAE;QACjFC,QAAQ,EAAE,IAAI,CAAC/F,gBAAgB;QAC/B5F,SAAS,EAAE,IAAI,CAAC6F,iBAAiB,CAAC+F,WAAW,EAAE;QAC/C1K,WAAW,EAAE,IAAI,CAAC6B,UAAU;QAC5B8I,SAAS,EAAE,IAAI,CAACtG,MAAM,CAAC,CAAC,CAAC,CAACiB,MAAM;QAChC9B,aAAa,EAAE,IAAI,CAACA;OACrB;MAEDwD,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEqD,OAAO,CAAC;MACrE,IAAI,CAACpH,uBAAuB,CAAC0H,eAAe,CAACN,OAAO,EAAE,IAAI,CAAC9F,IAAI,CAAC;MAChEwC,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE,IAAI,CAACzC,IAAI,CAAC;KAClF,MAAM;MACLwC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;QACpEtD,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBD,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCc,IAAI,EAAE,IAAI,CAACA,IAAI;QACfqG,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAACpG;OAC5B,CAAC;;EAEN;EAEA;EACA;EACA;EAEAqG,mBAAmBA,CAACC,SAAuB,EAAEhC,OAAe;IAC1D,MAAMiC,gBAAgB,GAAG,IAAI,CAACC,cAAc,CAACF,SAAS,CAAC;IACvD,OAAO;MAAEzE,OAAO,EAAEyC,OAAO;MAAExC,KAAK,EAAE,IAAI,CAACwD,iBAAiB,CAACiB,gBAAgB,CAAC,CAAC,CAAC;IAAC,CAAE;EACjF;EAEAvB,sBAAsBA,CAACsB,SAAuB;IAC5C,MAAMC,gBAAgB,GAAG,IAAI,CAACC,cAAc,CAACF,SAAS,CAAC;IACvD,MAAMG,iBAAiB,GAAGF,gBAAgB,CAAC7E,GAAG,CAACgF,IAAI,IAAG;MACpD,OAAO,IAAI,CAACC,aAAa,CAACD,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,OAAO;MAAEhE,MAAM,EAAE+D;IAAiB,CAAE;EACtC;EAEAD,cAAcA,CAACI,QAAsB;IACnC,OAAOC,MAAM,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAClF,GAAG,CAAE4E,SAAqB,IAAI;MAC3D,OAAOA,SAAS;IAClB,CAAC,CAAC;EACJ;EAEAhB,iBAAiBA,CAACgB,SAAqB;IACrC,OAAOA,SAAS,CAAC5E,GAAG,CAAEgF,IAAc,IAAI;MACtC,OAAO;QACL7D,UAAU,EAAE6D,IAAI,CAAC5D,UAAU;QAC3BI,QAAQ,EAAEwD,IAAI,CAACvD,QAAQ;QACvBJ,iBAAiB,EAAE,IAAI,CAACO,oBAAoB,CAACoD,IAAI,CAACzD,eAAe;OAClE;IACH,CAAC,CAAC;EACJ;EAEA0D,aAAaA,CAACL,SAAqB;IACjC,MAAMS,QAAQ,GAAG,IAAI,CAACzB,iBAAiB,CAACgB,SAAS,CAAC;IAElD,MAAMU,aAAa,GAAGV,SAAS,CAAC,CAAC,CAAC;IAClC,OAAO;MACLrE,SAAS,EAAE+E,aAAa,CAAC9E,SAAS;MAClCC,SAAS,EAAE1I,qBAAqB,CAACuN,aAAa,CAAC5E,IAAI,CAAC;MACpDC,MAAM,EAAE2E,aAAa,CAAC1E,MAAM;MAC5BR,KAAK,EAAEzI,CAAC,CAAC4N,KAAK,CAACF,QAAQ;KACxB;EACH;EAEA;EACAG,2BAA2BA,CAAA;IACzB,QAAQ,IAAI,CAACnI,aAAa;MACxB,KAAK,SAAS;QACZ,OAAO,uBAAuB;MAChC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,UAAU;QACb,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,MAAM;MACf;QACE,OAAO,wBAAwB;;EAErC;EAEAoI,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpI,aAAa,KAAK,SAAS;EACzC;EAEAqI,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;EACvD;EAEA;EACA3D,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC2C,WAAW,GAAG,WAAW,GAAG,OAAO;EACjD;EAEA9D,oBAAoBA,CAAA;IAClB,MAAMqL,MAAM,GAAG,IAAI,CAACvH,WAAW,GAAG,CAAC,GAAG,iBAAiB,GAAG,eAAe;IACzE,MAAMwH,cAAc,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpDC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC,IAAI,CAACvK,UAAU,IAAI,CAAC,CAAC;IAC/B,OAAO,GAAGiK,MAAM,KAAKC,cAAc,GAAG;EACxC;EAEAM,QAAQA,CAAA;IACN,OAAO,IAAI,CAAClM,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACE,SAAS,GAAG,CAAC;EACrE;EAEAiM,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACzK,UAAU,GAAG,IAAI,CAACC,cAAc;EAC9C;EAEA;EACAsG,kBAAkBA,CAACnI,gBAAuB;IACxC;IACA,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACqF,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO,EAAE;;IAGX,OAAOrF,gBAAgB,CAACkG,GAAG,CAACiB,KAAK,KAAK;MACpChI,IAAI,EAAEgI,KAAK,CAACzI,WAAW;MACvBU,GAAG,EAAE+H,KAAK,CAAC/H,GAAG,IAAI;KACnB,CAAC,CAAC;EACL;;;uBAjjBWgD,4BAA4B,EAAAlE,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CA6C7B1O,eAAe,GAAAM,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAE,SAAA,GAAAvO,EAAA,CAAAoO,iBAAA,CAAAI,EAAA,CAAAC,KAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAC,MAAA,GAAA3O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAA7O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAE,WAAA,GAAA9O,EAAA,CAAAoO,iBAAA,CAAAW,EAAA,CAAAC,QAAA,GAAAhP,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAK,UAAA,GAAAjP,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAM,YAAA,GAAAlP,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAO,0BAAA,GAAAnP,EAAA,CAAAoO,iBAAA,CAAAgB,EAAA,CAAAC,uBAAA;IAAA;EAAA;;;YA7CdnL,4BAA4B;MAAAoL,SAAA;MAAAC,QAAA,GAAAvP,EAAA,CAAAwP,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1FzC9P,EAAA,CAAAC,cAAA,yBAAoB;UAI+BD,EAAA,CAAA0C,UAAA,mBAAAsN,8DAAA;YAAA,OAASD,GAAA,CAAA9M,WAAA,EAAa;UAAA,EAAC;UAClEjD,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAK9BJ,EAAA,CAAAmB,UAAA,IAAA8O,2CAAA,iBAGM;UAGNjQ,EAAA,CAAAmB,UAAA,IAAA+O,2CAAA,mBA+EM;UAGNlQ,EAAA,CAAAmB,UAAA,IAAAgP,yDAAA,+BAAwF;UAGxFnQ,EAAA,CAAAmB,UAAA,IAAAiP,2CAAA,iBAMM;UACRpQ,EAAA,CAAAI,YAAA,EAAM;;;UAlGEJ,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAqB,UAAA,SAAA0O,GAAA,CAAA3K,cAAA,CAAoB;UAMpBpF,EAAA,CAAAK,SAAA,GAAsD;UAAtDL,EAAA,CAAAqB,UAAA,UAAA0O,GAAA,CAAA3K,cAAA,KAAA2K,GAAA,CAAA/K,WAAA,KAAA+K,GAAA,CAAA9K,YAAA,CAAsD;UAkFxCjF,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAqB,UAAA,SAAA0O,GAAA,CAAA/K,WAAA,CAAiB;UAG/BhF,EAAA,CAAAK,SAAA,GAAkB;UAAlBL,EAAA,CAAAqB,UAAA,SAAA0O,GAAA,CAAA9K,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}