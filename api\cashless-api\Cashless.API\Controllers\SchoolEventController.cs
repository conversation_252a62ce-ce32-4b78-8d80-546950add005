﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Controllers
{
    /* ------------------------------------------------------------
        SchoolEventController is being used by Parents from the Web
        and from the mobile app. Need to review and make end points
        secure for parent use
       ------------------------------------------------------------ */
    [Authorize]
    [CheckUserRole(UserRole.Admin, UserRole.Parent)]
    [Route("api/[controller]")]
    [ApiController]
    public class SchoolEventController : ControllerBase
    {
        private readonly ITelemetryService telemetryService;
        private readonly IDBHelper dbHelper;
        private readonly IUserService userService;
        private readonly IUnitOfWork unitOfWork;
        private readonly ICanteenService canteenService;
        private readonly ISchoolEventValidator schoolEventValidator;
        public SchoolEventController(ITelemetryService telemetryService, IDBHelper dbHelper, IUserService userService, ICanteenService canteenService, ISchoolEventValidator schoolEventValidator, IUnitOfWork unitOfWork)
        {
            this.telemetryService = telemetryService;
            this.dbHelper = dbHelper;
            this.userService = userService;
            this.canteenService = canteenService;
            this.unitOfWork = unitOfWork;
            this.schoolEventValidator = schoolEventValidator;
        }

        /// <summary>
        /// Get event
        /// </summary>
        [Route("{eventId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetEvent(int eventId)
        {
            SchoolEvent schoolEventResponse = await this.dbHelper.ExecSprocByParams<SchoolEvent>("sp_SchoolEvent_GetBy_Id",
                                                                    new Dictionary<string, string>() { { "id", $"{eventId}" } });
            await this.schoolEventValidator.ValidateAccessToSchoolEvent(schoolEventResponse);
            return new OkObjectResult(schoolEventResponse);
        }

        [Route("GetEventsBySchool")]
        [HttpPost]
        public async Task<IActionResult> GetEventsBySchool([FromBody] SchoolEventsRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this.schoolEventValidator.ValidateAccessToStudent(request.StudentId);

            SchoolEventsResponse listEventsResponse = new();

            List<SchoolEvent> res = await this.dbHelper.ExecSprocByParams<List<SchoolEvent>>("sp_SchoolEvent_GetBy_SchoolId",
                                                        new Dictionary<string, string>() {
                                                            { "schoolId", $"{request.SchoolId}" },
                                                            { "studentId", $"{request.StudentId}" }
                                                        });

            if (res != null && res.Count > 0)
            {
                //get student Class
                User student = await this.userService.GetUserById(request.StudentId);

                // check if the events are specific to a class
                listEventsResponse.Events = new List<SchoolEvent>();

                foreach (var ev in res)
                {
                    if (String.IsNullOrEmpty(ev.SpecificClasses))
                    {
                        // no specific class for the event
                        listEventsResponse.Events.Add(ev);
                    }
                    else if (ev.SpecificClasses.Contains(student.ClassId.ToString()))
                    {
                        // the student is in one of the classes specify in the event
                        listEventsResponse.Events.Add(ev);
                    }
                }
            }
            else
            {
                listEventsResponse.Events = res;
            }

            return new OkObjectResult(listEventsResponse);
        }

        [Route("GetUniformShopBySchool")]
        [HttpPost]
        public async Task<IActionResult> GetUniformShopBySchool([FromBody] SchoolEventsRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this.schoolEventValidator.ValidateAccessToSchool(request.SchoolId);

            OptionSchool res = await this.dbHelper.ExecSprocByParams<OptionSchool>("sp_SchoolUniformShop_GetBy_SchoolId",
                                                    new Dictionary<string, string>() { { "schoolId", $"{request.SchoolId}" } });

            return new OkObjectResult(res);
        }

        [Route("GetPayAtCanteenBySchool")]
        [HttpPost]
        public async Task<IActionResult> GetPayAtCanteenBySchool([FromBody] SchoolEventsRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this.schoolEventValidator.ValidateAccessToStudent(request.StudentId);

            OptionSchool res = await this.dbHelper.ExecSprocByParams<OptionSchool>("sp_SchoolPayAtCanteen_GetBy_SchoolId",
                                                    new Dictionary<string, string>() { { "schoolId", $"{request.SchoolId}" } });

            return new OkObjectResult(res);
        }

        // To Do: Remove duplicate end point
        [Route("GetEventsById/{id}")]
        [HttpGet]
        public async Task<IActionResult> GetEventsById(int id)
        {
            SchoolEvent schoolEventResponse = await this.dbHelper.ExecSprocByParams<SchoolEvent>("sp_SchoolEvent_GetBy_Id",
                                                                     new Dictionary<string, string>() { { "id", $"{id}" } });

            await this.schoolEventValidator.ValidateAccessToSchoolEvent(schoolEventResponse);
            return new OkObjectResult(schoolEventResponse);
        }


        #region

        /// <summary>
        /// Get all events available to parents. Not the same as GetEventsBySchool
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("GetEventsBySchoolMobile")]
        [HttpPost]
        public async Task<IActionResult> GetEventsBySchoolMobile([FromBody] SchoolEventsRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this.schoolEventValidator.ValidateAccessToStudent(request.StudentId);

            SchoolEventsResponse listEventsResponse = new();

            this.telemetryService.TrackTrace("GetEventsBySchoolMobile",
                                new Dictionary<string, string>() {
                                    {"request", JsonConvert.SerializeObject(request)}
                                });

            var json = await this.dbHelper.ExecSprocByParams("sp_SchoolEvent_GetBy_SchoolId_Mobile",
                                            new Dictionary<string, string>() {
                                                { "schoolId", $"{request.SchoolId}" },
                                                { "studentId", $"{request.StudentId}" }
                                            });

            this.telemetryService.TrackTrace("GetEventsBySchoolMobile",
                                new Dictionary<string, string>() {
                                        {"response", json}
                                });

            List<SchoolEvent> res = JsonConvert.DeserializeObject<List<SchoolEvent>>(json);

            if (res != null && res.Count > 0)
            {
                //get student Class
                User student = await this.userService.GetUserById(request.StudentId);

                // check if the events are specific to a class
                listEventsResponse.Events = new List<SchoolEvent>();

                foreach (var ev in res)
                {
                    if (String.IsNullOrEmpty(ev.SpecificClasses))
                    {
                        // no specific class for the event
                        listEventsResponse.Events.Add(ev);
                    }
                    else if (ev.SpecificClasses.Contains(student.ClassId.ToString()))
                    {
                        // the student is in one of the classes specify in the event
                        listEventsResponse.Events.Add(ev);
                    }
                }
            }
            else
            {
                listEventsResponse.Events = res;
            }

            return new OkObjectResult(listEventsResponse);
        }

        #endregion
    }
}
