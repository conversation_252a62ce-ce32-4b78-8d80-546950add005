using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Schools.BLL.Services;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;

namespace Cashless.API.HealthChecks;

/// <summary>
/// A health check to parse the responses produced by instances of IHealthCheckService
/// </summary>
public class PingHealthCheck : BaseHealthCheck, IHealthCheck
{
    /// <summary>
    /// Logger!
    /// </summary>
    private readonly ILogger<PingHealthCheck> _logger;

    /// <summary>
    /// URL for the health-check endpoint on the schools orders functions app
    /// </summary>
    private readonly string _url;

    /// <summary>
    /// HTTP client factory
    /// </summary>
    private readonly IHttpClientFactory _httpClientFactory;

    public PingHealthCheck(IHttpClientFactory httpClientFactory, ILogger<PingHealthCheck> logger, string baseUrl)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _url = GetUrl(baseUrl, "health-check");
    }

    /// <summary>
    /// Return a failure result based on the configuration of the health check
    /// </summary>
    private HealthCheckResult GetFailureResult(HealthCheckContext context, Stopwatch stopwatch, object response)
    {
        _logger.LogWarning("Configured context for ping check against URL: {Url} has failure status: {FailureStatus}", _url, context?.Registration.FailureStatus);

        // Config says degraded!
        if (context.Registration != null && context.Registration.FailureStatus == HealthStatus.Degraded)
        {
            return GetDegradedResult(stopwatch, response, _url);
        }

        return GetUnhealthyResult(stopwatch, response, _url);
    }

    /// <summary>
    /// Check Schools Orders functions app is running. The status returned depends on the configuration context passed in
    /// </summary>
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            using (var client = _httpClientFactory.CreateClient())
            {
                var response = await client.GetAsync(_url);
                if (response == null || response.StatusCode != HttpStatusCode.OK)
                {
                    return GetFailureResult(context, stopwatch, response);
                }

                var body = await response.Content.ReadAsStringAsync();
                if (body == null)
                {
                    return GetFailureResult(context, stopwatch, response);
                }

                var result = JsonSerializer.Deserialize<PingResult>(body);
                if (!result.IsHealthy())
                {
                    return GetFailureResult(context, stopwatch, response);
                }

                return GetHealthyResult(stopwatch, result, _url);
            }
        }
        catch (Exception ex)
        {
            return GetFailureResult(context, stopwatch, ex);
        }
    }
}
