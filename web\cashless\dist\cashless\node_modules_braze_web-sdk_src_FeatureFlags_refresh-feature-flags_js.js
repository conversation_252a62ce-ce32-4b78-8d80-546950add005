"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_FeatureFlags_refresh-feature-flags_js"],{

/***/ 3119:
/*!******************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flag-factory.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   newFeatureFlagFromJson: () => (/* binding */ newFeatureFlagFromJson),
/* harmony export */   newFeatureFlagFromSerializedValue: () => (/* binding */ newFeatureFlagFromSerializedValue)
/* harmony export */ });
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _feature_flag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./feature-flag.js */ 96052);


function newFeatureFlagFromJson(e) {
  if (e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ns] && "boolean" == typeof e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.Fe]) return new _feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"](e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ns], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.Fe], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.we], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ze]);
  _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Unable to create feature flag from ${JSON.stringify(e, null, 2)}`);
}
function newFeatureFlagFromSerializedValue(e) {
  if (e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns] && "boolean" == typeof e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.Fe]) return new _feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"](e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.Fe], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.we], e[_feature_flag_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ze]);
  _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Unable to deserialize feature flag from ${JSON.stringify(e, null, 2)}`);
}

/***/ }),

/***/ 96052:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flag.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ FeatureFlag)
/* harmony export */ });
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);

class FeatureFlag {
  constructor(t, r = !1, e = {}, s) {
    this.id = t, this.enabled = r, this.properties = e, this.trackingString = s, this.id = t, this.enabled = r, this.properties = e, this.trackingString = s;
  }
  getStringProperty(t) {
    const r = this.properties[t];
    return null == r ? (this.Er(t), null) : this.Ir(r) ? r.value : (this.Nr("string"), null);
  }
  getNumberProperty(t) {
    const r = this.properties[t];
    return null == r ? (this.Er(t), null) : this.Tr(r) ? r.value : (this.Nr("number"), null);
  }
  getBooleanProperty(t) {
    const r = this.properties[t];
    return null == r ? (this.Er(t), null) : this.Ar(r) ? r.value : (this.Nr("boolean"), null);
  }
  ss() {
    const t = {};
    return t[FeatureFlag.hs.ns] = this.id, t[FeatureFlag.hs.Fe] = this.enabled, t[FeatureFlag.hs.we] = this.properties, t[FeatureFlag.hs.ze] = this.trackingString, t;
  }
  Nr(t) {
    _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_0__["default"].j.info(`Property is not of type ${t}.`);
  }
  Er(t) {
    _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_0__["default"].j.info(`${t} not found in feature flag properties.`);
  }
  Ir(t) {
    return "string" === t.type && "string" == typeof t.value;
  }
  Tr(t) {
    return "number" === t.type && "number" == typeof t.value;
  }
  Ar(t) {
    return "boolean" === t.type && "boolean" == typeof t.value;
  }
}
FeatureFlag.hs = {
  ns: "id",
  Fe: "e",
  we: "pr",
  ze: "fts"
}, FeatureFlag.Tt = {
  ns: "id",
  Fe: "enabled",
  we: "properties",
  ze: "fts"
};

/***/ }),

/***/ 19246:
/*!****************************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flags-provider-factory.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feature_flags_provider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./feature-flags-provider.js */ 92253);


const ir = {
  t: !1,
  provider: null,
  er: () => (ir.o(), ir.provider || (ir.provider = new _feature_flags_provider_js__WEBPACK_IMPORTED_MODULE_0__["default"](_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].tr(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].ar(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].l()), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].dr(ir.provider)), ir.provider),
  o: () => {
    ir.t || (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].g(ir), ir.t = !0);
  },
  destroy: () => {
    ir.provider = null, ir.t = !1;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ir);

/***/ }),

/***/ 92253:
/*!********************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flags-provider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ er)
/* harmony export */ });
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _common_base_provider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common/base-provider.js */ 72406);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../managers/storage-manager.js */ 57309);
/* harmony import */ var _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/subscription-manager.js */ 25235);
/* harmony import */ var _util_math_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/math.js */ 80178);
/* harmony import */ var _util_net_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/net.js */ 21453);
/* harmony import */ var _feature_flag_factory_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./feature-flag-factory.js */ 3119);
/* harmony import */ var _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/request-header-utils.js */ 43853);









class er extends _common_base_provider_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(t, s, i) {
    super(), this.wt = t, this.gt = s, this.u = i, this.pi = [], this.gi = 0, this.wt = t, this.gt = s, this.u = i, this.Fi = null, this.wi = new _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__["default"](), this.yi = 10, this.ji = null, this.bi = null, _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_2__["default"].jt(this.wi);
  }
  Ts(t) {
    if ((!this.wt || this.wt.vi()) && null != t && t.feature_flags) {
      this.pi = [];
      for (const s of t.feature_flags) {
        const t = (0,_feature_flag_factory_js__WEBPACK_IMPORTED_MODULE_3__.newFeatureFlagFromJson)(s);
        t && this.pi.push(t);
      }
      this.gi = new Date().getTime(), this.Ti(), this.wi.Et(this.pi);
    }
  }
  Di() {
    let t = {};
    this.u && (t = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__.STORAGE_KEYS.k.Ri));
    const s = {};
    for (const i in t) {
      const e = (0,_feature_flag_factory_js__WEBPACK_IMPORTED_MODULE_3__.newFeatureFlagFromSerializedValue)(t[i]);
      e && (s[e.id] = e);
    }
    return s;
  }
  Ni() {
    var t;
    return (null === (t = this.u) || void 0 === t ? void 0 : t.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__.STORAGE_KEYS.k.qi)) || {};
  }
  xi(t) {
    this.u && this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__.STORAGE_KEYS.k.qi, t);
  }
  ri(t) {
    return this.wi.lt(t);
  }
  refreshFeatureFlags(t, s, i = !1, e = !0) {
    if (!this.zi(i)) return !this.Fi && this.wt && (this.Fi = this.wt.Ci(() => {
      this.refreshFeatureFlags(t, s);
    })), void ("function" == typeof s && s());
    if (e && this.Ii(), !this.gt) return void ("function" == typeof s && s());
    const r = this.gt.Bs({}, !0),
      h = this.gt.Hs(r, _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].Os.Si);
    let o = !1;
    this.gt.Qs(r, () => {
      this.gt ? (_util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].Ws(this.u, _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].Os.Si, new Date().valueOf()), _util_net_js__WEBPACK_IMPORTED_MODULE_6__["default"].Xs({
        url: `${this.gt.Ys()}/feature_flags/sync`,
        headers: h,
        data: r,
        O: i => {
          if (!this.gt.Zs(r, i, h)) return o = !0, void ("function" == typeof s && s());
          this.gt.ti(), this.Ts(i), o = !1, _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].si(this.u, _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].Os.Si, 1), "function" == typeof t && t();
        },
        error: t => {
          this.gt.ii(t, "retrieving feature flags"), o = !0, "function" == typeof s && s();
        },
        ei: () => {
          if (e && o && !this.bi) {
            _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].hi(this.u, _util_request_header_utils_js__WEBPACK_IMPORTED_MODULE_5__["default"].Os.Si);
            let e = this.ji;
            (null == e || e < 1e3 * this.yi) && (e = 1e3 * this.yi), this.$i(Math.min(3e5, (0,_util_math_js__WEBPACK_IMPORTED_MODULE_7__.randomInclusive)(1e3 * this.yi, 3 * e)), t, s, i);
          }
        }
      })) : "function" == typeof s && s();
    });
  }
  Ii() {
    null != this.bi && (clearTimeout(this.bi), this.bi = null);
  }
  $i(t = 1e3 * this.yi, s, i, e = !1) {
    this.Ii(), this.bi = window.setTimeout(() => {
      this.refreshFeatureFlags(s, i, e);
    }, t), this.ji = t;
  }
  zi(t) {
    if (!this.wt) return !1;
    if (!t) {
      const t = this.wt.Mi();
      if (null == t) return !1;
      let s = !1;
      if (!isNaN(t)) {
        if (-1 === t) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_8__["default"].j.info("Feature flag refreshes not allowed"), !1;
        s = new Date().getTime() >= (this.gi || 0) + 1e3 * t;
      }
      if (!s) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_8__["default"].j.info(`Feature flag refreshes were rate limited to ${t} seconds`), !1;
    }
    return this.wt.vi();
  }
  Ti() {
    if (!this.u) return;
    const t = {};
    for (const s of this.pi) {
      const i = s.ss();
      t[s.id] = i;
    }
    this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__.STORAGE_KEYS.k.Ri, t), this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_4__.STORAGE_KEYS.k.Ui, this.gi);
  }
}

/***/ }),

/***/ 52609:
/*!*******************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/FeatureFlags/refresh-feature-flags.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   refreshFeatureFlags: () => (/* binding */ refreshFeatureFlags)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feature_flags_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./feature-flags-provider-factory.js */ 19246);


function tr(r, t, a = !1) {
  if (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return _feature_flags_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__["default"].er().refreshFeatureFlags(r, t, a);
}
function refreshFeatureFlags(r, e) {
  tr(r, e);
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tr);

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_FeatureFlags_refresh-feature-flags_js.js.map