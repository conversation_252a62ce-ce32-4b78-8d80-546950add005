﻿using System;
using System.Linq;
using System.Collections.Generic;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Assemblers.Orders;

public class OrdersAssembler
{
    public static OrderShorterDto ConvertOrderEntityToOrderShorterDto(Order entity)
    {
        OrderShorterDto dto = new()
        {
            OrderId = Convert.ToInt64(entity.OrderId),
            SchoolId = entity.SchoolId,
            StudentId = entity.StudentId,
            MenuId = Convert.ToInt64(entity.MenuId),
            MenuType = entity.MenuType,
            OrderDate = DateTime.Parse(entity.OrderDate),
            OrderFee = Convert.ToDecimal(entity.OrderFee),
            OrderStatusId = Convert.ToInt32(entity.OrderStatusId),
            Price = Convert.ToDecimal(entity.Price),
        };

        return dto;
    }

    public static List<OrderShorterDto> ConvertListOrderEntityToListOrderShorterDto(IEnumerable<Order> entities)
    {
        List<OrderShorterDto> dto = new();

        if (entities != null && entities.Any())
        {
            foreach (var e in entities)
            {
                dto.Add(ConvertOrderEntityToOrderShorterDto(e));
            }
        }

        return dto;
    }
}