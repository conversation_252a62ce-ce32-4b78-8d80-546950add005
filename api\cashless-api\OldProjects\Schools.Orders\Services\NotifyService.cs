// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using Schools.BLL.Classes;
// using Schools.BLL.Classes.Notification;
// using Schools.BLL.Exceptions;
// using Microsoft.Extensions.Logging;
// using Schools.BLL.Services.Interfaces;

// namespace Schools.Orders.Services;

// public class NotifyService : INotifyService
// {
//     // PlaceOrderNotification/{orderStatus}
//     // Order stus is 4 = confirmed
//     // To Do: - replace with new endpoint that does not need the order status
//     private const int OrderConfirmedStatus = 4;
//     private readonly ILogger<NotifyService> _logger;
//     private readonly INotificationService _notificationService;

//     public NotifyService(ILogger<NotifyService> logger, INotificationService notificationService)
//     {
//         _logger = logger;
//         _notificationService = notificationService;
//     }

//     public async Task OrderPlacedNotification(IEnumerable<NotificationRequest> request)
//     {
//         if (request == null || !request.Any())
//         {
//             throw new OrderFunctionException("No notifications provided");
//         }

//         await _notificationService.SendNotification(
//             request.Select(i => new OrderUpsertResponse()
//             {
//                 OrderId = i.OrderId
//             }).ToList(), OrderConfirmedStatus);
//     }
// }