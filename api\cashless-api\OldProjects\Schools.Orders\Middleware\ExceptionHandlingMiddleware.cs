using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Schools.BLL.Services;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;

namespace Schools.Orders.Middleware;

/// <summary>
/// Handle all exceptions
/// </summary>
public class ExceptionHandlingMiddleware : IFunctionsWorkerMiddleware
{
    private readonly ITelemetryService _telemetryService;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(ITelemetryService telemetryService, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _telemetryService = telemetryService;
        _logger = logger;
    }

    /// <summary>
    /// Process the function context and log any exceptions to console and to Application Insights
    /// </summary>
    public async Task Invoke(FunctionContext context, FunctionExecutionDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing function: { Function}", context.FunctionDefinition.Name);

            // TODO - Add parameters. If this was a message/event triggered function,
            //        log the incoming message. If this was a HTTP triggered function,
            //        log the request
            _telemetryService.TrackException(ex, new Dictionary<string, string>
            {
                { "FunctionName", context.FunctionDefinition.Name },
            });

            var httpReqData = await context.GetHttpRequestDataAsync();

            if (httpReqData != null)
            {
                // Create an instance of HttpResponseData with 500 status code.
                var newHttpResponse = httpReqData.CreateResponse(HttpStatusCode.InternalServerError);

                // You need to explicitly pass the status code in WriteAsJsonAsync method.
                // https://github.com/Azure/azure-functions-dotnet-worker/issues/776
                await newHttpResponse.WriteAsJsonAsync(new { FooStatus = "Invocation failed!" }, newHttpResponse.StatusCode);
                var invocationResult = context.GetInvocationResult();
                var httpOutputBindingFromMultipleOutputBindings = GetHttpOutputBindingFromMultipleOutputBinding(context);

                if (httpOutputBindingFromMultipleOutputBindings is not null)
                {
                    httpOutputBindingFromMultipleOutputBindings.Value = newHttpResponse;
                }
                else
                {
                    invocationResult.Value = newHttpResponse;
                }
            }
        }
    }

    private OutputBindingData<HttpResponseData> GetHttpOutputBindingFromMultipleOutputBinding(FunctionContext context)
    {
        // The output binding entry name will be "$return" only when the function return type is HttpResponseData
        var httpOutputBinding = context.GetOutputBindings<HttpResponseData>()
            .FirstOrDefault(b => b.BindingType == "http" && b.Name != "$return");

        return httpOutputBinding;
    }
}
