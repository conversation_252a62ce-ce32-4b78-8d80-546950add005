using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Cashless.API.HealthChecks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.HealthChecks;

/// <summary>
/// Operations to use when checking if the Spriggy 
/// Core API is up and running
/// </summary>
public enum ScoreApiOperation
{
    CreateCustomer,
    GetCustomer,
}

/// <summary>
/// Check the Spriggy Core API
/// </summary>
public class ScoreApiHealthCheck : BaseHealthCheck, IHealthCheck
{
    /// <summary>
    /// Services to inject
    /// </summary>
    private readonly IServiceProvider _serviceProvider;
    private readonly ScoreApiOperation _operation;

    public ScoreApiHealthCheck(IServiceProvider serviceProvider, ScoreApiOperation operation)
    {
        _serviceProvider = serviceProvider;
        _operation = operation;
    }

    /// <summary>
    /// Check Spriggy API Calls
    /// </summary>
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            if (_operation == ScoreApiOperation.CreateCustomer)
            {
                return await SendCreateCustomer(stopwatch);
            }

            return await SendGetCustomer(stopwatch);
        }
        catch (Exception ex)
        {
            return GetUnhealthyResult(stopwatch, ex);
        }
    }

    /// <summary>
    /// Call CreateCustomer with an empty response
    /// </summary>
    private async Task<HealthCheckResult> SendCreateCustomer(Stopwatch stopwatch)
    {
        // Send an empty create customer request
        var scoreApiClient = _serviceProvider.GetRequiredService<IScoreApiClient>();
        var response = await scoreApiClient.CreateCustomer(null);

        // Unable to reach spriggy-core
        if (response == null)
        {
            return GetUnhealthyResult(stopwatch, response);
        }

        // Reached spriggy-core but failed to execute the operation
        if (!response.IsSuccess.HasValue || !response.IsSuccess.Value)
        {
            return GetDegradedResult(stopwatch, response);
        }

        return GetHealthyResult(stopwatch, response);
    }

    /// <summary>
    /// Get the customer balance for the Fee Account
    /// </summary>
    private async Task<HealthCheckResult> SendGetCustomer(Stopwatch stopwatch)
    {
        // Send a get balance request on Fee Account
        var billingService = _serviceProvider.GetRequiredService<IBillingService>();
        var feeAccount = await billingService.GetFeeAccount();
        var scoreApiClient = _serviceProvider.GetRequiredService<IScoreApiClient>();
        var response = await scoreApiClient.GetCustomer(feeAccount.ExternalUserId);

        // Unable to reach spriggy-core
        if (response == null)
        {
            return GetUnhealthyResult(stopwatch, response);
        }

        // Reached spriggy-core but failed to execute the operation
        if (!response.IsSuccess.HasValue || !response.IsSuccess.Value)
        {
            return GetDegradedResult(stopwatch, response);
        }

        return GetHealthyResult(stopwatch, response);
    }

    /// <summary>
    /// Get telemetry for logging
    /// </summary>
    private Dictionary<string, object> GetData()
    {
        return new Dictionary<string, object>
        {
            { "operation", _operation }
        };
    }

    /// <summary>
    /// Return a healthy result
    /// </summary>
    private HealthCheckResult GetHealthyResult(Stopwatch stopwatch, object response) => base.GetHealthyResult(stopwatch, response, _operation, GetData());

    /// <summary>
    /// Return a degraded result
    /// </summary>
    private HealthCheckResult GetDegradedResult(Stopwatch stopwatch, object response) => base.GetDegradedResult(stopwatch, response, _operation, GetData());

    /// <summary>
    /// Return an unhealthy result
    /// </summary>
    private HealthCheckResult GetUnhealthyResult(Stopwatch stopwatch, object response) => base.GetUnhealthyResult(stopwatch, response, _operation, GetData());
}
