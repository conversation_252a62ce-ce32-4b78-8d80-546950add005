"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_Feed_ui_show-feed_js"],{

/***/ 33941:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/card-manager-factory.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _card_manager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card-manager.js */ 75946);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);


const n = {
  t: !1,
  i: null,
  m: () => (n.o(), n.i || (n.i = new _card_manager_js__WEBPACK_IMPORTED_MODULE_0__["default"](_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].l())), n.i),
  o: () => {
    n.t || (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].g(n), n.t = !0);
  },
  destroy: () => {
    n.i = null, n.t = !1;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (n);

/***/ }),

/***/ 75946:
/*!**************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/card-manager.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ a)
/* harmony export */ });
/* harmony import */ var _models_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./models/index.js */ 26545);
/* harmony import */ var _common_event_logger_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/event-logger.js */ 86860);
/* harmony import */ var _models_request_result_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../models/request-result.js */ 77299);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../managers/storage-manager.js */ 57309);





class a {
  constructor(s) {
    this.u = s, this.u = s;
  }
  h(n, o) {
    const e = new _models_request_result_js__WEBPACK_IMPORTED_MODULE_0__["default"]();
    if (n.p(), null == n.url || "" === n.url) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Card ${n.id} has no url. Not logging click to Braze servers.`), e;
    if (o && n.id && this.u) {
      const s = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.C) || {};
      s[n.id] = !0, this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.C, s);
    }
    const l = this.I([n]);
    if (null == l) return e;
    const u = o ? _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.$ : _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.B;
    return _common_event_logger_js__WEBPACK_IMPORTED_MODULE_3__["default"].N(u, l);
  }
  A(n) {
    const o = new _models_request_result_js__WEBPACK_IMPORTED_MODULE_0__["default"]();
    if (!n.F()) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Card ${n.id} refused this dismissal. Ignoring analytics event.`), o;
    if (n.id && this.u) {
      const s = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.G) || {};
      s[n.id] = !0, this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.G, s);
    }
    const e = this.I([n]);
    return null == e ? o : _common_event_logger_js__WEBPACK_IMPORTED_MODULE_3__["default"].N(_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.H, e);
  }
  J(n, o) {
    const e = new _models_request_result_js__WEBPACK_IMPORTED_MODULE_0__["default"](!0),
      l = [],
      u = [];
    let a = {};
    this.u && (a = o ? this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.K) || {} : this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.L) || {});
    for (const s of n) s.M(), s instanceof _models_index_js__WEBPACK_IMPORTED_MODULE_4__["default"] ? u.push(s) : l.push(s), s.id && (a[s.id] = !0);
    const h = this.I(l),
      c = this.I(u);
    if (null == h && null == c) return e.O = !1, e;
    if (this.u && (o ? this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.K, a) : this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.k.L, a)), null != h) {
      const t = o ? _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.P : _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.R,
        n = _common_event_logger_js__WEBPACK_IMPORTED_MODULE_3__["default"].N(t, h);
      e.S(n);
    }
    if (null != c && o) {
      const t = _common_event_logger_js__WEBPACK_IMPORTED_MODULE_3__["default"].N(_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.T, c);
      e.S(t);
    }
    return e;
  }
  I(s) {
    let t,
      r = null;
    for (let n = 0; n < s.length; n++) t = s[n].id, null != t && "" !== t && (r = r || {}, r.ids = r.ids || [], r.ids.push(t));
    return r;
  }
}

/***/ }),

/***/ 28748:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/display/card-display.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BOTTOM_IMPRESSION_DATA_ATTRIBUTE: () => (/* binding */ BOTTOM_IMPRESSION_DATA_ATTRIBUTE),
/* harmony export */   TOP_IMPRESSION_DATA_ATTRIBUTE: () => (/* binding */ TOP_IMPRESSION_DATA_ATTRIBUTE),
/* harmony export */   _setImageAltText: () => (/* binding */ _setImageAltText),
/* harmony export */   bottomHadImpression: () => (/* binding */ bottomHadImpression),
/* harmony export */   cardToHtml: () => (/* binding */ cardToHtml),
/* harmony export */   getCardId: () => (/* binding */ getCardId),
/* harmony export */   impressOnBottom: () => (/* binding */ impressOnBottom),
/* harmony export */   impressOnTop: () => (/* binding */ impressOnTop),
/* harmony export */   markCardAsRead: () => (/* binding */ markCardAsRead),
/* harmony export */   setCardHeight: () => (/* binding */ setCardHeight),
/* harmony export */   topHadImpression: () => (/* binding */ topHadImpression)
/* harmony export */ });
/* harmony import */ var _util_component_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/component-utils.js */ 11713);
/* harmony import */ var _util_dom_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../util/dom-utils.js */ 93550);
/* harmony import */ var _Core_handle_braze_action_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../Core/handle-braze-action.js */ 63175);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index.js */ 33565);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared-lib/braze-shared-lib.js */ 37366);





const TOP_IMPRESSION_DATA_ATTRIBUTE = "data-ab-had-top-impression";
const BOTTOM_IMPRESSION_DATA_ATTRIBUTE = "data-ab-had-bottom-impression";
function topHadImpression(t) {
  return null != t && !!t.getAttribute("data-ab-had-top-impression");
}
function impressOnTop(t) {
  null != t && t.setAttribute("data-ab-had-top-impression", "true");
}
function bottomHadImpression(t) {
  return null != t && !!t.getAttribute("data-ab-had-bottom-impression");
}
function impressOnBottom(t) {
  null != t && t.setAttribute("data-ab-had-bottom-impression", "true");
}
function markCardAsRead(t) {
  if (null != t) {
    const o = t.querySelectorAll(".ab-unread-indicator")[0];
    null != o && (o.className += " read");
  }
}
function getCardId(t) {
  return t.getAttribute("data-ab-card-id");
}
function _setImageAltText(t, o) {
  let e = "";
  t.title || t.description || (e = "Feed Image"), o.setAttribute("alt", e);
}
function setCardHeight(t, o) {
  const e = o.querySelectorAll(".ab-image-area");
  let a,
    n = 0;
  e.length > 0 && (n = e[0].offsetWidth);
  for (const o of t) if (a = o._, a && o.imageUrl && "number" == typeof o.aspectRatio) {
    const t = n / o.aspectRatio;
    t && (a.style.height = `${t}px`);
  }
}
function cardToHtml(t, logCardClick, e) {
  const a = document.createElement("div");
  a.className = "ab-card ab-effect-card " + t.U, t.id && (a.setAttribute("data-ab-card-id", t.id), a.setAttribute("id", t.id)), a.setAttribute("role", "article"), a.setAttribute("tabindex", "0");
  let n = "",
    i = !1;
  t.url && "" !== t.url && (n = t.url, i = !0);
  const s = o => (markCardAsRead(a), i && (logCardClick(t), (0,_Core_handle_braze_action_js__WEBPACK_IMPORTED_MODULE_0__._handleBrazeAction)(n, e, o)), !1);
  if (t.pinned) {
    const t = document.createElement("div");
    t.className = "ab-pinned-indicator";
    const o = document.createElement("i");
    o.className = "fa fa-star", t.appendChild(o), a.appendChild(t);
  }
  if (t.imageUrl && "" !== t.imageUrl) {
    const o = document.createElement("div");
    o.className = "ab-image-area";
    const e = document.createElement("img");
    if (e.setAttribute("src", t.imageUrl), e.onload = () => {
      a.style.height = "auto";
    }, _setImageAltText(t, e), o.appendChild(e), a.className += " with-image", i && !t.V) {
      const t = document.createElement("a");
      t.setAttribute("href", n), t.onclick = s, t.appendChild(o), a.appendChild(t);
    } else a.appendChild(o);
  }
  const u = document.createElement("div");
  if (u.className = "ab-card-body", t.dismissible) {
    t.logCardDismissal = () => (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.logCardDismissal)(t);
    const e = (0,_util_component_utils_js__WEBPACK_IMPORTED_MODULE_2__.createCloseButton)("Dismiss Card", void 0, t.dismissCard.bind(t));
    a.appendChild(e), (0,_util_dom_utils_js__WEBPACK_IMPORTED_MODULE_3__.detectSwipe)(u, _util_dom_utils_js__WEBPACK_IMPORTED_MODULE_3__.DIRECTIONS.W, t => {
      a.className += " ab-swiped-left", e.onclick(t);
    }), (0,_util_dom_utils_js__WEBPACK_IMPORTED_MODULE_3__.detectSwipe)(u, _util_dom_utils_js__WEBPACK_IMPORTED_MODULE_3__.DIRECTIONS.X, t => {
      a.className += " ab-swiped-right", e.onclick(t);
    });
  }
  let p = "",
    b = !1;
  if (t.title && "" !== t.title && (p = t.title, b = !0), b) {
    const t = document.createElement("h1");
    if (t.className = "ab-title", t.id = _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].Z.Y(), a.setAttribute("aria-labelledby", t.id), i) {
      const o = document.createElement("a");
      o.setAttribute("href", n), o.onclick = s, o.appendChild(document.createTextNode(p)), t.appendChild(o);
    } else t.appendChild(document.createTextNode(p));
    u.appendChild(t);
  }
  const l = document.createElement("div");
  if (l.className = b ? "ab-description" : "ab-description ab-no-title", l.id = _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].Z.Y(), a.setAttribute("aria-describedby", l.id), t.description && l.appendChild(document.createTextNode(t.description)), i) {
    const o = document.createElement("div");
    o.className = "ab-url-area";
    const e = document.createElement("a");
    e.setAttribute("href", n), t.linkText && e.appendChild(document.createTextNode(t.linkText)), e.onclick = s, o.appendChild(e), l.appendChild(o);
  }
  u.appendChild(l), a.appendChild(u);
  const f = document.createElement("div");
  return f.className = "ab-unread-indicator", t.viewed && (f.className += " read"), a.appendChild(f), t._ = a, a;
}

/***/ }),

/***/ 96704:
/*!****************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/log-card-click.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logCardClick: () => (/* binding */ logCardClick)
/* harmony export */ });
/* harmony import */ var _models_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./models/index.js */ 54681);
/* harmony import */ var _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./card-manager-factory.js */ 33941);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../common/constants.js */ 12238);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);





function logCardClick(o, a) {
  return !!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr() && (o instanceof _models_index_js__WEBPACK_IMPORTED_MODULE_1__["default"] ? _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_2__["default"].m().h(o, a).O : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_3__["default"].j.error("card " + _common_constants_js__WEBPACK_IMPORTED_MODULE_4__.MUST_BE_CARD_WARNING_SUFFIX), !1));
}

/***/ }),

/***/ 33565:
/*!********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/log-card-dismissal.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logCardDismissal: () => (/* binding */ logCardDismissal)
/* harmony export */ });
/* harmony import */ var _models_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./models/index.js */ 54681);
/* harmony import */ var _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./card-manager-factory.js */ 33941);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../common/constants.js */ 12238);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);





function logCardDismissal(o) {
  return !!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr() && (o instanceof _models_index_js__WEBPACK_IMPORTED_MODULE_1__["default"] ? _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_2__["default"].m().A(o).O : (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_3__["default"].j.error("card " + _common_constants_js__WEBPACK_IMPORTED_MODULE_4__.MUST_BE_CARD_WARNING_SUFFIX), !1));
}

/***/ }),

/***/ 52322:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/log-card-impressions.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logCardImpressions: () => (/* binding */ logCardImpressions)
/* harmony export */ });
/* harmony import */ var _models_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./models/index.js */ 54681);
/* harmony import */ var _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./card-manager-factory.js */ 33941);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _util_code_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/code-utils.js */ 92133);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../common/constants.js */ 12238);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);






function logCardImpressions(o, s) {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return !1;
  if (!(0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(o)) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_2__["default"].j.error("cards must be an array"), !1;
  for (const s of o) if (!(s instanceof _models_index_js__WEBPACK_IMPORTED_MODULE_3__["default"])) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_2__["default"].j.error(`Each card in cards ${_common_constants_js__WEBPACK_IMPORTED_MODULE_4__.MUST_BE_CARD_WARNING_SUFFIX}`), !1;
  return _card_manager_factory_js__WEBPACK_IMPORTED_MODULE_5__["default"].m().J(o, s).O;
}

/***/ }),

/***/ 7113:
/*!***************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/banner.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Banner)
/* harmony export */ });
/* harmony import */ var _card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card.js */ 54681);

class Banner extends _card_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(s, t, i, h, r, e, n, a, l, o, u, c, b, d) {
    super(s, t, null, i, null, h, r, e, n, a, l, o, u, c, b, d), this.U = "ab-banner ab-image-only", this.V = !1, this.test = !1;
  }
  ss() {
    const s = {};
    return s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ts] = _card_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.rs, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns] = this.id, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ls] = this.viewed, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.os] = this.imageUrl, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.us] = this.updated, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.cs] = this.created, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.bs] = this.categories, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ds] = this.expiresAt, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.URL] = this.url, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ps] = this.linkText, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.fs] = this.aspectRatio, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.xs] = this.extras, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.gs] = this.pinned, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.js] = this.dismissible, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ys] = this.clicked, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.zs] = this.test, s;
  }
}

/***/ }),

/***/ 63492:
/*!************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/captioned-image.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ CaptionedImage)
/* harmony export */ });
/* harmony import */ var _card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card.js */ 54681);

class CaptionedImage extends _card_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(t, s, i, h, e, r, a, o, c, n, d, p, u, l, m, f) {
    super(t, s, i, h, e, r, a, o, c, n, d, p, u, l, m, f), this.U = "ab-captioned-image", this.V = !0, this.test = !1;
  }
  ss() {
    const t = {};
    return t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ts] = _card_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.tt, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns] = this.id, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ls] = this.viewed, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.st] = this.title, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.os] = this.imageUrl, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.it] = this.description, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.us] = this.updated, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.cs] = this.created, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.bs] = this.categories, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ds] = this.expiresAt, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.URL] = this.url, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ps] = this.linkText, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.fs] = this.aspectRatio, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.xs] = this.extras, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.gs] = this.pinned, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.js] = this.dismissible, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ys] = this.clicked, t[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.zs] = this.test, t;
  }
}

/***/ }),

/***/ 54681:
/*!*************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/card.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Card)
/* harmony export */ });
/* harmony import */ var _util_date_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/date-utils.js */ 10492);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../common/constants.js */ 12238);
/* harmony import */ var _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../managers/subscription-manager.js */ 25235);



class Card {
  constructor(t, i, s, h, n, l, e, r, u, E, o, T, I, a, N, c) {
    this.id = t, this.viewed = i, this.title = s, this.imageUrl = h, this.description = n, this.created = l, this.updated = e, this.categories = r, this.expiresAt = u, this.url = E, this.linkText = o, this.aspectRatio = T, this.extras = I, this.pinned = a, this.dismissible = N, this.clicked = c, this.id = t, this.viewed = i || !1, this.title = s || "", this.imageUrl = h, this.description = n || "", this.created = l || null, this.updated = e || null, this.categories = r || [], this.expiresAt = u || null, this.url = E, this.linkText = o, null == T ? this.aspectRatio = null : (T = parseFloat(T.toString()), this.aspectRatio = isNaN(T) ? null : T), this.extras = I || {}, this.pinned = a || !1, this.dismissible = N || !1, this.dismissed = !1, this.clicked = c || !1, this.isControl = !1, this.test = !1, this.ht = null, this.nt = null;
  }
  subscribeToClickedEvent(t) {
    return this.et().lt(t);
  }
  subscribeToDismissedEvent(t) {
    return this.rt().lt(t);
  }
  removeSubscription(t) {
    this.et().removeSubscription(t), this.rt().removeSubscription(t);
  }
  removeAllSubscriptions() {
    this.et().removeAllSubscriptions(), this.rt().removeAllSubscriptions();
  }
  dismissCard() {
    if (!this.dismissible || this.dismissed) return;
    "function" == typeof this.logCardDismissal && this.logCardDismissal();
    let t = this._;
    !t && this.id && (t = document.getElementById(this.id)), t && (t.style.height = t.offsetHeight + "px", t.className = t.className + " ab-hide", setTimeout(function () {
      t && t.parentNode && (t.style.height = "0", t.style.margin = "0", setTimeout(function () {
        t && t.parentNode && t.parentNode.removeChild(t);
      }, Card.ut));
    }, _common_constants_js__WEBPACK_IMPORTED_MODULE_0__.FEED_ANIMATION_DURATION));
  }
  et() {
    return null == this.ht && (this.ht = new _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__["default"]()), this.ht;
  }
  rt() {
    return null == this.nt && (this.nt = new _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__["default"]()), this.nt;
  }
  M() {
    this.viewed = !0;
  }
  p() {
    this.viewed = !0, this.clicked = !0, this.et().Et();
  }
  F() {
    return !(!this.dismissible || this.dismissed) && (this.dismissed = !0, this.rt().Et(), !0);
  }
  ot(t) {
    if (null == t || t[Card.Tt.ns] !== this.id) return !0;
    if (t[Card.Tt.It]) return !1;
    if (null != t[Card.Tt.us] && null != this.updated && parseInt(t[Card.Tt.us]) < (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.convertMsToSeconds)(this.updated.valueOf())) return !0;
    if (t[Card.Tt.ls] && !this.viewed && (this.viewed = !0), t[Card.Tt.ys] && !this.clicked && (this.clicked = t[Card.Tt.ys]), null != t[Card.Tt.st] && (this.title = t[Card.Tt.st]), null != t[Card.Tt.os] && (this.imageUrl = t[Card.Tt.os]), null != t[Card.Tt.it] && (this.description = t[Card.Tt.it]), null != t[Card.Tt.us]) {
      const i = (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.dateFromUnixTimestamp)(t[Card.Tt.us]);
      null != i && (this.updated = i);
    }
    if (null != t[Card.Tt.ds]) {
      let i;
      i = t[Card.Tt.ds] === Card.Nt ? null : (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.dateFromUnixTimestamp)(t[Card.Tt.ds]), this.expiresAt = i;
    }
    if (null != t[Card.Tt.URL] && (this.url = t[Card.Tt.URL]), null != t[Card.Tt.ps] && (this.linkText = t[Card.Tt.ps]), null != t[Card.Tt.fs]) {
      const i = parseFloat(t[Card.Tt.fs].toString());
      this.aspectRatio = isNaN(i) ? null : i;
    }
    return null != t[Card.Tt.xs] && (this.extras = t[Card.Tt.xs]), null != t[Card.Tt.gs] && (this.pinned = t[Card.Tt.gs]), null != t[Card.Tt.js] && (this.dismissible = t[Card.Tt.js]), null != t[Card.Tt.zs] && (this.test = t[Card.Tt.zs]), !0;
  }
  ss() {
    throw new Error("Must be implemented in a subclass");
  }
}
Card.Nt = -1, Card.Tt = {
  ns: "id",
  ls: "v",
  js: "db",
  It: "r",
  us: "ca",
  gs: "p",
  ds: "ea",
  xs: "e",
  ts: "tp",
  os: "i",
  st: "tt",
  it: "ds",
  URL: "u",
  ps: "dm",
  fs: "ar",
  ys: "cl",
  zs: "t"
}, Card.es = {
  tt: "captioned_image",
  ct: "text_announcement",
  St: "short_news",
  rs: "banner_image",
  At: "control"
}, Card.hs = {
  ns: "id",
  ls: "v",
  js: "db",
  cs: "cr",
  us: "ca",
  gs: "p",
  bs: "t",
  ds: "ea",
  xs: "e",
  ts: "tp",
  os: "i",
  st: "tt",
  it: "ds",
  URL: "u",
  ps: "dm",
  fs: "ar",
  ys: "cl",
  zs: "s"
}, Card.Dt = {
  dt: "ADVERTISING",
  Ct: "ANNOUNCEMENTS",
  Rt: "NEWS",
  bt: "SOCIAL"
}, Card.ut = 400;

/***/ }),

/***/ 64106:
/*!*********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/classic-card.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ClassicCard)
/* harmony export */ });
/* harmony import */ var _card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card.js */ 54681);

class ClassicCard extends _card_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(s, t, i, h, r, c, e, a, o, d, l, n, u, p, f, m) {
    super(s, t, i, h, r, c, e, a, o, d, l, n, u, p, f, m), this.U = "ab-classic-card", this.V = !0;
  }
  ss() {
    const s = {};
    return s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ts] = _card_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.St, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns] = this.id, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ls] = this.viewed, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.st] = this.title, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.os] = this.imageUrl, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.it] = this.description, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.us] = this.updated, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.cs] = this.created, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.bs] = this.categories, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ds] = this.expiresAt, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.URL] = this.url, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ps] = this.linkText, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.fs] = this.aspectRatio, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.xs] = this.extras, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.gs] = this.pinned, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.js] = this.dismissible, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ys] = this.clicked, s[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.zs] = this.test, s;
  }
}

/***/ }),

/***/ 26545:
/*!*********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/control-card.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ControlCard)
/* harmony export */ });
/* harmony import */ var _card_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card.js */ 54681);

class ControlCard extends _card_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(l, t, s, i, n, r) {
    super(l, t, null, null, null, null, s, null, i, null, null, null, n, r), this.isControl = !0, this.U = "ab-control-card", this.V = !1;
  }
  ss() {
    const l = {};
    return l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ts] = _card_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.At, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns] = this.id, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ls] = this.viewed, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.us] = this.updated, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ds] = this.expiresAt, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.xs] = this.extras, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.gs] = this.pinned, l[_card_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.zs] = this.test, l;
  }
}

/***/ }),

/***/ 96034:
/*!*******************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/models/image-only.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ImageOnly)
/* harmony export */ });
/* harmony import */ var _card_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./card.js */ 54681);
/* harmony import */ var _banner_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./banner.js */ 7113);


class ImageOnly extends _banner_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(s, t, i, h, r, e, a, n, o, c, m, b, l, p) {
    super(s, t, i, h, r, e, a, n, o, c, m, b, l, p), this.U = "ab-banner ab-image-only", this.V = !1, this.test = !1;
  }
  ss() {
    const s = {};
    return s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ts] = _card_js__WEBPACK_IMPORTED_MODULE_1__["default"].es.rs, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ns] = this.id, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ls] = this.viewed, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.os] = this.imageUrl, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.us] = this.updated, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.cs] = this.created, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.bs] = this.categories, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ds] = this.expiresAt, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.URL] = this.url, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ps] = this.linkText, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.fs] = this.aspectRatio, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.xs] = this.extras, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.gs] = this.pinned, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.js] = this.dismissible, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.ys] = this.clicked, s[_card_js__WEBPACK_IMPORTED_MODULE_1__["default"].hs.zs] = this.test, s;
  }
}

/***/ }),

/***/ 43796:
/*!*******************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Card/util/card-factory.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   newCard: () => (/* binding */ newCard),
/* harmony export */   newCardFromContentCardsJson: () => (/* binding */ newCardFromContentCardsJson),
/* harmony export */   newCardFromFeedJson: () => (/* binding */ newCardFromFeedJson),
/* harmony export */   newCardFromSerializedValue: () => (/* binding */ newCardFromSerializedValue)
/* harmony export */ });
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../index.js */ 54681);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index.js */ 64106);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../index.js */ 63492);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../index.js */ 96034);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../index.js */ 26545);
/* harmony import */ var _util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../util/date-utils.js */ 10492);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared-lib/braze-shared-lib.js */ 37366);



function newCard(e, n, t, o, i, l, u, d, a, s, w, f, m, C, p, c, x, F) {
  let b;
  if (n === _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.ct || n === _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.St) b = new _index_js__WEBPACK_IMPORTED_MODULE_1__["default"](e, t, o, i, l, u, d, a, s, w, f, m, C, p, c, x);else if (n === _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.tt) b = new _index_js__WEBPACK_IMPORTED_MODULE_2__["default"](e, t, o, i, l, u, d, a, s, w, f, m, C, p, c, x);else if (n === _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.rs) b = new _index_js__WEBPACK_IMPORTED_MODULE_3__["default"](e, t, i, u, d, a, s, w, f, m, C, p, c, x);else {
    if (n !== _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].es.At) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].j.error("Ignoring card with unknown type " + n), null;
    b = new _index_js__WEBPACK_IMPORTED_MODULE_5__["default"](e, t, d, s, C, p);
  }
  return F && (b.test = F), b;
}
function newCardFromContentCardsJson(e) {
  if (e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.It]) return null;
  const n = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ns],
    r = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ts],
    t = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ls],
    o = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.st],
    i = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.os],
    u = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.it],
    d = (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.dateFromUnixTimestamp)(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.us]),
    a = d;
  let s;
  s = e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ds] === _index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Nt ? null : (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.dateFromUnixTimestamp)(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ds]);
  return newCard(n, r, t, o, i, u, a, d, null, s, e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.URL], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ps], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.fs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.xs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.gs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.js], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.ys], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].Tt.zs] || !1);
}
function newCardFromFeedJson(e) {
  return newCard(e.id, e.type, e.viewed, e.title, e.image, e.description, (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.dateFromUnixTimestamp)(e.created), (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.dateFromUnixTimestamp)(e.updated), e.categories, (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.dateFromUnixTimestamp)(e.expires_at), e.url, e.domain, e.aspect_ratio, e.extras, !1, !1);
}
function newCardFromSerializedValue(e) {
  return newCard(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ns], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ts], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ls], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.st], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.os], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.it], (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.rehydrateDateAfterJsonization)(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.cs]), (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.rehydrateDateAfterJsonization)(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.us]), e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.bs], (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_6__.rehydrateDateAfterJsonization)(e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ds]), e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.URL], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ps], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.fs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.xs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.gs], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.js], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.ys], e[_index_js__WEBPACK_IMPORTED_MODULE_0__["default"].hs.zs] || !1) || void 0;
}

/***/ }),

/***/ 66687:
/*!*********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Core/remove-subscription.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   removeSubscription: () => (/* binding */ removeSubscription)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);

function removeSubscription(r) {
  _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr() && _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].removeSubscription(r);
}

/***/ }),

/***/ 18379:
/*!***********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/feed-provider-factory.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feed_provider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./feed-provider.js */ 1856);


const ie = {
  t: !1,
  provider: null,
  er: () => (ie.o(), ie.provider || (ie.provider = new _feed_provider_js__WEBPACK_IMPORTED_MODULE_0__["default"](_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].l(), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].cr()), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].dr(ie.provider)), ie.provider),
  o: () => {
    ie.t || (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].g(ie), ie.t = !0);
  },
  destroy: () => {
    ie.provider = null, ie.t = !1;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ie);

/***/ }),

/***/ 1856:
/*!***************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/feed-provider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ re)
/* harmony export */ });
/* harmony import */ var _common_base_provider_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common/base-provider.js */ 72406);
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feed_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./feed.js */ 15892);
/* harmony import */ var _Card_util_card_factory_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Card/util/card-factory.js */ 43796);
/* harmony import */ var _util_date_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/date-utils.js */ 10492);
/* harmony import */ var _managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../managers/storage-manager.js */ 57309);
/* harmony import */ var _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/subscription-manager.js */ 25235);







class re extends _common_base_provider_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(t, s) {
    super(), this.u = t, this.ki = s, this.cards = [], this.Ai = null, this.u = t, this.ki = s, this.yt = new _managers_subscription_manager_js__WEBPACK_IMPORTED_MODULE_1__["default"](), _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_2__["default"].jt(this.yt), this.Lt();
  }
  Lt() {
    let t = [];
    this.u && (t = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Bi) || []);
    const s = [];
    for (let i = 0; i < t.length; i++) {
      const e = (0,_Card_util_card_factory_js__WEBPACK_IMPORTED_MODULE_4__.newCardFromSerializedValue)(t[i]);
      null != e && s.push(e);
    }
    this.cards = s, this.u && (this.Ai = (0,_util_date_utils_js__WEBPACK_IMPORTED_MODULE_5__.rehydrateDateAfterJsonization)(this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Ei)));
  }
  Gi(t) {
    const s = [];
    let e = null,
      r = {};
    this.u && (r = this.u.v(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.L) || {});
    const h = {};
    for (let i = 0; i < t.length; i++) {
      e = t[i];
      const o = (0,_Card_util_card_factory_js__WEBPACK_IMPORTED_MODULE_4__.newCardFromFeedJson)(e);
      if (null != o) {
        const t = o.id;
        t && r[t] && (o.viewed = !0, h[t] = !0), s.push(o);
      }
    }
    this.cards = s, this.Rs(), this.Ai = new Date(), this.u && (this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.L, h), this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Ei, this.Ai));
  }
  Rs() {
    if (!this.u) return;
    const t = [];
    for (let s = 0; s < this.cards.length; s++) t.push(this.cards[s].ss());
    this.u.D(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Bi, t);
  }
  Ts(t) {
    null != t && t.feed && (this.Lt(), this.Gi(t.feed), this.yt.Et(new _feed_js__WEBPACK_IMPORTED_MODULE_6__["default"](this.cards.slice(), this.Ai)));
  }
  Hi() {
    this.Lt();
    const t = [],
      s = new Date();
    for (let i = 0; i < this.cards.length; i++) {
      const e = this.cards[i].expiresAt;
      let r = !0;
      null != e && (r = e >= s), r && t.push(this.cards[i]);
    }
    return new _feed_js__WEBPACK_IMPORTED_MODULE_6__["default"](t, this.Ai);
  }
  Ms() {
    this.ki && this.ki.requestFeedRefresh();
  }
  ri(t) {
    return this.yt.lt(t);
  }
  clearData(t) {
    null == t && (t = !1), this.cards = [], this.Ai = null, t && this.u && (this.u.ni(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Bi), this.u.ni(_managers_storage_manager_js__WEBPACK_IMPORTED_MODULE_3__.STORAGE_KEYS.k.Ei)), this.yt.Et(new _feed_js__WEBPACK_IMPORTED_MODULE_6__["default"](this.cards.slice(), this.Ai));
  }
}

/***/ }),

/***/ 15892:
/*!******************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/feed.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Feed)
/* harmony export */ });
/* harmony import */ var _common_base_feed_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common/base-feed.js */ 90315);
/* harmony import */ var _Card_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Card/index.js */ 52322);
/* harmony import */ var _Card_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Card/index.js */ 96704);
/* harmony import */ var _request_feed_refresh_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./request-feed-refresh.js */ 44094);



class Feed extends _common_base_feed_js__WEBPACK_IMPORTED_MODULE_0__["default"] {
  constructor(r, e) {
    super(r, e);
  }
  logCardImpressions(r) {
    (0,_Card_index_js__WEBPACK_IMPORTED_MODULE_1__.logCardImpressions)(r, !1);
  }
  logCardClick(r) {
    return (0,_Card_index_js__WEBPACK_IMPORTED_MODULE_2__.logCardClick)(r, !1);
  }
  sr() {
    (0,_request_feed_refresh_js__WEBPACK_IMPORTED_MODULE_3__.requestFeedRefresh)();
  }
  ur() {
    return !1;
  }
}

/***/ }),

/***/ 31083:
/*!********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/log-feed-displayed.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logFeedDisplayed: () => (/* binding */ logFeedDisplayed)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);


function logFeedDisplayed() {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return;
  const i = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].ar();
  return i ? i.qr(_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].Cr.Br).O : void 0;
}

/***/ }),

/***/ 44094:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/request-feed-refresh.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   requestFeedRefresh: () => (/* binding */ requestFeedRefresh)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./feed-provider-factory.js */ 18379);


function requestFeedRefresh() {
  if (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__["default"].er().Ms();
}

/***/ }),

/***/ 41228:
/*!***************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/subscribe-to-feed-updates.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   subscribeToFeedUpdates: () => (/* binding */ subscribeToFeedUpdates)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./feed-provider-factory.js */ 18379);


function subscribeToFeedUpdates(r) {
  if (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_1__["default"].er().ri(r);
}

/***/ }),

/***/ 61129:
/*!**************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Feed/ui/show-feed.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   showFeed: () => (/* binding */ showFeed)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../managers/braze-instance.js */ 30186);
/* harmony import */ var _common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../common/feed-display.js */ 63752);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../index.js */ 15892);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../index.js */ 41228);
/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../index.js */ 31083);
/* harmony import */ var _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../feed-provider-factory.js */ 18379);
/* harmony import */ var _util_code_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../util/code-utils.js */ 92133);
/* harmony import */ var _Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../Card/display/card-display.js */ 28748);
/* harmony import */ var _ui_js_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../ui/js/index.js */ 53010);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../shared-lib/braze-shared-lib.js */ 37366);








function showFeed(t, n, o) {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return;
  (0,_ui_js_index_js__WEBPACK_IMPORTED_MODULE_1__.setupFeedUI)();
  const s = (e, t) => {
      if (null == t) return e;
      const n = [];
      for (let e = 0; e < t.length; e++) n.push(t[e].toLowerCase());
      const o = [];
      for (let t = 0; t < e.length; t++) {
        const r = [],
          s = e[t].categories || [];
        for (let e = 0; e < s.length; e++) r.push(s[e].toLowerCase());
        (0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_2__.intersection)(r, n).length > 0 && o.push(e[t]);
      }
      return o;
    },
    i = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__.OPTIONS.tn) || _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__.OPTIONS.en) || !1;
  let l = !1;
  null == t && (t = document.body, l = !0);
  let a,
    f = !1;
  null == n ? (a = _feed_provider_factory_js__WEBPACK_IMPORTED_MODULE_3__["default"].er().Hi(), (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.updateFeedCards)(a, s(a.cards, o), a.lastUpdated, null, i), f = !0) : a = new _index_js__WEBPACK_IMPORTED_MODULE_5__["default"](s(n, o), new Date());
  const u = (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.feedToHtml)(a, i, l);
  if (f) {
    (null == a.lastUpdated || new Date().valueOf() - a.lastUpdated.valueOf() > _index_js__WEBPACK_IMPORTED_MODULE_5__["default"].mr) && (_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_6__["default"].j.info(`Cached feed was older than max TTL of ${_index_js__WEBPACK_IMPORTED_MODULE_5__["default"].mr} ms, requesting an update from the server.`), (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.refreshFeed)(a, u));
    const e = new Date().valueOf(),
      t = (0,_index_js__WEBPACK_IMPORTED_MODULE_7__.subscribeToFeedUpdates)(function (t) {
        const n = u.querySelectorAll(".ab-refresh-button")[0];
        if (null != n) {
          let t = 500;
          t -= new Date().valueOf() - e;
          const o = u.getAttribute(_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.LAST_REQUESTED_REFRESH_DATA_ATTRIBUTE);
          if (o) {
            const e = parseInt(o);
            isNaN(e) || (t -= new Date().valueOf() - e);
          }
          setTimeout(function () {
            n.className = n.className.replace(/fa-spin/g, "");
          }, Math.max(t, 0));
        }
        (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.updateFeedCards)(a, s(t.cards, o), t.lastUpdated, u, i);
      });
    (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.registerFeedSubscriptionId)(t, u);
  }
  const d = e => {
    const t = e.querySelectorAll(".ab-feed");
    let n = null;
    for (let o = 0; o < t.length; o++) t[o].parentNode === e && (n = t[o]);
    null != n ? ((0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.destroyFeedHtml)(n), n.parentNode && n.parentNode.replaceChild(u, n)) : e.appendChild(u), setTimeout(function () {
      u.className = u.className.replace("ab-hide", "ab-show");
    }, 0), l && u.focus(), (0,_index_js__WEBPACK_IMPORTED_MODULE_8__.logFeedDisplayed)(), (0,_common_feed_display_js__WEBPACK_IMPORTED_MODULE_4__.detectFeedImpressions)(a, u), a && (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_9__.setCardHeight)(a.cards, e);
  };
  var m;
  null != t ? d(t) : window.onload = (m = window.onload, function () {
    "function" == typeof m && m(new Event("oldLoad")), d(document.body);
  });
}

/***/ }),

/***/ 90315:
/*!*************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/common/base-feed.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FEED_ANIMATION_DURATION: () => (/* binding */ FEED_ANIMATION_DURATION),
/* harmony export */   "default": () => (/* binding */ x)
/* harmony export */ });
/* harmony import */ var _Card_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Card/index.js */ 26545);

const FEED_ANIMATION_DURATION = 500;
class x {
  constructor(e, s) {
    this.cards = e, this.lastUpdated = s, this.cards = e, this.lastUpdated = s;
  }
  getUnreadCardCount() {
    let e = 0;
    for (const s of this.cards) s.viewed || s instanceof _Card_index_js__WEBPACK_IMPORTED_MODULE_0__["default"] || e++;
    return e;
  }
  ur() {
    throw new Error("Must be implemented in a subclass");
  }
  logCardImpressions(e) {
    throw new Error("Must be implemented in a subclass");
  }
  logCardClick(e) {
    throw new Error("Must be implemented in a subclass");
  }
  sr() {
    throw new Error("Must be implemented in a subclass");
  }
}
x.mr = 6e4, x.Oh = 500, x.uo = 1e4;

/***/ }),

/***/ 63752:
/*!****************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/common/feed-display.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LAST_REQUESTED_REFRESH_DATA_ATTRIBUTE: () => (/* binding */ LAST_REQUESTED_REFRESH_DATA_ATTRIBUTE),
/* harmony export */   SCROLL_LISTENER_ID: () => (/* binding */ SCROLL_LISTENER_ID),
/* harmony export */   SUBSCRIPTION_ID_DATA_ATTRIBUTE: () => (/* binding */ SUBSCRIPTION_ID_DATA_ATTRIBUTE),
/* harmony export */   destroyFeedHtml: () => (/* binding */ destroyFeedHtml),
/* harmony export */   detectFeedImpressions: () => (/* binding */ detectFeedImpressions),
/* harmony export */   feedToHtml: () => (/* binding */ feedToHtml),
/* harmony export */   generateFeedBody: () => (/* binding */ generateFeedBody),
/* harmony export */   refreshFeed: () => (/* binding */ refreshFeed),
/* harmony export */   registerFeedSubscriptionId: () => (/* binding */ registerFeedSubscriptionId),
/* harmony export */   scrollListeners: () => (/* binding */ scrollListeners),
/* harmony export */   updateFeedCards: () => (/* binding */ updateFeedCards)
/* harmony export */ });
/* harmony import */ var _base_feed_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-feed.js */ 90315);
/* harmony import */ var _Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Card/display/card-display.js */ 28748);
/* harmony import */ var _util_dom_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/dom-utils.js */ 93550);
/* harmony import */ var _Card_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Card/index.js */ 26545);
/* harmony import */ var _Card_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../Card/index.js */ 54681);
/* harmony import */ var _util_code_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/code-utils.js */ 92133);
/* harmony import */ var _util_key_codes_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util/key-codes.js */ 26450);
/* harmony import */ var _l10n_l10n_manager_factory_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../l10n/l10n-manager-factory.js */ 27663);
/* harmony import */ var _Core_remove_subscription_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Core/remove-subscription.js */ 66687);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _util_validation_utils_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../util/validation-utils.js */ 27607);
/* harmony import */ var _util_braze_actions_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../util/braze-actions.js */ 23974);












const LAST_REQUESTED_REFRESH_DATA_ATTRIBUTE = "data-last-requested-refresh";
const SUBSCRIPTION_ID_DATA_ATTRIBUTE = "data-update-subscription-id";
const SCROLL_LISTENER_ID = "data-listener-id";
const scrollListeners = {};
function destroyFeedHtml(e) {
  e && (e.className = e.className.replace("ab-show", "ab-hide"), setTimeout(() => {
    e && e.parentNode && e.parentNode.removeChild(e);
  }, _base_feed_js__WEBPACK_IMPORTED_MODULE_0__["default"].Oh));
  const t = e.getAttribute("data-update-subscription-id");
  null != t && (0,_Core_remove_subscription_js__WEBPACK_IMPORTED_MODULE_1__.removeSubscription)(t);
  const o = e.getAttribute("data-listener-id");
  null != o && (window.removeEventListener("scroll", scrollListeners[o]), delete scrollListeners[o]);
}
function generateFeedBody(e, t) {
  const o = document.createElement("div");
  if (o.className = "ab-feed-body", o.setAttribute("aria-label", "Feed"), o.setAttribute("role", "feed"), null == e.lastUpdated) {
    const e = document.createElement("div");
    e.className = "ab-no-cards-message";
    const t = document.createElement("i");
    t.className = "fa fa-spinner fa-spin fa-4x ab-initial-spinner", e.appendChild(t), o.appendChild(e);
  } else {
    let s = !1;
    const logCardClick = t => e.logCardClick(t);
    for (const n of e.cards) {
      const i = n instanceof _Card_index_js__WEBPACK_IMPORTED_MODULE_2__["default"];
      !i || e.ur() ? (o.appendChild((0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.cardToHtml)(n, logCardClick, t)), s = s || !i) : _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].j.error("Received a control card for a legacy news feed. Control cards are only supported with content cards.");
    }
    if (!s) {
      const e = document.createElement("div");
      e.className = "ab-no-cards-message", e.innerHTML = _l10n_l10n_manager_factory_js__WEBPACK_IMPORTED_MODULE_5__["default"].m().get("NO_CARDS_MESSAGE") || "", e.setAttribute("role", "article"), o.appendChild(e);
    }
  }
  return o;
}
function detectFeedImpressions(e, t) {
  if (null != e && null != t) {
    const o = [],
      s = t.querySelectorAll(".ab-card");
    e.yo || (e.yo = {});
    for (let t = 0; t < s.length; t++) {
      const n = (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.getCardId)(s[t]),
        r = (0,_util_dom_utils_js__WEBPACK_IMPORTED_MODULE_6__.topIsInView)(s[t]),
        i = (0,_util_dom_utils_js__WEBPACK_IMPORTED_MODULE_6__.bottomIsInView)(s[t]);
      if (e.yo[n]) {
        r || i || (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.markCardAsRead)(s[t]);
        continue;
      }
      let a = (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.topHadImpression)(s[t]),
        d = (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.bottomHadImpression)(s[t]);
      const l = a,
        c = d;
      if (!a && r && (a = !0, (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.impressOnTop)(s[t])), !d && i && (d = !0, (0,_Card_display_card_display_js__WEBPACK_IMPORTED_MODULE_3__.impressOnBottom)(s[t])), a && d) {
        if (l && c) continue;
        for (const t of e.cards) if (t.id === n) {
          e.yo[t.id] = !0, o.push(t);
          break;
        }
      }
    }
    o.length > 0 && e.logCardImpressions(o);
  }
}
function refreshFeed(e, t) {
  if (null == e || null == t) return;
  t.setAttribute("aria-busy", "true");
  const o = t.querySelectorAll(".ab-refresh-button")[0];
  null != o && (o.className += " fa-spin");
  const s = new Date().valueOf().toString();
  t.setAttribute("data-last-requested-refresh", s), setTimeout(() => {
    if (t.getAttribute("data-last-requested-refresh") === s) {
      const e = t.querySelectorAll(".fa-spin");
      for (let t = 0; t < e.length; t++) e[t].className = e[t].className.replace(/fa-spin/g, "");
      const o = t.querySelectorAll(".ab-initial-spinner")[0];
      if (null != o) {
        const e = document.createElement("span");
        e.innerHTML = _l10n_l10n_manager_factory_js__WEBPACK_IMPORTED_MODULE_5__["default"].m().get("FEED_TIMEOUT_MESSAGE") || "", null != o.parentNode && (o.parentNode.appendChild(e), o.parentNode.removeChild(o));
      }
      "true" === t.getAttribute("aria-busy") && t.setAttribute("aria-busy", "false");
    }
  }, _base_feed_js__WEBPACK_IMPORTED_MODULE_0__["default"].uo), e.sr();
}
function feedToHtml(e, t, o) {
  const s = document.createElement("div");
  s.className = "ab-feed ab-hide ab-effect-slide", s.setAttribute("role", "dialog"), s.setAttribute("aria-label", "Feed"), s.setAttribute("tabindex", "-1");
  const n = document.createElement("div");
  n.className = "ab-feed-buttons-wrapper", n.setAttribute("role", "group"), s.appendChild(n);
  const i = document.createElement("i");
  i.className = "fa fa-times ab-close-button", i.setAttribute("aria-label", "Close Feed"), i.setAttribute("tabindex", "0"), i.setAttribute("role", "button");
  const a = e => {
    destroyFeedHtml(s), e.stopPropagation();
  };
  i.addEventListener("keydown", e => {
    e.keyCode !== _util_key_codes_js__WEBPACK_IMPORTED_MODULE_7__.KeyCodes.Fo && e.keyCode !== _util_key_codes_js__WEBPACK_IMPORTED_MODULE_7__.KeyCodes.To || a(e);
  }), i.onclick = a;
  const d = document.createElement("i");
  d.className = "fa fa-refresh ab-refresh-button", e && null == e.lastUpdated && (d.className += " fa-spin"), d.setAttribute("aria-label", "Refresh Feed"), d.setAttribute("tabindex", "0"), d.setAttribute("role", "button");
  const l = t => {
    refreshFeed(e, s), t.stopPropagation();
  };
  d.addEventListener("keydown", e => {
    e.keyCode !== _util_key_codes_js__WEBPACK_IMPORTED_MODULE_7__.KeyCodes.Fo && e.keyCode !== _util_key_codes_js__WEBPACK_IMPORTED_MODULE_7__.KeyCodes.To || l(e);
  }), d.onclick = l, n.appendChild(d), n.appendChild(i), s.appendChild(generateFeedBody(e, t));
  const c = () => detectFeedImpressions(e, s);
  if (s.addEventListener("scroll", c), !o) {
    window.addEventListener("scroll", c);
    const e = _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].Z.Y();
    scrollListeners[e] = c, s.setAttribute("data-listener-id", e);
  }
  return s;
}
function updateFeedCards(e, t, o, s, n) {
  if (!(0,_util_code_utils_js__WEBPACK_IMPORTED_MODULE_8__.isArray)(t)) return;
  const i = [];
  for (const e of t) if (e instanceof _Card_index_js__WEBPACK_IMPORTED_MODULE_9__["default"]) {
    if (e.url && _util_validation_utils_js__WEBPACK_IMPORTED_MODULE_10__.BRAZE_ACTION_URI_REGEX.test(e.url)) {
      const t = (0,_util_braze_actions_js__WEBPACK_IMPORTED_MODULE_11__.getDecodedBrazeAction)(e.url);
      if ((0,_util_braze_actions_js__WEBPACK_IMPORTED_MODULE_11__.containsUnknownBrazeAction)(t)) {
        _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_4__["default"].j.error((0,_util_braze_actions_js__WEBPACK_IMPORTED_MODULE_11__.ineligibleBrazeActionURLErrorMessage)(_util_braze_actions_js__WEBPACK_IMPORTED_MODULE_11__.INELIGIBLE_BRAZE_ACTION_URL_ERROR_TYPES._r, "Content Card"));
        continue;
      }
    }
    i.push(e);
  }
  if (e.cards = i, e.lastUpdated = o, null != s) if (s.setAttribute("aria-busy", "false"), null == e.lastUpdated) destroyFeedHtml(s);else {
    const t = s.querySelectorAll(".ab-feed-body")[0];
    if (null != t) {
      const o = generateFeedBody(e, n);
      t.parentNode && t.parentNode.replaceChild(o, t), detectFeedImpressions(e, o.parentNode);
    }
  }
}
function registerFeedSubscriptionId(e, t) {
  e && t.setAttribute("data-update-subscription-id", e);
}

/***/ }),

/***/ 82167:
/*!****************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/common/translations.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  en: {
    NO_CARDS_MESSAGE: "We have no updates for you at this time.<br/>Please check again later.",
    FEED_TIMEOUT_MESSAGE: "Sorry, this refresh timed out.<br/>Please try again later."
  },
  ar: {
    NO_CARDS_MESSAGE: "ليس لدينا أي تحديث. يرجى التحقق مرة أخرى لاحقاً",
    FEED_TIMEOUT_MESSAGE: "يرجى تكرار المحاولة لاحقا"
  },
  cs: {
    NO_CARDS_MESSAGE: "V tuto chvíli pro vás nemáme žádné aktualizace.<br/>Zkontrolujte prosím znovu později.",
    FEED_TIMEOUT_MESSAGE: "Prosím zkuste to znovu později."
  },
  da: {
    NO_CARDS_MESSAGE: "Vi har ingen updates.<br/>Prøv venligst senere.",
    FEED_TIMEOUT_MESSAGE: "Prøv venligst senere."
  },
  de: {
    NO_CARDS_MESSAGE: "Derzeit sind keine Updates verfügbar.<br/>Bitte später noch einmal versuchen.",
    FEED_TIMEOUT_MESSAGE: "Bitte später noch einmal versuchen."
  },
  es: {
    NO_CARDS_MESSAGE: "No tenemos actualizaciones.<br/>Por favor compruébelo más tarde.",
    FEED_TIMEOUT_MESSAGE: "Por favor inténtelo más tarde."
  },
  "es-mx": {
    NO_CARDS_MESSAGE: "No tenemos ninguna actualización.<br/>Vuelva a verificar más tarde.",
    FEED_TIMEOUT_MESSAGE: "Por favor, vuelva a intentarlo más tarde."
  },
  et: {
    NO_CARDS_MESSAGE: "Uuendusi pole praegu saadaval.<br/>Proovige hiljem uuesti.",
    FEED_TIMEOUT_MESSAGE: "Palun proovige hiljem uuesti."
  },
  fi: {
    NO_CARDS_MESSAGE: "Päivityksiä ei ole saatavilla.<br/>Tarkista myöhemmin uudelleen.",
    FEED_TIMEOUT_MESSAGE: "Yritä myöhemmin uudelleen."
  },
  fr: {
    NO_CARDS_MESSAGE: "Aucune mise à jour disponible.<br/>Veuillez vérifier ultérieurement.",
    FEED_TIMEOUT_MESSAGE: "Veuillez réessayer ultérieurement."
  },
  he: {
    NO_CARDS_MESSAGE: ".אין לנו עדכונים. בבקשה בדוק שוב בקרוב",
    FEED_TIMEOUT_MESSAGE: ".בבקשה נסה שוב בקרוב"
  },
  hi: {
    NO_CARDS_MESSAGE: "हमारे पास कोई अपडेट नहीं हैं। कृपया बाद में फिर से जाँच करें.।",
    FEED_TIMEOUT_MESSAGE: "कृपया बाद में दोबारा प्रयास करें।."
  },
  id: {
    NO_CARDS_MESSAGE: "Kami tidak memiliki pembaruan. Coba lagi nanti.",
    FEED_TIMEOUT_MESSAGE: "Coba lagi nanti."
  },
  it: {
    NO_CARDS_MESSAGE: "Non ci sono aggiornamenti.<br/>Ricontrollare più tardi.",
    FEED_TIMEOUT_MESSAGE: "Riprovare più tardi."
  },
  ja: {
    NO_CARDS_MESSAGE: "アップデートはありません。<br/>後でもう一度確認してください。",
    FEED_TIMEOUT_MESSAGE: "後でもう一度試してください。"
  },
  ko: {
    NO_CARDS_MESSAGE: "업데이트가 없습니다. 다음에 다시 확인해 주십시오.",
    FEED_TIMEOUT_MESSAGE: "나중에 다시 시도해 주십시오."
  },
  ms: {
    NO_CARDS_MESSAGE: "Tiada kemas kini. Sila periksa kemudian.",
    FEED_TIMEOUT_MESSAGE: "Sila cuba kemudian."
  },
  nl: {
    NO_CARDS_MESSAGE: "Er zijn geen updates.<br/>Probeer het later opnieuw.",
    FEED_TIMEOUT_MESSAGE: "Probeer het later opnieuw."
  },
  no: {
    NO_CARDS_MESSAGE: "Vi har ingen oppdateringer.<br/>Vennligst sjekk igjen senere.",
    FEED_TIMEOUT_MESSAGE: "Vennligst prøv igjen senere."
  },
  pl: {
    NO_CARDS_MESSAGE: "Brak aktualizacji.<br/>Proszę sprawdzić ponownie później.",
    FEED_TIMEOUT_MESSAGE: "Proszę spróbować ponownie później."
  },
  pt: {
    NO_CARDS_MESSAGE: "Não temos atualizações.<br/>Por favor, verifique mais tarde.",
    FEED_TIMEOUT_MESSAGE: "Por favor, tente mais tarde."
  },
  "pt-br": {
    NO_CARDS_MESSAGE: "Não temos nenhuma atualização.<br/>Verifique novamente mais tarde.",
    FEED_TIMEOUT_MESSAGE: "Tente novamente mais tarde."
  },
  ru: {
    NO_CARDS_MESSAGE: "Обновления недоступны.<br/>Пожалуйста, проверьте снова позже.",
    FEED_TIMEOUT_MESSAGE: "Пожалуйста, повторите попытку позже."
  },
  sv: {
    NO_CARDS_MESSAGE: "Det finns inga uppdateringar.<br/>Försök igen senare.",
    FEED_TIMEOUT_MESSAGE: "Försök igen senare."
  },
  th: {
    NO_CARDS_MESSAGE: "เราไม่มีการอัพเดต กรุณาตรวจสอบภายหลัง.",
    FEED_TIMEOUT_MESSAGE: "กรุณาลองใหม่ภายหลัง."
  },
  uk: {
    NO_CARDS_MESSAGE: "Оновлення недоступні.<br/>ласка, перевірте знову пізніше.",
    FEED_TIMEOUT_MESSAGE: "Будь ласка, спробуйте ще раз пізніше."
  },
  vi: {
    NO_CARDS_MESSAGE: "Chúng tôi không có cập nhật nào.<br/>Vui lòng kiểm tra lại sau.",
    FEED_TIMEOUT_MESSAGE: "Vui lòng thử lại sau."
  },
  "zh-hk": {
    NO_CARDS_MESSAGE: "暫時沒有更新.<br/>請稍候再試.",
    FEED_TIMEOUT_MESSAGE: "請稍候再試."
  },
  "zh-hans": {
    NO_CARDS_MESSAGE: "暂时没有更新.<br/>请稍后再试.",
    FEED_TIMEOUT_MESSAGE: "请稍候再试."
  },
  "zh-hant": {
    NO_CARDS_MESSAGE: "暫時沒有更新.<br/>請稍候再試.",
    FEED_TIMEOUT_MESSAGE: "請稍候再試."
  },
  "zh-tw": {
    NO_CARDS_MESSAGE: "暫時沒有更新.<br/>請稍候再試.",
    FEED_TIMEOUT_MESSAGE: "請稍候再試."
  },
  zh: {
    NO_CARDS_MESSAGE: "暂时没有更新.<br/>请稍后再试.",
    FEED_TIMEOUT_MESSAGE: "请稍候再试."
  }
});

/***/ }),

/***/ 27663:
/*!**********************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/l10n/l10n-manager-factory.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _util_browser_detector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/browser-detector.js */ 90049);
/* harmony import */ var _l10n_manager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./l10n-manager.js */ 33300);



const Ce = {
  t: !1,
  i: null,
  m: () => {
    if (Ce.o(), !Ce.i) {
      let r = _util_browser_detector_js__WEBPACK_IMPORTED_MODULE_0__["default"].language,
        t = !1;
      _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS.Ba) && (r = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].nn(_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__.OPTIONS.Ba), t = !0), Ce.i = new _l10n_manager_js__WEBPACK_IMPORTED_MODULE_2__["default"](r, t);
    }
    return Ce.i;
  },
  o: () => {
    Ce.t || (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_1__["default"].g(Ce), Ce.t = !0);
  },
  destroy: () => {
    Ce.i = null, Ce.t = !1;
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Ce);

/***/ }),

/***/ 33300:
/*!**************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/l10n/l10n-manager.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ nr)
/* harmony export */ });
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _common_translations_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common/translations.js */ 82167);


class nr {
  constructor(t, l = !1) {
    if (this.language = t, null != t && (t = t.toLowerCase()), null != t && null == _common_translations_js__WEBPACK_IMPORTED_MODULE_0__["default"][t]) {
      const l = t.indexOf("-");
      l > 0 && (t = t.substring(0, l));
    }
    if (null == _common_translations_js__WEBPACK_IMPORTED_MODULE_0__["default"][t]) {
      const a = "Braze does not yet have a localization for language " + t + ", defaulting to English. Please contact us if you are willing and able to help us translate our SDK into this language.";
      l ? _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(a) : _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(a), t = "en";
    }
    this.language = t;
  }
  get(t) {
    return _common_translations_js__WEBPACK_IMPORTED_MODULE_0__["default"][this.language][t];
  }
}

/***/ }),

/***/ 53010:
/*!***********************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/ui/js/feed-css.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   attachFeedCSS: () => (/* binding */ attachFeedCSS),
/* harmony export */   setupFeedUI: () => (/* binding */ setupFeedUI)
/* harmony export */ });
/* harmony import */ var _attach_css_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./attach-css.js */ 47039);
/* harmony import */ var _load_font_awesome_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./load-font-awesome.js */ 55614);


function attachFeedCSS(t) {
  (0,_attach_css_js__WEBPACK_IMPORTED_MODULE_0__.attachCSS)(t, "feed", "body>.ab-feed{position:fixed;top:0;right:0;bottom:0;width:421px;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0}body>.ab-feed .ab-feed-body{position:absolute;top:0;left:0;right:0;border:none;border-left:1px solid #d0d0d0;padding-top:70px;min-height:100%}body>.ab-feed .ab-initial-spinner{float:none}body>.ab-feed .ab-no-cards-message{position:absolute;width:100%;margin-left:-20px;top:40%}.ab-feed{-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-box-shadow:0 1px 7px 1px rgba(66,82,113,.15);-moz-box-shadow:0 1px 7px 1px rgba(66,82,113,.15);box-shadow:0 1px 7px 1px rgba(66,82,113,.15);width:402px;background-color:#eee;font-family:'Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,'Lucida Grande',sans-serif;font-size:13px;line-height:130%;letter-spacing:normal;overflow-y:auto;overflow-x:visible;z-index:9011;-webkit-overflow-scrolling:touch}.ab-feed :focus,.ab-feed:focus{outline:0}.ab-feed .ab-feed-body{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;border:1px solid #d0d0d0;border-top:none;padding:20px 20px 0 20px}.ab-feed.ab-effect-slide{-webkit-transform:translateX(450px);-moz-transform:translateX(450px);-ms-transform:translateX(450px);transform:translateX(450px);-webkit-transition:transform .5s ease-in-out;-moz-transition:transform .5s ease-in-out;-o-transition:transform .5s ease-in-out;transition:transform .5s ease-in-out}.ab-feed.ab-effect-slide.ab-show{-webkit-transform:translateX(0);-moz-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0)}.ab-feed.ab-effect-slide.ab-hide{-webkit-transform:translateX(450px);-moz-transform:translateX(450px);-ms-transform:translateX(450px);transform:translateX(450px)}.ab-feed .ab-card{position:relative;-webkit-box-shadow:0 2px 3px 0 rgba(178,178,178,.5);-moz-box-shadow:0 2px 3px 0 rgba(178,178,178,.5);box-shadow:0 2px 3px 0 rgba(178,178,178,.5);-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;width:100%;border:1px solid #d0d0d0;margin-bottom:20px;overflow:hidden;background-color:#fff;-webkit-transition:height .4s ease-in-out,margin .4s ease-in-out;-moz-transition:height .4s ease-in-out,margin .4s ease-in-out;-o-transition:height .4s ease-in-out,margin .4s ease-in-out;transition:height .4s ease-in-out,margin .4s ease-in-out}.ab-feed .ab-card .ab-pinned-indicator{position:absolute;right:0;top:0;margin-right:-1px;width:0;height:0;border-style:solid;border-width:0 24px 24px 0;border-color:transparent #1676d0 transparent transparent}.ab-feed .ab-card .ab-pinned-indicator .fa-star{position:absolute;right:-21px;top:2px;font-size:9px;color:#fff}.ab-feed .ab-card.ab-effect-card.ab-hide{-webkit-transition:all .5s ease-in-out;-moz-transition:all .5s ease-in-out;-o-transition:all .5s ease-in-out;transition:all .5s ease-in-out}.ab-feed .ab-card.ab-effect-card.ab-hide.ab-swiped-left{-webkit-transform:translateX(-450px);-moz-transform:translateX(-450px);-ms-transform:translateX(-450px);transform:translateX(-450px)}.ab-feed .ab-card.ab-effect-card.ab-hide.ab-swiped-right{-webkit-transform:translateX(450px);-moz-transform:translateX(450px);-ms-transform:translateX(450px);transform:translateX(450px)}.ab-feed .ab-card.ab-effect-card.ab-hide:not(.ab-swiped-left):not(.ab-swiped-right){opacity:0}.ab-feed .ab-card .ab-close-button{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;background-color:transparent;background-size:15px;border:none;width:15px;min-width:15px;height:15px;cursor:pointer;display:block;font-size:15px;line-height:0;padding-top:15px;padding-right:15px;padding-left:15px;padding-bottom:15px;position:absolute;right:0;top:0;z-index:9021;opacity:0;-webkit-transition:.5s;-moz-transition:.5s;-o-transition:.5s;transition:.5s}.ab-feed .ab-card .ab-close-button svg{-webkit-transition:.2s ease;-moz-transition:.2s ease;-o-transition:.2s ease;transition:.2s ease;fill:#9b9b9b;height:auto;width:100%}.ab-feed .ab-card .ab-close-button svg.ab-chevron{display:none}.ab-feed .ab-card .ab-close-button:active{background-color:transparent}.ab-feed .ab-card .ab-close-button:focus{background-color:transparent}.ab-feed .ab-card .ab-close-button:hover{background-color:transparent}.ab-feed .ab-card .ab-close-button:hover svg{fill-opacity:.8}.ab-feed .ab-card .ab-close-button:hover{opacity:1}.ab-feed .ab-card .ab-close-button:focus{opacity:1}.ab-feed .ab-card a{float:none;color:inherit;text-decoration:none}.ab-feed .ab-card a:hover{text-decoration:underline}.ab-feed .ab-card .ab-image-area{float:none;display:inline-block;vertical-align:top;line-height:0;overflow:hidden;width:100%;-webkit-box-sizing:initial;-moz-box-sizing:initial;box-sizing:initial}.ab-feed .ab-card .ab-image-area img{float:none;height:auto;width:100%}.ab-feed .ab-card.ab-banner .ab-card-body{display:none}.ab-feed .ab-card.ab-image-only .ab-card-body{display:none}.ab-feed .ab-card .ab-card-body{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:inline-block;width:100%;position:relative}.ab-feed .ab-card .ab-unread-indicator{position:absolute;bottom:0;margin-right:-1px;width:100%;height:5px;background-color:#1676d0}.ab-feed .ab-card .ab-unread-indicator.read{background-color:transparent}.ab-feed .ab-card .ab-title{float:none;letter-spacing:0;margin:0;font-weight:700;font-family:'Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,'Lucida Grande',sans-serif;display:block;overflow:hidden;word-wrap:break-word;text-overflow:ellipsis;font-size:18px;line-height:130%;padding:20px 25px 0 25px}.ab-feed .ab-card .ab-description{float:none;color:#545454;padding:15px 25px 20px 25px;word-wrap:break-word;white-space:pre-wrap}.ab-feed .ab-card .ab-description.ab-no-title{padding-top:20px}.ab-feed .ab-card .ab-url-area{float:none;color:#1676d0;margin-top:12px;font-family:'Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,'Lucida Grande',sans-serif}.ab-feed .ab-card.ab-classic-card .ab-card-body{min-height:40px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px}.ab-feed .ab-card.ab-classic-card.with-image .ab-card-body{min-height:100px;padding-left:72px}.ab-feed .ab-card.ab-classic-card.with-image .ab-image-area{width:60px;height:60px;padding:20px 0 25px 25px;position:absolute}.ab-feed .ab-card.ab-classic-card.with-image .ab-image-area img{-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;max-width:100%;max-height:100%;width:auto;height:auto}.ab-feed .ab-card.ab-classic-card.with-image .ab-title{background-color:transparent;font-size:16px}.ab-feed .ab-card.ab-classic-card.with-image .ab-description{padding-top:10px}.ab-feed .ab-card.ab-control-card{height:0;width:0;margin:0;border:0}.ab-feed .ab-feed-buttons-wrapper{float:none;position:relative;background-color:#282828;height:50px;-webkit-box-shadow:0 2px 3px 0 rgba(178,178,178,.5);-moz-box-shadow:0 2px 3px 0 rgba(178,178,178,.5);box-shadow:0 2px 3px 0 rgba(178,178,178,.5);z-index:1}.ab-feed .ab-feed-buttons-wrapper .ab-close-button,.ab-feed .ab-feed-buttons-wrapper .ab-refresh-button{float:none;cursor:pointer;color:#fff;font-size:18px;padding:16px;-webkit-transition:.2s;-moz-transition:.2s;-o-transition:.2s;transition:.2s}.ab-feed .ab-feed-buttons-wrapper .ab-close-button:hover,.ab-feed .ab-feed-buttons-wrapper .ab-refresh-button:hover{font-size:22px}.ab-feed .ab-feed-buttons-wrapper .ab-close-button{float:right}.ab-feed .ab-feed-buttons-wrapper .ab-close-button:hover{padding-top:12px;padding-right:14px}.ab-feed .ab-feed-buttons-wrapper .ab-refresh-button{padding-left:17px}.ab-feed .ab-feed-buttons-wrapper .ab-refresh-button:hover{padding-top:13px;padding-left:14px}.ab-feed .ab-no-cards-message{text-align:center;margin-bottom:20px}@media (max-width:600px){body>.ab-feed{width:100%}}");
}
function setupFeedUI() {
  attachFeedCSS(), (0,_load_font_awesome_js__WEBPACK_IMPORTED_MODULE_1__.loadFontAwesome)();
}

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_Feed_ui_show-feed_js.js.map