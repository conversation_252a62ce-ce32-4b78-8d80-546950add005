"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_admin-merchant_admin-merchant_module_ts"],{

/***/ 27740:
/*!*****************************************************************!*\
  !*** ./src/app/admin-merchant/admin-merchant-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminMerchantRoutingModule: () => (/* binding */ AdminMerchantRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 84326);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../sharedServices */ 2902);
/* harmony import */ var _components_finance_report_with_history_generatedInvoiceList_resolver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/finance-report-with-history/generatedInvoiceList.resolver */ 78246);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);

// components





// routes
const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AdminListMerchantsComponent,
  resolve: {
    merchants: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.ListMerchantsAdminResolver
  }
}, {
  path: 'financeReport',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.FinanceReportComponent
}, {
  path: 'financeReportv2',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.FinanceReportWithHistoryComponent,
  resolve: {
    generatedInvoiceList: _components_finance_report_with_history_generatedInvoiceList_resolver__WEBPACK_IMPORTED_MODULE_2__.GeneratedInvoiceList
  }
}, {
  path: ':merchantId/schoolSearch',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.LinkSchoolToMerchantPageComponent,
  resolve: {
    merchant: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.currentMerchantResolver
  }
}, {
  path: ':merchantId/userSearch',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AddMerchantUserSearchComponent,
  resolve: {
    merchant: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.currentMerchantResolver
  }
}, {
  path: ':merchantId/userSearch/:id',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.MerchantUserFormComponent,
  resolve: {
    user: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.UserDetailsResolver,
    merchant: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.currentMerchantResolver,
    schools: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantSchoolResolver
  }
}, {
  path: ':merchantId/editmerchantuser/:id',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.MerchantUserFormComponent,
  resolve: {
    user: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantUserFormResolver,
    merchant: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.currentMerchantResolver,
    schools: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantSchoolResolver
  }
}, {
  path: 'createmerchant',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.CreateMerchantSearchComponent
}, {
  path: ':merchantId/school/:schoolId',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.MerchantLinkedSchoolsDetailsComponent
  // resolve: {
  //   feeData: FeeCalculatorResolver,
  // },
}, {
  path: 'createmerchant/:id',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.CreateMerchantFormComponent,
  resolve: {
    user: _sharedServices__WEBPACK_IMPORTED_MODULE_1__.UserDetailsResolver
  }
}];
class AdminMerchantRoutingModule {
  static {
    this.ɵfac = function AdminMerchantRoutingModule_Factory(t) {
      return new (t || AdminMerchantRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: AdminMerchantRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](AdminMerchantRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterModule]
  });
})();

/***/ }),

/***/ 40029:
/*!*********************************************************!*\
  !*** ./src/app/admin-merchant/admin-merchant.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminMerchantModule: () => (/* binding */ AdminMerchantModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _admin_merchant_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./admin-merchant-routing.module */ 27740);
/* harmony import */ var _account_account_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../account/account.module */ 90359);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-tools/shared-tools.module */ 23879);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../schools-form/schools-form.module */ 97162);
/* harmony import */ var _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../schools-common/schools-common.module */ 53943);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/datepicker */ 61977);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/radio */ 53804);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components */ 84326);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);


// module







// material













class AdminMerchantModule {
  static {
    this.ɵfac = function AdminMerchantModule_Factory(t) {
      return new (t || AdminMerchantModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: AdminMerchantModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsFormModule, _admin_merchant_routing_module__WEBPACK_IMPORTED_MODULE_0__.AdminMerchantRoutingModule, _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsButtonModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsCommonModule,
      // material
      _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__.MatIconModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_12__.MatCheckboxModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_13__.MatSortModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_14__.MatInputModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_15__.MatCardModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_16__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_17__.MatDatepickerModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_18__.MatRadioModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_19__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_20__.MatTableModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_21__.MatButtonModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](AdminMerchantModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_7__.AdminListMerchantsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantLinkedSchoolsTableComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantSchoolSearchComponent, _components__WEBPACK_IMPORTED_MODULE_7__.LinkSchoolToMerchantPageComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantUserFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantUserPermissionsTableComponent, _components__WEBPACK_IMPORTED_MODULE_7__.AddMerchantUserSearchComponent, _components__WEBPACK_IMPORTED_MODULE_7__.CreateMerchantFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.CreateMerchantSearchComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantDetailsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantContactDetailsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MerchantLinkedSchoolsDetailsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.SchoolHeaderComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FinanceReportComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FeeCalculatorFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FeeCalculatorFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FeeCalculatorSelectListComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FinanceReportWithHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_7__.WeeklyInvoiceButtonsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.WeeklyReportComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsFormModule, _admin_merchant_routing_module__WEBPACK_IMPORTED_MODULE_0__.AdminMerchantRoutingModule, _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsButtonModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsCommonModule,
    // material
    _angular_material_icon__WEBPACK_IMPORTED_MODULE_11__.MatIconModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_12__.MatCheckboxModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_13__.MatSortModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_14__.MatInputModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_15__.MatCardModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_16__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_17__.MatDatepickerModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_18__.MatRadioModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_19__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_20__.MatTableModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_21__.MatButtonModule]
  });
})();

/***/ }),

/***/ 79260:
/*!**********************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/add-merchant-user-search/add-merchant-user-search.component.ts ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddMerchantUserSearchComponent: () => (/* binding */ AddMerchantUserSearchComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-common/components/search-panel/search-panel.component */ 43913);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/table */ 77697);
//models









function AddMerchantUserSearchComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 8)(1, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.noResultsMessage);
  }
}
function AddMerchantUserSearchComponent_div_10_th_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "User ID");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AddMerchantUserSearchComponent_div_10_td_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r16 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r16.UserId);
  }
}
function AddMerchantUserSearchComponent_div_10_th_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "First name");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AddMerchantUserSearchComponent_div_10_td_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r17 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r17.FirstName);
  }
}
function AddMerchantUserSearchComponent_div_10_th_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Last name");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AddMerchantUserSearchComponent_div_10_td_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r18 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r18.Lastname);
  }
}
function AddMerchantUserSearchComponent_div_10_th_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Phone");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AddMerchantUserSearchComponent_div_10_td_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r19 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r19.Mobile);
  }
}
function AddMerchantUserSearchComponent_div_10_th_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Email");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AddMerchantUserSearchComponent_div_10_td_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r20.Email);
  }
}
function AddMerchantUserSearchComponent_div_10_th_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "th", 20);
  }
}
function AddMerchantUserSearchComponent_div_10_td_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 21)(1, "a", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Add User ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "mat-icon", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "chevron_right");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
}
function AddMerchantUserSearchComponent_div_10_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "tr", 24);
  }
}
function AddMerchantUserSearchComponent_div_10_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "tr", 25);
  }
  if (rf & 2) {
    const row_r22 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpropertyInterpolate1"]("routerLink", "./", row_r22.UserId, "");
  }
}
function AddMerchantUserSearchComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "table", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](2, 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](3, AddMerchantUserSearchComponent_div_10_th_3_Template, 2, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, AddMerchantUserSearchComponent_div_10_td_4_Template, 2, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](5, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, AddMerchantUserSearchComponent_div_10_th_6_Template, 2, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, AddMerchantUserSearchComponent_div_10_td_7_Template, 2, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](8, 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](9, AddMerchantUserSearchComponent_div_10_th_9_Template, 2, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](10, AddMerchantUserSearchComponent_div_10_td_10_Template, 2, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](11, 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](12, AddMerchantUserSearchComponent_div_10_th_12_Template, 2, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](13, AddMerchantUserSearchComponent_div_10_td_13_Template, 2, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](14, 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, AddMerchantUserSearchComponent_div_10_th_15_Template, 2, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](16, AddMerchantUserSearchComponent_div_10_td_16_Template, 2, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](17, 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](18, AddMerchantUserSearchComponent_div_10_th_18_Template, 1, 0, "th", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](19, AddMerchantUserSearchComponent_div_10_td_19_Template, 5, 0, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, AddMerchantUserSearchComponent_div_10_tr_20_Template, 1, 0, "tr", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](21, AddMerchantUserSearchComponent_div_10_tr_21_Template, 1, 1, "tr", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("dataSource", ctx_r1.dataSource);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("matHeaderRowDef", ctx_r1.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("matRowDefColumns", ctx_r1.displayedColumns);
  }
}
const _columns = ['id', 'firstName', 'lastName', 'phone', 'email', 'select'];
class AddMerchantUserSearchComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BasePaginatorComponent {
  constructor(spinnerService, router, route, merchantService) {
    super(_columns);
    this.spinnerService = spinnerService;
    this.router = router;
    this.route = route;
    this.merchantService = merchantService;
    this.noResultsMessage = '';
    this.showResultsTable = false;
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
    });
    // get current filters
    this.listfilters = this.merchantService.getMerchantSearchFilters();
    if (this.listfilters?.Filter) {
      this._requestUsers();
    } else {
      this.initFilters();
    }
  }
  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('userSearch')) {
      this.clearFilter();
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  goBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }
  clearFilter() {
    this.clearFiltersAndResults();
    //reset saved filter
    this.merchantService.setMerchantSearchFilters(this.listfilters);
  }
  fetchData(searchInput) {
    this.listfilters.Filter = searchInput;
    this._requestUsers();
  }
  /** Call the user service to get the users */
  _requestUsers() {
    this.spinnerService.start();
    // save current filters
    this.merchantService.setMerchantSearchFilters(this.listfilters);
    this.merchantService.GetUsersToAddToCanteen(this.selectedMerchant.canteenId, this.listfilters.Filter).subscribe({
      next: res => {
        this._ProcessResponseData(res);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  /** Process the list of users to be used in the search results */
  _ProcessResponseData(response) {
    if (response) {
      this.listObjects = response;
      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;
    this.spinnerService.stop();
  }
  static {
    this.ɵfac = function AddMerchantUserSearchComponent_Factory(t) {
      return new (t || AddMerchantUserSearchComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AddMerchantUserSearchComponent,
      selectors: [["app-add-merchant-user-search"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 12,
      vars: 3,
      consts: [["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "header"], [1, "merchant-heading"], ["placeholder", "Search parent account name...", 3, "searchInput", "triggerSearch", "triggerClear"], ["class", "noResults", 4, "ngIf"], [4, "ngIf"], [2, "height", "70px"], [1, "noResults"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "userTable", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "firstName"], ["matColumnDef", "lastName"], ["matColumnDef", "phone"], ["matColumnDef", "email"], ["matColumnDef", "select", "stickyEnd", ""], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 3, "routerLink", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [2, "float", "right"], [1, "chevron"], ["mat-header-row", ""], ["mat-row", "", 3, "routerLink"]],
      template: function AddMerchantUserSearchComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("navBack", function AddMerchantUserSearchComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.goBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "h3", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Add merchant user");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6, "Search existing users to add them as a merchant user");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "search-panel", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("triggerSearch", function AddMerchantUserSearchComponent_Template_search_panel_triggerSearch_7_listener($event) {
            return ctx.fetchData($event);
          })("triggerClear", function AddMerchantUserSearchComponent_Template_search_panel_triggerClear_7_listener() {
            return ctx.clearFilter();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](9, AddMerchantUserSearchComponent_div_9_Template, 3, 1, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](10, AddMerchantUserSearchComponent_div_10_Template, 22, 3, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](11, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("searchInput", ctx.listfilters.Filter);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.noResultsMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showResultsTable);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__.NavBackButtonComponent, _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_3__.SearchPanelComponent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__.MatIcon, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatRow],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.merchant-heading[_ngcontent-%COMP%] {\n  font-size: 28px;\n  color: #1b1f39;\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.userTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\na[_ngcontent-%COMP%] {\n  cursor: pointer;\n  color: #ff7a00;\n  display: flex;\n  align-items: center;\n  font-weight: 700;\n}\n\na[_ngcontent-%COMP%]:hover {\n  color: #ff7a00;\n}\n\n.chevron[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.filterField[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.noResults[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 18px;\n}\n.noResults[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  padding: 0;\n  padding-left: 5px;\n  margin: 0;\n  font-weight: 400;\n  font-size: 16px;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 6446:
/*!**************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/admin-list-merchants/admin-list-merchants.component.ts ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminListMerchantsComponent: () => (/* binding */ AdminListMerchantsComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _merchant_linked_schools_table_merchant_linked_schools_table_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../merchant-linked-schools-table/merchant-linked-schools-table.component */ 61900);
/* harmony import */ var _merchant_user_permissions_table_merchant_user_permissions_table_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../merchant-user-permissions-table/merchant-user-permissions-table.component */ 10616);
/* harmony import */ var _merchant_details_merchant_details_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../merchant-details/merchant-details.component */ 69536);
/* harmony import */ var _merchant_contact_details_merchant_contact_details_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../merchant-contact-details/merchant-contact-details.component */ 71733);
/* harmony import */ var _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../school-header/school-header.component */ 4840);













const _c0 = ["userPermissions"];
const _c1 = function (a0) {
  return {
    selectedTab: a0
  };
};
const _c2 = function (a0) {
  return {
    selectedTabText: a0
  };
};
function AdminListMerchantsComponent_td_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "td", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function AdminListMerchantsComponent_td_15_Template_td_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r5);
      const element_r3 = restoredCtx.$implicit;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r4.selectMerchant(element_r3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](1, "div", 17)(2, "h5", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](4, "p", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](6, "img", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r3 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](4, _c1, ctx_r0.selectedMerchant && ctx_r0.selectedMerchant.canteenId == element_r3.canteenId));
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](6, _c2, ctx_r0.selectedMerchant && ctx_r0.selectedMerchant.canteenId == element_r3.canteenId));
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate1"](" ", element_r3.merchantName, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](element_r3.ownerName);
  }
}
function AdminListMerchantsComponent_tr_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](0, "tr", 21);
  }
}
function AdminListMerchantsComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](1, "merchant-contact-details", 23)(2, "merchant-details", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](3, "merchant-linked-schools-table", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("stopLoad", function AdminListMerchantsComponent_div_17_Template_merchant_linked_schools_table_stopLoad_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r8.loadCheck());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](4, "div", null, 26)(6, "merchant-user-permissions-table", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("stopLoad", function AdminListMerchantsComponent_div_17_Template_merchant_user_permissions_table_stopLoad_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r9);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r10.loadCheck());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("contactDetails", ctx_r2.contactDetails);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("merchantDetails", ctx_r2.merchantDetails);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("merchantType", ctx_r2.merchantType);
  }
}
const _c3 = function (a0) {
  return {
    disableCoverGrey: a0
  };
};
const _c4 = function (a0) {
  return {
    disableCoverTab: a0
  };
};
const _c5 = function (a0, a1) {
  return {
    tabContainerOpen: a0,
    stopScroll: a1
  };
};
const _columns = ['name'];
class AdminListMerchantsComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BasePaginatorComponent {
  constructor(spinnerService, route, router, merchantService, dialog) {
    super(_columns);
    this.spinnerService = spinnerService;
    this.route = route;
    this.router = router;
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.disableMode = false;
    this.loadCount = 0;
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    this.merchantDataSubscription = this.merchantService.merchantListUpdatedEvent$.subscribe({
      next: res => {
        this._ProcessResponseMerchants(res);
        //find newly created merchant
        let newMerchantId = this.merchantService.getNewMerchantId();
        if (newMerchantId) {
          let findNewMerchant = res.findIndex(el => el.canteenId === this.merchantService.getNewMerchantId());
          this.merchantService.setSelectedMerchant(res[findNewMerchant]);
          this.merchantService.setNewMerchantId(null);
        }
        this.selectedMerchant = this.merchantService.getSelectedMerchant();
      }
    });
    // get data from resolver
    this.route.data.subscribe(data => {
      let tempRes = data['merchants'];
      this.merchantService.setMerchantList(tempRes);
    });
    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
    if (this.selectedMerchant) {
      this.spinnerService.start();
      this.LoadMerchantData(this.selectedMerchant.canteenId);
    }
  }
  ngOnDestroy() {
    if (this.editSubscription) {
      this.merchantService.setDisableMode(false);
      this.editSubscription.unsubscribe();
    }
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.merchantDataSubscription) {
      this.merchantDataSubscription.unsubscribe();
    }
  }
  /** Process the list of users to be used in the component */
  _ProcessResponseMerchants(response) {
    if (response) {
      this.listObjects = response;
      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
      } else {
        this.totalRows = 0;
      }
    } else {
      this.listObjects = [];
      this.totalRows = 0;
    }
    this.dataSource.data = this.listObjects;
    this.selectedMerchant = null;
    //Stop spinner
    this.spinnerService.stop();
  }
  selectMerchant(merchant) {
    if (!this.selectedMerchant || merchant.canteenId != this.selectedMerchant.canteenId) {
      this.loadCount = 0;
      this.selectedMerchant = merchant;
      this.merchantService.setSelectedMerchant(merchant);
      this.LoadMerchantData(this.selectedMerchant.canteenId);
    }
  }
  //function to track the loading status of all child components
  loadCheck() {
    this.loadCount++;
    //once the 3 child components have finsihed loading - the global loading will be stopped
    if (this.loadCount >= 3) {
      if (this.selectedMerchant && this.merchantService.getUpdateMerchantUserPermissions()) {
        //scroll to User permissions table
        this.userPermissionsSection.nativeElement.scrollIntoView({
          block: 'start'
        });
        window.scroll(0, 0);
        this.merchantService.setUpdateMerchantUserPermissions(false);
      }
      this.spinnerService.stop();
    }
  }
  LoadMerchantData(merchantId) {
    this.spinnerService.start();
    //Load merchant details
    this.merchantService.GetMerchant(merchantId).subscribe({
      next: res => {
        this.merchantDetails = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantDetails();
        this.merchantDetails = res.merchantDetails;
        this.merchantType = res.merchantDetails.type;
        this.contactDetails = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantContactDetails();
        this.contactDetails = res.contactDetails;
        //stop loading
        this.loadCheck();
      },
      error: error => {
        //stop loading
        this.loadCheck();
        this.handleErrorFromService(error);
      }
    });
  }
  createMerchantClick() {
    this.router.navigate(['./admin/merchants/createmerchant']);
  }
  financeReportClick() {
    this.router.navigate(['./admin/merchants/financeReport']);
  }
  static {
    this.ɵfac = function AdminListMerchantsComponent_Factory(t) {
      return new (t || AdminListMerchantsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineComponent"]({
      type: AdminListMerchantsComponent,
      selectors: [["app-admin-list-merchants"]],
      viewQuery: function AdminListMerchantsComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵloadQuery"]()) && (ctx.userPermissionsSection = _t.first);
        }
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵInheritDefinitionFeature"]],
      decls: 18,
      vars: 18,
      consts: [[1, "container-fluid"], [1, "row", "pt-5", "pb-4"], [1, "col-6"], [3, "ngClass"], ["title", "Merchants"], [1, "col-6", "d-flex", "justify-content-end"], ["text", "Create Merchant", 1, "mr-3", 3, "buttonStyle", "onPress"], ["text", "Finance Reports", 3, "buttonStyle", "onPress"], [1, "row"], [1, "col-12", "searchResults"], [1, "tabContainer", 3, "ngClass"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "accountTable", 3, "dataSource"], ["matColumnDef", "name"], ["mat-cell", "", "class", "result-tab", 3, "ngClass", "click", 4, "matCellDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["class", "result-details", 4, "ngIf"], ["mat-cell", "", 1, "result-tab", 3, "ngClass", "click"], [1, "result-tab-inner"], [1, "result-title", 3, "ngClass"], [1, "result-subtitle"], ["src", "assets/icons/orange-arrow-right.svg", "alt", "chevron", 1, "chevron"], ["mat-row", ""], [1, "result-details"], [3, "contactDetails"], [3, "merchantDetails"], [3, "merchantType", "stopLoad"], ["userPermissions", ""], [3, "stopLoad"]],
      template: function AdminListMerchantsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](3, "div", 3)(4, "school-header", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](6, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](7, "basic-button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("onPress", function AdminListMerchantsComponent_Template_basic_button_onPress_7_listener() {
            return ctx.createMerchantClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](8, "basic-button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("onPress", function AdminListMerchantsComponent_Template_basic_button_onPress_8_listener() {
            return ctx.financeReportClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](9, "div", 8)(10, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](11, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](12, "div", 10)(13, "table", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerStart"](14, 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](15, AdminListMerchantsComponent_td_15_Template, 7, 8, "td", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](16, AdminListMerchantsComponent_tr_16_Template, 1, 0, "tr", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](17, AdminListMerchantsComponent_div_17_Template, 7, 3, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](9, _c3, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](11, _c3, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("buttonStyle", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("buttonStyle", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](13, _c4, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction2"](15, _c5, ctx.selectedMerchant, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("dataSource", ctx.dataSource);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngIf", ctx.selectedMerchant);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_11__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_11__.NgIf, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__.BasicButtonComponent, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_12__.MatRow, _merchant_linked_schools_table_merchant_linked_schools_table_component__WEBPACK_IMPORTED_MODULE_3__.MerchantLinkedSchoolsTableComponent, _merchant_user_permissions_table_merchant_user_permissions_table_component__WEBPACK_IMPORTED_MODULE_4__.MerchantUserPermissionsTableComponent, _merchant_details_merchant_details_component__WEBPACK_IMPORTED_MODULE_5__.MerchantDetailsComponent, _merchant_contact_details_merchant_contact_details_component__WEBPACK_IMPORTED_MODULE_6__.MerchantContactDetailsComponent, _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_7__.SchoolHeaderComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.accountTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.searchResults[_ngcontent-%COMP%] {\n  padding-bottom: 60px;\n  position: relative;\n  display: flex;\n}\n\n.chevron[_ngcontent-%COMP%] {\n  height: 14px;\n  width: 8px;\n}\n\n.result-title[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 0;\n  color: #333b44;\n}\n\n.result-subtitle[_ngcontent-%COMP%] {\n  font-size: 12px;\n  margin: 0;\n  color: #333b44;\n}\n\n.result-details[_ngcontent-%COMP%] {\n  width: 70%;\n  padding: 5px 20px 40px 20px;\n  background-color: white;\n  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);\n  position: relative;\n  z-index: 1;\n  overflow-y: scroll;\n  height: 80vh;\n}\n\n.tabContainer[_ngcontent-%COMP%] {\n  padding: 0;\n  overflow-y: scroll;\n  height: 80vh;\n  width: 100%;\n}\n\n.tabContainerOpen[_ngcontent-%COMP%] {\n  width: 30%;\n}\n\n.mat-mdc-table[_ngcontent-%COMP%] {\n  box-shadow: none;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%] {\n  background-color: white;\n  margin: 0;\n  width: 100%;\n  height: auto;\n}\n\n.mat-mdc-cell[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #e5e5e5;\n  padding: 2px 0;\n  margin: 0;\n}\n\n.result-tab[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-right: 14px;\n}\n\n.result-tab[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n  cursor: pointer;\n}\n\n.result-tab-inner[_ngcontent-%COMP%] {\n  padding: 10px;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.selectedTab[_ngcontent-%COMP%] {\n  background-color: #e5e5e5;\n}\n\n.selectedTabText[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.disableCoverTab[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: rgb(255, 255, 255);\n  opacity: 0.5;\n  width: 30%;\n  height: 80vh;\n  z-index: 100;\n}\n\n.disableCoverGrey[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #f2f2f2;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 76922:
/*!**************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/create-merchant-form/create-merchant-form.component.ts ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CreateMerchantFormComponent: () => (/* binding */ CreateMerchantFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components/ */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/radio */ 53804);













function CreateMerchantFormComponent_mat_error_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "You must enter a value");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantFormComponent_mat_error_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "You must enter a value");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantFormComponent_mat_error_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "You must enter a value");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
class CreateMerchantFormComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(spinnerService, merchantService, dialog, router, route) {
    super();
    this.spinnerService = spinnerService;
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.router = router;
    this.route = route;
    this.merchantTypeEnum = _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum;
    this.editUserFirstName = false;
    this.editUserLastName = false;
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.currentUser = data['user'];
    });
    this.CreateForm();
  }
  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('createmerchant')) {
      this.merchantService.setMerchantSearchFilters(null);
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    this.merchantService.setDisableMode(false);
  }
  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants/createmerchant']);
  }
  CreateForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroup({
      firstName: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser.FirstName, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      lastName: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser.Lastname, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      email: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser.Email),
      merchantName: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      mobile: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser.Mobile),
      merchantType: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl('Canteen', [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required])
    });
  }
  onSubmit() {
    if (!this.form.invalid) {
      let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
      data.TitleLine1 = 'Are you sure?';
      data.TextLine1 = `Are you sure you want to create a new merchant?`;
      data.CancelButton = 'No, Cancel';
      data.ConfirmButton = 'Yes, Create';
      const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data
      });
      dialogRef.afterClosed().subscribe(cancelResult => {
        if (!cancelResult) {
          this.confirmCreateMerchant();
        }
      });
    }
  }
  confirmCreateMerchant() {
    this.spinnerService.start();
    let ownerData = {
      ownerId: this.currentUser.UserId,
      ownerFirstName: this.firstName.value,
      ownerLastName: this.lastName.value,
      ownerMobile: this.mobile.value
    };
    let merchantData = {
      owner: ownerData,
      name: this.merchantName.value,
      type: this.type.value
    };
    this.merchantService.CreateMerchant(merchantData).subscribe({
      next: res => {
        this.merchantService.setNewMerchantId(res);
        this.SuccessPopUp();
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup();
      }
    });
  }
  get merchantName() {
    return this.form.get('merchantName');
  }
  get type() {
    return this.form.get('merchantType');
  }
  get firstName() {
    return this.form.get('firstName');
  }
  get lastName() {
    return this.form.get('lastName');
  }
  get mobile() {
    return this.form.get('mobile');
  }
  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = 'We were unable to create this merchant.';
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmCreateMerchant();
      }
    });
  }
  SuccessPopUp() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = 'Merchant created successfully.';
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
    });
  }
  static {
    this.ɵfac = function CreateMerchantFormComponent_Factory(t) {
      return new (t || CreateMerchantFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_6__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: CreateMerchantFormComponent,
      selectors: [["create-merchant-form"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 49,
      vars: 10,
      consts: [[1, "relative-section"], ["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [3, "formGroup", "ngSubmit"], [1, "header", "relative-section"], ["type", "submit", 1, "merchant-btn"], ["src", "assets/icons/white-plus.svg", "alt", "plus symbol"], [1, "col-12", "user-card"], [1, "row"], [1, "col-sm-12", "col-md-6"], ["appearance", "outline"], ["matInput", "", "placeholder", "First name", "formControlName", "firstName", "type", "text", "required", ""], [4, "ngIf"], ["matInput", "", "placeholder", "Last name", "formControlName", "lastName", "type", "text", "required", ""], ["id", "type-label"], ["aria-labelledby", "type-label", "formControlName", "merchantType", 1, "radioButtons"], [2, "padding-right", "15px", 3, "value"], [3, "value"], [1, "details-divider"], ["matInput", "", "placeholder", "Merchant Name", "formControlName", "merchantName", "type", "text", "required", ""], [1, "read-only-wrapper"], ["matInput", "", "placeholder", "Mobile", "formControlName", "mobile", "type", "text", "readonly", ""], [1, "read-only-wrapper", 2, "padding-top", "10px"], ["matInput", "", "placeholder", "Email", "formControlName", "email", "type", "text", "readonly", ""]],
      template: function CreateMerchantFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "nav-back-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("navBack", function CreateMerchantFormComponent_Template_nav_back_button_navBack_1_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "div", 2)(3, "form", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngSubmit", function CreateMerchantFormComponent_Template_form_ngSubmit_3_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 4)(5, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6, "Create Merchant");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](8, "img", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](9, " Create ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 7)(11, "div", 8)(12, "div", 9)(13, "mat-form-field", 10)(14, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "First name");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](16, "input", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](17, CreateMerchantFormComponent_mat_error_17_Template, 2, 0, "mat-error", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](18, "mat-form-field", 10)(19, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](20, "Last name");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](21, "input", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](22, CreateMerchantFormComponent_mat_error_22_Template, 2, 0, "mat-error", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "label", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](24, "Type");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "mat-radio-group", 15)(26, "mat-radio-button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "mat-radio-button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](29);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](30, "mat-radio-button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](31);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](32, "hr", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "div", 9)(34, "mat-form-field", 10)(35, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](36, "Merchant name");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](37, "input", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](38, CreateMerchantFormComponent_mat_error_38_Template, 2, 0, "mat-error", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](39, "div", 20)(40, "mat-form-field", 10)(41, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](42, "Mobile");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](43, "input", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](44, "div", 22)(45, "mat-form-field", 10)(46, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](47, "Email");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](48, "input", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.merchantName.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.merchantName.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx.merchantTypeEnum.Canteen);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.merchantTypeEnum.Canteen);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx.merchantTypeEnum.Event);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.merchantTypeEnum.Event);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx.merchantTypeEnum.Uniform);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.merchantTypeEnum.Uniform);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.merchantName.invalid);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatError, _angular_material_radio__WEBPACK_IMPORTED_MODULE_11__.MatRadioGroup, _angular_material_radio__WEBPACK_IMPORTED_MODULE_11__.MatRadioButton],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.header[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 28px;\n  color: #1b1f39;\n  text-align: center;\n  height: 100%;\n  margin: 0;\n}\n\n.user-card[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 20px;\n  border-radius: 8px;\n}\n\n.user-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 50px;\n  padding-left: 10px;\n}\n\n.mat-mdc-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.user-details[_ngcontent-%COMP%] {\n  padding: 10px;\n}\n\nul[_ngcontent-%COMP%] {\n  list-style-type: none;\n  padding: 0;\n  margin: 0;\n}\n\nli[_ngcontent-%COMP%] {\n  padding: 6px;\n  font-size: 14px;\n  margin: 0;\n  color: #bbbbbb;\n}\n\n.editBtn[_ngcontent-%COMP%] {\n  background-color: transparent;\n  border: 0;\n  outline: 0;\n  cursor: pointer;\n  position: absolute;\n  right: 0;\n  top: 20px;\n}\n\n.merchant-btn[_ngcontent-%COMP%] {\n  background-color: #272c50;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 10px;\n  outline: none;\n  border: none;\n  color: #ffffff;\n  font-size: 18px;\n  line-height: 24px;\n  padding: 6px 16px;\n  border-radius: 14px;\n  font-weight: 700;\n  cursor: pointer;\n}\n.merchant-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n\n.merchant-btn[_ngcontent-%COMP%]:disabled {\n  background-color: #b9b9c8;\n  cursor: default;\n}\n\n.relative-section[_ngcontent-%COMP%] {\n  position: relative;\n}\n\n.radioButtons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n}\n\n\n\n.radioButtons[_ngcontent-%COMP%]     .mat-mdc-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {\n  background-color: #ff7a00;\n}\n\n\n\n.radioButtons[_ngcontent-%COMP%]     .mat-mdc-radio-button.mat-accent .mat-radio-inner-circle {\n  background-color: #ff7a00;\n}\n\n\n\n.radioButtons[_ngcontent-%COMP%]    .mat-mdc-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {\n  border-color: #ff7a00;\n}\n\n#type-label[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #828282;\n  padding-bottom: 10px;\n}\n\n\n\nmat-radio-group[_ngcontent-%COMP%] {\n  padding-top: 8px;\n  padding-bottom: 8px;\n}\n\n.details-divider[_ngcontent-%COMP%] {\n  border: none;\n  height: 0.1px;\n  background-color: #989494;\n  margin: 0 0 5px 0;\n}\n\n\n\n.read-only-wrapper[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-form-field-wrapper .mat-form-field-flex .mat-form-field-infix .mat-input-element {\n  color: #6b6c89;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 20298:
/*!******************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/create-merchant-search/create-merchant-search.component.ts ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CreateMerchantSearchComponent: () => (/* binding */ CreateMerchantSearchComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-common/components/search-panel/search-panel.component */ 43913);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/table */ 77697);









function CreateMerchantSearchComponent_h3_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.noResultsMessage);
  }
}
function CreateMerchantSearchComponent_div_8_th_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "User ID");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantSearchComponent_div_8_td_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r16 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r16.UserId);
  }
}
function CreateMerchantSearchComponent_div_8_th_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "First name");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantSearchComponent_div_8_td_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r17 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r17.FirstName);
  }
}
function CreateMerchantSearchComponent_div_8_th_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Last name");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantSearchComponent_div_8_td_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r18 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r18.Lastname);
  }
}
function CreateMerchantSearchComponent_div_8_th_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Phone");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantSearchComponent_div_8_td_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r19 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r19.Mobile);
  }
}
function CreateMerchantSearchComponent_div_8_th_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "th", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Email");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function CreateMerchantSearchComponent_div_8_td_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](element_r20.Email);
  }
}
function CreateMerchantSearchComponent_div_8_th_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "th", 18);
  }
}
function CreateMerchantSearchComponent_div_8_td_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "td", 19)(1, "a", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Select ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "mat-icon", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "chevron_right");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
}
function CreateMerchantSearchComponent_div_8_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "tr", 22);
  }
}
function CreateMerchantSearchComponent_div_8_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "tr", 23);
  }
  if (rf & 2) {
    const row_r22 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpropertyInterpolate1"]("routerLink", "./", row_r22.UserId, "");
  }
}
function CreateMerchantSearchComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "table", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](2, 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](3, CreateMerchantSearchComponent_div_8_th_3_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, CreateMerchantSearchComponent_div_8_td_4_Template, 2, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](5, 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, CreateMerchantSearchComponent_div_8_th_6_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, CreateMerchantSearchComponent_div_8_td_7_Template, 2, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](8, 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](9, CreateMerchantSearchComponent_div_8_th_9_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](10, CreateMerchantSearchComponent_div_8_td_10_Template, 2, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](11, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](12, CreateMerchantSearchComponent_div_8_th_12_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](13, CreateMerchantSearchComponent_div_8_td_13_Template, 2, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](14, 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, CreateMerchantSearchComponent_div_8_th_15_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](16, CreateMerchantSearchComponent_div_8_td_16_Template, 2, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerStart"](17, 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](18, CreateMerchantSearchComponent_div_8_th_18_Template, 1, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](19, CreateMerchantSearchComponent_div_8_td_19_Template, 5, 0, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, CreateMerchantSearchComponent_div_8_tr_20_Template, 1, 0, "tr", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](21, CreateMerchantSearchComponent_div_8_tr_21_Template, 1, 1, "tr", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("dataSource", ctx_r1.dataSource);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("matHeaderRowDef", ctx_r1.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("matRowDefColumns", ctx_r1.displayedColumns);
  }
}
const _columns = ['id', 'firstName', 'lastName', 'phone', 'email', 'select'];
class CreateMerchantSearchComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BasePaginatorComponent {
  constructor(spinnerService, router, route, merchantService) {
    super(_columns);
    this.spinnerService = spinnerService;
    this.router = router;
    this.route = route;
    this.merchantService = merchantService;
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    // get current filters
    this.listfilters = this.merchantService.getMerchantSearchFilters();
    this.initFilters();
    if (this.listfilters?.Filter) {
      this._requestData();
    }
  }
  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    //clear search filter if navigating off search result pages
    if (!this.currentRoute.url.includes('createmerchant')) {
      this.clearFilter();
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }
  clearFilter() {
    this.clearFiltersAndResults();
    this.merchantService.setMerchantSearchFilters(this.listfilters);
  }
  fetchData(searchInput) {
    this.listfilters.Filter = searchInput;
    this._requestData();
  }
  /** Call for data */
  _requestData() {
    this.spinnerService.start();
    // save current filters
    this.merchantService.setMerchantSearchFilters(this.listfilters);
    this.merchantService.GetCreateMerchantSearchResults(this.listfilters.Filter).subscribe({
      next: res => {
        this._ProcessResponseMerchants(res);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  /** Process the list of users to be used in the search results */
  _ProcessResponseMerchants(response) {
    if (response) {
      this.listObjects = response;
      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;
    //Stop spinner
    this.spinnerService.stop();
  }
  static {
    this.ɵfac = function CreateMerchantSearchComponent_Factory(t) {
      return new (t || CreateMerchantSearchComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: CreateMerchantSearchComponent,
      selectors: [["app-create-merchant-search"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 10,
      vars: 3,
      consts: [["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "header"], [1, "merchant-heading"], ["placeholder", "Search parent account name...", 3, "searchInput", "triggerSearch", "triggerClear"], [4, "ngIf"], [2, "height", "70px"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "userTable", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "firstName"], ["matColumnDef", "lastName"], ["matColumnDef", "phone"], ["matColumnDef", "email"], ["matColumnDef", "select", "stickyEnd", ""], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 3, "routerLink", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [2, "float", "right"], [1, "chevron"], ["mat-header-row", ""], ["mat-row", "", 3, "routerLink"]],
      template: function CreateMerchantSearchComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("navBack", function CreateMerchantSearchComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "h3", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Create Merchant");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "search-panel", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("triggerSearch", function CreateMerchantSearchComponent_Template_search_panel_triggerSearch_5_listener($event) {
            return ctx.fetchData($event);
          })("triggerClear", function CreateMerchantSearchComponent_Template_search_panel_triggerClear_5_listener() {
            return ctx.clearFilter();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, CreateMerchantSearchComponent_h3_7_Template, 2, 1, "h3", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](8, CreateMerchantSearchComponent_div_8_Template, 22, 3, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](9, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("searchInput", ctx.listfilters.Filter);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.noResultsMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showResultsTable);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__.NavBackButtonComponent, _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_3__.SearchPanelComponent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_7__.MatIcon, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_8__.MatRow],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.merchant-heading[_ngcontent-%COMP%] {\n  font-size: 28px;\n  color: #1b1f39;\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n  padding-bottom: 15px;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.userTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\na[_ngcontent-%COMP%] {\n  cursor: pointer;\n  color: #ff7a00;\n  display: flex;\n  align-items: center;\n  font-weight: 700;\n}\n\na[_ngcontent-%COMP%]:hover {\n  color: #ff7a00;\n}\n\n.chevron[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.filterField[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.noResults[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 18px;\n}\n.noResults[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  padding: 0;\n  padding-left: 5px;\n  margin: 0;\n  font-weight: 400;\n  font-size: 16px;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 71548:
/*!************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/fee-calculator-form/fee-calculator-form.component.ts ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeeCalculatorFormComponent: () => (/* binding */ FeeCalculatorFormComponent)
/* harmony export */ });
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedModels_fee_FeeCalculator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels/fee/FeeCalculator */ 35995);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices_fee_feeCalculator_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices/fee/feeCalculator.service */ 64432);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared/components/spinner/spinner.component */ 71517);
/* harmony import */ var _fee_calculator_select_list_fee_calculator_select_list_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../fee-calculator-select-list/fee-calculator-select-list.component */ 98492);

//Models









function FeeCalculatorFormComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "app-spinner", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("manual", true);
  }
}
function FeeCalculatorFormComponent_ng_template_4_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "fee-calculator-select-list", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("saveFee", function FeeCalculatorFormComponent_ng_template_4_ng_container_0_Template_fee_calculator_select_list_saveFee_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r6.saveFeeCalculator($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "fee-calculator-select-list", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("saveFee", function FeeCalculatorFormComponent_ng_template_4_ng_container_0_Template_fee_calculator_select_list_saveFee_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r7);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r8.saveFeeCalculator($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("values", ctx_r3.recessFeeList)("initialVal", ctx_r3.initialValues.Recess);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("values", ctx_r3.lunchFeeList)("initialVal", ctx_r3.initialValues.Lunch);
  }
}
function FeeCalculatorFormComponent_ng_template_4_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "fee-calculator-select-list", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("saveFee", function FeeCalculatorFormComponent_ng_template_4_ng_container_1_Template_fee_calculator_select_list_saveFee_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r9.saveFeeCalculator($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("values", ctx_r4.eventFeeList)("initialVal", ctx_r4.initialValues.Event);
  }
}
function FeeCalculatorFormComponent_ng_template_4_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "fee-calculator-select-list", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("saveFee", function FeeCalculatorFormComponent_ng_template_4_ng_container_2_Template_fee_calculator_select_list_saveFee_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r12);
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r11.saveFeeCalculator($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("values", ctx_r5.uniformFeeList)("initialVal", ctx_r5.initialValues.Uniform);
  }
}
function FeeCalculatorFormComponent_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](0, FeeCalculatorFormComponent_ng_template_4_ng_container_0_Template, 3, 4, "ng-container", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, FeeCalculatorFormComponent_ng_template_4_ng_container_1_Template, 2, 2, "ng-container", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, FeeCalculatorFormComponent_ng_template_4_ng_container_2_Template, 2, 2, "ng-container", 6);
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r2.merchantType == ctx_r2.merchantTypeEnum.Canteen);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r2.merchantType == ctx_r2.merchantTypeEnum.Canteen || ctx_r2.merchantType == ctx_r2.merchantTypeEnum.Event);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r2.merchantType === ctx_r2.merchantTypeEnum.Uniform);
  }
}
class FeeCalculatorFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(feeCalculatorService, route, dialog) {
    super();
    this.feeCalculatorService = feeCalculatorService;
    this.route = route;
    this.dialog = dialog;
    this.merchantTypeEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantTypeEnum;
    this.fullFeeList = [];
    this.loading = true;
    this.initialValues = new src_app_sharedModels_fee_FeeCalculator__WEBPACK_IMPORTED_MODULE_2__.FeeCalculatorInitialValue();
    this.recessFeeList = [];
    this.lunchFeeList = [];
    this.eventFeeList = [];
    this.uniformFeeList = [];
  }
  ngOnInit() {
    this.schoolId = this.route.snapshot.params['schoolId'];
    this.merchantId = this.route.snapshot.params['merchantId'];
    // get data from resolver
    this.route.data.subscribe(data => {
      this.processFeeData(data.feeData);
    });
  }
  processFeeData(res) {
    this.fullFeeList = res.allFeeCalculators;
    this.currentSchoolFeeCalculators = res.schoolFeeCalculators;
    this.getFeeSelectListForEachMenuType(this.fullFeeList);
    this.getInitialValues();
    this.loading = false;
  }
  getInitialValues() {
    this.initialValues.Recess = this.getDefaultValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Recess']);
    this.initialValues.Lunch = this.getDefaultValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Lunch']);
    this.initialValues.Event = this.getDefaultValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Event']);
    this.initialValues.Uniform = this.getDefaultValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Uniform']);
  }
  getFeeSelectListForEachMenuType(feeOptionArray) {
    if (this.merchantType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantTypeEnum.Canteen) {
      this.recessFeeList = this.filterCalculatorByOrderType(feeOptionArray, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Recess']);
      this.lunchFeeList = this.filterCalculatorByOrderType(feeOptionArray, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Lunch']);
    }
    if (this.merchantType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantTypeEnum.Canteen || this.merchantType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantTypeEnum.Event) {
      this.eventFeeList = this.filterCalculatorByOrderType(feeOptionArray, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Event']);
    }
    if (this.merchantType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantTypeEnum.Uniform) {
      this.uniformFeeList = this.filterCalculatorByOrderType(feeOptionArray, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.OrderTypeEnum['Uniform']);
    }
  }
  filterCalculatorByOrderType(calculatorList, menuType) {
    let filteredList = calculatorList.filter(x => x.calculatorOrderType === menuType);
    let selectListValues = [];
    filteredList.forEach(x => {
      selectListValues.push({
        key: x.feeCalculatorId,
        value: x.feeCalculatorName
      });
    });
    return selectListValues;
  }
  getDefaultValue(menuType) {
    let customFee = null;
    if (this.currentSchoolFeeCalculators?.length) {
      customFee = this.getOrderTypeFee(this.currentSchoolFeeCalculators, menuType);
    }
    if (!customFee) {
      let defaultList = this.fullFeeList.filter(fee => fee.isDefault === true);
      customFee = this.getOrderTypeFee(defaultList, menuType);
    }
    return customFee;
  }
  getOrderTypeFee(feeArray, menuType) {
    let fee = feeArray.find(x => x.calculatorOrderType === menuType);
    return fee?.feeCalculatorId || null;
  }
  getFeeById(id) {
    return this.fullFeeList.find(x => x.feeCalculatorId === id);
  }
  saveFeeCalculator(event) {
    this.latestUpdateEvent = event;
    let newFeeCalculator = this.getFeeById(event.newFeeId);
    let initialFeeCalculator = this.getFeeById(event.initialFeeId);
    if (!newFeeCalculator || !initialFeeCalculator) {
      return;
    }
    this.loading = true;
    //change from custom option to default
    if (newFeeCalculator.isDefault) {
      this.removeFeeCalculator(initialFeeCalculator.feeCalculatorId, newFeeCalculator.feeCalculatorId);
      return;
    }
    //change from default to custom option
    if (initialFeeCalculator.isDefault) {
      this.addFeeCalculator(newFeeCalculator.feeCalculatorId);
      return;
    }
    //change from custom option to custom option
    this.updateFeeCalculator(newFeeCalculator.feeCalculatorId);
  }
  removeFeeCalculator(feeId, newFeeId) {
    this.feeCalculatorService.RemoveSchoolFromFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe(res => {
      this.processApiSuccess(newFeeId);
    }, error => {
      this.errorPopUp();
      this.handleErrorFromService(error);
    });
  }
  addFeeCalculator(feeId) {
    this.feeCalculatorService.AddSchoolToFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe({
      next: res => {
        this.processApiSuccess(feeId);
      },
      error: error => {
        this.errorPopUp();
        this.handleErrorFromService(error);
      }
    });
  }
  updateFeeCalculator(feeId) {
    this.feeCalculatorService.UpdateSchoolFromFeeCalculator(feeId, this.schoolId, this.merchantId).subscribe({
      next: res => {
        this.processApiSuccess(feeId);
      },
      error: error => {
        this.errorPopUp();
        this.handleErrorFromService(error);
      }
    });
  }
  processApiSuccess(newFeeId) {
    this.initialValues[this.latestUpdateEvent.menuType] = newFeeId;
    this.successPopUp();
    this.loading = false;
  }
  errorPopUp() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = 'Something went wrong';
    data.Text = 'The fee calculator could not be updated.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__.DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.saveFeeCalculator(this.latestUpdateEvent);
      }
    });
  }
  successPopUp() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = 'Success!';
    data.Text = 'The fee calculator has been successfully updated.';
    data.ConfirmButton = 'Ok';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_0__.DialogConfirmV2Component, {
      width: '500px',
      disableClose: false,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {}
    });
  }
  static {
    this.ɵfac = function FeeCalculatorFormComponent_Factory(t) {
      return new (t || FeeCalculatorFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices_fee_feeCalculator_service__WEBPACK_IMPORTED_MODULE_3__.FeeCalculatorService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_8__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: FeeCalculatorFormComponent,
      selectors: [["fee-calculator-form"]],
      inputs: {
        merchantType: "merchantType"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵInheritDefinitionFeature"]],
      decls: 6,
      vars: 2,
      consts: [[1, "p-4", "pt-0"], [1, "m-0", "pb-4"], ["class", "col-12 d-flex justify-content-center", 4, "ngIf", "ngIfElse"], ["form", ""], [1, "col-12", "d-flex", "justify-content-center"], [3, "manual"], [4, "ngIf"], ["placeholder", "Recess", 3, "values", "initialVal", "saveFee"], ["placeholder", "Lunch", 3, "values", "initialVal", "saveFee"], ["placeholder", "Event", 3, "values", "initialVal", "saveFee"], ["placeholder", "Uniform", 3, "values", "initialVal", "saveFee"]],
      template: function FeeCalculatorFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "h3", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "Fee Calculators");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, FeeCalculatorFormComponent_div_3_Template, 2, 1, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, FeeCalculatorFormComponent_ng_template_4_Template, 3, 3, "ng-template", null, 3, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.loading)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_4__.SpinnerComponent, _fee_calculator_select_list_fee_calculator_select_list_component__WEBPACK_IMPORTED_MODULE_5__.FeeCalculatorSelectListComponent],
      styles: [".option-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tbWVyY2hhbnQvY29tcG9uZW50cy9mZWUtY2FsY3VsYXRvci1mb3JtL2ZlZS1jYWxjdWxhdG9yLWZvcm0uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsYUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLm9wdGlvbi13cmFwcGVyIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGRpc3BsYXk6IGZsZXg7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 34989:
/*!*****************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/fee-calculator-form/feeCalculator.resolver.ts ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeeCalculatorResolver: () => (/* binding */ FeeCalculatorResolver)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices_fee_feeCalculator_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedServices/fee/feeCalculator.service */ 64432);


class FeeCalculatorResolver {
  constructor(feeCalculatorService) {
    this.feeCalculatorService = feeCalculatorService;
  }
  resolve(route) {
    let schoolId = route.params['schoolId'];
    let merchantId = route.params['merchantId'];
    return this.feeCalculatorService.requestDataForAdminFeeForm(merchantId, schoolId);
  }
  static {
    this.ɵfac = function FeeCalculatorResolver_Factory(t) {
      return new (t || FeeCalculatorResolver)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](src_app_sharedServices_fee_feeCalculator_service__WEBPACK_IMPORTED_MODULE_0__.FeeCalculatorService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: FeeCalculatorResolver,
      factory: FeeCalculatorResolver.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 98492:
/*!**************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/fee-calculator-select-list/fee-calculator-select-list.component.ts ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeeCalculatorSelectListComponent: () => (/* binding */ FeeCalculatorSelectListComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);







function FeeCalculatorSelectListComponent_form_0_basic_button_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "basic-button", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function FeeCalculatorSelectListComponent_form_0_basic_button_3_Template_basic_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r2.saveFeeCalculator());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("buttonStyle", 0)("disabled", ctx_r1.form.invalid);
  }
}
function FeeCalculatorSelectListComponent_form_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "form", 1)(1, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "input-select-list", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, FeeCalculatorSelectListComponent_form_0_basic_button_3_Template, 1, 2, "basic-button", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx_r0.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("placeholder", ctx_r0.placeholder)("values", ctx_r0.values);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r0.initialVal != ctx_r0.menuOption.value);
  }
}
class FeeCalculatorSelectListComponent {
  constructor() {
    this.values = [];
    this.saveFee = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
  }
  ngOnInit() {
    this._createForm();
  }
  _createForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({
      menuOption: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(this.initialVal)
    });
  }
  saveFeeCalculator() {
    if (this.menuOption.value === this.initialVal) {
      return;
    }
    let feeInfo = {
      newFeeId: this.menuOption.value,
      initialFeeId: this.initialVal,
      menuType: this.placeholder
    };
    this.saveFee.emit(feeInfo);
  }
  get menuOption() {
    return this.form.get('menuOption');
  }
  static {
    this.ɵfac = function FeeCalculatorSelectListComponent_Factory(t) {
      return new (t || FeeCalculatorSelectListComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: FeeCalculatorSelectListComponent,
      selectors: [["fee-calculator-select-list"]],
      inputs: {
        values: "values",
        placeholder: "placeholder",
        initialVal: "initialVal"
      },
      outputs: {
        saveFee: "saveFee"
      },
      decls: 1,
      vars: 1,
      consts: [["class", "form", 3, "formGroup", 4, "ngIf"], [1, "form", 3, "formGroup"], [1, "picker", "mr-2"], ["formControlName", "menuOption", 3, "placeholder", "values"], ["text", "Save", "class", "mr-2", 3, "buttonStyle", "disabled", "click", 4, "ngIf"], ["text", "Save", 1, "mr-2", 3, "buttonStyle", "disabled", "click"]],
      template: function FeeCalculatorSelectListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](0, FeeCalculatorSelectListComponent_form_0_Template, 4, 4, "form", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.form);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_3__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_0__.InputSelectListComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControlName, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__.BasicButtonComponent],
      styles: [".form[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n.form[_ngcontent-%COMP%]   .picker[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 470px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tbWVyY2hhbnQvY29tcG9uZW50cy9mZWUtY2FsY3VsYXRvci1zZWxlY3QtbGlzdC9mZWUtY2FsY3VsYXRvci1zZWxlY3QtbGlzdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtBQUNGO0FBQ0U7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5mb3JtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAucGlja2VyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBtYXgtd2lkdGg6IDQ3MHB4O1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 21404:
/*!****************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/finance-report-with-history/finance-report-with-history.component.ts ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FinanceReportWithHistoryComponent: () => (/* binding */ FinanceReportWithHistoryComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-form/components */ 46282);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../schools-common/components/school-panel/school-panel.component */ 74153);
/* harmony import */ var _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../school-header/school-header.component */ 4840);
/* harmony import */ var _weekly_report_weekly_report_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../weekly-report/weekly-report.component */ 66292);















function FinanceReportWithHistoryComponent_form_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "form", 10)(1, "div", 11)(2, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](3, "input-date", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](4, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](5, "input-date", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](6, "p", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](7, "Export as .csv");
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](8, "div", 12)(9, "basic-button", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("click", function FinanceReportWithHistoryComponent_form_13_Template_basic_button_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵresetView"](ctx_r1.getRevenue());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("formGroup", ctx_r0.formGroupDates);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("error", ctx_r0.startDate.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("error", ctx_r0.endDate.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
  }
}
class FinanceReportWithHistoryComponent extends src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__.BaseFormComponent {
  constructor(router, merchantService, spinnerService, route) {
    super();
    this.router = router;
    this.merchantService = merchantService;
    this.spinnerService = spinnerService;
    this.route = route;
    this.invalidValueError = 'Invalid value entered';
    this.InvoiceType = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.InvoiceType;
  }
  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.generatedInvoiceList = data['generatedInvoiceList'];
    });
    this.createFrom();
  }
  createFrom() {
    this.formGroupDates = new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormGroup({
      startDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl([_angular_forms__WEBPACK_IMPORTED_MODULE_11__.Validators.required]),
      endDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControl([_angular_forms__WEBPACK_IMPORTED_MODULE_11__.Validators.required])
    });
  }
  goBackClick() {
    this.router.navigate(['./admin/merchants']);
  }
  getRevenue() {
    if (this.formGroupDates.invalid) {
      return;
    }
    this._downloadDatesReport();
  }
  _downloadDatesReport() {
    let filename = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.InvoiceType.Revenue + '.csv';
    const request = this.getDatesRequest();
    this.spinnerService.animatedStart();
    this.merchantService.GetInvoice(request).subscribe({
      next: res => {
        (0,src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.DownloadCSV)(filename, res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      }
    });
  }
  getDatesRequest() {
    return {
      exportType: src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.InvoiceTypeEnum.Revenue,
      startDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_2__.formatDateToUniversal)(this.startDate.value._d),
      endDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_2__.formatDateToUniversal)(this.endDate.value._d)
    };
  }
  get startDate() {
    return this.formGroupDates?.get('startDate');
  }
  get endDate() {
    return this.formGroupDates?.get('endDate');
  }
  static {
    this.ɵfac = function FinanceReportWithHistoryComponent_Factory(t) {
      return new (t || FinanceReportWithHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵdefineComponent"]({
      type: FinanceReportWithHistoryComponent,
      selectors: [["finance-report-with-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵInheritDefinitionFeature"]],
      decls: 14,
      vars: 2,
      consts: [["smallText", "true", "text", "Go Back", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "row"], [1, "col-12"], ["title", "Finance Reports v2"], [1, "col-lg-6", "col-md-9", "col-sm-12"], [1, "pt-4"], [3, "generatedInvoiceList"], [1, "mb-0", "panelTitle"], ["class", "pb-4", 3, "formGroup", 4, "ngIf"], [1, "pb-4", 3, "formGroup"], [1, "row", "pt-4"], [1, "col-6"], ["placeholder", "Start Date", "formControlName", "startDate", 3, "error"], ["placeholder", "End Date", "formControlName", "endDate", 3, "error"], [1, "col-12", "subtitle"], ["text", "Export", 3, "buttonStyle", "fullWidth", "click"]],
      template: function FinanceReportWithHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵlistener"]("navBack", function FinanceReportWithHistoryComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.goBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](4, "school-header", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](5, "div", 2)(6, "div", 5)(7, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelement"](8, "weekly-report", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementStart"](9, "div", 6)(10, "school-panel")(11, "p", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtext"](12, "Export settlement information for all active schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵtemplate"](13, FinanceReportWithHistoryComponent_form_13_Template, 10, 5, "form", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵelementEnd"]()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("generatedInvoiceList", ctx.generatedInvoiceList);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_10__["ɵɵproperty"]("ngIf", ctx.formGroupDates);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_11__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.NgControlStatusGroup, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_4__.InputDateComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__.NavBackButtonComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__.BasicButtonComponent, _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_7__.SchoolPanelComponent, _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_8__.SchoolHeaderComponent, _weekly_report_weekly_report_component__WEBPACK_IMPORTED_MODULE_9__.WeeklyReportComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.subtitle[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #878787;\n}\n\n.panelTitle[_ngcontent-%COMP%] {\n  font-weight: 700;\n  font-size: 16px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 78246:
/*!********************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/finance-report-with-history/generatedInvoiceList.resolver.ts ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GeneratedInvoiceList: () => (/* binding */ GeneratedInvoiceList)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedServices */ 2902);


const GeneratedInvoiceList = () => {
  const merchantService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_sharedServices__WEBPACK_IMPORTED_MODULE_0__.MerchantService);
  return merchantService.GetGeneratedInvoiceList();
};

/***/ }),

/***/ 73236:
/*!**************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/finance-report/finance-report.component.ts ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FinanceReportComponent: () => (/* binding */ FinanceReportComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-form/components */ 46282);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../schools-common/components/school-panel/school-panel.component */ 74153);
/* harmony import */ var _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../school-header/school-header.component */ 4840);

















function FinanceReportComponent_form_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](0, "form", 9)(1, "div", 2)(2, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](3, "input-select-list", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](4, "p", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtext"](5, "Export as .csv");
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](6, "div", 13)(7, "basic-button", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("click", function FinanceReportComponent_form_11_Template_basic_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r2.getSettlement());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](8, "div", 13)(9, "basic-button", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("click", function FinanceReportComponent_form_11_Template_basic_button_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r3);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r4.getInvoice());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("formGroup", ctx_r0.formGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("values", ctx_r0.selectWeekValues);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
  }
}
function FinanceReportComponent_form_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](0, "form", 9)(1, "div", 16)(2, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](3, "input-date", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](4, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](5, "input-date", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](6, "p", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtext"](7, "Export as .csv");
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](8, "div", 13)(9, "basic-button", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("click", function FinanceReportComponent_form_16_Template_basic_button_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r6);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r5.getRevenue());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("formGroup", ctx_r1.formGroupDates);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("error", ctx_r1.startDate.invalid ? ctx_r1.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("error", ctx_r1.endDate.invalid ? ctx_r1.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
  }
}
var WeekDateType;
(function (WeekDateType) {
  WeekDateType[WeekDateType["start"] = 0] = "start";
  WeekDateType[WeekDateType["end"] = 1] = "end";
})(WeekDateType || (WeekDateType = {}));
class FinanceReportComponent extends src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__.BaseFormComponent {
  constructor(router, merchantService, spinnerService) {
    super();
    this.router = router;
    this.merchantService = merchantService;
    this.spinnerService = spinnerService;
    this.selectWeekValues = [];
    this.invalidValueError = 'Invalid value entered';
  }
  ngOnInit() {
    this.getSelectWeekValues();
    // setup form for weekly export
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormGroup({
      week: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.selectWeekValues[0].key)
    });
    //set form for custom date export
    this.formGroupDates = new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormGroup({
      startDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl([_angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.required]),
      endDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl([_angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.required])
    });
  }
  goBackClick() {
    this.router.navigate(['./admin/merchants']);
  }
  /**
   * Get the values / text to display in the select list
   */
  getSelectWeekValues() {
    let endCurrentWeek = moment__WEBPACK_IMPORTED_MODULE_1__().endOf('week');
    this.selectWeekValues.push({
      key: this.getWeekYearNumber(endCurrentWeek),
      value: this.getWeekText(endCurrentWeek)
    });
    for (let index = 0; index < 80; index++) {
      endCurrentWeek = endCurrentWeek.subtract(1, 'week');
      this.selectWeekValues.push({
        key: this.getWeekYearNumber(endCurrentWeek),
        value: this.getWeekText(endCurrentWeek)
      });
    }
  }
  /**
   * Get the value to use in the select list
   * @param date
   * @returns
   */
  getWeekYearNumber(date) {
    return date.format('w-YYYY');
  }
  /**
   * Get the text to display in the select list
   * @param date
   * @returns
   */
  getWeekText(date) {
    const endDate = lodash__WEBPACK_IMPORTED_MODULE_2__.cloneDeep(date);
    const startDate = lodash__WEBPACK_IMPORTED_MODULE_2__.cloneDeep(date);
    const textStart = startDate.subtract(1, 'week').add(1, 'day').format('D-MMM-YYYY');
    const textEnd = endDate.format('D-MMM-YYYY');
    return textStart + ' to ' + textEnd;
  }
  getInvoice() {
    this._downloadReport(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__.InvoiceType.Invoice);
  }
  getSettlement() {
    this._downloadReport(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__.InvoiceType.Settlement);
  }
  getRevenue() {
    if (this.formGroupDates.invalid) {
      return;
    }
    this._downloadReport(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__.InvoiceType.Revenue);
  }
  /**
   * Download the report from the API
   * @param type
   */
  _downloadReport(type) {
    let filename = type + '.csv';
    const request = this.getRequest(type);
    this.spinnerService.animatedStart();
    this.merchantService.GetInvoice(request).subscribe({
      next: res => {
        var downloadURL = window.URL.createObjectURL(res);
        var link = document.createElement('a');
        link.href = downloadURL;
        link.download = filename;
        link.click();
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      }
    });
  }
  getRequest(type) {
    const selectedDateValues = this.getSelectedWeekAndYear();
    return {
      exportType: src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__.InvoiceTypeEnum[type],
      startDate: this.getRequestDate(type, selectedDateValues, WeekDateType.start),
      endDate: this.getRequestDate(type, selectedDateValues, WeekDateType.end)
    };
  }
  getSelectedWeekAndYear() {
    const weekYearKeyValueData = this.formGroup.get('week').value;
    const weekYearArray = weekYearKeyValueData.split('-');
    const selectedWeek = +weekYearArray[0];
    const selectedYear = +weekYearArray[1];
    return {
      selectedWeek,
      selectedYear
    };
  }
  getRequestDate(type, selectedDateValues, weekType) {
    const momentValue = type === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_3__.InvoiceType.Revenue ? this.getRevenueDate(weekType) : this.getWeekDate(selectedDateValues, weekType);
    return momentValue.format(src_app_utility__WEBPACK_IMPORTED_MODULE_4__.UNIVERSAL_DATE_FORMAT);
  }
  getRevenueDate(weekType) {
    return weekType === WeekDateType.start ? moment__WEBPACK_IMPORTED_MODULE_1__(this.startDate.value._d) : moment__WEBPACK_IMPORTED_MODULE_1__(this.endDate.value._d);
  }
  getWeekDate(selectedDateValues, weekType) {
    const year = selectedDateValues.selectedYear.toString(); // year value needs to be string
    const week = selectedDateValues.selectedWeek; // week value needs to be int
    const momentValue = moment__WEBPACK_IMPORTED_MODULE_1__(year).weeks(week);
    return weekType === WeekDateType.start ? momentValue.startOf('week') : momentValue.endOf('week');
  }
  get startDate() {
    return this.formGroupDates.get('startDate');
  }
  get endDate() {
    return this.formGroupDates.get('endDate');
  }
  static {
    this.ɵfac = function FinanceReportComponent_Factory(t) {
      return new (t || FinanceReportComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__.SpinnerService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdefineComponent"]({
      type: FinanceReportComponent,
      selectors: [["finance-report"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵInheritDefinitionFeature"]],
      decls: 17,
      vars: 2,
      consts: [["smallText", "true", "text", "Go Back", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "row"], [1, "col-12"], ["title", "Finance Reports"], [1, "col-lg-6", "col-md-9", "col-sm-12"], [1, "pt-4"], [1, "mb-0", "panelTitle"], ["class", "pb-4", 3, "formGroup", 4, "ngIf"], [1, "pb-4", 3, "formGroup"], [1, "col-12", "pt-4"], ["formControlName", "week", "placeholder", "Select week", 3, "values"], [1, "col-12", "subtitle"], [1, "col-6"], ["text", "Settlement", 3, "buttonStyle", "fullWidth", "click"], ["text", "Invoice", 3, "buttonStyle", "fullWidth", "click"], [1, "row", "pt-4"], ["placeholder", "Start Date", "formControlName", "startDate", 3, "error"], ["placeholder", "End Date", "formControlName", "endDate", 3, "error"], ["text", "Export", 3, "buttonStyle", "fullWidth", "click"]],
      template: function FinanceReportComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("navBack", function FinanceReportComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.goBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](4, "school-header", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](5, "div", 2)(6, "div", 5)(7, "div", 6)(8, "school-panel")(9, "p", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtext"](10, "Export weekly settlement information for all active schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtemplate"](11, FinanceReportComponent_form_11_Template, 10, 6, "form", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](12, "div", 6)(13, "school-panel")(14, "p", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtext"](15, "Export settlement information for all active schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtemplate"](16, FinanceReportComponent_form_16_Template, 10, 5, "form", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("ngIf", ctx.formGroup);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("ngIf", ctx.formGroup);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_15__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_13__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgControlStatusGroup, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_6__.InputSelectListComponent, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_7__.InputDateComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_8__.NavBackButtonComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_9__.BasicButtonComponent, _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_10__.SchoolPanelComponent, _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_11__.SchoolHeaderComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.subtitle[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #878787;\n}\n\n.panelTitle[_ngcontent-%COMP%] {\n  font-weight: 700;\n  font-size: 16px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 84326:
/*!****************************************************!*\
  !*** ./src/app/admin-merchant/components/index.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddMerchantUserSearchComponent: () => (/* reexport safe */ _add_merchant_user_search_add_merchant_user_search_component__WEBPACK_IMPORTED_MODULE_5__.AddMerchantUserSearchComponent),
/* harmony export */   AdminListMerchantsComponent: () => (/* reexport safe */ _admin_list_merchants_admin_list_merchants_component__WEBPACK_IMPORTED_MODULE_0__.AdminListMerchantsComponent),
/* harmony export */   CreateMerchantFormComponent: () => (/* reexport safe */ _create_merchant_form_create_merchant_form_component__WEBPACK_IMPORTED_MODULE_7__.CreateMerchantFormComponent),
/* harmony export */   CreateMerchantSearchComponent: () => (/* reexport safe */ _create_merchant_search_create_merchant_search_component__WEBPACK_IMPORTED_MODULE_8__.CreateMerchantSearchComponent),
/* harmony export */   FeeCalculatorFormComponent: () => (/* reexport safe */ _fee_calculator_form_fee_calculator_form_component__WEBPACK_IMPORTED_MODULE_14__.FeeCalculatorFormComponent),
/* harmony export */   FeeCalculatorResolver: () => (/* reexport safe */ _fee_calculator_form_feeCalculator_resolver__WEBPACK_IMPORTED_MODULE_16__.FeeCalculatorResolver),
/* harmony export */   FeeCalculatorSelectListComponent: () => (/* reexport safe */ _fee_calculator_select_list_fee_calculator_select_list_component__WEBPACK_IMPORTED_MODULE_15__.FeeCalculatorSelectListComponent),
/* harmony export */   FinanceReportComponent: () => (/* reexport safe */ _finance_report_finance_report_component__WEBPACK_IMPORTED_MODULE_13__.FinanceReportComponent),
/* harmony export */   FinanceReportWithHistoryComponent: () => (/* reexport safe */ _finance_report_with_history_finance_report_with_history_component__WEBPACK_IMPORTED_MODULE_17__.FinanceReportWithHistoryComponent),
/* harmony export */   LinkSchoolToMerchantPageComponent: () => (/* reexport safe */ _link_school_to_merchant_page_link_school_to_merchant_page_component__WEBPACK_IMPORTED_MODULE_3__.LinkSchoolToMerchantPageComponent),
/* harmony export */   MerchantContactDetailsComponent: () => (/* reexport safe */ _merchant_contact_details_merchant_contact_details_component__WEBPACK_IMPORTED_MODULE_10__.MerchantContactDetailsComponent),
/* harmony export */   MerchantDetailsComponent: () => (/* reexport safe */ _merchant_details_merchant_details_component__WEBPACK_IMPORTED_MODULE_9__.MerchantDetailsComponent),
/* harmony export */   MerchantLinkedSchoolsDetailsComponent: () => (/* reexport safe */ _merchant_linked_schools_details_merchant_linked_schools_details_component__WEBPACK_IMPORTED_MODULE_11__.MerchantLinkedSchoolsDetailsComponent),
/* harmony export */   MerchantLinkedSchoolsTableComponent: () => (/* reexport safe */ _merchant_linked_schools_table_merchant_linked_schools_table_component__WEBPACK_IMPORTED_MODULE_2__.MerchantLinkedSchoolsTableComponent),
/* harmony export */   MerchantSchoolSearchComponent: () => (/* reexport safe */ _merchant_school_search_merchant_school_search_component__WEBPACK_IMPORTED_MODULE_1__.MerchantSchoolSearchComponent),
/* harmony export */   MerchantUserFormComponent: () => (/* reexport safe */ _merchant_user_form_merchant_user_form_component__WEBPACK_IMPORTED_MODULE_6__.MerchantUserFormComponent),
/* harmony export */   MerchantUserPermissionsTableComponent: () => (/* reexport safe */ _merchant_user_permissions_table_merchant_user_permissions_table_component__WEBPACK_IMPORTED_MODULE_4__.MerchantUserPermissionsTableComponent),
/* harmony export */   SchoolHeaderComponent: () => (/* reexport safe */ _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_12__.SchoolHeaderComponent),
/* harmony export */   SchoolInvoicingFunctions: () => (/* reexport safe */ _merchant_linked_schools_details_merchant_linked_schools_details_component__WEBPACK_IMPORTED_MODULE_11__.SchoolInvoicingFunctions),
/* harmony export */   WeeklyInvoiceButtonsComponent: () => (/* reexport safe */ _weekly_invoice_buttons_weekly_invoice_buttons_component__WEBPACK_IMPORTED_MODULE_18__.WeeklyInvoiceButtonsComponent),
/* harmony export */   WeeklyReportComponent: () => (/* reexport safe */ _weekly_report_weekly_report_component__WEBPACK_IMPORTED_MODULE_19__.WeeklyReportComponent)
/* harmony export */ });
/* harmony import */ var _admin_list_merchants_admin_list_merchants_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./admin-list-merchants/admin-list-merchants.component */ 6446);
/* harmony import */ var _merchant_school_search_merchant_school_search_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./merchant-school-search/merchant-school-search.component */ 64394);
/* harmony import */ var _merchant_linked_schools_table_merchant_linked_schools_table_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./merchant-linked-schools-table/merchant-linked-schools-table.component */ 61900);
/* harmony import */ var _link_school_to_merchant_page_link_school_to_merchant_page_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./link-school-to-merchant-page/link-school-to-merchant-page.component */ 59566);
/* harmony import */ var _merchant_user_permissions_table_merchant_user_permissions_table_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./merchant-user-permissions-table/merchant-user-permissions-table.component */ 10616);
/* harmony import */ var _add_merchant_user_search_add_merchant_user_search_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./add-merchant-user-search/add-merchant-user-search.component */ 79260);
/* harmony import */ var _merchant_user_form_merchant_user_form_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./merchant-user-form/merchant-user-form.component */ 91068);
/* harmony import */ var _create_merchant_form_create_merchant_form_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./create-merchant-form/create-merchant-form.component */ 76922);
/* harmony import */ var _create_merchant_search_create_merchant_search_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./create-merchant-search/create-merchant-search.component */ 20298);
/* harmony import */ var _merchant_details_merchant_details_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./merchant-details/merchant-details.component */ 69536);
/* harmony import */ var _merchant_contact_details_merchant_contact_details_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./merchant-contact-details/merchant-contact-details.component */ 71733);
/* harmony import */ var _merchant_linked_schools_details_merchant_linked_schools_details_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./merchant-linked-schools-details/merchant-linked-schools-details.component */ 39536);
/* harmony import */ var _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./school-header/school-header.component */ 4840);
/* harmony import */ var _finance_report_finance_report_component__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./finance-report/finance-report.component */ 73236);
/* harmony import */ var _fee_calculator_form_fee_calculator_form_component__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./fee-calculator-form/fee-calculator-form.component */ 71548);
/* harmony import */ var _fee_calculator_select_list_fee_calculator_select_list_component__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./fee-calculator-select-list/fee-calculator-select-list.component */ 98492);
/* harmony import */ var _fee_calculator_form_feeCalculator_resolver__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./fee-calculator-form/feeCalculator.resolver */ 34989);
/* harmony import */ var _finance_report_with_history_finance_report_with_history_component__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./finance-report-with-history/finance-report-with-history.component */ 21404);
/* harmony import */ var _weekly_invoice_buttons_weekly_invoice_buttons_component__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./weekly-invoice-buttons/weekly-invoice-buttons.component */ 124);
/* harmony import */ var _weekly_report_weekly_report_component__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./weekly-report/weekly-report.component */ 66292);





















/***/ }),

/***/ 59566:
/*!******************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/link-school-to-merchant-page/link-school-to-merchant-page.component.ts ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LinkSchoolToMerchantPageComponent: () => (/* binding */ LinkSchoolToMerchantPageComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components/ */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _merchant_school_search_merchant_school_search_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../merchant-school-search/merchant-school-search.component */ 64394);








class LinkSchoolToMerchantPageComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(spinnerService, router, merchantService, dialog, route) {
    super();
    this.spinnerService = spinnerService;
    this.router = router;
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.route = route;
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
    });
  }
  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  GoBackClick() {
    this.spinnerService.start();
    this.router.navigate(['./admin/merchants']);
  }
  selectSchool(school) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = `Are you sure you want to link '${school.Name}' to ‘${this.selectedMerchant.name}’?`;
    data.TextLine2 = '';
    data.CancelButton = 'No, Cancel';
    data.ConfirmButton = 'Yes, link now';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmLinkSchool(school);
      }
    });
  }
  confirmLinkSchool(school) {
    this.spinnerService.start();
    this.merchantService.LinkSchoolToMerchant(this.selectedMerchant.canteenId, school.SchoolId).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp(school.Name);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup(school);
      }
    });
  }
  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup(school) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = `School could not be linked to merchant.`;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.confirmLinkSchool(school);
      }
    });
  }
  SuccessPopUp(schoolName) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = `School linked to merchant successfully.`;
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      //navigate back to home
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
    });
  }
  static {
    this.ɵfac = function LinkSchoolToMerchantPageComponent_Factory(t) {
      return new (t || LinkSchoolToMerchantPageComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: LinkSchoolToMerchantPageComponent,
      selectors: [["app-link-school-to-merchant-page"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 8,
      vars: 2,
      consts: [["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "header"], [1, "merchant-heading"], [3, "selectedMerchant", "selectSchool"]],
      template: function LinkSchoolToMerchantPageComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("navBack", function LinkSchoolToMerchantPageComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "h3", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, "Use the search bar to search for existing schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "merchant-school-search", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("selectSchool", function LinkSchoolToMerchantPageComponent_Template_merchant_school_search_selectSchool_7_listener($event) {
            return ctx.selectSchool($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"]("Link School to '", ctx.selectedMerchant.name, "'");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("selectedMerchant", ctx.selectedMerchant);
        }
      },
      dependencies: [_shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _merchant_school_search_merchant_school_search_component__WEBPACK_IMPORTED_MODULE_4__.MerchantSchoolSearchComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.merchant-heading[_ngcontent-%COMP%] {\n  font-size: 28px;\n  color: #1b1f39;\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 71733:
/*!**********************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-contact-details/merchant-contact-details.component.ts ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantContactDetailsComponent: () => (/* binding */ MerchantContactDetailsComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _models_base_merchant_form__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../models/base-merchant-form */ 6279);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);

// components







function MerchantContactDetailsComponent_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantContactDetailsComponent_button_5_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r3.triggerEdit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "img", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantContactDetailsComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div")(1, "ul")(2, "li")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Name: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "li")(7, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, "Email: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "li")(11, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](12, "Mobile: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("", ctx_r1.contactDetails.ownerFirstName, " ", ctx_r1.contactDetails.ownerLastName, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.contactDetails.ownerEmail);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.contactDetails.ownerMobile);
  }
}
function MerchantContactDetailsComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 5)(1, "form", 10)(2, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "input-text", 12)(4, "input-text", 13)(5, "input-text", 14)(6, "input-text", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 16)(8, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantContactDetailsComponent_div_9_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r6);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r5.saveContactDetails());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, " Save ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantContactDetailsComponent_div_9_Template_button_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r6);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r7.cancelEditPopup());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "Cancel");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx_r2.formGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.firstName.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.lastName.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.email.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.phone.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r2.isFormDisabled());
  }
}
const _c0 = function (a0) {
  return {
    disableCoverWhite: a0
  };
};
class MerchantContactDetailsComponent extends _models_base_merchant_form__WEBPACK_IMPORTED_MODULE_0__.BaseMerchantFormComponent {
  constructor(spinnerService, merchantService, dialog, phoneNumberService) {
    super(dialog, merchantService);
    this.spinnerService = spinnerService;
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.phoneNumberService = phoneNumberService;
  }
  ngOnInit() {
    this._baseOnInit();
  }
  ngOnDestroy() {
    this._baseOnDestroy();
  }
  /**
   * Retry popup after having an error when saving
   */
  SomethingWentWrongPopup() {
    const dialogRef = this._somethingWentWrongPopup();
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.saveContactDetails();
      }
    });
  }
  /**
   * Save the contactDetails
   */
  saveContactDetails() {
    this.spinnerService.start();
    let phoneNumber = this.phoneNumberService.serverMobileNumber(this.phone.value);
    let data = {
      ownerId: this.contactDetails.ownerId,
      ownerFirstName: this.firstName.value,
      ownerLastName: this.lastName.value,
      ownerMobile: phoneNumber
    };
    this.merchantService.UpdateMerchantContactDetails(this.selectedMerchant.canteenId, data).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp();
        //update form values
        this.contactDetails.ownerFirstName = this.firstName.value;
        this.contactDetails.ownerLastName = this.lastName.value;
        this.contactDetails.ownerMobile = phoneNumber;
        //update merchant sidebar values
        let fullName = this.firstName.value + ' ' + this.lastName.value;
        this.merchantService.UpsertMerchantToService(fullName, this.selectedMerchant.canteenId, this.selectedMerchant.ownerId, 'UserName');
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup();
      }
    });
  }
  ///////////////////////////////////
  // Form
  ///////////////////////////////////
  /**
   * Trigger the form creation
   */
  triggerEdit() {
    this._createForm();
    this.editDetailsMode = true;
    this.merchantService.setDisableMode(true);
  }
  /**
   * Setup the form with all the controls
   */
  _createForm() {
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      firstName: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.contactDetails.ownerFirstName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required),
      lastName: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.contactDetails.ownerLastName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required),
      email: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl({
        value: this.contactDetails.ownerEmail,
        disabled: true
      }, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.email]),
      phone: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.contactDetails.ownerMobile, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.pattern(/(^\+614[0-9]{8}$)|(^614[0-9]{8}$)|(^04[0-9]{8}$)/)])
    });
  }
  /**
   * Form controls accessors
   */
  get firstName() {
    return this.formGroup.get('firstName');
  }
  get lastName() {
    return this.formGroup.get('lastName');
  }
  get email() {
    return this.formGroup.get('email');
  }
  get phone() {
    return this.formGroup.get('phone');
  }
  static {
    this.ɵfac = function MerchantContactDetailsComponent_Factory(t) {
      return new (t || MerchantContactDetailsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.PhoneNumberService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MerchantContactDetailsComponent,
      selectors: [["merchant-contact-details"]],
      inputs: {
        contactDetails: "contactDetails"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 10,
      vars: 6,
      consts: [[1, "merchant-section"], [3, "ngClass"], [1, "details-header"], ["class", "editBtn", 3, "click", 4, "ngIf"], [1, "details-divider"], [1, "top-margin"], [4, "ngIf"], ["class", "top-margin", 4, "ngIf"], [1, "editBtn", 3, "click"], ["src", "assets/icons/orange-pencil.svg", "alt", "edit symbol", 1, "editIcon"], [1, "cashlessForm", 3, "formGroup"], [1, "editInput"], ["placeholder", "First Name", "formControlName", "firstName", 3, "error"], ["placeholder", "Last Name", "formControlName", "lastName", 3, "error"], ["placeholder", "Email", "formControlName", "email", 3, "error"], ["placeholder", "Mobile", "formControlName", "phone", 3, "error"], [1, "editBtnContainer"], [1, "saveBtn", 3, "disabled", "click"], [1, "cancelBtn", 3, "click"]],
      template: function MerchantContactDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div", 2)(3, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Contact Details");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, MerchantContactDetailsComponent_button_5_Template, 2, 0, "button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "hr", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantContactDetailsComponent_div_8_Template, 14, 4, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, MerchantContactDetailsComponent_div_9_Template, 12, 6, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c0, ctx.disableMode && !ctx.editDetailsMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.editDetailsMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.editDetailsMode && ctx.contactDetails);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.editDetailsMode);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__.InputTextComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.top-margin[_ngcontent-%COMP%] {\n  margin: 10px 0 30px 0;\n}\n\n.details-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n.details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 28px;\n  font-weight: 700;\n  color: #1b1f39;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  margin: 0;\n}\n\nul[_ngcontent-%COMP%] {\n  list-style-type: none;\n  padding: 0;\n  margin: 0;\n}\n\nli[_ngcontent-%COMP%] {\n  padding: 6px;\n  font-size: 14px;\n  margin: 0;\n  color: #1b1f39;\n}\n\n.editBtn[_ngcontent-%COMP%] {\n  cursor: pointer;\n  border: 0;\n  outline: 0;\n  background-color: white;\n}\n\n.editIcon[_ngcontent-%COMP%] {\n  height: 16px;\n  width: 16px;\n}\n\n.details-divider[_ngcontent-%COMP%] {\n  border: none;\n  height: 0.1px;\n  background-color: #b8b8b8;\n  margin: 5px 0;\n}\n\n.editInput[_ngcontent-%COMP%] {\n  width: 300px;\n}\n\n.editBtnContainer[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  margin: 10px 0 20px 0;\n}\n\n.saveBtn[_ngcontent-%COMP%] {\n  border-radius: 10px;\n  background-color: #ff7a00;\n  border: 0;\n  font-size: 18px;\n  color: white;\n  cursor: pointer;\n  width: 70px;\n  height: 32px;\n}\n\n.saveBtn[disabled][_ngcontent-%COMP%] {\n  background-color: #e5e5e5;\n  color: #b9b9c8;\n  cursor: default;\n}\n\n.cancelBtn[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  border: 0;\n  border: 0;\n  font-size: 18px;\n  background-color: white;\n  height: 32px;\n  cursor: pointer;\n  width: 70px;\n}\n\n.merchant-section[_ngcontent-%COMP%] {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: rgb(255, 255, 255);\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 69536:
/*!******************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-details/merchant-details.component.ts ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantDetailsComponent: () => (/* binding */ MerchantDetailsComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _models_base_merchant_form__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../models/base-merchant-form */ 6279);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);

// components







function MerchantDetailsComponent_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantDetailsComponent_button_5_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r3.triggerEdit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "img", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantDetailsComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div")(1, "ul")(2, "li")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Name: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "li")(7, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, "Friendly Name: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "li")(11, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](12, "Type: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "li")(15, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](16, "Phone: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.merchantDetails.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.merchantDetails.friendlyName);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.merchantDetails.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r1.displayLandLine(ctx_r1.merchantDetails.phone));
  }
}
function MerchantDetailsComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 5)(1, "form", 10)(2, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "input-text", 12)(4, "input-text", 13)(5, "input-text", 14)(6, "input-text", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 16)(8, "button", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantDetailsComponent_div_9_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r6);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r5.saveMerchantDetails());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, "Save");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantDetailsComponent_div_9_Template_button_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r6);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r7.cancelEditPopup());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "Cancel");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx_r2.formGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.name.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.friendlyName.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("error", ctx_r2.phone.invalid ? ctx_r2.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r2.isFormDisabled());
  }
}
const _c0 = function (a0) {
  return {
    disableCoverWhite: a0
  };
};
class MerchantDetailsComponent extends _models_base_merchant_form__WEBPACK_IMPORTED_MODULE_0__.BaseMerchantFormComponent {
  constructor(spinnerService, merchantService, dialog, phoneNumberService) {
    super(dialog, merchantService);
    this.spinnerService = spinnerService;
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.phoneNumberService = phoneNumberService;
  }
  ngOnInit() {
    this._baseOnInit();
  }
  ngOnDestroy() {
    this._baseOnDestroy();
  }
  /**
   * Retry popup after having an error when saving
   */
  SomethingWentWrongPopup() {
    const dialogRef = this._somethingWentWrongPopup();
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.saveMerchantDetails();
      }
    });
  }
  /**
   * Save the merchantDetails
   */
  saveMerchantDetails() {
    this.spinnerService.start();
    let merchantData = {
      name: this.name.value,
      friendlyName: this.friendlyName.value,
      type: this.type.value,
      phone: this.phoneNumberService.serverMobileNumber(this.phone.value),
      abn: '',
      status: null,
      statusDate: null
    };
    this.merchantService.UpdateMerchantDetails(this.selectedMerchant.canteenId, merchantData).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp();
        //update form values
        this.merchantDetails = merchantData;
        //update merchant sidebar values
        this.merchantService.UpsertMerchantToService(this.merchantDetails.name, this.selectedMerchant.canteenId, this.selectedMerchant.ownerId, 'Merchant');
      },
      error: error => {
        this.spinnerService.stop();
        this.SomethingWentWrongPopup();
      }
    });
  }
  /**
   * format landline numbers
   * @param phoneNumber
   * @returns
   */
  displayLandLine(phoneNumber) {
    if (phoneNumber && phoneNumber.length > 0) {
      if (phoneNumber.match(/^0[1-9]/) && !phoneNumber.match(/^04/)) {
        let landline = '(' + phoneNumber.slice(0, 2) + ') ' + phoneNumber.slice(2);
        return landline;
      }
    }
    return phoneNumber;
  }
  ///////////////////////////////////
  // Form
  ///////////////////////////////////
  /**
   * Trigger the form creation
   */
  triggerEdit() {
    this.ReminderPopUp();
    this._createForm();
    this.editDetailsMode = true;
    this.merchantService.setDisableMode(true);
  }
  /**
   * Setup the form with all the controls
   */
  _createForm() {
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.merchantDetails.name, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required),
      friendlyName: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.merchantDetails.friendlyName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required),
      type: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl({
        value: this.merchantDetails.type,
        disabled: true
      }, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required),
      phone: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.merchantDetails.phone, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.pattern(/(^\+614[0-9]{8}$)|(^614[0-9]{8}$)|(^04[0-9]{8}$)|(^0[0-9]{9}$)/)])
    });
  }
  /**
   * Form controls accessors
   */
  get name() {
    return this.formGroup.get('name');
  }
  get friendlyName() {
    return this.formGroup.get('friendlyName');
  }
  get type() {
    return this.formGroup.get('type');
  }
  get phone() {
    return this.formGroup.get('phone');
  }
  static {
    this.ɵfac = function MerchantDetailsComponent_Factory(t) {
      return new (t || MerchantDetailsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.PhoneNumberService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MerchantDetailsComponent,
      selectors: [["merchant-details"]],
      inputs: {
        merchantDetails: "merchantDetails"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 10,
      vars: 6,
      consts: [[1, "merchant-section"], [3, "ngClass"], [1, "details-header"], ["class", "editBtn", 3, "click", 4, "ngIf"], [1, "details-divider"], [1, "top-margin"], [4, "ngIf"], ["class", "top-margin", 4, "ngIf"], [1, "editBtn", 3, "click"], ["src", "assets/icons/orange-pencil.svg", "alt", "edit symbol", 1, "editIcon"], [1, "cashlessForm", 3, "formGroup"], [1, "editInput"], ["placeholder", "Name", "formControlName", "name", 3, "error"], ["placeholder", "Friendly Name", "formControlName", "friendlyName", 3, "error"], ["placeholder", "Type", "formControlName", "type"], ["placeholder", "Phone", "formControlName", "phone", 3, "error"], [1, "editBtnContainer"], [1, "saveBtn", 3, "disabled", "click"], [1, "cancelBtn", 3, "click"]],
      template: function MerchantDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div", 2)(3, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Merchant Details");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, MerchantDetailsComponent_button_5_Template, 2, 0, "button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "hr", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantDetailsComponent_div_8_Template, 18, 4, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, MerchantDetailsComponent_div_9_Template, 12, 5, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c0, ctx.disableMode && !ctx.editDetailsMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.editDetailsMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.editDetailsMode && ctx.merchantDetails);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.editDetailsMode && ctx.formGroup);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__.InputTextComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.top-margin[_ngcontent-%COMP%] {\n  margin: 10px 0 30px 0;\n}\n\n.details-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n.details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 28px;\n  font-weight: 700;\n  color: #1b1f39;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  margin: 0;\n}\n\nul[_ngcontent-%COMP%] {\n  list-style-type: none;\n  padding: 0;\n  margin: 0;\n}\n\nli[_ngcontent-%COMP%] {\n  padding: 6px;\n  font-size: 14px;\n  margin: 0;\n  color: #1b1f39;\n}\n\n.editBtn[_ngcontent-%COMP%] {\n  cursor: pointer;\n  border: 0;\n  outline: 0;\n  background-color: white;\n}\n\n.editIcon[_ngcontent-%COMP%] {\n  height: 16px;\n  width: 16px;\n}\n\n.details-divider[_ngcontent-%COMP%] {\n  border: none;\n  height: 0.1px;\n  background-color: #b8b8b8;\n  margin: 5px 0;\n}\n\n.editInput[_ngcontent-%COMP%] {\n  width: 300px;\n}\n\n.editBtnContainer[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  margin: 10px 0 20px 0;\n}\n\n.saveBtn[_ngcontent-%COMP%] {\n  border-radius: 10px;\n  background-color: #ff7a00;\n  border: 0;\n  font-size: 18px;\n  color: white;\n  cursor: pointer;\n  width: 70px;\n  height: 32px;\n}\n\n.saveBtn[disabled][_ngcontent-%COMP%] {\n  background-color: #e5e5e5;\n  color: #b9b9c8;\n  cursor: default;\n}\n\n.cancelBtn[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  border: 0;\n  border: 0;\n  font-size: 18px;\n  background-color: white;\n  height: 32px;\n  cursor: pointer;\n  width: 70px;\n}\n\n.merchant-section[_ngcontent-%COMP%] {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: rgb(255, 255, 255);\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 39536:
/*!************************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-linked-schools-details/merchant-linked-schools-details.component.ts ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantLinkedSchoolsDetailsComponent: () => (/* binding */ MerchantLinkedSchoolsDetailsComponent),
/* harmony export */   SchoolInvoicingFunctions: () => (/* binding */ SchoolInvoicingFunctions)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_shared_components___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/shared/components/ */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _schools_button_components_dropdown_button_dropdown_button_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../schools-button/components/dropdown-button/dropdown-button.component */ 17566);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../school-header/school-header.component */ 4840);



//models















function MerchantLinkedSchoolsDetailsComponent_div_1_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div")(1, "basic-button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function MerchantLinkedSchoolsDetailsComponent_div_1_div_21_Template_basic_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r3.clickEdit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("buttonStyle", 0);
  }
}
function MerchantLinkedSchoolsDetailsComponent_div_1_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div", 22)(1, "basic-button", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function MerchantLinkedSchoolsDetailsComponent_div_1_div_22_Template_basic_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r5.updateInformation(3));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](2, "basic-button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("click", function MerchantLinkedSchoolsDetailsComponent_div_1_div_22_Template_basic_button_click_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r6);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r7.areYouSureCancel());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("buttonStyle", 0)("disabled", ctx_r2.form.invalid);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("buttonStyle", 2);
  }
}
const _c0 = function (a0) {
  return {
    disableCoverGrey: a0
  };
};
function MerchantLinkedSchoolsDetailsComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div", 2)(1, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](2, "school-header", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](3, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](4, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](5, "dropdown-button", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("onPress", function MerchantLinkedSchoolsDetailsComponent_div_1_Template_dropdown_button_onPress_5_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r8.UpdateBillingStatus($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](6, "basic-button", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("onPress", function MerchantLinkedSchoolsDetailsComponent_div_1_Template_basic_button_onPress_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r9);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r10.unlinkSchool());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](7, "div", 9)(8, "form", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](9, "input-text", 11)(10, "input-date", 12)(11, "input-date", 13)(12, "input-select-list", 14)(13, "input-text", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](14, "div")(15, "mat-checkbox", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](16, "Waive Event Order Fee");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](17, "div")(18, "mat-checkbox", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](19, "Absorb Fees");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](20, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](21, MerchantLinkedSchoolsDetailsComponent_div_1_div_21_Template, 2, 1, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](22, MerchantLinkedSchoolsDetailsComponent_div_1_div_22_Template, 3, 3, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("title", ctx_r0.currentSchool.Name);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpureFunction1"](15, _c0, ctx_r0.editMode));
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("values", ctx_r0.schoolStatusOptions)("currentValue", ctx_r0.currentSchool.BillingStatus)("confirmation", ctx_r0.confirmStatusChange.asObservable());
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("buttonStyle", 1)("disabled", ctx_r0.currentSchool.BillingStatus === "Churned");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("formGroup", ctx_r0.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("error", ctx_r0.canteenFee.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("error", ctx_r0.startDate.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("error", ctx_r0.churnedDate.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("values", ctx_r0.listStatus);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("error", ctx_r0.instructions.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", !ctx_r0.editMode);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", ctx_r0.editMode);
  }
}
var SchoolInvoicingFunctions;
(function (SchoolInvoicingFunctions) {
  SchoolInvoicingFunctions[SchoolInvoicingFunctions["unlinkSchool"] = 0] = "unlinkSchool";
  SchoolInvoicingFunctions[SchoolInvoicingFunctions["updateStatus"] = 1] = "updateStatus";
  SchoolInvoicingFunctions[SchoolInvoicingFunctions["cancelChanges"] = 2] = "cancelChanges";
  SchoolInvoicingFunctions[SchoolInvoicingFunctions["saveChanges"] = 3] = "saveChanges";
})(SchoolInvoicingFunctions || (SchoolInvoicingFunctions = {}));
class MerchantLinkedSchoolsDetailsComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(location, merchantService, spinnerService, dialog) {
    super();
    this.location = location;
    this.merchantService = merchantService;
    this.spinnerService = spinnerService;
    this.dialog = dialog;
    this.MerchantStatus = _sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantStatus;
    this.isUniformMerchant = false;
    this.editMode = false;
    this.listStatus = [];
    this.confirmStatusChange = new rxjs__WEBPACK_IMPORTED_MODULE_12__.Subject();
    // const
    this.invalidValueError = 'Invalid value entered';
  }
  ngOnInit() {
    if (!history.state.row) {
      this.goBack();
    }
    // setup list status
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Default.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Default
    });
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Skip.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Skip
    });
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Demo.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Demo
    });
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Internal.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Internal
    });
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Extra.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Extra
    });
    this.listStatus.push({
      key: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatusEnum.Error.toString(),
      value: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.SchoolInternalStatus.Error
    });
    this.currentSchool = history.state.row;
    this.currentMerchant = history.state.selectedMerchant;
    this.merchantType = history.state.merchantType;
    this.currentBillingStatus = this.currentSchool.BillingStatus;
    this.schoolStatusOptions = [this.MerchantStatus.Active, this.MerchantStatus.Churned];
    this.CreateForm();
  }
  goBack() {
    this.location.back();
  }
  CreateForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormGroup({
      canteenFee: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.CanteenFee, [_angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.min(0), _angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.max(1)]),
      startDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.BillingStartDate, [_angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.required]),
      internalStatus: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.InternalStatus.toString(), [_angular_forms__WEBPACK_IMPORTED_MODULE_13__.Validators.required]),
      churnedDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.BillingEndDate),
      absorbFees: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.CanteenAbsorbsFees),
      instructions: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.SpecialInstructions),
      waiveEventOrderFee: new _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControl(this.currentSchool.WaiveEventOrderFee)
    });
    this.form.disable();
    this.internalStatus.setValue(this.currentSchool.InternalStatus.toString());
  }
  get canteenFee() {
    return this.form.get('canteenFee');
  }
  get waiveEventOrderFee() {
    return this.form.get('waiveEventOrderFee');
  }
  get startDate() {
    return this.form.get('startDate');
  }
  get internalStatus() {
    return this.form.get('internalStatus');
  }
  get churnedDate() {
    return this.form.get('churnedDate');
  }
  get instructions() {
    return this.form.get('instructions');
  }
  get absorbFees() {
    return this.form.get('absorbFees');
  }
  updateInformation(callingFunction) {
    this.spinnerService.start();
    if (this.currentSchool.BillingStatus == 'Churned') {
      this.merchantService.UpdateSchoolInternalStatus(this.currentSchool.SchoolCanteenId, +this.internalStatus.value).subscribe({
        next: res => {
          this.spinnerService.stop();
          this.closeForm();
          if (callingFunction === SchoolInvoicingFunctions.updateStatus) {
            this.confirmStatusChange.next();
            this.SuccessPopUp('School status successfully updated.', 1);
          } else {
            this.SuccessPopUp('School invoicing data successfully updated.', 3);
          }
        },
        error: error => {
          this.spinnerService.stop();
          let errorMessage = this.GetApiError(error, 'School invoicing data unable to be updated.');
          this.SomethingWentWrongPopup(errorMessage, callingFunction);
          this.handleErrorFromService(error);
        }
      });
    } else {
      let request = {
        SchoolId: this.currentSchool.SchoolId,
        CanteenId: this.currentMerchant.canteenId,
        CanteenFee: this.canteenFee.value,
        StartDate: this.currentSchool.StartDate,
        BillingStartDate: this.startDate.value ? moment__WEBPACK_IMPORTED_MODULE_0__(this.startDate.value).format('YYYY-MM-DD') : null,
        BillingEndDate: this.churnedDate.value ? moment__WEBPACK_IMPORTED_MODULE_0__(this.churnedDate.value).format('YYYY-MM-DD') : null,
        SpecialInstructions: this.instructions.value,
        CanteenType: this.merchantType,
        BillingStatus: _sharedModels__WEBPACK_IMPORTED_MODULE_1__.MerchantStatusEnum[this.currentBillingStatus],
        InternalStatus: +this.internalStatus.value,
        CanteenAbsorbsFees: this.absorbFees.value,
        WaiveEventOrderFee: this.waiveEventOrderFee.value
      };
      this.merchantService.UpdateSchoolInvoicingDetails(request.CanteenId, request.SchoolId, request).subscribe({
        next: res => {
          this.spinnerService.stop();
          this.closeForm();
          this.currentSchool.CanteenFee = request.CanteenFee;
          this.currentSchool.BillingStartDate = request.StartDate;
          this.currentSchool.BillingEndDate = request.BillingEndDate;
          this.currentSchool.SpecialInstructions = request.SpecialInstructions;
          this.currentSchool.CanteenAbsorbsFees = request.CanteenAbsorbsFees;
          if (callingFunction === SchoolInvoicingFunctions.updateStatus) {
            this.confirmStatusChange.next();
            this.SuccessPopUp('School status successfully updated.', 1);
          } else {
            this.SuccessPopUp('School invoicing data successfully updated.', 3);
          }
        },
        error: error => {
          this.spinnerService.stop();
          let errorMessage = this.GetApiError(error, 'School invoicing data unable to be updated.');
          this.SomethingWentWrongPopup(errorMessage, callingFunction);
          this.handleErrorFromService(error);
        }
      });
    }
  }
  ///////////////////////
  // Edit form
  ///////////////////////
  GetApiError(error, defaultErrorMessage) {
    if (!error || !error?.errors) {
      return defaultErrorMessage ?? 'Something went wrong';
    }
    let errorArray = [];
    let errorList = Object.entries(error.errors);
    errorList.forEach(err => errorArray.push(err[1][0]));
    // Show first error
    if (errorArray.length > 0) {
      return errorArray[0];
    }
    return defaultErrorMessage ?? 'Something went wrong';
  }
  clickEdit() {
    this.editMode = true;
    this.form.enable();
    if (!this.currentSchool.BillingEndDate) {
      this.churnedDate.disable();
    }
    if (!this.isNotChurned()) {
      this.canteenFee.disable();
      this.startDate.disable();
      this.churnedDate.disable();
      this.absorbFees.disable();
      this.instructions.disable();
      this.waiveEventOrderFee.disable();
    }
  }
  isNotChurned() {
    return this.currentSchool.BillingStatus != 'Churned';
  }
  areYouSureCancel() {
    let dialogMessage = 'Are you sure you want to cancel your changes?';
    let cancelMessage = 'No, Return';
    let confirmMessage = 'Yes, Cancel';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 2);
  }
  cancelChanges() {
    this.form.patchValue({
      canteenFee: this.currentSchool.CanteenFee,
      startDate: this.currentSchool.BillingStartDate,
      churnedDate: this.currentSchool.BillingEndDate,
      instructions: this.currentSchool.SpecialInstructions,
      absorbFees: this.currentSchool.CanteenAbsorbsFees
    });
    this.closeForm();
  }
  closeForm() {
    this.merchantService.setDisableMode(false);
    this.editMode = false;
    this.form.disable();
  }
  ///////////////////////
  // Unpdate school status
  ///////////////////////
  UpdateBillingStatus(billingStatus) {
    this.currentBillingStatus = billingStatus;
    let dialogMessage = `Are you sure you want to change the status of ${this.currentSchool.Name} to ${this.currentBillingStatus}?`;
    let cancelMessage = 'No, Cancel';
    let confirmMessage = 'Yes, Change status';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 1);
  }
  ///////////////////////
  // Unlinking Schools
  ///////////////////////
  unlinkSchool() {
    let dialogMessage = `Are you sure you want to unlink '${this.currentSchool.Name}' from '${this.currentMerchant.merchantName}'?`;
    let cancelMessage = 'Cancel';
    let confirmMessage = 'Yes, unlink now';
    this.areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, 0);
  }
  confirmUnlinkSchool() {
    this.spinnerService.start();
    this.merchantService.UnlinkSchoolFromMerchant(this.currentMerchant.canteenId, this.currentSchool.SchoolId).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp('School unlinked from merchant successfully.', 0);
      },
      error: error => {
        this.spinnerService.stop();
        let errorMessage = typeof error === 'string' ? error : 'School could not be unlinked from merchant.';
        this.SomethingWentWrongPopup(errorMessage, 0);
        this.handleErrorFromService(error);
      }
    });
  }
  ///////////////////////
  // Pop ups
  ///////////////////////
  triggerFunction(functionEnum) {
    switch (functionEnum) {
      case 0:
        this.confirmUnlinkSchool();
        break;
      case 1:
        this.updateInformation(1);
        break;
      case 2:
        this.cancelChanges();
        break;
      case 3:
        this.updateInformation(3);
        break;
      default:
        break;
    }
  }
  SomethingWentWrongPopup(dialogMessage, functionEnum) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = dialogMessage;
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_2__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.triggerFunction(functionEnum);
      }
    });
  }
  SuccessPopUp(dialogMessage, functionEnum) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = dialogMessage;
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_2__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        if (functionEnum === SchoolInvoicingFunctions.unlinkSchool) {
          this.location.back();
        }
      }
    });
  }
  areYouSurePopUp(dialogMessage, cancelMessage, confirmMessage, functionEnum) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = dialogMessage;
    data.CancelButton = cancelMessage;
    data.ConfirmButton = confirmMessage;
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_2__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        this.triggerFunction(functionEnum);
      }
    });
  }
  static {
    this.ɵfac = function MerchantLinkedSchoolsDetailsComponent_Factory(t) {
      return new (t || MerchantLinkedSchoolsDetailsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_14__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_15__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineComponent"]({
      type: MerchantLinkedSchoolsDetailsComponent,
      selectors: [["app-merchant-linked-schools-details"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵInheritDefinitionFeature"]],
      decls: 2,
      vars: 1,
      consts: [["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], ["class", "col-md-8 col-sm-12 page-container", 4, "ngIf"], [1, "col-md-8", "col-sm-12", "page-container"], [1, "d-flex", "justify-content-between", "align-items-center", "mt-3", "mb-3"], [3, "title"], [1, "d-flex", "justify-content-end", "buttonContainer"], [3, "ngClass"], ["label", "Status", "waitForConfirm", "true", 1, "mr-3", 3, "values", "currentValue", "confirmation", "onPress"], ["text", "Unlink School", 3, "buttonStyle", "disabled", "onPress"], [1, "form-container"], [1, "form", "m-4", "pt-4", "pb-4", 3, "formGroup"], ["placeholder", "Canteen Fee", "formControlName", "canteenFee", "inputType", "number", "step", "0.01", "min", "0", "max", "1", 3, "error"], ["placeholder", "Invoicing Start Date", "formControlName", "startDate", 3, "error"], ["placeholder", "Churned Date", "formControlName", "churnedDate", 3, "error"], ["formControlName", "internalStatus", "placeholder", "Internal Status", 3, "values"], ["placeholder", "Special Instructions", "formControlName", "instructions", "multiline", "true", 3, "error"], ["formControlName", "waiveEventOrderFee"], ["formControlName", "absorbFees"], [1, "pt-3"], [4, "ngIf"], ["class", "d-flex", 4, "ngIf"], ["text", "Edit", 3, "buttonStyle", "click"], [1, "d-flex"], ["text", "Save", 1, "mr-2", 3, "buttonStyle", "disabled", "click"], ["text", "Cancel", 3, "buttonStyle", "click"]],
      template: function MerchantLinkedSchoolsDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("navBack", function MerchantLinkedSchoolsDetailsComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.goBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](1, MerchantLinkedSchoolsDetailsComponent_div_1_Template, 23, 17, "div", 1);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", ctx.currentSchool);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_14__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_14__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_13__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.NgControlStatusGroup, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_4__.InputSelectListComponent, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_5__.InputTextComponent, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_6__.InputDateComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormControlName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_7__.NavBackButtonComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_8__.BasicButtonComponent, _schools_button_components_dropdown_button_dropdown_button_component__WEBPACK_IMPORTED_MODULE_9__.DropdownButtonComponent, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_16__.MatCheckbox, _school_header_school_header_component__WEBPACK_IMPORTED_MODULE_10__.SchoolHeaderComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.page-container[_ngcontent-%COMP%] {\n  max-width: 740px;\n}\n\nform[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 470px;\n}\n\n.form-container[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.05);\n  border-radius: 12px;\n}\n\n.buttonContainer[_ngcontent-%COMP%] {\n  position: relative;\n  width: 280px;\n}\n\n.disableCoverGrey[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #f2f2f2;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.activeBox[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n\n\n.form[_ngcontent-%COMP%]    .mat-form-field-disabled .mat-form-field-underline {\n  background-image: linear-gradient(to right, rgb(0, 0, 0) 0, rgba(0, 0, 0, 0.42) 33%, #c2c7cc 0) !important;\n  background-size: 1px 100% !important;\n  background-repeat: repeat-x !important;\n}\n\n.form[_ngcontent-%COMP%]    .mat-form-field-disabled .mat-mdc-input-element {\n  color: #1b1f39;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tbWVyY2hhbnQvY29tcG9uZW50cy9tZXJjaGFudC1saW5rZWQtc2Nob29scy1kZXRhaWxzL21lcmNoYW50LWxpbmtlZC1zY2hvb2xzLWRldGFpbHMuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi9zcmMvc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS0E7RUFDRSxhQUFBO0FDSkY7QURLRTtFQUZGO0lBR0ksY0FBQTtFQ0ZGO0FBQ0Y7O0FES0E7RUFDRSxhQUFBO0FDRkY7QURHRTtFQUZGO0lBR0ksY0FBQTtFQ0FGO0FBQ0Y7O0FBZEE7RUFDRSxnQkFBQTtBQWlCRjs7QUFkQTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtBQWlCRjs7QUFkQTtFQUNFLHlCQUFBO0VBQ0EsMkNBQUE7RUFDQSxtQkFBQTtBQWlCRjs7QUFkQTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtBQWlCRjs7QUFkQTtFQUNFLGtCQUFBO0VBQ0EseUJDa0JPO0VEakJQLFlBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7QUFpQkY7O0FBZEE7RUFDRSxtQkFBQTtBQWlCRjs7QUFWQSwrSEFBQTtBQUNBO0VBRUUsMEdBQUE7RUFNQSxvQ0FBQTtFQUNBLHNDQUFBO0FBT0Y7O0FBSkE7RUFDRSxjQ0hRO0FEVVYiLCJzb3VyY2VzQ29udGVudCI6WyIkYnJlYWtwb2ludC1zbTogNTc2cHg7XG4kYnJlYWtwb2ludC1tZDogNzY3cHg7XG4kYnJlYWtwb2ludC1sZzogOTkycHg7XG4kYnJlYWtwb2ludC14bDogMTIwMHB4O1xuXG4ubW9iaWxlIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtYXgtd2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbi8vIE5PVEUgQ3VycmVudGx5IHRhYmxldCBhbmQgbW9iaWxlIGlzIHRoZSBzYW1lLiBDaGFuZ2UgdG8gJGJyZWFrcG9pbnQtbGcgbGF0ZXIgaWYgd2UgZ2V0IGEgcHJvcGVyIHRhYmxldCBkZXNpZ24uXG4uZGVza3RvcCB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWluLXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4iLCJAaW1wb3J0ICcuLi8uLi8uLi8uLi9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2Nzcyc7XG5cbi5wYWdlLWNvbnRhaW5lciB7XG4gIG1heC13aWR0aDogNzQwcHg7XG59XG5cbmZvcm0ge1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiA0NzBweDtcbn1cblxuLmZvcm0tY29udGFpbmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcbiAgYm94LXNoYWRvdzogMHB4IDNweCAzcHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbn1cblxuLmJ1dHRvbkNvbnRhaW5lciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgd2lkdGg6IDI4MHB4O1xufVxuXG4uZGlzYWJsZUNvdmVyR3JleSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYmFja2dyb3VuZC1jb2xvcjogJGdyZXktNDtcbiAgb3BhY2l0eTogMC41O1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICB6LWluZGV4OiAxMDA7XG59XG5cbi5hY3RpdmVCb3gge1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufVxuXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuLy8gQ3VzdG9tIHN0eWxlIGZvciBpbnB1dC10ZXh0XG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuXG4vKlRPRE8obWRjLW1pZ3JhdGlvbik6IFRoZSBmb2xsb3dpbmcgcnVsZSB0YXJnZXRzIGludGVybmFsIGNsYXNzZXMgb2YgZm9ybS1maWVsZCB0aGF0IG1heSBubyBsb25nZXIgYXBwbHkgZm9yIHRoZSBNREMgdmVyc2lvbi4qL1xuLmZvcm0gOjpuZy1kZWVwLm1hdC1mb3JtLWZpZWxkLWRpc2FibGVkIC5tYXQtZm9ybS1maWVsZC11bmRlcmxpbmUge1xuICAvL21ha2VzIHRoZSBkb3R0ZWQgdW5kZXJsaW5lIGZvciB0aGUgZGlhYmxlZCBpbnB1dCBhcHBlYXIgYXMgYSBzb2xpZCBsaW5lXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudChcbiAgICB0byByaWdodCxcbiAgICByZ2JhKDAsIDAsIDAsIDEpIDAsXG4gICAgcmdiYSgwLCAwLCAwLCAwLjQyKSAzMyUsXG4gICAgI2MyYzdjYyAwXG4gICkgIWltcG9ydGFudDtcbiAgYmFja2dyb3VuZC1zaXplOiAxcHggMTAwJSAhaW1wb3J0YW50O1xuICBiYWNrZ3JvdW5kLXJlcGVhdDogcmVwZWF0LXggIWltcG9ydGFudDtcbn1cblxuLmZvcm0gOjpuZy1kZWVwLm1hdC1mb3JtLWZpZWxkLWRpc2FibGVkIC5tYXQtbWRjLWlucHV0LWVsZW1lbnQge1xuICBjb2xvcjogJGdyZXktMTI7XG59XG4iLCJAaW1wb3J0ICdjYXNobGVzcy1icmVha3BvaW50cyc7XG5cbi8vIFByaW1hcnkgY29sb3Vyc1xuXG4kYmx1ZS0xOiAjMWVhM2NlO1xuJGJsdWUtMjogIzQ3NTRiMDtcbiRibHVlLTM6ICMxNDRjZGM7XG5cbiRyZWQtMTogI2YxNDc2MjtcbiRyZWQtMjogI2ZmZWJlYjtcbiRyZWQtMzogI2MwNDU0NTtcbiRyZWQtNDogI2ZmY2ZjYztcblxuJHB1cnBsZS0xOiAjN2YzZGIzO1xuJG5hdnktMTogIzFjNDI3MDtcbiRjaGFyY29hbC0xOiAjMzMzYjQ0O1xuXG4kZ3JlZW4tMTogIzAwYmE2YjtcbiRncmVlbi0yOiAjZDlmNWU5O1xuJGdyZWVuLTM6ICMwMDZmNDk7XG4kZ3JlZW4tNDogI2UzZjVlZjtcbiRncmVlbi01OiAjZGZmZmYwO1xuXG4vLyBPcmFuZ2VcbiRvcmFuZ2UtMTogI2ZmOWUwMDtcbiRvcmFuZ2UtMzogI2ZmN2EwMDtcbiRvcmFuZ2UtMjogI2ZmNGIxNztcbiRvcmFuZ2UtNDogI2ZmZTJjNztcbiRvcmFuZ2UtNTogI2ZmZjJlNjtcbiRvcmFuZ2UtNjogI2ZmODAwMDtcbiRvcmFuZ2UtNzogI2ZmZWFkNjtcbiRvcmFuZ2UtODogI2ZlZjBlMDtcbiRvcmFuZ2UtOTogI2ZmZjBlMDtcbiRvcmFuZ2UtMTA6ICNmMzY2MDA7XG4kb3JhbmdlLTExOiAjZmZlM2JiO1xuJG1vYmlsZS1kYXJrLW9yYW5nZTogI0Q5NUIwMDtcbiRtb2JpbGUtbGlnaHQtb3JhbmdlOiAjRkZFQUQ2O1xuXG4vLyBncmV5XG4kZ3JleS0xOiAjODg5NDlmO1xuJGdyZXktMjogI2UwZTFlMjtcbiRncmV5LTM6ICNkZGRkZGQ7XG4kZ3JleS00OiAjZjJmMmYyO1xuJGdyZXktNTogIzMzM2I0NDtcbiRncmV5LTY6ICNlNWU1ZTU7XG4kZ3JleS03OiAjYjliOWM4O1xuJGdyZXktODogIzg3ODc4NztcbiRncmV5LTk6ICNlMGUwZTA7XG4kZ3JleS0xMDogI2JkYmRiZDtcbiRncmV5LTExOiAjODI4MjgyO1xuJGdyZXktMTI6ICMxYjFmMzk7XG4kZ3JleS0xMzogI2I4YjhiODtcbiRncmV5LTE0OiAjMjcyYzUwO1xuJGdyZXktMTU6ICNmNmY1ZjM7XG4kZ3JleS0xNjogI2ZhZjlmODtcbiRncmV5LTE3OiAjNmI2Yzg5O1xuXG4vLyBTZWNvbmRhcnkgY29sb3Vyc1xuJGJsdWUtc2Vjb25kYXJ5LTE6IHJnYmEoMjU1LCAyNDMsIDIxOSwgMSk7XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 61900:
/*!********************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-linked-schools-table/merchant-linked-schools-table.component.ts ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantLinkedSchoolsTableComponent: () => (/* binding */ MerchantLinkedSchoolsTableComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/icon */ 93840);











function MerchantLinkedSchoolsTableComponent_table_7_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "School");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 22)(1, "h5")(2, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const element_r16 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r16.Name);
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Status");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 23)(1, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r17 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r17.BillingStatus);
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Canteen Fee");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 23)(1, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r18 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](3, 1, element_r18.CanteenFee * 100, "1.2"), "%");
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_th_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Start Date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 23)(1, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r19 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](3, 1, element_r19.StartDate, "dd/MM/yyyy"));
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_th_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Invoicing Start Date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 22)(1, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](3, 1, element_r20.BillingStartDate, "dd/MM/yyyy"));
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_th_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "th", 24);
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_td_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 25)(1, "mat-icon", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "chevron_right");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_tr_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 27);
  }
}
const _c0 = function (a0, a1, a2) {
  return {
    row: a0,
    selectedMerchant: a1,
    merchantType: a2
  };
};
function MerchantLinkedSchoolsTableComponent_table_7_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 28);
  }
  if (rf & 2) {
    const row_r22 = ctx.$implicit;
    const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate2"]("routerLink", "./", ctx_r15.selectedMerchant.canteenId, "/school/", row_r22.SchoolId, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("state", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](3, _c0, row_r22, ctx_r15.selectedMerchant, ctx_r15.merchantType));
  }
}
function MerchantLinkedSchoolsTableComponent_table_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "table", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](1, 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantLinkedSchoolsTableComponent_table_7_th_2_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, MerchantLinkedSchoolsTableComponent_table_7_td_3_Template, 4, 1, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](4, 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, MerchantLinkedSchoolsTableComponent_table_7_th_5_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, MerchantLinkedSchoolsTableComponent_table_7_td_6_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](7, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantLinkedSchoolsTableComponent_table_7_th_8_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, MerchantLinkedSchoolsTableComponent_table_7_td_9_Template, 4, 4, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](10, 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, MerchantLinkedSchoolsTableComponent_table_7_th_11_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, MerchantLinkedSchoolsTableComponent_table_7_td_12_Template, 4, 4, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](13, 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, MerchantLinkedSchoolsTableComponent_table_7_th_14_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](15, MerchantLinkedSchoolsTableComponent_table_7_td_15_Template, 4, 4, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](16, 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](17, MerchantLinkedSchoolsTableComponent_table_7_th_17_Template, 1, 0, "th", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](18, MerchantLinkedSchoolsTableComponent_table_7_td_18_Template, 3, 0, "td", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](19, MerchantLinkedSchoolsTableComponent_table_7_tr_19_Template, 1, 0, "tr", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, MerchantLinkedSchoolsTableComponent_table_7_tr_20_Template, 1, 7, "tr", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("dataSource", ctx_r0.dataSource);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matHeaderRowDef", ctx_r0.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matRowDefColumns", ctx_r0.displayedColumns);
  }
}
function MerchantLinkedSchoolsTableComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "No linked schools");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
const _c1 = function (a0) {
  return {
    disableCoverWhite: a0
  };
};
const _columns = ['school', 'status', 'canteen-fee', 'start-date', 'invoicing-start-date', 'chevron'];
class MerchantLinkedSchoolsTableComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(merchantService, dialog, router) {
    super();
    this.merchantService = merchantService;
    this.dialog = dialog;
    this.router = router;
    this.stopLoad = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.dataSource = new _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTableDataSource();
    this.displayedColumns = _columns;
    this.disableMode = false;
  }
  ngOnInit() {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();
    this.loadData();
    this.trackSelectedMerchant = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
      this.loadData();
    });
    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }
  ngOnDestroy() {
    if (this.editSubscription) {
      this.editSubscription.unsubscribe();
    }
    if (this.trackSelectedMerchant) {
      this.trackSelectedMerchant.unsubscribe();
    }
  }
  loadData() {
    //global spinner is started in admin-list-merchants component
    //stopload.emit communicates with admin-list-merchants component to turn it off
    this.merchantService.GetSchoolsLinkedToMerchant(this.selectedMerchant.canteenId).subscribe({
      next: res => {
        this.RefreshTable(res);
        this.stopLoad.emit();
      },
      error: error => {
        this.stopLoad.emit();
        this.handleErrorFromService(error);
      }
    });
  }
  RefreshTable(schools) {
    this.dataSource.data = schools;
  }
  isListEmpty() {
    return !this.dataSource.data || this.dataSource.data.length === 0;
  }
  LinkSchoolClick() {
    this.router.navigate([`./admin/merchants/${this.selectedMerchant.canteenId}/schoolSearch`]);
  }
  static {
    this.ɵfac = function MerchantLinkedSchoolsTableComponent_Factory(t) {
      return new (t || MerchantLinkedSchoolsTableComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MerchantLinkedSchoolsTableComponent,
      selectors: [["merchant-linked-schools-table"]],
      inputs: {
        merchantType: "merchantType"
      },
      outputs: {
        stopLoad: "stopLoad"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 9,
      vars: 6,
      consts: [[1, "merchant-section"], [3, "ngClass"], [1, "details-header"], ["text", "Link School", 3, "buttonStyle", "onPress"], [1, "details-divider"], ["mat-table", "", "class", "table", 3, "dataSource", 4, "ngIf"], ["class", "emptyMessage", 4, "ngIf"], ["mat-table", "", 1, "table", 3, "dataSource"], ["matColumnDef", "school"], ["mat-header-cell", "", "class", "header", 4, "matHeaderCellDef"], ["mat-cell", "", "class", "noBorder", 4, "matCellDef"], ["matColumnDef", "status"], ["mat-cell", "", "class", "noBorder smallColumn", 4, "matCellDef"], ["matColumnDef", "canteen-fee"], ["matColumnDef", "start-date"], ["matColumnDef", "invoicing-start-date"], ["matColumnDef", "chevron", 1, "result-tab"], ["mat-header-cell", "", "class", "header smallColumn", 4, "matHeaderCellDef"], ["mat-cell", "", "class", "noBorder smallColumn lastColumn", 4, "matCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 3, "routerLink", "state", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", "", 1, "header"], ["mat-cell", "", 1, "noBorder"], ["mat-cell", "", 1, "noBorder", "smallColumn"], ["mat-header-cell", "", 1, "header", "smallColumn"], ["mat-cell", "", 1, "noBorder", "smallColumn", "lastColumn"], [1, "chevron"], ["mat-header-row", ""], ["mat-row", "", 3, "routerLink", "state"], [1, "emptyMessage"]],
      template: function MerchantLinkedSchoolsTableComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div", 2)(3, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Schools linked to Merchant");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "basic-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("onPress", function MerchantLinkedSchoolsTableComponent_Template_basic_button_onPress_5_listener() {
            return ctx.LinkSchoolClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "hr", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, MerchantLinkedSchoolsTableComponent_table_7_Template, 21, 3, "table", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantLinkedSchoolsTableComponent_div_8_Template, 2, 0, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c1, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("buttonStyle", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.isListEmpty());
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isListEmpty());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterLink, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__.BasicButtonComponent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_8__.MatIcon, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRow, _angular_common__WEBPACK_IMPORTED_MODULE_7__.DecimalPipe, _angular_common__WEBPACK_IMPORTED_MODULE_7__.DatePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.top-margin[_ngcontent-%COMP%] {\n  margin: 10px 0 30px 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #ffffff;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.details-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n.details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 28px;\n  font-weight: 700;\n  color: #1b1f39;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  margin: 0;\n}\n\n.merchant-section[_ngcontent-%COMP%] {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #ffffff;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.table[_ngcontent-%COMP%] {\n  width: 100%;\n  overflow: hidden;\n  margin-bottom: 30px;\n  border-color: #ffffff;\n}\n.table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(odd) {\n  background-color: #f6f5f3;\n  border-color: #ffffff;\n}\n.table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(even) {\n  background-color: #faf9f8;\n}\n.table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\n  background-color: #fff2e6;\n  cursor: pointer;\n}\n\n.mat-mdc-header-row[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #ffffff;\n  border-top: 1px solid #ffffff;\n  background-color: #ffffff;\n  color: #6b6c89;\n  font-size: 14px;\n  font-weight: 700;\n  padding-right: 8px;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #ffffff;\n  border-top: 1px solid #ffffff;\n}\n\n.editIcon[_ngcontent-%COMP%] {\n  height: 16px;\n  width: 16px;\n}\n\n.mat-column-school[_ngcontent-%COMP%] {\n  color: #1b1f39;\n}\n.mat-column-school[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  font-size: 14px;\n  line-height: 24px;\n  padding: 0;\n  margin: 0;\n}\n\n.link[_ngcontent-%COMP%] {\n  float: right;\n  color: #ff7a00;\n  font-weight: 700;\n  cursor: pointer;\n}\n\n.merchant-btn[_ngcontent-%COMP%] {\n  background-color: #272c50;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 10px;\n  outline: none;\n  border: none;\n  color: #ffffff;\n  font-size: 18px;\n  line-height: 24px;\n  padding: 6px 16px;\n  border-radius: 14px;\n  font-weight: 700;\n  cursor: pointer;\n}\n.merchant-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n\n.emptyMessage[_ngcontent-%COMP%] {\n  padding: 10px;\n  font-size: 14px;\n  line-height: 24px;\n}\n\n.lastColumn[_ngcontent-%COMP%] {\n  padding-right: 18px !important;\n  text-align: right;\n  color: #ff7a00;\n}\n\n.result-tab[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n  cursor: pointer;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4tbWVyY2hhbnQvY29tcG9uZW50cy9tZXJjaGFudC1saW5rZWQtc2Nob29scy10YWJsZS9tZXJjaGFudC1saW5rZWQtc2Nob29scy10YWJsZS5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFmQTtFQUNFLHFCQUFBO0FBa0JGOztBQWZBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7QUFrQkY7O0FBZkE7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBa0JGO0FBaEJFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0MyQk07RUQxQk4sZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFrQko7O0FBZEE7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0FBaUJGOztBQWRBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7QUFpQkY7O0FBZEE7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBaUJGO0FBaEJFO0VBQ0UseUJDRU07RURETixxQkFBQTtBQWtCSjtBQWhCRTtFQUNFLHlCQ0RNO0FEbUJWO0FBaEJFO0VBQ0UseUJDOUJPO0VEK0JQLGVBQUE7QUFrQko7O0FBZEE7RUFDRSxnQ0FBQTtFQUNBLDZCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQ1pRO0VEYVIsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFpQkY7O0FBZEE7RUFDRSxnQ0FBQTtFQUNBLDZCQUFBO0FBaUJGOztBQWRBO0VBQ0UsWUFBQTtFQUNBLFdBQUE7QUFpQkY7O0FBZEE7RUFDRSxjQ2xDUTtBRG1EVjtBQWZFO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsVUFBQTtFQUNBLFNBQUE7QUFpQko7O0FBYkE7RUFDRSxZQUFBO0VBQ0EsY0N2RVM7RUR3RVQsZ0JBQUE7RUFDQSxlQUFBO0FBZ0JGOztBQWJBO0VBQ0UseUJDbERRO0VEbURSLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7QUFnQkY7QUFkRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0FBZ0JKOztBQVpBO0VBQ0UsYUFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtBQWVGOztBQVpBO0VBQ0UsOEJBQUE7RUFDQSxpQkFBQTtFQUNBLGNDM0dTO0FEMEhYOztBQWJBO0VBQ0UseUJDekdTO0VEMEdULGVBQUE7QUFnQkYiLCJzb3VyY2VzQ29udGVudCI6WyIkYnJlYWtwb2ludC1zbTogNTc2cHg7XG4kYnJlYWtwb2ludC1tZDogNzY3cHg7XG4kYnJlYWtwb2ludC1sZzogOTkycHg7XG4kYnJlYWtwb2ludC14bDogMTIwMHB4O1xuXG4ubW9iaWxlIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtYXgtd2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbi8vIE5PVEUgQ3VycmVudGx5IHRhYmxldCBhbmQgbW9iaWxlIGlzIHRoZSBzYW1lLiBDaGFuZ2UgdG8gJGJyZWFrcG9pbnQtbGcgbGF0ZXIgaWYgd2UgZ2V0IGEgcHJvcGVyIHRhYmxldCBkZXNpZ24uXG4uZGVza3RvcCB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWluLXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4iLCJAaW1wb3J0ICcuLi8uLi8uLi8uLi9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2Nzcyc7XG4udG9wLW1hcmdpbiB7XG4gIG1hcmdpbjogMTBweCAwIDMwcHggMDtcbn1cblxuLmRpc2FibGVDb3ZlcldoaXRlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBvcGFjaXR5OiAwLjU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIHotaW5kZXg6IDEwMDtcbn1cblxuLmRldGFpbHMtaGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW4tdG9wOiAxMHB4O1xuXG4gIGg0IHtcbiAgICBmb250LXNpemU6IDI4cHg7XG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICBjb2xvcjogJGdyZXktMTI7XG4gICAgcGFkZGluZy10b3A6IDhweDtcbiAgICBwYWRkaW5nLWJvdHRvbTogOHB4O1xuICAgIG1hcmdpbjogMDtcbiAgfVxufVxuXG4ubWVyY2hhbnQtc2VjdGlvbiB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgbWFyZ2luOiAwO1xuICBwYWRkaW5nOiAwO1xufVxuXG4uZGlzYWJsZUNvdmVyV2hpdGUge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XG4gIG9wYWNpdHk6IDAuNTtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgei1pbmRleDogMTAwO1xufVxuXG4udGFibGUge1xuICB3aWR0aDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgYm9yZGVyLWNvbG9yOiAjZmZmZmZmO1xuICB0cjpudGgtY2hpbGQob2RkKSB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogJGdyZXktMTU7XG4gICAgYm9yZGVyLWNvbG9yOiAjZmZmZmZmO1xuICB9XG4gIHRyOm50aC1jaGlsZChldmVuKSB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogJGdyZXktMTY7XG4gIH1cbiAgdHI6aG92ZXIge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICRvcmFuZ2UtNTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gIH1cbn1cblxuLm1hdC1tZGMtaGVhZGVyLXJvdyAubWF0LW1kYy1oZWFkZXItY2VsbCB7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZmZmZmZmO1xuICBib3JkZXItdG9wOiAxcHggc29saWQgI2ZmZmZmZjtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcbiAgY29sb3I6ICRncmV5LTE3O1xuICBmb250LXNpemU6IDE0cHg7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIHBhZGRpbmctcmlnaHQ6IDhweDtcbn1cblxuLm1hdC1tZGMtcm93IC5tYXQtbWRjLWNlbGwge1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ZmZmZmZjtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmZmZmZmY7XG59XG5cbi5lZGl0SWNvbiB7XG4gIGhlaWdodDogMTZweDtcbiAgd2lkdGg6IDE2cHg7XG59XG5cbi5tYXQtY29sdW1uLXNjaG9vbCB7XG4gIGNvbG9yOiAkZ3JleS0xMjtcblxuICBoNSB7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGxpbmUtaGVpZ2h0OiAyNHB4O1xuICAgIHBhZGRpbmc6IDA7XG4gICAgbWFyZ2luOiAwO1xuICB9XG59XG5cbi5saW5rIHtcbiAgZmxvYXQ6IHJpZ2h0O1xuICBjb2xvcjogJG9yYW5nZS0zO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG5cbi5tZXJjaGFudC1idG4ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAkZ3JleS0xNDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBnYXA6IDEwcHg7XG4gIG91dGxpbmU6IG5vbmU7XG4gIGJvcmRlcjogbm9uZTtcbiAgY29sb3I6ICNmZmZmZmY7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgbGluZS1oZWlnaHQ6IDI0cHg7XG4gIHBhZGRpbmc6IDZweCAxNnB4O1xuICBib3JkZXItcmFkaXVzOiAxNHB4O1xuICBmb250LXdlaWdodDogNzAwO1xuICBjdXJzb3I6IHBvaW50ZXI7XG5cbiAgaW1nIHtcbiAgICB3aWR0aDogMTZweDtcbiAgICBoZWlnaHQ6IDE2cHg7XG4gIH1cbn1cblxuLmVtcHR5TWVzc2FnZSB7XG4gIHBhZGRpbmc6IDEwcHg7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgbGluZS1oZWlnaHQ6IDI0cHg7XG59XG5cbi5sYXN0Q29sdW1uIHtcbiAgcGFkZGluZy1yaWdodDogMThweCAhaW1wb3J0YW50O1xuICB0ZXh0LWFsaWduOiByaWdodDtcbiAgY29sb3I6ICRvcmFuZ2UtMztcbn1cbi5yZXN1bHQtdGFiOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogJG9yYW5nZS03O1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG4iLCJAaW1wb3J0ICdjYXNobGVzcy1icmVha3BvaW50cyc7XG5cbi8vIFByaW1hcnkgY29sb3Vyc1xuXG4kYmx1ZS0xOiAjMWVhM2NlO1xuJGJsdWUtMjogIzQ3NTRiMDtcbiRibHVlLTM6ICMxNDRjZGM7XG5cbiRyZWQtMTogI2YxNDc2MjtcbiRyZWQtMjogI2ZmZWJlYjtcbiRyZWQtMzogI2MwNDU0NTtcbiRyZWQtNDogI2ZmY2ZjYztcblxuJHB1cnBsZS0xOiAjN2YzZGIzO1xuJG5hdnktMTogIzFjNDI3MDtcbiRjaGFyY29hbC0xOiAjMzMzYjQ0O1xuXG4kZ3JlZW4tMTogIzAwYmE2YjtcbiRncmVlbi0yOiAjZDlmNWU5O1xuJGdyZWVuLTM6ICMwMDZmNDk7XG4kZ3JlZW4tNDogI2UzZjVlZjtcbiRncmVlbi01OiAjZGZmZmYwO1xuXG4vLyBPcmFuZ2VcbiRvcmFuZ2UtMTogI2ZmOWUwMDtcbiRvcmFuZ2UtMzogI2ZmN2EwMDtcbiRvcmFuZ2UtMjogI2ZmNGIxNztcbiRvcmFuZ2UtNDogI2ZmZTJjNztcbiRvcmFuZ2UtNTogI2ZmZjJlNjtcbiRvcmFuZ2UtNjogI2ZmODAwMDtcbiRvcmFuZ2UtNzogI2ZmZWFkNjtcbiRvcmFuZ2UtODogI2ZlZjBlMDtcbiRvcmFuZ2UtOTogI2ZmZjBlMDtcbiRvcmFuZ2UtMTA6ICNmMzY2MDA7XG4kb3JhbmdlLTExOiAjZmZlM2JiO1xuJG1vYmlsZS1kYXJrLW9yYW5nZTogI0Q5NUIwMDtcbiRtb2JpbGUtbGlnaHQtb3JhbmdlOiAjRkZFQUQ2O1xuXG4vLyBncmV5XG4kZ3JleS0xOiAjODg5NDlmO1xuJGdyZXktMjogI2UwZTFlMjtcbiRncmV5LTM6ICNkZGRkZGQ7XG4kZ3JleS00OiAjZjJmMmYyO1xuJGdyZXktNTogIzMzM2I0NDtcbiRncmV5LTY6ICNlNWU1ZTU7XG4kZ3JleS03OiAjYjliOWM4O1xuJGdyZXktODogIzg3ODc4NztcbiRncmV5LTk6ICNlMGUwZTA7XG4kZ3JleS0xMDogI2JkYmRiZDtcbiRncmV5LTExOiAjODI4MjgyO1xuJGdyZXktMTI6ICMxYjFmMzk7XG4kZ3JleS0xMzogI2I4YjhiODtcbiRncmV5LTE0OiAjMjcyYzUwO1xuJGdyZXktMTU6ICNmNmY1ZjM7XG4kZ3JleS0xNjogI2ZhZjlmODtcbiRncmV5LTE3OiAjNmI2Yzg5O1xuXG4vLyBTZWNvbmRhcnkgY29sb3Vyc1xuJGJsdWUtc2Vjb25kYXJ5LTE6IHJnYmEoMjU1LCAyNDMsIDIxOSwgMSk7XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 64394:
/*!******************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-school-search/merchant-school-search.component.ts ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantSchoolSearchComponent: () => (/* binding */ MerchantSchoolSearchComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-common/components/search-panel/search-panel.component */ 43913);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/table */ 77697);







function MerchantSchoolSearchComponent_h3_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r0.noResultsMessage);
  }
}
function MerchantSchoolSearchComponent_div_4_th_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "school ID");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r20.SchoolId);
  }
}
function MerchantSchoolSearchComponent_div_4_th_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r21.Name);
  }
}
function MerchantSchoolSearchComponent_div_4_th_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Cut off time");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_10_span_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](2, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](2, 1, element_r22.CutOffTime, "shortTime"), " ");
  }
}
function MerchantSchoolSearchComponent_div_4_td_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, MerchantSchoolSearchComponent_div_4_td_10_span_1_Template, 3, 4, "span", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r22 = ctx.$implicit;
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r7.ShowCutOffTime(element_r22.CutOffTime));
  }
}
function MerchantSchoolSearchComponent_div_4_th_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Phone");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r25 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r25.PhoneNumber);
  }
}
function MerchantSchoolSearchComponent_div_4_th_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "School code");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r26 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r26.SchoolCode);
  }
}
function MerchantSchoolSearchComponent_div_4_th_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Pricing model");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r27 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r27.PricingModel);
  }
}
function MerchantSchoolSearchComponent_div_4_th_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Pricing amount");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](2, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r28 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](2, 1, element_r28.PricingAmount));
  }
}
function MerchantSchoolSearchComponent_div_4_th_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Pricing Cap");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_td_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r29 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r29.PricingCap);
  }
}
function MerchantSchoolSearchComponent_div_4_tr_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 18);
  }
}
function MerchantSchoolSearchComponent_div_4_tr_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r32 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "tr", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function MerchantSchoolSearchComponent_div_4_tr_27_Template_tr_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r32);
      const row_r30 = restoredCtx.$implicit;
      const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r31.schoolSelected(row_r30));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantSchoolSearchComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div")(1, "table", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](2, 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, MerchantSchoolSearchComponent_div_4_th_3_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](4, MerchantSchoolSearchComponent_div_4_td_4_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](5, 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, MerchantSchoolSearchComponent_div_4_th_6_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, MerchantSchoolSearchComponent_div_4_td_7_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](8, 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, MerchantSchoolSearchComponent_div_4_th_9_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, MerchantSchoolSearchComponent_div_4_td_10_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](11, 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, MerchantSchoolSearchComponent_div_4_th_12_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](13, MerchantSchoolSearchComponent_div_4_td_13_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](14, 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](15, MerchantSchoolSearchComponent_div_4_th_15_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](16, MerchantSchoolSearchComponent_div_4_td_16_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](17, 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](18, MerchantSchoolSearchComponent_div_4_th_18_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](19, MerchantSchoolSearchComponent_div_4_td_19_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](20, 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, MerchantSchoolSearchComponent_div_4_th_21_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, MerchantSchoolSearchComponent_div_4_td_22_Template, 3, 3, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](23, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](24, MerchantSchoolSearchComponent_div_4_th_24_Template, 2, 0, "th", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, MerchantSchoolSearchComponent_div_4_td_25_Template, 2, 1, "td", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](26, MerchantSchoolSearchComponent_div_4_tr_26_Template, 1, 0, "tr", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](27, MerchantSchoolSearchComponent_div_4_tr_27_Template, 1, 0, "tr", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("dataSource", ctx_r1.dataSource);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matHeaderRowDef", ctx_r1.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matRowDefColumns", ctx_r1.displayedColumns);
  }
}
const _columns = ['id', 'name', 'cutOffTime', 'deactivatedFilters', 'schoolCode', 'pricingModel', 'pricingAmount', 'pricingCap'];
class MerchantSchoolSearchComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BasePaginatorComponent {
  constructor(spinnerService, merchantService) {
    super(_columns);
    this.spinnerService = spinnerService;
    this.merchantService = merchantService;
    this.selectSchool = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.noResultsMessage = '';
    this.showResultsTable = false;
  }
  ngOnInit() {
    this.initFilters();
  }
  clearFilter() {
    this.clearFiltersAndResults();
  }
  fetchData(searchInput) {
    this.listfilters.Filter = searchInput;
    this._requestSchools();
  }
  /** load search data */
  _requestSchools() {
    this.spinnerService.start();
    this.merchantService.GetMerchantSchoolsSearchReuslts(this.selectedMerchant.canteenId, this.listfilters.Filter).subscribe({
      next: res => {
        this._ProcessResponseSchools(res);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  /** Process the list of schools to be used in the search results */
  _ProcessResponseSchools(response) {
    if (response) {
      this.listObjects = response;
      if (this.listObjects && this.listObjects.length > 0) {
        this.totalRows = this.listObjects[0].TotalRows;
        this.showResults();
      } else {
        this.totalRows = 0;
      }
    } else {
      this.noSearchResults(this.listfilters.Filter);
    }
    this.dataSource.data = this.listObjects;
    this.spinnerService.stop();
  }
  schoolSelected(school) {
    this.selectSchool.emit(school);
  }
  ShowCutOffTime(time) {
    let compare = new Date('0001-01-01');
    return new Date(time) > compare;
  }
  static {
    this.ɵfac = function MerchantSchoolSearchComponent_Factory(t) {
      return new (t || MerchantSchoolSearchComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MerchantSchoolSearchComponent,
      selectors: [["merchant-school-search"]],
      inputs: {
        selectedMerchant: "selectedMerchant"
      },
      outputs: {
        selectSchool: "selectSchool"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 6,
      vars: 3,
      consts: [["placeholder", "Search the name of a school...", 3, "searchInput", "triggerSearch", "triggerClear"], [4, "ngIf"], [2, "height", "70px"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "schoolTable", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "name"], ["matColumnDef", "cutOffTime"], ["matColumnDef", "deactivatedFilters"], ["matColumnDef", "schoolCode"], ["matColumnDef", "pricingModel"], ["matColumnDef", "pricingAmount"], ["matColumnDef", "pricingCap"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 3, "click", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], ["mat-header-row", ""], ["mat-row", "", 3, "click"]],
      template: function MerchantSchoolSearchComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div")(1, "search-panel", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("triggerSearch", function MerchantSchoolSearchComponent_Template_search_panel_triggerSearch_1_listener($event) {
            return ctx.fetchData($event);
          })("triggerClear", function MerchantSchoolSearchComponent_Template_search_panel_triggerClear_1_listener() {
            return ctx.clearFilter();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, MerchantSchoolSearchComponent_h3_3_Template, 2, 1, "h3", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](4, MerchantSchoolSearchComponent_div_4_Template, 28, 3, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("searchInput", ctx.listfilters.Filter);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.noResultsMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showResultsTable);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _schools_common_components_search_panel_search_panel_component__WEBPACK_IMPORTED_MODULE_2__.SearchPanelComponent, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_5__.MatRow, _angular_common__WEBPACK_IMPORTED_MODULE_4__.CurrencyPipe, _angular_common__WEBPACK_IMPORTED_MODULE_4__.DatePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.filterField[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.noResults[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 18px;\n}\n.noResults[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  padding: 0;\n  padding-left: 5px;\n  margin: 0;\n  font-weight: 400;\n  font-size: 16px;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n  cursor: pointer;\n}\n\n.schoolTable[_ngcontent-%COMP%] {\n  width: 100%;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 91068:
/*!**********************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-user-form/merchant-user-form.component.ts ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantUserFormComponent: () => (/* binding */ MerchantUserFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components/ */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/form-field */ 24950);













function MerchantUserFormComponent_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_button_5_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r8.addUser());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Add User ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function MerchantUserFormComponent_button_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_button_6_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r11);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r10.removeUser());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "img", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Remove User ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function MerchantUserFormComponent_form_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "form", 28)(1, "div", 29)(2, "mat-form-field", 30)(3, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "input", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "mat-form-field", 32)(7, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8, "Email");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](9, "input", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx_r2.form);
  }
}
function MerchantUserFormComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 14)(1, "a", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_div_11_Template_a_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r12.formSelect("School", false));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, "Clear");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "a", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_div_11_Template_a_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r14.formSelect("School", true));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Select All");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function MerchantUserFormComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 34)(1, "mat-checkbox", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const i_r16 = ctx.index;
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formControlName", i_r16);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r4.schoolData[i_r16].Name, " ");
  }
}
function MerchantUserFormComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "No linked schools");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function MerchantUserFormComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "img", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r6.getSchoolError());
  }
}
function MerchantUserFormComponent_div_39_button_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_div_39_button_1_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r19);
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r18.updateUser());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " Save Changes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", ctx_r17.disableSaveChanges());
  }
}
function MerchantUserFormComponent_div_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](1, MerchantUserFormComponent_div_39_button_1_Template, 2, 1, "button", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r7.editMode);
  }
}
const _c0 = function (a0) {
  return {
    error: a0
  };
};
class MerchantUserFormComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(spinnerService, router, merchantService, formBuilder, dialog, route) {
    super();
    this.spinnerService = spinnerService;
    this.router = router;
    this.merchantService = merchantService;
    this.formBuilder = formBuilder;
    this.dialog = dialog;
    this.route = route;
    this.submitError = false;
    this.editUserInput = false;
    this.formTouched = false;
    this.checkedSchoolIndex = [];
    //get current route
    this.routeSubscription = router.events.subscribe(route => this.currentRoute = route);
  }
  ngOnInit() {
    // get data from resolver
    this.route.data.subscribe(data => {
      this.selectedMerchant = data['merchant'];
      this.currentUser = data['user'];
      this.schoolData = data['schools'];
    });
    //set state of page
    this.editMode = this.currentRoute.url.includes('userSearch') ? false : true;
    this.pageTitle = this.currentRoute.url.includes('userSearch') ? 'Add merchant user' : 'Edit merchant user';
    this.FindIndexOfCheckedSchools();
    this.CreateForm();
  }
  ngOnDestroy() {
    //clear selectedMerchant if navigating off a merchant page
    if (!this.currentRoute.url.includes('merchants')) {
      this.merchantService.setSelectedMerchant(null);
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (!this.editMode) {
      //clear search filter if navigating off search result pages
      if (!this.currentRoute.url.includes('userSearch')) {
        this.merchantService.setMerchantSearchFilters(null);
      }
    }
    this.merchantService.setDisableMode(false);
  }
  GoBackClick() {
    if (this.editMode) {
      let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
      data.TitleLine1 = 'Are you sure?';
      data.TextLine1 = 'Are you sure you want to leave this page?';
      data.TextLine2 = 'Changes you made will not be saved.';
      data.CancelButton = 'Stay on Page';
      data.ConfirmButton = 'Leave Page';
      const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data
      });
      dialogRef.afterClosed().subscribe(stayResult => {
        if (!stayResult) {
          this.spinnerService.start();
          this.router.navigate(['./admin/merchants']);
        }
      });
    } else {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants/' + this.selectedMerchant.canteenId + '/userSearch']);
    }
  }
  isSchoolListEmpty() {
    return !this.schoolData || this.schoolData.length === 0;
  }
  get schoolFormArray() {
    return this.schoolForm.controls.schools;
  }
  //return array of indexes of selected schools in schoolData
  FindIndexOfCheckedSchools() {
    if (this.currentUser && this.currentUser.Schools && this.editMode) {
      this.currentUser.Schools.forEach(selectedSchool => {
        let index = this.schoolData.findIndex(schoolOption => schoolOption.SchoolId === selectedSchool.SchoolId);
        this.checkedSchoolIndex.push(index);
      });
    }
  }
  //function to see if current index matches a selected school index
  matchIndex(count) {
    let result = false;
    this.checkedSchoolIndex.forEach(selectedSchool => {
      if (count === selectedSchool) {
        result = true;
      }
    });
    return result;
  }
  CreateForm() {
    const name = this.currentUser.FirstName + ' ' + this.currentUser.Lastname;
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroup({
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(name, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      email: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser.Email, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.email]),
      isAdmin: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.IsAdmin),
      menuEditor: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.IsMenuEditorAvailable),
      salesReport: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.IsSaleReportsAvailable),
      viewEvent: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.IsEventManagementAvailable),
      allowUnprintedOrders: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.IsOrdersNotPrintedReportsAvailable),
      emailUnprintedOrders: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.currentUser?.NotifyOrdersNotPrinted)
    });
    //generate dynamic schools form
    this.schoolForm = this.formBuilder.group({
      schools: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormArray([])
    });
    // Create form control for each school checkbox value
    if (this.schoolData) {
      this.schoolData.forEach((el, count) => {
        //check if school index matches the index of currentUsers selected school
        if (this.editMode && this.matchIndex(count)) {
          this.schoolFormArray.push(new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(true));
        } else {
          this.schoolFormArray.push(new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(false));
        }
      });
    }
  }
  //function for 'Select all' and 'Clear' buttons
  formSelect(formType, setValue) {
    this.formTouched = true;
    if (formType === _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormPermissions.user) {
      this.setUserPermissions(setValue);
    } else {
      this.schoolFormArray.setValue(this.setAllSchoolValues(setValue));
    }
  }
  //function to set all user permission to a boolean val
  setUserPermissions(value) {
    this.form.patchValue({
      emailUnprintedOrders: value,
      allowUnprintedOrders: value,
      salesReport: value,
      menuEditor: value,
      viewEvent: value
    });
  }
  // function to generate an array of values to mimic select all or clear all for the schools form
  setAllSchoolValues(value) {
    let valueArray = [];
    this.schoolData.forEach(() => {
      valueArray.push(value);
    });
    return valueArray;
  }
  //function to genrate a list of all selected schools in form
  getSelectedSchools() {
    const selectedSchoolIds = this.schoolForm.value.schools.map((v, i) => v ? this.schoolData[i].SchoolId : null).filter(v => v !== null);
    return selectedSchoolIds;
  }
  getSchoolError() {
    if (this.isSchoolListEmpty()) {
      return 'Merchant must have at least one school to make user changes';
    } else {
      return 'Selected school permissions';
    }
  }
  getPermissionsData() {
    let permissions = {
      isAdmin: this.form.get('isAdmin').value,
      isMenuEditorAvailable: this.form.get('menuEditor').value,
      isSaleReportsAvailable: this.form.get('salesReport').value,
      isOrdersNotPrintedReportsAvailable: this.form.get('allowUnprintedOrders').value,
      isEventManagementAvailable: this.form.get('viewEvent').value,
      notifyOrdersNotPrinted: this.form.get('emailUnprintedOrders').value
    };
    let selectedSchools = this.getSelectedSchools();
    let data = {
      userId: this.currentUser.UserId,
      schools: selectedSchools,
      permissions: permissions
    };
    return data;
  }
  isFormInvalid() {
    return this.form.invalid || this.getSelectedSchools().length === 0;
  }
  disableSaveChanges() {
    if (this.formTouched) {
      return !this.formTouched;
    } else {
      return !(this.form.dirty || this.schoolForm.dirty);
    }
  }
  ///////////////////////
  // Add User
  ///////////////////////
  addUser() {
    if (this.isFormInvalid()) {
      this.submitError = true;
    } else {
      this.submitError = false;
      let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
      data.TitleLine1 = 'Add merchant user';
      data.TextLine1 = `Are you sure you want to add ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) as a Merchant User?`;
      data.TextLine2 = '';
      data.CancelButton = 'Cancel';
      data.ConfirmButton = 'Yes, add now';
      const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
        width: '400px',
        disableClose: true,
        data: data
      });
      dialogRef.afterClosed().subscribe(cancelResult => {
        if (!cancelResult) {
          this.ConfirmAddUser();
        }
      });
    }
  }
  ConfirmAddUser() {
    let newUser = this.getPermissionsData();
    this.spinnerService.start();
    this.merchantService.AddUserToMerchantAdmin(this.selectedMerchant.canteenId, newUser).subscribe(res => {
      this.spinnerService.stop();
      this.SuccessPopUp(`${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) added successfully.`);
    }, error => {
      this.spinnerService.stop();
      this.handleErrorFromService(error);
      this.SomethingWentWrongPopup(`We were unable to add ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`, _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormType.Add);
    });
  }
  ///////////////////////
  // Update User
  ///////////////////////
  updateUser() {
    if (this.isFormInvalid()) {
      this.submitError = true;
    } else {
      this.submitError = false;
      this.spinnerService.start();
      let data = this.getPermissionsData();
      this.merchantService.UpdateMerchantAdminPermissions(this.selectedMerchant.canteenId, this.currentUser.UserId, data).subscribe({
        next: res => {
          this.spinnerService.stop();
          this.SuccessPopUp(`${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) updated successfully.`);
        },
        error: error => {
          this.SomethingWentWrongPopup(`We were unable to update ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`, _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormType.Update);
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        }
      });
    }
  }
  ///////////////////////
  // Remove User
  ///////////////////////
  removeUser() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Remove merchant user';
    data.TextLine1 = `Are you sure you want to remove ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}) as a Merchant User?`;
    data.TextLine2 = '';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, remove';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelRemove => {
      if (!cancelRemove) {
        this.ConfirmRemoveUser();
      }
    });
  }
  ConfirmRemoveUser() {
    this.spinnerService.start();
    this.merchantService.RemoveMerchantAdminFromMerchant(this.selectedMerchant.canteenId, this.currentUser.UserId).subscribe({
      next: res => {
        this.spinnerService.stop();
        this.SuccessPopUp(`Merchant removed successfully.`);
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
        this.SomethingWentWrongPopup(`We were unable to remove ${this.currentUser.FirstName} ${this.currentUser.Lastname} (${this.currentUser.Email}).`, _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormType.Remove);
      }
    });
  }
  ///////////////////////
  // Pop ups
  ///////////////////////
  SomethingWentWrongPopup(text, type) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = text;
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (!cancelResult) {
        if (type === _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormType.Add) {
          this.addUser();
        } else if (type === _sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantFormType.Update) {
          this.updateUser();
        } else {
          this.removeUser();
        }
      }
    });
  }
  SuccessPopUp(text) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = text;
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components___WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      this.spinnerService.start();
      this.router.navigate(['./admin/merchants']);
      this.merchantService.setUpdateMerchantUserPermissions(true);
    });
  }
  static {
    this.ɵfac = function MerchantUserFormComponent_Factory(t) {
      return new (t || MerchantUserFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: MerchantUserFormComponent,
      selectors: [["app-merchant-user-form"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 40,
      vars: 14,
      consts: [["smallText", "true", "text", "Go Back", "smallFont", "true", "noPadding", "true", 1, "backButton", 3, "navBack"], [1, "container-fluid"], [1, "header"], [1, "merchant-heading"], ["class", "merchant-btn", 3, "click", 4, "ngIf"], ["class", "user-form", 3, "formGroup", 4, "ngIf"], [1, "sub-heading-wrapper"], [1, "sub-heading"], ["class", "button-wrapper", 4, "ngIf"], [3, "formGroup"], [1, "form-wrapper", "school-form", 3, "ngClass"], ["class", "checkbox-wrapper", "formArrayName", "schools", 4, "ngFor", "ngForOf"], ["class", "emptyMessage", 4, "ngIf"], ["class", "school-error-message", 4, "ngIf"], [1, "button-wrapper"], [3, "click"], [2, "font-weight", "700", 3, "click"], [1, "form-wrapper", "permissions-form"], ["formControlName", "isAdmin"], ["formControlName", "menuEditor"], ["formControlName", "salesReport"], ["formControlName", "viewEvent"], ["formControlName", "allowUnprintedOrders"], ["formControlName", "emailUnprintedOrders"], ["class", "save-btn-wrapper", 4, "ngIf"], [1, "merchant-btn", 3, "click"], ["src", "assets/icons/white-plus.svg", "alt", "plus symbol"], ["src", "assets/icons/white-dash.svg", "alt", "white dash"], [1, "user-form", 3, "formGroup"], [1, "input-wrapper"], ["appearance", "outline"], ["matInput", "", "placeholder", "Name", "formControlName", "name", "type", "text", "required", "", "readonly", ""], ["appearance", "outline", 1, "email-input"], ["matInput", "", "placeholder", "Email", "formControlName", "email", "type", "email", "required", "", "readonly", ""], ["formArrayName", "schools", 1, "checkbox-wrapper"], [3, "formControlName"], [1, "emptyMessage"], [1, "school-error-message"], ["src", "assets/icons/black-error-icon.svg", "alt", "error symbol"], [1, "save-btn-wrapper"], ["class", "save-changes-btn", 3, "disabled", "click", 4, "ngIf"], [1, "save-changes-btn", 3, "disabled", "click"]],
      template: function MerchantUserFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "nav-back-button", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("navBack", function MerchantUserFormComponent_Template_nav_back_button_navBack_0_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "h3", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](5, MerchantUserFormComponent_button_5_Template, 3, 0, "button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, MerchantUserFormComponent_button_6_Template, 3, 0, "button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, MerchantUserFormComponent_form_7_Template, 10, 1, "form", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 6)(9, "h4", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10, "School Permissions");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](11, MerchantUserFormComponent_div_11_Template, 5, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "form", 9)(13, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](14, MerchantUserFormComponent_div_14_Template, 3, 2, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, MerchantUserFormComponent_div_15_Template, 2, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](16, MerchantUserFormComponent_div_16_Template, 4, 1, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "div", 6)(18, "h4", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, "User Permissions");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "div", 14)(21, "a", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_Template_a_click_21_listener() {
            return ctx.formSelect("User", false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](22, "Clear");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "a", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function MerchantUserFormComponent_Template_a_click_23_listener() {
            return ctx.formSelect("User", true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](24, "Select All");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "form", 9)(26, "div", 17)(27, "mat-checkbox", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](28, " Is Admin ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](29, "mat-checkbox", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, " View menu editor ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "mat-checkbox", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](32, " View sales reports ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "mat-checkbox", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](34, " View event management ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](35, "mat-checkbox", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](36, " Allow unprinted orders ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](37, "mat-checkbox", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](38, " Email unprinted orders ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](39, MerchantUserFormComponent_div_39_Template, 2, 1, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.pageTitle);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.editMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.editMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.schoolData);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx.schoolForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](12, _c0, ctx.submitError));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx.schoolFormArray.controls);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.isSchoolListEmpty());
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.submitError);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.editMode);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControlName, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormArrayName, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_9__.MatCheckbox, _angular_material_input__WEBPACK_IMPORTED_MODULE_10__.MatInput, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_11__.MatLabel],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.merchant-heading[_ngcontent-%COMP%] {\n  font-size: 28px;\n  color: #1b1f39;\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.user-form[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 15px;\n  margin-top: 30px;\n}\n\n.mat-mdc-form-field[_ngcontent-%COMP%] {\n  width: 200px;\n}\n\n.mat-mdc-form-field.email-input[_ngcontent-%COMP%] {\n  width: 270px;\n}\n\n.sub-heading-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  margin-bottom: 8px;\n}\n\n.sub-heading[_ngcontent-%COMP%] {\n  font-size: 18px;\n  color: #1b1f39;\n  font-weight: 700;\n  padding: 0;\n  margin-bottom: 0px;\n}\n\n.button-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 10px;\n  padding-right: 15px;\n}\n.button-wrapper[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  font-size: 14px;\n  cursor: pointer;\n}\n\n.form-wrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.user-form.form-wrapper[_ngcontent-%COMP%] {\n  padding-top: 18px;\n  padding-bottom: 5px;\n}\n\n.school-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  flex-basis: 25%;\n}\n.school-form[_ngcontent-%COMP%]   .checkbox-wrapper[_ngcontent-%COMP%] {\n  width: 33.3333333333%;\n  margin: 0;\n  padding: 0;\n}\n\n.school-form.error[_ngcontent-%COMP%] {\n  border: solid #ff7a00 1px;\n}\n\n.permissions-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  flex-basis: 25%;\n}\n.permissions-form[_ngcontent-%COMP%]   .mat-mdc-checkbox[_ngcontent-%COMP%] {\n  width: 25%;\n  margin: 0;\n  padding: 0;\n}\n\n.school-error-message[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding-left: 15px;\n}\n.school-error-message[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n\n.merchant-btn[_ngcontent-%COMP%] {\n  background-color: #272c50;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 10px;\n  outline: none;\n  border: none;\n  color: #ffffff;\n  font-size: 18px;\n  line-height: 24px;\n  padding: 6px 16px;\n  border-radius: 14px;\n  font-weight: 700;\n  cursor: pointer;\n}\n.merchant-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n\n.edit-btn[_ngcontent-%COMP%] {\n  background: transparent;\n  outline: none;\n  border: none;\n  cursor: pointer;\n  float: right;\n  padding-bottom: 8px;\n}\n\n.save-btn-wrapper[_ngcontent-%COMP%] {\n  padding-top: 15px;\n  padding-bottom: 100px;\n  position: relative;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.save-changes-btn[_ngcontent-%COMP%] {\n  background-color: #ff7a00;\n  gap: 10px;\n  outline: none;\n  border: none;\n  color: #ffffff;\n  font-size: 18px;\n  line-height: 24px;\n  padding: 6px 16px;\n  border-radius: 14px;\n  font-weight: 700;\n  cursor: pointer;\n}\n\n.save-changes-btn[_ngcontent-%COMP%]:disabled {\n  background-color: #e5e5e5;\n  color: #878787;\n  cursor: default;\n}\n\n.input-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n}\n\n.editBtnContainer[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  margin: 10px 0 20px 0;\n}\n\n.saveBtn[_ngcontent-%COMP%] {\n  border-radius: 10px;\n  background-color: #ff7a00;\n  border: 0;\n  font-size: 18px;\n  color: white;\n  cursor: pointer;\n  width: 70px;\n  height: 32px;\n}\n\n.saveBtn[_ngcontent-%COMP%]:disabled {\n  background-color: #e5e5e5;\n  color: #878787;\n  cursor: default;\n}\n\n.cancelBtn[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  border: 0;\n  border: 0;\n  font-size: 18px;\n  background-color: white;\n  height: 32px;\n  cursor: pointer;\n  width: 70px;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: rgb(255, 255, 255);\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.disableCoverGrey[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #f2f2f2;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.relative-section[_ngcontent-%COMP%] {\n  position: relative;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 10616:
/*!************************************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/merchant-user-permissions-table/merchant-user-permissions-table.component.ts ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerchantUserPermissionsTableComponent: () => (/* binding */ MerchantUserPermissionsTableComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);









function MerchantUserPermissionsTableComponent_table_7_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "User Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 22)(1, "div", 23)(2, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "h6");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const element_r18 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("", element_r18.FirstName, " ", element_r18.Lastname, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r18.Email);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "View menu editor");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_6_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "img", 27);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 24)(1, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_td_6_img_2_Template, 1, 0, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r19 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", element_r19.IsMenuEditorAvailable);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "View sales reports");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_9_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "img", 27);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 24)(1, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_td_9_img_2_Template, 1, 0, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", element_r21.IsSaleReportsAvailable);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "View event management");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_12_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "img", 27);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 24)(1, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_td_12_img_2_Template, 1, 0, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r23 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", element_r23.IsEventManagementAvailable);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Allow unprinted orders");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_15_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "img", 27);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 24)(1, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_td_15_img_2_Template, 1, 0, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r25 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", element_r25.IsOrdersNotPrintedReportsAvailable);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Email unprinted orders");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_18_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "img", 27);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 24)(1, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_td_18_img_2_Template, 1, 0, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r27 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", element_r27.NotifyOrdersNotPrinted);
  }
}
function MerchantUserPermissionsTableComponent_table_7_th_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "th", 21);
  }
}
function MerchantUserPermissionsTableComponent_table_7_td_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 28)(1, "a", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "img", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const element_r29 = ctx.$implicit;
    const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate2"]("routerLink", "./", ctx_r15.selectedMerchant.canteenId, "/editmerchantuser/", element_r29.UserId, "");
  }
}
function MerchantUserPermissionsTableComponent_table_7_tr_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 31);
  }
}
function MerchantUserPermissionsTableComponent_table_7_tr_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 32);
  }
}
function MerchantUserPermissionsTableComponent_table_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "table", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](1, 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, MerchantUserPermissionsTableComponent_table_7_th_2_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, MerchantUserPermissionsTableComponent_table_7_td_3_Template, 6, 3, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](4, 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, MerchantUserPermissionsTableComponent_table_7_th_5_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, MerchantUserPermissionsTableComponent_table_7_td_6_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](7, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantUserPermissionsTableComponent_table_7_th_8_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, MerchantUserPermissionsTableComponent_table_7_td_9_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](10, 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, MerchantUserPermissionsTableComponent_table_7_th_11_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, MerchantUserPermissionsTableComponent_table_7_td_12_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](13, 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, MerchantUserPermissionsTableComponent_table_7_th_14_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](15, MerchantUserPermissionsTableComponent_table_7_td_15_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](16, 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](17, MerchantUserPermissionsTableComponent_table_7_th_17_Template, 2, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](18, MerchantUserPermissionsTableComponent_table_7_td_18_Template, 3, 1, "td", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](19, 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, MerchantUserPermissionsTableComponent_table_7_th_20_Template, 1, 0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, MerchantUserPermissionsTableComponent_table_7_td_21_Template, 3, 2, "td", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, MerchantUserPermissionsTableComponent_table_7_tr_22_Template, 1, 0, "tr", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](23, MerchantUserPermissionsTableComponent_table_7_tr_23_Template, 1, 0, "tr", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("dataSource", ctx_r0.dataSource);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matHeaderRowDef", ctx_r0.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matRowDefColumns", ctx_r0.displayedColumns);
  }
}
function MerchantUserPermissionsTableComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "No linked users");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a0) {
  return {
    disableCoverWhite: a0
  };
};
const _columns = ['username', 'menuEditor', 'salesReport', 'viewEvent', 'allowUnprintedOrders', 'emailUnprintedOrders', 'edit'];
class MerchantUserPermissionsTableComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(merchantService, router) {
    super();
    this.merchantService = merchantService;
    this.router = router;
    this.stopLoad = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.displayedColumns = _columns;
    this.disableMode = false;
    this.dataSource = new _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTableDataSource();
  }
  ngOnInit() {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();
    this.loadData();
    this.trackSelectedMerchant = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
      this.loadData();
    });
    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }
  ngOnDestroy() {
    if (this.editSubscription) {
      this.editSubscription.unsubscribe();
    }
    if (this.trackSelectedMerchant) {
      this.trackSelectedMerchant.unsubscribe();
    }
  }
  RefreshTable(users) {
    this.dataSource.data = users;
  }
  loadData() {
    this.merchantService.GetAdminUsersLinkedToMerchant(this.selectedMerchant.canteenId).subscribe({
      next: res => {
        this.RefreshTable(res);
        this.stopLoad.emit();
      },
      error: error => {
        this.stopLoad.emit();
        this.handleErrorFromService(error);
      }
    });
  }
  isListEmpty() {
    return !this.dataSource.data || this.dataSource.data.length === 0;
  }
  addUserClick() {
    this.router.navigate([`./admin/merchants/${this.selectedMerchant.canteenId}/userSearch`]);
  }
  static {
    this.ɵfac = function MerchantUserPermissionsTableComponent_Factory(t) {
      return new (t || MerchantUserPermissionsTableComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MerchantUserPermissionsTableComponent,
      selectors: [["merchant-user-permissions-table"]],
      outputs: {
        stopLoad: "stopLoad"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 9,
      vars: 6,
      consts: [[1, "merchant-section"], [3, "ngClass"], [1, "details-header"], ["text", "Add User", 3, "buttonStyle", "onPress"], [1, "details-divider"], ["mat-table", "", "class", "table", 3, "dataSource", 4, "ngIf"], ["class", "emptyMessage", 4, "ngIf"], ["mat-table", "", 1, "table", 3, "dataSource"], ["matColumnDef", "username"], ["mat-header-cell", "", "class", "header", 4, "matHeaderCellDef"], ["mat-cell", "", "class", "noBorder", 4, "matCellDef"], ["matColumnDef", "menuEditor"], ["mat-cell", "", "class", "noBorder mediumColumn", 4, "matCellDef"], ["matColumnDef", "salesReport"], ["matColumnDef", "viewEvent"], ["matColumnDef", "allowUnprintedOrders"], ["matColumnDef", "emailUnprintedOrders"], ["matColumnDef", "edit"], ["mat-cell", "", "class", "noBorder smallColumn noPadding", 4, "matCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", "", 1, "header"], ["mat-cell", "", 1, "noBorder"], [2, "padding-top", "8px", "padding-bottom", "8px"], ["mat-cell", "", 1, "noBorder", "mediumColumn"], [1, "checkboxWrapper"], ["size", "24", "src", "assets/icons/black-tick.svg", "class", "checkBox", 4, "ngIf"], ["size", "24", "src", "assets/icons/black-tick.svg", 1, "checkBox"], ["mat-cell", "", 1, "noBorder", "smallColumn", "noPadding"], [2, "cursor", "pointer", "float", "right", 3, "routerLink"], ["src", "assets/icons/orange-pencil.svg", "alt", "edit symbol", 1, "editIcon"], ["mat-header-row", ""], ["mat-row", ""], [1, "emptyMessage"]],
      template: function MerchantUserPermissionsTableComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div", 2)(3, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Merchant users and Permissions");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "basic-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("onPress", function MerchantUserPermissionsTableComponent_Template_basic_button_onPress_5_listener() {
            return ctx.addUserClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "hr", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, MerchantUserPermissionsTableComponent_table_7_Template, 24, 3, "table", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, MerchantUserPermissionsTableComponent_div_8_Template, 2, 0, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c0, ctx.disableMode));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("buttonStyle", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.dataSource && !ctx.isListEmpty());
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isListEmpty());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__.BasicButtonComponent, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRow],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.top-margin[_ngcontent-%COMP%] {\n  margin: 10px 0 30px 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: rgb(255, 255, 255);\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.details-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n}\n.details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 28px;\n  font-weight: 700;\n  color: #1b1f39;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  margin: 0;\n}\n\n.merchant-section[_ngcontent-%COMP%] {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n\n.disableCoverWhite[_ngcontent-%COMP%] {\n  position: absolute;\n  background-color: #ffffff;\n  opacity: 0.5;\n  width: 100%;\n  height: 100%;\n  z-index: 100;\n}\n\n.table[_ngcontent-%COMP%] {\n  width: 100%;\n  overflow: hidden;\n  margin-bottom: 30px;\n  border-color: #ffffff;\n}\n.table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(odd) {\n  background-color: #f6f5f3;\n  border-color: #ffffff;\n}\n.table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(even) {\n  background-color: #faf9f8;\n}\n\n.mat-mdc-header-row[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #ffffff;\n  border-top: 1px solid #ffffff;\n  background-color: #ffffff;\n  color: #6b6c89;\n  font-size: 14px;\n  font-weight: 700;\n  padding-right: 8px;\n}\n\n.mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #ffffff;\n  border-top: 1px solid #ffffff;\n}\n\n.editIcon[_ngcontent-%COMP%] {\n  height: 16px;\n  width: 16px;\n}\n\n.mat-column-username[_ngcontent-%COMP%] {\n  width: 35%;\n  color: #1b1f39;\n}\n.mat-column-username[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  font-size: 14px;\n  line-height: 24px;\n  padding: 0;\n  margin: 0;\n}\n.mat-column-username[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\n  font-size: 12px;\n  line-height: 18px;\n  padding: 0;\n  margin: 0;\n}\n\n.mat-column-edit[_ngcontent-%COMP%] {\n  width: 25%;\n}\n\n.mat-column-allowUnprintedOrders[_ngcontent-%COMP%], .mat-column-menuEditor[_ngcontent-%COMP%], .mat-column-salesReport[_ngcontent-%COMP%], .mat-column-viewEvent[_ngcontent-%COMP%], .mat-column-emailUnprintedOrders[_ngcontent-%COMP%] {\n  width: 10%;\n}\n\n.merchant-btn[_ngcontent-%COMP%] {\n  background-color: #272c50;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 10px;\n  outline: none;\n  border: none;\n  color: #ffffff;\n  font-size: 18px;\n  line-height: 24px;\n  padding: 6px 16px;\n  border-radius: 14px;\n  font-weight: 700;\n  cursor: pointer;\n}\n.merchant-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n\n.emptyMessage[_ngcontent-%COMP%] {\n  padding: 10px;\n  font-size: 14px;\n  line-height: 24px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 4840:
/*!************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/school-header/school-header.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolHeaderComponent: () => (/* binding */ SchoolHeaderComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);


function SchoolHeaderComponent_p_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx_r0.description);
  }
}
class SchoolHeaderComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function SchoolHeaderComponent_Factory(t) {
      return new (t || SchoolHeaderComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: SchoolHeaderComponent,
      selectors: [["school-header"]],
      inputs: {
        title: "title",
        description: "description"
      },
      decls: 4,
      vars: 2,
      consts: [[1, "header"], [4, "ngIf"]],
      template: function SchoolHeaderComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](3, SchoolHeaderComponent_p_3_Template, 2, 1, "p", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.title);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.description);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.header[_ngcontent-%COMP%] {\n  color: #1b1f39;\n}\n.header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 28px;\n  margin: 0;\n  padding: 0;\n}\n.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 16px;\n  line-height: 24px;\n  color: #272c50;\n  margin-top: 8px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 124:
/*!******************************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/weekly-invoice-buttons/weekly-invoice-buttons.component.ts ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WeeklyInvoiceButtonsComponent: () => (/* binding */ WeeklyInvoiceButtonsComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);





function WeeklyInvoiceButtonsComponent_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "p", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Export as .csv");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 4)(4, "basic-button", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function WeeklyInvoiceButtonsComponent_ng_container_1_Template_basic_button_click_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r3.buttonPressed(ctx_r3.InvoiceType.Settlement));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 4)(6, "basic-button", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function WeeklyInvoiceButtonsComponent_ng_container_1_Template_basic_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r4);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r5.buttonPressed(ctx_r5.InvoiceType.Invoice));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
  }
}
function WeeklyInvoiceButtonsComponent_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4)(1, "basic-button", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function WeeklyInvoiceButtonsComponent_ng_template_2_Template_basic_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r6.buttonPressed(ctx_r6.InvoiceType.Generate));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("buttonStyle", 1)("fullWidth", true);
  }
}
class WeeklyInvoiceButtonsComponent {
  constructor() {
    this.getWeeklyReport = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.InvoiceType = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.InvoiceType;
  }
  ngOnChanges() {
    this.invoiceForSelectedWeek = this.getSelectedWeekInvoice();
  }
  getSelectedWeekInvoice() {
    let index = this.generatedInvoiceList?.findIndex(existingInvoice => existingInvoice.key === this.selectedWeek);
    return index >= 0 ? this.generatedInvoiceList[index] : null;
  }
  buttonPressed(invoiceType) {
    const data = {
      invoiceType,
      invoiceId: this.getInvoiceId()
    };
    this.getWeeklyReport.emit(data);
  }
  getInvoiceId() {
    return this.invoiceForSelectedWeek ? this.invoiceForSelectedWeek.invoiceId : null;
  }
  static {
    this.ɵfac = function WeeklyInvoiceButtonsComponent_Factory(t) {
      return new (t || WeeklyInvoiceButtonsComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: WeeklyInvoiceButtonsComponent,
      selectors: [["weekly-invoice-buttons"]],
      inputs: {
        generatedInvoiceList: "generatedInvoiceList",
        selectedWeek: "selectedWeek"
      },
      outputs: {
        getWeeklyReport: "getWeeklyReport"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵNgOnChangesFeature"]],
      decls: 4,
      vars: 2,
      consts: [[1, "row"], [4, "ngIf", "ngIfElse"], ["noInvoice", ""], [1, "col-12", "subtitle"], [1, "col-6"], ["text", "Settlement", 3, "buttonStyle", "fullWidth", "click"], ["text", "Invoice", 3, "buttonStyle", "fullWidth", "click"], ["text", "Generate report", 3, "buttonStyle", "fullWidth", "click"]],
      template: function WeeklyInvoiceButtonsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, WeeklyInvoiceButtonsComponent_ng_container_1_Template, 7, 4, "ng-container", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, WeeklyInvoiceButtonsComponent_ng_template_2_Template, 2, 2, "ng-template", null, 2, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.invoiceForSelectedWeek)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__.BasicButtonComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.subtitle[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #878787;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 66292:
/*!************************************************************************************!*\
  !*** ./src/app/admin-merchant/components/weekly-report/weekly-report.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WeeklyReportComponent: () => (/* binding */ WeeklyReportComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/schools-form/components */ 46282);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../schools-common/components/school-panel/school-panel.component */ 74153);
/* harmony import */ var _weekly_invoice_buttons_weekly_invoice_buttons_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../weekly-invoice-buttons/weekly-invoice-buttons.component */ 124);













function WeeklyReportComponent_form_3_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div")(1, "weekly-invoice-buttons", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("getWeeklyReport", function WeeklyReportComponent_form_3_div_5_Template_weekly_invoice_buttons_getWeeklyReport_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresetView"](ctx_r2.getWeeklyReport($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("selectedWeek", ctx_r1.selectedWeek.value)("generatedInvoiceList", ctx_r1.generatedInvoiceData);
  }
}
function WeeklyReportComponent_form_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "form", 2)(1, "div", 3)(2, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](3, "input-select-list", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](5, WeeklyReportComponent_form_3_div_5_Template, 2, 2, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("formGroup", ctx_r0.formGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("values", ctx_r0.selectWeekValues);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx_r0.selectedWeek);
  }
}
class WeeklyReportComponent extends src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_1__.BaseFormComponent {
  constructor(merchantService, spinnerService) {
    super();
    this.merchantService = merchantService;
    this.spinnerService = spinnerService;
    this.INVOICE_WEEK_TOTAL = 100;
    this.selectWeekValues = [];
  }
  ngOnChanges() {
    if (this.generatedInvoiceList) {
      this.getInputListData(this.generatedInvoiceList);
    }
  }
  getInputListData(generatedInvoiceList) {
    this.selectWeekValues = this.getSelectWeekValues();
    this.generatedInvoiceData = this.mapGeneratedInvoiceToSelectedWeekDate(generatedInvoiceList);
    this.createFrom();
  }
  mapGeneratedInvoiceToSelectedWeekDate(generatedInvoiceList) {
    return generatedInvoiceList.map(invoiceData => ({
      invoiceId: invoiceData.invoiceId,
      key: this.getWeekYearNumber(invoiceData.toDate)
    }));
  }
  getWeekYearNumber(date) {
    return moment__WEBPACK_IMPORTED_MODULE_0___default()(date).format('w-YYYY');
  }
  createFrom() {
    const weekToDisplay = this.getWeekToDisplay();
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormGroup({
      week: new _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormControl(weekToDisplay)
    });
  }
  getWeekToDisplay() {
    const currentWeek = this.selectWeekValues[0].key;
    const selectedWeek = this.selectedWeek?.value;
    return selectedWeek ? selectedWeek : currentWeek;
  }
  getSelectWeekValues() {
    let endCurrentWeek = moment__WEBPACK_IMPORTED_MODULE_0___default()().endOf('week').add(1, 'week').format(src_app_utility__WEBPACK_IMPORTED_MODULE_3__.UNIVERSAL_DATE_FORMAT);
    const weekValues = [];
    for (let index = 0; index < this.INVOICE_WEEK_TOTAL; index++) {
      endCurrentWeek = moment__WEBPACK_IMPORTED_MODULE_0___default()(endCurrentWeek).subtract(1, 'week').format(src_app_utility__WEBPACK_IMPORTED_MODULE_3__.UNIVERSAL_DATE_FORMAT);
      weekValues.push({
        key: this.getWeekYearNumber(endCurrentWeek),
        value: this.getWeekText(endCurrentWeek)
      });
    }
    return weekValues;
  }
  getWeeklyReport(invoiceToExport) {
    const {
      invoiceType
    } = invoiceToExport;
    if (invoiceType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.InvoiceType.Generate) {
      this.generateInvoice();
      return;
    }
    this._downloadWeeklyReport(invoiceToExport);
  }
  getExportRequest(invoiceToExport) {
    const {
      invoiceId,
      invoiceType
    } = invoiceToExport;
    return {
      ExportType: src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.InvoiceTypeEnum[invoiceType],
      InvoiceId: invoiceId
    };
  }
  getGenerateInvoiceRequest() {
    const selectedDateValues = this.getSelectedWeekAndYear();
    return {
      exportType: 0,
      startDate: this.getWeekDate(selectedDateValues, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.WeekDateType.start),
      endDate: this.getWeekDate(selectedDateValues, src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.WeekDateType.end)
    };
  }
  getWeekDate(selectedDateValues, weekType) {
    const year = selectedDateValues.selectedYear.toString(); // year value needs to be string
    const week = selectedDateValues.selectedWeek; // week value needs to be int
    const momentValue = moment__WEBPACK_IMPORTED_MODULE_0___default()(year).weeks(week);
    const momentDate = weekType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.WeekDateType.start ? momentValue.startOf('week') : momentValue.endOf('week');
    return moment__WEBPACK_IMPORTED_MODULE_0___default()(momentDate).format(src_app_utility__WEBPACK_IMPORTED_MODULE_3__.UNIVERSAL_DATE_FORMAT);
  }
  getWeekText(date) {
    const {
      startDate,
      endDate
    } = this.getWeekStartAndEndDate(date);
    return `${startDate} to ${endDate}`;
  }
  getWeekStartAndEndDate(date) {
    const endDate = lodash__WEBPACK_IMPORTED_MODULE_4__.cloneDeep(date);
    const startDate = lodash__WEBPACK_IMPORTED_MODULE_4__.cloneDeep(date);
    const textStart = moment__WEBPACK_IMPORTED_MODULE_0___default()(startDate).subtract(1, 'week').add(1, 'day').format(src_app_utility__WEBPACK_IMPORTED_MODULE_3__.UNIVERSAL_DATE_FORMAT);
    const textEnd = moment__WEBPACK_IMPORTED_MODULE_0___default()(endDate).format(src_app_utility__WEBPACK_IMPORTED_MODULE_3__.UNIVERSAL_DATE_FORMAT);
    return {
      startDate: textStart,
      endDate: textEnd
    };
  }
  getSelectedWeekAndYear() {
    const weekYearKeyValueData = this.selectedWeek.value;
    const weekYearArray = weekYearKeyValueData.split('-');
    const selectedWeek = +weekYearArray[0];
    const selectedYear = +weekYearArray[1];
    return {
      selectedWeek,
      selectedYear
    };
  }
  generateInvoice() {
    const request = this.getGenerateInvoiceRequest();
    this.spinnerService.animatedStart();
    this.merchantService.GenerateInvoice(request).subscribe({
      next: res => {
        this.refreshGeneratedInvoiceList();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      }
    });
  }
  refreshGeneratedInvoiceList() {
    this.merchantService.GetGeneratedInvoiceList().subscribe({
      next: res => {
        this.getInputListData(res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      }
    });
  }
  _downloadWeeklyReport(invoiceToExport) {
    let filename = invoiceToExport.invoiceType + '.csv';
    const request = this.getExportRequest(invoiceToExport);
    this.spinnerService.animatedStart();
    this.merchantService.ExportInvoice(request).subscribe({
      next: res => {
        (0,src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.DownloadCSV)(filename, res);
        this.spinnerService.animatedStop();
      },
      error: error => {
        this.spinnerService.animatedStop();
        this.ErrorModal('Something went wrong', error);
      }
    });
  }
  get selectedWeek() {
    return this.formGroup?.get('week');
  }
  static {
    this.ɵfac = function WeeklyReportComponent_Factory(t) {
      return new (t || WeeklyReportComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__.MerchantService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__.SpinnerService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineComponent"]({
      type: WeeklyReportComponent,
      selectors: [["weekly-report"]],
      inputs: {
        generatedInvoiceList: "generatedInvoiceList"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵNgOnChangesFeature"]],
      decls: 4,
      vars: 1,
      consts: [[1, "mb-0", "panelTitle"], ["class", "pb-4", 3, "formGroup", 4, "ngIf"], [1, "pb-4", 3, "formGroup"], [1, "row"], [1, "col-12", "pt-4"], ["formControlName", "week", "placeholder", "Select week", 3, "values"], [1, "col-12"], [4, "ngIf"], [3, "selectedWeek", "generatedInvoiceList", "getWeeklyReport"]],
      template: function WeeklyReportComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "school-panel")(1, "p", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](2, "Export weekly settlement information for all active schools");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtemplate"](3, WeeklyReportComponent_form_3_Template, 6, 3, "form", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵproperty"]("ngIf", ctx.formGroup && (ctx.selectWeekValues == null ? null : ctx.selectWeekValues.length));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_11__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_10__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatusGroup, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_6__.InputSelectListComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormControlName, _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_7__.SchoolPanelComponent, _weekly_invoice_buttons_weekly_invoice_buttons_component__WEBPACK_IMPORTED_MODULE_8__.WeeklyInvoiceButtonsComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 6279:
/*!*************************************************************!*\
  !*** ./src/app/admin-merchant/models/base-merchant-form.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseMerchantFormComponent: () => (/* binding */ BaseMerchantFormComponent)
/* harmony export */ });
/* harmony import */ var src_app_schools_form_components_base_form_base_form_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-form/components/base-form/base-form.component */ 74260);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
// components


// models

class BaseMerchantFormComponent extends src_app_schools_form_components_base_form_base_form_component__WEBPACK_IMPORTED_MODULE_0__.BaseFormComponent {
  constructor(dialog, merchantService) {
    super();
    this.dialog = dialog;
    this.merchantService = merchantService;
    this.editDetailsMode = false;
    this.disableMode = false;
    // const
    this.invalidValueError = 'Invalid value entered';
  }
  /**
   * Setup the common subscriptions
   */
  _baseOnInit() {
    this.selectedMerchant = this.merchantService.getSelectedMerchant();
    this.selectedMerchantSubscription = this.merchantService.selectedMerchantUpdatedEvent$.subscribe(res => {
      this.selectedMerchant = res;
    });
    this.editSubscription = this.merchantService.editMode$.subscribe(status => {
      this.disableMode = status;
    });
  }
  /**
   * Unsubscribe from the common subscriptions
   */
  _baseOnDestroy() {
    if (this.editSubscription) {
      this.merchantService.setDisableMode(false);
      this.editSubscription.unsubscribe();
    }
    if (this.selectedMerchantSubscription) {
      this.selectedMerchantSubscription.unsubscribe();
    }
  }
  /**
   * Check form validation
   * @returns
   */
  isFormDisabled() {
    return !this.formGroup.valid;
  }
  /**
   * Common popup to display the Success message to the user
   */
  SuccessPopUp() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.ResultDialogData();
    data.TitleLine1 = 'Success!';
    data.TextLine1 = 'Changes have been saved successfully.';
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      //close edit
      this.editDetailsMode = false;
      this.merchantService.setDisableMode(false);
    });
  }
  /**
   * Common popup to display error message to the user
   * @returns the user choice (retry or not) to manage the retry in the component extending this component
   */
  _somethingWentWrongPopup() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.ResultDialogData();
    data.ShowErrorSymbol = true;
    data.TitleLine1 = 'Oops! Something went wrong';
    data.TextLine1 = 'We were unable to save your changes.';
    data.TextLine2 = 'Please try again.';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Try again';
    return this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
  }
  /**
   * Common popup to display the Cancel message to the user
   */
  cancelEditPopup() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.ResultDialogData();
    data.TitleLine1 = 'Are you sure?';
    data.TextLine1 = 'Are you sure you want to cancel your changes?';
    data.TextLine2 = 'They will not be saved.';
    data.CancelButton = 'Yes, Cancel';
    data.ConfirmButton = 'No, return';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(cancelResult => {
      if (cancelResult) {
        //close edit
        this.editDetailsMode = false;
        this.merchantService.setDisableMode(false);
      }
    });
  }
  /**
   * Popup to display reminder message to users
   */
  ReminderPopUp() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.ResultDialogData();
    data.TitleLine1 = 'Reminder';
    data.TextLine1 = 'If you make any changes to a merchant please alert finance immediately.';
    data.ConfirmButton = 'Okay';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__.DialogResultComponent, {
      width: '400px',
      disableClose: true,
      data: data
    });
  }
}

/***/ }),

/***/ 35995:
/*!***************************************************!*\
  !*** ./src/app/sharedModels/fee/FeeCalculator.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminFeeResponse: () => (/* binding */ AdminFeeResponse),
/* harmony export */   FeeCalculator: () => (/* binding */ FeeCalculator),
/* harmony export */   FeeCalculatorInitialValue: () => (/* binding */ FeeCalculatorInitialValue),
/* harmony export */   FeeCalculatorRequest: () => (/* binding */ FeeCalculatorRequest),
/* harmony export */   FeeChangedEvent: () => (/* binding */ FeeChangedEvent),
/* harmony export */   SchoolFeeCalculator: () => (/* binding */ SchoolFeeCalculator)
/* harmony export */ });
class FeeCalculator {}
class SchoolFeeCalculator {}
class FeeCalculatorRequest {}
class AdminFeeResponse {}
class FeeChangedEvent {}
class FeeCalculatorInitialValue {}

/***/ }),

/***/ 64432:
/*!*************************************************************!*\
  !*** ./src/app/sharedServices/fee/feeCalculator.service.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeeCalculatorService: () => (/* binding */ FeeCalculatorService)
/* harmony export */ });
/* harmony import */ var _base_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base.service */ 58796);
/* harmony import */ var src_app_sharedModels_fee_FeeCalculator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels/fee/FeeCalculator */ 35995);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 61873);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 46443);





class FeeCalculatorService extends _base_service__WEBPACK_IMPORTED_MODULE_0__.BaseService {
  constructor(http) {
    super('api/feeCalculator');
    this.http = http;
  }
  createFeeCalculatorRequest(feeCalculatorId, schoolId, merchantId) {
    let request = new src_app_sharedModels_fee_FeeCalculator__WEBPACK_IMPORTED_MODULE_1__.FeeCalculatorRequest();
    request.feeCalculatorId = feeCalculatorId;
    request.schoolIds = [];
    request.schoolIds.push(schoolId);
    request.merchantId = merchantId;
    return request;
  }
  getAllFeeCalculators() {
    this.SetAction('GetAll');
    return this.http.get(this.serviceUrl);
  }
  getFeeCalculatorByMerchantAndSchool(merchantId, schoolId) {
    this.SetAction('GetFeeCalculatorByMerchantAndSchool');
    return this.http.get(`${this.serviceUrl}/${merchantId}/${schoolId}`);
  }
  AddSchoolToFeeCalculator(feeCalculatorId, schoolId, merchantId) {
    let request = this.createFeeCalculatorRequest(feeCalculatorId, schoolId, merchantId);
    this.SetAction('AddSchoolsToFeeCalculator');
    return this.http.post(this.serviceUrl, request);
  }
  UpdateSchoolFromFeeCalculator(feeCalculatorId, schoolId, merchantId) {
    let request = this.createFeeCalculatorRequest(feeCalculatorId, schoolId, merchantId);
    this.SetAction('UpdateSchoolsToFeeCalculator');
    return this.http.post(this.serviceUrl, request);
  }
  RemoveSchoolFromFeeCalculator(feeCalculatorId, schoolId, merchantId) {
    let request = this.createFeeCalculatorRequest(feeCalculatorId, schoolId, merchantId);
    this.SetAction('RemoveSchoolsToFeeCalculator');
    const options = {
      headers: {},
      body: request
    };
    return this.http.delete(this.serviceUrl, options);
  }
  requestDataForAdminFeeForm(merchantId, schoolId) {
    let response1 = this.getAllFeeCalculators();
    let response2 = this.getFeeCalculatorByMerchantAndSchool(merchantId, schoolId);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.forkJoin)({
      allFeeCalculators: response1,
      schoolFeeCalculators: response2
    });
  }
  static {
    this.ɵfac = function FeeCalculatorService_Factory(t) {
      return new (t || FeeCalculatorService)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: FeeCalculatorService,
      factory: FeeCalculatorService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_admin-merchant_admin-merchant_module_ts.js.map