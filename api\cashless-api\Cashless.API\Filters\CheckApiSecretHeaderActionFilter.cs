using Schools.BLL.Extensions;
using Schools.BLL.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Filters;

/// <summary>
/// MVC action filter attribute to check the presence of a valid 
/// api secret header on given controller actions
/// </summary>
public class CheckApiSecretHeaderActionFilter : IActionFilter
{
    // Value to expect in the request headers
    public static string CashlessApiSecret;

    private readonly ITelemetryService telemetryService;
    private readonly ILogger<CheckApiSecretHeaderActionFilter> logger;

    public CheckApiSecretHeaderActionFilter(ITelemetryService telemetryService, ILogger<CheckApiSecretHeaderActionFilter> logger)
    {
        this.telemetryService = telemetryService;
        this.logger = logger;
    }

    /// <summary>
    /// Check the cashless-api-secret on a given controller action
    /// </summary>
    public void OnActionExecuting(ActionExecutingContext context)
    {
        var header = HttpRequestExtension.CashlessApiSecretHeader;
        var apiSecret = context.HttpContext.Request.Headers[header];

        if (string.IsNullOrEmpty(apiSecret))
        {
            logger.LogError("Unable to find '{Header}' header in request", header);
            telemetryService.TrackTrace($"Unable to find '{header}' header in request");

            context.Result = new UnauthorizedResult();

            return;
        }

        if (apiSecret != CashlessApiSecret)
        {
            logger.LogError("Value '{Value}' for '{Header}' header does not match API secret in app configuration", apiSecret, header);
            telemetryService.TrackTrace($"Value '{apiSecret}' for '{header}' header does not match API secret in app configuration");

            context.Result = new UnauthorizedResult();

            return;
        }

        logger.LogDebug("Found valid '{Header}' header in request", header);
        telemetryService.TrackTrace($"Found valid '{header}' header in request");
    }

    public void OnActionExecuted(ActionExecutedContext context) { }
}