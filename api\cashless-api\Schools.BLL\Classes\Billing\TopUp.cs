using System.Collections.Generic;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class TopUp
    {
        [JsonProperty(PropertyName = "PaymentId")]
        public int PaymentId { get; set; }

        [JsonProperty(PropertyName = "TopupAmount")]
        public decimal TopupAmount { get; set; }

        [JsonProperty(PropertyName = "Date")]
        public string Date { get; set; }

        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }
    }

    public class AccountBillingHistoryLine
    {
        [JsonProperty(PropertyName = "PaymentId")]
        public int PaymentId { get; set; }

        [JsonProperty(PropertyName = "Date")]
        public string Date { get; set; }

        [JsonProperty(PropertyName = "TopupAmount")]
        public decimal TopupAmount { get; set; }

        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }
    }

    public class AccountBillingHistory
    {
        [JsonProperty(PropertyName = "Date")]
        public string Date { get; set; }

        [JsonProperty(PropertyName = "HistoryLines")]
        public List<AccountBillingHistoryLine> HistoryLines { get; set; }
    }
}
