using Schools.BLL.Classes.Notices;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class NoticeMapper
{
    public static Notice MapEntityToDto(NoticeEntity entity)
    {
        if (entity == null) return null;

        return new Notice
        {
            DateCreated = entity.DateCreated,
            DateModified = entity.DateModified,
            Description = entity.Description,
            EndDate = entity.EndDate,
            IsActive = entity.IsActive,
            MerchantId = entity.MerchantId,
            NoticeId = entity.NoticeId,
            NoticeType = (NoticeTypes)entity.NoticeType,
            SchoolId = entity.SchoolId,
            StartDate = entity.StartDate,
            Status = entity.Status,
            Title = entity.Title,
            ValidationDescription = entity.ValidationDescription
        };
    }
}
