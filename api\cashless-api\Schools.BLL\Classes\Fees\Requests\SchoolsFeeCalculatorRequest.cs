using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Schools.BLL.Classes.Fees.Requests;

public class SchoolsFeeCalculatorRequest
{
    public SchoolsFeeCalculatorRequest()
    {
        SchoolIds = new List<long>();
    }

    [Required]
    public long FeeCalculatorId { get; set; }

    [Required]
    public IEnumerable<long> SchoolIds { get; set; }

    [Required]
    public long MerchantId { get; set; }
}
