﻿using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StudentController : ControllerBase
    {
        private readonly IUserService userService;
        private readonly IStudentValidator studentValidator;

        public StudentController(IUserService userService, IStudentValidator studentValidator)
        {
            this.userService = userService;
            this.studentValidator = studentValidator;
        }

        [Route("GetStudentList/{parentId}")]
        [Route("GetStudentListMobile/{parentId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Parent)]
        public async Task<IActionResult> GetStudentList(int parentId)
        {
            await studentValidator.ValidateAccessToParent(parentId);
            ListUsersResponse response = new ListUsersResponse();
            response.Users = await this.userService.GetStudentsByParentId(parentId);

            return new OkObjectResult(response);
        }

        [Route("ArchiveStudent/{studentId}")]
        [Route("ArchiveStudentMobile/{studentId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Parent)]
        public async Task<IActionResult> ArchiveStudent(int studentId)
        {
            await this.studentValidator.ValidateAccessToStudent(studentId);
            // TODO - Remove the GET on clients
            await this.userService.ArchiveUser(studentId, "");

            return new OkResult();
        }
    }
}
