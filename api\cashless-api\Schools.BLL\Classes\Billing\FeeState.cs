﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class FeeState
    {
        [JsonProperty(PropertyName = "FeeStateId")]
        public int? FeeStateId { get; set; }

        [JsonProperty(PropertyName = "TermId")]
        public int TermId { get; set; }

        [JsonProperty(PropertyName = "TermName")]
        public string TermName { get; set; }

        [JsonProperty(PropertyName = "StudentId")]
        public int StudentId { get; set; }

        [JsonProperty(PropertyName = "NumberOrders")]
        public int? NumberOrders { get; set; }

        [JsonProperty(PropertyName = "Status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "OrderId")]
        public int? OrderId { get; set; }

        [JsonProperty(PropertyName = "PricingModel")]
        public string PricingModel { get; set; }

        [JsonProperty(PropertyName = "PricingAmount")]
        public decimal? PricingAmount { get; set; }

        [JsonProperty(PropertyName = "PricingCap")]
        public decimal? PricingCap { get; set; }

        [JsonProperty(PropertyName = "TotalOrders")]
        public decimal? TotalOrders { get; set; }
    }
}
