"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_authentication_authentication_module_ts"],{

/***/ 13512:
/*!*****************************************************************!*\
  !*** ./src/app/authentication/authentication-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthenticationRoutingModule: () => (/* binding */ AuthenticationRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 63184);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);




const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AuthenticationComponent,
  children: [{
    path: '',
    pathMatch: 'full',
    redirectTo: 'login'
  }, {
    path: 'login',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.LoginComponent,
    data: {
      login: true
    }
  }, {
    path: 'register',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.LoginComponent,
    data: {
      login: false
    }
  }, {
    path: 'reset',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.ResetComponent
  }]
}];
class AuthenticationRoutingModule {
  static {
    this.ɵfac = function AuthenticationRoutingModule_Factory(t) {
      return new (t || AuthenticationRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: AuthenticationRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](AuthenticationRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 74329:
/*!*********************************************************!*\
  !*** ./src/app/authentication/authentication.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthenticationModule: () => (/* binding */ AuthenticationModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _authentication_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./authentication-routing.module */ 13512);
/* harmony import */ var _components_login_login_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/login/login.component */ 54210);
/* harmony import */ var _components_authentication_authentication_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/authentication/authentication.component */ 7964);
/* harmony import */ var _components_reset_reset_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/reset/reset.component */ 89518);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);


// google material






//import { CashlessCoreModule } from 'cashless-core';



class AuthenticationModule {
  static {
    this.ɵfac = function AuthenticationModule_Factory(t) {
      return new (t || AuthenticationModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineNgModule"]({
      type: AuthenticationModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.CommonModule, _authentication_routing_module__WEBPACK_IMPORTED_MODULE_0__.AuthenticationRoutingModule,
      //CashlessCoreModule,
      _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.ReactiveFormsModule,
      // material
      _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatFormFieldModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_8__.MatCheckboxModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_10__.MatButtonModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsetNgModuleScope"](AuthenticationModule, {
    declarations: [_components_login_login_component__WEBPACK_IMPORTED_MODULE_1__.LoginComponent, _components_authentication_authentication_component__WEBPACK_IMPORTED_MODULE_2__.AuthenticationComponent, _components_reset_reset_component__WEBPACK_IMPORTED_MODULE_3__.ResetComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.CommonModule, _authentication_routing_module__WEBPACK_IMPORTED_MODULE_0__.AuthenticationRoutingModule,
    //CashlessCoreModule,
    _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.ReactiveFormsModule,
    // material
    _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatFormFieldModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_8__.MatCheckboxModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_9__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_10__.MatButtonModule]
  });
})();

/***/ }),

/***/ 7964:
/*!**************************************************************************************!*\
  !*** ./src/app/authentication/components/authentication/authentication.component.ts ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthenticationComponent: () => (/* binding */ AuthenticationComponent)
/* harmony export */ });
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);



class AuthenticationComponent {
  constructor() {}
  ngOnInit() {
    this.appVersion = src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.AppVersion;
  }
  static {
    this.ɵfac = function AuthenticationComponent_Factory(t) {
      return new (t || AuthenticationComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: AuthenticationComponent,
      selectors: [["app-authentication"]],
      decls: 12,
      vars: 1,
      consts: [[1, "container"], [1, "row", "justify-content-center"], [1, "col-12", "col-sm-10", "col-md-8", "col-lg-6", "col-xl-4"], [1, "containerLogo"], ["src", "./assets/icons/spriggyschoolicon.png", "alt", "spriggy school logo", 1, "logo"], [1, "col-12", "col-sm-4", "col-md-4", "col-lg-2", "versionRow"], [1, "appVersion"]],
      template: function AuthenticationComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](4, "img", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 1)(6, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](7, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "div", 1)(9, "div", 5)(10, "span", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("Version: ", ctx.appVersion, "");
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterOutlet],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.containerLogo[_ngcontent-%COMP%] {\n  padding-bottom: 24px;\n  text-align: center;\n  margin-top: 20%;\n}\n.containerLogo[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\n  margin: 0px;\n  padding: 0px;\n  max-width: 70%;\n}\n\n.versionRow[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.versionRow[_ngcontent-%COMP%]   .appVersion[_ngcontent-%COMP%] {\n  font-size: 12px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 63184:
/*!****************************************************!*\
  !*** ./src/app/authentication/components/index.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthenticationComponent: () => (/* reexport safe */ _authentication_authentication_component__WEBPACK_IMPORTED_MODULE_1__.AuthenticationComponent),
/* harmony export */   LoginComponent: () => (/* reexport safe */ _login_login_component__WEBPACK_IMPORTED_MODULE_0__.LoginComponent),
/* harmony export */   ResetComponent: () => (/* reexport safe */ _reset_reset_component__WEBPACK_IMPORTED_MODULE_2__.ResetComponent)
/* harmony export */ });
/* harmony import */ var _login_login_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login/login.component */ 54210);
/* harmony import */ var _authentication_authentication_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authentication/authentication.component */ 7964);
/* harmony import */ var _reset_reset_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reset/reset.component */ 89518);




/***/ }),

/***/ 54210:
/*!********************************************************************!*\
  !*** ./src/app/authentication/components/login/login.component.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginComponent: () => (/* binding */ LoginComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 72354);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/animations */ 47172);
/* harmony import */ var ng_animate__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ng-animate */ 77975);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_fire_compat_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/fire/compat/auth */ 8245);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var src_app_sharedServices_authentication_auth_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/sharedServices/authentication/auth.service */ 55372);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/input */ 95541);


















function LoginComponent_mat_form_field_3_mat_error_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r11.getErrorMessageFirstname());
  }
}
function LoginComponent_mat_form_field_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-form-field", 4)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "First name");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "input", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, LoginComponent_mat_form_field_3_mat_error_4_Template, 2, 1, "mat-error", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.firstname.invalid);
  }
}
function LoginComponent_mat_form_field_4_mat_error_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r12.getErrorMessageLastname());
  }
}
function LoginComponent_mat_form_field_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-form-field", 4)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "Last name");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "input", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, LoginComponent_mat_form_field_4_mat_error_4_Template, 2, 1, "mat-error", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r1.lastname.invalid);
  }
}
function LoginComponent_mat_form_field_5_mat_error_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r13.getErrorMessageMobile());
  }
}
function LoginComponent_mat_form_field_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-form-field", 4)(1, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "Mobile number");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "input", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("keyup", function LoginComponent_mat_form_field_5_Template_input_keyup_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r15);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r14.formatMobileInput());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, LoginComponent_mat_form_field_5_mat_error_4_Template, 2, 1, "mat-error", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r2.mobile.invalid);
  }
}
function LoginComponent_mat_error_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r3.getErrorMessageEmail());
  }
}
function LoginComponent_mat_error_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r4.getErrorMessagePassword());
  }
}
function LoginComponent_div_16_mat_error_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r16.getErrorMessageTerms());
  }
}
function LoginComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 18)(1, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "mat-checkbox", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " I agree to the Spriggy Schools ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "a", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, "Terms & Conditions");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, ", ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "a", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](9, "Privacy Policy");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](10, " and ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "a", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](12, "Financial Services Guide");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](13, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](14, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](15, "mat-checkbox", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](17, " I agree that I am 18 years of age or older, or that I am between 15 and 18 years of age and my parent or legal guardian authorises me entering into the ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "a", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](19, "Terms & Conditions");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](20, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](21, LoginComponent_div_16_mat_error_21_Template, 2, 1, "mat-error", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("href", ctx_r5.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("href", ctx_r5.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("href", ctx_r5.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("href", ctx_r5.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r5.termsConditions.invalid || ctx_r5.ageConditions.invalid);
  }
}
function LoginComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 23)(1, "a", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, "Forgot password?");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
}
function LoginComponent_mat_error_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r7.errorMessage, " ");
  }
}
function LoginComponent_span_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Already have an account? ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "p")(3, "a", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, "Log in");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function LoginComponent_span_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "No account? Register today! ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function LoginComponent_button_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Register");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("@bounce", ctx_r10.bounce);
  }
}
class LoginComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.BaseComponent {
  constructor(afAuth, route, router, spinnerService, userService, authService, ngZone, phoneNumberService) {
    super();
    this.afAuth = afAuth;
    this.route = route;
    this.router = router;
    this.spinnerService = spinnerService;
    this.userService = userService;
    this.authService = authService;
    this.ngZone = ngZone;
    this.phoneNumberService = phoneNumberService;
    this.isOffline = false;
    this.termsLink = src_constants__WEBPACK_IMPORTED_MODULE_1__.SPRIGGY_SCHOOLS_TERMS_LINK;
  }
  ngOnInit() {
    this.route.data.subscribe(data => {
      this.loginMode = data.login;
      this._createForm();
    });
    this.userService.IdentifyUser();
  }
  ngOnDestroy() {
    if (this._offlineSubscription) {
      this._offlineSubscription.unsubscribe();
    }
  }
  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get email() {
    return this.form.get('email');
  }
  get password() {
    return this.form.get('password');
  }
  get firstname() {
    return this.form.get('firstname');
  }
  get lastname() {
    return this.form.get('lastname');
  }
  get mobile() {
    return this.form.get('mobile');
  }
  get termsConditions() {
    return this.form.get('termsConditions');
  }
  get ageConditions() {
    return this.form.get('ageConditions');
  }
  getErrorMessageEmail() {
    return this.form.get('email').hasError('required') ? 'You must enter a value' : this.form.get('email').hasError('email') ? 'Not a valid email' : '';
  }
  getErrorMessagePassword() {
    if (this.password.hasError('required')) {
      return 'You must enter a value';
    } else if (this.password.hasError('minlength')) {
      return 'Must be at least 14 characters';
    } else {
      return '';
    }
  }
  getErrorMessageFirstname() {
    return this.form.get('firstname').hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageLastname() {
    return this.form.get('lastname').hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageMobile() {
    if (this.form.get('mobile').hasError('required')) {
      return 'You must enter a value';
    }
    return 'You must enter a valid mobile number';
  }
  getErrorMessageTerms() {
    return this.form.get('termsConditions').hasError('required') || this.form.get('ageConditions').hasError('required') ? 'You must agree to the Terms to continue' : '';
  }
  ////////////////////////////////////////
  // Log in / Sign up
  ////////////////////////////////////////
  onSubmit() {
    if (this.form.valid) {
      this.clearApiError();
      // Start spinner
      this.spinnerService.start();
      if (this.loginMode) {
        this.signIn();
      } else {
        this.signUp();
      }
    }
  }
  // Call Firebase to sign in
  signIn() {
    this.afAuth.signInWithEmailAndPassword(this.email.value, this.password.value).then(fuser => {
      this.signInSucceeded(fuser);
    }).catch(error => {
      this.signInFailed(error);
    });
  }
  // Register a new User
  signUp() {
    // Create all accounts as parents for the time being
    let request = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.CreateUserRequest();
    request.Email = this.email.value;
    request.Password = this.password.value;
    request.Role = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.Roles.Parent;
    request.FirstName = this.firstname.value;
    request.Lastname = this.lastname.value;
    request.Mobile = this.phoneNumberService.serverMobileNumber(this.mobile.value);
    this.userService.RegisterUser(request).subscribe({
      next: response => {
        this.signIn();
      },
      error: error => {
        this.signUpFailed(error);
      }
    });
  }
  // Sign in failed
  signInFailed(error) {
    if (error.code == 'auth/invalid-password' || error.code == 'auth/wrong-password' || error.code == 'auth/invalid-credential' || error.code == 'auth/user-not-found') {
      this.errorMessage = 'Sorry this email and password combination was incorrect';
    } else {
      this.errorMessage = 'Something has gone wrong...';
    }
    this.spinnerService.stop();
  }
  // Sign up failed
  // - 4xx: Failed validations or authorisations
  // - 5xx: API errors
  signUpFailed(error) {
    this.spinnerService.stop();
    this.errorMessage = this.getApiError(error);
  }
  // Sign in an existing user
  signInSucceeded(fuser) {
    // console.log("signInSucceeded(): Sign in user success - " + JSON.stringify(fuser));
    this._ClearStorageOnNewVersion();
    this.userFire = null;
    this.userFire = fuser.user;
    this.userFire.getIdToken().then(token => {
      this.authService.SetToken(token);
      this._GetUser();
    });
    // console.log("signInSucceeded(): Clean up after sign in");
    // Will we get to these statements once a redirect is issued?
    this.spinnerService.stop();
    this.clearApiError();
  }
  _GetUser() {
    this.userService.GetUserByFireBaseId(this.userFire.uid).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.timeout)(5000)).subscribe({
      next: userResponse => {
        if (userResponse) {
          this.userService.SetUserConnected(userResponse);
          this._RedirectUser(userResponse);
        }
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  _ClearStorageOnNewVersion() {
    let savedVersion = localStorage.getItem('appVersion');
    if (savedVersion != src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.AppVersion) {
      localStorage.clear();
      localStorage.setItem('appVersion', src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.AppVersion);
    }
  }
  _RedirectUser(user) {
    if (user.Role == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.Roles.Admin) {
      this.ngZone.run(() => this.router.navigate(['/admin']));
    } else if (user.Role == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.Roles.Canteen) {
      this.ngZone.run(() => this.router.navigate(['/canteen']));
    } else {
      this.ngZone.run(() => this.router.navigate(['/family']));
    }
  }
  _createForm() {
    if (this.loginMode) {
      this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
        email: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.email]),
        password: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required])
      });
    } else {
      this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
        email: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.email]),
        password: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.minLength(14)]),
        firstname: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required]),
        lastname: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required]),
        mobile: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.minLength(12)]),
        termsConditions: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(true, [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.requiredTrue]),
        ageConditions: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(true, [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.requiredTrue])
      });
    }
  }
  ////////////////////////////////////////
  // Get Text
  ////////////////////////////////////////
  GetTextSubmit() {
    if (this.loginMode) {
      return 'Sign In';
    } else {
      return 'Sign Up';
    }
  }
  formatMobileInput() {
    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);
    this.mobile.setValue(res);
  }
  ////////////////////////////////////////
  // API error handling                 //
  //                                    //
  // TODO - Move to a shared component  //
  ////////////////////////////////////////
  // Initialiate Schools API error handling
  clearApiError() {
    this.errorMessage = null;
  }
  // // Process Schools API errors
  // private processApiError(error: any): void {
  //   this.error = error;
  // }
  // Fetch the first error message returned by the API. Expecting a structure that looks like this:
  // {
  //    "errors": {
  //      "Email": ["There's already an account with the email address: <EMAIL>"]
  //    },
  //    "type": "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1",
  //    "title": "One or more validation errors occurred.",
  //    "status": 400
  // }
  getApiError(error) {
    if (!error) {
      return null;
    }
    // console.log("getApiError(): Fetching API error from - " + JSON.stringify(this.error));
    if (error.errors) {
      let messages = this.getErrorMessages(error.errors);
      // Fetch the first error
      if ((0,lodash__WEBPACK_IMPORTED_MODULE_3__.isArray)(messages) && messages.length > 0) {
        return messages[0];
      }
    }
    // Old format
    if (error.message) {
      return error.message;
    }
    if (error.title) {
      return error.title;
    }
    // Return a generic message
    return 'Something has gone wrong...';
  }
  // Get all the error messages in a structure that looks like this:
  // {
  //    "errors": {
  //      "Email": ["There's already an account with the email address: <EMAIL>"]
  //    },
  //    "type": "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1",
  //    "title": "One or more validation errors occurred.",
  //    "status": 400
  // }
  getErrorMessages(errors) {
    if (!errors) {
      return;
    }
    let messages = [];
    Object.entries(errors).forEach(err => {
      if ((0,lodash__WEBPACK_IMPORTED_MODULE_3__.isArray)(err) && err.length > 1 && (0,lodash__WEBPACK_IMPORTED_MODULE_3__.isArray)(err[1]) && err[1].length > 0) {
        // Expecting err = ["FieldName" : ["Some sort of error message"]]
        messages.push(err[1][0]);
      }
    });
    return messages;
  }
  static {
    this.ɵfac = function LoginComponent_Factory(t) {
      return new (t || LoginComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_fire_compat_auth__WEBPACK_IMPORTED_MODULE_9__.AngularFireAuth), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_10__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_10__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices_authentication_auth_service__WEBPACK_IMPORTED_MODULE_5__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.NgZone), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_4__.PhoneNumberService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: LoginComponent,
      selectors: [["authentication-login"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵInheritDefinitionFeature"]],
      decls: 26,
      vars: 14,
      consts: [[1, "justify-content-center"], [1, "container"], [1, "cashlessForm", 3, "formGroup", "ngSubmit"], ["appearance", "outline", 4, "ngIf"], ["appearance", "outline"], ["matInput", "", "placeholder", "Email", "formControlName", "email", "type", "email", "required", ""], [4, "ngIf"], ["matInput", "", "placeholder", "Password", "formControlName", "password", "type", "password", "required", ""], ["class", "tcWrapper", 4, "ngIf"], ["type", "submit", 1, "submitButton"], ["class", "cashlessLink forgotPwd", 4, "ngIf"], [1, "containerBottom"], [1, "cashlessLink", "bottomLink"], ["class", "registerLabel", 4, "ngIf"], ["routerLink", "/register", "class", "register", 4, "ngIf"], ["matInput", "", "placeholder", "First name", "formControlName", "firstname", "type", "text", "required", ""], ["matInput", "", "placeholder", "Last name", "formControlName", "lastname", "type", "text", "required", ""], ["matInput", "", "placeholder", "Mobile number", "formControlName", "mobile", "type", "text", "required", "", 3, "keyup"], [1, "tcWrapper"], ["matPrefix", "", 1, "subtext"], ["formControlName", "termsConditions"], ["target", "_blank", 3, "href"], ["formControlName", "ageConditions"], [1, "cashlessLink", "forgotPwd"], ["routerLink", "/reset", 1, "resetLink"], [1, "registerLabel"], ["routerLink", "/login"], ["routerLink", "/register", 1, "register"]],
      template: function LoginComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "form", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("ngSubmit", function LoginComponent_Template_form_ngSubmit_2_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, LoginComponent_mat_form_field_3_Template, 5, 1, "mat-form-field", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, LoginComponent_mat_form_field_4_Template, 5, 1, "mat-form-field", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](5, LoginComponent_mat_form_field_5_Template, 5, 1, "mat-form-field", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "mat-form-field", 4)(7, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Email");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](9, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, LoginComponent_mat_error_10_Template, 2, 1, "mat-error", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "mat-form-field", 4)(12, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](13, "Password");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](14, "input", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, LoginComponent_mat_error_15_Template, 2, 1, "mat-error", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](16, LoginComponent_div_16_Template, 22, 5, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](17, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](19, LoginComponent_div_19_Template, 3, 0, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](20, LoginComponent_mat_error_20_Template, 3, 1, "mat-error", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div", 11)(22, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](23, LoginComponent_span_23_Template, 5, 0, "span", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](24, LoginComponent_span_24_Template, 2, 0, "span", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](25, LoginComponent_button_25_Template, 2, 1, "button", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.email.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.password.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("@bounce", ctx.bounce);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx.GetTextSubmit());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.errorMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.loginMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.loginMode);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_11__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_10__.RouterLink, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControlName, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_12__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_12__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_12__.MatError, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_12__.MatPrefix, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_13__.MatCheckbox, _angular_material_input__WEBPACK_IMPORTED_MODULE_14__.MatInput],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.container[_ngcontent-%COMP%] {\n  background-color: white;\n  border: 1px solid;\n  padding: 10px;\n  border: 1px solid #dddddd;\n  border-radius: 12px;\n  box-sizing: border-box;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.titleHeader[_ngcontent-%COMP%] {\n  color: #333b44;\n  font-size: 36px;\n  margin-left: auto;\n  margin-right: auto;\n  text-align: center;\n}\n\n.loginForm[_ngcontent-%COMP%] {\n  border: 1px solid black;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n\n.forgotPwd[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 15px;\n}\n\n.subtext[_ngcontent-%COMP%] {\n  font-size: 12px;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 5px 0;\n  size: 11px;\n  font-weight: 400;\n  color: #272c50;\n}\n\n.tcWrapper[_ngcontent-%COMP%] {\n  padding-left: 5px;\n  padding-right: 5px;\n}\n\n.mat-mdc-unelevated-button[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n  font-size: 22px;\n}\n\n.submitButton[_ngcontent-%COMP%] {\n  border-radius: 8px;\n  width: 100%;\n  height: 56px;\n  background: linear-gradient(96.62deg, #ff9e00 0%, #ff4b17 100%);\n  color: white;\n  font-family: \"bariol_bold\";\n  font-size: 22px;\n  border-width: 0px;\n}\n@media (min-width: 576px) {\n  .submitButton[_ngcontent-%COMP%] {\n    text-align: center;\n  }\n}\n\n.googleButton[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 36px;\n  border: 1px solid lightgrey;\n  color: grey;\n}\n\n.bottomLink[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 17px;\n}\n\n.resetLink[_ngcontent-%COMP%] {\n  color: #ff9e00;\n}\n\n.containerBottom[_ngcontent-%COMP%] {\n  background-color: white;\n  border: 1px solid;\n  border: 1px solid #dddddd;\n  border-radius: 12px;\n  box-sizing: border-box;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.register[_ngcontent-%COMP%] {\n  background: rgba(51, 59, 68, 0.15);\n  border-radius: 8px;\n  border-width: 0;\n  width: 90%;\n  height: 56px;\n  color: #333b44;\n  font-family: \"bariol_regular\";\n  font-size: 20px;\n  margin-bottom: 21px;\n  margin-top: 12px;\n}\n\n.offlineMode[_ngcontent-%COMP%] {\n  color: red;\n}\n\n.hidden[_ngcontent-%COMP%] {\n  display: none;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_15__.trigger)('bounce', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_15__.transition)('* => *', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_15__.useAnimation)(ng_animate__WEBPACK_IMPORTED_MODULE_16__.fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: {
            timing: 0.5,
            delay: 0
          }
        }))])]
      }
    });
  }
}

/***/ }),

/***/ 89518:
/*!********************************************************************!*\
  !*** ./src/app/authentication/components/reset/reset.component.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ResetComponent: () => (/* binding */ ResetComponent)
/* harmony export */ });
/* harmony import */ var D_projects_spriggy_git_spriggy_latest_web_cashless_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_fire_compat_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/fire/compat/auth */ 8245);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/button */ 84175);











function ResetComponent_mat_error_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.getErrorMessageEmail());
  }
}
function ResetComponent_mat_error_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r1.errorReset);
  }
}
function ResetComponent_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, " If the email address you entered is valid, instructions to reset your password will be sent there. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "p", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, " If you cannot locate the email, please check your junk/spam folder. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "p")(6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, "Come back to ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "a", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "sign in");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, " once your password has been reset");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
}
function ResetComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 7)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Go back to ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "a", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Sign in");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
}
class ResetComponent {
  constructor(afAuth, router, appInsightsService, userService) {
    this.afAuth = afAuth;
    this.router = router;
    this.appInsightsService = appInsightsService;
    this.userService = userService;
    this.submitted = false;
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({
      email: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl('', [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.email])
    });
  }
  ngOnInit() {}
  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get email() {
    return this.form.get('email');
  }
  getErrorMessageEmail() {
    return this.form.get('email').hasError('required') ? 'You must enter a value' : this.form.get('email').hasError('email') ? 'Not a valid email' : '';
  }
  ////////////////////////////////////////
  // Functions
  ////////////////////////////////////////
  ResetPassword() {
    var _this = this;
    return (0,D_projects_spriggy_git_spriggy_latest_web_cashless_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.errorReset = '';
      _this.submitted = true;
      _this.userService.UserResetPasswordEmail(_this.email.value).subscribe({
        next: res => {
          _this.appInsightsService.TrackEvent('ResetPasswordEmailSent', {
            Email: _this.email.value
          });
        },
        error: err => {
          _this.submitted = false;
          _this.errorReset = 'Oops, something went wrong. Please try again or contact us for assistance.';
        }
      });
    })();
  }
  static {
    this.ɵfac = function ResetComponent_Factory(t) {
      return new (t || ResetComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_fire_compat_auth__WEBPACK_IMPORTED_MODULE_4__.AngularFireAuth), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.CashlessAppInsightsService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.UserService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: ResetComponent,
      selectors: [["authentication-reset"]],
      decls: 10,
      vars: 6,
      consts: [[1, "cashlessForm", 3, "formGroup", "ngSubmit"], ["matInput", "", "placeholder", "Email", "formControlName", "email", "type", "email", "required", ""], [4, "ngIf"], ["mat-flat-button", "", "color", "primary", "type", "submit", 3, "disabled"], ["class", "cashlessLink bottomLink", 4, "ngIf"], [2, "font-weight", "bold"], ["routerLink", "/login"], [1, "cashlessLink", "bottomLink"]],
      template: function ResetComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "form", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngSubmit", function ResetComponent_Template_form_ngSubmit_0_listener() {
            return ctx.ResetPassword();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "mat-form-field");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "input", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, ResetComponent_mat_error_3_Template, 2, 1, "mat-error", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, ResetComponent_mat_error_4_Template, 2, 1, "mat-error", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "p")(6, "button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, "Reset");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](8, ResetComponent_ng_container_8_Template, 11, 0, "ng-container", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](9, ResetComponent_div_9_Template, 5, 0, "div", 4);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.email.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", (ctx.errorReset == null ? null : ctx.errorReset.length) > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", !ctx.form.valid);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.submitted);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.submitted);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _angular_forms__WEBPACK_IMPORTED_MODULE_3__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_3__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControlName, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_7__.MatError, _angular_material_input__WEBPACK_IMPORTED_MODULE_8__.MatInput, _angular_material_button__WEBPACK_IMPORTED_MODULE_9__.MatButton],
      styles: [".bottomLink[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 40px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aGVudGljYXRpb24vY29tcG9uZW50cy9yZXNldC9yZXNldC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5ib3R0b21MaW5rIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tdG9wOiA0MHB4O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_authentication_authentication_module_ts.js.map