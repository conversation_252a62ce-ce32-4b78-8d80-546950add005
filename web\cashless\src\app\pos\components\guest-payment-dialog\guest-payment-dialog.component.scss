.guest-payment-dialog {
  max-width: 500px;
  min-width: 400px;
  padding: 0;
  position: relative;

  .close-button-container {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 10;

    .close-button {
      color: #666;
      
      &:hover {
        color: #333;
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .dialog-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
  }

  .dialog-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0 0 24px 0;
  }

  // Success State
  .success-state {
    text-align: center;
    padding: 40px 24px;

    .success-icon {
      margin-bottom: 16px;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #4caf50;
      }
    }

    .success-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .success-message {
      font-size: 16px;
      color: #666;
      margin: 0 0 24px 0;
    }

    .success-details {
      p {
        margin: 8px 0;
        font-size: 14px;
        color: #333;
      }
    }
  }

  // Payment Form Container
  .payment-form-container {
    padding: 24px;
    padding-top: 48px; // Account for close button
  }

  // Order Summary
  .order-summary {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 12px 0;
      color: #333;
    }

    .order-items {
      .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e0e0e0;

        &:last-child {
          border-bottom: none;
        }

        .item-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .item-quantity {
          font-size: 14px;
          color: #666;
          margin: 0 12px;
        }

        .item-price {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }
    }

    .order-total {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 2px solid #e0e0e0;
      text-align: right;
      font-size: 16px;
      color: #333;
    }
  }

  // Payment Form
  .payment-form {
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .expiry-row {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;

      .expiry-month,
      .expiry-year {
        flex: 1;
      }
    }

    .expiry-error {
      margin-top: -12px;
      margin-bottom: 16px;
    }

    .cvv-field {
      width: 150px;
      margin-bottom: 16px;
    }

    // Card type styling
    .card-type-visa {
      color: #1a1f71;
    }

    .card-type-mastercard {
      color: #eb001b;
    }

    .card-type-american-express {
      color: #006fcf;
    }

    .card-type-unknown {
      color: #666;
    }
  }

  // Error Message
  .error-message {
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 16px;

    mat-error {
      margin: 0;
      font-size: 14px;
    }
  }

  // Security Notice
  .security-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #e8f5e8;
    border-radius: 4px;
    margin-bottom: 24px;

    mat-icon {
      color: #4caf50;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    span {
      font-size: 12px;
      color: #2e7d32;
    }
  }

  // Action Buttons
  .action-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;

    .cancel-btn {
      min-width: 100px;
    }

    .pay-btn {
      min-width: 140px;
      position: relative;

      mat-spinner {
        margin-right: 8px;
      }
    }
  }
}

// Form field customizations
::ng-deep {
  .guest-payment-dialog {
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        background-color: #fff;
      }

      &.mat-form-field-invalid {
        .mat-mdc-text-field-wrapper {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-select-panel {
      max-height: 200px;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .guest-payment-dialog {
    min-width: 320px;
    max-width: 100vw;

    .payment-form-container {
      padding: 16px;
      padding-top: 40px;
    }

    .expiry-row {
      flex-direction: column;
      gap: 8px;

      .expiry-month,
      .expiry-year {
        width: 100%;
      }
    }

    .cvv-field {
      width: 100%;
    }

    .action-buttons {
      flex-direction: column;

      .cancel-btn,
      .pay-btn {
        width: 100%;
        min-width: auto;
      }
    }
  }
}

// Loading state
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

// Animation for success state
.success-state {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
