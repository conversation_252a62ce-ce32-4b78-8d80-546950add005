﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class Term
    {
        [JsonProperty(PropertyName = "termId")]
        public int TermId { get; set; }

        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "startDate")]
        public string StartDate { get; set; }

        [JsonProperty(PropertyName = "endDate")]
        public string EndDate { get; set; }

        [JsonProperty(PropertyName = "schoolId")]
        public int SchoolId { get; set; }
    }
}
