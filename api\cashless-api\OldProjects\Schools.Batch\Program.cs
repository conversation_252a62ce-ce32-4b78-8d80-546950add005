﻿using System;
using System.IO;
using System.Reflection;
using Schools.BLL.Helpers;
using Schools.BLL.Services;
using Schools.BLL.Assemblers;
using FirebaseAdmin;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Schools.Batch.Helpers;
using Schools.Batch.Workers;
using Schools.DAL.Interfaces;
using Schools.DAL.Repositories;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;

namespace Schools.Batch;

public class Program
{
    public static void Main(string[] args)
    {
        // Create & inite the DI container
        var host = CreateHostBuilder(args).Build();
        var env = host.Services.GetRequiredService<IHostEnvironment>();
        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        var configService = host.Services.GetRequiredService<IConfigService>();

        logger.LogInformation("*** Environment: {Env} ***", env.EnvironmentName);
        configService.CheckConfig();

        logger.LogInformation("Start batch processing");

        host.Run();

        logger.LogInformation("End batch processing");
    }

    /// <summary>
    /// Load configuration, add logging and initialise the DI container
    /// </summary>
    public static IHostBuilder CreateHostBuilder(string[] args)
    {
        return Host.CreateDefaultBuilder(args)
                    .ConfigureAppConfiguration((context, config) =>
                    {
                        var settings = config.Build();

                        config.AddAzureAppConfiguration(options =>
                        {
                            options.Connect(settings["ConnectionStrings:AzureAppConfig"]);
                        });
                    })
                    .ConfigureLogging(logging =>
                    {
                        logging.ClearProviders();
                        logging.AddConsole();
                    })
                    .ConfigureServices((context, services) =>
                    {
                        services.AddSingleton<IConfigService, ConfigService>();
                        services.AddSingleton<IDBHelper, DBHelper>();
                        services.AddTransient<ISettingsRepository, SettingsRepository>();
                        services.AddTransient<IUserService, UserService>();
                        services.AddTransient<IIntercomUserService, IntercomUserService>();
                        services.AddTransient<IUserRepository, UserRepository>();
                        services.AddTransient<ISettingsService, SettingsService>();
                        services.AddTransient<ISendGridService, SendGridService>();
                        services.AddTransient<IFirebaseService, FirebaseService>();
                        services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
                        services.AddTransient<IUnitOfWork, UnitOfWork>();
                        services.AddTransient<IStockRepository, StockRepository>();
                        services.AddTransient<IMenuRepository, MenuRepository>();
                        services.AddTransient<IPaymentRepository, PaymentRepository>();
                        services.AddTransient<IPaymentService, PaymentService>();
                        services.AddTransient<IReconciliationService, ReconciliationService>();
                        services.AddTransient<IOrderRepository, OrderRepository>();
                        services.AddTransient<ICanteenRepository, CanteenRepository>();
                        services.AddTransient<ISchoolRepository, SchoolRepository>();
                        services.AddTransient<IAddressRepository, AddressRepository>();
                        services.AddTransient<INoticeRepository, NoticeRepository>();
                        services.AddTransient<ITelemetryService, TelemetryService>();
                        services.AddTransient<TelemetryClient>();
                        services.AddTransient<IAuditService, AuditService>();
                        services.AddTransient<ICurrentUserHelper, CurrentUserHelper>();
                        services.AddTransient<ISchoolService, SchoolService>();
                        services.AddTransient<ITransactionRepository, TransactionRepository>();
                        services.AddTransient<IScoreApiClient, ScoreApiClient>();
                        services.AddTransient<ISchoolClassRepository, SchoolClassRepository>();
                        services.AddTransient<ISchoolFeatureRepository, SchoolFeatureRepository>();
                        services.AddTransient<IReconciliationRepository, ReconciliationRepository>();
                        services.AddTransient<IAddressService, AddressService>();
                        services.AddTransient<IAddressAssembler, AddressAssembler>();
                        services.AddTransient<IPaymentValidator, PaymentValidator>();
                        services.AddTransient<ITransactionHistoryService, TransactionHistoryService>();
                        services.AddTransient<IPaymentResultProcessor, PaymentResultProcessor>();
                        services.AddTransient<IWorkerHelper, WorkerHelper>();

                        RegisterProcess(services);

                        // Is this needed?
                        services.AddHttpClient();

                        ConfigureScoreApiClient(context.Configuration);

                        services.AddHttpClient(ScoreApiClient.HttpClientName, ScoreApiClient.ConfigureHttpClient)
                                .SetHandlerLifetime(TimeSpan.FromMinutes(60));

                        ConfigAuthentication(services);
                    });
    }

    private static void RegisterProcess(IServiceCollection services)
    {
        Console.WriteLine("Please select a process to run:");
        Console.WriteLine("1 - Update user role claims");
        Console.WriteLine("2 - Update reconciliation data");
        Console.WriteLine("3 - Update missing Spriggy Account IDs");

        string processName = Console.ReadLine();

        switch (processName)
        {
            case "1":
                services.AddHostedService<UserCustomClaimsWorker>();
                break;

            case "2":
                services.AddHostedService<ReconciliationWorker>();
                break;

            case "3":
                services.AddHostedService<SpriggyAccountIdWorker>();
                break;

            default:
                Console.WriteLine("Invalid choice");
                break;
        }
    }

    /// <summary>
    /// Configure the spriggy-core API client
    /// </summary>
    private static void ConfigureScoreApiClient(IConfiguration config)
    {
        ScoreApiClient.ScoreUrl = config["scoreUrl"];
        ScoreApiClient.ScoreToken = config["scoreToken"];
        ScoreApiClient.ScoreConfigUri = config["scoreConfigUri"];
        ScoreApiClient.ApimKey = config["apimKey"];
        ScoreApiClient.Capacity = Int32.Parse(config["scoreCapacity"]);
        ScoreApiClient.QueueLength = Int32.Parse(config["scoreQueueLength"]);
        ScoreApiClient.MedianFirstRetryDelay = Int32.Parse(config["scoreMedianFirstRetryDelay"]);
        ScoreApiClient.RetryCount = Int32.Parse(config["scoreRetryCount"]);

        ScoreApiClient.CheckApiSettings();
    }

    /// <summary>
    /// Read the Firebase settings from App Configuration and initilise the FirebaseApp instance
    /// </summary>
    private static void ConfigAuthentication(IServiceCollection services)
    {
        var serviceProvider = services.BuildServiceProvider();
        var env = serviceProvider.GetRequiredService<IHostEnvironment>();
        var config = serviceProvider.GetRequiredService<IConfiguration>();
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("***** ConfigAuthentication [ env : {EnvironmentName}] *****", env.EnvironmentName);

        // Get the settings to configure Firebase
        var credentialPath = GetFirebaseSettingsFromAppConfiguration(config, logger);

        logger.LogDebug("Using Firebase application credentials at {CredentialPath}", credentialPath);

        // Configure Firebase according to the env.
        Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", credentialPath);
        FirebaseApp.Create();
    }

    /// <summary>
    /// Fetch the settings from App Configuration and write them to a local file
    /// </summary>
    private static string GetFirebaseSettingsFromAppConfiguration(IConfiguration config, ILogger<Program> logger)
    {
        string directory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        string filename = "firebase-settings.json";

        try
        {
            // Fetch the Firebase config from App Configuration
            string json = config["firebaseSettings"];
            if (string.IsNullOrWhiteSpace(json))
            {
                throw new ApplicationException("Unable to load Firebase settings from App Configuration");
            }

            logger.LogDebug("Writing {Bytes:D} bytes of Firebase settings to file: {Filename}", json.Length, filename);

            // Write the JSON loaded to a file
            return Utils.WriteTextFile(directory, filename, json);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to fetch Firebase settings / write them to local file system at: {Directory}", directory);

            return null;
        }
    }
}
