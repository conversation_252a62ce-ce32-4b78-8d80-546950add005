﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Schools.BLL.Classes.Payments;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class MultipleOrdersWithPayment
    {
        [JsonProperty(PropertyName = "Orders")]
        public MultipleOrders Orders { get; set; }

        [JsonProperty(PropertyName = "Payment")]
        public PaymentRequest Payment { get; set; }

        public bool IsEditOrder => !Orders?.Orders?.Any(o => o.OrderId == null) ?? false;
        public bool UseNewOrderProcess(HashSet<long> approvedSchools) => approvedSchools != null && approvedSchools.Any() && (!Orders?.Orders?.Select(o => (long)o.SchoolId)?.ToList()?.Except(approvedSchools).Any() ?? false);
    }

    public class MultipleOrderRequests
    {
        [JsonProperty(PropertyName = "Orders")]
        [Required]
        [MinLength(1)]
        public List<OrderPlacement> Orders { get; set; }
    }

    public class OrderEditRequest
    {
        [JsonProperty(PropertyName = "OrderId")]
        [Range(1, int.MaxValue, ErrorMessage = "Enter a valid OrderId")]
        public int OrderId { get; set; }

        [JsonProperty(PropertyName = "Items")]
        [Required]
        [MinLength(1)]
        public List<OrderPlacementItem> Items { get; set; }
    }
}
