﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Cashless.APIs.Validators;
using Schools.BLL.Assemblers;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Schools.DAL.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SchoolController : ControllerBase
    {
        private readonly IDBHelper dbHelper;
        private readonly IMenuService menuService;
        private readonly ICanteenService canteenService;
        private readonly INotificationService notificationService;
        private readonly ISchoolService schoolService;
        private readonly IXeroService xeroService;
        private readonly ISchoolMerchantService _schoolMerchantService;
        private readonly IUnitOfWork unitOfWork;
        private readonly ILogger<SchoolController> logger;

        private readonly ISchoolValidator schoolValidation;

        public SchoolController(IDBHelper dbHelper, ISchoolService schoolService, IMenuService menuService,
                                ICanteenService canteenService, INotificationService notificationService,
                                IXeroService xeroService, ISchoolMerchantService schoolMerchantService, IUnitOfWork unitOfWork,
                                ILogger<SchoolController> logger, ISchoolValidator schoolValidation)
        {
            this.dbHelper = dbHelper;
            this.schoolService = schoolService;
            this.menuService = menuService;
            this.canteenService = canteenService;
            this.notificationService = notificationService;
            this.xeroService = xeroService;
            this._schoolMerchantService = schoolMerchantService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
            this.schoolValidation = schoolValidation;
        }

        [Route("GetCanteenListByUserId/{id}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetCanteenListByUserId(string id)
        {
            await this.schoolValidation.ValidateCurrentUser(id);

            var res = await this._schoolMerchantService.GetMerchantsAndSchoolsWithPermissionsByUser();

            return new OkObjectResult(res);
        }

        [Route("GetCanteenList")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetCanteenList()
        {
            var res = await this.dbHelper.ExecSprocByParams<List<CanteenEntity>>("sp_Canteen_List");

            return new OkObjectResult(res);
        }

        /// <summary>
        /// This is used by the sps-cas-alert-order-not-printed logic app. Needs anonymous access
        /// 
        /// Needs anonymous access, but we will check if the request
        /// contains the required cashless-api-secret header.
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("NotifyNotPrintedOrdersListByCanteen")]
        [HttpGet]
        public async Task<IActionResult> NotifyNotPrintedOrdersListByCanteen()
        {
            IEnumerable<School> schools = await this.unitOfWork.SchoolRepository.GetSchoolsToNotifyOfUnprintedOrders();

            if (schools == null)
            {
                this.logger.LogWarning("There were no schools to notify of unprinted orders!");

                return new OkObjectResult(null);
            }

            foreach (var school in schools)
            {
                var date = DateTime.Now.ToString("yyyy-MM-dd");
                var listOrders = await this.dbHelper.ExecSprocByParams<List<Order>>("sp_Order_NotPrinted_GetBy_SchoolId_And_Date",
                                                    new Dictionary<string, string>() {
                                                        { "id", $"{school.SchoolId}" },
                                                        { "orderdate", $"{date}" }
                                                    });

                if (listOrders != null && listOrders.Count > 0)
                {
                    // notify users
                    // Get Canteen Users
                    var canteenUsers = await this.dbHelper.ExecSprocByParams<List<User>>("sp_School_Users_GetBy_SchoolId_With_NotPrinted_Alert",
                                                            new Dictionary<string, string>() { { "schoolId", $"{school.SchoolId}" } });

                    await this.notificationService.SendPrintedAlertNotification(listOrders, canteenUsers);
                }
            }

            return new OkObjectResult(schools);
        }

        [Route("GetSchoolList")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetSchoolList([FromBody] SchoolsRequest request)
        {
            // To Do: Restrict to school names only in response
            // TODO - Request params are ignored. Change the clients to use
            //        the GET method above
            ListSchoolsResponse ListSchoolsResponse = new();
            ListSchoolsResponse.Schools = await this.dbHelper.ExecSprocBySearch<List<School>>("sp_School_GetBy_Search", "");

            return new OkObjectResult(ListSchoolsResponse);
        }

        [Route("GetSchools")]
        [Route("GetSchoolsMobile")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetSchools()
        {
            // To Do: Restrict to school names only in response
            ListSchoolsResponse response = new();
            response.Schools = await this.dbHelper.ExecSprocBySearch<List<School>>("sp_School_GetBy_Search", "");

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Create a school and associate it with the given canteen
        /// Obsolete => new endpoint had been created to use in replacement of this one 
        /// </summary>
        [Route("CreateSchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        [Obsolete] // currently in use on the FE => to be deleted at a later date
        public async Task<IActionResult> CreateSchool([FromBody] CreateSchoolRequest request)
        {
            //check if new school name is a duplicate of an existing school name
            IEnumerable<School> result = await this.unitOfWork.SchoolRepository.SearchForSchoolsByName(request.School.Name);

            if (result != null && result.Count() > 0)
            {
                throw new ValidationException("Name", "Cannot create school with duplicate school name.");
            }

            // Create School
            SchoolEntity convertedRequest = new()
            {
                AddressId = 0,
                IsActive = true,
                Name = request.School.Name,
                PricingAmount = request.School.PricingAmount,
                PricingCap = request.School.PricingCap,
                PricingModel = request.School.PricingModel,
                StateId = Convert.ToInt16(request.School.StateId),
                DeactivatedFilters = request.School.DeactivatedFilters
            };

            SchoolEntity schoolEntity = await this.schoolService.UpsertSchool(convertedRequest);

            // get canteen
            CanteenEntity canteenEntity = await this.canteenService.GetCanteenById(request.CanteenId);

            // create defaults menus
            await this.menuService.CreateMenus(Convert.ToInt32(schoolEntity.SchoolId), Convert.ToInt32(canteenEntity.CanteenId), canteenEntity.CanteenType);

            // create default terms
            await this.unitOfWork.SchoolRepository.SetupTermsBySchoolId(Convert.ToInt32(schoolEntity.SchoolId));

            // link school to merchant
            UpsertSchoolBillingDto dto = new()
            {
                SchoolId = schoolEntity.SchoolId,
                CanteenId = canteenEntity.CanteenId,
                CanteenType = canteenEntity.CanteenType,
            };

            await this.canteenService.CreateSchoolBillingSettings(dto);

            // add Admin Users to this new school
            // TODO delete this procedure and use "sp_SchoolsUsers_Insert" wit the list of users to add
            await this.dbHelper.ExecSprocByParams("sp_School_Add_Admin_Users",
                                new Dictionary<string, string>() {
                                    { "schoolId", $"{schoolEntity.SchoolId}" },
                                    { "canteenId", $"{request.CanteenId}" }
                                });

            // create default label settings
            await this.schoolService.SetupLabelPositionsBySchoolId(Convert.ToInt32(schoolEntity.SchoolId));

            return new OkObjectResult(schoolEntity);
        }

        /// <summary>
        /// Create a new School
        /// </summary>
        [Route("")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> Create([FromBody] CreateSchoolDto school)
        {
            //check if new school name is a duplicate of an existing school name
            IEnumerable<School> result = await this.unitOfWork.SchoolRepository.SearchForSchoolsByName(school.Name);

            if (result != null && result.Count() > 0)
            {
                throw new ValidationException("Name", "Cannot create school with duplicate school name.");
            }

            // Create School
            SchoolEntity schoolEntity = await this.schoolService.CreateSchool(school);

            // link school to merchant
            await this.canteenService.LinkSchoolToMerchant(Convert.ToInt32(schoolEntity.SchoolId), school.MerchantId);

            // add Admin Users to this new school
            await this.dbHelper.ExecSprocByParams("sp_School_Add_Admin_Users",
                                new Dictionary<string, string>() {
                                    { "schoolId", $"{schoolEntity.SchoolId}" },
                                    { "canteenId", $"{school.MerchantId}" }
                                });

            return new OkObjectResult(schoolEntity.SchoolId);
        }

        /// <summary>
        /// Edit School information
        /// </summary>
        [Route("{schoolId}")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> EditSchool(int schoolId, [FromBody] SchoolDto school)
        {
            // check request
            if (schoolId == 0 || schoolId != school.SchoolId) throw new ValidationException("schoolId", "Invalid request");

            await this.schoolService.EditSchool(school);

            return new OkResult();
        }

        /// <summary>
        /// Get the active merchants link to the school
        /// </summary>
        [Route("{schoolId}/Merchants")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetMerchantsPerSchool(int schoolId)
        {
            if (schoolId <= 0) throw new ValidationException("schoolId", "Invalid request");

            // get the active merchants associated with the school
            IEnumerable<CanteenEntity> merchants = await this.canteenService.GetAllMerchantsBySchoolId(schoolId, false);

            // convert entities to dto
            List<MerchantDto> res = new MerchantAssembler().Convert_ListCanteenEntity_To_ListMerchantDto(merchants);

            return new OkObjectResult(res);
        }

        [Route("UpsertSchool")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [Obsolete] // TODO delete when new school management is out
        public async Task<IActionResult> UpsertSchool([FromBody] School school)
        {
            if (school.SchoolId == null || school.SchoolId == 0) throw new ValidationException("schoolId", "Invalid request");

            // Get School by ID
            // note: thise does not fetch/set the school id on the entity
            SchoolEntity schoolEntity = await this.unitOfWork.SchoolRepository.GetSchoolEntityById(Convert.ToInt32(school.SchoolId));

            if (schoolEntity == null)
            {
                throw new SchoolNotFoundException($"School{school.SchoolId} not found");
            }

            await this.schoolValidation.ValidateAccessToSchool((long)school.SchoolId);

            schoolEntity.SchoolId = Convert.ToInt32(school.SchoolId);
            schoolEntity.AddressId = 0;
            schoolEntity.IsActive = school.IsActive;
            schoolEntity.Name = school.Name;
            schoolEntity.PricingAmount = school.PricingAmount;
            schoolEntity.PricingCap = school.PricingCap;
            schoolEntity.PricingModel = school.PricingModel;
            schoolEntity.StateId = Convert.ToInt16(school.StateId);
            schoolEntity.DeactivatedFilters = school.DeactivatedFilters;
            schoolEntity.OpeningDays = school.OpeningDays;
            schoolEntity.WeeksPreOrder = Convert.ToInt16(school.WeeksPreOrder);

            SchoolEntity schoolResponse = await this.schoolService.UpsertSchool(schoolEntity);

            return new OkObjectResult(school);
        }

        [Route("GetSchoolById/{schoolId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetSchoolById(int schoolId)
        {
            await this.schoolValidation.ValidateAccessToSchool(schoolId: schoolId);

            School schoolResponse = new();

            if (schoolId == 0) throw new ValidationException("schoolId", "Invalid request");

            schoolResponse = await this.schoolService.GetSchoolById(schoolId);

            return new OkObjectResult(schoolResponse);
        }

        [Route("GetSchoolsWithFilters")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetSchoolsWithFilters([FromBody] ArrayFilter filter)
        {
            SchoolListResults response = new();
            response.SchoolList = await this.dbHelper.ExecSprocByParams<List<School>>("sp_School_GetAll_With_Filters",
                                                        new Dictionary<string, string>() {
                                                            { "filter", $"{filter.Filter}" },
                                                            { "pageIndex", $"{filter.PageIndex}" },
                                                            { "numberRows", $"{filter.NumberRows}" }
                                                        });

            return new OkObjectResult(response);
        }

        [Route("SaveAccountXero/{code}/{canteenId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin)]
        // Wired up to front end but not used
        [Obsolete]
        public async Task<IActionResult> SaveAccountXero(string code, int canteenId)
        {
            await this.xeroService.GetXeroToken(canteenId, code);

            return new OkResult();
        }

        [Route("UpsertSchoolDate")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertSchoolDate([FromBody] SchoolDate schoolDate)
        {
            var schoolDateToUpsert = JsonConvert.SerializeObject(schoolDate);
            await this.schoolValidation.ValidateAccessToSchool(schoolDate.SchoolId);
            var result = await this.dbHelper.ExecSproc("sp_SchoolDate_Upsert", schoolDateToUpsert);

            if (result == string.Empty)
                return new BadRequestResult();

            return new OkResult();
        }

        [Route("DeleteSchoolDate")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteSchoolDate(long schoolDateId)
        {
            if (schoolDateId == 0)
            {
                return new OkResult();
            }

            var schoolDate = await this.schoolService.GetSchoolDateById(schoolDateId);

            await this.schoolValidation.ValidateAccessToSchool(schoolDate.SchoolId);

            if (schoolDate == null)
            {
                return new OkResult();
            }

            await this.schoolService.DeleteSchoolDateById(schoolDateId);

            return new OkResult();
        }

        [Route("GetSchoolDatesById/{schoolId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin)]
        [Obsolete]
        public Task<IActionResult> GetSchoolDatesById(long schoolId)
        {
            throw new DeprecatedApiException();
        }

        [Route("GetValidSchoolClosingDatesById/{schoolId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetValidSchoolClosingDatesById(int schoolId)
        {
            if (schoolId == 0) throw new ValidationException("schoolId", "Invalid request");

            await this.schoolValidation.ValidateAccessToSchool(schoolId);

            var schoolDates = await this.schoolService.GetValidCloseDatesBySchoolId(schoolId);

            if (schoolDates is null)
                return new NoContentResult();

            return new OkObjectResult(schoolDates);
        }

        [Route("UpsertSchoolTerm")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [Obsolete]
        public Task<IActionResult> UpsertSchoolTerm([FromBody] SchoolTerm schoolTerm)
        {
            throw new DeprecatedApiException();
        }

        [Route("GetSchoolTermsById/{schoolId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin)]
        [Obsolete]
        public Task<IActionResult> GetSchoolTermsById(long schoolId)
        {
            throw new DeprecatedApiException();
        }

        [Route("GetNutritionalValuesByState/{stateId}")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetNutritionalValuesByState(long stateId)
        {
            var res = await this.dbHelper.ExecSprocByParams<List<NutritionalValue>>("sp_NutritionalValues_GetBy_StateId",
                                            new Dictionary<string, string>() { { "stateId", $"{stateId}" } });

            return new OkObjectResult(res);
        }

        [Route("GetAllStates")]
        [HttpGet]
        [CheckUserRole(UserRole.Admin)]
        public async Task<IActionResult> GetAllStates()
        {
            var res = await this.dbHelper.ExecSprocByParams<List<State>>("sp_States_Get_All",
                                            new Dictionary<string, string>() { });

            return new OkObjectResult(res);
        }

        [Route("UpsertSchoolOptions")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertSchoolOptions([FromBody] OptionSchool optionSchoolRequest)
        {
            this.schoolValidation.ValidateOptionSchool(optionSchoolRequest);

            await this.schoolValidation.ValidateAccessToSchool(optionSchoolRequest.SchoolId);

            var result = await this.schoolService.UpsertSchoolOption(optionSchoolRequest);

            if (result == string.Empty)
                return new BadRequestResult();

            // active / deactive alleergies position on the labels
            if (optionSchoolRequest.OptionName == SchoolFeatureEnum.AllergyAlert)
            {
                var json = await this.dbHelper.ExecSprocByParams("sp_School_Update_Allergies_positions",
                                                new Dictionary<string, string>() {
                                                    { "schoolId", $"{optionSchoolRequest.SchoolId}" },
                                                    { "isActive", $"{optionSchoolRequest.IsActive}" }
                                                });
            }

            return new OkResult();
        }
    }
}
