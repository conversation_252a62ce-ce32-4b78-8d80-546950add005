﻿using System;
using System.Collections.Generic;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Orders;

public class OrderStatusDto
{
    public long OrderStatusId { get; set; }

    public long OrderId { get; set; }

    public OrderActionsEnum ActionType { get; set; }

    public OrderProcessStatusEnum PaymentStatus { get; set; }

    public OrderProcessStatusEnum StockStatus { get; set; }

    public OrderProcessStatusEnum NotificationStatus { get; set; }

    public long TriggeredBy { get; set; }

    public string CorrelationId { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime? DateModifiedUtc { get; set; }
}

public class OrderWithErrorDto : OrderWithErrorCommon
{
    public string Action { get; set; }

    public long ParentId { get; set; }

    public List<OrderErrorDto> Errors { get; set; }
}

public class OrderErrorDto
{
    public long ErrorId { get; set; }

    public string ErrorType { get; set; }

    public string ErrorMessage { get; set; }
}

public class OrderDashboardDto
{
    public List<SelectListDataDto> SchoolsFilter { get; set; }

    public List<SelectListDataDto> OrderDatesFilter { get; set; }

    public IEnumerable<OrderWithErrorDto> Orders { get; set; }
}

public class SelectListDataDto
{
    public long Id { get; set; }

    public string Label { get; set; }

    public long? AdditionalNumber { get; set; }
}