[{"ContainingType": "Cashless.API.Controllers.AdminController", "Method": "MoveDraftOrderToConfirmed", "RelativePath": "api/Admin/MoveDraftOrderToConfirmed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.API.Controllers.AdminController", "Method": "MoveDraftOrderToConfirmed", "RelativePath": "api/Admin/MoveDraftOrderToConfirmed/{orderId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.API.Controllers.AdminController", "Method": "MoveDraftOrderToError", "RelativePath": "api/Admin/MoveDraftOrderToError", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.API.Controllers.AdminController", "Method": "MoveDraftOrderToError", "RelativePath": "api/Admin/MoveDraftOrderToError/{orderId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.AppVersionController", "Method": "Index", "RelativePath": "api/AppVersion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.AppVersionController", "Method": "GetSupportedVersions", "RelativePath": "api/AppVersion/GetSupportedVersions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetBillingHistory", "RelativePath": "api/Billing/GetBillingHistory/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetCreditHistory", "RelativePath": "api/Billing/GetCreditHistory/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetPaymentHistory", "RelativePath": "api/Billing/GetPaymentHistory/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetTopUpHistory", "RelativePath": "api/Billing/GetTopUpHistory/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetTransactionHistory", "RelativePath": "api/Billing/GetTransactionHistory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Payments.TransactionHistorySearchRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "GetTransactionOrders", "RelativePath": "api/Billing/GetTransactionOrders/{transactionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.BillingController", "Method": "UpdateBillingState", "RelativePath": "api/Billing/UpdateBillingState/{paymentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "multipleOrders", "Type": "Schools.BLL.Classes.MultipleOrders", "IsRequired": true}, {"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "AddOptionToItem", "RelativePath": "api/Editor/AddOptionToItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.AddRemoveOptionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "AddStockToItem", "RelativePath": "api/Editor/AddStockToItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.AddRemoveStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "ArchiveCategory", "RelativePath": "api/Editor/ArchiveCategory/{categoryId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "ArchiveItemOptions", "RelativePath": "api/Editor/ArchiveItemOptions", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "option", "Type": "Schools.DAL.Entities.Option", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "ArchiveSubOption", "RelativePath": "api/Editor/ArchiveSubOption", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "subOption", "Type": "Schools.DAL.Entities.SubOption", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "DeleteImage", "RelativePath": "api/Editor/DeleteImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "image", "Type": "Schools.DAL.DtosToMoveToBLL.MenuItemImage", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "DeleteItemAvailabilityDates", "RelativePath": "api/Editor/DeleteItemAvailabilityDates", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "availabilityId", "Type": "System.Int64", "IsRequired": false}, {"Name": "menuItemId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "DeleteMenuItem", "RelativePath": "api/Editor/DeleteMenuItem", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItem", "Type": "MenuItem", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GenerateAllMenus", "RelativePath": "api/Editor/GenerateAllMenus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GenerateAllMenus", "RelativePath": "api/Editor/GenerateAllMenus/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetActiveSchoolNotices", "RelativePath": "api/Editor/GetAvailabilities/{menuItemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetCategoriesForEditorByCanteenId", "RelativePath": "api/Editor/GetCategoriesForEditorByCanteenId/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetCategoriesImagesByCanteenType", "RelativePath": "api/Editor/GetCategoriesImagesByCanteenType/{canteenType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetCategoriesListByCanteenId", "RelativePath": "api/Editor/GetCategoriesListByCanteenId/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetCategoriesMenusByCanteenId", "RelativePath": "api/Editor/GetCategoriesMenusByCanteenId/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemAvailabilityDates", "RelativePath": "api/Editor/GetItemAvailabilityDates/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemOptions", "RelativePath": "api/Editor/GetItemOptions/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemsByCanteen", "RelativePath": "api/Editor/GetItemsByCanteen/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemsByCategoryId", "RelativePath": "api/Editor/GetItemsByCategoryId/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemsByOptionId", "RelativePath": "api/Editor/GetItemsByOptionId/{optionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "optionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetMenuItemsByStockId", "RelativePath": "api/Editor/GetMenuItemsByStockId/{stockId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetOptionById", "RelativePath": "api/Editor/GetOptionById/{optionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "optionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetOptionsByItemId", "RelativePath": "api/Editor/GetOptionsByItemId/{menuItemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetSchoolList", "RelativePath": "api/Editor/GetSchoolList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetStocksByCanteenId", "RelativePath": "api/Editor/GetStocksByCanteenId/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetStocksById", "RelativePath": "api/Editor/GetStocksById/{stockId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "InsertCategory", "RelativePath": "api/Editor/InsertCategory", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.ItemCategoryInsertRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "MarkItemAsOutOfStock", "RelativePath": "api/Editor/Item/{menuItemId}/MarkOutOfStock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItemId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "GetItemForSelectListsByCanteen", "RelativePath": "api/Editor/items/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "RemoveOptionFromItem", "RelativePath": "api/Editor/RemoveOptionFromItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.AddRemoveOptionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "RemoveStockFromItem", "RelativePath": "api/Editor/RemoveStockFromItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.AddRemoveStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "CreateStock", "RelativePath": "api/Editor/Stock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.UpsertStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Schools.DAL.DtosToMoveToBLL.Stock", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpdateStock", "RelativePath": "api/Editor/Stock", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.UpsertStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Schools.DAL.DtosToMoveToBLL.Stock", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "InsertSubOption", "RelativePath": "api/Editor/SubOption", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "subOption", "Type": "Schools.DAL.Entities.SubOption", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpdateSubOption", "RelativePath": "api/Editor/SubOption", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "subOption", "Type": "Schools.BLL.Classes.SubOptionEdit", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "ArchiveSubOption", "RelativePath": "api/Editor/SubOption/{subOptionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "subOptionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpdateCategory", "RelativePath": "api/Editor/UpdateCategory", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.ItemCategoryUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpdateLinkItemAndMenu", "RelativePath": "api/Editor/UpdateLinkItemAndMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ItemMenus", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpdateSchoolPrintOptions", "RelativePath": "api/Editor/UpdateSchoolPrintOptions", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolPrintOptionsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UploadImage", "RelativePath": "api/Editor/UploadImage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "itemId", "Type": "System.Int64", "IsRequired": false}, {"Name": "schoolCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertItemAvailabilityDates", "RelativePath": "api/Editor/UpsertItemAvailabilityDates", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItemAvailabilityRequest", "Type": "Schools.BLL.Classes.Menus.MenuItemAvailabilityRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertItemOptions", "RelativePath": "api/Editor/UpsertItemOptions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "option", "Type": "Schools.DAL.Entities.Option", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertMenu", "RelativePath": "api/Editor/UpsertMenu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menu", "Type": "Schools.DAL.Entities.Menu", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertMenuItem", "RelativePath": "api/Editor/UpsertMenuItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItem", "Type": "MenuItem", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertStock", "RelativePath": "api/Editor/UpsertStock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.UpsertStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.EditorController", "Method": "UpsertSubOption", "RelativePath": "api/Editor/UpsertSubOption", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "subOption", "Type": "Schools.DAL.Entities.SubOption", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "AddFee", "RelativePath": "api/Fee/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.AddFeeUsedRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "AddFees", "RelativePath": "api/Fee/AddMany", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.AddFeeUsedRequest, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetFee", "RelativePath": "api/Fee/Get", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.FeesRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetFees", "RelativePath": "api/Fee/Get<PERSON>any", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.FeesRequestNoMerchantId, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetManyFees", "RelativePath": "api/Fee/GetManyFees", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.FeesRequest, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "UpdateFeeStatusByOrder", "RelativePath": "api/Fee/UpdateStatusByOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.UpdateFeeStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "AddFeeCalculator", "RelativePath": "api/FeeCalculator/AddFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.AddFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "AddSchoolsToFeeCalculator", "RelativePath": "api/FeeCalculator/AddSchoolsToFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.SchoolsFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetAll", "RelativePath": "api/FeeCalculator/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetAllSchoolMappings", "RelativePath": "api/FeeCalculator/GetAllSchoolFeeMappings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculator", "RelativePath": "api/FeeCalculator/GetFeeCalculator/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculatorByMerchantAndSchool", "RelativePath": "api/FeeCalculator/GetFeeCalculatorByMerchantAndSchool/{merchantId}/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculatorIdByName", "RelativePath": "api/FeeCalculator/GetFeeCalculatorIdByName/{feeCalculatorName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetSchoolsByFeeCalculator", "RelativePath": "api/FeeCalculator/GetSchoolsByFeeCalculator/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "HasActiveSchoolFeeConfiguration", "RelativePath": "api/FeeCalculator/HasActiveSchoolFeeConfiguration/{merchantId}/{schoolId}/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}, {"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "RemoveFeeCalculator", "RelativePath": "api/FeeCalculator/RemoveFeeCalculator/{feeCalculatorId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "RemoveSchoolFromFeeCalculator", "RelativePath": "api/FeeCalculator/RemoveSchoolFromFeeCalculator/{merchantId}/{schoolId}/{feeCalculatorId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}, {"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "UpdateSchoolsToFeeCalculator", "RelativePath": "api/FeeCalculator/UpdateSchoolsToFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.SchoolsFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.HomeController", "Method": "Home", "RelativePath": "api/Home", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Screens.HomeScreenRefreshRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.HomeController", "Method": "HomeChangeWeek", "RelativePath": "api/Home/HomeRefresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Screens.HomeScreenRefreshRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.HomeController", "Method": "LoginSuccess", "RelativePath": "api/Home/LoginSuccess", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetConfiguredMenuList", "RelativePath": "api/Menu/GetConfiguredMenuList/{merchantId}/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int32", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetItemByCategory", "RelativePath": "api/Menu/GetItemByCategory/{category}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetItemByCategoryAndMenu", "RelativePath": "api/Menu/GetItemByCategoryAndMenu/{category}/{menu}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": true}, {"Name": "menu", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetItemById", "RelativePath": "api/Menu/GetItemById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenu", "RelativePath": "api/Menu/GetMenu/{menuId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuBySchoolAndType", "RelativePath": "api/Menu/GetMenuBySchoolAndType/{schoolId}/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuBySchoolAndType", "RelativePath": "api/Menu/GetMenuBySchoolAndTypeMobile/{schoolId}/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuBySchoolAndType2", "RelativePath": "api/Menu/GetMenuByStudentAndType/{type}/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": true}, {"Name": "studentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuItemList", "RelativePath": "api/Menu/GetMenuItemList/{menuId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuItemListBySchool", "RelativePath": "api/Menu/GetMenuItemListBySchool/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuItemListBySchoolDate", "RelativePath": "api/Menu/GetMenuItemListBySchoolDate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Menus.AvailableMenuItemsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuList", "RelativePath": "api/Menu/GetMenuList/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "GetMenuNamesList", "RelativePath": "api/Menu/GetMenuNamesList/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MenuController", "Method": "UpdateFoodBreakSettings", "RelativePath": "api/Menu/UpdateFoodBreakSettings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Menus.FoodBreakSettingsUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "GetAll", "RelativePath": "api/Merchant", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "CreateMerchant", "RelativePath": "api/Merchant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Canteens.CreateMerchantRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "Get", "RelativePath": "api/Merchant/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "EditMerchant", "RelativePath": "api/Merchant/{canteenId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Canteens.UpdateMerchantDetailsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "EditMerchantContact", "RelativePath": "api/Merchant/{canteenId}/Contact", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Canteens.UpdateMerchantOwnerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "Update", "RelativePath": "api/Merchant/{canteenId}/Fee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Canteens.UpdateMerchantFeeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantSchools", "RelativePath": "api/Merchant/{canteenId}/schools", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "LinkSchoolToMerchant", "RelativePath": "api/Merchant/{canteenId}/schools/{schoolId}/link", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "UnlinkSchoolFromMerchant", "RelativePath": "api/Merchant/{canteenId}/schools/{schoolId}/unlink", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "UpdateSchoolMerchantSettings", "RelativePath": "api/Merchant/{canteenId}/schools/{schoolId}/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.UpsertSchoolBillingDto", "IsRequired": true}, {"Name": "canteenId", "Type": "", "IsRequired": true}, {"Name": "schoolId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantActiveSchools", "RelativePath": "api/Merchant/{canteenId}/schools/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "SearchSchoolForMerchant", "RelativePath": "api/Merchant/{canteenId}/schools/unlinked/{search}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "search", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantUsers", "RelativePath": "api/Merchant/{canteenId}/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantAddUser", "RelativePath": "api/Merchant/{canteenId}/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Canteens.UpsertMerchantUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantGetUser", "RelativePath": "api/Merchant/{canteenId}/users/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantEditUser", "RelativePath": "api/Merchant/{canteenId}/users/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Canteens.UpsertMerchantUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantRemoveUser", "RelativePath": "api/Merchant/{canteenId}/users/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MerchantUsersSearch", "RelativePath": "api/Merchant/{canteenId}/users/search/{search}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "search", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "UpdateSchoolMerchantStatus", "RelativePath": "api/Merchant/{SchoolCanteenId}/update/status/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "SchoolCanteenId", "Type": "System.Int64", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "GetInvoices", "RelativePath": "api/Merchant/invoice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "ExportInvoiceData", "RelativePath": "api/Merchant/invoice/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Invoices.InvoiceExportRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "ExportInvoiceFile", "RelativePath": "api/Merchant/invoice/exportv2", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Invoices.InvoiceFileRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "GenerateInvoiceData", "RelativePath": "api/Merchant/invoice/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Invoices.InvoiceExportRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MoveMerchantFromStripeToSCore", "RelativePath": "api/Merchant/MoveMerchantFromStripeToSCore/{userId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "MoveParentFromStripeToSCore", "RelativePath": "api/Merchant/MoveParentFromStripeToSCore/{userId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.MerchantController", "Method": "SearchUserForCreation", "RelativePath": "api/Merchant/SearchUserForCreation/{search}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "DeleteNotice", "RelativePath": "api/Notice/{noticeId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "noticeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "CreateNotice", "RelativePath": "api/Notice/Create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Notices.CreateNoticeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "GetActiveSchoolNotices", "RelativePath": "api/Notice/GetActiveSchoolNotices/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "GetActiveSchoolNotices", "RelativePath": "api/Notice/GetActiveSchoolNoticesMobile/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "GetAllSchoolNotices", "RelativePath": "api/Notice/GetAllSchoolNotices/{merchantId}/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int32", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "GetAllNoticeWaitingForValidation", "RelativePath": "api/Notice/GetWaitingNotice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "UpdateNotice", "RelativePath": "api/Notice/Update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Notices.UpdateNoticeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NoticeController", "Method": "UpdateNoticeStatus", "RelativePath": "api/Notice/UpdateNoticeStatus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Notices.UpdateNoticeStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.NotificationController", "Method": "PlaceOrderNotification", "RelativePath": "api/Notification/PlaceOrderNotification/{orderStatus}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationRequest", "Type": "System.Collections.Generic.List`1[[Schools.BLL.Classes.OrderUpsertResponse, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}, {"Name": "orderStatus", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderById", "RelativePath": "api/Order/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderByIdWithCutOffTimes", "RelativePath": "api/Order/{orderId}/CutOffTimes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderViewByIdWithCutOffTimes", "RelativePath": "api/Order/{orderId}/CutOffTimes2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "OrderPickedUp", "RelativePath": "api/Order/{orderId}/pickedUp/{pickedUp}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "pickedUp", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "AdminCopyOrderItems", "RelativePath": "api/Order/AdminCopyOrderItems", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "FixUsersBalanceAfterPaymentOutage", "RelativePath": "api/Order/AdminFixUsersBalanceAfterPaymentOutage", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CancelOrder", "RelativePath": "api/Order/CancelOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderWithPayment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CancelOrder", "RelativePath": "api/Order/CancelOrder/{orderId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CancelOrder", "RelativePath": "api/Order/CancelOrderMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderWithPayment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "ChangeMenuTypeOrder", "RelativePath": "api/Order/ChangeMenuTypeOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "order", "Type": "Schools.DAL.DtosToMoveToBLL.Order", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "ChangeMenuTypeOrder", "RelativePath": "api/Order/ChangeMenuTypeOrder/{orderId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CheckIfOrderExist", "RelativePath": "api/Order/CheckIfOrderExist", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.CheckExistingOrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CreateOrders", "RelativePath": "api/Order/CreateOrders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.MultipleOrderRequests", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "CreateOrdersSummary", "RelativePath": "api/Order/CreateOrdersSummary", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.MultipleOrderRequests", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "EditOrder", "RelativePath": "api/Order/EditOrder", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderEditRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "EditOrderSummary", "RelativePath": "api/Order/EditOrderSummary", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderEditRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "EventOrderHistoryByParent", "RelativePath": "api/Order/EventOrderHistoryByParent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.EventUniformHistoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetDashboardOrders", "RelativePath": "api/Order/GetDashboardOrders/{date}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderByStudentOrderDateAndMenuType", "RelativePath": "api/Order/GetOrderByStudentOrderDateAndMenuType", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Orders.OrderByStudentOrderDateAndMenuTypeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderItemStudentList", "RelativePath": "api/Order/GetOrderItemStudentList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderListByParent", "RelativePath": "api/Order/GetOrderListByParent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderListByPayment", "RelativePath": "api/Order/GetOrderListByPayment/{paymentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderListBySchoolandMerchant", "RelativePath": "api/Order/GetOrderListBySchoolandMerchant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderListByStudent", "RelativePath": "api/Order/GetOrderListByStudent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderListByStudentMobile", "RelativePath": "api/Order/GetOrderListByStudentMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersByStudentWithFilters", "RelativePath": "api/Order/GetOrdersByStudentWithFilters/{studentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Schools.BLL.Classes.OrderFilter", "IsRequired": true}, {"Name": "studentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersByUserWithFilters", "RelativePath": "api/Order/GetOrdersByUserWithFilters/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Schools.DAL.DtosToMoveToBLL.ArrayFilter", "IsRequired": true}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersHistoryByParent", "RelativePath": "api/Order/GetOrdersHistoryByParent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersHistoryByParentMobile", "RelativePath": "api/Order/GetOrdersHistoryByParentMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryRequestMobile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersToPrintByGuid", "RelativePath": "api/Order/GetOrdersToPrintByGuid/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrderUniformListByStudent", "RelativePath": "api/Order/GetOrderUniformListByStudent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetRecentOrdersHistoryByParentMobile", "RelativePath": "api/Order/GetRecentOrdersHistoryByParentMobile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportClassItem", "RelativePath": "api/Order/GetReportClassItem", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportClassOrdersBySchoolV4", "RelativePath": "api/Order/GetReportClassOrdersBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersByMenuBySchool", "RelativePath": "api/Order/GetReportOrdersByMenuBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersPrintedCancelledBySchool", "RelativePath": "api/Order/GetReportOrdersPrintedCancelledBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersPrintedCancelledBySchool", "RelativePath": "api/Order/GetReportOrdersPrintedCancelledBySchool-V1", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersPrintedCancelledBySchoolV4", "RelativePath": "api/Order/GetReportOrdersPrintedCancelledBySchool-V4", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersUnprintedBySchool", "RelativePath": "api/Order/GetReportOrdersUnprintedBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersUnprintedBySchool", "RelativePath": "api/Order/GetReportOrdersUnprintedBySchool-V1", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportOrdersUnprintedBySchoolV4", "RelativePath": "api/Order/GetReportOrdersUnprintedBySchool-V4", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetReportSales", "RelativePath": "api/Order/GetReportSales", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.ReportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "MakePaymentAndMultipleOrdersMobile", "RelativePath": "api/Order/MakePaymentAndMultipleOrders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.MultipleOrdersWithPayment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "MakePaymentAndMultipleOrdersMobile", "RelativePath": "api/Order/MakePaymentAndMultipleOrdersMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.MultipleOrdersWithPayment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersByUserWithFilters2", "RelativePath": "api/Order/OrdersByUserWithFilters/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Schools.DAL.DtosToMoveToBLL.ArrayFilter", "IsRequired": true}, {"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetOrdersHistoryByParentMobile2", "RelativePath": "api/Order/OrdersHistoryByParentMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderHistoryRequestMobile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetRecentOrdersHistoryByParentMobile2", "RelativePath": "api/Order/RecentOrdersHistoryByParentMobile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "RefundFullOrder", "RelativePath": "api/Order/RefundFullOrder", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Orders.FullOrderRefundRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "RefundPartialOrder", "RelativePath": "api/Order/RefundPartialOrder", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderWithPayment", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "RefundPartialOrderV2", "RelativePath": "api/Order/RefundPartialOrderV2", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderEditRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "ReOrderInfo", "RelativePath": "api/Order/ReOrderInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.MultipleOrderRequests", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "RetryFailedPayments", "RelativePath": "api/Order/RetryFailedPayments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "SaveDraftOrders", "RelativePath": "api/Order/SaveDraftOrders/{logicAppIdentifier}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logicAppIdentifier", "Type": "System.String", "IsRequired": true}, {"Name": "orders", "Type": "Schools.BLL.Classes.MultipleOrders", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "SaveItemsOrder", "RelativePath": "api/Order/SaveItemsOrder/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "SetOrdersHasPrinted", "RelativePath": "api/Order/SetOrdersHasPrinted", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SetPrintedRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "SetOrdersToPrint", "RelativePath": "api/Order/SetOrdersToPrint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "SetOrderToPrintByOrderId", "RelativePath": "api/Order/SetOrderToPrintByOrderId/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "UniformOrderHistoryByParent", "RelativePath": "api/Order/UniformOrderHistoryByParent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.EventUniformHistoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "GetUniformOrEventsOrderHistoryByParent", "RelativePath": "api/Order/UniformOrEventsOrderHistoryByParent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.EventUniformHistoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "UpdateCanteenStatusOrderList", "RelativePath": "api/Order/UpdateCanteenStatusOrderList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Orders.UpdateOrderListCanteenStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "UpdateDraftOrders", "RelativePath": "api/Order/UpdateDraftOrders/{logicAppIdentifier}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logicAppIdentifier", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.Orders.UpdateDraftOrdersRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "UpdateFailedOrders", "RelativePath": "api/Order/UpdateFailedOrders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logicAppIdentifier", "Type": "System.String", "IsRequired": false}, {"Name": "request", "Type": "Schools.BLL.Classes.Orders.FailedOrdersRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderController", "Method": "ValidOrder", "RelativePath": "api/Order/ValidOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "order", "Type": "Schools.DAL.DtosToMoveToBLL.Order", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderStatusController", "Method": "Get", "RelativePath": "api/OrderStatus/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderStatusController", "Method": "GetDashboardErrors", "RelativePath": "api/OrderStatus/DashboardErrors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderStatusController", "Method": "CreateError", "RelativePath": "api/OrderStatus/Error", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Orders.CreateOrderErrorDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.OrderStatusController", "Method": "GetErrorsByOrderStatus", "RelativePath": "api/OrderStatus/Error/{orderStatusId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderStatusId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "CheckBalanceFromPayment", "RelativePath": "api/Pay/{paymentId}/CheckBalance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "AddPaymentMethod", "RelativePath": "api/Pay/AddPaymentMethod", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Payments.AddPaymentMethod", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "AddPaymentMethod1", "RelativePath": "api/Pay/AddPaymentMethod1", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "AddPaymentMethod", "RelativePath": "api/Pay/AddPaymentMethodMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Payments.AddPaymentMethod", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "AttachPaymentMethod", "RelativePath": "api/Pay/AttachPaymentMethod", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "CreateAndConfirmPaymentIntent", "RelativePath": "api/Pay/CreateAndConfirmPaymentIntent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "CreateCustomer", "RelativePath": "api/Pay/CreateCustomer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.ThirdParty.General.CreateCustomerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "DeletePaymentMethod", "RelativePath": "api/Pay/DeletePaymentMethod", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "DeletePaymentMethod", "RelativePath": "api/Pay/DeletePaymentMethod/{custId}/{methodId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "custId", "Type": "System.String", "IsRequired": true}, {"Name": "methodId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "DeletePaymentMethod", "RelativePath": "api/Pay/DeletePaymentMethod/{methodId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "methodId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "DeletePaymentMethod", "RelativePath": "api/Pay/DeletePaymentMethodMobile/{custId}/{methodId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "custId", "Type": "System.String", "IsRequired": true}, {"Name": "methodId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetBalance", "RelativePath": "api/Pay/GetBalance", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.ThirdParty.General.GetCustomerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetBalance", "RelativePath": "api/Pay/GetBalanceMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.ThirdParty.General.GetCustomerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetNewToken", "RelativePath": "api/Pay/GetNewToken/{externalUserId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetNewToken", "RelativePath": "api/Pay/GetNewToken/{externalUserId}/{braintreeFormat}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}, {"Name": "braintreeFormat", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetNewToken2", "RelativePath": "api/Pay/GetNewToken2/{amount}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "amount", "Type": "System.Decimal", "IsRequired": true}, {"Name": "fullTokenResponse", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetNewToken", "RelativePath": "api/Pay/GetNewTokenMobile/{externalUserId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetNewToken", "RelativePath": "api/Pay/GetNewTokenMobile/{externalUserId}/{braintreeFormat}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}, {"Name": "braintreeFormat", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetPaymentIntentToken", "RelativePath": "api/Pay/GetPaymentIntentToken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetPaymentMethods", "RelativePath": "api/Pay/GetPaymentMethods", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetPaymentMethods", "RelativePath": "api/Pay/GetPaymentMethods/{externalUserId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetPaymentMethods1", "RelativePath": "api/Pay/GetPaymentMethods1", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "GetPaymentMethods", "RelativePath": "api/Pay/GetPaymentMethodsMobile/{externalUserId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "externalUserId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "MakePayment", "RelativePath": "api/Pay/MakePayment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "Schools.BLL.Classes.Payments.PaymentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "OrdersPaymentLink", "RelativePath": "api/Pay/OrdersPaymentLink", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Payments.OrdersPaymentLinkRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "ReceiveStripeEvents", "RelativePath": "api/Pay/ReceiveStripeEvents", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "TopupAmount", "RelativePath": "api/Pay/TopupAmount", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.ThirdParty.General.TopupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.PayController", "Method": "TopupAmount", "RelativePath": "api/Pay/TopupAmountMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.ThirdParty.General.TopupRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "GetOrdersToPrintByGuid", "RelativePath": "api/Printing/GetOrdersToPrintByGuid/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "LabelTypes", "RelativePath": "api/Printing/LabelTypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetItemsToPrint", "RelativePath": "api/Printing/SetItemsToPrint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetItemToPrintByItemId", "RelativePath": "api/Printing/SetItemToPrintByItemId/{orderId}/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetOrdersHasPrinted", "RelativePath": "api/Printing/SetOrdersHasPrinted", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SetPrintedRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetOrdersHasPrintedWithRunNumber", "RelativePath": "api/Printing/SetOrdersHasPrintedWithRunNumber", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SetPrintedRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetOrdersToPrint", "RelativePath": "api/Printing/SetOrdersToPrint", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.OrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "SetOrderToPrintByOrderId", "RelativePath": "api/Printing/SetOrderToPrintByOrderId/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.PrintingController", "Method": "UpdateSchoolPrintOptions", "RelativePath": "api/Printing/UpdateSchoolPrintOptions", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolPrintOptionsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.ReconciliationController", "Method": "GetReconciliationRecord", "RelativePath": "api/Reconciliation/{UserId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.ReconciliationController", "Method": "ReconcileUser", "RelativePath": "api/Reconciliation/ReconcileUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.ThirdParty.General.ReconciliationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "Create", "RelativePath": "api/School", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "school", "Type": "Schools.BLL.Classes.CreateSchoolDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "EditSchool", "RelativePath": "api/School/{schoolId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}, {"Name": "school", "Type": "Schools.BLL.Classes.SchoolDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetMerchantsPerSchool", "RelativePath": "api/School/{schoolId}/Merchants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "CreateSchool", "RelativePath": "api/School/CreateSchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.CreateSchoolRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "DeleteSchoolDate", "RelativePath": "api/School/DeleteSchoolDate", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolDateId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetAllStates", "RelativePath": "api/School/GetAllStates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetCanteenList", "RelativePath": "api/School/GetCanteenList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetCanteenListByUserId", "RelativePath": "api/School/GetCanteenListByUserId/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetNutritionalValuesByState", "RelativePath": "api/School/GetNutritionalValuesByState/{stateId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stateId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchoolById", "RelativePath": "api/School/GetSchoolById/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchoolDatesById", "RelativePath": "api/School/GetSchoolDatesById/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchoolList", "RelativePath": "api/School/GetSchoolList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchools", "RelativePath": "api/School/GetSchools", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchools", "RelativePath": "api/School/GetSchoolsMobile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchoolsWithFilters", "RelativePath": "api/School/GetSchoolsWithFilters", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Schools.DAL.DtosToMoveToBLL.ArrayFilter", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetSchoolTermsById", "RelativePath": "api/School/GetSchoolTermsById/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "GetValidSchoolClosingDatesById", "RelativePath": "api/School/GetValidSchoolClosingDatesById/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "NotifyNotPrintedOrdersListByCanteen", "RelativePath": "api/School/NotifyNotPrintedOrdersListByCanteen", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "SaveAccountXero", "RelativePath": "api/School/SaveAccountXero/{code}/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}, {"Name": "canteenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "UpsertSchool", "RelativePath": "api/School/UpsertSchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "school", "Type": "Schools.DAL.DtosToMoveToBLL.School", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "UpsertSchoolDate", "RelativePath": "api/School/UpsertSchoolDate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolDate", "Type": "Schools.BLL.Classes.SchoolDate", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "UpsertSchoolOptions", "RelativePath": "api/School/UpsertSchoolOptions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "optionSchoolRequest", "Type": "Schools.DAL.DtosToMoveToBLL.OptionSchool", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolController", "Method": "UpsertSchoolTerm", "RelativePath": "api/School/UpsertSchoolTerm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolTerm", "Type": "Schools.BLL.Classes.SchoolTerm", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "UpdateClass", "RelativePath": "api/SchoolClass/{classId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "classId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.SchoolClassUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "ArchiveClass", "RelativePath": "api/SchoolClass/{classId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "classId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "GetClassList", "RelativePath": "api/SchoolClass/{schoolId}/classes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "ArchiveClass", "RelativePath": "api/SchoolClass/ArchiveClass", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolClassUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "CreateClass", "RelativePath": "api/SchoolClass/createClass", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolClass", "Type": "Schools.BLL.Classes.SchoolClassCreateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "GetClassList", "RelativePath": "api/SchoolClass/GetClassList", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.ClassRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "GetSchoolClassById", "RelativePath": "api/SchoolClass/GetSchoolClassById", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.ClassRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolClassController", "Method": "UpdateClass", "RelativePath": "api/SchoolClass/UpdateClass", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolClassUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetEvent", "RelativePath": "api/SchoolEvent/{eventId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetEventsById", "RelativePath": "api/SchoolEvent/GetEventsById/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetEventsBySchool", "RelativePath": "api/SchoolEvent/GetEventsBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolEventsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetEventsBySchoolMobile", "RelativePath": "api/SchoolEvent/GetEventsBySchoolMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolEventsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetPayAtCanteenBySchool", "RelativePath": "api/SchoolEvent/GetPayAtCanteenBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolEventsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventController", "Method": "GetUniformShopBySchool", "RelativePath": "api/SchoolEvent/GetUniformShopBySchool", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.SchoolEventsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "CreateEvent", "RelativePath": "api/SchoolEventManager", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SchoolEventCreateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventView", "RelativePath": "api/SchoolEventManager/{eventId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "EditEvent", "RelativePath": "api/SchoolEventManager/{eventId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Schools.BLL.Classes.SchoolEventUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "DeleteEvent", "RelativePath": "api/SchoolEventManager/{eventId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "CreateItemForEvent", "RelativePath": "api/SchoolEventManager/{eventId}/items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuItem", "Type": "MenuItem", "IsRequired": true}, {"Name": "eventId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventItems", "RelativePath": "api/SchoolEventManager/{eventId}/items", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "LinkItemToEvent", "RelativePath": "api/SchoolEventManager/{eventId}/items/{itemId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "RemoveItemFromEvent", "RelativePath": "api/SchoolEventManager/{eventId}/items/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int32", "IsRequired": true}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "PublisEvent", "RelativePath": "api/SchoolEventManager/{eventId}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "PublisEvent", "RelativePath": "api/SchoolEventManager/{eventId}/publish/{cancelOrders}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int64", "IsRequired": true}, {"Name": "cancelOrders", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "PublisEvent", "RelativePath": "api/SchoolEventManager/{eventId}/publish/Notify/{sendComms}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int64", "IsRequired": true}, {"Name": "sendComms", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventsBySchoolMerchantStudent", "RelativePath": "api/SchoolEventManager/GetEventsBySchoolMerchantStudent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SchoolEventMerchantStudentRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "UploadImage", "RelativePath": "api/SchoolEventManager/Image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "eventId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "DeleteImage", "RelativePath": "api/SchoolEventManager/Image/{eventId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "eventId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventsByMerchantAndSchool", "RelativePath": "api/SchoolEventManager/merchant/{merchantId}/school/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int32", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventsBySchool", "RelativePath": "api/SchoolEventManager/school/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolEventManagerController", "Method": "GetEventTemplate", "RelativePath": "api/SchoolEventManager/Templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolFeatureController", "Method": "GetFeaturesBySchool", "RelativePath": "api/SchoolFeature/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.SchoolFeatureController", "Method": "CreateFeature", "RelativePath": "api/SchoolFeature/{schoolId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "schoolId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "System.Collections.Generic.List`1[[Schools.BLL.Classes.SchoolFeature.SchoolFeatureDto, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StockController", "Method": "ArchiveStock", "RelativePath": "api/Stock/{stockId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "stockId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StockController", "Method": "GetRemainingQuantityStock", "RelativePath": "api/Stock/GetRemainingQuantityStock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.RemainingStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StockController", "Method": "GetRemainingQuantityStock", "RelativePath": "api/Stock/GetRemainingQuantityStockMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Stocks.RemainingStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StockController", "Method": "OrderUpdateStocks", "RelativePath": "api/Stock/OrderUpdateStocks/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StudentController", "Method": "ArchiveStudent", "RelativePath": "api/Student/ArchiveStudent/{studentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StudentController", "Method": "ArchiveStudent", "RelativePath": "api/Student/ArchiveStudentMobile/{studentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StudentController", "Method": "GetStudentList", "RelativePath": "api/Student/GetStudentList/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.StudentController", "Method": "GetStudentList", "RelativePath": "api/Student/GetStudentListMobile/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.CreateSchoolsUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.UpdateSchoolsUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "ArchiveParentAccount", "RelativePath": "api/User/ArchiveParentAccount", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Users.ArchiveUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetCustomClaims", "RelativePath": "api/User/GetCustomClaims/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetSpriggyAccountId", "RelativePath": "api/User/GetSpriggyAccountId/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetCurrentUser", "RelativePath": "api/User/GetUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.UserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetUsersDetails", "RelativePath": "api/User/GetUserDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetUsersByCanteen", "RelativePath": "api/User/GetUsersByCanteen/{canteenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "canteenId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetUsersDetails", "RelativePath": "api/User/GetUsersDetails/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetUsersList", "RelativePath": "api/User/GetUsersList/{schooldId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schooldId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "GetUsersWithFilters", "RelativePath": "api/User/GetUsersWithFilters", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Schools.DAL.DtosToMoveToBLL.ArrayFilter", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "PopulateSpriggyAccounts", "RelativePath": "api/User/PopulateSpriggyAccounts/{maxUsers}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "maxUsers", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "PrepareStripeCustomersForExistingUsers", "RelativePath": "api/User/PrepareStripeCustomersForExistingUsers/{maxUsers}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "maxUsers", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "RefundUser", "RelativePath": "api/User/RefundUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Orders.RefundUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "RegisterUser", "RelativePath": "api/User/RegisterUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "RegisterUser2", "RelativePath": "api/User/RegisterUser2", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "RegisterUser", "RelativePath": "api/User/RegisterUserMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "ShowReactNativeBuild", "RelativePath": "api/User/ShowReactNativeBuildMobile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "TransferUserBalance", "RelativePath": "api/User/TransferUserBalance", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Users.TransferUserBalanceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpdateCanteenUserSettings", "RelativePath": "api/User/UpdateCanteenUserSettings", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.CanteenUserSettingsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpdateCustomClaims", "RelativePath": "api/User/UpdateCustomClaims/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpdateSpriggyAccountId", "RelativePath": "api/User/UpdateSpriggyAccountId/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpdateStudentClass", "RelativePath": "api/User/UpdateStudentClass", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Schools.DAL.DtosToMoveToBLL.User", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpsertStudent", "RelativePath": "api/User/UpsertStudent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.Student", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpsertStudentMobile", "RelativePath": "api/User/UpsertStudentMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.Student", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpsertUser", "RelativePath": "api/User/UpsertUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Schools.DAL.DtosToMoveToBLL.User", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UpsertUserAudit", "RelativePath": "api/User/UpsertUserAudit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Users.UserAuditRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UserResetPasswordEmail", "RelativePath": "api/User/UserResetPasswordEmail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UserUpdateProfileMobile", "RelativePath": "api/User/UserUpdateProfile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.UserUpdateProfile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.UserController", "Method": "UserUpdateProfileMobile", "RelativePath": "api/User/UserUpdateProfileMobile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.DAL.DtosToMoveToBLL.UserUpdateProfile", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "AddFee", "RelativePath": "Fee/Add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.AddFeeUsedRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "AddFees", "RelativePath": "Fee/AddMany", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.AddFeeUsedRequest, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetFee", "RelativePath": "Fee/Get", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.FeesRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetFees", "RelativePath": "Fee/GetMany", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.FeesRequestNoMerchantId, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "GetManyFees", "RelativePath": "<PERSON><PERSON>/GetManyFees", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requests", "Type": "System.Collections.Generic.IEnumerable`1[[Schools.BLL.Classes.Fees.Requests.FeesRequest, Schools.BLL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeController", "Method": "UpdateFeeStatusByOrder", "RelativePath": "Fee/UpdateStatusByOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.UpdateFeeStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "AddFeeCalculator", "RelativePath": "FeeCalculator/AddFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.AddFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "AddSchoolsToFeeCalculator", "RelativePath": "FeeCalculator/AddSchoolsToFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.SchoolsFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetAll", "RelativePath": "FeeCalculator/GetAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetAllSchoolMappings", "RelativePath": "FeeCalculator/GetAllSchoolFeeMappings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculator", "RelativePath": "FeeCalculator/GetFeeCalculator/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculatorByMerchantAndSchool", "RelativePath": "FeeCalculator/GetFeeCalculatorByMerchantAndSchool/{merchantId}/{schoolId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetFeeCalculatorIdByName", "RelativePath": "FeeCalculator/GetFeeCalculatorIdByName/{feeCalculatorName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "GetSchoolsByFeeCalculator", "RelativePath": "FeeCalculator/GetSchoolsByFeeCalculator/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "HasActiveSchoolFeeConfiguration", "RelativePath": "FeeCalculator/HasActiveSchoolFeeConfiguration/{merchantId}/{schoolId}/{feeCalculatorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}, {"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "RemoveFeeCalculator", "RelativePath": "FeeCalculator/RemoveFeeCalculator/{feeCalculatorId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "RemoveSchoolFromFeeCalculator", "RelativePath": "FeeCalculator/RemoveSchoolFromFeeCalculator/{merchantId}/{schoolId}/{feeCalculatorId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "schoolId", "Type": "System.Int64", "IsRequired": true}, {"Name": "feeCalculatorId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Cashless.APIs.Controllers.FeeCalculatorController", "Method": "UpdateSchoolsToFeeCalculator", "RelativePath": "FeeCalculator/UpdateSchoolsToFeeCalculator", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Schools.BLL.Classes.Fees.Requests.SchoolsFeeCalculatorRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}]