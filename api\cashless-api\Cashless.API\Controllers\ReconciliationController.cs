using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.ThirdParty.General;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class ReconciliationController : ControllerBase
{
    private readonly ITelemetryService _telemetryService;
    private readonly IReconciliationService _reconciliationService;
    private readonly IReconciliationValidator _reconciliationValidator;
    private readonly ILogger<ReconciliationController> _logger;

    public ReconciliationController(ITelemetryService telemetryService, IReconciliationService reconciliationService, IReconciliationValidator reconciliationValidator, ILogger<ReconciliationController> logger)
    {
        _telemetryService = telemetryService;
        _reconciliationService = reconciliationService;
        _reconciliationValidator = reconciliationValidator;
        _logger = logger;
    }

    [Route("ReconcileUser")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> ReconcileUser([FromBody] ReconciliationRequest request)
    {
        ReconciliationResponse response;

        this._reconciliationValidator.ValidateReconciliationBetweenDatesRequest(request);

        if (request.StartDate != null)
        {
            response = await _reconciliationService.BeginReconcileBetweenDates(request);
        }
        else
        {
            response = await _reconciliationService.BeginReconcile(request);
        }

        return new OkObjectResult(response);
    }

    [Route("{UserId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> GetReconciliationRecord(long UserId)
    {
        var response = await _reconciliationService.GetReconciliationRecord(UserId);
        return new OkObjectResult(response);
    }
}