﻿using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using System;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SchoolEventManagerController : ControllerBase
    {
        private readonly ISchoolEventService _eventService;
        private readonly IMenuService _menuService;
        private readonly IAuthenticationValidator _authenticationValidator;
        private readonly IEditorValidator _editorValidator;

        public SchoolEventManagerController(ISchoolEventService eventService, IMenuService menuService, IAuthenticationValidator authenticationValidator, IEditorValidator editorValidator)
        {
            this._eventService = eventService;
            this._menuService = menuService;
            this._authenticationValidator = authenticationValidator;
            this._editorValidator = editorValidator;
        }

        /// <summary>
        /// Get event
        /// </summary>
        [Route("{eventId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetEventView(int eventId)
        {
            // get event
            var eventDto = await this._eventService.GetEventById(eventId);

            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(eventDto.SchoolId);

            EventViewDto res = new()
            {
                Event = eventDto,
                Items = await this._eventService.GetEventItemsView(Convert.ToInt32(eventDto.MenuId))
            };

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get all events(not archived) for the given school (admin only)
        /// </summary>
        [Route("school/{schoolId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetEventsBySchool(int schoolId)
        {
            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(schoolId);

            // get events (not archived)
            var events = await this._eventService.GetSchoolEventsBySchoolId(schoolId);

            return new OkObjectResult(events);
        }

        [Route("merchant/{merchantId}/school/{schoolId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetEventsByMerchantAndSchool(int merchantId, int schoolId)
        {
            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(schoolId);

            // get events (not archived)
            var events = await this._eventService.GetSchoolEventsByMerchantAndSchool(merchantId, schoolId);

            return new OkObjectResult(events);
        }

        /// <summary>
        /// Get all events(not archived) for the given school with the event orders for the given student
        /// Used for Walk-up orders
        /// </summary>
        [Route("GetEventsBySchoolMerchantStudent")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [HttpPost]
        public async Task<IActionResult> GetEventsBySchoolMerchantStudent([FromBody] SchoolEventMerchantStudentRequest request)
        {
            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(request.SchoolId);

            // get active events for school, merchant and student
            var events = await this._eventService.GetActiveSchoolEvents(request.SchoolId, request.MerchantId, request.StudentId);

            return new OkObjectResult(events);
        }

        /// <summary>
        /// Edit and existing event
        /// </summary>
        [Route("{eventId}")]
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> EditEvent(int eventId, [FromBody] SchoolEventUpdateRequest request)
        {
            if (eventId != request.SchoolEventId)
            {
                // given evenId doesn't match the request eventId
                return new BadRequestResult();
            }
            var res = await this._eventService.UpdateEvent(request);

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Create a new event
        /// </summary>
        [Route("")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> CreateEvent([FromBody] SchoolEventCreateRequest request)
        {
            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(request.SchoolId);

            var res = await this._eventService.CreateSchoolEvent(request);

            return new OkObjectResult(res);
        }

        [Route("{eventId}/publish")]
        [Route("{eventId}/publish/{cancelOrders}")]
        [Route("{eventId}/publish/Notify/{sendComms}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> PublisEvent(long eventId, bool cancelOrders = false, bool sendComms = false)
        {
            await this._eventService.PublishEvent(eventId, cancelOrders, sendComms);

            return new OkResult();
        }

        /// <summary>
        /// Archive event
        /// </summary>
        [Route("{eventId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteEvent(int eventId)
        {
            await this._eventService.ArchiveEvent(eventId);

            return new OkResult();
        }

        [Route("{eventId}/items/{itemId}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> LinkItemToEvent(int eventId, int itemId)
        {
            await this._eventService.LinkItemToEvent(eventId, itemId);

            return new OkResult();
        }

        [Route("{eventId}/items/{itemId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> RemoveItemFromEvent(int eventId, int itemId)
        {
            await this._eventService.UnlinkItemFromEvent(eventId, itemId);

            return new OkResult();
        }

        [Route("{eventId}/items")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> CreateItemForEvent([FromBody] MenuItem menuItem, int eventId)
        {
            await this._editorValidator.ValidateRequest(menuItem);

            var itemId = await this._menuService.UpsertMenuItem(menuItem);

            if (itemId <= 0)
            {
                throw new Exception("Item created Id is wrong");
            }

            await this._eventService.LinkItemToEvent(eventId, itemId);

            return new OkResult();
        }

        [Route("{eventId}/items")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetEventItems(int eventId)
        {
            // get event
            var eventDto = await this._eventService.GetEventById(eventId);

            // access validation
            await this._authenticationValidator.ValidateAccessToSchool(eventDto.SchoolId);

            var items = await this._eventService.GetEventItemsView(Convert.ToInt32(eventDto.MenuId));

            return new OkObjectResult(items);
        }

        [Route("Image")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UploadImage([FromForm(Name = "uploadedFile")] IFormFile file, long eventId)
        {
            if (file == null || file.Length == 0)
            {
                throw new Exception("No file");
            }

            string fileUrl = await this._eventService.UploadEventImage(file, eventId);

            return Content(fileUrl);
        }

        [Route("Image/{eventId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteImage(long eventId)
        {

            if (eventId <= 0)
            {
                throw new ValidationException("Wrong event Id");
            }

            await this._eventService.DeleteEventImage(eventId);

            return new OkResult();
        }

        [Route("Templates")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetEventTemplate()
        {
            var templates = await this._eventService.GetEventTemplates();

            return new OkObjectResult(templates);
        }
    }
}