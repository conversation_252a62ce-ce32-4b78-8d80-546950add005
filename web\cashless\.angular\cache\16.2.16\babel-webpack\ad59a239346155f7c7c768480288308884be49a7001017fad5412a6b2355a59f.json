{"ast": null, "code": "import _asyncToGenerator from \"D:/projects/spriggy/git-spriggy-latest/web/cashless/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\n// Ngrx\nimport { select } from '@ngrx/store';\nimport * as cartSelectors from '../../../states/shoppingCart/shopping-cart.selectors';\nimport * as cartActions from '../../../states/shoppingCart/shopping-cart.actions';\nimport { PosMessageType } from '../../models/pos-messages.interface';\n// Models\nimport { BaseComponent, MenuTypeEnum, ImageUrlEnum } from 'src/app/sharedModels';\nimport { environment } from 'src/environments/environment';\nimport * as moment from 'moment';\n// Components\nimport { PosPlaceOrderDialogComponent } from '../pos-place-order-dialog/pos-place-order-dialog.component';\nimport { StudentSearchDropdownComponent } from '../student-search-dropdown/student-search-dropdown.component';\nimport { ConvertToUniversalDateFormat } from 'src/app/utility';\nimport { GetCartItemsPrice } from '../../../manage-order/functions/calculate-price';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngrx/store\";\nimport * as i4 from \"src/app/sharedServices\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"../../services/pos-communication.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"../../../shared/components/spinner/spinner.component\";\nimport * as i15 from \"../../../shared/components/item/item.component\";\nimport * as i16 from \"../../../manage-order/components/category-tile/category-tile.component\";\nimport * as i17 from \"../student-search-dropdown/student-search-dropdown.component\";\nfunction PosTabComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21);\n  }\n}\nfunction PosTabComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"img\", 24);\n    i0.ɵɵlistener(\"error\", function PosTabComponent_div_1_Template_img_error_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onLogoImageError());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getLogoImageSrc(), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PosTabComponent_mat_card_7_mat_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const menuOption_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", menuOption_r23.menuType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", menuOption_r23.menuName, \" \");\n  }\n}\nfunction PosTabComponent_mat_card_7_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", day_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 2, day_r24, \"EEEE d MMM\"), \" \");\n  }\n}\nfunction PosTabComponent_mat_card_7_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\");\n    i0.ɵɵtext(6, \"Ask the child's favourite colour to confirm their identity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 39);\n    i0.ɵɵelement(8, \"div\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r22.studentFavoriteColor());\n  }\n}\nfunction PosTabComponent_mat_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 25)(1, \"mat-card-content\")(2, \"form\", 26)(3, \"div\", 3)(4, \"div\", 27)(5, \"student-search-dropdown\", 28, 29);\n    i0.ɵɵlistener(\"studentSelected\", function PosTabComponent_mat_card_7_Template_student_search_dropdown_studentSelected_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onStudentSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 27)(8, \"mat-form-field\", 30)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Ordering Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-select\", 31);\n    i0.ɵɵtemplate(12, PosTabComponent_mat_card_7_mat_option_12_Template, 2, 2, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 27)(14, \"mat-form-field\", 30)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Order Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 33);\n    i0.ɵɵtemplate(18, PosTabComponent_mat_card_7_mat_option_18_Template, 3, 5, \"mat-option\", 32);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(19, PosTabComponent_mat_card_7_div_19_Template, 9, 2, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.orderingForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"schoolId\", ctx_r2.schoolId);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.menuPickerData);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listDays);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showColorConfirmation() && !ctx_r2.IsGuest);\n  }\n}\nfunction PosTabComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"app-spinner\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"manual\", true);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"mat-card\", 45)(3, \"mat-card-content\", 46)(4, \"mat-icon\", 47);\n    i0.ɵɵtext(5, \"person_search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 48);\n    i0.ɵɵtext(7, \"Waiting for Student Selection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 49);\n    i0.ɵɵtext(9, \" Please select a student from the merchant view to begin ordering. \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction PosTabComponent_ng_template_9_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 3)(2, \"div\", 4)(3, \"mat-card\", 52)(4, \"mat-card-content\")(5, \"p\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r30.noMenuMessage);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_1_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PosTabComponent_ng_template_9_div_1_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 54);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const _r15 = i0.ɵɵreference(28);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r15);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PosTabComponent_ng_template_9_div_1_div_1_Template, 7, 1, \"div\", 50);\n    i0.ɵɵtemplate(2, PosTabComponent_ng_template_9_div_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 51, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r31 = i0.ɵɵreference(3);\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.noMenuMessage)(\"ngIfElse\", _r31);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 3)(2, \"div\", 4)(3, \"mat-card\", 52)(4, \"mat-card-content\")(5, \"p\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r34.noMenuMessage);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_2_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PosTabComponent_ng_template_9_div_2_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 54);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const _r15 = i0.ɵɵreference(28);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r15);\n  }\n}\nfunction PosTabComponent_ng_template_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PosTabComponent_ng_template_9_div_2_div_1_Template, 7, 1, \"div\", 50);\n    i0.ɵɵtemplate(2, PosTabComponent_ng_template_9_div_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 55, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r35 = i0.ɵɵreference(3);\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.noMenuMessage)(\"ngIfElse\", _r35);\n  }\n}\nfunction PosTabComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PosTabComponent_ng_template_9_div_0_Template, 10, 0, \"div\", 43);\n    i0.ɵɵtemplate(1, PosTabComponent_ng_template_9_div_1_Template, 4, 2, \"div\", 44);\n    i0.ɵɵtemplate(2, PosTabComponent_ng_template_9_div_2_Template, 4, 2, \"div\", 44);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.viewType === \"student\" && !ctx_r5.showMenuInStudentView());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.viewType === \"merchant\" && ctx_r5.selectedStudent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.viewType === \"student\" && ctx_r5.showMenuInStudentView());\n  }\n}\nfunction PosTabComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"sync\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Cart updating from merchant...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PosTabComponent_div_19_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Your cart is empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosTabComponent_div_19_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Merchant's cart is empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosTabComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtemplate(1, PosTabComponent_div_19_p_1_Template, 2, 0, \"p\", 44);\n    i0.ɵɵtemplate(2, PosTabComponent_div_19_p_2_Template, 2, 0, \"p\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.viewType === \"merchant\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.viewType === \"student\");\n  }\n}\nfunction PosTabComponent_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"mat-icon\", 63);\n    i0.ɵɵtext(2, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Loading balance...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PosTabComponent_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"account_balance_wallet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Remaining balance in parent wallet: \", i0.ɵɵpipeBind1(5, 1, ctx_r41.parentBalance()), \"\");\n  }\n}\nfunction PosTabComponent_div_20_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r42.balanceError());\n  }\n}\nfunction PosTabComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, PosTabComponent_div_20_div_1_Template, 5, 0, \"div\", 59);\n    i0.ɵɵtemplate(2, PosTabComponent_div_20_div_2_Template, 6, 3, \"div\", 60);\n    i0.ɵɵtemplate(3, PosTabComponent_div_20_div_3_Template, 5, 1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.balanceLoading());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.balanceLoading() && ctx_r8.parentBalance() !== null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.balanceLoading() && ctx_r8.balanceError());\n  }\n}\nfunction PosTabComponent_div_21_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r49 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r49.optionName, \" \");\n  }\n}\nfunction PosTabComponent_div_21_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r44 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"x\", item_r44.quantity, \"\");\n  }\n}\nfunction PosTabComponent_div_21_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_div_21_div_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const item_r44 = i0.ɵɵnextContext().$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.removeFromCart(item_r44.itemCartId));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PosTabComponent_div_21_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PosTabComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"h6\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 72);\n    i0.ɵɵtemplate(5, PosTabComponent_div_21_div_1_span_5_Template, 2, 1, \"span\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 74)(7, \"span\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, PosTabComponent_div_21_div_1_span_10_Template, 2, 1, \"span\", 76);\n    i0.ɵɵtemplate(11, PosTabComponent_div_21_div_1_button_11_Template, 3, 0, \"button\", 77);\n    i0.ɵɵtemplate(12, PosTabComponent_div_21_div_1_span_12_Template, 3, 0, \"span\", 78);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r44 = ctx.$implicit;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r44.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r44.selectedOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 6, item_r44.itemPriceIncGst));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r44.quantity > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.viewType === \"merchant\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.viewType === \"student\");\n  }\n}\nfunction PosTabComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, PosTabComponent_div_21_div_1_Template, 13, 8, \"div\", 67);\n    i0.ɵɵelementStart(2, \"div\", 68)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.shoppingCart);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Total: \", i0.ɵɵpipeBind1(5, 2, ctx_r9.priceCart()), \"\");\n  }\n}\nfunction PosTabComponent_mat_card_actions_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 91)(2, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_div_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.OrderClick());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"credit_card\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Pay with Spriggy Card (\", i0.ɵɵpipeBind1(7, 1, ctx_r54.priceCart()), \")\");\n  }\n}\nfunction PosTabComponent_mat_card_actions_22_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 91)(2, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_div_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.GuestOrderClick());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Pay with Card (\", i0.ɵɵpipeBind1(7, 1, ctx_r55.priceCart()), \")\");\n  }\n}\nfunction PosTabComponent_mat_card_actions_22_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Merchant will place order: \", i0.ɵɵpipeBind1(5, 1, ctx_r56.priceCart()), \"\");\n  }\n}\nfunction PosTabComponent_mat_card_actions_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card-actions\", 82)(1, \"div\", 83)(2, \"mat-card-title\", 84);\n    i0.ɵɵtext(3, \"Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PosTabComponent_mat_card_actions_22_div_4_Template, 8, 3, \"div\", 43);\n    i0.ɵɵtemplate(5, PosTabComponent_mat_card_actions_22_div_5_Template, 8, 3, \"div\", 43);\n    i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 85)(8, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.selectPaymentMethod(\"stripe\"));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Pay with Stripe\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 87)(14, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.selectPaymentMethod(\"cash\"));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"money\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Pay Cash\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"div\", 3)(20, \"div\", 85)(21, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.selectPaymentMethod(\"applepay\"));\n    });\n    i0.ɵɵelementStart(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"phone_iphone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Apple Pay\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 87)(27, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_mat_card_actions_22_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.selectPaymentMethod(\"visa\"));\n    });\n    i0.ɵɵelementStart(28, \"mat-icon\");\n    i0.ɵɵtext(29, \"credit_card\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Visa\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 3)(33, \"div\", 91)(34, \"button\", 92);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 3)(38, \"div\", 91);\n    i0.ɵɵtemplate(39, PosTabComponent_mat_card_actions_22_div_39_Template, 6, 3, \"div\", 93);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.IsGuest);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.IsGuest);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r10.selectedPaymentMethod() === \"stripe\" ? \"payment-btn secondary-payment selected\" : \"payment-btn secondary-payment\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMap(ctx_r10.selectedPaymentMethod() === \"cash\" ? \"payment-btn secondary-payment selected\" : \"payment-btn secondary-payment\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(ctx_r10.selectedPaymentMethod() === \"applepay\" ? \"payment-btn secondary-payment selected\" : \"payment-btn secondary-payment\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMap(ctx_r10.selectedPaymentMethod() === \"visa\" ? \"payment-btn secondary-payment selected\" : \"payment-btn secondary-payment\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" Place Order (\", i0.ɵɵpipeBind1(36, 12, ctx_r10.priceCart()), \") \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.viewType === \"student\");\n  }\n}\nfunction PosTabComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"account_balance_wallet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Remaining balance: \", i0.ɵɵpipeBind1(6, 1, ctx_r11.parentBalance()), \"\");\n  }\n}\nfunction PosTabComponent_div_24_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 104);\n    i0.ɵɵtext(1, \"Pay with Spriggy Card\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosTabComponent_div_24_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 104);\n    i0.ɵɵtext(1, \"Guest User - Cannot Order\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PosTabComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"div\", 101)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"credit_card\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, PosTabComponent_div_24_span_5_Template, 2, 0, \"span\", 102);\n    i0.ɵɵtemplate(6, PosTabComponent_div_24_span_6_Template, 2, 0, \"span\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_div_24_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r69);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.OrderClick());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"shopping_cart\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.IsGuest);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.IsGuest);\n  }\n}\nfunction PosTabComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 68);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 1, ctx_r13.priceCart()));\n  }\n}\nfunction PosTabComponent_div_26_div_5_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 115);\n    i0.ɵɵlistener(\"error\", function PosTabComponent_div_26_div_5_img_1_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r74);\n      const ctx_r73 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView($event.target.src = ctx_r73.defaultImagePath);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r71.getItemImageUrl(ctx_r71.studentPopupData().item.Images[0].ImageUrl), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r71.studentPopupData().item.Name);\n  }\n}\nfunction PosTabComponent_div_26_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r72 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r72.studentPopupData().item.Description, \" \");\n  }\n}\nfunction PosTabComponent_div_26_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵtemplate(1, PosTabComponent_div_26_div_5_img_1_Template, 1, 2, \"img\", 112);\n    i0.ɵɵelementStart(2, \"div\", 113)(3, \"div\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PosTabComponent_div_26_div_5_div_8_Template, 2, 1, \"div\", 114);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r70 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r70.studentPopupData().item.Images && ctx_r70.studentPopupData().item.Images.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r70.studentPopupData().item.Name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", i0.ɵɵpipeBind2(7, 4, ctx_r70.studentPopupData().item.Price, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r70.studentPopupData().item.Description);\n  }\n}\nfunction PosTabComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108)(2, \"div\", 109)(3, \"h3\");\n    i0.ɵɵtext(4, \"Item Selected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, PosTabComponent_div_26_div_5_Template, 9, 7, \"div\", 110);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.studentPopupData());\n  }\n}\nfunction PosTabComponent_ng_template_27_div_0_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 120)(1, \"category-tile\", 121);\n    i0.ɵɵlistener(\"click\", function PosTabComponent_ng_template_27_div_0_li_3_Template_category_tile_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r81);\n      const cat_r78 = restoredCtx.$implicit;\n      const ctx_r80 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r80.viewType === \"merchant\" ? ctx_r80.SetCategory(cat_r78) : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const cat_r78 = ctx.$implicit;\n    const i_r79 = ctx.index;\n    const ctx_r77 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"menu-category-\", i_r79, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"readonly-category\", ctx_r77.viewType === \"student\");\n    i0.ɵɵproperty(\"name\", cat_r78.CatName)(\"iconName\", cat_r78.CatUrl)(\"isSelected\", ctx_r77.IsCurrentCategory(cat_r78));\n  }\n}\nfunction PosTabComponent_ng_template_27_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 117)(2, \"ul\", 118);\n    i0.ɵɵtemplate(3, PosTabComponent_ng_template_27_div_0_li_3_Template, 2, 6, \"li\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r75.currentMenu.MenuJSON);\n  }\n}\nfunction PosTabComponent_ng_template_27_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"mat-card\", 127)(2, \"product-item\", 128);\n    i0.ɵɵlistener(\"clickItem\", function PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_clickItem_2_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r86 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r86.AddToCart($event));\n    })(\"itemDialogOpened\", function PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_itemDialogOpened_2_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r88 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r88.onItemDialogOpened($event));\n    })(\"itemDialogClosed\", function PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_itemDialogClosed_2_listener($event) {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r89 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r89.onItemDialogClosed($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r84 = ctx.$implicit;\n    const i_r85 = ctx.index;\n    const ctx_r82 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"category\", ctx_r82.currentCategoryToDisplay)(\"item\", item_r84)(\"dateOrder\", ctx_r82.selectedOrderDate)(\"currentMenuType\", ctx_r82.selectedMenuType)(\"schoolCutOffTime\", ctx_r82.menuCutOffTime)(\"id\", \"pos-product-item-\" + i_r85);\n  }\n}\nfunction PosTabComponent_ng_template_27_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"mat-card\", 129)(2, \"mat-card-content\")(3, \"p\", 130);\n    i0.ɵɵtext(4, \"No items available in this category\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PosTabComponent_ng_template_27_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 122)(3, \"h4\", 123);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵtemplate(6, PosTabComponent_ng_template_27_ng_container_1_div_6_Template, 3, 6, \"div\", 124);\n    i0.ɵɵtemplate(7, PosTabComponent_ng_template_27_ng_container_1_div_7_Template, 5, 0, \"div\", 125);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r76 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r76.currentCategoryToDisplay.CatName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r76.currentCategoryToDisplay.item);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r76.currentCategoryToDisplay.item && ctx_r76.currentCategoryToDisplay.item.length === 0);\n  }\n}\nfunction PosTabComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PosTabComponent_ng_template_27_div_0_Template, 4, 1, \"div\", 43);\n    i0.ɵɵtemplate(1, PosTabComponent_ng_template_27_ng_container_1_Template, 8, 3, \"ng-container\", 44);\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.selectedStudent && (ctx_r16.currentMenu == null ? null : ctx_r16.currentMenu.MenuJSON));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.selectedStudent && ctx_r16.currentCategoryToDisplay);\n  }\n}\nexport class PosTabComponent extends BaseComponent {\n  constructor(route, location, store, itemsFiltersService, orderApiService, debounceService, menuService, dialog, posCommunicationService, userService, authService) {\n    super();\n    this.route = route;\n    this.location = location;\n    this.store = store;\n    this.itemsFiltersService = itemsFiltersService;\n    this.orderApiService = orderApiService;\n    this.debounceService = debounceService;\n    this.menuService = menuService;\n    this.dialog = dialog;\n    this.posCommunicationService = posCommunicationService;\n    this.userService = userService;\n    this.authService = authService;\n    // Component state\n    this.selectedStudent = null;\n    this.selectedMenuType = MenuTypeEnum.Recess; // Default to recess\n    this.selectedOrderDate = new Date();\n    this.menuName = '';\n    this.menuLoading = signal(false);\n    this.GENERIC_ERROR_MESSAGE = 'Something went wrong, Please try again';\n    this.IsAdminOrMerchant = true; // Always true for POS\n    // Menu and display\n    this.titlePage = signal('POS - Point of Sale');\n    this.showMobilePlaceOrder = false;\n    this.priceCart = signal(0);\n    this.menuTypeEnum = MenuTypeEnum;\n    this.noMenuMessage = null;\n    this.shoppingCart = [];\n    // Form controls\n    this.menuPickerData = [];\n    this.listDays = [];\n    // Cross-tab communication\n    this.isConnectedToOtherTabs = signal(false);\n    this.lastSyncTime = signal(null);\n    this.showMenuInStudentView = signal(false);\n    // Image error handling\n    this.logoImageError = signal(false);\n    this.defaultImagePath = 'assets/images/spriggy-default-image.png';\n    // Student view popup state\n    this.showStudentPopup = signal(false);\n    this.studentPopupData = signal(null);\n    // Cart synchronization state\n    this.cartSyncInProgress = signal(false);\n    this.lastCartSyncTime = signal('');\n    // Balance state\n    this.parentBalance = signal(null);\n    this.balanceLoading = signal(false);\n    this.balanceError = signal(null);\n    // Favorite color confirmation state\n    this.showColorConfirmation = signal(false);\n    this.studentFavoriteColor = signal(null);\n    this.colorConfirmationPending = signal(false);\n    // Payment method selection - Only Spriggy is functional\n    this.selectedPaymentMethod = signal('spriggy');\n    this.showMobilePaymentSelector = signal(false);\n    this.paymentMethods = [{\n      id: 'spriggy',\n      name: 'Pay with Spriggy Card / Wallet',\n      icon: 'credit_card',\n      enabled: true,\n      primary: true\n    }\n    // Note: Other payment methods (Stripe, Cash, Apple Pay, Visa) are disabled\n    // as they are not functional in the current system. Only Spriggy payment\n    // works with the existing canteen/order/place API infrastructure.\n    ];\n\n    this.loadMenuDebounce = this.debounceService.callDebounce(this.refreshMenu, 350, false, true);\n    this.IsGuest = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.initializeFromRoute();\n      _this.setupSubscriptions();\n      _this.setupCrossTabCommunication();\n      _this.initializeForms();\n      _this.loadMenuNames();\n      _this.generateDatesList();\n      // Only merchant view should load guest user automatically\n      // Student view should wait for cross-tab communication from merchant view\n      if (_this.viewType === 'merchant') {\n        console.log(`[POS] Merchant view - loading default guest user`);\n        yield _this.checkAuthenticationAndLoadGuestUser();\n      } else {\n        console.log(`[POS] Student view - waiting for guest user selection from merchant view`);\n        // Student view will receive guest user via handleStudentSelectedMessage()\n      }\n    })();\n  }\n\n  ngOnDestroy() {\n    this.unsubscribeAll();\n  }\n  initializeFromRoute() {\n    this.route.queryParams.subscribe(params => {\n      this.schoolId = +params['schoolId'] || 52243;\n      this.guid = params['guid'];\n      this.viewType = params['viewType'] || 'merchant';\n    });\n  }\n  setupSubscriptions() {\n    // Shopping cart subscription\n    this.subscriptionShoppingCart$ = this.store.pipe(select(cartSelectors.getCartItems)).subscribe(cartItems => {\n      console.log(`[POS] Cart items updated:`, cartItems);\n      this.showMobilePlaceOrder = cartItems.length > 0;\n      this.priceCart.set(GetCartItemsPrice(cartItems));\n      this.shoppingCart = cartItems;\n      // Send cart update message to other tabs (only from merchant view)\n      if (this.viewType === 'merchant' && this.selectedStudent) {\n        this.sendCartUpdateMessage(cartItems);\n      }\n    });\n    // Filters subscription - simplified for POS\n    this.subscriptionItemsFilters$ = this.itemsFiltersService.filtersUpdatedEvent$.subscribe(() => {\n      this.FilterItems();\n    });\n  }\n  setupCrossTabCommunication() {\n    console.log(`[POS] Setting up cross-tab communication for ${this.viewType} view with GUID: ${this.guid}`);\n    // Subscribe to connection status\n    this.posCommunicationService.getConnectionStatus().subscribe(isConnected => {\n      this.isConnectedToOtherTabs.set(isConnected);\n      console.log(`[POS] Connection status changed: ${isConnected}`);\n    });\n    // Subscribe to messages for this view type\n    this.subscriptionCrossTabMessages$ = this.posCommunicationService.onMessageForViewType(this.viewType).subscribe(message => {\n      console.log(`[POS] Received message for ${this.viewType} view:`, message);\n      this.handleCrossTabMessage(message);\n    });\n  }\n  handleCrossTabMessage(message) {\n    // Ignore messages from the same tab\n    if (message.sourceGuid === this.guid) {\n      return;\n    }\n    this.lastSyncTime.set(new Date());\n    switch (message.type) {\n      case PosMessageType.ORDER_PLACED:\n        this.handleOrderPlacedMessage(message.payload);\n        break;\n      case PosMessageType.STUDENT_SELECTED:\n        this.handleStudentSelectedMessage(message.payload);\n        break;\n      case PosMessageType.MENU_CHANGED:\n        this.handleMenuChangedMessage(message.payload);\n        break;\n      case PosMessageType.CART_UPDATED:\n        this.handleCartUpdatedMessage(message.payload);\n        break;\n      case PosMessageType.CART_CLEARED:\n        this.handleCartClearedMessage(message.payload);\n        break;\n      case PosMessageType.VIEW_REFRESH:\n        this.handleViewRefreshMessage(message.payload);\n        break;\n      case PosMessageType.ITEM_POPUP_OPENED:\n        this.handleItemPopupOpenedMessage(message.payload);\n        break;\n      case PosMessageType.ITEM_POPUP_CLOSED:\n        this.handleItemPopupClosedMessage(message.payload);\n        break;\n      case PosMessageType.CATEGORY_CHANGED:\n        this.handleCategoryChangedMessage(message.payload);\n        break;\n      case PosMessageType.BALANCE_UPDATED:\n        this.handleBalanceUpdatedMessage(message.payload);\n        break;\n    }\n  }\n  initializeForms() {\n    this.orderingForm = new FormGroup({\n      menuType: new FormControl(this.selectedMenuType, [Validators.required]),\n      orderDate: new FormControl(this.selectedOrderDate, [Validators.required])\n    });\n    // Subscribe to form changes\n    this.orderingForm.get('menuType')?.valueChanges.subscribe(value => {\n      this.selectedMenuType = value;\n      this.loadMenuDebounce();\n      // Send menu change message to other tabs (only from merchant view)\n      if (this.viewType === 'merchant') {\n        this.sendMenuChangedMessage();\n      }\n    });\n    this.orderingForm.get('orderDate')?.valueChanges.subscribe(value => {\n      this.selectedOrderDate = value;\n      this.loadMenuDebounce();\n      // Send menu change message to other tabs (only from merchant view)\n      if (this.viewType === 'merchant') {\n        this.sendMenuChangedMessage();\n      }\n    });\n  }\n  onStudentSelected(student) {\n    console.log(`[POS] Student selected:`, student);\n    // Clear cart when changing students (only in merchant view)\n    if (this.viewType === 'merchant' && this.selectedStudent && student && this.selectedStudent.UserId !== student.UserId) {\n      console.log(`[POS] Clearing cart due to student change from ${this.selectedStudent.FirstName} to ${student.FirstName}`);\n      this.store.dispatch(cartActions.clearCart());\n      // Send cart cleared message to student view\n      this.sendCartClearedMessage('student_changed', this.selectedStudent.UserId, student.UserId);\n    }\n    this.selectedStudent = student;\n    if (student) {\n      // Set IsGuest property immediately based on student data\n      this.IsGuest = student.IsGuest || false;\n      console.log(`[POS] Student selected - IsGuest: ${this.IsGuest}`);\n      // Only Spriggy payment is supported - guest users cannot place orders\n      if (this.IsGuest) {\n        console.log(`[POS] Guest user selected - guest users cannot place orders in POS system`);\n        // Keep Spriggy selected but orders will be blocked for guest users\n      }\n      // Ensure Spriggy is always selected (only functional payment method)\n      this.selectedPaymentMethod.set('spriggy');\n      this.deactivatedFilters = student.SchoolDeactivatedFilters || null;\n      this.loadMenuDebounce();\n      // Call GetUsersDetails API first, then load balance (only for non-guest users)\n      if (!this.IsGuest) {}\n      this.loadUserDetailsAndBalance(student);\n      //  else {\n      //   // For guest users, clear balance information\n      //   this.parentBalance.set(null);\n      //   this.balanceLoading.set(false);\n      //   this.balanceError.set(null);\n      //   console.log(`[POS] Guest user selected - skipping balance loading`);\n      // }\n    } else {\n      // Clear menu, categories, filters, and cart when no student selected\n      this.currentMenu = null;\n      this.currentCategory = null;\n      this.currentCategoryToDisplay = null;\n      this.noMenuMessage = null;\n      this.parentBalance.set(null);\n      this.balanceError.set(null);\n      this.IsGuest = false;\n      // Clear color confirmation dialog\n      this.showColorConfirmation.set(false);\n      this.studentFavoriteColor.set(null);\n      this.colorConfirmationPending.set(false);\n      // Clear any active filter states\n      this.deactivatedFilters = null;\n      console.log(`[POS] No student selected - clearing all UI elements and filters`);\n      // Clear cart when no student is selected (only in merchant view)\n      if (this.viewType === 'merchant') {\n        console.log(`[POS] Clearing cart due to no student selected`);\n        this.store.dispatch(cartActions.clearCart());\n        // Send cart cleared message to student view\n        this.sendCartClearedMessage('no_student_selected');\n      }\n    }\n    // Send student selection message to other tabs (only from merchant view)\n    if (this.viewType === 'merchant') {\n      this.sendStudentSelectedMessage(student);\n    }\n  }\n  loadMenuNames() {\n    this.menuService.GetMenuNamesList(this.schoolId).subscribe({\n      next: menuList => {\n        this.processMenuData(menuList);\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n      }\n    });\n  }\n  processMenuData(menuList) {\n    this.menuPickerData = [];\n    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Recess));\n    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Lunch));\n    // Set default menu name\n    const defaultMenu = this.menuPickerData.find(m => m.menuType === this.selectedMenuType);\n    if (defaultMenu) {\n      this.menuName = defaultMenu.menuName;\n    }\n  }\n  getMenuData(menuList, menuType) {\n    const menuIndex = this.getMenuIndex(menuList, menuType);\n    return {\n      menuType: menuType,\n      menuName: menuIndex >= 0 ? menuList[menuIndex].friendlyName : menuType\n    };\n  }\n  getMenuIndex(menuList, menuType) {\n    if (!menuList?.length || !menuType) return -1;\n    return menuList?.findIndex(menu => menu.menuType === menuType);\n  }\n  generateDatesList() {\n    this.listDays = [];\n    const today = new Date();\n    // Generate next 14 days\n    for (let i = 0; i < 14; i++) {\n      const date = new Date(today);\n      date.setDate(today.getDate() + i);\n      this.listDays.push(date);\n    }\n  }\n  refreshMenu() {\n    if (!this.selectedMenuType || !this.selectedStudent || !this.selectedOrderDate) {\n      return;\n    }\n    this.menuLoading.set(true);\n    this.currentMenu = null;\n    if (this.IsGuest) {\n      this.loadMenu();\n      return;\n    }\n    this.canteenMenuAvailableCheck();\n  }\n  canteenMenuAvailableCheck() {\n    const request = {\n      studentId: this.selectedStudent.UserId,\n      orderDate: ConvertToUniversalDateFormat(this.selectedOrderDate),\n      menuType: this.selectedMenuType\n    };\n    this.orderApiService.GetOrderByStudentOrderDateAndMenuType(request).subscribe({\n      next: res => {\n        this.processPreMenuCheck(res);\n      },\n      error: error => {\n        this.menuLoading.set(false);\n        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n        this.handleErrorFromService(error);\n      }\n    });\n  }\n  processPreMenuCheck(res) {\n    if (!res) {\n      this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n      this.menuLoading.set(false);\n      return;\n    }\n    if (this.isSchoolClosedForCanteenOrders(res)) {\n      this.setNoMenuMessage(`Sorry, the canteen's closed right now`);\n      return;\n    }\n    if (this.orderAlreadyPlaced(res)) {\n      const message = `You have already placed an Order for: ${this.menuName} - ${this.getFormattedDate()}`;\n      this.setNoMenuMessage(message);\n      return;\n    }\n    this.loadMenu();\n  }\n  isSchoolClosedForCanteenOrders(res) {\n    return res?.isSchoolClosed;\n  }\n  orderAlreadyPlaced(res) {\n    return Boolean(res?.order);\n  }\n  setNoMenuMessage(message) {\n    this.noMenuMessage = message;\n    this.menuLoading.set(false);\n  }\n  getFormattedDate() {\n    return moment(this.selectedOrderDate).format('dddd DD/MM');\n  }\n  loadMenu() {\n    if (!this.selectedMenuType || !this.selectedStudent) {\n      return;\n    }\n    this.menuService.GetMenuBySchoolAndType(this.selectedStudent.SchoolId, this.selectedMenuType).subscribe({\n      next: res => {\n        this.menuLoading.set(false);\n        const menuToDisplay = this.menuDataExists(res) ? this.getMenuToDisplay(res) : null;\n        if (menuToDisplay && menuToDisplay?.MenuJSON) {\n          this.processMenuResult(menuToDisplay);\n          return;\n        }\n        this.noMenuMessage = `No ${this.menuName} Menu Available`;\n      },\n      error: error => {\n        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n        this.menuLoading.set(false);\n        this.handleErrorFromService(error);\n      }\n    });\n  }\n  menuDataExists(res) {\n    return res && res.length > 0;\n  }\n  getMenuToDisplay(res) {\n    return res[0];\n  }\n  processMenuResult(menuResult) {\n    this.noMenuMessage = null;\n    this.currentMenu = menuResult;\n    // Set menuId and menuName from the loaded menu\n    this.menuId = this.currentMenu.MenuId;\n    this.menuName = this.currentMenu.Name;\n    this.menuCutOffTime = this.getMenuCutOffTime();\n    console.log(`[POS] Menu loaded - ID: ${this.menuId}, Name: ${this.menuName}`);\n    // Automatically select the first category\n    if (this.currentMenu.MenuJSON && this.currentMenu.MenuJSON.length > 0) {\n      this.SetCategory(this.currentMenu.MenuJSON[0]);\n    }\n  }\n  getMenuCutOffTime() {\n    // Implementation for cut off time logic\n    return '';\n  }\n  SetCategory(category) {\n    this.currentCategory = category;\n    this.currentCategoryToDisplay = category;\n    this.FilterItems();\n    // Send category change message to other tabs (only from merchant view)\n    if (this.viewType === 'merchant') {\n      this.sendCategoryChangedMessage(category);\n    }\n  }\n  IsCurrentCategory(cat) {\n    return this.currentCategory?.CatName == cat.CatName;\n  }\n  FilterItems() {\n    if (!this.currentCategory) {\n      return;\n    }\n    // Apply filters to items - simplified for POS\n    this.currentCategoryToDisplay = {\n      ...this.currentCategory\n    };\n  }\n  AddToCart(item) {\n    console.log(`[POS] Adding item to cart in ${this.viewType} view:`, item);\n    try {\n      // Convert RefinedOrderItem to CartItem\n      const cartItem = this.convertOrderItemToCartItem(item);\n      console.log(`[POS] Converted cart item:`, cartItem);\n      this.store.dispatch(cartActions.addToCart({\n        cartItem\n      }));\n      console.log(`[POS] Cart action dispatched successfully`);\n    } catch (error) {\n      console.error(`[POS] Error adding item to cart:`, error);\n    }\n  }\n  // Handle item dialog events for cross-tab communication\n  onItemDialogOpened(event) {\n    if (this.viewType === 'merchant') {\n      console.log(`[POS] Item dialog opened in merchant view:`, event);\n      this.sendItemPopupOpenedMessage(event.item, event.category);\n    }\n  }\n  onItemDialogClosed(event) {\n    if (this.viewType === 'merchant') {\n      console.log(`[POS] Item dialog closed in merchant view:`, event);\n      this.sendItemPopupClosedMessage(event.itemAdded, event.item);\n    }\n  }\n  removeFromCart(itemCartId) {\n    console.log(`[POS] Removing item from cart: ${itemCartId}`);\n    this.store.dispatch(cartActions.removeItem({\n      itemCartId\n    }));\n    // Cart update message will be sent automatically via the cart subscription\n  }\n  // Convert RefinedOrderItem to CartItem for POS system\n  convertOrderItemToCartItem(item) {\n    if (!this.selectedStudent) {\n      throw new Error('No student selected for cart item');\n    }\n    const options = item?.SelectedOptions?.map(option => {\n      return {\n        menuItemOptionId: option.MenuItemOptionId,\n        optionName: option.OptionName,\n        optionCost: option.OptionCost,\n        parentOptionId: option.MenuItemOptionsCategoryId\n      };\n    }) || [];\n    const cartItem = {\n      date: this.selectedOrderDate,\n      studentId: this.selectedStudent.UserId,\n      studentName: this.selectedStudent.FirstName,\n      schoolId: this.selectedStudent.SchoolId,\n      menuType: this.selectedMenuType,\n      menuName: this.menuName,\n      menuId: this.menuId,\n      menuCutOffDateTime: this.menuCutOffTime,\n      canteenId: this.currentMenu?.CanteenId || 0,\n      itemCartId: moment().unix(),\n      menuItemId: item.MenuItemId,\n      name: item.Name,\n      itemPriceIncGst: item.ItemPriceIncGst,\n      selectedOptions: options,\n      quantity: item.Quantity\n    };\n    console.log(`[POS] Created cart item with menuId: ${cartItem.menuId}, menuName: ${cartItem.menuName}`);\n    return cartItem;\n  }\n  // Payment method selection\n  selectPaymentMethod(methodId) {\n    this.selectedPaymentMethod.set(methodId);\n    this.showMobilePaymentSelector.set(false); // Close mobile selector if open\n    console.log(`[POS] Payment method selected: ${methodId}`);\n  }\n  // Mobile payment selector methods\n  openMobilePaymentSelector() {\n    this.showMobilePaymentSelector.set(true);\n  }\n  closeMobilePaymentSelector() {\n    this.showMobilePaymentSelector.set(false);\n  }\n  // Place order with Spriggy payment (only functional payment method)\n  OrderClick() {\n    // Validate prerequisites\n    if (!this.validateOrderPrerequisites()) {\n      return;\n    }\n    // Only Spriggy payment is supported in POS\n    if (this.selectedPaymentMethod() !== 'spriggy') {\n      alert('Only Spriggy payment is currently supported in the POS system.');\n      return;\n    }\n    // Use POS-specific place order dialog\n    const groupedCarts = [this.shoppingCart];\n    const dialogRef = this.dialog.open(PosPlaceOrderDialogComponent, {\n      width: '600px',\n      disableClose: true,\n      data: {\n        groupedCarts: groupedCarts,\n        editOrderId: null,\n        // POS-specific properties\n        viewType: this.viewType,\n        guid: this.guid,\n        selectedStudent: this.selectedStudent,\n        selectedMenuType: this.selectedMenuType,\n        selectedOrderDate: this.selectedOrderDate\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.success) {\n        console.log(`[POS] Order placed successfully with ID: ${result.orderId}`);\n        // Refresh menu to update availability\n        this.refreshMenu();\n        // Clear cart after successful order (merchant view only)\n        if (this.viewType === 'merchant') {\n          this.store.dispatch(cartActions.clearCart());\n          // Send cart cleared message to student view\n          this.sendCartClearedMessage('manual_clear');\n        }\n        // Order placed message is sent from within the dialog component\n        console.log(`[POS] Order placement workflow completed successfully`);\n        // Revert to default guest user after order completion (merchant view only)\n        if (this.viewType === 'merchant') {\n          this.revertToGuestUserAfterOrder();\n        }\n      } else if (result === true) {\n        // Handle error case\n        console.error(`[POS] Order placement failed`);\n      }\n    });\n  }\n  validateOrderPrerequisites() {\n    // Check if student is selected\n    if (!this.selectedStudent) {\n      alert('Please select a student before placing an order.');\n      return false;\n    }\n    // Check if cart has items\n    if (!this.shoppingCart || this.shoppingCart.length === 0) {\n      alert('Your cart is empty. Please add items before placing an order.');\n      return false;\n    }\n    // Check if menu type and date are selected\n    if (!this.selectedMenuType || !this.selectedOrderDate) {\n      alert('Please select a menu type and order date.');\n      return false;\n    }\n    // Note: Guest users are allowed to place orders in POS system\n    // Payment method restrictions are handled in the payment dialog\n    return true;\n  }\n  GoBackClick() {\n    this.location.back();\n  }\n  // Helper method to determine if menu should be shown in student view\n  shouldShowMenuInStudentView() {\n    return this.viewType === 'student' && this.showMenuInStudentView();\n  }\n  // Image error handling\n  onLogoImageError() {\n    this.logoImageError.set(true);\n  }\n  getLogoImageSrc() {\n    return this.logoImageError() ? this.defaultImagePath : 'assets/images/spriggy_schools_logo.png';\n  }\n  getItemImageUrl(imageUrl) {\n    if (!imageUrl) return this.defaultImagePath;\n    if (imageUrl.indexOf('https:') > -1) {\n      return imageUrl;\n    } else {\n      return environment.blobStorage + ImageUrlEnum.ItemsLG + imageUrl;\n    }\n  }\n  // Cross-tab communication message senders\n  sendStudentSelectedMessage(student) {\n    const payload = {\n      student\n    };\n    console.log(`[POS] Merchant view sending student selected message to student view:`, {\n      studentName: student ? `${student.FirstName} ${student.Lastname}` : 'None',\n      isGuest: student?.IsGuest || false,\n      studentId: student?.UserId,\n      guid: this.guid\n    });\n    this.posCommunicationService.sendStudentSelected(payload, this.guid);\n    console.log(`[POS] Student selected message sent successfully`);\n  }\n  sendGuestUserReversionMessage(guestUser) {\n    console.log(`[POS] Sending guest user reversion message after order completion:`, {\n      studentName: `${guestUser.FirstName} ${guestUser.Lastname}`,\n      isGuest: guestUser.IsGuest,\n      studentId: guestUser.UserId,\n      reason: 'order_completed_reversion_to_guest'\n    });\n    // Use the same student selected message but with additional context for guest reversion\n    const payload = {\n      student: guestUser\n    };\n    this.posCommunicationService.sendStudentSelected(payload, this.guid);\n    console.log(`[POS] Guest user reversion message sent to student view`);\n  }\n  sendCartUpdateMessage(cartItems) {\n    const payload = {\n      itemCount: cartItems.length,\n      totalAmount: GetCartItemsPrice(cartItems),\n      studentId: this.selectedStudent?.UserId,\n      cartItems: cartItems,\n      timestamp: new Date().toISOString()\n    };\n    console.log(`[POS] Sending cart update message with ${cartItems.length} items:`, payload);\n    this.posCommunicationService.sendCartUpdated(payload, this.guid);\n  }\n  sendMenuChangedMessage() {\n    const payload = {\n      menuType: this.selectedMenuType,\n      orderDate: this.selectedOrderDate,\n      studentId: this.selectedStudent?.UserId,\n      menuName: this.menuName\n    };\n    this.posCommunicationService.sendMenuChanged(payload, this.guid);\n  }\n  sendOrderPlacedMessage(orderId, paymentMethod) {\n    if (!this.selectedStudent) return;\n    const payload = {\n      orderId,\n      studentId: this.selectedStudent.UserId,\n      studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,\n      menuType: this.selectedMenuType,\n      orderDate: this.selectedOrderDate.toISOString(),\n      totalAmount: this.priceCart(),\n      itemCount: this.shoppingCart.length,\n      paymentMethod: paymentMethod || this.selectedPaymentMethod()\n    };\n    this.posCommunicationService.sendOrderPlaced(payload, this.guid);\n  }\n  sendItemPopupOpenedMessage(item, category) {\n    const payload = {\n      item,\n      date: this.selectedOrderDate,\n      category\n    };\n    console.log(`[POS] Sending item popup opened message:`, payload);\n    this.posCommunicationService.sendItemPopupOpened(payload, this.guid);\n  }\n  sendItemPopupClosedMessage(itemAdded, item) {\n    const payload = {\n      itemAdded,\n      item\n    };\n    console.log(`[POS] Sending item popup closed message:`, payload);\n    this.posCommunicationService.sendItemPopupClosed(payload, this.guid);\n  }\n  sendCategoryChangedMessage(category) {\n    const payload = {\n      category,\n      categoryId: category.CategoryId,\n      categoryName: category.CatName\n    };\n    console.log(`[POS] Sending category changed message:`, payload);\n    this.posCommunicationService.sendCategoryChanged(payload, this.guid);\n  }\n  sendBalanceUpdatedMessage(studentId, balance, studentName, favoriteColor, isGuest) {\n    const payload = {\n      studentId,\n      balance,\n      studentName,\n      favoriteColor,\n      isGuest\n    };\n    console.log(`[POS] Sending balance updated message:`, payload);\n    this.posCommunicationService.sendBalanceUpdated(payload, this.guid);\n  }\n  sendCartClearedMessage(reason, previousStudentId, newStudentId) {\n    const payload = {\n      reason,\n      previousStudentId,\n      newStudentId\n    };\n    console.log(`[POS] Sending cart cleared message:`, payload);\n    this.posCommunicationService.sendCartCleared(payload, this.guid);\n  }\n  // Cross-tab communication message handlers\n  handleOrderPlacedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Order placed notification received in student view for ${payload.studentName}: $${payload.totalAmount}`);\n      // Refresh menu if it's for the same student and menu type\n      if (this.selectedStudent?.UserId === payload.studentId && this.selectedMenuType === payload.menuType) {\n        console.log(`[POS] Refreshing menu in student view after order completion`);\n        this.refreshMenu();\n      }\n      // Clear any order-related UI state in student view\n      this.clearOrderRelatedState();\n      console.log(`[POS] ✓ Student view updated after order completion`);\n    }\n  }\n  /**\r\n   * Clear order-related state in student view after order completion\r\n   */\n  clearOrderRelatedState() {\n    // Clear any color confirmation dialogs\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n    // Reset any temporary UI states\n    this.cartSyncInProgress.set(false);\n    console.log(`[POS] Order-related state cleared in student view`);\n  }\n  handleStudentSelectedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Student view received student selected message:`, {\n        studentName: payload.student ? `${payload.student.FirstName} ${payload.student.Lastname}` : 'None',\n        isGuest: payload.student?.IsGuest || false,\n        studentId: payload.student?.UserId,\n        currentStudent: this.selectedStudent ? `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}` : 'None'\n      });\n      if (payload.student) {\n        // Clear cart display when changing students in student view\n        if (this.selectedStudent && this.selectedStudent.UserId !== payload.student.UserId) {\n          console.log(`[POS] Clearing student view cart due to student change from ${this.selectedStudent.FirstName} to ${payload.student.FirstName}`);\n          this.shoppingCart = [];\n          this.priceCart.set(0);\n          this.showMobilePlaceOrder = false;\n        }\n        // Set the selected student in student view\n        this.selectedStudent = payload.student;\n        this.deactivatedFilters = payload.student.SchoolDeactivatedFilters || null;\n        // Set IsGuest property in student view\n        this.IsGuest = payload.student.IsGuest || false;\n        console.log(`[POS] Student selected in student view - IsGuest: ${this.IsGuest}, Name: ${payload.student.FirstName} ${payload.student.Lastname}`);\n        // Show menu content in student view\n        this.showMenuInStudentView.set(true);\n        console.log(`[POS] Menu content shown in student view`);\n        // Load menu for the selected student\n        this.loadMenuDebounce();\n        console.log(`[POS] Menu loading triggered for selected student in student view`);\n        console.log(`[POS] ✓ Student successfully synchronized in student view: ${payload.student.FirstName} ${payload.student.Lastname}`);\n      } else {\n        console.log(`[POS] No student selected - clearing student view UI`);\n        // Hide menu content and clear all UI elements if no student selected\n        this.showMenuInStudentView.set(false);\n        this.selectedStudent = null;\n        this.currentMenu = null;\n        this.currentCategory = null;\n        this.currentCategoryToDisplay = null;\n        this.IsGuest = false;\n        // Clear color confirmation dialog\n        this.showColorConfirmation.set(false);\n        this.studentFavoriteColor.set(null);\n        this.colorConfirmationPending.set(false);\n        // Clear balance information\n        this.parentBalance.set(null);\n        this.balanceLoading.set(false);\n        this.balanceError.set(null);\n        // Clear any active filter states\n        this.deactivatedFilters = null;\n        // Clear cart display when no student is selected\n        this.shoppingCart = [];\n        this.priceCart.set(0);\n        this.showMobilePlaceOrder = false;\n        console.log('[POS] ✓ Student view cleared - no student selected');\n      }\n    }\n  }\n  handleMenuChangedMessage(payload) {\n    if (this.viewType === 'student') {\n      // Update menu type and date in student view\n      if (this.selectedMenuType !== payload.menuType || this.selectedOrderDate.getTime() !== payload.orderDate.getTime()) {\n        this.selectedMenuType = payload.menuType;\n        this.selectedOrderDate = payload.orderDate;\n        this.orderingForm.patchValue({\n          menuType: payload.menuType,\n          orderDate: payload.orderDate\n        });\n        console.log(`Menu changed to: ${payload.menuType} for ${payload.orderDate}`);\n      }\n    }\n  }\n  handleCartUpdatedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Cart updated in student view: ${payload.itemCount} items, total: $${payload.totalAmount}`);\n      console.log(`[POS] Received cart items:`, payload.cartItems);\n      // Show sync in progress\n      this.cartSyncInProgress.set(true);\n      // Update student view cart display with merchant's cart items\n      this.shoppingCart = payload.cartItems || [];\n      this.priceCart.set(payload.totalAmount);\n      this.showMobilePlaceOrder = payload.itemCount > 0;\n      this.lastCartSyncTime.set(new Date().toLocaleTimeString());\n      console.log(`[POS] Student view cart synchronized with ${this.shoppingCart.length} items`);\n      // Hide sync indicator after a brief moment\n      setTimeout(() => {\n        this.cartSyncInProgress.set(false);\n      }, 1000);\n    }\n  }\n  handleViewRefreshMessage(payload) {\n    console.log(`View refresh requested: ${payload.reason}`);\n    // Refresh current view based on reason\n    if (payload.reason === 'order_completed') {\n      this.refreshMenu();\n    }\n  }\n  handleItemPopupOpenedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Item popup opened in student view:`, payload.item.Name);\n      this.studentPopupData.set(payload);\n      this.showStudentPopup.set(true);\n    }\n  }\n  handleItemPopupClosedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Item popup closed in student view. Item added:`, payload.itemAdded);\n      this.showStudentPopup.set(false);\n      this.studentPopupData.set(null);\n    }\n  }\n  handleCategoryChangedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Category changed in student view:`, payload.categoryName);\n      // Find the category in the current menu and set it\n      if (this.currentMenu?.MenuJSON) {\n        const category = this.currentMenu.MenuJSON.find(cat => cat.CategoryId === payload.categoryId);\n        if (category) {\n          this.SetCategory(category);\n          console.log(`[POS] Student view switched to category: ${payload.categoryName}`);\n        }\n      }\n    }\n  }\n  handleBalanceUpdatedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Balance updated in student view for student ${payload.studentName}: $${payload.balance}, IsGuest: ${payload.isGuest}`);\n      // Update balance information in student view\n      if (this.selectedStudent?.UserId === payload.studentId) {\n        this.parentBalance.set(payload.balance);\n        this.balanceLoading.set(false);\n        this.balanceError.set(null);\n        // Update guest status\n        if (payload.isGuest !== undefined) {\n          this.IsGuest = payload.isGuest;\n        }\n        // Update favorite color if provided\n        if (payload.favoriteColor) {\n          this.studentFavoriteColor.set(payload.favoriteColor);\n        }\n        console.log(`[POS] Student view balance synchronized: $${payload.balance}, IsGuest: ${this.IsGuest}`);\n      }\n    }\n  }\n  handleCartClearedMessage(payload) {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Cart cleared in student view. Reason: ${payload.reason}`);\n      // Clear cart display in student view\n      this.shoppingCart = [];\n      this.priceCart.set(0);\n      this.showMobilePlaceOrder = false;\n      console.log(`[POS] Student view cart cleared due to: ${payload.reason}`);\n    }\n  }\n  checkAuthenticationAndLoadGuestUser() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const isAuthenticated = yield _this2.authService.IsAuthenticated();\n        const connectedUser = _this2.userService.GetUserConnected();\n        if (!isAuthenticated || !connectedUser) {\n          console.error(`[POS] User not authenticated. Cannot load guest user or access balance API.`);\n          _this2.balanceError.set('Authentication required. Please log in to access POS features.');\n          return;\n        }\n        console.log(`[POS] User authenticated:`, connectedUser.Email, `Role:`, connectedUser.Role);\n        console.log(`[POS] Auth token available:`, !!_this2.authService.GetToken());\n        // Only load guest user if authenticated\n        _this2.loadDefaultGuestUser();\n      } catch (error) {\n        console.error(`[POS] Authentication check failed:`, error);\n        _this2.balanceError.set('Authentication check failed. Please refresh and try again.');\n      }\n    })();\n  }\n  loadDefaultGuestUser() {\n    console.log(`[POS] Loading default guest user...`);\n    // Search for guest user using the existing search functionality\n    const filters = {\n      Filter: 'guest',\n      NumberRows: 10,\n      PageIndex: 0,\n      FilterId: 0,\n      SortBy: '',\n      SortDirection: '',\n      MultipleFilterId: this.schoolId.toString(),\n      Role: '',\n      MerchantId: 0\n    };\n    this.userService.GetUsersWithFilterAPI(filters).subscribe({\n      next: response => {\n        const users = response.Users || [];\n        if (users.length > 0) {\n          const guestUser = users[0]; // Select first guest user found\n          console.log(`[POS] Default guest user found:`, guestUser);\n          // Automatically select the guest user in the dropdown\n          this.autoSelectGuestUser(guestUser);\n        } else {\n          console.log(`[POS] No guest user found for school ${this.schoolId}`);\n          this.balanceError.set('No guest user found');\n        }\n      },\n      error: error => {\n        console.error(`[POS] Error loading default guest user:`, error);\n        this.balanceError.set('Failed to load default guest user');\n        this.handleErrorFromService(error);\n      }\n    });\n  }\n  loadDefaultGuestUserAfterOrder() {\n    console.log(`[POS] Loading default guest user after order completion...`);\n    // Search for guest user using the existing search functionality\n    const filters = {\n      Filter: 'guest',\n      NumberRows: 10,\n      PageIndex: 0,\n      FilterId: 0,\n      SortBy: '',\n      SortDirection: '',\n      MultipleFilterId: this.schoolId.toString(),\n      Role: '',\n      MerchantId: 0\n    };\n    this.userService.GetUsersWithFilterAPI(filters).subscribe({\n      next: response => {\n        const users = response.Users || [];\n        if (users.length > 0) {\n          const guestUser = users[0]; // Select first guest user found\n          console.log(`[POS] Default guest user found for reversion:`, guestUser);\n          // Automatically select the guest user in the dropdown\n          this.autoSelectGuestUser(guestUser);\n          // Send a specific message about guest user reversion for better tracking\n          this.sendGuestUserReversionMessage(guestUser);\n        } else {\n          console.log(`[POS] No guest user found for reversion for school ${this.schoolId}`);\n          this.balanceError.set('No guest user found for reversion');\n        }\n      },\n      error: error => {\n        console.error(`[POS] Error loading default guest user for reversion:`, error);\n        this.balanceError.set('Failed to load default guest user for reversion');\n        this.handleErrorFromService(error);\n      }\n    });\n  }\n  autoSelectGuestUser(guestUser) {\n    console.log(`[POS] Auto-selecting guest user in ${this.viewType} view:`, guestUser.FirstName, guestUser.Lastname);\n    // Set today's date as the default order date - use the exact date from listDays\n    const todayDate = this.findTodayInDatesList();\n    if (todayDate) {\n      this.selectedOrderDate = todayDate;\n      this.orderingForm.get('orderDate')?.setValue(todayDate, {\n        emitEvent: false\n      });\n      console.log(`[POS] Order date set to today:`, todayDate);\n    } else {\n      console.warn(`[POS] Today's date not found in listDays, using first available date`);\n      if (this.listDays.length > 0) {\n        this.selectedOrderDate = this.listDays[0];\n        this.orderingForm.get('orderDate')?.setValue(this.listDays[0], {\n          emitEvent: false\n        });\n      }\n    }\n    // Add a small delay to ensure cross-tab communication is ready\n    setTimeout(() => {\n      console.log(`[POS] Triggering guest user selection after delay...`);\n      // Programmatically select the guest user in the dropdown\n      if (this.studentSearchDropdown) {\n        this.studentSearchDropdown.selectStudent(guestUser);\n        console.log(`[POS] Guest user selected via dropdown component`);\n      } else {\n        // If ViewChild is not ready yet, trigger the selection manually\n        console.log(`[POS] Dropdown not ready, triggering manual selection`);\n        this.onStudentSelected(guestUser);\n      }\n    }, 500); // 500ms delay to ensure cross-tab communication is established\n  }\n\n  findTodayInDatesList() {\n    const today = new Date();\n    const todayDateString = today.toDateString(); // Compare date strings to ignore time\n    return this.listDays.find(date => date.toDateString() === todayDateString) || null;\n  }\n  /**\r\n   * Revert to default guest user after order completion to reset POS to neutral state\r\n   */\n  revertToGuestUserAfterOrder() {\n    console.log(`[POS] Reverting to default guest user after order completion...`);\n    // Store the current student information for logging\n    const currentStudent = this.selectedStudent;\n    if (!currentStudent) {\n      console.log(`[POS] No student was selected, loading default guest user anyway`);\n    } else if (currentStudent.IsGuest) {\n      console.log(`[POS] Current user is already a guest - no reversion needed`);\n      return;\n    } else {\n      console.log(`[POS] Reverting from student: ${currentStudent.FirstName} ${currentStudent.Lastname} (ID: ${currentStudent.UserId}) back to guest user`);\n    }\n    // Add a small delay to ensure all post-order cleanup is complete\n    setTimeout(() => {\n      // Reset order date to today\n      this.resetOrderDateToToday();\n      // Load and select the default guest user\n      this.loadDefaultGuestUserAfterOrder();\n      console.log(`[POS] ✓ Reversion to guest user initiated`);\n    }, 1000); // 1 second delay to ensure cleanup is complete\n  }\n  /**\r\n   * Reset the order date to today's date for the next order\r\n   */\n  resetOrderDateToToday() {\n    const todayDate = this.findTodayInDatesList();\n    if (todayDate) {\n      this.selectedOrderDate = todayDate;\n      this.orderingForm.get('orderDate')?.setValue(todayDate, {\n        emitEvent: false\n      });\n      console.log(`[POS] Order date reset to today: ${todayDate}`);\n    } else {\n      console.warn(`[POS] Today's date not found in listDays, keeping current date`);\n    }\n  }\n  loadUserDetailsAndBalance(student) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!student?.UserId) {\n        _this3.balanceError.set('Student ID not found');\n        return;\n      }\n      // Check authentication before making API calls\n      const isAuthenticated = yield _this3.authService.IsAuthenticated();\n      const connectedUser = _this3.userService.GetUserConnected();\n      if (!isAuthenticated || !connectedUser) {\n        _this3.balanceError.set('Authentication required. Please log in.');\n        console.error(`[POS] User not authenticated for balance API call`);\n        return;\n      }\n      console.log(`[POS] Loading user details for student ID: ${student.UserId}`);\n      console.log(`[POS] Authenticated user:`, connectedUser.Email, `Role:`, connectedUser.Role);\n      _this3.balanceLoading.set(true);\n      _this3.balanceError.set(null);\n      // Call GetUsersDetails API first\n      _this3.userService.GetUserDetailsById(student.UserId).subscribe({\n        next: userDetails => {\n          console.log(`[POS] User details loaded:`, userDetails);\n          if (userDetails?.ExternalUserId) {\n            // Update the selected student with complete details\n            _this3.selectedStudent = userDetails;\n            // Extract balance from user details\n            const balance = parseFloat(userDetails.Parents[0].SpriggyBalance);\n            _this3.balanceLoading.set(false);\n            _this3.parentBalance.set(balance);\n            // Update IsGuest property from detailed user data (in case it changed)\n            _this3.IsGuest = userDetails.IsGuest;\n            // Send balance update message to student view (only from merchant view)\n            if (_this3.viewType === 'merchant') {\n              _this3.sendBalanceUpdatedMessage(userDetails.UserId, balance, `${userDetails.FirstName} ${userDetails.Lastname}`, userDetails.FavouriteColour, userDetails.IsGuest);\n            }\n            if (userDetails.FavouriteColour) {\n              _this3.studentFavoriteColor.set(userDetails.FavouriteColour);\n              _this3.showColorConfirmation.set(true);\n              console.log(`[POS] Showing color confirmation for ${userDetails.FirstName}. Favorite color: ${userDetails.FavouriteColour}`);\n            } else {\n              // No favorite color set\n              console.log(`[POS] No favorite color set for ${userDetails.FirstName}, balance loaded: $${balance}`);\n            }\n          } else {\n            _this3.balanceLoading.set(false);\n            _this3.balanceError.set('External User ID not found in user details');\n            console.error(`[POS] ExternalUserId not found in user details for student: ${student.FirstName}`);\n          }\n        },\n        error: error => {\n          _this3.balanceLoading.set(false);\n          _this3.balanceError.set('Failed to load user details');\n          console.error(`[POS] GetUsersDetails API error:`, error);\n          _this3.handleErrorFromService(error);\n        }\n      });\n    })();\n  }\n  // Color confirmation methods\n  confirmColorIdentity() {\n    console.log(`[POS] Color identity confirmed for student`);\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n    // Send balance update message after color confirmation (only from merchant view)\n    if (this.viewType === 'merchant' && this.selectedStudent && this.parentBalance() !== null) {\n      this.sendBalanceUpdatedMessage(this.selectedStudent.UserId, this.parentBalance(), `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`, this.studentFavoriteColor(), this.IsGuest);\n    }\n  }\n  cancelColorConfirmation() {\n    console.log(`[POS] Color confirmation cancelled`);\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n    this.balanceLoading.set(false);\n    this.balanceError.set('Identity confirmation cancelled');\n    // Clear selected student\n    this.selectedStudent = null;\n    this.parentBalance.set(null);\n    // Clear the dropdown selection\n    if (this.studentSearchDropdown) {\n      this.studentSearchDropdown.clearSelection();\n    }\n  }\n  unsubscribeAll() {\n    if (this.subscriptionShoppingCart$) {\n      this.subscriptionShoppingCart$.unsubscribe();\n    }\n    if (this.subscriptionItemsFilters$) {\n      this.subscriptionItemsFilters$.unsubscribe();\n    }\n    if (this.subscriptionCrossTabMessages$) {\n      this.subscriptionCrossTabMessages$.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function PosTabComponent_Factory(t) {\n      return new (t || PosTabComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Location), i0.ɵɵdirectiveInject(i3.Store), i0.ɵɵdirectiveInject(i4.ItemsFilterService), i0.ɵɵdirectiveInject(i4.OrderApiService), i0.ɵɵdirectiveInject(i4.DebounceService), i0.ɵɵdirectiveInject(i4.MenuService), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.PosCommunicationService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i4.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PosTabComponent,\n      selectors: [[\"app-pos-tab\"]],\n      viewQuery: function PosTabComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(StudentSearchDropdownComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.studentSearchDropdown = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 29,\n      vars: 16,\n      consts: [[\"class\", \"readonly-overlay\", 4, \"ngIf\"], [\"class\", \"top-header\", 4, \"ngIf\"], [1, \"pos-tab-container\"], [1, \"row\"], [1, \"col-12\", \"col-md-8\", \"col-lg-8\", \"col-xl-7\", \"offset-xl-1\"], [\"class\", \"filters-card\", 4, \"ngIf\"], [\"class\", \"col-12 d-flex justify-content-center pt-4\", 4, \"ngIf\", \"ngIfElse\"], [\"result\", \"\"], [1, \"col-12\", \"col-md-4\", \"col-lg-4\", \"col-xl-3\"], [1, \"shopping-cart-sidebar\"], [1, \"cart-card\"], [\"class\", \"cart-sync-notification\", 4, \"ngIf\"], [\"class\", \"empty-cart\", 4, \"ngIf\"], [\"class\", \"balance-section\", \"style\", \"margin-bottom: 13px;\", 4, \"ngIf\"], [\"class\", \"cart-items\", 4, \"ngIf\"], [\"class\", \"payment-section\", 4, \"ngIf\"], [\"class\", \"mobile-balance-display d-block d-md-none\", 4, \"ngIf\"], [\"class\", \"mobile-place-order d-block d-md-none\", 4, \"ngIf\"], [\"class\", \"mobile-readonly-indicator d-block d-md-none\", 4, \"ngIf\"], [\"class\", \"student-popup-overlay\", 4, \"ngIf\"], [\"sharedMenuTemplate\", \"\"], [1, \"readonly-overlay\"], [1, \"top-header\"], [1, \"logo-container\"], [\"alt\", \"Spriggy Schools Logo\", 3, \"src\", \"error\"], [1, \"filters-card\"], [1, \"ordering-form\", 3, \"formGroup\"], [1, \"col-12\", \"col-md-4\", \"mb-3\"], [\"placeholder\", \"Search for a student...\", 3, \"schoolId\", \"studentSelected\"], [\"studentSearchDropdown\", \"\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"formControlName\", \"menuType\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"orderDate\"], [\"class\", \"col-12 mb-3\", 4, \"ngIf\"], [3, \"value\"], [1, \"col-12\", \"mb-3\"], [1, \"color-confirmation-card\"], [1, \"color-confirmation-header\"], [1, \"color-confirmation-content\"], [1, \"color-circle\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"pt-4\"], [3, \"manual\"], [\"class\", \"row\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"waiting-card\"], [1, \"text-center\"], [1, \"waiting-icon\"], [1, \"waiting-title\"], [1, \"waiting-message\"], [4, \"ngIf\", \"ngIfElse\"], [\"merchantMenu\", \"\"], [1, \"no-menu-card\"], [1, \"no-menu-message\"], [4, \"ngTemplateOutlet\"], [\"studentMenu\", \"\"], [1, \"cart-sync-notification\"], [1, \"empty-cart\"], [1, \"balance-section\", 2, \"margin-bottom\", \"13px\"], [\"class\", \"balance-loading\", 4, \"ngIf\"], [\"class\", \"balance-display\", 4, \"ngIf\"], [\"class\", \"balance-error\", 4, \"ngIf\"], [1, \"balance-loading\"], [1, \"loading-icon\"], [1, \"balance-display\"], [1, \"balance-error\"], [1, \"cart-items\"], [\"class\", \"cart-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"cart-total\"], [1, \"cart-item\"], [1, \"item-info\"], [1, \"item-name\"], [1, \"item-details\"], [4, \"ngFor\", \"ngForOf\"], [1, \"item-actions\"], [1, \"item-price\"], [\"class\", \"item-quantity\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"readonly-indicator\", 4, \"ngIf\"], [1, \"item-quantity\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"readonly-indicator\"], [1, \"payment-section\"], [1, \"payment-methods-container\"], [1, \"mat-mdc-card-title\", 2, \"font-size\", \"17px\"], [1, \"col-lg-6\", \"col-md-12\"], [\"mat-stroked-button\", \"\", \"disabled\", \"\", \"data-payment\", \"stripe\", 3, \"click\"], [1, \"col-lg-6\", \"col-md-12\", 2, \"padding-left\", \"0px\"], [\"mat-stroked-button\", \"\", \"disabled\", \"\", \"data-payment\", \"cash\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"disabled\", \"\", \"data-payment\", \"applepay\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"disabled\", \"\", \"data-payment\", \"visa\", 3, \"click\"], [1, \"col-12\"], [\"mat-raised-button\", \"\", 1, \"final-place-order-btn\"], [\"class\", \"readonly-order-info\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", 1, \"payment-btn\", \"primary-payment\", \"selected\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"payment-btn\", \"guest-payment\", \"selected\", 3, \"click\"], [1, \"readonly-order-info\"], [1, \"mobile-balance-display\", \"d-block\", \"d-md-none\"], [1, \"mobile-balance-content\"], [1, \"mobile-place-order\", \"d-block\", \"d-md-none\"], [1, \"mobile-payment-container\"], [1, \"mobile-payment-indicator\"], [\"style\", \"font-size: 0.9rem!important;\", 4, \"ngIf\"], [\"mat-fab\", \"\", \"color\", \"primary\", 1, \"fab-place-order\", 3, \"click\"], [2, \"font-size\", \"0.9rem!important\"], [1, \"mobile-readonly-indicator\", \"d-block\", \"d-md-none\"], [1, \"readonly-fab\"], [1, \"student-popup-overlay\"], [1, \"student-popup-content\"], [1, \"popup-header\"], [\"class\", \"popup-body\", 4, \"ngIf\"], [1, \"popup-body\"], [\"class\", \"popup-item-image\", 3, \"src\", \"alt\", \"error\", 4, \"ngIf\"], [1, \"popup-item-details\"], [\"class\", \"item-description\", 4, \"ngIf\"], [1, \"popup-item-image\", 3, \"src\", \"alt\", \"error\"], [1, \"item-description\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\", \"col-xl-12\", \"offset-xl-1\", \"categories-container\"], [1, \"categories-list\", \"scrolling-horizontal-wrapper\"], [\"class\", \"category-item\", 0, \"trackBy\", \"cat.CategoryId\", \"\", 3, \"id\", 4, \"ngFor\", \"ngForOf\"], [0, \"trackBy\", \"cat.CategoryId\", \"\", 1, \"category-item\", 3, \"id\"], [3, \"name\", \"iconName\", \"isSelected\", \"click\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\", \"col-xl-12\", \"offset-xl-1\", 2, \"margin-left\", \"0px\"], [1, \"category-title\"], [\"class\", \"col-12 col-lg-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"col-12\", \"col-lg-6\", \"mb-3\"], [\"appearance\", \"outlined\", 1, \"menu-item-card\"], [3, \"category\", \"item\", \"dateOrder\", \"currentMenuType\", \"schoolCutOffTime\", \"id\", \"clickItem\", \"itemDialogOpened\", \"itemDialogClosed\"], [1, \"no-items-card\"], [1, \"no-items-message\"]],\n      template: function PosTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PosTabComponent_div_0_Template, 1, 0, \"div\", 0);\n          i0.ɵɵtemplate(1, PosTabComponent_div_1_Template, 3, 1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵelement(3, \"br\")(4, \"br\");\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4);\n          i0.ɵɵtemplate(7, PosTabComponent_mat_card_7_Template, 20, 5, \"mat-card\", 5);\n          i0.ɵɵtemplate(8, PosTabComponent_div_8_Template, 2, 1, \"div\", 6);\n          i0.ɵɵtemplate(9, PosTabComponent_ng_template_9_Template, 3, 3, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"mat-card\", 10)(14, \"mat-card-header\")(15, \"mat-card-title\");\n          i0.ɵɵtext(16, \" Shopping Cart \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-card-content\");\n          i0.ɵɵtemplate(18, PosTabComponent_div_18_Template, 5, 0, \"div\", 11);\n          i0.ɵɵtemplate(19, PosTabComponent_div_19_Template, 3, 2, \"div\", 12);\n          i0.ɵɵtemplate(20, PosTabComponent_div_20_Template, 4, 3, \"div\", 13);\n          i0.ɵɵtemplate(21, PosTabComponent_div_21_Template, 6, 4, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, PosTabComponent_mat_card_actions_22_Template, 40, 14, \"mat-card-actions\", 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(23, PosTabComponent_div_23_Template, 7, 3, \"div\", 16);\n          i0.ɵɵtemplate(24, PosTabComponent_div_24_Template, 10, 2, \"div\", 17);\n          i0.ɵɵtemplate(25, PosTabComponent_div_25_Template, 7, 3, \"div\", 18);\n          i0.ɵɵtemplate(26, PosTabComponent_div_26_Template, 6, 1, \"div\", 19);\n          i0.ɵɵtemplate(27, PosTabComponent_ng_template_27_Template, 2, 2, \"ng-template\", null, 20, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.viewType === \"student\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.viewType == \"student\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"readonly-mode\", ctx.viewType === \"student\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.viewType == \"merchant\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.menuLoading() && (ctx.viewType === \"merchant\" || ctx.viewType === \"student\" && ctx.showMenuInStudentView()))(\"ngIfElse\", _r4);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.viewType === \"student\" && ctx.cartSyncInProgress());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.shoppingCart.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedStudent && !ctx.IsGuest);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.shoppingCart.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.shoppingCart.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedStudent && ctx.parentBalance() !== null && ctx.viewType === \"merchant\" && !ctx.IsGuest);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMobilePlaceOrder && ctx.viewType === \"merchant\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMobilePlaceOrder && ctx.viewType === \"student\" && ctx.showMenuInStudentView());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showStudentPopup() && ctx.viewType === \"student\");\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i7.ɵNgNoValidate, i7.NgControlStatus, i7.NgControlStatusGroup, i7.FormGroupDirective, i7.FormControlName, i8.MatButton, i8.MatIconButton, i8.MatFabButton, i9.MatCard, i9.MatCardActions, i9.MatCardContent, i9.MatCardHeader, i9.MatCardTitle, i10.MatFormField, i10.MatLabel, i11.MatSelect, i12.MatOption, i13.MatIcon, i14.SpinnerComponent, i15.ItemComponent, i16.CategoryTileComponent, i17.StudentSearchDropdownComponent, i2.DecimalPipe, i2.CurrencyPipe, i2.DatePipe],\n      styles: [\".mobile[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 767px) {\\n  .mobile[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.desktop[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (min-width: 767px) {\\n  .desktop[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.mobile[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 767px) {\\n  .mobile[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.desktop[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (min-width: 767px) {\\n  .desktop[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.pos-tab-container[_ngcontent-%COMP%] {\\n  padding: 0rem 1rem 1rem 1rem;\\n  background-color: #f5f5f5;\\n}\\n\\n.top-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: #fff;\\n  padding: 1rem;\\n  position: sticky; \\n\\n  top: 0;\\n  z-index: 100; \\n\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); \\n\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  width: 290px; \\n\\n}\\n.logo-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  width: 300px; \\n\\n}\\n\\n.sync-status-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.sync-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n}\\n.sync-indicator.connected[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n  border: 1px solid #c8e6c9;\\n}\\n.sync-indicator.connected[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n.sync-indicator.disconnected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.sync-indicator.disconnected[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.sync-indicator[_ngcontent-%COMP%]   .sync-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.sync-indicator[_ngcontent-%COMP%]   .last-sync[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  opacity: 0.8;\\n  margin-top: 0.25rem;\\n}\\n\\n.merchant-sync-status[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  border: none;\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n}\\n.merchant-sync-status[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #2196f3;\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.readonly-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: transparent;\\n  z-index: 999;\\n  pointer-events: none;\\n  cursor: not-allowed;\\n}\\n\\n.readonly-banner[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  color: white;\\n  z-index: 1001;\\n  padding: 0.75rem 0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.readonly-banner[_ngcontent-%COMP%]   .readonly-banner-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n}\\n.readonly-banner[_ngcontent-%COMP%]   .readonly-banner-content[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.readonly-banner[_ngcontent-%COMP%]   .readonly-banner-content[_ngcontent-%COMP%]   .readonly-text[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 1.1rem;\\n  letter-spacing: 0.5px;\\n}\\n.readonly-banner[_ngcontent-%COMP%]   .readonly-banner-content[_ngcontent-%COMP%]   .readonly-description[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n  margin-left: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .readonly-banner[_ngcontent-%COMP%]   .readonly-banner-content[_ngcontent-%COMP%]   .readonly-description[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.readonly-mode[_ngcontent-%COMP%] {\\n  pointer-events: none !important;\\n  -webkit-user-select: none !important;\\n          user-select: none !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(255, 107, 107, 0.05);\\n  z-index: 998;\\n  pointer-events: none;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .mat-select[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .mat-option[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .category-tile[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   product-item[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   .mat-fab[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   [role=button][_ngcontent-%COMP%], .readonly-mode[_ngcontent-%COMP%]   [tabindex][_ngcontent-%COMP%] {\\n  pointer-events: none !important;\\n  cursor: not-allowed !important;\\n  opacity: 0.8;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .mat-select[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .mat-option[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .category-tile[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   product-item[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   .mat-fab[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   [role=button][_ngcontent-%COMP%]:hover, .readonly-mode[_ngcontent-%COMP%]   [tabindex][_ngcontent-%COMP%]:hover {\\n  transform: none !important;\\n  box-shadow: none !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .menu-item-card[_ngcontent-%COMP%] {\\n  opacity: 0.85;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .menu-item-card[_ngcontent-%COMP%]:hover {\\n  transform: none !important;\\n  box-shadow: none !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .category-tile[_ngcontent-%COMP%] {\\n  opacity: 0.85;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .category-tile[_ngcontent-%COMP%]:hover {\\n  background-color: transparent !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-category[_ngcontent-%COMP%] {\\n  cursor: not-allowed !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-category[_ngcontent-%COMP%]:hover {\\n  transform: none !important;\\n  background-color: transparent !important;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .category-sync-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1rem;\\n  padding: 0.5rem 1rem;\\n  background-color: #e3f2fd;\\n  border-radius: 8px;\\n  color: #1565c0;\\n  font-size: 0.9rem;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .category-sync-indicator[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 1rem;\\n  font-size: 0.8rem;\\n  color: #2196f3;\\n  font-weight: 400;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  margin-right: 0.25rem;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%]   .mat-icon.syncing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1s linear infinite;\\n  color: #4caf50;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]:not(.syncing) {\\n  animation: none;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%]   .sync-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n@media (max-width: 768px) {\\n  .readonly-mode[_ngcontent-%COMP%]   .cart-sync-indicator[_ngcontent-%COMP%]   .sync-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .shopping-cart-sidebar[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .shopping-cart-sidebar[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .shopping-cart-sidebar[_ngcontent-%COMP%]   .place-order-btn[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .mobile-place-order[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-indicator[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  margin-right: 0.25rem;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-order-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0.75rem;\\n  background-color: #f5f5f5;\\n  border-radius: 4px;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n.readonly-mode[_ngcontent-%COMP%]   .readonly-order-info[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.readonly-mode[_ngcontent-%COMP%]   .top-header[_ngcontent-%COMP%] {\\n  margin-top: 60px;\\n}\\n\\n.readonly-mode[_ngcontent-%COMP%]   .pos-tab-container[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.student-popup-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 1002;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.student-popup-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 2rem;\\n  max-width: 400px;\\n  width: 90%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n  position: relative;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-header[_ngcontent-%COMP%]   .merchant-indicator[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  margin-right: 1rem;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-item-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 200px;\\n  height: auto;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-item-details[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-item-details[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #2196f3;\\n  font-weight: 500;\\n  margin-bottom: 1rem;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .popup-item-details[_ngcontent-%COMP%]   .item-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.5;\\n}\\n.student-popup-content[_ngcontent-%COMP%]   .readonly-notice[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 4px;\\n  padding: 0.75rem;\\n  margin-top: 1rem;\\n  text-align: center;\\n  color: #856404;\\n  font-size: 0.9rem;\\n}\\n\\n.pos-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n.pos-header[_ngcontent-%COMP%]   .pos-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n.pos-header[_ngcontent-%COMP%]   .pos-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.filters-card[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.filters-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n\\n.ordering-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.ordering-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.categories-container[_ngcontent-%COMP%] {\\n  margin-bottom: 0rem;\\n  margin-left: 0px;\\n}\\n\\n.categories-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: nowrap;\\n  overflow-x: auto;\\n  padding: 3px;\\n  margin: 0;\\n  list-style: none;\\n  gap: 0.75rem;\\n}\\n.categories-list.scrolling-horizontal-wrapper[_ngcontent-%COMP%] {\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.categories-list.scrolling-horizontal-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  margin: 0;\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 1rem 0;\\n  color: #333;\\n}\\n\\n.menu-item-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.menu-item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.no-menu-card[_ngcontent-%COMP%], .no-items-card[_ngcontent-%COMP%], .waiting-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n}\\n.no-menu-card[_ngcontent-%COMP%]   .no-menu-message[_ngcontent-%COMP%], .no-menu-card[_ngcontent-%COMP%]   .no-items-message[_ngcontent-%COMP%], .no-items-card[_ngcontent-%COMP%]   .no-menu-message[_ngcontent-%COMP%], .no-items-card[_ngcontent-%COMP%]   .no-items-message[_ngcontent-%COMP%], .waiting-card[_ngcontent-%COMP%]   .no-menu-message[_ngcontent-%COMP%], .waiting-card[_ngcontent-%COMP%]   .no-items-message[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.waiting-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border: 2px dashed #dee2e6;\\n}\\n.waiting-card[_ngcontent-%COMP%]   .waiting-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  color: #6c757d;\\n  margin-bottom: 1rem;\\n}\\n.waiting-card[_ngcontent-%COMP%]   .waiting-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n}\\n.waiting-card[_ngcontent-%COMP%]   .waiting-message[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 1.1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.waiting-card[_ngcontent-%COMP%]   .sync-status-inline[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n}\\n.waiting-card[_ngcontent-%COMP%]   .sync-status-inline[_ngcontent-%COMP%]   .sync-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n}\\n\\n.shopping-cart-sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 2rem;\\n}\\n.shopping-cart-sidebar[_ngcontent-%COMP%]   .cart-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.shopping-cart-sidebar[_ngcontent-%COMP%]   .cart-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n.shopping-cart-sidebar[_ngcontent-%COMP%]   .cart-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.empty-cart[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 1rem 1rem;\\n  color: #999;\\n}\\n\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid #eee;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  margin: 0 0 0.25rem 0;\\n  color: #333;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  margin: 0;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: auto;\\n  width: 32px;\\n  height: 32px;\\n}\\n.cart-items[_ngcontent-%COMP%]   .cart-item[_ngcontent-%COMP%]   .item-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.cart-total[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.5rem 0;\\n  border-top: 2px solid #eee;\\n  margin-top: 1rem;\\n}\\n.cart-total[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.payment-section[_ngcontent-%COMP%] {\\n  padding: 1rem !important;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin: 0 0 1rem 0;\\n  color: #333;\\n  text-align: left;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  text-align: left;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  transition: all 0.3s ease;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.primary-payment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);\\n  color: white;\\n  border: none;\\n  font-weight: 600;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.primary-payment[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.primary-payment.selected[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.3);\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  border: 2px solid #dee2e6;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #e9ecef;\\n  border-color: #ced4da;\\n  transform: translateY(-1px);\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment.selected[_ngcontent-%COMP%] {\\n  background-color: #e7f3ff;\\n  border-color: #007bff;\\n  color: #0056b3;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[_ngcontent-%COMP%]:disabled:hover {\\n  transform: none;\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[data-payment=stripe][_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #635bff;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[data-payment=cash][_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[data-payment=applepay][_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #000;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn.secondary-payment[data-payment=visa][_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #1a1f71;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .final-place-order-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-top: 1rem;\\n  background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .final-place-order-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 140, 0, 0.4);\\n}\\n\\n.place-order-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n\\n.mobile-place-order[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 2rem;\\n  right: 2rem;\\n  z-index: 1000;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 0.5rem;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%]   .mobile-payment-indicator[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 20px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.8rem;\\n  color: #333;\\n  max-width: 200px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%]   .mobile-payment-indicator[_ngcontent-%COMP%]:hover {\\n  background-color: rgb(255, 255, 255);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%]   .mobile-payment-indicator[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n  color: #ff8c00;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%]   .mobile-payment-indicator[_ngcontent-%COMP%]   .mat-icon.expand-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-left: auto;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .mobile-payment-container[_ngcontent-%COMP%]   .mobile-payment-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  flex: 1;\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .fab-place-order[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);\\n}\\n.mobile-place-order[_ngcontent-%COMP%]   .fab-place-order[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);\\n}\\n\\n.mobile-readonly-indicator[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 2rem;\\n  right: 2rem;\\n  z-index: 1000;\\n}\\n.mobile-readonly-indicator[_ngcontent-%COMP%]   .readonly-fab[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 64px;\\n  height: 64px;\\n  background-color: #666;\\n  color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  font-size: 0.7rem;\\n  text-align: center;\\n}\\n.mobile-readonly-indicator[_ngcontent-%COMP%]   .readonly-fab[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  margin-bottom: 0.1rem;\\n}\\n.mobile-readonly-indicator[_ngcontent-%COMP%]   .readonly-fab[_ngcontent-%COMP%]   .cart-total[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.65rem;\\n}\\n\\n@media (max-width: 767px) {\\n  .pos-tab-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .pos-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .pos-header[_ngcontent-%COMP%]   .pos-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .filters-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .filters-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .shopping-cart-sidebar[_ngcontent-%COMP%] {\\n    position: static;\\n    margin-top: 2rem;\\n  }\\n  .payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    padding: 0.6rem 0.8rem;\\n  }\\n  .payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n  .payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .payment-section[_ngcontent-%COMP%]   .payment-methods-container[_ngcontent-%COMP%]   .final-place-order-btn[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 0.8rem;\\n  }\\n  .category-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n    margin: 0.5rem 0;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .ordering-form[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .menu-item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.cart-sync-notification[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem;\\n  margin-bottom: 1rem;\\n  background-color: #e8f5e8;\\n  border-radius: 4px;\\n  color: #2e7d32;\\n  font-size: 0.9rem;\\n  border-left: 3px solid #4caf50;\\n}\\n.cart-sync-notification[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  font-size: 1.1rem;\\n  width: 1.1rem;\\n  height: 1.1rem;\\n  animation: _ngcontent-%COMP%_rotate 1s linear infinite;\\n}\\n\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 1001;\\n  display: flex;\\n  align-items: flex-end;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: white;\\n  border-radius: 16px 16px 0 0;\\n  padding: 1rem;\\n  max-height: 70vh;\\n  overflow-y: auto;\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease-out;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #eee;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  text-align: left;\\n  border: 2px solid #dee2e6;\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  transition: all 0.3s ease;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option.primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);\\n  color: white;\\n  border-color: #ff8c00;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option.primary[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #e7f3ff;\\n  color: #0056b3;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option.selected.primary[_ngcontent-%COMP%] {\\n  border-color: #ff6b47;\\n  box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.3);\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option[_ngcontent-%COMP%]   .mat-icon.check-icon[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  color: #28a745;\\n}\\n.mobile-payment-selector-overlay[_ngcontent-%COMP%]   .mobile-payment-selector[_ngcontent-%COMP%]   .mobile-payment-options[_ngcontent-%COMP%]   .mobile-payment-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    transform: translateY(100%);\\n  }\\n  to {\\n    transform: translateY(0);\\n  }\\n}\\n@media (min-width: 768px) {\\n  .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 0.75rem 1rem;\\n  }\\n  .payment-methods-container[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 1rem;\\n  }\\n  .payment-methods-container[_ngcontent-%COMP%]   .secondary-payment-row[_ngcontent-%COMP%]   .payment-btn[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .payment-methods-container[_ngcontent-%COMP%]   .final-place-order-btn[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .payment-methods-container[_ngcontent-%COMP%] {\\n    max-width: 700px;\\n    margin: auto;\\n  }\\n}\\n  .mdc-button__label {\\n  font-size: 0.8rem !important;\\n}\\n\\n.guest-user-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 16px;\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  color: #856404;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.guest-user-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n  .mb-3 {\\n  margin-bottom: 0.5rem !important;\\n}\\n\\n  .mat-mdc-card-content:last-child {\\n  padding-bottom: 0px !important;\\n}\\n\\n.balance-section[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding: 0.75rem;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n  background-color: #f9f9f9;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-loading[_ngcontent-%COMP%], .balance-section[_ngcontent-%COMP%]   .balance-display[_ngcontent-%COMP%], .balance-section[_ngcontent-%COMP%]   .balance-error[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-loading[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-loading[_ngcontent-%COMP%]   .loading-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 1s linear infinite;\\n  color: #2196f3;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-display[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 500;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-display[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-error[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.balance-section[_ngcontent-%COMP%]   .balance-error[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.mobile-balance-display[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 120px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 1000;\\n}\\n.mobile-balance-display[_ngcontent-%COMP%]   .mobile-balance-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  background-color: #4caf50;\\n  color: white;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 25px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.mobile-balance-display[_ngcontent-%COMP%]   .mobile-balance-content[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-header[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 3px solid #fff;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  flex-shrink: 0;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n  color: white;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   .confirm-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #45a049;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #da190b;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  font-weight: 500;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n.color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .color-confirmation-card[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: 1rem;\\n  }\\n  .color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .color-confirmation-card[_ngcontent-%COMP%]   .color-confirmation-content[_ngcontent-%COMP%]   .color-confirmation-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    flex: 1;\\n    max-width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["signal", "FormControl", "FormGroup", "Validators", "select", "cartSelectors", "cartActions", "PosMessageType", "BaseComponent", "MenuTypeEnum", "ImageUrlEnum", "environment", "moment", "PosPlaceOrderDialogComponent", "StudentSearchDropdownComponent", "ConvertToUniversalDateFormat", "GetCartItemsPrice", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "PosTabComponent_div_1_Template_img_error_2_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "onLogoImageError", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "getLogoImageSrc", "ɵɵsanitizeUrl", "ɵɵtext", "menuOption_r23", "menuType", "ɵɵtextInterpolate1", "menuName", "day_r24", "ɵɵpipeBind2", "ɵɵstyleProp", "ctx_r22", "studentFavoriteColor", "PosTabComponent_mat_card_7_Template_student_search_dropdown_studentSelected_5_listener", "$event", "_r26", "ctx_r25", "onStudentSelected", "ɵɵtemplate", "PosTabComponent_mat_card_7_mat_option_12_Template", "PosTabComponent_mat_card_7_mat_option_18_Template", "PosTabComponent_mat_card_7_div_19_Template", "ctx_r2", "orderingForm", "schoolId", "menuPickerData", "listDays", "showColorConfirmation", "IsGuest", "ɵɵtextInterpolate", "ctx_r30", "noMenuMessage", "ɵɵelementContainer", "PosTabComponent_ng_template_9_div_1_ng_template_2_ng_container_0_Template", "_r15", "PosTabComponent_ng_template_9_div_1_div_1_Template", "PosTabComponent_ng_template_9_div_1_ng_template_2_Template", "ɵɵtemplateRefExtractor", "ctx_r28", "_r31", "ctx_r34", "PosTabComponent_ng_template_9_div_2_ng_template_2_ng_container_0_Template", "PosTabComponent_ng_template_9_div_2_div_1_Template", "PosTabComponent_ng_template_9_div_2_ng_template_2_Template", "ctx_r29", "_r35", "PosTabComponent_ng_template_9_div_0_Template", "PosTabComponent_ng_template_9_div_1_Template", "PosTabComponent_ng_template_9_div_2_Template", "ctx_r5", "viewType", "showMenuInStudentView", "selectedStudent", "PosTabComponent_div_19_p_1_Template", "PosTabComponent_div_19_p_2_Template", "ctx_r7", "ɵɵpipeBind1", "ctx_r41", "parentBalance", "ctx_r42", "balanceError", "PosTabComponent_div_20_div_1_Template", "PosTabComponent_div_20_div_2_Template", "PosTabComponent_div_20_div_3_Template", "ctx_r8", "balanceLoading", "option_r49", "optionName", "item_r44", "quantity", "PosTabComponent_div_21_div_1_button_11_Template_button_click_0_listener", "_r53", "$implicit", "ctx_r51", "removeFromCart", "itemCartId", "PosTabComponent_div_21_div_1_span_5_Template", "PosTabComponent_div_21_div_1_span_10_Template", "PosTabComponent_div_21_div_1_button_11_Template", "PosTabComponent_div_21_div_1_span_12_Template", "name", "selectedOptions", "itemPriceIncGst", "ctx_r43", "PosTabComponent_div_21_div_1_Template", "ctx_r9", "shoppingCart", "priceCart", "PosTabComponent_mat_card_actions_22_div_4_Template_button_click_2_listener", "_r58", "ctx_r57", "OrderClick", "ctx_r54", "PosTabComponent_mat_card_actions_22_div_5_Template_button_click_2_listener", "_r60", "ctx_r59", "Guest<PERSON><PERSON><PERSON><PERSON><PERSON>", "ctx_r55", "ctx_r56", "PosTabComponent_mat_card_actions_22_div_4_Template", "PosTabComponent_mat_card_actions_22_div_5_Template", "PosTabComponent_mat_card_actions_22_Template_button_click_8_listener", "_r62", "ctx_r61", "selectPaymentMethod", "PosTabComponent_mat_card_actions_22_Template_button_click_14_listener", "ctx_r63", "PosTabComponent_mat_card_actions_22_Template_button_click_21_listener", "ctx_r64", "PosTabComponent_mat_card_actions_22_Template_button_click_27_listener", "ctx_r65", "PosTabComponent_mat_card_actions_22_div_39_Template", "ctx_r10", "ɵɵclassMap", "selectedPaymentMethod", "ctx_r11", "PosTabComponent_div_24_span_5_Template", "PosTabComponent_div_24_span_6_Template", "PosTabComponent_div_24_Template_button_click_7_listener", "_r69", "ctx_r68", "ctx_r12", "ctx_r13", "PosTabComponent_div_26_div_5_img_1_Template_img_error_0_listener", "_r74", "ctx_r73", "target", "src", "defaultImagePath", "ctx_r71", "getItemImageUrl", "studentPopupData", "item", "Images", "ImageUrl", "Name", "ctx_r72", "Description", "PosTabComponent_div_26_div_5_img_1_Template", "PosTabComponent_div_26_div_5_div_8_Template", "ctx_r70", "length", "Price", "PosTabComponent_div_26_div_5_Template", "ctx_r14", "PosTabComponent_ng_template_27_div_0_li_3_Template_category_tile_click_1_listener", "restoredCtx", "_r81", "cat_r78", "ctx_r80", "SetCategory", "ɵɵpropertyInterpolate1", "i_r79", "ɵɵclassProp", "ctx_r77", "CatName", "CatUrl", "IsCurrentCategory", "PosTabComponent_ng_template_27_div_0_li_3_Template", "ctx_r75", "currentMenu", "MenuJSON", "PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_clickItem_2_listener", "_r87", "ctx_r86", "AddToCart", "PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_itemDialogOpened_2_listener", "ctx_r88", "onItemDialogOpened", "PosTabComponent_ng_template_27_ng_container_1_div_6_Template_product_item_itemDialogClosed_2_listener", "ctx_r89", "onItemDialogClosed", "ctx_r82", "currentCategoryToDisplay", "item_r84", "selectedOrderDate", "selectedMenuType", "menuCutOffTime", "i_r85", "ɵɵelementContainerStart", "PosTabComponent_ng_template_27_ng_container_1_div_6_Template", "PosTabComponent_ng_template_27_ng_container_1_div_7_Template", "ɵɵelementContainerEnd", "ctx_r76", "PosTabComponent_ng_template_27_div_0_Template", "PosTabComponent_ng_template_27_ng_container_1_Template", "ctx_r16", "PosTabComponent", "constructor", "route", "location", "store", "itemsFiltersService", "orderApiService", "debounceService", "menuService", "dialog", "posCommunicationService", "userService", "authService", "Recess", "Date", "menuLoading", "GENERIC_ERROR_MESSAGE", "IsAdminOrMerchant", "titlePage", "showMobilePlaceOrder", "menuTypeEnum", "isConnectedToOtherTabs", "lastSyncTime", "logoImageError", "showStudentPopup", "cartSyncInProgress", "lastCartSyncTime", "colorConfirmationPending", "showMobilePaymentSelector", "paymentMethods", "id", "icon", "enabled", "primary", "loadMenuDebounce", "callDebounce", "refreshMenu", "ngOnInit", "_this", "_asyncToGenerator", "initializeFromRoute", "setupSubscriptions", "setupCrossTabCommunication", "initializeForms", "loadMenuNames", "generateDatesList", "console", "log", "checkAuthenticationAndLoadGuestUser", "ngOnDestroy", "unsubscribeAll", "queryParams", "subscribe", "params", "guid", "subscriptionShoppingCart$", "pipe", "getCartItems", "cartItems", "set", "sendCartUpdateMessage", "subscriptionItemsFilters$", "filtersUpdatedEvent$", "FilterItems", "getConnectionStatus", "isConnected", "subscriptionCrossTabMessages$", "onMessageForViewType", "message", "handleCrossTabMessage", "sourceGuid", "type", "ORDER_PLACED", "handleOrderPlacedMessage", "payload", "STUDENT_SELECTED", "handleStudentSelectedMessage", "MENU_CHANGED", "handleMenuChangedMessage", "CART_UPDATED", "handleCartUpdatedMessage", "CART_CLEARED", "handleCartClearedMessage", "VIEW_REFRESH", "handleViewRefreshMessage", "ITEM_POPUP_OPENED", "handleItemPopupOpenedMessage", "ITEM_POPUP_CLOSED", "handleItemPopupClosedMessage", "CATEGORY_CHANGED", "handleCategoryChangedMessage", "BALANCE_UPDATED", "handleBalanceUpdatedMessage", "required", "orderDate", "get", "valueChanges", "value", "sendMenuChangedMessage", "student", "UserId", "FirstName", "dispatch", "clearCart", "sendCartClearedMessage", "deactivatedFilters", "SchoolDeactivatedFilters", "loadUserDetailsAndBalance", "currentCategory", "sendStudentSelectedMessage", "GetMenuNamesList", "next", "menuList", "processMenuData", "error", "handleErrorFromService", "push", "getMenuData", "Lunch", "defaultMenu", "find", "m", "menuIndex", "getMenuIndex", "friendlyName", "findIndex", "menu", "today", "i", "date", "setDate", "getDate", "loadMenu", "canteenMenuAvailableCheck", "request", "studentId", "GetOrderByStudentOrderDateAndMenuType", "res", "processPreMenuCheck", "isSchoolClosedForCanteenOrders", "setNoMenuMessage", "orderAlreadyPlaced", "getFormattedDate", "isSchoolClosed", "Boolean", "order", "format", "GetMenuBySchoolAndType", "SchoolId", "menuToDisplay", "menuDataExists", "getMenuToDisplay", "processMenuResult", "menuResult", "menuId", "MenuId", "getMenuCutOffTime", "category", "sendCategoryChangedMessage", "cat", "cartItem", "convertOrderItemToCartItem", "addToCart", "event", "sendItemPopupOpenedMessage", "sendItemPopupClosedMessage", "itemAdded", "removeItem", "Error", "options", "SelectedOptions", "map", "option", "menuItemOptionId", "MenuItemOptionId", "OptionName", "optionCost", "OptionCost", "parentOptionId", "MenuItemOptionsCategoryId", "studentName", "menuCutOffDateTime", "canteenId", "CanteenId", "unix", "menuItemId", "MenuItemId", "ItemPriceIncGst", "Quantity", "methodId", "openMobilePaymentSelector", "closeMobilePaymentSelector", "validateOrderPrerequisites", "alert", "groupedCarts", "dialogRef", "open", "width", "disableClose", "data", "editOrderId", "afterClosed", "result", "success", "orderId", "revertToGuestUserAfterOrder", "GoBackClick", "back", "shouldShowMenuInStudentView", "imageUrl", "indexOf", "blobStorage", "ItemsLG", "Lastname", "isGuest", "sendStudentSelected", "sendGuestUserReversionMessage", "guestUser", "reason", "itemCount", "totalAmount", "timestamp", "toISOString", "sendCartUpdated", "sendMenuChanged", "sendOrderPlacedMessage", "paymentMethod", "sendOrderPlaced", "sendItemPopupOpened", "sendItemPopupClosed", "categoryId", "CategoryId", "categoryName", "sendCategoryChanged", "sendBalanceUpdatedMessage", "balance", "favoriteColor", "sendBalanceUpdated", "previousStudentId", "newStudentId", "sendCartCleared", "clearOrderRelatedState", "currentStudent", "getTime", "patchValue", "toLocaleTimeString", "setTimeout", "undefined", "_this2", "isAuthenticated", "IsAuthenticated", "connectedUser", "GetUserConnected", "Email", "Role", "GetToken", "loadDefaultGuestUser", "filters", "Filter", "NumberRows", "PageIndex", "FilterId", "SortBy", "SortDirection", "MultipleFilterId", "toString", "MerchantId", "GetUsersWithFilterAPI", "response", "users", "Users", "autoSelectGuestUser", "loadDefaultGuestUserAfterOrder", "todayDate", "findTodayInDatesList", "setValue", "emitEvent", "warn", "studentSearchDropdown", "selectStudent", "todayDateString", "toDateString", "resetOrderDateToToday", "_this3", "GetUserDetailsById", "userDetails", "ExternalUserId", "parseFloat", "Parents", "SpriggyBalance", "FavouriteColour", "confirmColorIdentity", "cancelColorConfirmation", "clearSelection", "unsubscribe", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "Location", "i3", "Store", "i4", "ItemsFilterService", "OrderApiService", "DebounceService", "MenuService", "i5", "MatDialog", "i6", "PosCommunicationService", "UserService", "AuthService", "selectors", "viewQuery", "PosTabComponent_Query", "rf", "ctx", "PosTabComponent_div_0_Template", "PosTabComponent_div_1_Template", "PosTabComponent_mat_card_7_Template", "PosTabComponent_div_8_Template", "PosTabComponent_ng_template_9_Template", "PosTabComponent_div_18_Template", "PosTabComponent_div_19_Template", "PosTabComponent_div_20_Template", "PosTabComponent_div_21_Template", "PosTabComponent_mat_card_actions_22_Template", "PosTabComponent_div_23_Template", "PosTabComponent_div_24_Template", "PosTabComponent_div_25_Template", "PosTabComponent_div_26_Template", "PosTabComponent_ng_template_27_Template", "_r4"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\pos-tab\\pos-tab.component.ts", "D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\pos-tab\\pos-tab.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy, signal, ViewChild } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { Location } from '@angular/common';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { MatDialog } from '@angular/material/dialog';\n\n// Ngrx\nimport { Store, select } from '@ngrx/store';\nimport { Subscription } from 'rxjs';\nimport * as cartSelectors from '../../../states/shoppingCart/shopping-cart.selectors';\nimport * as cartActions from '../../../states/shoppingCart/shopping-cart.actions';\nimport { FamilyState } from '../../../states';\n\n// POS Communication\nimport { PosCommunicationService } from '../../services/pos-communication.service';\nimport {\n  PosMessage,\n  PosMessageType,\n  OrderPlacedPayload,\n  StudentSelectedPayload,\n  MenuChangedPayload,\n  CartUpdatedPayload,\n  ViewRefreshPayload,\n  ItemPopupOpenedPayload,\n  ItemPopupClosedPayload,\n  CategoryChangedPayload,\n  BalanceUpdatedPayload,\n  CartClearedPayload\n} from '../../models/pos-messages.interface';\n\n// Models\nimport {\n  Menu,\n  BaseComponent,\n  Category,\n  MenuTypeEnum,\n  UserCashless,\n  OrderFilterRequest,\n  CheckIfOrderExist,\n  CartItem,\n  MenuPickerData,\n  MenuNamesResponse,\n  ImageUrlEnum,\n  RefinedOrderItem,\n  CartOption,\n  ArrayFilter,\n} from 'src/app/sharedModels';\nimport { environment } from 'src/environments/environment';\nimport * as moment from 'moment';\n\n// Services\nimport {\n  ItemsFilterService,\n  OrderApiService,\n  DebounceService,\n  MenuService,\n  UserService,\n  AuthService,\n} from 'src/app/sharedServices';\n\n// Components\nimport { PosPlaceOrderDialogComponent } from '../pos-place-order-dialog/pos-place-order-dialog.component';\nimport { StudentSearchDropdownComponent } from '../student-search-dropdown/student-search-dropdown.component';\nimport { ConvertToUniversalDateFormat } from 'src/app/utility';\nimport { GetCartItemsPrice } from '../../../manage-order/functions/calculate-price';\n\n@Component({\n  selector: 'app-pos-tab',\n  templateUrl: './pos-tab.component.html',\n  styleUrls: ['./pos-tab.component.scss'],\n})\nexport class PosTabComponent extends BaseComponent implements OnInit, OnDestroy {\n  // ViewChild references\n  @ViewChild(StudentSearchDropdownComponent) studentSearchDropdown: StudentSearchDropdownComponent;\n\n  // Route parameters\n  schoolId: number;\n  guid: string;\n  viewType: 'merchant' | 'student';\n\n  // Component state\n  selectedStudent: UserCashless | null = null;\n  selectedMenuType: string = MenuTypeEnum.Recess; // Default to recess\n  selectedOrderDate: Date = new Date();\n  menuName: string = '';\n  menuId: number;\n  menuLoading = signal<boolean>(false);\n  GENERIC_ERROR_MESSAGE = 'Something went wrong, Please try again';\n  IsAdminOrMerchant: boolean = true; // Always true for POS\n\n  // Menu and display\n  titlePage = signal<string>('POS - Point of Sale');\n  currentMenu: Menu;\n  private currentCategory: Category;\n  currentCategoryToDisplay: Category;\n  showMobilePlaceOrder: boolean = false;\n  priceCart = signal<number>(0);\n  menuTypeEnum = MenuTypeEnum;\n  deactivatedFilters: string;\n  menuCutOffTime: string;\n  noMenuMessage = null;\n  shoppingCart: CartItem[] = [];\n\n  // Form controls\n  menuPickerData: MenuPickerData[] = [];\n  listDays: Date[] = [];\n  orderingForm: FormGroup;\n\n  // Subscriptions\n  private subscriptionShoppingCart$: Subscription;\n  private subscriptionItemsFilters$: Subscription;\n  private subscriptionCrossTabMessages$: Subscription;\n\n  // Cross-tab communication\n  isConnectedToOtherTabs = signal<boolean>(false);\n  lastSyncTime = signal<Date | null>(null);\n  showMenuInStudentView = signal<boolean>(false);\n\n  // Image error handling\n  logoImageError = signal<boolean>(false);\n  defaultImagePath: string = 'assets/images/spriggy-default-image.png';\n\n  // Student view popup state\n  showStudentPopup = signal<boolean>(false);\n  studentPopupData = signal<any>(null);\n\n  // Cart synchronization state\n  cartSyncInProgress = signal<boolean>(false);\n  lastCartSyncTime = signal<string>('');\n\n  // Balance state\n  parentBalance = signal<number | null>(null);\n  balanceLoading = signal<boolean>(false);\n  balanceError = signal<string | null>(null);\n\n  // Favorite color confirmation state\n  showColorConfirmation = signal<boolean>(false);\n  studentFavoriteColor = signal<string | null>(null);\n  colorConfirmationPending = signal<boolean>(false);\n\n  // Payment method selection - Only Spriggy is functional\n  selectedPaymentMethod = signal<string>('spriggy');\n  showMobilePaymentSelector = signal<boolean>(false);\n  paymentMethods = [\n    {\n      id: 'spriggy',\n      name: 'Pay with Spriggy Card / Wallet',\n      icon: 'credit_card',\n      enabled: true,\n      primary: true\n    }\n    // Note: Other payment methods (Stripe, Cash, Apple Pay, Visa) are disabled\n    // as they are not functional in the current system. Only Spriggy payment\n    // works with the existing canteen/order/place API infrastructure.\n  ];\n\n  constructor(\n    private route: ActivatedRoute,\n    private location: Location,\n    private store: Store<{ family: FamilyState }>,\n    private itemsFiltersService: ItemsFilterService,\n    private orderApiService: OrderApiService,\n    private debounceService: DebounceService,\n    private menuService: MenuService,\n    private dialog: MatDialog,\n    private posCommunicationService: PosCommunicationService,\n    private userService: UserService,\n    private authService: AuthService\n  ) {\n    super();\n  }\n\n  async ngOnInit() {\n    this.initializeFromRoute();\n    this.setupSubscriptions();\n    this.setupCrossTabCommunication();\n    this.initializeForms();\n    this.loadMenuNames();\n    this.generateDatesList();\n\n    // Only merchant view should load guest user automatically\n    // Student view should wait for cross-tab communication from merchant view\n    if (this.viewType === 'merchant') {\n      console.log(`[POS] Merchant view - loading default guest user`);\n      await this.checkAuthenticationAndLoadGuestUser();\n    } else {\n      console.log(`[POS] Student view - waiting for guest user selection from merchant view`);\n      // Student view will receive guest user via handleStudentSelectedMessage()\n    }\n  }\n\n  ngOnDestroy() {\n    this.unsubscribeAll();\n  }\n\n  private initializeFromRoute(): void {\n    this.route.queryParams.subscribe(params => {\n      this.schoolId = +params['schoolId'] || 52243;\n      this.guid = params['guid'];\n      this.viewType = params['viewType'] || 'merchant';\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Shopping cart subscription\n    this.subscriptionShoppingCart$ = this.store\n      .pipe(select(cartSelectors.getCartItems))\n      .subscribe((cartItems: CartItem[]) => {\n        console.log(`[POS] Cart items updated:`, cartItems);\n        this.showMobilePlaceOrder = cartItems.length > 0;\n        this.priceCart.set(GetCartItemsPrice(cartItems));\n        this.shoppingCart = cartItems;\n\n        // Send cart update message to other tabs (only from merchant view)\n        if (this.viewType === 'merchant' && this.selectedStudent) {\n          this.sendCartUpdateMessage(cartItems);\n        }\n      });\n\n    // Filters subscription - simplified for POS\n    this.subscriptionItemsFilters$ = this.itemsFiltersService.filtersUpdatedEvent$.subscribe(() => {\n      this.FilterItems();\n    });\n  }\n\n  private setupCrossTabCommunication(): void {\n    console.log(`[POS] Setting up cross-tab communication for ${this.viewType} view with GUID: ${this.guid}`);\n\n    // Subscribe to connection status\n    this.posCommunicationService.getConnectionStatus().subscribe(isConnected => {\n      this.isConnectedToOtherTabs.set(isConnected);\n      console.log(`[POS] Connection status changed: ${isConnected}`);\n    });\n\n    // Subscribe to messages for this view type\n    this.subscriptionCrossTabMessages$ = this.posCommunicationService\n      .onMessageForViewType(this.viewType)\n      .subscribe((message: PosMessage) => {\n        console.log(`[POS] Received message for ${this.viewType} view:`, message);\n        this.handleCrossTabMessage(message);\n      });\n  }\n\n  private handleCrossTabMessage(message: PosMessage): void {\n    // Ignore messages from the same tab\n    if (message.sourceGuid === this.guid) {\n      return;\n    }\n\n    this.lastSyncTime.set(new Date());\n\n    switch (message.type) {\n      case PosMessageType.ORDER_PLACED:\n        this.handleOrderPlacedMessage(message.payload as OrderPlacedPayload);\n        break;\n      case PosMessageType.STUDENT_SELECTED:\n        this.handleStudentSelectedMessage(message.payload as StudentSelectedPayload);\n        break;\n      case PosMessageType.MENU_CHANGED:\n        this.handleMenuChangedMessage(message.payload as MenuChangedPayload);\n        break;\n      case PosMessageType.CART_UPDATED:\n        this.handleCartUpdatedMessage(message.payload as CartUpdatedPayload);\n        break;\n      case PosMessageType.CART_CLEARED:\n        this.handleCartClearedMessage(message.payload as CartClearedPayload);\n        break;\n      case PosMessageType.VIEW_REFRESH:\n        this.handleViewRefreshMessage(message.payload as ViewRefreshPayload);\n        break;\n      case PosMessageType.ITEM_POPUP_OPENED:\n        this.handleItemPopupOpenedMessage(message.payload as ItemPopupOpenedPayload);\n        break;\n      case PosMessageType.ITEM_POPUP_CLOSED:\n        this.handleItemPopupClosedMessage(message.payload as ItemPopupClosedPayload);\n        break;\n      case PosMessageType.CATEGORY_CHANGED:\n        this.handleCategoryChangedMessage(message.payload as CategoryChangedPayload);\n        break;\n      case PosMessageType.BALANCE_UPDATED:\n        this.handleBalanceUpdatedMessage(message.payload as BalanceUpdatedPayload);\n        break;\n    }\n  }\n\n  private initializeForms(): void {\n    this.orderingForm = new FormGroup({\n      menuType: new FormControl(this.selectedMenuType, [Validators.required]),\n      orderDate: new FormControl(this.selectedOrderDate, [Validators.required]),\n    });\n\n    // Subscribe to form changes\n    this.orderingForm.get('menuType')?.valueChanges.subscribe(value => {\n      this.selectedMenuType = value;\n      this.loadMenuDebounce();\n\n      // Send menu change message to other tabs (only from merchant view)\n      if (this.viewType === 'merchant') {\n        this.sendMenuChangedMessage();\n      }\n    });\n\n    this.orderingForm.get('orderDate')?.valueChanges.subscribe(value => {\n      this.selectedOrderDate = value;\n      this.loadMenuDebounce();\n\n      // Send menu change message to other tabs (only from merchant view)\n      if (this.viewType === 'merchant') {\n        this.sendMenuChangedMessage();\n      }\n    });\n  }\n\n  onStudentSelected(student: UserCashless): void {\n    console.log(`[POS] Student selected:`, student);\n\n    // Clear cart when changing students (only in merchant view)\n    if (this.viewType === 'merchant' && this.selectedStudent && student &&\n      this.selectedStudent.UserId !== student.UserId) {\n      console.log(`[POS] Clearing cart due to student change from ${this.selectedStudent.FirstName} to ${student.FirstName}`);\n      this.store.dispatch(cartActions.clearCart());\n\n      // Send cart cleared message to student view\n      this.sendCartClearedMessage('student_changed', this.selectedStudent.UserId, student.UserId);\n    }\n\n    this.selectedStudent = student;\n    if (student) {\n      // Set IsGuest property immediately based on student data\n      this.IsGuest = student.IsGuest || false;\n      console.log(`[POS] Student selected - IsGuest: ${this.IsGuest}`);\n\n      // Only Spriggy payment is supported - guest users cannot place orders\n      if (this.IsGuest) {\n        console.log(`[POS] Guest user selected - guest users cannot place orders in POS system`);\n        // Keep Spriggy selected but orders will be blocked for guest users\n      }\n\n      // Ensure Spriggy is always selected (only functional payment method)\n      this.selectedPaymentMethod.set('spriggy');\n\n      this.deactivatedFilters = student.SchoolDeactivatedFilters || null;\n      this.loadMenuDebounce();\n\n      // Call GetUsersDetails API first, then load balance (only for non-guest users)\n      if (!this.IsGuest) {\n\n      }\n      this.loadUserDetailsAndBalance(student);\n      //  else {\n      //   // For guest users, clear balance information\n      //   this.parentBalance.set(null);\n      //   this.balanceLoading.set(false);\n      //   this.balanceError.set(null);\n      //   console.log(`[POS] Guest user selected - skipping balance loading`);\n      // }\n    } else {\n      // Clear menu, categories, filters, and cart when no student selected\n      this.currentMenu = null;\n      this.currentCategory = null;\n      this.currentCategoryToDisplay = null;\n      this.noMenuMessage = null;\n      this.parentBalance.set(null);\n      this.balanceError.set(null);\n      this.IsGuest = false;\n\n      // Clear color confirmation dialog\n      this.showColorConfirmation.set(false);\n      this.studentFavoriteColor.set(null);\n      this.colorConfirmationPending.set(false);\n\n      // Clear any active filter states\n      this.deactivatedFilters = null;\n\n      console.log(`[POS] No student selected - clearing all UI elements and filters`);\n\n      // Clear cart when no student is selected (only in merchant view)\n      if (this.viewType === 'merchant') {\n        console.log(`[POS] Clearing cart due to no student selected`);\n        this.store.dispatch(cartActions.clearCart());\n\n        // Send cart cleared message to student view\n        this.sendCartClearedMessage('no_student_selected');\n      }\n    }\n\n    // Send student selection message to other tabs (only from merchant view)\n    if (this.viewType === 'merchant') {\n      this.sendStudentSelectedMessage(student);\n    }\n  }\n\n  loadMenuNames(): void {\n    this.menuService.GetMenuNamesList(this.schoolId).subscribe({\n      next: (menuList: MenuNamesResponse[]) => {\n        this.processMenuData(menuList);\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  processMenuData(menuList: MenuNamesResponse[]): void {\n    this.menuPickerData = [];\n    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Recess));\n    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Lunch));\n\n    // Set default menu name\n    const defaultMenu = this.menuPickerData.find(m => m.menuType === this.selectedMenuType);\n    if (defaultMenu) {\n      this.menuName = defaultMenu.menuName;\n    }\n  }\n\n  private getMenuData(menuList: MenuNamesResponse[], menuType: string): MenuPickerData {\n    const menuIndex = this.getMenuIndex(menuList, menuType);\n    return {\n      menuType: menuType,\n      menuName: menuIndex >= 0 ? menuList[menuIndex].friendlyName : menuType,\n    };\n  }\n\n  private getMenuIndex(menuList: MenuNamesResponse[], menuType: string): number {\n    if (!menuList?.length || !menuType) return -1;\n    return menuList?.findIndex(menu => menu.menuType === menuType);\n  }\n\n  generateDatesList(): void {\n    this.listDays = [];\n    const today = new Date();\n\n    // Generate next 14 days\n    for (let i = 0; i < 14; i++) {\n      const date = new Date(today);\n      date.setDate(today.getDate() + i);\n      this.listDays.push(date);\n    }\n  }\n\n  loadMenuDebounce = this.debounceService.callDebounce(this.refreshMenu, 350, false, true);\n\n  refreshMenu(): void {\n    if (!this.selectedMenuType || !this.selectedStudent || !this.selectedOrderDate) {\n      return;\n    }\n    this.menuLoading.set(true);\n    this.currentMenu = null;\n    if (this.IsGuest) {\n      this.loadMenu();\n      return;\n    }\n    this.canteenMenuAvailableCheck();\n\n  }\n\n  canteenMenuAvailableCheck(): void {\n    const request: OrderFilterRequest = {\n      studentId: this.selectedStudent.UserId,\n      orderDate: ConvertToUniversalDateFormat(this.selectedOrderDate),\n      menuType: this.selectedMenuType,\n    };\n\n    this.orderApiService.GetOrderByStudentOrderDateAndMenuType(request).subscribe({\n      next: (res: CheckIfOrderExist) => {\n        this.processPreMenuCheck(res);\n      },\n      error: error => {\n        this.menuLoading.set(false);\n        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  processPreMenuCheck(res: CheckIfOrderExist): void {\n    if (!res) {\n      this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n      this.menuLoading.set(false);\n      return;\n    }\n\n    if (this.isSchoolClosedForCanteenOrders(res)) {\n      this.setNoMenuMessage(`Sorry, the canteen's closed right now`);\n      return;\n    }\n\n    if (this.orderAlreadyPlaced(res)) {\n      const message = `You have already placed an Order for: ${this.menuName} - ${this.getFormattedDate()}`;\n      this.setNoMenuMessage(message);\n      return;\n    }\n    this.loadMenu();\n  }\n\n  private isSchoolClosedForCanteenOrders(res: CheckIfOrderExist): boolean {\n    return res?.isSchoolClosed;\n  }\n\n  private orderAlreadyPlaced(res: CheckIfOrderExist): boolean {\n    return Boolean(res?.order);\n  }\n\n  private setNoMenuMessage(message: string): void {\n    this.noMenuMessage = message;\n    this.menuLoading.set(false);\n  }\n\n  private getFormattedDate(): string {\n    return moment(this.selectedOrderDate).format('dddd DD/MM');\n  }\n\n  private loadMenu(): void {\n    if (!this.selectedMenuType || !this.selectedStudent) {\n      return;\n    }\n\n    this.menuService.GetMenuBySchoolAndType(this.selectedStudent.SchoolId, this.selectedMenuType).subscribe({\n      next: (res: Menu[]) => {\n        this.menuLoading.set(false);\n        const menuToDisplay = this.menuDataExists(res) ? this.getMenuToDisplay(res) : null;\n        if (menuToDisplay && menuToDisplay?.MenuJSON) {\n          this.processMenuResult(menuToDisplay);\n          return;\n        }\n        this.noMenuMessage = `No ${this.menuName} Menu Available`;\n      },\n      error: error => {\n        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;\n        this.menuLoading.set(false);\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  private menuDataExists(res: Menu[]): boolean {\n    return res && res.length > 0;\n  }\n\n  private getMenuToDisplay(res: Menu[]): Menu {\n    return res[0];\n  }\n\n  processMenuResult(menuResult: Menu): void {\n    this.noMenuMessage = null;\n    this.currentMenu = menuResult;\n\n    // Set menuId and menuName from the loaded menu\n    this.menuId = this.currentMenu.MenuId;\n    this.menuName = this.currentMenu.Name;\n\n    this.menuCutOffTime = this.getMenuCutOffTime();\n\n    console.log(`[POS] Menu loaded - ID: ${this.menuId}, Name: ${this.menuName}`);\n\n    // Automatically select the first category\n    if (this.currentMenu.MenuJSON && this.currentMenu.MenuJSON.length > 0) {\n      this.SetCategory(this.currentMenu.MenuJSON[0]);\n    }\n  }\n\n  private getMenuCutOffTime(): string {\n    // Implementation for cut off time logic\n    return '';\n  }\n\n  SetCategory(category: Category): void {\n    this.currentCategory = category;\n    this.currentCategoryToDisplay = category;\n    this.FilterItems();\n\n    // Send category change message to other tabs (only from merchant view)\n    if (this.viewType === 'merchant') {\n      this.sendCategoryChangedMessage(category);\n    }\n  }\n\n  IsCurrentCategory(cat: Category): boolean {\n    return this.currentCategory?.CatName == cat.CatName;\n  }\n\n  FilterItems(): void {\n    if (!this.currentCategory) {\n      return;\n    }\n    // Apply filters to items - simplified for POS\n    this.currentCategoryToDisplay = { ...this.currentCategory };\n  }\n\n  AddToCart(item: RefinedOrderItem): void {\n    console.log(`[POS] Adding item to cart in ${this.viewType} view:`, item);\n\n    try {\n      // Convert RefinedOrderItem to CartItem\n      const cartItem: CartItem = this.convertOrderItemToCartItem(item);\n      console.log(`[POS] Converted cart item:`, cartItem);\n\n      this.store.dispatch(cartActions.addToCart({ cartItem }));\n      console.log(`[POS] Cart action dispatched successfully`);\n    } catch (error) {\n      console.error(`[POS] Error adding item to cart:`, error);\n    }\n  }\n\n  // Handle item dialog events for cross-tab communication\n  onItemDialogOpened(event: any): void {\n    if (this.viewType === 'merchant') {\n      console.log(`[POS] Item dialog opened in merchant view:`, event);\n      this.sendItemPopupOpenedMessage(event.item, event.category);\n    }\n  }\n\n  onItemDialogClosed(event: any): void {\n    if (this.viewType === 'merchant') {\n      console.log(`[POS] Item dialog closed in merchant view:`, event);\n      this.sendItemPopupClosedMessage(event.itemAdded, event.item);\n    }\n  }\n\n\n\n  removeFromCart(itemCartId: number): void {\n    console.log(`[POS] Removing item from cart: ${itemCartId}`);\n    this.store.dispatch(cartActions.removeItem({ itemCartId }));\n    // Cart update message will be sent automatically via the cart subscription\n  }\n\n  // Convert RefinedOrderItem to CartItem for POS system\n  convertOrderItemToCartItem(item: RefinedOrderItem): CartItem {\n    if (!this.selectedStudent) {\n      throw new Error('No student selected for cart item');\n    }\n\n    const options: CartOption[] = item?.SelectedOptions?.map(option => {\n      return {\n        menuItemOptionId: option.MenuItemOptionId,\n        optionName: option.OptionName,\n        optionCost: option.OptionCost,\n        parentOptionId: option.MenuItemOptionsCategoryId,\n      };\n    }) || [];\n\n    const cartItem = {\n      date: this.selectedOrderDate,\n      studentId: this.selectedStudent.UserId,\n      studentName: this.selectedStudent.FirstName,\n      schoolId: this.selectedStudent.SchoolId,\n      menuType: this.selectedMenuType,\n      menuName: this.menuName,\n      menuId: this.menuId,\n      menuCutOffDateTime: this.menuCutOffTime,\n      canteenId: this.currentMenu?.CanteenId || 0,\n      itemCartId: moment().unix(),\n      menuItemId: item.MenuItemId,\n      name: item.Name,\n      itemPriceIncGst: item.ItemPriceIncGst,\n      selectedOptions: options,\n      quantity: item.Quantity,\n    };\n\n    console.log(`[POS] Created cart item with menuId: ${cartItem.menuId}, menuName: ${cartItem.menuName}`);\n    return cartItem;\n  }\n\n  // Payment method selection\n  selectPaymentMethod(methodId: string): void {\n    this.selectedPaymentMethod.set(methodId);\n    this.showMobilePaymentSelector.set(false); // Close mobile selector if open\n    console.log(`[POS] Payment method selected: ${methodId}`);\n  }\n\n  // Mobile payment selector methods\n  openMobilePaymentSelector(): void {\n    this.showMobilePaymentSelector.set(true);\n  }\n\n  closeMobilePaymentSelector(): void {\n    this.showMobilePaymentSelector.set(false);\n  }\n\n  // Place order with Spriggy payment (only functional payment method)\n  OrderClick(): void {\n    // Validate prerequisites\n    if (!this.validateOrderPrerequisites()) {\n      return;\n    }\n\n    // Only Spriggy payment is supported in POS\n    if (this.selectedPaymentMethod() !== 'spriggy') {\n      alert('Only Spriggy payment is currently supported in the POS system.');\n      return;\n    }\n\n    // Use POS-specific place order dialog\n    const groupedCarts = [this.shoppingCart];\n\n    const dialogRef = this.dialog.open(PosPlaceOrderDialogComponent, {\n      width: '600px',\n      disableClose: true,\n      data: {\n        groupedCarts: groupedCarts,\n        editOrderId: null,\n        // POS-specific properties\n        viewType: this.viewType,\n        guid: this.guid,\n        selectedStudent: this.selectedStudent,\n        selectedMenuType: this.selectedMenuType,\n        selectedOrderDate: this.selectedOrderDate,\n      },\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result && result.success) {\n        console.log(`[POS] Order placed successfully with ID: ${result.orderId}`);\n\n        // Refresh menu to update availability\n        this.refreshMenu();\n\n        // Clear cart after successful order (merchant view only)\n        if (this.viewType === 'merchant') {\n          this.store.dispatch(cartActions.clearCart());\n\n          // Send cart cleared message to student view\n          this.sendCartClearedMessage('manual_clear');\n        }\n\n        // Order placed message is sent from within the dialog component\n        console.log(`[POS] Order placement workflow completed successfully`);\n\n        // Revert to default guest user after order completion (merchant view only)\n        if (this.viewType === 'merchant') {\n          this.revertToGuestUserAfterOrder();\n        }\n      } else if (result === true) {\n        // Handle error case\n        console.error(`[POS] Order placement failed`);\n      }\n    });\n  }\n\n  private validateOrderPrerequisites(): boolean {\n    // Check if student is selected\n    if (!this.selectedStudent) {\n      alert('Please select a student before placing an order.');\n      return false;\n    }\n\n    // Check if cart has items\n    if (!this.shoppingCart || this.shoppingCart.length === 0) {\n      alert('Your cart is empty. Please add items before placing an order.');\n      return false;\n    }\n\n    // Check if menu type and date are selected\n    if (!this.selectedMenuType || !this.selectedOrderDate) {\n      alert('Please select a menu type and order date.');\n      return false;\n    }\n\n    // Note: Guest users are allowed to place orders in POS system\n    // Payment method restrictions are handled in the payment dialog\n\n    return true;\n  }\n\n  GoBackClick(): void {\n    this.location.back();\n  }\n\n  // Helper method to determine if menu should be shown in student view\n  shouldShowMenuInStudentView(): boolean {\n    return this.viewType === 'student' && this.showMenuInStudentView();\n  }\n\n  // Image error handling\n  onLogoImageError(): void {\n    this.logoImageError.set(true);\n  }\n\n  getLogoImageSrc(): string {\n    return this.logoImageError() ? this.defaultImagePath : 'assets/images/spriggy_schools_logo.png';\n  }\n\n  getItemImageUrl(imageUrl: string): string {\n    if (!imageUrl) return this.defaultImagePath;\n\n    if (imageUrl.indexOf('https:') > -1) {\n      return imageUrl;\n    } else {\n      return environment.blobStorage + ImageUrlEnum.ItemsLG + imageUrl;\n    }\n  }\n\n  // Cross-tab communication message senders\n  private sendStudentSelectedMessage(student: UserCashless | null): void {\n    const payload: StudentSelectedPayload = { student };\n    console.log(`[POS] Merchant view sending student selected message to student view:`, {\n      studentName: student ? `${student.FirstName} ${student.Lastname}` : 'None',\n      isGuest: student?.IsGuest || false,\n      studentId: student?.UserId,\n      guid: this.guid\n    });\n    this.posCommunicationService.sendStudentSelected(payload, this.guid);\n    console.log(`[POS] Student selected message sent successfully`);\n  }\n\n  private sendGuestUserReversionMessage(guestUser: UserCashless): void {\n    console.log(`[POS] Sending guest user reversion message after order completion:`, {\n      studentName: `${guestUser.FirstName} ${guestUser.Lastname}`,\n      isGuest: guestUser.IsGuest,\n      studentId: guestUser.UserId,\n      reason: 'order_completed_reversion_to_guest'\n    });\n\n    // Use the same student selected message but with additional context for guest reversion\n    const payload: StudentSelectedPayload = { student: guestUser };\n    this.posCommunicationService.sendStudentSelected(payload, this.guid);\n    console.log(`[POS] Guest user reversion message sent to student view`);\n  }\n\n  private sendCartUpdateMessage(cartItems: CartItem[]): void {\n    const payload: CartUpdatedPayload = {\n      itemCount: cartItems.length,\n      totalAmount: GetCartItemsPrice(cartItems),\n      studentId: this.selectedStudent?.UserId,\n      cartItems: cartItems, // Send full cart items for real-time sync\n      timestamp: new Date().toISOString()\n    };\n    console.log(`[POS] Sending cart update message with ${cartItems.length} items:`, payload);\n    this.posCommunicationService.sendCartUpdated(payload, this.guid);\n  }\n\n  private sendMenuChangedMessage(): void {\n    const payload: MenuChangedPayload = {\n      menuType: this.selectedMenuType,\n      orderDate: this.selectedOrderDate,\n      studentId: this.selectedStudent?.UserId,\n      menuName: this.menuName\n    };\n    this.posCommunicationService.sendMenuChanged(payload, this.guid);\n  }\n\n  private sendOrderPlacedMessage(orderId: number, paymentMethod?: string): void {\n    if (!this.selectedStudent) return;\n\n    const payload: OrderPlacedPayload = {\n      orderId,\n      studentId: this.selectedStudent.UserId,\n      studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,\n      menuType: this.selectedMenuType,\n      orderDate: this.selectedOrderDate.toISOString(),\n      totalAmount: this.priceCart(),\n      itemCount: this.shoppingCart.length,\n      paymentMethod: paymentMethod || this.selectedPaymentMethod()\n    };\n    this.posCommunicationService.sendOrderPlaced(payload, this.guid);\n  }\n\n  private sendItemPopupOpenedMessage(item: any, category: any): void {\n    const payload: ItemPopupOpenedPayload = {\n      item,\n      date: this.selectedOrderDate,\n      category\n    };\n    console.log(`[POS] Sending item popup opened message:`, payload);\n    this.posCommunicationService.sendItemPopupOpened(payload, this.guid);\n  }\n\n\n\n  private sendItemPopupClosedMessage(itemAdded: boolean, item?: any): void {\n    const payload: ItemPopupClosedPayload = {\n      itemAdded,\n      item\n    };\n    console.log(`[POS] Sending item popup closed message:`, payload);\n    this.posCommunicationService.sendItemPopupClosed(payload, this.guid);\n  }\n\n  private sendCategoryChangedMessage(category: any): void {\n    const payload: CategoryChangedPayload = {\n      category,\n      categoryId: category.CategoryId,\n      categoryName: category.CatName\n    };\n    console.log(`[POS] Sending category changed message:`, payload);\n    this.posCommunicationService.sendCategoryChanged(payload, this.guid);\n  }\n\n  private sendBalanceUpdatedMessage(studentId: number, balance: number, studentName: string, favoriteColor?: string, isGuest?: boolean): void {\n    const payload: BalanceUpdatedPayload = {\n      studentId,\n      balance,\n      studentName,\n      favoriteColor,\n      isGuest\n    };\n    console.log(`[POS] Sending balance updated message:`, payload);\n    this.posCommunicationService.sendBalanceUpdated(payload, this.guid);\n  }\n\n  private sendCartClearedMessage(reason: 'student_changed' | 'manual_clear' | 'no_student_selected', previousStudentId?: number, newStudentId?: number): void {\n    const payload: CartClearedPayload = {\n      reason,\n      previousStudentId,\n      newStudentId\n    };\n    console.log(`[POS] Sending cart cleared message:`, payload);\n    this.posCommunicationService.sendCartCleared(payload, this.guid);\n  }\n\n  // Cross-tab communication message handlers\n  private handleOrderPlacedMessage(payload: OrderPlacedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Order placed notification received in student view for ${payload.studentName}: $${payload.totalAmount}`);\n\n      // Refresh menu if it's for the same student and menu type\n      if (this.selectedStudent?.UserId === payload.studentId &&\n        this.selectedMenuType === payload.menuType) {\n        console.log(`[POS] Refreshing menu in student view after order completion`);\n        this.refreshMenu();\n      }\n\n      // Clear any order-related UI state in student view\n      this.clearOrderRelatedState();\n\n      console.log(`[POS] ✓ Student view updated after order completion`);\n    }\n  }\n\n  /**\n   * Clear order-related state in student view after order completion\n   */\n  private clearOrderRelatedState(): void {\n    // Clear any color confirmation dialogs\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n\n    // Reset any temporary UI states\n    this.cartSyncInProgress.set(false);\n\n    console.log(`[POS] Order-related state cleared in student view`);\n  }\n\n  private handleStudentSelectedMessage(payload: StudentSelectedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Student view received student selected message:`, {\n        studentName: payload.student ? `${payload.student.FirstName} ${payload.student.Lastname}` : 'None',\n        isGuest: payload.student?.IsGuest || false,\n        studentId: payload.student?.UserId,\n        currentStudent: this.selectedStudent ? `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}` : 'None'\n      });\n\n      if (payload.student) {\n        // Clear cart display when changing students in student view\n        if (this.selectedStudent && this.selectedStudent.UserId !== payload.student.UserId) {\n          console.log(`[POS] Clearing student view cart due to student change from ${this.selectedStudent.FirstName} to ${payload.student.FirstName}`);\n          this.shoppingCart = [];\n          this.priceCart.set(0);\n          this.showMobilePlaceOrder = false;\n        }\n\n        // Set the selected student in student view\n        this.selectedStudent = payload.student;\n        this.deactivatedFilters = payload.student.SchoolDeactivatedFilters || null;\n\n        // Set IsGuest property in student view\n        this.IsGuest = payload.student.IsGuest || false;\n        console.log(`[POS] Student selected in student view - IsGuest: ${this.IsGuest}, Name: ${payload.student.FirstName} ${payload.student.Lastname}`);\n\n        // Show menu content in student view\n        this.showMenuInStudentView.set(true);\n        console.log(`[POS] Menu content shown in student view`);\n\n        // Load menu for the selected student\n        this.loadMenuDebounce();\n        console.log(`[POS] Menu loading triggered for selected student in student view`);\n\n        console.log(`[POS] ✓ Student successfully synchronized in student view: ${payload.student.FirstName} ${payload.student.Lastname}`);\n      } else {\n        console.log(`[POS] No student selected - clearing student view UI`);\n\n        // Hide menu content and clear all UI elements if no student selected\n        this.showMenuInStudentView.set(false);\n        this.selectedStudent = null;\n        this.currentMenu = null;\n        this.currentCategory = null;\n        this.currentCategoryToDisplay = null;\n        this.IsGuest = false;\n\n        // Clear color confirmation dialog\n        this.showColorConfirmation.set(false);\n        this.studentFavoriteColor.set(null);\n        this.colorConfirmationPending.set(false);\n\n        // Clear balance information\n        this.parentBalance.set(null);\n        this.balanceLoading.set(false);\n        this.balanceError.set(null);\n\n        // Clear any active filter states\n        this.deactivatedFilters = null;\n\n        // Clear cart display when no student is selected\n        this.shoppingCart = [];\n        this.priceCart.set(0);\n        this.showMobilePlaceOrder = false;\n\n        console.log('[POS] ✓ Student view cleared - no student selected');\n      }\n    }\n  }\n\n  private handleMenuChangedMessage(payload: MenuChangedPayload): void {\n    if (this.viewType === 'student') {\n      // Update menu type and date in student view\n      if (this.selectedMenuType !== payload.menuType ||\n        this.selectedOrderDate.getTime() !== payload.orderDate.getTime()) {\n        this.selectedMenuType = payload.menuType;\n        this.selectedOrderDate = payload.orderDate;\n        this.orderingForm.patchValue({\n          menuType: payload.menuType,\n          orderDate: payload.orderDate\n        });\n        console.log(`Menu changed to: ${payload.menuType} for ${payload.orderDate}`);\n      }\n    }\n  }\n\n  private handleCartUpdatedMessage(payload: CartUpdatedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Cart updated in student view: ${payload.itemCount} items, total: $${payload.totalAmount}`);\n      console.log(`[POS] Received cart items:`, payload.cartItems);\n\n      // Show sync in progress\n      this.cartSyncInProgress.set(true);\n\n      // Update student view cart display with merchant's cart items\n      this.shoppingCart = payload.cartItems || [];\n      this.priceCart.set(payload.totalAmount);\n      this.showMobilePlaceOrder = payload.itemCount > 0;\n      this.lastCartSyncTime.set(new Date().toLocaleTimeString());\n\n      console.log(`[POS] Student view cart synchronized with ${this.shoppingCart.length} items`);\n\n      // Hide sync indicator after a brief moment\n      setTimeout(() => {\n        this.cartSyncInProgress.set(false);\n      }, 1000);\n    }\n  }\n\n  private handleViewRefreshMessage(payload: ViewRefreshPayload): void {\n    console.log(`View refresh requested: ${payload.reason}`);\n    // Refresh current view based on reason\n    if (payload.reason === 'order_completed') {\n      this.refreshMenu();\n    }\n  }\n\n  private handleItemPopupOpenedMessage(payload: ItemPopupOpenedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Item popup opened in student view:`, payload.item.Name);\n      this.studentPopupData.set(payload);\n      this.showStudentPopup.set(true);\n    }\n  }\n\n  private handleItemPopupClosedMessage(payload: ItemPopupClosedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Item popup closed in student view. Item added:`, payload.itemAdded);\n      this.showStudentPopup.set(false);\n      this.studentPopupData.set(null);\n    }\n  }\n\n  private handleCategoryChangedMessage(payload: CategoryChangedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Category changed in student view:`, payload.categoryName);\n      // Find the category in the current menu and set it\n      if (this.currentMenu?.MenuJSON) {\n        const category = this.currentMenu.MenuJSON.find(cat => cat.CategoryId === payload.categoryId);\n        if (category) {\n          this.SetCategory(category);\n          console.log(`[POS] Student view switched to category: ${payload.categoryName}`);\n        }\n      }\n    }\n  }\n\n  private handleBalanceUpdatedMessage(payload: BalanceUpdatedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Balance updated in student view for student ${payload.studentName}: $${payload.balance}, IsGuest: ${payload.isGuest}`);\n\n      // Update balance information in student view\n      if (this.selectedStudent?.UserId === payload.studentId) {\n        this.parentBalance.set(payload.balance);\n        this.balanceLoading.set(false);\n        this.balanceError.set(null);\n\n        // Update guest status\n        if (payload.isGuest !== undefined) {\n          this.IsGuest = payload.isGuest;\n        }\n\n        // Update favorite color if provided\n        if (payload.favoriteColor) {\n          this.studentFavoriteColor.set(payload.favoriteColor);\n        }\n\n        console.log(`[POS] Student view balance synchronized: $${payload.balance}, IsGuest: ${this.IsGuest}`);\n      }\n    }\n  }\n\n  private handleCartClearedMessage(payload: CartClearedPayload): void {\n    if (this.viewType === 'student') {\n      console.log(`[POS] Cart cleared in student view. Reason: ${payload.reason}`);\n\n      // Clear cart display in student view\n      this.shoppingCart = [];\n      this.priceCart.set(0);\n      this.showMobilePlaceOrder = false;\n\n      console.log(`[POS] Student view cart cleared due to: ${payload.reason}`);\n    }\n  }\n\n  private async checkAuthenticationAndLoadGuestUser(): Promise<void> {\n    try {\n      const isAuthenticated = await this.authService.IsAuthenticated();\n      const connectedUser = this.userService.GetUserConnected();\n\n      if (!isAuthenticated || !connectedUser) {\n        console.error(`[POS] User not authenticated. Cannot load guest user or access balance API.`);\n        this.balanceError.set('Authentication required. Please log in to access POS features.');\n        return;\n      }\n\n      console.log(`[POS] User authenticated:`, connectedUser.Email, `Role:`, connectedUser.Role);\n      console.log(`[POS] Auth token available:`, !!this.authService.GetToken());\n\n      // Only load guest user if authenticated\n      this.loadDefaultGuestUser();\n    } catch (error) {\n      console.error(`[POS] Authentication check failed:`, error);\n      this.balanceError.set('Authentication check failed. Please refresh and try again.');\n    }\n  }\n\n  private loadDefaultGuestUser(): void {\n    console.log(`[POS] Loading default guest user...`);\n\n    // Search for guest user using the existing search functionality\n    const filters: ArrayFilter = {\n      Filter: 'guest',\n      NumberRows: 10,\n      PageIndex: 0,\n      FilterId: 0,\n      SortBy: '',\n      SortDirection: '',\n      MultipleFilterId: this.schoolId.toString(),\n      Role: '',\n      MerchantId: 0\n    };\n\n    this.userService.GetUsersWithFilterAPI(filters).subscribe({\n      next: (response: any) => {\n        const users = response.Users || [];\n        if (users.length > 0) {\n          const guestUser = users[0]; // Select first guest user found\n          console.log(`[POS] Default guest user found:`, guestUser);\n\n          // Automatically select the guest user in the dropdown\n          this.autoSelectGuestUser(guestUser);\n        } else {\n          console.log(`[POS] No guest user found for school ${this.schoolId}`);\n          this.balanceError.set('No guest user found');\n        }\n      },\n      error: error => {\n        console.error(`[POS] Error loading default guest user:`, error);\n        this.balanceError.set('Failed to load default guest user');\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  private loadDefaultGuestUserAfterOrder(): void {\n    console.log(`[POS] Loading default guest user after order completion...`);\n\n    // Search for guest user using the existing search functionality\n    const filters: ArrayFilter = {\n      Filter: 'guest',\n      NumberRows: 10,\n      PageIndex: 0,\n      FilterId: 0,\n      SortBy: '',\n      SortDirection: '',\n      MultipleFilterId: this.schoolId.toString(),\n      Role: '',\n      MerchantId: 0\n    };\n\n    this.userService.GetUsersWithFilterAPI(filters).subscribe({\n      next: (response: any) => {\n        const users = response.Users || [];\n        if (users.length > 0) {\n          const guestUser = users[0]; // Select first guest user found\n          console.log(`[POS] Default guest user found for reversion:`, guestUser);\n\n          // Automatically select the guest user in the dropdown\n          this.autoSelectGuestUser(guestUser);\n\n          // Send a specific message about guest user reversion for better tracking\n          this.sendGuestUserReversionMessage(guestUser);\n        } else {\n          console.log(`[POS] No guest user found for reversion for school ${this.schoolId}`);\n          this.balanceError.set('No guest user found for reversion');\n        }\n      },\n      error: error => {\n        console.error(`[POS] Error loading default guest user for reversion:`, error);\n        this.balanceError.set('Failed to load default guest user for reversion');\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  private autoSelectGuestUser(guestUser: UserCashless): void {\n    console.log(`[POS] Auto-selecting guest user in ${this.viewType} view:`, guestUser.FirstName, guestUser.Lastname);\n\n    // Set today's date as the default order date - use the exact date from listDays\n    const todayDate = this.findTodayInDatesList();\n    if (todayDate) {\n      this.selectedOrderDate = todayDate;\n      this.orderingForm.get('orderDate')?.setValue(todayDate, { emitEvent: false });\n      console.log(`[POS] Order date set to today:`, todayDate);\n    } else {\n      console.warn(`[POS] Today's date not found in listDays, using first available date`);\n      if (this.listDays.length > 0) {\n        this.selectedOrderDate = this.listDays[0];\n        this.orderingForm.get('orderDate')?.setValue(this.listDays[0], { emitEvent: false });\n      }\n    }\n\n    // Add a small delay to ensure cross-tab communication is ready\n    setTimeout(() => {\n      console.log(`[POS] Triggering guest user selection after delay...`);\n\n      // Programmatically select the guest user in the dropdown\n      if (this.studentSearchDropdown) {\n        this.studentSearchDropdown.selectStudent(guestUser);\n        console.log(`[POS] Guest user selected via dropdown component`);\n      } else {\n        // If ViewChild is not ready yet, trigger the selection manually\n        console.log(`[POS] Dropdown not ready, triggering manual selection`);\n        this.onStudentSelected(guestUser);\n      }\n    }, 500); // 500ms delay to ensure cross-tab communication is established\n  }\n\n\n\n  private findTodayInDatesList(): Date | null {\n    const today = new Date();\n    const todayDateString = today.toDateString(); // Compare date strings to ignore time\n\n    return this.listDays.find(date => date.toDateString() === todayDateString) || null;\n  }\n\n  /**\n   * Revert to default guest user after order completion to reset POS to neutral state\n   */\n  private revertToGuestUserAfterOrder(): void {\n    console.log(`[POS] Reverting to default guest user after order completion...`);\n\n    // Store the current student information for logging\n    const currentStudent = this.selectedStudent;\n\n    if (!currentStudent) {\n      console.log(`[POS] No student was selected, loading default guest user anyway`);\n    } else if (currentStudent.IsGuest) {\n      console.log(`[POS] Current user is already a guest - no reversion needed`);\n      return;\n    } else {\n      console.log(`[POS] Reverting from student: ${currentStudent.FirstName} ${currentStudent.Lastname} (ID: ${currentStudent.UserId}) back to guest user`);\n    }\n\n    // Add a small delay to ensure all post-order cleanup is complete\n    setTimeout(() => {\n      // Reset order date to today\n      this.resetOrderDateToToday();\n\n      // Load and select the default guest user\n      this.loadDefaultGuestUserAfterOrder();\n\n      console.log(`[POS] ✓ Reversion to guest user initiated`);\n    }, 1000); // 1 second delay to ensure cleanup is complete\n  }\n\n  /**\n   * Reset the order date to today's date for the next order\n   */\n  private resetOrderDateToToday(): void {\n    const todayDate = this.findTodayInDatesList();\n    if (todayDate) {\n      this.selectedOrderDate = todayDate;\n      this.orderingForm.get('orderDate')?.setValue(todayDate, { emitEvent: false });\n      console.log(`[POS] Order date reset to today: ${todayDate}`);\n    } else {\n      console.warn(`[POS] Today's date not found in listDays, keeping current date`);\n    }\n  }\n\n\n  IsGuest: boolean = false;\n  private async loadUserDetailsAndBalance(student: UserCashless): Promise<void> {\n    if (!student?.UserId) {\n      this.balanceError.set('Student ID not found');\n      return;\n    }\n\n    // Check authentication before making API calls\n    const isAuthenticated = await this.authService.IsAuthenticated();\n    const connectedUser = this.userService.GetUserConnected();\n\n    if (!isAuthenticated || !connectedUser) {\n      this.balanceError.set('Authentication required. Please log in.');\n      console.error(`[POS] User not authenticated for balance API call`);\n      return;\n    }\n\n    console.log(`[POS] Loading user details for student ID: ${student.UserId}`);\n    console.log(`[POS] Authenticated user:`, connectedUser.Email, `Role:`, connectedUser.Role);\n    this.balanceLoading.set(true);\n    this.balanceError.set(null);\n\n    // Call GetUsersDetails API first\n    this.userService.GetUserDetailsById(student.UserId).subscribe({\n      next: (userDetails: UserCashless) => {\n        console.log(`[POS] User details loaded:`, userDetails);\n        if (userDetails?.ExternalUserId) {\n          // Update the selected student with complete details\n          this.selectedStudent = userDetails;\n\n          // Extract balance from user details\n          const balance = parseFloat(userDetails.Parents[0].SpriggyBalance);\n          this.balanceLoading.set(false);\n          this.parentBalance.set(balance);\n\n          // Update IsGuest property from detailed user data (in case it changed)\n          this.IsGuest = userDetails.IsGuest;\n\n          // Send balance update message to student view (only from merchant view)\n          if (this.viewType === 'merchant') {\n            this.sendBalanceUpdatedMessage(\n              userDetails.UserId,\n              balance,\n              `${userDetails.FirstName} ${userDetails.Lastname}`,\n              userDetails.FavouriteColour,\n              userDetails.IsGuest\n            );\n          }\n\n          if (userDetails.FavouriteColour) {\n            this.studentFavoriteColor.set(userDetails.FavouriteColour);\n            this.showColorConfirmation.set(true);\n            console.log(`[POS] Showing color confirmation for ${userDetails.FirstName}. Favorite color: ${userDetails.FavouriteColour}`);\n          } else {\n            // No favorite color set\n            console.log(`[POS] No favorite color set for ${userDetails.FirstName}, balance loaded: $${balance}`);\n          }\n        } else {\n          this.balanceLoading.set(false);\n          this.balanceError.set('External User ID not found in user details');\n          console.error(`[POS] ExternalUserId not found in user details for student: ${student.FirstName}`);\n        }\n      },\n      error: error => {\n        this.balanceLoading.set(false);\n        this.balanceError.set('Failed to load user details');\n        console.error(`[POS] GetUsersDetails API error:`, error);\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  // Color confirmation methods\n  confirmColorIdentity(): void {\n    console.log(`[POS] Color identity confirmed for student`);\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n\n    // Send balance update message after color confirmation (only from merchant view)\n    if (this.viewType === 'merchant' && this.selectedStudent && this.parentBalance() !== null) {\n      this.sendBalanceUpdatedMessage(\n        this.selectedStudent.UserId,\n        this.parentBalance(),\n        `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,\n        this.studentFavoriteColor(),\n        this.IsGuest\n      );\n    }\n  }\n\n  cancelColorConfirmation(): void {\n    console.log(`[POS] Color confirmation cancelled`);\n    this.showColorConfirmation.set(false);\n    this.colorConfirmationPending.set(false);\n    this.balanceLoading.set(false);\n    this.balanceError.set('Identity confirmation cancelled');\n\n    // Clear selected student\n    this.selectedStudent = null;\n    this.parentBalance.set(null);\n\n    // Clear the dropdown selection\n    if (this.studentSearchDropdown) {\n      this.studentSearchDropdown.clearSelection();\n    }\n  }\n\n\n\n\n\n  private unsubscribeAll(): void {\n    if (this.subscriptionShoppingCart$) {\n      this.subscriptionShoppingCart$.unsubscribe();\n    }\n    if (this.subscriptionItemsFilters$) {\n      this.subscriptionItemsFilters$.unsubscribe();\n    }\n    if (this.subscriptionCrossTabMessages$) {\n      this.subscriptionCrossTabMessages$.unsubscribe();\n    }\n  }\n}\n", "<!-- Read-only overlay for entire student view -->\n<div *ngIf=\"this.viewType === 'student'\" class=\"readonly-overlay\"></div>\n\n<!-- Read-only banner for student view -->\n<!-- <div *ngIf=\"this.viewType === 'student'\" class=\"readonly-banner\">\n  <div class=\"readonly-banner-content\">\n    <mat-icon>visibility</mat-icon>\n    <span class=\"readonly-text\">READ-ONLY VIEW</span>\n    <span class=\"readonly-description\">You are observing the merchant's actions. This screen is for viewing only.</span>\n  </div>\n</div> -->\n\n<div class=\"top-header\" *ngIf=\"this.viewType == 'student'\">\n  <div class=\"logo-container\">\n    <img [src]=\"getLogoImageSrc()\" alt=\"Spriggy Schools Logo\" (error)=\"onLogoImageError()\">\n  </div>\n\n  <!-- Cross-tab sync status -->\n  <!-- <div class=\"sync-status-container\">\n    <div class=\"sync-indicator\" [class.connected]=\"isConnectedToOtherTabs()\" [class.disconnected]=\"!isConnectedToOtherTabs()\">\n      <mat-icon>{{ isConnectedToOtherTabs() ? 'sync' : 'sync_disabled' }}</mat-icon>\n      <span class=\"sync-text\">\n        {{ isConnectedToOtherTabs() ? 'Synced with Merchant' : 'Not Connected' }}\n      </span>\n      <small *ngIf=\"lastSyncTime()\" class=\"last-sync\">\n        Last sync: {{ lastSyncTime() | date:'HH:mm:ss' }}\n      </small>\n    </div>\n  </div> -->\n\n  <!-- <div class=\"search-container\">\n      <mat-form-field appearance=\"outline\">\n        <input matInput placeholder=\"Search Student\" (input)=\"onSearchInput($event)\">\n        <mat-icon matSuffix>search</mat-icon>\n      </mat-form-field>\n    </div> -->\n</div>\n<div class=\"pos-tab-container\" [class.readonly-mode]=\"this.viewType === 'student'\">\n\n\n  <br>\n  <br>\n  <!-- Filters Section -->\n  <!-- Loading State -->\n\n\n  <div class=\"row\">\n    <div class=\"col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1\">\n      <!-- Cross-tab sync status for merchant -->\n      <!-- <div class=\"merchant-sync-status mb-3\" *ngIf=\"isConnectedToOtherTabs()\">\n        <div class=\"alert alert-info d-flex align-items-center\">\n          <mat-icon class=\"me-2\">sync</mat-icon>\n          <span>Connected to Student View</span>\n          <small *ngIf=\"lastSyncTime()\" class=\"ms-auto\">\n            Last sync: {{ lastSyncTime() | date:'HH:mm:ss' }}\n          </small>\n        </div>\n      </div> -->\n\n      <mat-card class=\"filters-card\" *ngIf=\"this.viewType == 'merchant'\">\n        <mat-card-content>\n          <form [formGroup]=\"orderingForm\" class=\"ordering-form\">\n            <div class=\"row\">\n              <!-- Student Selection -->\n              <div class=\"col-12 col-md-4 mb-3\">\n                <student-search-dropdown #studentSearchDropdown placeholder=\"Search for a student...\"\n                  [schoolId]=\"schoolId\" (studentSelected)=\"onStudentSelected($event)\"></student-search-dropdown>\n              </div>\n\n              <!-- Menu Type Selection -->\n              <div class=\"col-12 col-md-4 mb-3\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Ordering Type</mat-label>\n                  <mat-select formControlName=\"menuType\">\n                    <mat-option *ngFor=\"let menuOption of menuPickerData\" [value]=\"menuOption.menuType\">\n                      {{ menuOption.menuName }}\n                    </mat-option>\n                  </mat-select>\n                </mat-form-field>\n              </div>\n\n              <!-- Date Selection -->\n              <div class=\"col-12 col-md-4 mb-3\">\n                <mat-form-field appearance=\"outline\" class=\"full-width\">\n                  <mat-label>Order Date</mat-label>\n                  <mat-select formControlName=\"orderDate\">\n                    <mat-option *ngFor=\"let day of listDays\" [value]=\"day\">\n                      {{ day | date : 'EEEE d MMM' }}\n                    </mat-option>\n                  </mat-select>\n                </mat-form-field>\n              </div>\n            </div>\n          </form>\n          <!-- Favorite Color Confirmation -->\n          <div *ngIf=\"showColorConfirmation() && !IsGuest\" class=\"col-12 mb-3\">\n            <div class=\"color-confirmation-card\">\n              <div class=\"color-confirmation-header\">\n                <mat-icon>security</mat-icon>\n                <h6>Ask the child's favourite colour to confirm their identity</h6>\n              </div>\n              <div class=\"color-confirmation-content\">\n                <div class=\"color-circle\" [style.background-color]=\"studentFavoriteColor()\"></div>\n                <!-- <div class=\"color-confirmation-actions\">\n                      <button mat-raised-button color=\"primary\" (click)=\"confirmColorIdentity()\" class=\"confirm-btn\">\n                        <mat-icon>check</mat-icon>\n                        Confirmed\n                      </button>\n                      <button mat-raised-button color=\"warn\" (click)=\"cancelColorConfirmation()\" class=\"cancel-btn\">\n                        <mat-icon>close</mat-icon>\n                        Cancel\n                      </button>\n                    </div> -->\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n      <div\n        *ngIf=\"menuLoading() && (this.viewType === 'merchant' || (this.viewType === 'student' && showMenuInStudentView())); else result\"\n        class=\"col-12 d-flex justify-content-center pt-4\">\n        <app-spinner [manual]=\"true\"></app-spinner>\n      </div>\n      <!-- Results Section -->\n      <ng-template #result>\n        <!-- Student view waiting message -->\n        <div *ngIf=\"this.viewType === 'student' && !showMenuInStudentView()\" class=\"row\">\n          <div class=\"col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1\">\n            <mat-card class=\"waiting-card\">\n              <mat-card-content class=\"text-center\">\n                <mat-icon class=\"waiting-icon\">person_search</mat-icon>\n                <h4 class=\"waiting-title\">Waiting for Student Selection</h4>\n                <p class=\"waiting-message\">\n                  Please select a student from the merchant view to begin ordering.\n                </p>\n                <!-- <div class=\"sync-status-inline\" *ngIf=\"isConnectedToOtherTabs()\">\n              <mat-icon class=\"sync-icon\">sync</mat-icon>\n              <span>Connected to Merchant View</span>\n            </div> -->\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- Show content based on view type and student selection -->\n        <!-- Merchant view: Show content if student is selected, otherwise show no student message -->\n        <div *ngIf=\"this.viewType === 'merchant' && selectedStudent\">\n          <!-- No Menu Message -->\n          <div *ngIf=\"noMenuMessage; else merchantMenu\">\n            <div class=\"row\">\n              <div class=\"col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1\">\n                <mat-card class=\"no-menu-card\">\n                  <mat-card-content>\n                    <p class=\"no-menu-message\">{{ noMenuMessage }}</p>\n                  </mat-card-content>\n                </mat-card>\n              </div>\n            </div>\n          </div>\n\n          <!-- Merchant Menu Display -->\n          <ng-template #merchantMenu>\n            <ng-container *ngTemplateOutlet=\"sharedMenuTemplate\"></ng-container>\n          </ng-template>\n        </div> <!-- Close conditional div for merchant with selected student -->\n\n        <!-- Student view: Show content if student is selected -->\n        <div *ngIf=\"this.viewType === 'student' && showMenuInStudentView()\">\n          <!-- No Menu Message -->\n          <div *ngIf=\"noMenuMessage; else studentMenu\">\n            <div class=\"row\">\n              <div class=\"col-12 col-md-8 col-lg-8 col-xl-7 offset-xl-1\">\n                <mat-card class=\"no-menu-card\">\n                  <mat-card-content>\n                    <p class=\"no-menu-message\">{{ noMenuMessage }}</p>\n                  </mat-card-content>\n                </mat-card>\n              </div>\n            </div>\n          </div>\n\n          <!-- Student Menu Display -->\n          <ng-template #studentMenu>\n            <ng-container *ngTemplateOutlet=\"sharedMenuTemplate\"></ng-container>\n          </ng-template>\n        </div> <!-- Close conditional div for student with selected student -->\n\n\n      </ng-template>\n\n    </div>\n    <!-- Shopping Cart Sidebar -->\n    <div class=\"col-12 col-md-4 col-lg-4 col-xl-3\">\n      <div class=\"shopping-cart-sidebar\">\n        <mat-card class=\"cart-card\">\n          <mat-card-header>\n            <mat-card-title>\n              Shopping Cart\n              <!-- <span *ngIf=\"this.viewType === 'student'\" class=\"cart-sync-indicator\">\n                      <mat-icon [class.syncing]=\"cartSyncInProgress()\">sync</mat-icon>\n                      <span class=\"sync-text\">\n                        <span *ngIf=\"cartSyncInProgress()\">Syncing...</span>\n                        <span *ngIf=\"!cartSyncInProgress() && lastCartSyncTime()\">\n                          Synced {{ lastCartSyncTime() }}\n                        </span>\n                        <span *ngIf=\"!cartSyncInProgress() && !lastCartSyncTime()\">\n                          Synced from Merchant\n                        </span>\n                      </span>\n                    </span> -->\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <!-- Cart sync notification for student view -->\n            <div *ngIf=\"this.viewType === 'student' && cartSyncInProgress()\" class=\"cart-sync-notification\">\n              <mat-icon>sync</mat-icon>\n              <span>Cart updating from merchant...</span>\n            </div>\n\n            <div *ngIf=\"shoppingCart.length === 0\" class=\"empty-cart\">\n              <p *ngIf=\"this.viewType === 'merchant'\">Your cart is empty</p>\n              <p *ngIf=\"this.viewType === 'student'\">Merchant's cart is empty</p>\n            </div>\n\n            <!-- Parent Balance Section -->\n            <div *ngIf=\"selectedStudent && !IsGuest\" class=\"balance-section\" style=\"margin-bottom: 13px;\">\n              <div *ngIf=\"balanceLoading()\" class=\"balance-loading\">\n                <mat-icon class=\"loading-icon\">hourglass_empty</mat-icon>\n                <span>Loading balance...</span>\n              </div>\n              <div *ngIf=\"!balanceLoading() && parentBalance() !== null\" class=\"balance-display\">\n                <mat-icon>account_balance_wallet</mat-icon>\n                <span>Remaining balance in parent wallet: {{ parentBalance() | currency }}</span>\n              </div>\n              <div *ngIf=\"!balanceLoading() && balanceError()\" class=\"balance-error\">\n                <mat-icon>error</mat-icon>\n                <span>{{ balanceError() }}</span>\n              </div>\n            </div>\n\n            <div *ngIf=\"shoppingCart.length > 0\" class=\"cart-items\">\n              <div *ngFor=\"let item of shoppingCart\" class=\"cart-item\">\n                <div class=\"item-info\">\n                  <h6 class=\"item-name\">{{ item.name }}</h6>\n                  <p class=\"item-details\">\n                    <span *ngFor=\"let option of item.selectedOptions\">\n                      {{ option.optionName }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"item-actions\">\n                  <span class=\"item-price\">{{ item.itemPriceIncGst | currency }}</span>\n                  <span class=\"item-quantity\" *ngIf=\"item.quantity > 1\">x{{ item.quantity }}</span>\n                  <button *ngIf=\"this.viewType === 'merchant'\" mat-icon-button\n                    (click)=\"removeFromCart(item.itemCartId)\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                  <span *ngIf=\"this.viewType === 'student'\" class=\"readonly-indicator\">\n                    <mat-icon>visibility</mat-icon>\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"cart-total\">\n                <h5>Total: {{ priceCart() | currency }}</h5>\n              </div>\n\n\n            </div>\n          </mat-card-content>\n          <mat-card-actions *ngIf=\"shoppingCart.length > 0\" class=\"payment-section\">\n            <!-- Payment Methods Section (Merchant Only) -->\n            <!-- <div  class=\"payment-methods-container\">\n                    <mat-card-title style=\"font-size:17px\" class=\"mat-mdc-card-title\">Payment</mat-card-title>\n\n                   \n                    <button\n                      mat-raised-button\n                      [class]=\"selectedPaymentMethod() === 'spriggy' ? 'payment-btn primary-payment selected' : 'payment-btn primary-payment'\"\n                      (click)=\"selectPaymentMethod('spriggy')\">\n                      <mat-icon>credit_card</mat-icon>\n                      <span>Pay with Spriggy Card / Wallet ({{ priceCart() | currency }})</span>\n                    </button>\n\n                  \n                    <div class=\"secondary-payment-row\">\n                      <button\n                        mat-stroked-button\n                        [class]=\"selectedPaymentMethod() === 'stripe' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                        data-payment=\"stripe\"\n                        (click)=\"selectPaymentMethod('stripe')\"\n                        [disabled]=\"false\">\n                        <mat-icon>payment</mat-icon>\n                        <span>Pay with Stripe</span>\n                      </button>\n\n                      <button\n                        mat-stroked-button\n                        [class]=\"selectedPaymentMethod() === 'cash' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                        data-payment=\"cash\"\n                        (click)=\"selectPaymentMethod('cash')\"\n                        [disabled]=\"false\">\n                        <mat-icon>money</mat-icon>\n                        <span>Pay Cash</span>\n                      </button>\n                    </div>\n\n                   \n                    <div class=\"secondary-payment-row\">\n                      <button\n                        mat-stroked-button\n                        [class]=\"selectedPaymentMethod() === 'applepay' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                        data-payment=\"applepay\"\n                        (click)=\"selectPaymentMethod('applepay')\"\n                        [disabled]=\"false\">\n                        <mat-icon>phone_iphone</mat-icon>\n                        <span>Apple Pay</span>\n                      </button>\n\n                      <button\n                        mat-stroked-button\n                        [class]=\"selectedPaymentMethod() === 'visa' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                        data-payment=\"visa\"\n                        (click)=\"selectPaymentMethod('visa')\"\n                        [disabled]=\"false\">\n                        <mat-icon>credit_card</mat-icon>\n                        <span>Visa</span>\n                      </button>\n                    </div>\n\n                  \n                    <button\n                      mat-raised-button\n                      class=\"final-place-order-btn\"\n                      (click)=\"OrderClick()\">\n                      Place Order ({{ priceCart() | currency }})\n                    </button>\n                  </div> -->\n            <!-- Payment Container -->\n            <div class=\"payment-methods-container\">\n              <mat-card-title style=\"font-size:17px\" class=\"mat-mdc-card-title\">Payment</mat-card-title>\n\n              <!-- Spriggy Payment (Only functional payment method for authenticated users) -->\n              <div class=\"row\" *ngIf=\"!IsGuest\">\n                <div class=\"col-12\">\n                  <button mat-raised-button class=\"payment-btn primary-payment selected\" (click)=\"OrderClick()\">\n                    <mat-icon>credit_card</mat-icon>\n                    <span>Pay with Spriggy Card ({{ priceCart() | currency }})</span>\n                  </button>\n                </div>\n              </div>\n\n              <!-- Guest Payment Option -->\n              <div class=\"row\" *ngIf=\"IsGuest\">\n                <div class=\"col-12\">\n                  <button mat-raised-button class=\"payment-btn guest-payment selected\" (click)=\"GuestOrderClick()\">\n                    <mat-icon>payment</mat-icon>\n                    <span>Pay with Card ({{ priceCart() | currency }})</span>\n                  </button>\n                </div>\n              </div>\n\n              <!-- Secondary Payment Options Row 1 -->\n              <div class=\"row\">\n                <div class=\"col-lg-6 col-md-12\">\n                  <button mat-stroked-button disabled\n                    [class]=\"selectedPaymentMethod() === 'stripe' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                    data-payment=\"stripe\" (click)=\"selectPaymentMethod('stripe')\">\n                    <mat-icon>payment</mat-icon>\n                    <span>Pay with Stripe</span>\n                  </button>\n                </div>\n                <div class=\"col-lg-6 col-md-12\" style=\"padding-left: 0px;\">\n                  <button mat-stroked-button disabled\n                    [class]=\"selectedPaymentMethod() === 'cash' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                    data-payment=\"cash\" (click)=\"selectPaymentMethod('cash')\">\n                    <mat-icon>money</mat-icon>\n                    <span>Pay Cash</span>\n                  </button>\n                </div>\n              </div>\n\n              <!-- Secondary Payment Options Row 2 -->\n              <div class=\"row\">\n                <div class=\"col-lg-6 col-md-12\">\n                  <button mat-stroked-button disabled\n                    [class]=\"selectedPaymentMethod() === 'applepay' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                    data-payment=\"applepay\" (click)=\"selectPaymentMethod('applepay')\">\n                    <mat-icon>phone_iphone</mat-icon>\n                    <span>Apple Pay</span>\n                  </button>\n                </div>\n                <div class=\"col-lg-6 col-md-12\" style=\"padding-left: 0px;\">\n                  <button mat-stroked-button disabled\n                    [class]=\"selectedPaymentMethod() === 'visa' ? 'payment-btn secondary-payment selected' : 'payment-btn secondary-payment'\"\n                    data-payment=\"visa\" (click)=\"selectPaymentMethod('visa')\">\n                    <mat-icon>credit_card</mat-icon>\n                    <span>Visa</span>\n                  </button>\n                </div>\n              </div>\n\n              <!-- Final Place Order Button -->\n              <div class=\"row\">\n                <div class=\"col-12\">\n                  <button mat-raised-button class=\"final-place-order-btn\">\n                    Place Order ({{ priceCart() | currency }})\n                  </button>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-12\">\n                  <div *ngIf=\"this.viewType === 'student'\" class=\"readonly-order-info\">\n                    <mat-icon>visibility</mat-icon>\n                    <span>Merchant will place order: {{ priceCart() | currency }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n\n\n  <!-- Mobile Balance Display -->\n  <div *ngIf=\"selectedStudent && parentBalance() !== null && this.viewType === 'merchant' && !IsGuest\"\n    class=\"mobile-balance-display d-block d-md-none\">\n    <div class=\"mobile-balance-content\">\n      <mat-icon>account_balance_wallet</mat-icon>\n      <span>Remaining balance: {{ parentBalance() | currency }}</span>\n    </div>\n  </div>\n\n  <!-- Mobile Place Order Button (Merchant Only) -->\n  <div *ngIf=\"showMobilePlaceOrder && this.viewType === 'merchant'\" class=\"mobile-place-order d-block d-md-none\">\n    <div class=\"mobile-payment-container\">\n      <!-- Selected Payment Method Indicator -->\n      <div class=\"mobile-payment-indicator\">\n        <mat-icon>credit_card</mat-icon>\n        <span style=\"font-size: 0.9rem!important;\" *ngIf=\"!IsGuest\">Pay with Spriggy Card</span>\n        <span style=\"font-size: 0.9rem!important;\" *ngIf=\"IsGuest\">Guest User - Cannot Order</span>\n      </div>\n\n      <!-- Place Order FAB -->\n      <button mat-fab color=\"primary\" (click)=\"OrderClick()\" class=\"fab-place-order\">\n        <mat-icon>shopping_cart</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Mobile Payment Method Selector - Removed since only Spriggy is supported -->\n\n  <!-- Mobile Read-only Indicator (Student Only) -->\n  <div *ngIf=\"showMobilePlaceOrder && this.viewType === 'student' && showMenuInStudentView()\"\n    class=\"mobile-readonly-indicator d-block d-md-none\">\n    <div class=\"readonly-fab\">\n      <mat-icon>visibility</mat-icon>\n      <span class=\"cart-total\">{{ priceCart() | currency }}</span>\n    </div>\n  </div>\n\n  <!-- Student View Popup (Read-only) -->\n  <div *ngIf=\"showStudentPopup() && this.viewType === 'student'\" class=\"student-popup-overlay\">\n    <div class=\"student-popup-content\">\n      <div class=\"popup-header\">\n        <!-- <span class=\"merchant-indicator\">Merchant View</span> -->\n        <h3>Item Selected</h3>\n      </div>\n\n      <div *ngIf=\"studentPopupData()\" class=\"popup-body\">\n        <img *ngIf=\"studentPopupData().item.Images && studentPopupData().item.Images.length > 0\"\n          [src]=\"getItemImageUrl(studentPopupData().item.Images[0].ImageUrl)\" [alt]=\"studentPopupData().item.Name\"\n          class=\"popup-item-image\" (error)=\"$event.target.src = defaultImagePath\" />\n\n        <div class=\"popup-item-details\">\n          <div class=\"item-name\">{{ studentPopupData().item.Name }}</div>\n          <div class=\"item-price\">${{ studentPopupData().item.Price | number:'1.2-2' }}</div>\n          <div *ngIf=\"studentPopupData().item.Description\" class=\"item-description\">\n            {{ studentPopupData().item.Description }}\n          </div>\n        </div>\n\n        <!-- <div class=\"readonly-notice\">\n          <mat-icon style=\"vertical-align: middle; margin-right: 0.5rem;\">visibility</mat-icon>\n          This is a read-only view. The merchant is selecting this item.\n        </div> -->\n      </div>\n    </div>\n  </div>\n\n  <!-- Shared Menu Template -->\n  <ng-template #sharedMenuTemplate>\n    <!-- Category Filters Section - Only show when student is selected -->\n    <div *ngIf=\"selectedStudent && currentMenu?.MenuJSON\" class=\"row\">\n      <div class=\"col-12 col-md-12 col-lg-12 col-xl-12 offset-xl-1 categories-container\">\n        <ul class=\"categories-list scrolling-horizontal-wrapper\">\n          <li *ngFor=\"let cat of currentMenu.MenuJSON; index as i\" id=\"menu-category-{{ i }}\" class=\"category-item\"\n            trackBy:cat.CategoryId>\n            <category-tile (click)=\"this.viewType === 'merchant' ? SetCategory(cat) : null\" [name]=\"cat.CatName\"\n              [iconName]=\"cat.CatUrl\" [isSelected]=\"IsCurrentCategory(cat)\"\n              [class.readonly-category]=\"this.viewType === 'student'\">\n            </category-tile>\n          </li>\n        </ul>\n      </div>\n    </div>\n\n    <!-- Menu Items Display - Only show when student is selected and category is available -->\n    <ng-container *ngIf=\"selectedStudent && currentCategoryToDisplay\">\n      <div class=\"row\">\n        <div class=\"col-12 col-md-12 col-lg-12 col-xl-12 offset-xl-1\" style=\"margin-left: 0px;\">\n          <h4 class=\"category-title\">{{ currentCategoryToDisplay.CatName }}</h4>\n          <div class=\"row\">\n            <div class=\"col-12 col-lg-6 mb-3\" *ngFor=\"let item of currentCategoryToDisplay.item; index as i\">\n              <mat-card appearance=\"outlined\" class=\"menu-item-card\">\n                <product-item [category]=\"currentCategoryToDisplay\" [item]=\"item\" [dateOrder]=\"selectedOrderDate\"\n                  [currentMenuType]=\"selectedMenuType\" [schoolCutOffTime]=\"menuCutOffTime\"\n                  (clickItem)=\"AddToCart($event)\" (itemDialogOpened)=\"onItemDialogOpened($event)\"\n                  (itemDialogClosed)=\"onItemDialogClosed($event)\" [id]=\"'pos-product-item-' + i\">\n                </product-item>\n              </mat-card>\n            </div>\n\n            <!-- No Items Message -->\n            <div *ngIf=\"currentCategoryToDisplay.item && currentCategoryToDisplay.item.length === 0\" class=\"col-12\">\n              <mat-card class=\"no-items-card\">\n                <mat-card-content>\n                  <p class=\"no-items-message\">No items available in this category</p>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ng-container>\n  </ng-template>"], "mappings": ";AAAA,SAAuCA,MAAM,QAAmB,eAAe;AAG/E,SAASC,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAGnE;AACA,SAAgBC,MAAM,QAAQ,aAAa;AAE3C,OAAO,KAAKC,aAAa,MAAM,sDAAsD;AACrF,OAAO,KAAKC,WAAW,MAAM,oDAAoD;AAKjF,SAEEC,cAAc,QAWT,qCAAqC;AAE5C;AACA,SAEEC,aAAa,EAEbC,YAAY,EAOZC,YAAY,QAIP,sBAAsB;AAC7B,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAYhC;AACA,SAASC,4BAA4B,QAAQ,4DAA4D;AACzG,SAASC,8BAA8B,QAAQ,8DAA8D;AAC7G,SAASC,4BAA4B,QAAQ,iBAAiB;AAC9D,SAASC,iBAAiB,QAAQ,iDAAiD;;;;;;;;;;;;;;;;;;;;;IC/DnFC,EAAA,CAAAC,SAAA,cAAwE;;;;;;IAWxED,EAAA,CAAAE,cAAA,cAA2D;IAEGF,EAAA,CAAAG,UAAA,mBAAAC,oDAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAAtFV,EAAA,CAAAW,YAAA,EAAuF;;;;IAAlFX,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAa,UAAA,QAAAC,MAAA,CAAAC,eAAA,IAAAf,EAAA,CAAAgB,aAAA,CAAyB;;;;;IA4DdhB,EAAA,CAAAE,cAAA,qBAAoF;IAClFF,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAa;;;;IAFyCX,EAAA,CAAAa,UAAA,UAAAK,cAAA,CAAAC,QAAA,CAA6B;IACjFnB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAoB,kBAAA,MAAAF,cAAA,CAAAG,QAAA,MACF;;;;;IAUArB,EAAA,CAAAE,cAAA,qBAAuD;IACrDF,EAAA,CAAAiB,MAAA,GACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAa;;;;IAF4BX,EAAA,CAAAa,UAAA,UAAAS,OAAA,CAAa;IACpDtB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAuB,WAAA,OAAAD,OAAA,qBACF;;;;;IAOVtB,EAAA,CAAAE,cAAA,cAAqE;IAGrDF,EAAA,CAAAiB,MAAA,eAAQ;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC7BX,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAiB,MAAA,iEAA0D;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAErEX,EAAA,CAAAE,cAAA,cAAwC;IACtCF,EAAA,CAAAC,SAAA,cAAkF;IAWpFD,EAAA,CAAAW,YAAA,EAAM;;;;IAXsBX,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAAwB,WAAA,qBAAAC,OAAA,CAAAC,oBAAA,GAAiD;;;;;;IA3CrF1B,EAAA,CAAAE,cAAA,mBAAmE;IAOjCF,EAAA,CAAAG,UAAA,6BAAAwB,uFAAAC,MAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAAwB,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAQ,aAAA;MAAA,OAAmBR,EAAA,CAAAS,WAAA,CAAAqB,OAAA,CAAAC,iBAAA,CAAAH,MAAA,CAAyB;IAAA,EAAC;IAAC5B,EAAA,CAAAW,YAAA,EAA0B;IAIlGX,EAAA,CAAAE,cAAA,cAAkC;IAEnBF,EAAA,CAAAiB,MAAA,qBAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAY;IACpCX,EAAA,CAAAE,cAAA,sBAAuC;IACrCF,EAAA,CAAAgC,UAAA,KAAAC,iDAAA,yBAEa;IACfjC,EAAA,CAAAW,YAAA,EAAa;IAKjBX,EAAA,CAAAE,cAAA,eAAkC;IAEnBF,EAAA,CAAAiB,MAAA,kBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAY;IACjCX,EAAA,CAAAE,cAAA,sBAAwC;IACtCF,EAAA,CAAAgC,UAAA,KAAAE,iDAAA,yBAEa;IACflC,EAAA,CAAAW,YAAA,EAAa;IAMrBX,EAAA,CAAAgC,UAAA,KAAAG,0CAAA,kBAoBM;IACRnC,EAAA,CAAAW,YAAA,EAAmB;;;;IAvDXX,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,UAAA,cAAAuB,MAAA,CAAAC,YAAA,CAA0B;IAKxBrC,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,UAAA,aAAAuB,MAAA,CAAAE,QAAA,CAAqB;IAQgBtC,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAa,UAAA,YAAAuB,MAAA,CAAAG,cAAA,CAAiB;IAYxBvC,EAAA,CAAAY,SAAA,GAAW;IAAXZ,EAAA,CAAAa,UAAA,YAAAuB,MAAA,CAAAI,QAAA,CAAW;IAS3CxC,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAa,UAAA,SAAAuB,MAAA,CAAAK,qBAAA,OAAAL,MAAA,CAAAM,OAAA,CAAyC;;;;;IAuBnD1C,EAAA,CAAAE,cAAA,cAEoD;IAClDF,EAAA,CAAAC,SAAA,sBAA2C;IAC7CD,EAAA,CAAAW,YAAA,EAAM;;;IADSX,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAa,UAAA,gBAAe;;;;;IAK5Bb,EAAA,CAAAE,cAAA,aAAiF;IAI1CF,EAAA,CAAAiB,MAAA,oBAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IACvDX,EAAA,CAAAE,cAAA,aAA0B;IAAAF,EAAA,CAAAiB,MAAA,oCAA6B;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC5DX,EAAA,CAAAE,cAAA,YAA2B;IACzBF,EAAA,CAAAiB,MAAA,0EACF;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;;IAcVX,EAAA,CAAAE,cAAA,UAA8C;IAKTF,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;IAAvBX,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAA2C,iBAAA,CAAAC,OAAA,CAAAC,aAAA,CAAmB;;;;;IAStD7C,EAAA,CAAA8C,kBAAA,GAAoE;;;;;IAApE9C,EAAA,CAAAgC,UAAA,IAAAe,yEAAA,2BAAoE;;;;;IAArD/C,EAAA,CAAAa,UAAA,qBAAAmC,IAAA,CAAoC;;;;;IAhBvDhD,EAAA,CAAAE,cAAA,UAA6D;IAE3DF,EAAA,CAAAgC,UAAA,IAAAiB,kDAAA,kBAUM;IAGNjD,EAAA,CAAAgC,UAAA,IAAAkB,0DAAA,iCAAAlD,EAAA,CAAAmD,sBAAA,CAEc;IAChBnD,EAAA,CAAAW,YAAA,EAAM;;;;;IAhBEX,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,UAAA,SAAAuC,OAAA,CAAAP,aAAA,CAAqB,aAAAQ,IAAA;;;;;IAqB3BrD,EAAA,CAAAE,cAAA,UAA6C;IAKRF,EAAA,CAAAiB,MAAA,GAAmB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;IAAvBX,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAA2C,iBAAA,CAAAW,OAAA,CAAAT,aAAA,CAAmB;;;;;IAStD7C,EAAA,CAAA8C,kBAAA,GAAoE;;;;;IAApE9C,EAAA,CAAAgC,UAAA,IAAAuB,yEAAA,2BAAoE;;;;;IAArDvD,EAAA,CAAAa,UAAA,qBAAAmC,IAAA,CAAoC;;;;;IAhBvDhD,EAAA,CAAAE,cAAA,UAAoE;IAElEF,EAAA,CAAAgC,UAAA,IAAAwB,kDAAA,kBAUM;IAGNxD,EAAA,CAAAgC,UAAA,IAAAyB,0DAAA,iCAAAzD,EAAA,CAAAmD,sBAAA,CAEc;IAChBnD,EAAA,CAAAW,YAAA,EAAM;;;;;IAhBEX,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,UAAA,SAAA6C,OAAA,CAAAb,aAAA,CAAqB,aAAAc,IAAA;;;;;IA3C7B3D,EAAA,CAAAgC,UAAA,IAAA4B,4CAAA,mBAgBM;IAIN5D,EAAA,CAAAgC,UAAA,IAAA6B,4CAAA,kBAkBM;IAGN7D,EAAA,CAAAgC,UAAA,IAAA8B,4CAAA,kBAkBM;;;;IA3DA9D,EAAA,CAAAa,UAAA,SAAAkD,MAAA,CAAAC,QAAA,mBAAAD,MAAA,CAAAE,qBAAA,GAA6D;IAoB7DjE,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAa,UAAA,SAAAkD,MAAA,CAAAC,QAAA,mBAAAD,MAAA,CAAAG,eAAA,CAAqD;IAqBrDlE,EAAA,CAAAY,SAAA,GAA4D;IAA5DZ,EAAA,CAAAa,UAAA,SAAAkD,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAE,qBAAA,GAA4D;;;;;IA+C9DjE,EAAA,CAAAE,cAAA,cAAgG;IACpFF,EAAA,CAAAiB,MAAA,WAAI;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IACzBX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,qCAA8B;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;;IAI3CX,EAAA,CAAAE,cAAA,QAAwC;IAAAF,EAAA,CAAAiB,MAAA,yBAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;;IAC9DX,EAAA,CAAAE,cAAA,QAAuC;IAAAF,EAAA,CAAAiB,MAAA,+BAAwB;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;;IAFrEX,EAAA,CAAAE,cAAA,cAA0D;IACxDF,EAAA,CAAAgC,UAAA,IAAAmC,mCAAA,gBAA8D;IAC9DnE,EAAA,CAAAgC,UAAA,IAAAoC,mCAAA,gBAAmE;IACrEpE,EAAA,CAAAW,YAAA,EAAM;;;;IAFAX,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,UAAA,SAAAwD,MAAA,CAAAL,QAAA,gBAAkC;IAClChE,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,UAAA,SAAAwD,MAAA,CAAAL,QAAA,eAAiC;;;;;IAKrChE,EAAA,CAAAE,cAAA,cAAsD;IACrBF,EAAA,CAAAiB,MAAA,sBAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IACzDX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,yBAAkB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;;IAEjCX,EAAA,CAAAE,cAAA,cAAmF;IACvEF,EAAA,CAAAiB,MAAA,6BAAsB;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC3CX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAAoE;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3EX,EAAA,CAAAY,SAAA,GAAoE;IAApEZ,EAAA,CAAAoB,kBAAA,yCAAApB,EAAA,CAAAsE,WAAA,OAAAC,OAAA,CAAAC,aAAA,QAAoE;;;;;IAE5ExE,EAAA,CAAAE,cAAA,cAAuE;IAC3DF,EAAA,CAAAiB,MAAA,YAAK;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC1BX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAAoB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3BX,EAAA,CAAAY,SAAA,GAAoB;IAApBZ,EAAA,CAAA2C,iBAAA,CAAA8B,OAAA,CAAAC,YAAA,GAAoB;;;;;IAX9B1E,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAgC,UAAA,IAAA2C,qCAAA,kBAGM;IACN3E,EAAA,CAAAgC,UAAA,IAAA4C,qCAAA,kBAGM;IACN5E,EAAA,CAAAgC,UAAA,IAAA6C,qCAAA,kBAGM;IACR7E,EAAA,CAAAW,YAAA,EAAM;;;;IAZEX,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAa,UAAA,SAAAiE,MAAA,CAAAC,cAAA,GAAsB;IAItB/E,EAAA,CAAAY,SAAA,GAAmD;IAAnDZ,EAAA,CAAAa,UAAA,UAAAiE,MAAA,CAAAC,cAAA,MAAAD,MAAA,CAAAN,aAAA,YAAmD;IAInDxE,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAa,UAAA,UAAAiE,MAAA,CAAAC,cAAA,MAAAD,MAAA,CAAAJ,YAAA,GAAyC;;;;;IAWzC1E,EAAA,CAAAE,cAAA,WAAkD;IAChDF,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAoB,kBAAA,MAAA4D,UAAA,CAAAC,UAAA,MACF;;;;;IAKFjF,EAAA,CAAAE,cAAA,eAAsD;IAAAF,EAAA,CAAAiB,MAAA,GAAoB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3BX,EAAA,CAAAY,SAAA,GAAoB;IAApBZ,EAAA,CAAAoB,kBAAA,MAAA8D,QAAA,CAAAC,QAAA,KAAoB;;;;;;IAC1EnF,EAAA,CAAAE,cAAA,iBAC4C;IAA1CF,EAAA,CAAAG,UAAA,mBAAAiF,wEAAA;MAAApF,EAAA,CAAAK,aAAA,CAAAgF,IAAA;MAAA,MAAAH,QAAA,GAAAlF,EAAA,CAAAQ,aAAA,GAAA8E,SAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8E,OAAA,CAAAC,cAAA,CAAAN,QAAA,CAAAO,UAAA,CAA+B;IAAA,EAAC;IACzCzF,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAiB,MAAA,aAAM;IAAAjB,EAAA,CAAAW,YAAA,EAAW;;;;;IAE7BX,EAAA,CAAAE,cAAA,eAAqE;IACzDF,EAAA,CAAAiB,MAAA,iBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAW;;;;;IAjBrCX,EAAA,CAAAE,cAAA,cAAyD;IAE/BF,EAAA,CAAAiB,MAAA,GAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAC1CX,EAAA,CAAAE,cAAA,YAAwB;IACtBF,EAAA,CAAAgC,UAAA,IAAA0D,4CAAA,mBAEO;IACT1F,EAAA,CAAAW,YAAA,EAAI;IAENX,EAAA,CAAAE,cAAA,cAA0B;IACCF,EAAA,CAAAiB,MAAA,GAAqC;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IACrEX,EAAA,CAAAgC,UAAA,KAAA2D,6CAAA,mBAAiF;IACjF3F,EAAA,CAAAgC,UAAA,KAAA4D,+CAAA,qBAGS;IACT5F,EAAA,CAAAgC,UAAA,KAAA6D,6CAAA,mBAEO;IACT7F,EAAA,CAAAW,YAAA,EAAM;;;;;IAjBkBX,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAA2C,iBAAA,CAAAuC,QAAA,CAAAY,IAAA,CAAe;IAEV9F,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAa,UAAA,YAAAqE,QAAA,CAAAa,eAAA,CAAuB;IAMzB/F,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAsE,WAAA,OAAAY,QAAA,CAAAc,eAAA,EAAqC;IACjChG,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAa,UAAA,SAAAqE,QAAA,CAAAC,QAAA,KAAuB;IAC3CnF,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,UAAA,SAAAoF,OAAA,CAAAjC,QAAA,gBAAkC;IAIpChE,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,UAAA,SAAAoF,OAAA,CAAAjC,QAAA,eAAiC;;;;;IAjB9ChE,EAAA,CAAAE,cAAA,cAAwD;IACtDF,EAAA,CAAAgC,UAAA,IAAAkE,qCAAA,mBAoBM;IAENlG,EAAA,CAAAE,cAAA,cAAwB;IAClBF,EAAA,CAAAiB,MAAA,GAAmC;;IAAAjB,EAAA,CAAAW,YAAA,EAAK;;;;IAvBxBX,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAa,UAAA,YAAAsF,MAAA,CAAAC,YAAA,CAAe;IAuB/BpG,EAAA,CAAAY,SAAA,GAAmC;IAAnCZ,EAAA,CAAAoB,kBAAA,YAAApB,EAAA,CAAAsE,WAAA,OAAA6B,MAAA,CAAAE,SAAA,QAAmC;;;;;;IA+EzCrG,EAAA,CAAAE,cAAA,aAAkC;IAEyCF,EAAA,CAAAG,UAAA,mBAAAmG,2EAAA;MAAAtG,EAAA,CAAAK,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAAxG,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA+F,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC3FzG,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAChCX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAAoD;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3DX,EAAA,CAAAY,SAAA,GAAoD;IAApDZ,EAAA,CAAAoB,kBAAA,4BAAApB,EAAA,CAAAsE,WAAA,OAAAoC,OAAA,CAAAL,SAAA,SAAoD;;;;;;IAMhErG,EAAA,CAAAE,cAAA,aAAiC;IAEwCF,EAAA,CAAAG,UAAA,mBAAAwG,2EAAA;MAAA3G,EAAA,CAAAK,aAAA,CAAAuG,IAAA;MAAA,MAAAC,OAAA,GAAA7G,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAoG,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAC9F9G,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAiB,MAAA,cAAO;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC5BX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAA4C;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAAnDX,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAoB,kBAAA,oBAAApB,EAAA,CAAAsE,WAAA,OAAAyC,OAAA,CAAAV,SAAA,SAA4C;;;;;IAwDpDrG,EAAA,CAAAE,cAAA,cAAqE;IACzDF,EAAA,CAAAiB,MAAA,iBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC/BX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAAuD;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA9DX,EAAA,CAAAY,SAAA,GAAuD;IAAvDZ,EAAA,CAAAoB,kBAAA,gCAAApB,EAAA,CAAAsE,WAAA,OAAA0C,OAAA,CAAAX,SAAA,QAAuD;;;;;;IAjJvErG,EAAA,CAAAE,cAAA,2BAA0E;IAsEJF,EAAA,CAAAiB,MAAA,cAAO;IAAAjB,EAAA,CAAAW,YAAA,EAAiB;IAG1FX,EAAA,CAAAgC,UAAA,IAAAiF,kDAAA,kBAOM;IAGNjH,EAAA,CAAAgC,UAAA,IAAAkF,kDAAA,kBAOM;IAGNlH,EAAA,CAAAE,cAAA,aAAiB;IAIWF,EAAA,CAAAG,UAAA,mBAAAgH,qEAAA;MAAAnH,EAAA,CAAAK,aAAA,CAAA+G,IAAA;MAAA,MAAAC,OAAA,GAAArH,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4G,OAAA,CAAAC,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAC7DtH,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAiB,MAAA,eAAO;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC5BX,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAiB,MAAA,uBAAe;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAGhCX,EAAA,CAAAE,cAAA,eAA2D;IAGnCF,EAAA,CAAAG,UAAA,mBAAAoH,sEAAA;MAAAvH,EAAA,CAAAK,aAAA,CAAA+G,IAAA;MAAA,MAAAI,OAAA,GAAAxH,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA+G,OAAA,CAAAF,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IACzDtH,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAiB,MAAA,aAAK;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC1BX,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAiB,MAAA,gBAAQ;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAM3BX,EAAA,CAAAE,cAAA,cAAiB;IAIaF,EAAA,CAAAG,UAAA,mBAAAsH,sEAAA;MAAAzH,EAAA,CAAAK,aAAA,CAAA+G,IAAA;MAAA,MAAAM,OAAA,GAAA1H,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAiH,OAAA,CAAAJ,mBAAA,CAAoB,UAAU,CAAC;IAAA,EAAC;IACjEtH,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAiB,MAAA,oBAAY;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IACjCX,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAiB,MAAA,iBAAS;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAG1BX,EAAA,CAAAE,cAAA,eAA2D;IAGnCF,EAAA,CAAAG,UAAA,mBAAAwH,sEAAA;MAAA3H,EAAA,CAAAK,aAAA,CAAA+G,IAAA;MAAA,MAAAQ,OAAA,GAAA5H,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAmH,OAAA,CAAAN,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IACzDtH,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAiB,MAAA,mBAAW;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAChCX,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAiB,MAAA,YAAI;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAMvBX,EAAA,CAAAE,cAAA,cAAiB;IAGXF,EAAA,CAAAiB,MAAA,IACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAS;IAIbX,EAAA,CAAAE,cAAA,cAAiB;IAEbF,EAAA,CAAAgC,UAAA,KAAA6F,mDAAA,kBAGM;IACR7H,EAAA,CAAAW,YAAA,EAAM;;;;IA1EUX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAa,UAAA,UAAAiH,OAAA,CAAApF,OAAA,CAAc;IAUd1C,EAAA,CAAAY,SAAA,GAAa;IAAbZ,EAAA,CAAAa,UAAA,SAAAiH,OAAA,CAAApF,OAAA,CAAa;IAazB1C,EAAA,CAAAY,SAAA,GAA2H;IAA3HZ,EAAA,CAAA+H,UAAA,CAAAD,OAAA,CAAAE,qBAAA,6FAA2H;IAQ3HhI,EAAA,CAAAY,SAAA,GAAyH;IAAzHZ,EAAA,CAAA+H,UAAA,CAAAD,OAAA,CAAAE,qBAAA,2FAAyH;IAYzHhI,EAAA,CAAAY,SAAA,GAA6H;IAA7HZ,EAAA,CAAA+H,UAAA,CAAAD,OAAA,CAAAE,qBAAA,+FAA6H;IAQ7HhI,EAAA,CAAAY,SAAA,GAAyH;IAAzHZ,EAAA,CAAA+H,UAAA,CAAAD,OAAA,CAAAE,qBAAA,2FAAyH;IAYzHhI,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAoB,kBAAA,mBAAApB,EAAA,CAAAsE,WAAA,SAAAwD,OAAA,CAAAzB,SAAA,UACF;IAMMrG,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAa,UAAA,SAAAiH,OAAA,CAAA9D,QAAA,eAAiC;;;;;IAiBvDhE,EAAA,CAAAE,cAAA,cACmD;IAErCF,EAAA,CAAAiB,MAAA,6BAAsB;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC3CX,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAiB,MAAA,GAAmD;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAA1DX,EAAA,CAAAY,SAAA,GAAmD;IAAnDZ,EAAA,CAAAoB,kBAAA,wBAAApB,EAAA,CAAAsE,WAAA,OAAA2D,OAAA,CAAAzD,aAAA,QAAmD;;;;;IAUvDxE,EAAA,CAAAE,cAAA,gBAA4D;IAAAF,EAAA,CAAAiB,MAAA,4BAAqB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;;IACxFX,EAAA,CAAAE,cAAA,gBAA2D;IAAAF,EAAA,CAAAiB,MAAA,gCAAyB;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;;;IANjGX,EAAA,CAAAE,cAAA,cAA+G;IAI/FF,EAAA,CAAAiB,MAAA,kBAAW;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAChCX,EAAA,CAAAgC,UAAA,IAAAkG,sCAAA,oBAAwF;IACxFlI,EAAA,CAAAgC,UAAA,IAAAmG,sCAAA,oBAA2F;IAC7FnI,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAE,cAAA,kBAA+E;IAA/CF,EAAA,CAAAG,UAAA,mBAAAiI,wDAAA;MAAApI,EAAA,CAAAK,aAAA,CAAAgI,IAAA;MAAA,MAAAC,OAAA,GAAAtI,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA6H,OAAA,CAAA7B,UAAA,EAAY;IAAA,EAAC;IACpDzG,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAiB,MAAA,oBAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAW;;;;IANUX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAa,UAAA,UAAA0H,OAAA,CAAA7F,OAAA,CAAc;IACd1C,EAAA,CAAAY,SAAA,GAAa;IAAbZ,EAAA,CAAAa,UAAA,SAAA0H,OAAA,CAAA7F,OAAA,CAAa;;;;;IAa/D1C,EAAA,CAAAE,cAAA,eACsD;IAExCF,EAAA,CAAAiB,MAAA,iBAAU;IAAAjB,EAAA,CAAAW,YAAA,EAAW;IAC/BX,EAAA,CAAAE,cAAA,eAAyB;IAAAF,EAAA,CAAAiB,MAAA,GAA4B;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;;IAAnCX,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAA2C,iBAAA,CAAA3C,EAAA,CAAAsE,WAAA,OAAAkE,OAAA,CAAAnC,SAAA,IAA4B;;;;;;IAanDrG,EAAA,CAAAE,cAAA,eAE4E;IAAjDF,EAAA,CAAAG,UAAA,mBAAAsI,iEAAA7G,MAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAAqI,IAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAmB,MAAA,CAAAgH,MAAA,CAAAC,GAAA,GAAAF,OAAA,CAAAG,gBAAA,CAAoC;IAAA,EAAC;IAFzE9I,EAAA,CAAAW,YAAA,EAE4E;;;;IAD1EX,EAAA,CAAAa,UAAA,QAAAkI,OAAA,CAAAC,eAAA,CAAAD,OAAA,CAAAE,gBAAA,GAAAC,IAAA,CAAAC,MAAA,IAAAC,QAAA,GAAApJ,EAAA,CAAAgB,aAAA,CAAmE,QAAA+H,OAAA,CAAAE,gBAAA,GAAAC,IAAA,CAAAG,IAAA;;;;;IAMnErJ,EAAA,CAAAE,cAAA,eAA0E;IACxEF,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAoB,kBAAA,MAAAkI,OAAA,CAAAL,gBAAA,GAAAC,IAAA,CAAAK,WAAA,MACF;;;;;IAVJvJ,EAAA,CAAAE,cAAA,eAAmD;IACjDF,EAAA,CAAAgC,UAAA,IAAAwH,2CAAA,mBAE4E;IAE5ExJ,EAAA,CAAAE,cAAA,eAAgC;IACPF,EAAA,CAAAiB,MAAA,GAAkC;IAAAjB,EAAA,CAAAW,YAAA,EAAM;IAC/DX,EAAA,CAAAE,cAAA,cAAwB;IAAAF,EAAA,CAAAiB,MAAA,GAAqD;;IAAAjB,EAAA,CAAAW,YAAA,EAAM;IACnFX,EAAA,CAAAgC,UAAA,IAAAyH,2CAAA,mBAEM;IACRzJ,EAAA,CAAAW,YAAA,EAAM;;;;IAVAX,EAAA,CAAAY,SAAA,GAAiF;IAAjFZ,EAAA,CAAAa,UAAA,SAAA6I,OAAA,CAAAT,gBAAA,GAAAC,IAAA,CAAAC,MAAA,IAAAO,OAAA,CAAAT,gBAAA,GAAAC,IAAA,CAAAC,MAAA,CAAAQ,MAAA,KAAiF;IAK9D3J,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAA2C,iBAAA,CAAA+G,OAAA,CAAAT,gBAAA,GAAAC,IAAA,CAAAG,IAAA,CAAkC;IACjCrJ,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAoB,kBAAA,MAAApB,EAAA,CAAAuB,WAAA,OAAAmI,OAAA,CAAAT,gBAAA,GAAAC,IAAA,CAAAU,KAAA,eAAqD;IACvE5J,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAa,UAAA,SAAA6I,OAAA,CAAAT,gBAAA,GAAAC,IAAA,CAAAK,WAAA,CAAyC;;;;;IAfvDvJ,EAAA,CAAAE,cAAA,eAA6F;IAInFF,EAAA,CAAAiB,MAAA,oBAAa;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IAGxBX,EAAA,CAAAgC,UAAA,IAAA6H,qCAAA,mBAiBM;IACR7J,EAAA,CAAAW,YAAA,EAAM;;;;IAlBEX,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAa,UAAA,SAAAiJ,OAAA,CAAAb,gBAAA,GAAwB;;;;;;IA2B1BjJ,EAAA,CAAAE,cAAA,cACyB;IACRF,EAAA,CAAAG,UAAA,mBAAA4J,kFAAA;MAAA,MAAAC,WAAA,GAAAhK,EAAA,CAAAK,aAAA,CAAA4J,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAA1E,SAAA;MAAA,MAAA6E,OAAA,GAAAnK,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA0J,OAAA,CAAAnG,QAAA,KAAkB,UAAU,GAAGmG,OAAA,CAAAC,WAAA,CAAAF,OAAA,CAAgB,GAAG,IAAI;IAAA,EAAC;IAG/ElK,EAAA,CAAAW,YAAA,EAAgB;;;;;;IALuCX,EAAA,CAAAqK,sBAAA,yBAAAC,KAAA,KAA0B;IAI/EtK,EAAA,CAAAY,SAAA,GAAuD;IAAvDZ,EAAA,CAAAuK,WAAA,sBAAAC,OAAA,CAAAxG,QAAA,eAAuD;IAFuBhE,EAAA,CAAAa,UAAA,SAAAqJ,OAAA,CAAAO,OAAA,CAAoB,aAAAP,OAAA,CAAAQ,MAAA,gBAAAF,OAAA,CAAAG,iBAAA,CAAAT,OAAA;;;;;IAL5GlK,EAAA,CAAAE,cAAA,aAAkE;IAG5DF,EAAA,CAAAgC,UAAA,IAAA4I,kDAAA,kBAMK;IACP5K,EAAA,CAAAW,YAAA,EAAK;;;;IAPiBX,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAa,UAAA,YAAAgK,OAAA,CAAAC,WAAA,CAAAC,QAAA,CAAyB;;;;;;IAiB3C/K,EAAA,CAAAE,cAAA,eAAiG;IAI3FF,EAAA,CAAAG,UAAA,uBAAA6K,+FAAApJ,MAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAC,OAAA,GAAAlL,EAAA,CAAAQ,aAAA;MAAA,OAAaR,EAAA,CAAAS,WAAA,CAAAyK,OAAA,CAAAC,SAAA,CAAAvJ,MAAA,CAAiB;IAAA,EAAC,8BAAAwJ,sGAAAxJ,MAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAI,OAAA,GAAArL,EAAA,CAAAQ,aAAA;MAAA,OAAqBR,EAAA,CAAAS,WAAA,CAAA4K,OAAA,CAAAC,kBAAA,CAAA1J,MAAA,CAA0B;IAAA,EAA/C,8BAAA2J,sGAAA3J,MAAA;MAAA5B,EAAA,CAAAK,aAAA,CAAA4K,IAAA;MAAA,MAAAO,OAAA,GAAAxL,EAAA,CAAAQ,aAAA;MAAA,OACXR,EAAA,CAAAS,WAAA,CAAA+K,OAAA,CAAAC,kBAAA,CAAA7J,MAAA,CAA0B;IAAA,EADf;IAEjC5B,EAAA,CAAAW,YAAA,EAAe;;;;;;IAJDX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,UAAA,aAAA6K,OAAA,CAAAC,wBAAA,CAAqC,SAAAC,QAAA,eAAAF,OAAA,CAAAG,iBAAA,qBAAAH,OAAA,CAAAI,gBAAA,sBAAAJ,OAAA,CAAAK,cAAA,8BAAAC,KAAA;;;;;IASvDhM,EAAA,CAAAE,cAAA,cAAwG;IAGtEF,EAAA,CAAAiB,MAAA,0CAAmC;IAAAjB,EAAA,CAAAW,YAAA,EAAI;;;;;IAnBjFX,EAAA,CAAAiM,uBAAA,GAAkE;IAChEjM,EAAA,CAAAE,cAAA,aAAiB;IAEcF,EAAA,CAAAiB,MAAA,GAAsC;IAAAjB,EAAA,CAAAW,YAAA,EAAK;IACtEX,EAAA,CAAAE,cAAA,aAAiB;IACfF,EAAA,CAAAgC,UAAA,IAAAkK,4DAAA,mBAQM;IAGNlM,EAAA,CAAAgC,UAAA,IAAAmK,4DAAA,mBAMM;IACRnM,EAAA,CAAAW,YAAA,EAAM;IAGZX,EAAA,CAAAoM,qBAAA,EAAe;;;;IAvBkBpM,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAA2C,iBAAA,CAAA0J,OAAA,CAAAV,wBAAA,CAAAlB,OAAA,CAAsC;IAEZzK,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAa,UAAA,YAAAwL,OAAA,CAAAV,wBAAA,CAAAzC,IAAA,CAAkC;IAW/ElJ,EAAA,CAAAY,SAAA,GAAiF;IAAjFZ,EAAA,CAAAa,UAAA,SAAAwL,OAAA,CAAAV,wBAAA,CAAAzC,IAAA,IAAAmD,OAAA,CAAAV,wBAAA,CAAAzC,IAAA,CAAAS,MAAA,OAAiF;;;;;IA/B/F3J,EAAA,CAAAgC,UAAA,IAAAsK,6CAAA,kBAYM;IAGNtM,EAAA,CAAAgC,UAAA,IAAAuK,sDAAA,2BA0Be;;;;IAzCTvM,EAAA,CAAAa,UAAA,SAAA2L,OAAA,CAAAtI,eAAA,KAAAsI,OAAA,CAAA1B,WAAA,kBAAA0B,OAAA,CAAA1B,WAAA,CAAAC,QAAA,EAA8C;IAerC/K,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAAa,UAAA,SAAA2L,OAAA,CAAAtI,eAAA,IAAAsI,OAAA,CAAAb,wBAAA,CAAiD;;;AD1bpE,OAAM,MAAOc,eAAgB,SAAQlN,aAAa;EAqFhDmN,YACUC,KAAqB,EACrBC,QAAkB,EAClBC,KAAqC,EACrCC,mBAAuC,EACvCC,eAAgC,EAChCC,eAAgC,EAChCC,WAAwB,EACxBC,MAAiB,EACjBC,uBAAgD,EAChDC,WAAwB,EACxBC,WAAwB;IAEhC,KAAK,EAAE;IAZC,KAAAV,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAvFrB;IACA,KAAAnJ,eAAe,GAAwB,IAAI;IAC3C,KAAA4H,gBAAgB,GAAWtM,YAAY,CAAC8N,MAAM,CAAC,CAAC;IAChD,KAAAzB,iBAAiB,GAAS,IAAI0B,IAAI,EAAE;IACpC,KAAAlM,QAAQ,GAAW,EAAE;IAErB,KAAAmM,WAAW,GAAGzO,MAAM,CAAU,KAAK,CAAC;IACpC,KAAA0O,qBAAqB,GAAG,wCAAwC;IAChE,KAAAC,iBAAiB,GAAY,IAAI,CAAC,CAAC;IAEnC;IACA,KAAAC,SAAS,GAAG5O,MAAM,CAAS,qBAAqB,CAAC;IAIjD,KAAA6O,oBAAoB,GAAY,KAAK;IACrC,KAAAvH,SAAS,GAAGtH,MAAM,CAAS,CAAC,CAAC;IAC7B,KAAA8O,YAAY,GAAGrO,YAAY;IAG3B,KAAAqD,aAAa,GAAG,IAAI;IACpB,KAAAuD,YAAY,GAAe,EAAE;IAE7B;IACA,KAAA7D,cAAc,GAAqB,EAAE;IACrC,KAAAC,QAAQ,GAAW,EAAE;IAQrB;IACA,KAAAsL,sBAAsB,GAAG/O,MAAM,CAAU,KAAK,CAAC;IAC/C,KAAAgP,YAAY,GAAGhP,MAAM,CAAc,IAAI,CAAC;IACxC,KAAAkF,qBAAqB,GAAGlF,MAAM,CAAU,KAAK,CAAC;IAE9C;IACA,KAAAiP,cAAc,GAAGjP,MAAM,CAAU,KAAK,CAAC;IACvC,KAAA+J,gBAAgB,GAAW,yCAAyC;IAEpE;IACA,KAAAmF,gBAAgB,GAAGlP,MAAM,CAAU,KAAK,CAAC;IACzC,KAAAkK,gBAAgB,GAAGlK,MAAM,CAAM,IAAI,CAAC;IAEpC;IACA,KAAAmP,kBAAkB,GAAGnP,MAAM,CAAU,KAAK,CAAC;IAC3C,KAAAoP,gBAAgB,GAAGpP,MAAM,CAAS,EAAE,CAAC;IAErC;IACA,KAAAyF,aAAa,GAAGzF,MAAM,CAAgB,IAAI,CAAC;IAC3C,KAAAgG,cAAc,GAAGhG,MAAM,CAAU,KAAK,CAAC;IACvC,KAAA2F,YAAY,GAAG3F,MAAM,CAAgB,IAAI,CAAC;IAE1C;IACA,KAAA0D,qBAAqB,GAAG1D,MAAM,CAAU,KAAK,CAAC;IAC9C,KAAA2C,oBAAoB,GAAG3C,MAAM,CAAgB,IAAI,CAAC;IAClD,KAAAqP,wBAAwB,GAAGrP,MAAM,CAAU,KAAK,CAAC;IAEjD;IACA,KAAAiJ,qBAAqB,GAAGjJ,MAAM,CAAS,SAAS,CAAC;IACjD,KAAAsP,yBAAyB,GAAGtP,MAAM,CAAU,KAAK,CAAC;IAClD,KAAAuP,cAAc,GAAG,CACf;MACEC,EAAE,EAAE,SAAS;MACbzI,IAAI,EAAE,gCAAgC;MACtC0I,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;;IAEX;IACA;IACA;IAAA,CACD;;IA8RD,KAAAC,gBAAgB,GAAG,IAAI,CAAC3B,eAAe,CAAC4B,YAAY,CAAC,IAAI,CAACC,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;IA42BxF,KAAAnM,OAAO,GAAY,KAAK;EA1nCxB;EAEMoM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACE,mBAAmB,EAAE;MAC1BF,KAAI,CAACG,kBAAkB,EAAE;MACzBH,KAAI,CAACI,0BAA0B,EAAE;MACjCJ,KAAI,CAACK,eAAe,EAAE;MACtBL,KAAI,CAACM,aAAa,EAAE;MACpBN,KAAI,CAACO,iBAAiB,EAAE;MAExB;MACA;MACA,IAAIP,KAAI,CAAC/K,QAAQ,KAAK,UAAU,EAAE;QAChCuL,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,MAAMT,KAAI,CAACU,mCAAmC,EAAE;OACjD,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;QACvF;;IACD;EACH;;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQV,mBAAmBA,CAAA;IACzB,IAAI,CAACtC,KAAK,CAACiD,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACxN,QAAQ,GAAG,CAACwN,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK;MAC5C,IAAI,CAACC,IAAI,GAAGD,MAAM,CAAC,MAAM,CAAC;MAC1B,IAAI,CAAC9L,QAAQ,GAAG8L,MAAM,CAAC,UAAU,CAAC,IAAI,UAAU;IAClD,CAAC,CAAC;EACJ;EAEQZ,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACc,yBAAyB,GAAG,IAAI,CAACnD,KAAK,CACxCoD,IAAI,CAAC9Q,MAAM,CAACC,aAAa,CAAC8Q,YAAY,CAAC,CAAC,CACxCL,SAAS,CAAEM,SAAqB,IAAI;MACnCZ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEW,SAAS,CAAC;MACnD,IAAI,CAACvC,oBAAoB,GAAGuC,SAAS,CAACxG,MAAM,GAAG,CAAC;MAChD,IAAI,CAACtD,SAAS,CAAC+J,GAAG,CAACrQ,iBAAiB,CAACoQ,SAAS,CAAC,CAAC;MAChD,IAAI,CAAC/J,YAAY,GAAG+J,SAAS;MAE7B;MACA,IAAI,IAAI,CAACnM,QAAQ,KAAK,UAAU,IAAI,IAAI,CAACE,eAAe,EAAE;QACxD,IAAI,CAACmM,qBAAqB,CAACF,SAAS,CAAC;;IAEzC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACG,yBAAyB,GAAG,IAAI,CAACxD,mBAAmB,CAACyD,oBAAoB,CAACV,SAAS,CAAC,MAAK;MAC5F,IAAI,CAACW,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEQrB,0BAA0BA,CAAA;IAChCI,OAAO,CAACC,GAAG,CAAC,gDAAgD,IAAI,CAACxL,QAAQ,oBAAoB,IAAI,CAAC+L,IAAI,EAAE,CAAC;IAEzG;IACA,IAAI,CAAC5C,uBAAuB,CAACsD,mBAAmB,EAAE,CAACZ,SAAS,CAACa,WAAW,IAAG;MACzE,IAAI,CAAC5C,sBAAsB,CAACsC,GAAG,CAACM,WAAW,CAAC;MAC5CnB,OAAO,CAACC,GAAG,CAAC,oCAAoCkB,WAAW,EAAE,CAAC;IAChE,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,6BAA6B,GAAG,IAAI,CAACxD,uBAAuB,CAC9DyD,oBAAoB,CAAC,IAAI,CAAC5M,QAAQ,CAAC,CACnC6L,SAAS,CAAEgB,OAAmB,IAAI;MACjCtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,IAAI,CAACxL,QAAQ,QAAQ,EAAE6M,OAAO,CAAC;MACzE,IAAI,CAACC,qBAAqB,CAACD,OAAO,CAAC;IACrC,CAAC,CAAC;EACN;EAEQC,qBAAqBA,CAACD,OAAmB;IAC/C;IACA,IAAIA,OAAO,CAACE,UAAU,KAAK,IAAI,CAAChB,IAAI,EAAE;MACpC;;IAGF,IAAI,CAAChC,YAAY,CAACqC,GAAG,CAAC,IAAI7C,IAAI,EAAE,CAAC;IAEjC,QAAQsD,OAAO,CAACG,IAAI;MAClB,KAAK1R,cAAc,CAAC2R,YAAY;QAC9B,IAAI,CAACC,wBAAwB,CAACL,OAAO,CAACM,OAA6B,CAAC;QACpE;MACF,KAAK7R,cAAc,CAAC8R,gBAAgB;QAClC,IAAI,CAACC,4BAA4B,CAACR,OAAO,CAACM,OAAiC,CAAC;QAC5E;MACF,KAAK7R,cAAc,CAACgS,YAAY;QAC9B,IAAI,CAACC,wBAAwB,CAACV,OAAO,CAACM,OAA6B,CAAC;QACpE;MACF,KAAK7R,cAAc,CAACkS,YAAY;QAC9B,IAAI,CAACC,wBAAwB,CAACZ,OAAO,CAACM,OAA6B,CAAC;QACpE;MACF,KAAK7R,cAAc,CAACoS,YAAY;QAC9B,IAAI,CAACC,wBAAwB,CAACd,OAAO,CAACM,OAA6B,CAAC;QACpE;MACF,KAAK7R,cAAc,CAACsS,YAAY;QAC9B,IAAI,CAACC,wBAAwB,CAAChB,OAAO,CAACM,OAA6B,CAAC;QACpE;MACF,KAAK7R,cAAc,CAACwS,iBAAiB;QACnC,IAAI,CAACC,4BAA4B,CAAClB,OAAO,CAACM,OAAiC,CAAC;QAC5E;MACF,KAAK7R,cAAc,CAAC0S,iBAAiB;QACnC,IAAI,CAACC,4BAA4B,CAACpB,OAAO,CAACM,OAAiC,CAAC;QAC5E;MACF,KAAK7R,cAAc,CAAC4S,gBAAgB;QAClC,IAAI,CAACC,4BAA4B,CAACtB,OAAO,CAACM,OAAiC,CAAC;QAC5E;MACF,KAAK7R,cAAc,CAAC8S,eAAe;QACjC,IAAI,CAACC,2BAA2B,CAACxB,OAAO,CAACM,OAAgC,CAAC;QAC1E;;EAEN;EAEQ/B,eAAeA,CAAA;IACrB,IAAI,CAAC/M,YAAY,GAAG,IAAIpD,SAAS,CAAC;MAChCkC,QAAQ,EAAE,IAAInC,WAAW,CAAC,IAAI,CAAC8M,gBAAgB,EAAE,CAAC5M,UAAU,CAACoT,QAAQ,CAAC,CAAC;MACvEC,SAAS,EAAE,IAAIvT,WAAW,CAAC,IAAI,CAAC6M,iBAAiB,EAAE,CAAC3M,UAAU,CAACoT,QAAQ,CAAC;KACzE,CAAC;IAEF;IACA,IAAI,CAACjQ,YAAY,CAACmQ,GAAG,CAAC,UAAU,CAAC,EAAEC,YAAY,CAAC5C,SAAS,CAAC6C,KAAK,IAAG;MAChE,IAAI,CAAC5G,gBAAgB,GAAG4G,KAAK;MAC7B,IAAI,CAAC/D,gBAAgB,EAAE;MAEvB;MACA,IAAI,IAAI,CAAC3K,QAAQ,KAAK,UAAU,EAAE;QAChC,IAAI,CAAC2O,sBAAsB,EAAE;;IAEjC,CAAC,CAAC;IAEF,IAAI,CAACtQ,YAAY,CAACmQ,GAAG,CAAC,WAAW,CAAC,EAAEC,YAAY,CAAC5C,SAAS,CAAC6C,KAAK,IAAG;MACjE,IAAI,CAAC7G,iBAAiB,GAAG6G,KAAK;MAC9B,IAAI,CAAC/D,gBAAgB,EAAE;MAEvB;MACA,IAAI,IAAI,CAAC3K,QAAQ,KAAK,UAAU,EAAE;QAChC,IAAI,CAAC2O,sBAAsB,EAAE;;IAEjC,CAAC,CAAC;EACJ;EAEA5Q,iBAAiBA,CAAC6Q,OAAqB;IACrCrD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoD,OAAO,CAAC;IAE/C;IACA,IAAI,IAAI,CAAC5O,QAAQ,KAAK,UAAU,IAAI,IAAI,CAACE,eAAe,IAAI0O,OAAO,IACjE,IAAI,CAAC1O,eAAe,CAAC2O,MAAM,KAAKD,OAAO,CAACC,MAAM,EAAE;MAChDtD,OAAO,CAACC,GAAG,CAAC,kDAAkD,IAAI,CAACtL,eAAe,CAAC4O,SAAS,OAAOF,OAAO,CAACE,SAAS,EAAE,CAAC;MACvH,IAAI,CAACjG,KAAK,CAACkG,QAAQ,CAAC1T,WAAW,CAAC2T,SAAS,EAAE,CAAC;MAE5C;MACA,IAAI,CAACC,sBAAsB,CAAC,iBAAiB,EAAE,IAAI,CAAC/O,eAAe,CAAC2O,MAAM,EAAED,OAAO,CAACC,MAAM,CAAC;;IAG7F,IAAI,CAAC3O,eAAe,GAAG0O,OAAO;IAC9B,IAAIA,OAAO,EAAE;MACX;MACA,IAAI,CAAClQ,OAAO,GAAGkQ,OAAO,CAAClQ,OAAO,IAAI,KAAK;MACvC6M,OAAO,CAACC,GAAG,CAAC,qCAAqC,IAAI,CAAC9M,OAAO,EAAE,CAAC;MAEhE;MACA,IAAI,IAAI,CAACA,OAAO,EAAE;QAChB6M,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;QACxF;;MAGF;MACA,IAAI,CAACxH,qBAAqB,CAACoI,GAAG,CAAC,SAAS,CAAC;MAEzC,IAAI,CAAC8C,kBAAkB,GAAGN,OAAO,CAACO,wBAAwB,IAAI,IAAI;MAClE,IAAI,CAACxE,gBAAgB,EAAE;MAEvB;MACA,IAAI,CAAC,IAAI,CAACjM,OAAO,EAAE,C;MAGnB,IAAI,CAAC0Q,yBAAyB,CAACR,OAAO,CAAC;MACvC;MACA;MACA;MACA;MACA;MACA;MACA;KACD,MAAM;MACL;MACA,IAAI,CAAC9H,WAAW,GAAG,IAAI;MACvB,IAAI,CAACuI,eAAe,GAAG,IAAI;MAC3B,IAAI,CAAC1H,wBAAwB,GAAG,IAAI;MACpC,IAAI,CAAC9I,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC2B,aAAa,CAAC4L,GAAG,CAAC,IAAI,CAAC;MAC5B,IAAI,CAAC1L,YAAY,CAAC0L,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAC1N,OAAO,GAAG,KAAK;MAEpB;MACA,IAAI,CAACD,qBAAqB,CAAC2N,GAAG,CAAC,KAAK,CAAC;MACrC,IAAI,CAAC1O,oBAAoB,CAAC0O,GAAG,CAAC,IAAI,CAAC;MACnC,IAAI,CAAChC,wBAAwB,CAACgC,GAAG,CAAC,KAAK,CAAC;MAExC;MACA,IAAI,CAAC8C,kBAAkB,GAAG,IAAI;MAE9B3D,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAE/E;MACA,IAAI,IAAI,CAACxL,QAAQ,KAAK,UAAU,EAAE;QAChCuL,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,IAAI,CAAC3C,KAAK,CAACkG,QAAQ,CAAC1T,WAAW,CAAC2T,SAAS,EAAE,CAAC;QAE5C;QACA,IAAI,CAACC,sBAAsB,CAAC,qBAAqB,CAAC;;;IAItD;IACA,IAAI,IAAI,CAACjP,QAAQ,KAAK,UAAU,EAAE;MAChC,IAAI,CAACsP,0BAA0B,CAACV,OAAO,CAAC;;EAE5C;EAEAvD,aAAaA,CAAA;IACX,IAAI,CAACpC,WAAW,CAACsG,gBAAgB,CAAC,IAAI,CAACjR,QAAQ,CAAC,CAACuN,SAAS,CAAC;MACzD2D,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACC,eAAe,CAACD,QAAQ,CAAC;MAChC,CAAC;MACDE,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAD,eAAeA,CAACD,QAA6B;IAC3C,IAAI,CAAClR,cAAc,GAAG,EAAE;IACxB,IAAI,CAACA,cAAc,CAACsR,IAAI,CAAC,IAAI,CAACC,WAAW,CAACL,QAAQ,EAAEjU,YAAY,CAAC8N,MAAM,CAAC,CAAC;IACzE,IAAI,CAAC/K,cAAc,CAACsR,IAAI,CAAC,IAAI,CAACC,WAAW,CAACL,QAAQ,EAAEjU,YAAY,CAACuU,KAAK,CAAC,CAAC;IAExE;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzR,cAAc,CAAC0R,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/S,QAAQ,KAAK,IAAI,CAAC2K,gBAAgB,CAAC;IACvF,IAAIkI,WAAW,EAAE;MACf,IAAI,CAAC3S,QAAQ,GAAG2S,WAAW,CAAC3S,QAAQ;;EAExC;EAEQyS,WAAWA,CAACL,QAA6B,EAAEtS,QAAgB;IACjE,MAAMgT,SAAS,GAAG,IAAI,CAACC,YAAY,CAACX,QAAQ,EAAEtS,QAAQ,CAAC;IACvD,OAAO;MACLA,QAAQ,EAAEA,QAAQ;MAClBE,QAAQ,EAAE8S,SAAS,IAAI,CAAC,GAAGV,QAAQ,CAACU,SAAS,CAAC,CAACE,YAAY,GAAGlT;KAC/D;EACH;EAEQiT,YAAYA,CAACX,QAA6B,EAAEtS,QAAgB;IAClE,IAAI,CAACsS,QAAQ,EAAE9J,MAAM,IAAI,CAACxI,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC7C,OAAOsS,QAAQ,EAAEa,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACpT,QAAQ,KAAKA,QAAQ,CAAC;EAChE;EAEAmO,iBAAiBA,CAAA;IACf,IAAI,CAAC9M,QAAQ,GAAG,EAAE;IAClB,MAAMgS,KAAK,GAAG,IAAIjH,IAAI,EAAE;IAExB;IACA,KAAK,IAAIkH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,IAAI,GAAG,IAAInH,IAAI,CAACiH,KAAK,CAAC;MAC5BE,IAAI,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,EAAE,GAAGH,CAAC,CAAC;MACjC,IAAI,CAACjS,QAAQ,CAACqR,IAAI,CAACa,IAAI,CAAC;;EAE5B;EAIA7F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC/C,gBAAgB,IAAI,CAAC,IAAI,CAAC5H,eAAe,IAAI,CAAC,IAAI,CAAC2H,iBAAiB,EAAE;MAC9E;;IAEF,IAAI,CAAC2B,WAAW,CAAC4C,GAAG,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACtF,WAAW,GAAG,IAAI;IACvB,IAAI,IAAI,CAACpI,OAAO,EAAE;MAChB,IAAI,CAACmS,QAAQ,EAAE;MACf;;IAEF,IAAI,CAACC,yBAAyB,EAAE;EAElC;EAEAA,yBAAyBA,CAAA;IACvB,MAAMC,OAAO,GAAuB;MAClCC,SAAS,EAAE,IAAI,CAAC9Q,eAAe,CAAC2O,MAAM;MACtCN,SAAS,EAAEzS,4BAA4B,CAAC,IAAI,CAAC+L,iBAAiB,CAAC;MAC/D1K,QAAQ,EAAE,IAAI,CAAC2K;KAChB;IAED,IAAI,CAACiB,eAAe,CAACkI,qCAAqC,CAACF,OAAO,CAAC,CAAClF,SAAS,CAAC;MAC5E2D,IAAI,EAAG0B,GAAsB,IAAI;QAC/B,IAAI,CAACC,mBAAmB,CAACD,GAAG,CAAC;MAC/B,CAAC;MACDvB,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACnG,WAAW,CAAC4C,GAAG,CAAC,KAAK,CAAC;QAC3B,IAAI,CAACvN,aAAa,GAAG,IAAI,CAAC4K,qBAAqB;QAC/C,IAAI,CAACmG,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEAwB,mBAAmBA,CAACD,GAAsB;IACxC,IAAI,CAACA,GAAG,EAAE;MACR,IAAI,CAACrS,aAAa,GAAG,IAAI,CAAC4K,qBAAqB;MAC/C,IAAI,CAACD,WAAW,CAAC4C,GAAG,CAAC,KAAK,CAAC;MAC3B;;IAGF,IAAI,IAAI,CAACgF,8BAA8B,CAACF,GAAG,CAAC,EAAE;MAC5C,IAAI,CAACG,gBAAgB,CAAC,uCAAuC,CAAC;MAC9D;;IAGF,IAAI,IAAI,CAACC,kBAAkB,CAACJ,GAAG,CAAC,EAAE;MAChC,MAAMrE,OAAO,GAAG,yCAAyC,IAAI,CAACxP,QAAQ,MAAM,IAAI,CAACkU,gBAAgB,EAAE,EAAE;MACrG,IAAI,CAACF,gBAAgB,CAACxE,OAAO,CAAC;MAC9B;;IAEF,IAAI,CAACgE,QAAQ,EAAE;EACjB;EAEQO,8BAA8BA,CAACF,GAAsB;IAC3D,OAAOA,GAAG,EAAEM,cAAc;EAC5B;EAEQF,kBAAkBA,CAACJ,GAAsB;IAC/C,OAAOO,OAAO,CAACP,GAAG,EAAEQ,KAAK,CAAC;EAC5B;EAEQL,gBAAgBA,CAACxE,OAAe;IACtC,IAAI,CAAChO,aAAa,GAAGgO,OAAO;IAC5B,IAAI,CAACrD,WAAW,CAAC4C,GAAG,CAAC,KAAK,CAAC;EAC7B;EAEQmF,gBAAgBA,CAAA;IACtB,OAAO5V,MAAM,CAAC,IAAI,CAACkM,iBAAiB,CAAC,CAAC8J,MAAM,CAAC,YAAY,CAAC;EAC5D;EAEQd,QAAQA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/I,gBAAgB,IAAI,CAAC,IAAI,CAAC5H,eAAe,EAAE;MACnD;;IAGF,IAAI,CAAC+I,WAAW,CAAC2I,sBAAsB,CAAC,IAAI,CAAC1R,eAAe,CAAC2R,QAAQ,EAAE,IAAI,CAAC/J,gBAAgB,CAAC,CAAC+D,SAAS,CAAC;MACtG2D,IAAI,EAAG0B,GAAW,IAAI;QACpB,IAAI,CAAC1H,WAAW,CAAC4C,GAAG,CAAC,KAAK,CAAC;QAC3B,MAAM0F,aAAa,GAAG,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC,GAAG,IAAI,CAACc,gBAAgB,CAACd,GAAG,CAAC,GAAG,IAAI;QAClF,IAAIY,aAAa,IAAIA,aAAa,EAAE/K,QAAQ,EAAE;UAC5C,IAAI,CAACkL,iBAAiB,CAACH,aAAa,CAAC;UACrC;;QAEF,IAAI,CAACjT,aAAa,GAAG,MAAM,IAAI,CAACxB,QAAQ,iBAAiB;MAC3D,CAAC;MACDsS,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC9Q,aAAa,GAAG,IAAI,CAAC4K,qBAAqB;QAC/C,IAAI,CAACD,WAAW,CAAC4C,GAAG,CAAC,KAAK,CAAC;QAC3B,IAAI,CAACwD,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEQoC,cAAcA,CAACb,GAAW;IAChC,OAAOA,GAAG,IAAIA,GAAG,CAACvL,MAAM,GAAG,CAAC;EAC9B;EAEQqM,gBAAgBA,CAACd,GAAW;IAClC,OAAOA,GAAG,CAAC,CAAC,CAAC;EACf;EAEAe,iBAAiBA,CAACC,UAAgB;IAChC,IAAI,CAACrT,aAAa,GAAG,IAAI;IACzB,IAAI,CAACiI,WAAW,GAAGoL,UAAU;IAE7B;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACrL,WAAW,CAACsL,MAAM;IACrC,IAAI,CAAC/U,QAAQ,GAAG,IAAI,CAACyJ,WAAW,CAACzB,IAAI;IAErC,IAAI,CAAC0C,cAAc,GAAG,IAAI,CAACsK,iBAAiB,EAAE;IAE9C9G,OAAO,CAACC,GAAG,CAAC,2BAA2B,IAAI,CAAC2G,MAAM,WAAW,IAAI,CAAC9U,QAAQ,EAAE,CAAC;IAE7E;IACA,IAAI,IAAI,CAACyJ,WAAW,CAACC,QAAQ,IAAI,IAAI,CAACD,WAAW,CAACC,QAAQ,CAACpB,MAAM,GAAG,CAAC,EAAE;MACrE,IAAI,CAACS,WAAW,CAAC,IAAI,CAACU,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElD;EAEQsL,iBAAiBA,CAAA;IACvB;IACA,OAAO,EAAE;EACX;EAEAjM,WAAWA,CAACkM,QAAkB;IAC5B,IAAI,CAACjD,eAAe,GAAGiD,QAAQ;IAC/B,IAAI,CAAC3K,wBAAwB,GAAG2K,QAAQ;IACxC,IAAI,CAAC9F,WAAW,EAAE;IAElB;IACA,IAAI,IAAI,CAACxM,QAAQ,KAAK,UAAU,EAAE;MAChC,IAAI,CAACuS,0BAA0B,CAACD,QAAQ,CAAC;;EAE7C;EAEA3L,iBAAiBA,CAAC6L,GAAa;IAC7B,OAAO,IAAI,CAACnD,eAAe,EAAE5I,OAAO,IAAI+L,GAAG,CAAC/L,OAAO;EACrD;EAEA+F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC6C,eAAe,EAAE;MACzB;;IAEF;IACA,IAAI,CAAC1H,wBAAwB,GAAG;MAAE,GAAG,IAAI,CAAC0H;IAAe,CAAE;EAC7D;EAEAlI,SAASA,CAACjC,IAAsB;IAC9BqG,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAACxL,QAAQ,QAAQ,EAAEkF,IAAI,CAAC;IAExE,IAAI;MACF;MACA,MAAMuN,QAAQ,GAAa,IAAI,CAACC,0BAA0B,CAACxN,IAAI,CAAC;MAChEqG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiH,QAAQ,CAAC;MAEnD,IAAI,CAAC5J,KAAK,CAACkG,QAAQ,CAAC1T,WAAW,CAACsX,SAAS,CAAC;QAAEF;MAAQ,CAAE,CAAC,CAAC;MACxDlH,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;KACzD,CAAC,OAAOmE,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;EAE5D;EAEA;EACArI,kBAAkBA,CAACsL,KAAU;IAC3B,IAAI,IAAI,CAAC5S,QAAQ,KAAK,UAAU,EAAE;MAChCuL,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoH,KAAK,CAAC;MAChE,IAAI,CAACC,0BAA0B,CAACD,KAAK,CAAC1N,IAAI,EAAE0N,KAAK,CAACN,QAAQ,CAAC;;EAE/D;EAEA7K,kBAAkBA,CAACmL,KAAU;IAC3B,IAAI,IAAI,CAAC5S,QAAQ,KAAK,UAAU,EAAE;MAChCuL,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEoH,KAAK,CAAC;MAChE,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAACG,SAAS,EAAEH,KAAK,CAAC1N,IAAI,CAAC;;EAEhE;EAIA1D,cAAcA,CAACC,UAAkB;IAC/B8J,OAAO,CAACC,GAAG,CAAC,kCAAkC/J,UAAU,EAAE,CAAC;IAC3D,IAAI,CAACoH,KAAK,CAACkG,QAAQ,CAAC1T,WAAW,CAAC2X,UAAU,CAAC;MAAEvR;IAAU,CAAE,CAAC,CAAC;IAC3D;EACF;EAEA;EACAiR,0BAA0BA,CAACxN,IAAsB;IAC/C,IAAI,CAAC,IAAI,CAAChF,eAAe,EAAE;MACzB,MAAM,IAAI+S,KAAK,CAAC,mCAAmC,CAAC;;IAGtD,MAAMC,OAAO,GAAiBhO,IAAI,EAAEiO,eAAe,EAAEC,GAAG,CAACC,MAAM,IAAG;MAChE,OAAO;QACLC,gBAAgB,EAAED,MAAM,CAACE,gBAAgB;QACzCtS,UAAU,EAAEoS,MAAM,CAACG,UAAU;QAC7BC,UAAU,EAAEJ,MAAM,CAACK,UAAU;QAC7BC,cAAc,EAAEN,MAAM,CAACO;OACxB;IACH,CAAC,CAAC,IAAI,EAAE;IAER,MAAMnB,QAAQ,GAAG;MACf/B,IAAI,EAAE,IAAI,CAAC7I,iBAAiB;MAC5BmJ,SAAS,EAAE,IAAI,CAAC9Q,eAAe,CAAC2O,MAAM;MACtCgF,WAAW,EAAE,IAAI,CAAC3T,eAAe,CAAC4O,SAAS;MAC3CxQ,QAAQ,EAAE,IAAI,CAAC4B,eAAe,CAAC2R,QAAQ;MACvC1U,QAAQ,EAAE,IAAI,CAAC2K,gBAAgB;MAC/BzK,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB8U,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB2B,kBAAkB,EAAE,IAAI,CAAC/L,cAAc;MACvCgM,SAAS,EAAE,IAAI,CAACjN,WAAW,EAAEkN,SAAS,IAAI,CAAC;MAC3CvS,UAAU,EAAE9F,MAAM,EAAE,CAACsY,IAAI,EAAE;MAC3BC,UAAU,EAAEhP,IAAI,CAACiP,UAAU;MAC3BrS,IAAI,EAAEoD,IAAI,CAACG,IAAI;MACfrD,eAAe,EAAEkD,IAAI,CAACkP,eAAe;MACrCrS,eAAe,EAAEmR,OAAO;MACxB/R,QAAQ,EAAE+D,IAAI,CAACmP;KAChB;IAED9I,OAAO,CAACC,GAAG,CAAC,wCAAwCiH,QAAQ,CAACN,MAAM,eAAeM,QAAQ,CAACpV,QAAQ,EAAE,CAAC;IACtG,OAAOoV,QAAQ;EACjB;EAEA;EACAnP,mBAAmBA,CAACgR,QAAgB;IAClC,IAAI,CAACtQ,qBAAqB,CAACoI,GAAG,CAACkI,QAAQ,CAAC;IACxC,IAAI,CAACjK,yBAAyB,CAAC+B,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3Cb,OAAO,CAACC,GAAG,CAAC,kCAAkC8I,QAAQ,EAAE,CAAC;EAC3D;EAEA;EACAC,yBAAyBA,CAAA;IACvB,IAAI,CAAClK,yBAAyB,CAAC+B,GAAG,CAAC,IAAI,CAAC;EAC1C;EAEAoI,0BAA0BA,CAAA;IACxB,IAAI,CAACnK,yBAAyB,CAAC+B,GAAG,CAAC,KAAK,CAAC;EAC3C;EAEA;EACA3J,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACgS,0BAA0B,EAAE,EAAE;MACtC;;IAGF;IACA,IAAI,IAAI,CAACzQ,qBAAqB,EAAE,KAAK,SAAS,EAAE;MAC9C0Q,KAAK,CAAC,gEAAgE,CAAC;MACvE;;IAGF;IACA,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACvS,YAAY,CAAC;IAExC,MAAMwS,SAAS,GAAG,IAAI,CAAC1L,MAAM,CAAC2L,IAAI,CAACjZ,4BAA4B,EAAE;MAC/DkZ,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBC,IAAI,EAAE;QACJL,YAAY,EAAEA,YAAY;QAC1BM,WAAW,EAAE,IAAI;QACjB;QACAjV,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB+L,IAAI,EAAE,IAAI,CAACA,IAAI;QACf7L,eAAe,EAAE,IAAI,CAACA,eAAe;QACrC4H,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCD,iBAAiB,EAAE,IAAI,CAACA;;KAE3B,CAAC;IAEF+M,SAAS,CAACM,WAAW,EAAE,CAACrJ,SAAS,CAACsJ,MAAM,IAAG;MACzC,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;QAC5B7J,OAAO,CAACC,GAAG,CAAC,4CAA4C2J,MAAM,CAACE,OAAO,EAAE,CAAC;QAEzE;QACA,IAAI,CAACxK,WAAW,EAAE;QAElB;QACA,IAAI,IAAI,CAAC7K,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAAC6I,KAAK,CAACkG,QAAQ,CAAC1T,WAAW,CAAC2T,SAAS,EAAE,CAAC;UAE5C;UACA,IAAI,CAACC,sBAAsB,CAAC,cAAc,CAAC;;QAG7C;QACA1D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QAEpE;QACA,IAAI,IAAI,CAACxL,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAACsV,2BAA2B,EAAE;;OAErC,MAAM,IAAIH,MAAM,KAAK,IAAI,EAAE;QAC1B;QACA5J,OAAO,CAACoE,KAAK,CAAC,8BAA8B,CAAC;;IAEjD,CAAC,CAAC;EACJ;EAEQ8E,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAAC,IAAI,CAACvU,eAAe,EAAE;MACzBwU,KAAK,CAAC,kDAAkD,CAAC;MACzD,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC,IAAI,CAACtS,YAAY,IAAI,IAAI,CAACA,YAAY,CAACuD,MAAM,KAAK,CAAC,EAAE;MACxD+O,KAAK,CAAC,+DAA+D,CAAC;MACtE,OAAO,KAAK;;IAGd;IACA,IAAI,CAAC,IAAI,CAAC5M,gBAAgB,IAAI,CAAC,IAAI,CAACD,iBAAiB,EAAE;MACrD6M,KAAK,CAAC,2CAA2C,CAAC;MAClD,OAAO,KAAK;;IAGd;IACA;IAEA,OAAO,IAAI;EACb;EAEAa,WAAWA,CAAA;IACT,IAAI,CAAC3M,QAAQ,CAAC4M,IAAI,EAAE;EACtB;EAEA;EACAC,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACzV,QAAQ,KAAK,SAAS,IAAI,IAAI,CAACC,qBAAqB,EAAE;EACpE;EAEA;EACAvD,gBAAgBA,CAAA;IACd,IAAI,CAACsN,cAAc,CAACoC,GAAG,CAAC,IAAI,CAAC;EAC/B;EAEArP,eAAeA,CAAA;IACb,OAAO,IAAI,CAACiN,cAAc,EAAE,GAAG,IAAI,CAAClF,gBAAgB,GAAG,wCAAwC;EACjG;EAEAE,eAAeA,CAAC0Q,QAAgB;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI,CAAC5Q,gBAAgB;IAE3C,IAAI4Q,QAAQ,CAACC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;MACnC,OAAOD,QAAQ;KAChB,MAAM;MACL,OAAOha,WAAW,CAACka,WAAW,GAAGna,YAAY,CAACoa,OAAO,GAAGH,QAAQ;;EAEpE;EAEA;EACQpG,0BAA0BA,CAACV,OAA4B;IAC7D,MAAMzB,OAAO,GAA2B;MAAEyB;IAAO,CAAE;IACnDrD,OAAO,CAACC,GAAG,CAAC,uEAAuE,EAAE;MACnFqI,WAAW,EAAEjF,OAAO,GAAG,GAAGA,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACkH,QAAQ,EAAE,GAAG,MAAM;MAC1EC,OAAO,EAAEnH,OAAO,EAAElQ,OAAO,IAAI,KAAK;MAClCsS,SAAS,EAAEpC,OAAO,EAAEC,MAAM;MAC1B9C,IAAI,EAAE,IAAI,CAACA;KACZ,CAAC;IACF,IAAI,CAAC5C,uBAAuB,CAAC6M,mBAAmB,CAAC7I,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;IACpER,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EACjE;EAEQyK,6BAA6BA,CAACC,SAAuB;IAC3D3K,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAE;MAChFqI,WAAW,EAAE,GAAGqC,SAAS,CAACpH,SAAS,IAAIoH,SAAS,CAACJ,QAAQ,EAAE;MAC3DC,OAAO,EAAEG,SAAS,CAACxX,OAAO;MAC1BsS,SAAS,EAAEkF,SAAS,CAACrH,MAAM;MAC3BsH,MAAM,EAAE;KACT,CAAC;IAEF;IACA,MAAMhJ,OAAO,GAA2B;MAAEyB,OAAO,EAAEsH;IAAS,CAAE;IAC9D,IAAI,CAAC/M,uBAAuB,CAAC6M,mBAAmB,CAAC7I,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;IACpER,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;EACxE;EAEQa,qBAAqBA,CAACF,SAAqB;IACjD,MAAMgB,OAAO,GAAuB;MAClCiJ,SAAS,EAAEjK,SAAS,CAACxG,MAAM;MAC3B0Q,WAAW,EAAEta,iBAAiB,CAACoQ,SAAS,CAAC;MACzC6E,SAAS,EAAE,IAAI,CAAC9Q,eAAe,EAAE2O,MAAM;MACvC1C,SAAS,EAAEA,SAAS;MACpBmK,SAAS,EAAE,IAAI/M,IAAI,EAAE,CAACgN,WAAW;KAClC;IACDhL,OAAO,CAACC,GAAG,CAAC,0CAA0CW,SAAS,CAACxG,MAAM,SAAS,EAAEwH,OAAO,CAAC;IACzF,IAAI,CAAChE,uBAAuB,CAACqN,eAAe,CAACrJ,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EAClE;EAEQ4C,sBAAsBA,CAAA;IAC5B,MAAMxB,OAAO,GAAuB;MAClChQ,QAAQ,EAAE,IAAI,CAAC2K,gBAAgB;MAC/ByG,SAAS,EAAE,IAAI,CAAC1G,iBAAiB;MACjCmJ,SAAS,EAAE,IAAI,CAAC9Q,eAAe,EAAE2O,MAAM;MACvCxR,QAAQ,EAAE,IAAI,CAACA;KAChB;IACD,IAAI,CAAC8L,uBAAuB,CAACsN,eAAe,CAACtJ,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EAClE;EAEQ2K,sBAAsBA,CAACrB,OAAe,EAAEsB,aAAsB;IACpE,IAAI,CAAC,IAAI,CAACzW,eAAe,EAAE;IAE3B,MAAMiN,OAAO,GAAuB;MAClCkI,OAAO;MACPrE,SAAS,EAAE,IAAI,CAAC9Q,eAAe,CAAC2O,MAAM;MACtCgF,WAAW,EAAE,GAAG,IAAI,CAAC3T,eAAe,CAAC4O,SAAS,IAAI,IAAI,CAAC5O,eAAe,CAAC4V,QAAQ,EAAE;MACjF3Y,QAAQ,EAAE,IAAI,CAAC2K,gBAAgB;MAC/ByG,SAAS,EAAE,IAAI,CAAC1G,iBAAiB,CAAC0O,WAAW,EAAE;MAC/CF,WAAW,EAAE,IAAI,CAAChU,SAAS,EAAE;MAC7B+T,SAAS,EAAE,IAAI,CAAChU,YAAY,CAACuD,MAAM;MACnCgR,aAAa,EAAEA,aAAa,IAAI,IAAI,CAAC3S,qBAAqB;KAC3D;IACD,IAAI,CAACmF,uBAAuB,CAACyN,eAAe,CAACzJ,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EAClE;EAEQ8G,0BAA0BA,CAAC3N,IAAS,EAAEoN,QAAa;IACzD,MAAMnF,OAAO,GAA2B;MACtCjI,IAAI;MACJwL,IAAI,EAAE,IAAI,CAAC7I,iBAAiB;MAC5ByK;KACD;IACD/G,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE2B,OAAO,CAAC;IAChE,IAAI,CAAChE,uBAAuB,CAAC0N,mBAAmB,CAAC1J,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EACtE;EAIQ+G,0BAA0BA,CAACC,SAAkB,EAAE7N,IAAU;IAC/D,MAAMiI,OAAO,GAA2B;MACtC4F,SAAS;MACT7N;KACD;IACDqG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE2B,OAAO,CAAC;IAChE,IAAI,CAAChE,uBAAuB,CAAC2N,mBAAmB,CAAC3J,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EACtE;EAEQwG,0BAA0BA,CAACD,QAAa;IAC9C,MAAMnF,OAAO,GAA2B;MACtCmF,QAAQ;MACRyE,UAAU,EAAEzE,QAAQ,CAAC0E,UAAU;MAC/BC,YAAY,EAAE3E,QAAQ,CAAC7L;KACxB;IACD8E,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2B,OAAO,CAAC;IAC/D,IAAI,CAAChE,uBAAuB,CAAC+N,mBAAmB,CAAC/J,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EACtE;EAEQoL,yBAAyBA,CAACnG,SAAiB,EAAEoG,OAAe,EAAEvD,WAAmB,EAAEwD,aAAsB,EAAEtB,OAAiB;IAClI,MAAM5I,OAAO,GAA0B;MACrC6D,SAAS;MACToG,OAAO;MACPvD,WAAW;MACXwD,aAAa;MACbtB;KACD;IACDxK,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE2B,OAAO,CAAC;IAC9D,IAAI,CAAChE,uBAAuB,CAACmO,kBAAkB,CAACnK,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EACrE;EAEQkD,sBAAsBA,CAACkH,MAAkE,EAAEoB,iBAA0B,EAAEC,YAAqB;IAClJ,MAAMrK,OAAO,GAAuB;MAClCgJ,MAAM;MACNoB,iBAAiB;MACjBC;KACD;IACDjM,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE2B,OAAO,CAAC;IAC3D,IAAI,CAAChE,uBAAuB,CAACsO,eAAe,CAACtK,OAAO,EAAE,IAAI,CAACpB,IAAI,CAAC;EAClE;EAEA;EACQmB,wBAAwBA,CAACC,OAA2B;IAC1D,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,gEAAgE2B,OAAO,CAAC0G,WAAW,MAAM1G,OAAO,CAACkJ,WAAW,EAAE,CAAC;MAE3H;MACA,IAAI,IAAI,CAACnW,eAAe,EAAE2O,MAAM,KAAK1B,OAAO,CAAC6D,SAAS,IACpD,IAAI,CAAClJ,gBAAgB,KAAKqF,OAAO,CAAChQ,QAAQ,EAAE;QAC5CoO,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;QAC3E,IAAI,CAACX,WAAW,EAAE;;MAGpB;MACA,IAAI,CAAC6M,sBAAsB,EAAE;MAE7BnM,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;EAEtE;EAEA;;;EAGQkM,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACjZ,qBAAqB,CAAC2N,GAAG,CAAC,KAAK,CAAC;IACrC,IAAI,CAAChC,wBAAwB,CAACgC,GAAG,CAAC,KAAK,CAAC;IAExC;IACA,IAAI,CAAClC,kBAAkB,CAACkC,GAAG,CAAC,KAAK,CAAC;IAElCb,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;EAClE;EAEQ6B,4BAA4BA,CAACF,OAA+B;IAClE,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE;QACnEqI,WAAW,EAAE1G,OAAO,CAACyB,OAAO,GAAG,GAAGzB,OAAO,CAACyB,OAAO,CAACE,SAAS,IAAI3B,OAAO,CAACyB,OAAO,CAACkH,QAAQ,EAAE,GAAG,MAAM;QAClGC,OAAO,EAAE5I,OAAO,CAACyB,OAAO,EAAElQ,OAAO,IAAI,KAAK;QAC1CsS,SAAS,EAAE7D,OAAO,CAACyB,OAAO,EAAEC,MAAM;QAClC8I,cAAc,EAAE,IAAI,CAACzX,eAAe,GAAG,GAAG,IAAI,CAACA,eAAe,CAAC4O,SAAS,IAAI,IAAI,CAAC5O,eAAe,CAAC4V,QAAQ,EAAE,GAAG;OAC/G,CAAC;MAEF,IAAI3I,OAAO,CAACyB,OAAO,EAAE;QACnB;QACA,IAAI,IAAI,CAAC1O,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC2O,MAAM,KAAK1B,OAAO,CAACyB,OAAO,CAACC,MAAM,EAAE;UAClFtD,OAAO,CAACC,GAAG,CAAC,+DAA+D,IAAI,CAACtL,eAAe,CAAC4O,SAAS,OAAO3B,OAAO,CAACyB,OAAO,CAACE,SAAS,EAAE,CAAC;UAC5I,IAAI,CAAC1M,YAAY,GAAG,EAAE;UACtB,IAAI,CAACC,SAAS,CAAC+J,GAAG,CAAC,CAAC,CAAC;UACrB,IAAI,CAACxC,oBAAoB,GAAG,KAAK;;QAGnC;QACA,IAAI,CAAC1J,eAAe,GAAGiN,OAAO,CAACyB,OAAO;QACtC,IAAI,CAACM,kBAAkB,GAAG/B,OAAO,CAACyB,OAAO,CAACO,wBAAwB,IAAI,IAAI;QAE1E;QACA,IAAI,CAACzQ,OAAO,GAAGyO,OAAO,CAACyB,OAAO,CAAClQ,OAAO,IAAI,KAAK;QAC/C6M,OAAO,CAACC,GAAG,CAAC,qDAAqD,IAAI,CAAC9M,OAAO,WAAWyO,OAAO,CAACyB,OAAO,CAACE,SAAS,IAAI3B,OAAO,CAACyB,OAAO,CAACkH,QAAQ,EAAE,CAAC;QAEhJ;QACA,IAAI,CAAC7V,qBAAqB,CAACmM,GAAG,CAAC,IAAI,CAAC;QACpCb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QAEvD;QACA,IAAI,CAACb,gBAAgB,EAAE;QACvBY,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;QAEhFD,OAAO,CAACC,GAAG,CAAC,8DAA8D2B,OAAO,CAACyB,OAAO,CAACE,SAAS,IAAI3B,OAAO,CAACyB,OAAO,CAACkH,QAAQ,EAAE,CAAC;OACnI,MAAM;QACLvK,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QAEnE;QACA,IAAI,CAACvL,qBAAqB,CAACmM,GAAG,CAAC,KAAK,CAAC;QACrC,IAAI,CAAClM,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC4G,WAAW,GAAG,IAAI;QACvB,IAAI,CAACuI,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC1H,wBAAwB,GAAG,IAAI;QACpC,IAAI,CAACjJ,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACD,qBAAqB,CAAC2N,GAAG,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC1O,oBAAoB,CAAC0O,GAAG,CAAC,IAAI,CAAC;QACnC,IAAI,CAAChC,wBAAwB,CAACgC,GAAG,CAAC,KAAK,CAAC;QAExC;QACA,IAAI,CAAC5L,aAAa,CAAC4L,GAAG,CAAC,IAAI,CAAC;QAC5B,IAAI,CAACrL,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC1L,YAAY,CAAC0L,GAAG,CAAC,IAAI,CAAC;QAE3B;QACA,IAAI,CAAC8C,kBAAkB,GAAG,IAAI;QAE9B;QACA,IAAI,CAAC9M,YAAY,GAAG,EAAE;QACtB,IAAI,CAACC,SAAS,CAAC+J,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,CAACxC,oBAAoB,GAAG,KAAK;QAEjC2B,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;;EAGvE;EAEQ+B,wBAAwBA,CAACJ,OAA2B;IAC1D,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/B;MACA,IAAI,IAAI,CAAC8H,gBAAgB,KAAKqF,OAAO,CAAChQ,QAAQ,IAC5C,IAAI,CAAC0K,iBAAiB,CAAC+P,OAAO,EAAE,KAAKzK,OAAO,CAACoB,SAAS,CAACqJ,OAAO,EAAE,EAAE;QAClE,IAAI,CAAC9P,gBAAgB,GAAGqF,OAAO,CAAChQ,QAAQ;QACxC,IAAI,CAAC0K,iBAAiB,GAAGsF,OAAO,CAACoB,SAAS;QAC1C,IAAI,CAAClQ,YAAY,CAACwZ,UAAU,CAAC;UAC3B1a,QAAQ,EAAEgQ,OAAO,CAAChQ,QAAQ;UAC1BoR,SAAS,EAAEpB,OAAO,CAACoB;SACpB,CAAC;QACFhD,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,OAAO,CAAChQ,QAAQ,QAAQgQ,OAAO,CAACoB,SAAS,EAAE,CAAC;;;EAGlF;EAEQd,wBAAwBA,CAACN,OAA2B;IAC1D,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,uCAAuC2B,OAAO,CAACiJ,SAAS,mBAAmBjJ,OAAO,CAACkJ,WAAW,EAAE,CAAC;MAC7G9K,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2B,OAAO,CAAChB,SAAS,CAAC;MAE5D;MACA,IAAI,CAACjC,kBAAkB,CAACkC,GAAG,CAAC,IAAI,CAAC;MAEjC;MACA,IAAI,CAAChK,YAAY,GAAG+K,OAAO,CAAChB,SAAS,IAAI,EAAE;MAC3C,IAAI,CAAC9J,SAAS,CAAC+J,GAAG,CAACe,OAAO,CAACkJ,WAAW,CAAC;MACvC,IAAI,CAACzM,oBAAoB,GAAGuD,OAAO,CAACiJ,SAAS,GAAG,CAAC;MACjD,IAAI,CAACjM,gBAAgB,CAACiC,GAAG,CAAC,IAAI7C,IAAI,EAAE,CAACuO,kBAAkB,EAAE,CAAC;MAE1DvM,OAAO,CAACC,GAAG,CAAC,6CAA6C,IAAI,CAACpJ,YAAY,CAACuD,MAAM,QAAQ,CAAC;MAE1F;MACAoS,UAAU,CAAC,MAAK;QACd,IAAI,CAAC7N,kBAAkB,CAACkC,GAAG,CAAC,KAAK,CAAC;MACpC,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEQyB,wBAAwBA,CAACV,OAA2B;IAC1D5B,OAAO,CAACC,GAAG,CAAC,2BAA2B2B,OAAO,CAACgJ,MAAM,EAAE,CAAC;IACxD;IACA,IAAIhJ,OAAO,CAACgJ,MAAM,KAAK,iBAAiB,EAAE;MACxC,IAAI,CAACtL,WAAW,EAAE;;EAEtB;EAEQkD,4BAA4BA,CAACZ,OAA+B;IAClE,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE2B,OAAO,CAACjI,IAAI,CAACG,IAAI,CAAC;MAC1E,IAAI,CAACJ,gBAAgB,CAACmH,GAAG,CAACe,OAAO,CAAC;MAClC,IAAI,CAAClD,gBAAgB,CAACmC,GAAG,CAAC,IAAI,CAAC;;EAEnC;EAEQ6B,4BAA4BA,CAACd,OAA+B;IAClE,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE2B,OAAO,CAAC4F,SAAS,CAAC;MACtF,IAAI,CAAC9I,gBAAgB,CAACmC,GAAG,CAAC,KAAK,CAAC;MAChC,IAAI,CAACnH,gBAAgB,CAACmH,GAAG,CAAC,IAAI,CAAC;;EAEnC;EAEQ+B,4BAA4BA,CAAChB,OAA+B;IAClE,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2B,OAAO,CAAC8J,YAAY,CAAC;MAC5E;MACA,IAAI,IAAI,CAACnQ,WAAW,EAAEC,QAAQ,EAAE;QAC9B,MAAMuL,QAAQ,GAAG,IAAI,CAACxL,WAAW,CAACC,QAAQ,CAACkJ,IAAI,CAACuC,GAAG,IAAIA,GAAG,CAACwE,UAAU,KAAK7J,OAAO,CAAC4J,UAAU,CAAC;QAC7F,IAAIzE,QAAQ,EAAE;UACZ,IAAI,CAAClM,WAAW,CAACkM,QAAQ,CAAC;UAC1B/G,OAAO,CAACC,GAAG,CAAC,4CAA4C2B,OAAO,CAAC8J,YAAY,EAAE,CAAC;;;;EAIvF;EAEQ5I,2BAA2BA,CAAClB,OAA8B;IAChE,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,qDAAqD2B,OAAO,CAAC0G,WAAW,MAAM1G,OAAO,CAACiK,OAAO,cAAcjK,OAAO,CAAC4I,OAAO,EAAE,CAAC;MAEzI;MACA,IAAI,IAAI,CAAC7V,eAAe,EAAE2O,MAAM,KAAK1B,OAAO,CAAC6D,SAAS,EAAE;QACtD,IAAI,CAACxQ,aAAa,CAAC4L,GAAG,CAACe,OAAO,CAACiK,OAAO,CAAC;QACvC,IAAI,CAACrW,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC1L,YAAY,CAAC0L,GAAG,CAAC,IAAI,CAAC;QAE3B;QACA,IAAIe,OAAO,CAAC4I,OAAO,KAAKiC,SAAS,EAAE;UACjC,IAAI,CAACtZ,OAAO,GAAGyO,OAAO,CAAC4I,OAAO;;QAGhC;QACA,IAAI5I,OAAO,CAACkK,aAAa,EAAE;UACzB,IAAI,CAAC3Z,oBAAoB,CAAC0O,GAAG,CAACe,OAAO,CAACkK,aAAa,CAAC;;QAGtD9L,OAAO,CAACC,GAAG,CAAC,6CAA6C2B,OAAO,CAACiK,OAAO,cAAc,IAAI,CAAC1Y,OAAO,EAAE,CAAC;;;EAG3G;EAEQiP,wBAAwBA,CAACR,OAA2B;IAC1D,IAAI,IAAI,CAACnN,QAAQ,KAAK,SAAS,EAAE;MAC/BuL,OAAO,CAACC,GAAG,CAAC,+CAA+C2B,OAAO,CAACgJ,MAAM,EAAE,CAAC;MAE5E;MACA,IAAI,CAAC/T,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,SAAS,CAAC+J,GAAG,CAAC,CAAC,CAAC;MACrB,IAAI,CAACxC,oBAAoB,GAAG,KAAK;MAEjC2B,OAAO,CAACC,GAAG,CAAC,2CAA2C2B,OAAO,CAACgJ,MAAM,EAAE,CAAC;;EAE5E;EAEc1K,mCAAmCA,CAAA;IAAA,IAAAwM,MAAA;IAAA,OAAAjN,iBAAA;MAC/C,IAAI;QACF,MAAMkN,eAAe,SAASD,MAAI,CAAC5O,WAAW,CAAC8O,eAAe,EAAE;QAChE,MAAMC,aAAa,GAAGH,MAAI,CAAC7O,WAAW,CAACiP,gBAAgB,EAAE;QAEzD,IAAI,CAACH,eAAe,IAAI,CAACE,aAAa,EAAE;UACtC7M,OAAO,CAACoE,KAAK,CAAC,6EAA6E,CAAC;UAC5FsI,MAAI,CAACvX,YAAY,CAAC0L,GAAG,CAAC,gEAAgE,CAAC;UACvF;;QAGFb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4M,aAAa,CAACE,KAAK,EAAE,OAAO,EAAEF,aAAa,CAACG,IAAI,CAAC;QAC1FhN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,CAAC,CAACyM,MAAI,CAAC5O,WAAW,CAACmP,QAAQ,EAAE,CAAC;QAEzE;QACAP,MAAI,CAACQ,oBAAoB,EAAE;OAC5B,CAAC,OAAO9I,KAAK,EAAE;QACdpE,OAAO,CAACoE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DsI,MAAI,CAACvX,YAAY,CAAC0L,GAAG,CAAC,4DAA4D,CAAC;;IACpF;EACH;EAEQqM,oBAAoBA,CAAA;IAC1BlN,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAElD;IACA,MAAMkN,OAAO,GAAgB;MAC3BC,MAAM,EAAE,OAAO;MACfC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,IAAI,CAAC3a,QAAQ,CAAC4a,QAAQ,EAAE;MAC1CX,IAAI,EAAE,EAAE;MACRY,UAAU,EAAE;KACb;IAED,IAAI,CAAC/P,WAAW,CAACgQ,qBAAqB,CAACV,OAAO,CAAC,CAAC7M,SAAS,CAAC;MACxD2D,IAAI,EAAG6J,QAAa,IAAI;QACtB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,IAAI,EAAE;QAClC,IAAID,KAAK,CAAC3T,MAAM,GAAG,CAAC,EAAE;UACpB,MAAMuQ,SAAS,GAAGoD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B/N,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE0K,SAAS,CAAC;UAEzD;UACA,IAAI,CAACsD,mBAAmB,CAACtD,SAAS,CAAC;SACpC,MAAM;UACL3K,OAAO,CAACC,GAAG,CAAC,wCAAwC,IAAI,CAAClN,QAAQ,EAAE,CAAC;UACpE,IAAI,CAACoC,YAAY,CAAC0L,GAAG,CAAC,qBAAqB,CAAC;;MAEhD,CAAC;MACDuD,KAAK,EAAEA,KAAK,IAAG;QACbpE,OAAO,CAACoE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACjP,YAAY,CAAC0L,GAAG,CAAC,mCAAmC,CAAC;QAC1D,IAAI,CAACwD,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEQ8J,8BAA8BA,CAAA;IACpClO,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IAEzE;IACA,MAAMkN,OAAO,GAAgB;MAC3BC,MAAM,EAAE,OAAO;MACfC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,EAAE;MACVC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,IAAI,CAAC3a,QAAQ,CAAC4a,QAAQ,EAAE;MAC1CX,IAAI,EAAE,EAAE;MACRY,UAAU,EAAE;KACb;IAED,IAAI,CAAC/P,WAAW,CAACgQ,qBAAqB,CAACV,OAAO,CAAC,CAAC7M,SAAS,CAAC;MACxD2D,IAAI,EAAG6J,QAAa,IAAI;QACtB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,IAAI,EAAE;QAClC,IAAID,KAAK,CAAC3T,MAAM,GAAG,CAAC,EAAE;UACpB,MAAMuQ,SAAS,GAAGoD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B/N,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE0K,SAAS,CAAC;UAEvE;UACA,IAAI,CAACsD,mBAAmB,CAACtD,SAAS,CAAC;UAEnC;UACA,IAAI,CAACD,6BAA6B,CAACC,SAAS,CAAC;SAC9C,MAAM;UACL3K,OAAO,CAACC,GAAG,CAAC,sDAAsD,IAAI,CAAClN,QAAQ,EAAE,CAAC;UAClF,IAAI,CAACoC,YAAY,CAAC0L,GAAG,CAAC,mCAAmC,CAAC;;MAE9D,CAAC;MACDuD,KAAK,EAAEA,KAAK,IAAG;QACbpE,OAAO,CAACoE,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;QAC7E,IAAI,CAACjP,YAAY,CAAC0L,GAAG,CAAC,iDAAiD,CAAC;QACxE,IAAI,CAACwD,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEQ6J,mBAAmBA,CAACtD,SAAuB;IACjD3K,OAAO,CAACC,GAAG,CAAC,sCAAsC,IAAI,CAACxL,QAAQ,QAAQ,EAAEkW,SAAS,CAACpH,SAAS,EAAEoH,SAAS,CAACJ,QAAQ,CAAC;IAEjH;IACA,MAAM4D,SAAS,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC7C,IAAID,SAAS,EAAE;MACb,IAAI,CAAC7R,iBAAiB,GAAG6R,SAAS;MAClC,IAAI,CAACrb,YAAY,CAACmQ,GAAG,CAAC,WAAW,CAAC,EAAEoL,QAAQ,CAACF,SAAS,EAAE;QAAEG,SAAS,EAAE;MAAK,CAAE,CAAC;MAC7EtO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkO,SAAS,CAAC;KACzD,MAAM;MACLnO,OAAO,CAACuO,IAAI,CAAC,sEAAsE,CAAC;MACpF,IAAI,IAAI,CAACtb,QAAQ,CAACmH,MAAM,GAAG,CAAC,EAAE;QAC5B,IAAI,CAACkC,iBAAiB,GAAG,IAAI,CAACrJ,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAI,CAACH,YAAY,CAACmQ,GAAG,CAAC,WAAW,CAAC,EAAEoL,QAAQ,CAAC,IAAI,CAACpb,QAAQ,CAAC,CAAC,CAAC,EAAE;UAAEqb,SAAS,EAAE;QAAK,CAAE,CAAC;;;IAIxF;IACA9B,UAAU,CAAC,MAAK;MACdxM,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MAEnE;MACA,IAAI,IAAI,CAACuO,qBAAqB,EAAE;QAC9B,IAAI,CAACA,qBAAqB,CAACC,aAAa,CAAC9D,SAAS,CAAC;QACnD3K,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;OAChE,MAAM;QACL;QACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,IAAI,CAACzN,iBAAiB,CAACmY,SAAS,CAAC;;IAErC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAIQyD,oBAAoBA,CAAA;IAC1B,MAAMnJ,KAAK,GAAG,IAAIjH,IAAI,EAAE;IACxB,MAAM0Q,eAAe,GAAGzJ,KAAK,CAAC0J,YAAY,EAAE,CAAC,CAAC;IAE9C,OAAO,IAAI,CAAC1b,QAAQ,CAACyR,IAAI,CAACS,IAAI,IAAIA,IAAI,CAACwJ,YAAY,EAAE,KAAKD,eAAe,CAAC,IAAI,IAAI;EACpF;EAEA;;;EAGQ3E,2BAA2BA,CAAA;IACjC/J,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;IAE9E;IACA,MAAMmM,cAAc,GAAG,IAAI,CAACzX,eAAe;IAE3C,IAAI,CAACyX,cAAc,EAAE;MACnBpM,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;KAChF,MAAM,IAAImM,cAAc,CAACjZ,OAAO,EAAE;MACjC6M,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E;KACD,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,iCAAiCmM,cAAc,CAAC7I,SAAS,IAAI6I,cAAc,CAAC7B,QAAQ,SAAS6B,cAAc,CAAC9I,MAAM,sBAAsB,CAAC;;IAGvJ;IACAkJ,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACoC,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAACV,8BAA8B,EAAE;MAErClO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEA;;;EAGQ2O,qBAAqBA,CAAA;IAC3B,MAAMT,SAAS,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC7C,IAAID,SAAS,EAAE;MACb,IAAI,CAAC7R,iBAAiB,GAAG6R,SAAS;MAClC,IAAI,CAACrb,YAAY,CAACmQ,GAAG,CAAC,WAAW,CAAC,EAAEoL,QAAQ,CAACF,SAAS,EAAE;QAAEG,SAAS,EAAE;MAAK,CAAE,CAAC;MAC7EtO,OAAO,CAACC,GAAG,CAAC,oCAAoCkO,SAAS,EAAE,CAAC;KAC7D,MAAM;MACLnO,OAAO,CAACuO,IAAI,CAAC,gEAAgE,CAAC;;EAElF;EAIc1K,yBAAyBA,CAACR,OAAqB;IAAA,IAAAwL,MAAA;IAAA,OAAApP,iBAAA;MAC3D,IAAI,CAAC4D,OAAO,EAAEC,MAAM,EAAE;QACpBuL,MAAI,CAAC1Z,YAAY,CAAC0L,GAAG,CAAC,sBAAsB,CAAC;QAC7C;;MAGF;MACA,MAAM8L,eAAe,SAASkC,MAAI,CAAC/Q,WAAW,CAAC8O,eAAe,EAAE;MAChE,MAAMC,aAAa,GAAGgC,MAAI,CAAChR,WAAW,CAACiP,gBAAgB,EAAE;MAEzD,IAAI,CAACH,eAAe,IAAI,CAACE,aAAa,EAAE;QACtCgC,MAAI,CAAC1Z,YAAY,CAAC0L,GAAG,CAAC,yCAAyC,CAAC;QAChEb,OAAO,CAACoE,KAAK,CAAC,mDAAmD,CAAC;QAClE;;MAGFpE,OAAO,CAACC,GAAG,CAAC,8CAA8CoD,OAAO,CAACC,MAAM,EAAE,CAAC;MAC3EtD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4M,aAAa,CAACE,KAAK,EAAE,OAAO,EAAEF,aAAa,CAACG,IAAI,CAAC;MAC1F6B,MAAI,CAACrZ,cAAc,CAACqL,GAAG,CAAC,IAAI,CAAC;MAC7BgO,MAAI,CAAC1Z,YAAY,CAAC0L,GAAG,CAAC,IAAI,CAAC;MAE3B;MACAgO,MAAI,CAAChR,WAAW,CAACiR,kBAAkB,CAACzL,OAAO,CAACC,MAAM,CAAC,CAAChD,SAAS,CAAC;QAC5D2D,IAAI,EAAG8K,WAAyB,IAAI;UAClC/O,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE8O,WAAW,CAAC;UACtD,IAAIA,WAAW,EAAEC,cAAc,EAAE;YAC/B;YACAH,MAAI,CAACla,eAAe,GAAGoa,WAAW;YAElC;YACA,MAAMlD,OAAO,GAAGoD,UAAU,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;YACjEN,MAAI,CAACrZ,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;YAC9BgO,MAAI,CAAC5Z,aAAa,CAAC4L,GAAG,CAACgL,OAAO,CAAC;YAE/B;YACAgD,MAAI,CAAC1b,OAAO,GAAG4b,WAAW,CAAC5b,OAAO;YAElC;YACA,IAAI0b,MAAI,CAACpa,QAAQ,KAAK,UAAU,EAAE;cAChCoa,MAAI,CAACjD,yBAAyB,CAC5BmD,WAAW,CAACzL,MAAM,EAClBuI,OAAO,EACP,GAAGkD,WAAW,CAACxL,SAAS,IAAIwL,WAAW,CAACxE,QAAQ,EAAE,EAClDwE,WAAW,CAACK,eAAe,EAC3BL,WAAW,CAAC5b,OAAO,CACpB;;YAGH,IAAI4b,WAAW,CAACK,eAAe,EAAE;cAC/BP,MAAI,CAAC1c,oBAAoB,CAAC0O,GAAG,CAACkO,WAAW,CAACK,eAAe,CAAC;cAC1DP,MAAI,CAAC3b,qBAAqB,CAAC2N,GAAG,CAAC,IAAI,CAAC;cACpCb,OAAO,CAACC,GAAG,CAAC,wCAAwC8O,WAAW,CAACxL,SAAS,qBAAqBwL,WAAW,CAACK,eAAe,EAAE,CAAC;aAC7H,MAAM;cACL;cACApP,OAAO,CAACC,GAAG,CAAC,mCAAmC8O,WAAW,CAACxL,SAAS,sBAAsBsI,OAAO,EAAE,CAAC;;WAEvG,MAAM;YACLgD,MAAI,CAACrZ,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;YAC9BgO,MAAI,CAAC1Z,YAAY,CAAC0L,GAAG,CAAC,4CAA4C,CAAC;YACnEb,OAAO,CAACoE,KAAK,CAAC,+DAA+Df,OAAO,CAACE,SAAS,EAAE,CAAC;;QAErG,CAAC;QACDa,KAAK,EAAEA,KAAK,IAAG;UACbyK,MAAI,CAACrZ,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;UAC9BgO,MAAI,CAAC1Z,YAAY,CAAC0L,GAAG,CAAC,6BAA6B,CAAC;UACpDb,OAAO,CAACoE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxDyK,MAAI,CAACxK,sBAAsB,CAACD,KAAK,CAAC;QACpC;OACD,CAAC;IAAC;EACL;EAEA;EACAiL,oBAAoBA,CAAA;IAClBrP,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,IAAI,CAAC/M,qBAAqB,CAAC2N,GAAG,CAAC,KAAK,CAAC;IACrC,IAAI,CAAChC,wBAAwB,CAACgC,GAAG,CAAC,KAAK,CAAC;IAExC;IACA,IAAI,IAAI,CAACpM,QAAQ,KAAK,UAAU,IAAI,IAAI,CAACE,eAAe,IAAI,IAAI,CAACM,aAAa,EAAE,KAAK,IAAI,EAAE;MACzF,IAAI,CAAC2W,yBAAyB,CAC5B,IAAI,CAACjX,eAAe,CAAC2O,MAAM,EAC3B,IAAI,CAACrO,aAAa,EAAE,EACpB,GAAG,IAAI,CAACN,eAAe,CAAC4O,SAAS,IAAI,IAAI,CAAC5O,eAAe,CAAC4V,QAAQ,EAAE,EACpE,IAAI,CAACpY,oBAAoB,EAAE,EAC3B,IAAI,CAACgB,OAAO,CACb;;EAEL;EAEAmc,uBAAuBA,CAAA;IACrBtP,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjD,IAAI,CAAC/M,qBAAqB,CAAC2N,GAAG,CAAC,KAAK,CAAC;IACrC,IAAI,CAAChC,wBAAwB,CAACgC,GAAG,CAAC,KAAK,CAAC;IACxC,IAAI,CAACrL,cAAc,CAACqL,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAI,CAAC1L,YAAY,CAAC0L,GAAG,CAAC,iCAAiC,CAAC;IAExD;IACA,IAAI,CAAClM,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACM,aAAa,CAAC4L,GAAG,CAAC,IAAI,CAAC;IAE5B;IACA,IAAI,IAAI,CAAC2N,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACe,cAAc,EAAE;;EAE/C;EAMQnP,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACK,yBAAyB,EAAE;MAClC,IAAI,CAACA,yBAAyB,CAAC+O,WAAW,EAAE;;IAE9C,IAAI,IAAI,CAACzO,yBAAyB,EAAE;MAClC,IAAI,CAACA,yBAAyB,CAACyO,WAAW,EAAE;;IAE9C,IAAI,IAAI,CAACpO,6BAA6B,EAAE;MACtC,IAAI,CAACA,6BAA6B,CAACoO,WAAW,EAAE;;EAEpD;;;uBAt1CWtS,eAAe,EAAAzM,EAAA,CAAAgf,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlf,EAAA,CAAAgf,iBAAA,CAAAG,EAAA,CAAAC,QAAA,GAAApf,EAAA,CAAAgf,iBAAA,CAAAK,EAAA,CAAAC,KAAA,GAAAtf,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAAxf,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAAzf,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAG,eAAA,GAAA1f,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAI,WAAA,GAAA3f,EAAA,CAAAgf,iBAAA,CAAAY,EAAA,CAAAC,SAAA,GAAA7f,EAAA,CAAAgf,iBAAA,CAAAc,EAAA,CAAAC,uBAAA,GAAA/f,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAS,WAAA,GAAAhgB,EAAA,CAAAgf,iBAAA,CAAAO,EAAA,CAAAU,WAAA;IAAA;EAAA;;;YAAfxT,eAAe;MAAAyT,SAAA;MAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAEfxgB,8BAA8B;;;;;;;;;;;;;UCxE3CG,EAAA,CAAAgC,UAAA,IAAAue,8BAAA,iBAAwE;UAWxEvgB,EAAA,CAAAgC,UAAA,IAAAwe,8BAAA,iBAwBM;UACNxgB,EAAA,CAAAE,cAAA,aAAmF;UAGjFF,EAAA,CAAAC,SAAA,SAAI;UAMJD,EAAA,CAAAE,cAAA,aAAiB;UAabF,EAAA,CAAAgC,UAAA,IAAAye,mCAAA,uBA0DW;UACXzgB,EAAA,CAAAgC,UAAA,IAAA0e,8BAAA,iBAIM;UAEN1gB,EAAA,CAAAgC,UAAA,IAAA2e,sCAAA,gCAAA3gB,EAAA,CAAAmD,sBAAA,CAgEc;UAEhBnD,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAE,cAAA,cAA+C;UAKrCF,EAAA,CAAAiB,MAAA,uBACA;UAYFjB,EAAA,CAAAW,YAAA,EAAiB;UAEnBX,EAAA,CAAAE,cAAA,wBAAkB;UAEhBF,EAAA,CAAAgC,UAAA,KAAA4e,+BAAA,kBAGM;UAEN5gB,EAAA,CAAAgC,UAAA,KAAA6e,+BAAA,kBAGM;UAGN7gB,EAAA,CAAAgC,UAAA,KAAA8e,+BAAA,kBAaM;UAEN9gB,EAAA,CAAAgC,UAAA,KAAA+e,+BAAA,kBA4BM;UACR/gB,EAAA,CAAAW,YAAA,EAAmB;UACnBX,EAAA,CAAAgC,UAAA,KAAAgf,4CAAA,iCAuJmB;UACrBhhB,EAAA,CAAAW,YAAA,EAAW;UAQjBX,EAAA,CAAAgC,UAAA,KAAAif,+BAAA,kBAMM;UAGNjhB,EAAA,CAAAgC,UAAA,KAAAkf,+BAAA,mBAcM;UAKNlhB,EAAA,CAAAgC,UAAA,KAAAmf,+BAAA,kBAMM;UAGNnhB,EAAA,CAAAgC,UAAA,KAAAof,+BAAA,kBA0BM;UAGNphB,EAAA,CAAAgC,UAAA,KAAAqf,uCAAA,iCAAArhB,EAAA,CAAAmD,sBAAA,CA4Cc;UAvfhBnD,EAAA,CAAAW,YAAA,EAAmF;;;;UApC7EX,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAtc,QAAA,eAAiC;UAWdhE,EAAA,CAAAY,SAAA,GAAgC;UAAhCZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAtc,QAAA,cAAgC;UAyB1BhE,EAAA,CAAAY,SAAA,GAAmD;UAAnDZ,EAAA,CAAAuK,WAAA,kBAAA+V,GAAA,CAAAtc,QAAA,eAAmD;UAsB5ChE,EAAA,CAAAY,SAAA,GAAiC;UAAjCZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAtc,QAAA,eAAiC;UA4D9DhE,EAAA,CAAAY,SAAA,GAAmH;UAAnHZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAA9S,WAAA,OAAA8S,GAAA,CAAAtc,QAAA,mBAAAsc,GAAA,CAAAtc,QAAA,kBAAAsc,GAAA,CAAArc,qBAAA,IAAmH,aAAAqd,GAAA;UA+F1GthB,EAAA,CAAAY,SAAA,IAAyD;UAAzDZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAtc,QAAA,kBAAAsc,GAAA,CAAApS,kBAAA,GAAyD;UAKzDlO,EAAA,CAAAY,SAAA,GAA+B;UAA/BZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAla,YAAA,CAAAuD,MAAA,OAA+B;UAM/B3J,EAAA,CAAAY,SAAA,GAAiC;UAAjCZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAApc,eAAA,KAAAoc,GAAA,CAAA5d,OAAA,CAAiC;UAejC1C,EAAA,CAAAY,SAAA,GAA6B;UAA7BZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAla,YAAA,CAAAuD,MAAA,KAA6B;UA8BlB3J,EAAA,CAAAY,SAAA,GAA6B;UAA7BZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAAla,YAAA,CAAAuD,MAAA,KAA6B;UAgKlD3J,EAAA,CAAAY,SAAA,GAA6F;UAA7FZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAApc,eAAA,IAAAoc,GAAA,CAAA9b,aAAA,eAAA8b,GAAA,CAAAtc,QAAA,oBAAAsc,GAAA,CAAA5d,OAAA,CAA6F;UAS7F1C,EAAA,CAAAY,SAAA,GAA0D;UAA1DZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAA1S,oBAAA,IAAA0S,GAAA,CAAAtc,QAAA,gBAA0D;UAmB1DhE,EAAA,CAAAY,SAAA,GAAoF;UAApFZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAA1S,oBAAA,IAAA0S,GAAA,CAAAtc,QAAA,kBAAAsc,GAAA,CAAArc,qBAAA,GAAoF;UASpFjE,EAAA,CAAAY,SAAA,GAAuD;UAAvDZ,EAAA,CAAAa,UAAA,SAAAyf,GAAA,CAAArS,gBAAA,MAAAqS,GAAA,CAAAtc,QAAA,eAAuD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}