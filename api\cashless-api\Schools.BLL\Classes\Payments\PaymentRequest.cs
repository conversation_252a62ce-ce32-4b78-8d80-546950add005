﻿using System;
using Schools.BLL.ThirdParty.General;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;
using User = Schools.DAL.DtosToMoveToBLL.User;

namespace Schools.BLL.Classes.Payments;

/// <summary>
/// This request is used in the "cashless-process-order" logic app.
/// New fields need to be added with care
/// </summary>
public class PaymentRequest : ICloneable
{
    public Decimal chargeAmount { get; set; }
    public Decimal topupAmount { get; set; }
    public string nonce { get; set; }
    public string payment_method_id { get; set; }
    public string toExternalCustomerId { get; set; }
    public string fromExternalCustomerId { get; set; }
    public int userId { get; set; }
    public string device_data { get; set; }
    public PaypalSupplementaryData supplementary_data { get; set; }
    public int? canteenId { get; set; }
    public string Message { get; set; }

    /// <summary>
    /// To help classify different payments in transaction history
    ///
    /// TODO - Remove once the logic app is retired
    /// </summary>
    public PaymentTypeEnum? paymentType { get; set; }

    /// <summary>
    /// To help classify different money movements in transaction history
    /// </summary>
    public TransactionTypeEnum? transactionType { get; set; }

    /// <summary>
    /// The User making the payment
    /// </summary>
    public User fromUser { get; set; }

    /// <summary>
    /// The User receiving the payment - can be another Parent, Canteen or the Spriggy Fee Account
    /// </summary>
    public User toUser { get; set; }

    /// <summary>
    /// Get the payment type and default to unknown if needed
    /// </summary>
    public PaymentTypeEnum GetPaymentType()
    {
        return paymentType.HasValue ? paymentType.Value : PaymentTypeEnum.Unknown;
    }

    /// <summary>
    /// Get the transaction type for this payment request
    /// </summary>
    public TransactionTypeEnum GetTransactionType()
    {
        return transactionType.HasValue ? transactionType.Value : TransactionTypeEnum.Unknown;
    }

    /// <summary>
    /// Clone this request
    /// </summary>
    public object Clone()
    {
        return this.MemberwiseClone();
    }
}

public class PaymentResponse
{
    public CashlessMakePaymentResponse PaymentDetails { get; set; }
}

public class RefundAccountBalanceResponse
{
    public decimal AccountBalance { get; set; }
    public CashlessMakePaymentResponse PaymentDetails { get; set; }
}

public class PaymentHistory
{
    public Decimal chargeAmount { get; set; }
    public Decimal topupAmount { get; set; }
    public string nonce { get; set; }
    public string toExternalCustomerId { get; set; }
    public string fromExternalCustomerId { get; set; }
    public int userId { get; set; }
    public Decimal updatedBalance { get; set; }
    public bool declined { get; set; }
}
