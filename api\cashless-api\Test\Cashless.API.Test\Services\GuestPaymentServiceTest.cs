using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Services;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.ThirdParty.General;
using Schools.DAL.Entities;
using Schools.DAL.Interfaces;

namespace Cashless.API.Test.Services
{
    [TestFixture]
    public class GuestPaymentServiceTest
    {
        private Mock<IStripeService> _mockStripeService;
        private Mock<IUserService> _mockUserService;
        private Mock<ICanteenService> _mockCanteenService;
        private Mock<IOrderService> _mockOrderService;
        private Mock<ITelemetryService> _mockTelemetryService;
        private Mock<ILogger<GuestPaymentService>> _mockLogger;
        private GuestPaymentService _guestPaymentService;

        [SetUp]
        public void Setup()
        {
            _mockStripeService = new Mock<IStripeService>();
            _mockUserService = new Mock<IUserService>();
            _mockCanteenService = new Mock<ICanteenService>();
            _mockOrderService = new Mock<IOrderService>();
            _mockTelemetryService = new Mock<ITelemetryService>();
            _mockLogger = new Mock<ILogger<GuestPaymentService>>();

            _guestPaymentService = new GuestPaymentService(
                _mockStripeService.Object,
                _mockUserService.Object,
                _mockCanteenService.Object,
                _mockOrderService.Object,
                _mockTelemetryService.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task ValidateGuestCard_ValidCard_ReturnsValid()
        {
            // Arrange
            var request = new GuestCardValidationRequest
            {
                CardNumber = "****************", // Valid test Visa number
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "123"
            };

            // Act
            var result = await _guestPaymentService.ValidateGuestCard(request);

            // Assert
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual("Visa", result.CardType);
            Assert.IsTrue(result.MaskedCardNumber.Contains("1111"));
        }

        [Test]
        public async Task ValidateGuestCard_InvalidCard_ReturnsInvalid()
        {
            // Arrange
            var request = new GuestCardValidationRequest
            {
                CardNumber = "1234567890123456", // Invalid card number
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "123"
            };

            // Act
            var result = await _guestPaymentService.ValidateGuestCard(request);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("Invalid card number", result.ErrorMessage);
        }

        [Test]
        public async Task ValidateGuestCard_ExpiredCard_ReturnsInvalid()
        {
            // Arrange
            var request = new GuestCardValidationRequest
            {
                CardNumber = "****************",
                ExpiryMonth = 1,
                ExpiryYear = 2020, // Expired
                CVV = "123"
            };

            // Act
            var result = await _guestPaymentService.ValidateGuestCard(request);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("Card has expired or invalid expiry date", result.ErrorMessage);
        }

        [Test]
        public async Task ValidateGuestCard_InvalidCVV_ReturnsInvalid()
        {
            // Arrange
            var request = new GuestCardValidationRequest
            {
                CardNumber = "****************",
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "12" // Too short
            };

            // Act
            var result = await _guestPaymentService.ValidateGuestCard(request);

            // Assert
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual("Invalid CVV", result.ErrorMessage);
        }

        [Test]
        public async Task ValidateGuestPermissions_ValidGuestUser_ReturnsTrue()
        {
            // Arrange
            var guestUserId = 123;
            var canteenId = 1;
            var mockUser = new UserEntity
            {
                UserId = guestUserId,
                IsGuest = true,
                IsActive = true,
                SchoolId = 1
            };

            _mockUserService.Setup(x => x.GetUserById(guestUserId))
                .ReturnsAsync(mockUser);

            // Act
            var result = await _guestPaymentService.ValidateGuestPermissions(guestUserId, canteenId);

            // Assert
            Assert.IsTrue(result);
        }

        [Test]
        public async Task ValidateGuestPermissions_NonGuestUser_ReturnsFalse()
        {
            // Arrange
            var guestUserId = 123;
            var canteenId = 1;
            var mockUser = new UserEntity
            {
                UserId = guestUserId,
                IsGuest = false, // Not a guest
                IsActive = true,
                SchoolId = 1
            };

            _mockUserService.Setup(x => x.GetUserById(guestUserId))
                .ReturnsAsync(mockUser);

            // Act
            var result = await _guestPaymentService.ValidateGuestPermissions(guestUserId, canteenId);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public async Task ValidateGuestPermissions_InactiveUser_ReturnsFalse()
        {
            // Arrange
            var guestUserId = 123;
            var canteenId = 1;
            var mockUser = new UserEntity
            {
                UserId = guestUserId,
                IsGuest = true,
                IsActive = false, // Inactive
                SchoolId = 1
            };

            _mockUserService.Setup(x => x.GetUserById(guestUserId))
                .ReturnsAsync(mockUser);

            // Act
            var result = await _guestPaymentService.ValidateGuestPermissions(guestUserId, canteenId);

            // Assert
            Assert.IsFalse(result);
        }

        [Test]
        public async Task ProcessGuestPayment_InvalidPermissions_ReturnsFailure()
        {
            // Arrange
            var request = new GuestPaymentRequest
            {
                GuestUserId = 123,
                CanteenId = 1,
                Amount = 10.50m,
                CardNumber = "****************",
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "123",
                CardholderName = "Test User",
                Items = new List<GuestOrderItem>()
            };

            _mockUserService.Setup(x => x.GetUserById(request.GuestUserId))
                .ReturnsAsync((UserEntity)null); // User not found

            // Act
            var result = await _guestPaymentService.ProcessGuestPayment(request);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("PERMISSION_DENIED", result.ErrorCode);
        }

        [Test]
        public async Task ProcessGuestPayment_InvalidCard_ReturnsFailure()
        {
            // Arrange
            var request = new GuestPaymentRequest
            {
                GuestUserId = 123,
                CanteenId = 1,
                Amount = 10.50m,
                CardNumber = "1234567890123456", // Invalid card
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "123",
                CardholderName = "Test User",
                Items = new List<GuestOrderItem>()
            };

            var mockUser = new UserEntity
            {
                UserId = 123,
                IsGuest = true,
                IsActive = true,
                SchoolId = 1
            };

            _mockUserService.Setup(x => x.GetUserById(request.GuestUserId))
                .ReturnsAsync(mockUser);

            // Act
            var result = await _guestPaymentService.ProcessGuestPayment(request);

            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("INVALID_CARD", result.ErrorCode);
        }

        [Test]
        public async Task GetGuestPaymentStatus_ValidOrderId_ReturnsStatus()
        {
            // Arrange
            var request = new GuestPaymentStatusRequest
            {
                OrderId = "ORDER_123"
            };

            // Act
            var result = await _guestPaymentService.GetGuestPaymentStatus(request);

            // Assert
            Assert.AreEqual("COMPLETED", result.Status);
            Assert.AreEqual("ORDER_123", result.OrderId);
            Assert.IsTrue(result.IsCompleted);
        }

        [TestCase("****************", "Visa")]
        [TestCase("****************", "Mastercard")]
        [TestCase("***************", "American Express")]
        [TestCase("1234567890123456", "Unknown")]
        public void DetectCardType_VariousCardNumbers_ReturnsCorrectType(string cardNumber, string expectedType)
        {
            // This tests the private method indirectly through ValidateGuestCard
            var request = new GuestCardValidationRequest
            {
                CardNumber = cardNumber,
                ExpiryMonth = 12,
                ExpiryYear = 2025,
                CVV = "123"
            };

            var result = _guestPaymentService.ValidateGuestCard(request).Result;

            if (expectedType != "Unknown")
            {
                Assert.AreEqual(expectedType, result.CardType);
            }
        }
    }
}
