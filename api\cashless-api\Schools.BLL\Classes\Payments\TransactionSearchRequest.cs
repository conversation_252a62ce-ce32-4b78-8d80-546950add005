﻿using System;

namespace Schools.BLL.Classes.Payments
{
    public class TransactionSearchRequest
    {
        public string CustomerId { get; set; }
        public string TxnId { get; set; }
        public string CodeId { get; set; }
        public string TransRef { get; set; }
        public string TransStatus { get; set; }
        public string TransType { get; set; }
        public string TransResult { get; set; }

        // search fields
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public DateTime CreatedFrom { get; set; }
        public DateTime CreatedTo { get; set; }

        // used to store to SQL
        public string TransGatewayDetails { get; set; }
    }
}
