using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Hydrators;
using Schools.BLL.Assemblers;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Audit;
using Schools.BLL.Classes.Canteens;
using Schools.BLL.Classes.Menus;
using Schools.BLL.Classes.Stocks;
using Schools.BLL.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.DAL.Entities;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using Schools.DAL.Enums;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EditorController : ControllerBase
    {
        private readonly ITelemetryService _telemetryService;
        private readonly IDBHelper _dbHelper;
        private readonly IStocksService _stocksService;
        private readonly ICanteenService _canteenService;
        private readonly IEditorService _editorService;
        private readonly ICategoryService _categoryService;
        private readonly IHydrator<List<MenuCategoryEntity>, List<ItemCategory>> _itemCategoriesHydrator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEditorValidator _editorValidator;
        private readonly IAuditService _auditService;
        private readonly ILogger<EditorController> _logger;
        private readonly IFileStorageService _fileStorageService;

        // constructor
        public EditorController(ITelemetryService telemetryService, IDBHelper dbHelper,
                                ISchoolService schoolService, IStocksService stocksService, ICanteenService canteenService,
                                IEditorService editorService, ICategoryService categoryService, IHydrator<List<MenuCategoryEntity>,
                                List<ItemCategory>> itemCategoriesHydrator, IUnitOfWork unitOfWork, IEditorValidator editorValidator,
                                IAuditService auditService, ILogger<EditorController> logger, IFileStorageService fileStorageService)
        {
            _telemetryService = telemetryService;
            _dbHelper = dbHelper;
            _stocksService = stocksService;
            _canteenService = canteenService;
            _editorService = editorService;
            _categoryService = categoryService;
            _itemCategoriesHydrator = itemCategoriesHydrator;
            _unitOfWork = unitOfWork;
            _editorValidator = editorValidator;
            _auditService = auditService;
            _logger = logger;
            _fileStorageService = fileStorageService;
        }

        [Route("GetSchoolList")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetSchoolList([FromBody] SchoolsRequest request)
        {
            // To Do: Restrict to just school names
            ListSchoolsResponse response = new ListSchoolsResponse();

            if (request.UserId == null && String.IsNullOrEmpty(request.PartialName)) throw new ValidationException("User Id or search must be supplied");

            response.Schools = await this._dbHelper.ExecSprocByParams<List<School>>("sp_Editor_School_List_Menu_GetBy_Search",
                                                    new Dictionary<string, string>() {
                                                        { "userId", $"{request.UserId}" },
                                                        { "search", $"{request.PartialName}" }
                                                    });

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Generate all the menus
        /// This will create a huge load on the DB, we need to be carefull when we run it and also be sure to not run it too many times
        /// (best to run it between 4 and 5pm during the week or during a weekend)
        /// </summary>
        [Route("GenerateAllMenus")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        /* *****
            - To Do: Follow up - can this now be moved or removed?
            - Not referenced in the front end
        ***** */
        public async Task<IActionResult> GenerateAllMenus()
        {
            // get all merchants
            // TODO move this to "menuService.GenerateAllMenus()" once telemetry is DI
            List<GetAllMerchantResponse> merchants = await this._canteenService.GetAllMerchants();

            // generate all menus
            await this._editorService.GenerateAllMenus(merchants);

            return new OkResult();
        }

        [Route("GenerateAllMenus/{schoolId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin)]
        /* *****
            - To Do: Follow up - can this now be moved or removed?
            - Not referenced in the front end
        ***** */
        public async Task<IActionResult> GenerateAllMenus(int schoolId)
        {
            if (schoolId == 118218)
            {
                // get the menu by his ID
                var res = await this._dbHelper.ExecSprocByParams<List<AllMenusResult>>("sp_Menus_GetAll_Active",
                                                new Dictionary<string, string>() { });

                if (res != null && res.Count > 0)
                {
                    foreach (var menu in res)
                    {
                        try
                        {
                            // TODO - Fix this silent failure 
                            await _editorService.GenerateMenu(menu.MenuId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Failed to generate menu - MenuId: {MenuId} - Exception: {Exception}", menu.MenuId, ex);

                            this._telemetryService.TrackException(new MenuException("Failed to generate menu", ex));
                        }
                    }
                }
            }

            return new OkResult();
        }

        [Route("GetItemsByCanteen/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetItemsByCanteen(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId: canteenId);

            List<MenuItem> items = await this._dbHelper.ExecSprocByParams<List<MenuItem>>("sp_MenuItems_GetBy_Canteen",
                                                        new Dictionary<string, string>() { { "canteenId", $"{canteenId}" } });

            return new OkObjectResult(items);
        }

        [Route("items/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetItemForSelectListsByCanteen(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId: canteenId);

            var items = await this._editorService.GetItemsForSelectListByCanteenId(canteenId);

            return new OkObjectResult(items);
        }

        [Route("UpdateLinkItemAndMenu")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateLinkItemAndMenu([FromBody] ItemMenus request)
        {
            if (request.MenuItemId == null) throw new ValidationException("MenuItemId", "Invalid request");

            await this._editorValidator.ValidateUserAccessToMenu(request.MenuId);

            if (request.IsSelected == true)
            {
                await this._editorService.LinkItemToMenu(request.MenuId, Convert.ToInt32(request.MenuItemId));
            }
            else
            {
                await this._editorService.UnlinkItemFromMenu(request.MenuId, Convert.ToInt32(request.MenuItemId));
            }

            MenuItem response = new();

            return new OkObjectResult(response);
        }

        [Route("GetCategoriesMenusByCanteenId/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetCategoriesMenusByCanteenId(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId);
            CanteenCategoriesMenus list = await this._dbHelper.ExecSprocById<CanteenCategoriesMenus>(
                                                                "sp_Editor_Categories_Menus_List_GetBy_CanteenId", canteenId);

            return new OkObjectResult(list);
        }

        [Route("GetCategoriesForEditorByCanteenId/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetCategoriesForEditorByCanteenId(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId);

            var records = await this._editorService.GetCategoriesForEditorByCanteenId(canteenId);
            var newList = this._itemCategoriesHydrator.Hydrate(records.ToList());

            return new OkObjectResult(newList);
        }

        [Route("GetCategoriesImagesByCanteenType/{canteenType}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetCategoriesImagesByCanteenType(string canteenType)
        {
            IEnumerable<CategoryImage> list = await this._unitOfWork.MenuRepository.GetCategoriesImagesByCanteenType(canteenType);

            var res = new List<string>();

            if (list != null)
            {
                res = list.Select(x => x.ImageName).ToList();
            }

            return new OkObjectResult(res);
        }

        [Route("InsertCategory")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> InsertCategory([FromBody] ItemCategoryInsertRequest request)
        {
            var response = await _categoryService.InsertCategory(request);

            return new OkObjectResult(response);
        }

        [Route("UpdateCategory")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Merchant)]
        public async Task<IActionResult> UpdateCategory([FromBody] ItemCategoryUpdateRequest request)
        {
            var response = await _categoryService.UpdateCategory(request);

            // Re-generate the menus once 
            await _editorService.GenerateMenusByCanteen(response.CanteenId);

            return new OkObjectResult(response);
        }


        [Route("ArchiveCategory/{categoryId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> ArchiveCategory(int categoryId)
        {
            // To Do: get category service, ensure exist and check user access

            // Upsert Category
            var response = await this._dbHelper.ExecSprocById("sp_Category_Archive", categoryId);

            return new OkResult();
        }

        [Route("GetCategoriesListByCanteenId/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetCategoriesListByCanteenId(long canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId);

            var list = await this._editorService.GetCategoriesListByCanteenId(canteenId);

            return new OkObjectResult(list);
        }

        [Route("GetItemOptions/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetItemOptions(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId: canteenId);

            ListOptionsResponse response = new ListOptionsResponse();
            response.Options = await this._dbHelper.ExecSprocById<List<Option>>(
                                                    "sp_Editor_Options_List_GetBy_Canteen_ID", canteenId);

            return new OkObjectResult(response);
        }

        [Route("GetOptionById/{optionId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetOptionById(int optionId)
        {
            Option optionResponse = await this._dbHelper.ExecSprocById<Option>("sp_Editor_Option_GetBy_ID", optionId);
            await this._editorValidator.ValidateAccessToCanteen(optionResponse.CanteenId);
            return new OkObjectResult(optionResponse);
        }

        [Route("GetItemsByOptionId/{optionId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetItemsByOptionId(int optionId)
        {
            List<MenuItem> listItems = await this._dbHelper.ExecSprocById<List<MenuItem>>(
                                                            "sp_Editor_MenuItems_GetBy_Option_ID", optionId);
            await this._editorValidator.ValidateAccessToMenuItems(listItems);

            return new OkObjectResult(listItems);
        }

        [Route("RemoveOptionFromItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> RemoveOptionFromItem([FromBody] AddRemoveOptionRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            if (request.CanteenId != null) await this._editorValidator.ValidateAccessToCanteen((long)request.CanteenId);
            else await this._editorValidator.ValidateUserAccessToMenu(request.MenuId);

            await this._dbHelper.ExecSprocByParams("sp_Editor_MenuItems_Remove_Option",
                                new Dictionary<string, string>() {
                                        { "itemId", $"{request.ItemId}" },
                                        { "optionId", $"{request.OptionId}" }
                                });

            // Generate Items' Menus
            MenuItem item = new MenuItem
            {
                MenuItemId = request.ItemId,
                CanteenId = request.CanteenId
            };

            await this._editorService.GenerateMenusByMenuItem(item);

            // Add log to audit service
            MerchantItemAuditLog log = new(ItemSubTypeAuditlogEnum.Edit)
            {
                Description = "Removed option from item",
                MerchantId = Convert.ToInt64(request.CanteenId)
            };

            log.Metadata.Add("itemId", $"{request.ItemId}");
            log.Metadata.Add("optionId", $"{request.OptionId}");

            //await this._auditService.MerchantItemlog(log);

            return new OkResult();
        }

        [Route("AddOptionToItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> AddOptionToItem([FromBody] AddRemoveOptionRequest request)
        {

            this._editorValidator.ValidateAddRemoveOptionRequest(request);
            await this._editorValidator.ValidateAccessToCanteen((long)request.CanteenId);

            await this._dbHelper.ExecSprocByParams("sp_Editor_MenuItems_Add_Option",
                                new Dictionary<string, string>() {
                                    { "itemId", $"{request.ItemId}" },
                                    { "optionId", $"{request.OptionId}" },
                                    { "canteenId", $"{request.CanteenId}" }
                                });

            // Generate Items' Menus
            MenuItem item = new MenuItem
            {
                MenuItemId = request.ItemId,
                CanteenId = request.CanteenId
            };

            await this._editorService.GenerateMenusByMenuItem(item);

            // Add log to audit service
            MerchantItemAuditLog log = new(ItemSubTypeAuditlogEnum.Edit)
            {
                Description = "Add option to item",
                MerchantId = Convert.ToInt64(request.CanteenId)
            };

            log.Metadata.Add("itemId", $"{request.ItemId}");
            log.Metadata.Add("optionId", $"{request.OptionId}");

            //await this._auditService.MerchantItemlog(log);

            return new OkResult();
        }

        [Route("UpsertItemOptions")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertItemOptions([FromBody] Option option)
        {
            if (option == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateAccessToCanteen(option.CanteenId);

            bool isEditOption = option.MenuItemOptionsCategoryId > 0;

            // Upsert Options
            var optionToUpsert = JsonConvert.SerializeObject(option);
            var response = await this._dbHelper.ExecSproc("sp_MenuItemsOptionsCategories_Upsert", optionToUpsert);

            if (!String.IsNullOrEmpty(response))
            {
                option.MenuItemOptionsCategoryId = Convert.ToInt32(response);

                // generate the menus only if it's an edit
                if (isEditOption)
                {
                    await this._editorService.GenerateMenusByItemOption(option.MenuItemOptionsCategoryId);
                }
            }

            // Add log to audit service
            MerchantOptionAuditLog log = new(OptionSubTypeAuditlogEnum.Edit)
            {
                Description = "Upsert Option",
                MerchantId = Convert.ToInt64(option.CanteenId)
            };

            log.Metadata.Add("OptionId", $"{option.MenuItemOptionsCategoryId}");
            log.Metadata.Add("Name", $"{option.Name}");

            //await this._auditService.MerchantOptionlog(log);

            return new OkObjectResult(option);
        }

        [Route("ArchiveItemOptions")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> ArchiveItemOptions([FromBody] Option option)
        {
            if (option == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateAccessToCanteen(option.CanteenId);

            option.IsActive = false;
            option.IsArchived = true;

            var optionToUpsert = JsonConvert.SerializeObject(option);
            await this._dbHelper.ExecSproc("sp_MenuItemsOptionsCategories_Upsert", optionToUpsert);

            // update the JSON object in database
            await this._editorService.GenerateMenusByItemOption(option.MenuItemOptionsCategoryId);

            // Add log to audit service
            MerchantOptionAuditLog log = new(OptionSubTypeAuditlogEnum.Delete)
            {
                Description = "Archive Option",
                MerchantId = Convert.ToInt64(option.CanteenId)
            };

            log.Metadata.Add("OptionId", $"{option.MenuItemOptionsCategoryId}");
            log.Metadata.Add("Name", $"{option.Name}");

            //await this._auditService.MerchantOptionlog(log);

            return new OkObjectResult(option);
        }

        [Route("UpsertSubOption")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertSubOption([FromBody] SubOption subOption)
        {
            if (subOption == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateAccessToCanteen(subOption.CanteenId);

            bool isEditOption = subOption.MenuItemOptionId > 0;
            var subOptionToUpsert = JsonConvert.SerializeObject(subOption);
            var responseSub = await this._dbHelper.ExecSproc("sp_MenuItemOption_Upsert", subOptionToUpsert);

            if (!String.IsNullOrEmpty(responseSub))
            {
                subOption.MenuItemOptionId = Convert.ToInt32(responseSub);
            }

            // generate the menus only if it's an edit
            if (isEditOption)
            {
                await this._editorService.GenerateMenusByItemOption(subOption.MenuItemOptionsCategoryId);
            }

            // Add log to audit service
            MerchantOptionAuditLog log = new(OptionSubTypeAuditlogEnum.Edit)
            {
                Description = "Upsert SubOption",
                MerchantId = subOption.CanteenId > 0 ? Convert.ToInt64(subOption.CanteenId) : 0
            };

            log.Metadata.Add("SubOptionId", $"{subOption.MenuItemOptionId}");
            log.Metadata.Add("Name", $"{subOption.OptionName}");

            //await this._auditService.MerchantOptionlog(log);

            return new OkObjectResult(subOption);
        }

        [Route("ArchiveSubOption")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> ArchiveSubOption([FromBody] SubOption subOption)
        {
            if (subOption == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateAccessToCanteen(subOption.CanteenId);

            subOption.IsActive = false;
            // option.IsArchived = true;

            var subOptionToUpsert = JsonConvert.SerializeObject(subOption);
            await this._dbHelper.ExecSproc("sp_MenuItemOption_Upsert", subOptionToUpsert);

            // update the JSON object in database
            await this._editorService.GenerateMenusByItemOption(subOption.MenuItemOptionsCategoryId);

            // Add log to audit service
            MerchantOptionAuditLog log = new(OptionSubTypeAuditlogEnum.Delete)
            {
                Description = "Archive SubOption",
                MerchantId = subOption.CanteenId > 0 ? Convert.ToInt64(subOption.CanteenId) : 0
            };

            log.Metadata.Add("SubOptionId", $"{subOption.MenuItemOptionId}");
            log.Metadata.Add("Name", $"{subOption.OptionName}");

            //await this._auditService.MerchantOptionlog(log);

            return new OkObjectResult(subOption);
        }

        [Route("SubOption")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> InsertSubOption([FromBody] SubOption subOption)
        {
            if (subOption == null) throw new ValidationException("Invalid request");

            var subOptionId = await _editorService.InsertSubOption(subOption);

            subOption.MenuItemOptionId = subOptionId;

            return new OkObjectResult(subOption);
        }

        [Route("SubOption")]
        [HttpPatch]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateSubOption([FromBody] SubOptionEdit subOption)
        {
            if (subOption == null) throw new ValidationException("Invalid request");

            var isMenuItemOptionsCategoryUpdated = await _editorService.UpdateSubOption(subOption);

            return new OkObjectResult(isMenuItemOptionsCategoryUpdated);
        }

        [Route("SubOption/{subOptionId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> ArchiveSubOption(int subOptionId)
        {
            var isMenuItemOptionsCategoryUpdated = await _editorService.ArchiveSubOption(subOptionId);

            return new OkObjectResult(isMenuItemOptionsCategoryUpdated);
        }

        [Route("UpsertMenu")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertMenu([FromBody] Menu menu)
        {
            await this._editorValidator.ValidateMenu(menu);
            Menu menuResponse = new Menu();

            var menuToUpsert = JsonConvert.SerializeObject(menu);
            var response = await this._dbHelper.ExecSproc("sp_Menu_Upsert", menuToUpsert);

            if (!String.IsNullOrEmpty(response))
            {
                menuResponse = menu;
                menuResponse.MenuId = Convert.ToInt32(response);
            }

            // Add log to audit service
            MerchantMenuAuditLog log = new(MenuSubTypeAuditlogEnum.Edit)
            {
                Description = "Upsert Menu",
                MerchantId = menu.CanteenId > 0 ? Convert.ToInt64(menu.CanteenId) : 0
            };

            log.Metadata.Add("MenuId", $"{menu.MenuId}");
            log.Metadata.Add("FriendlyName", $"{menu.FriendlyName}");

            //await this._auditService.MerchantMenulog(log);

            return new OkObjectResult(menuResponse);
        }

        [Route("UpsertMenuItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertMenuItem([FromBody] MenuItem menuItem)
        {
            await this._editorValidator.ValidateRequest(menuItem);

            bool isExistingItem = menuItem.MenuItemId > 0;

            var menuItemToUpsert = JsonConvert.SerializeObject(menuItem);
            var response = await this._dbHelper.ExecSproc("sp_MenuItem_Upsert", menuItemToUpsert);

            MenuItem menuItemResponse = new();

            if (!String.IsNullOrEmpty(response))
            {
                menuItemResponse = menuItem;
                menuItemResponse.MenuItemId = Convert.ToInt32(response);

                if (menuItem.Availabilities.Count > 0)
                {
                    menuItem.Availabilities.ForEach(availability =>
                    {
                        availability.MenuItemId = Convert.ToInt64(menuItemResponse.MenuItemId);
                    });
                    var noticeJson = JsonConvert.SerializeObject(menuItem.Availabilities);
                    await this._dbHelper.ExecSproc("sp_MenuItemAvailability_Upsert", noticeJson);
                }

                List<MenuItemAvailability> listItemAvailDates = new List<MenuItemAvailability>();
                listItemAvailDates = await this._editorService.GetItemAvailableDates(Convert.ToInt64(menuItemResponse.MenuItemId));

                if (listItemAvailDates.Count > 0)
                {
                    menuItemResponse.Availabilities = listItemAvailDates;
                }

                // Link to Category
                await this._dbHelper.ExecSproc("sp_CategoryItem_Upsert", JsonConvert.SerializeObject(menuItemResponse));

                // reload items and schools menus if it's a new item
                if (!isExistingItem)
                {
                    menuItemResponse.Menus = await this._unitOfWork.MenuRepository.GetMenusListByCanteenAndMenuItem(menuItemResponse.CanteenId, Convert.ToInt64(menuItemResponse.MenuItemId));
                }
                else if (menuItem.Menus != null && menuItem.Menus.Count > 0)
                {
                    // Generate Items' Menus
                    await this._editorService.GenerateMenusByMenuItem(menuItemResponse);
                }

                // Add log to audit service
                MerchantItemAuditLog log = new(ItemSubTypeAuditlogEnum.Edit)
                {
                    Description = "Upsert MenuItem",
                    MerchantId = menuItem.CanteenId > 0 ? Convert.ToInt64(menuItem.CanteenId) : 0
                };

                log.Metadata.Add("MenuItemId", $"{menuItem.MenuItemId}");

                //await this._auditService.MerchantItemlog(log);
            }

            return new OkObjectResult(menuItemResponse);
        }

        [Route("DeleteMenuItem")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteMenuItem([FromBody] MenuItem menuItem)
        {
            menuItem.IsActive = false;
            menuItem.IsDeleted = true;

            await this._editorValidator.ValidateRequestToDeleteMenuItem(menuItem);

            var menuItemToUpsert = JsonConvert.SerializeObject(menuItem);
            await this._dbHelper.ExecSproc("sp_MenuItem_Delete", menuItemToUpsert);
            await this._editorService.GenerateMenusByMenuItem(menuItem);

            // Add log to audit service
            MerchantItemAuditLog log = new(ItemSubTypeAuditlogEnum.Delete)
            {
                Description = "Delete MenuItem",
                MerchantId = menuItem.CanteenId > 0 ? Convert.ToInt64(menuItem.CanteenId) : 0
            };

            log.Metadata.Add("MenuItemId", $"{menuItem.MenuItemId}");

            //await this._auditService.MerchantItemlog(log);

            return new OkObjectResult(menuItem);
        }

        [Route("GetOptionsByItemId/{menuItemId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetOptionsByItemId(int menuItemId)
        {
            await this._editorValidator.ValidateUserAccessToMenuItem(menuItemId);
            List<Option> listOptions = await this._dbHelper.ExecSprocById<List<Option>>("sp_Editor_Options_GetBy_MenuItem_ID", menuItemId);

            return new OkObjectResult(listOptions);
        }

        #region Images

        [Route("UploadImage")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UploadImage([FromForm(Name = "uploadedFile")] IFormFile file, long itemId, string schoolCode)
        {
            // Upload file
            if (file == null || file.Length == 0)
            {
                throw new Exception("No file");
            }

            await this._editorValidator.ValidateUserAccessToMenuItem(itemId);

            string fileUrl = await this._editorService.UploadItemImage(file, itemId);

            return Content(fileUrl);
        }

        [Route("DeleteImage")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteImage([FromBody] MenuItemImage image)
        {
            if (image == null)
                throw new Exception("Bad request");

            await this._editorValidator.ValidateUserAccessToMenuItem(menuItemId: image.MenuItemId);

            await this._editorService.DeleteItemImage(Convert.ToInt64(image.MenuItemImageId));

            return new OkResult();
        }

        #endregion

        #region Stock

        [Obsolete]
        [Route("UpsertStock")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpsertStock([FromBody] UpsertStockRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");
            await this._editorValidator.ValidateAccessToCanteen(request.CanteenId);

            Stock res = await this._stocksService.UpsertStock(request);

            return new OkObjectResult(res);
        }

        [Route("Stock")]
        [HttpPost]
        [ProducesResponseType(typeof(Stock), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> CreateStock([FromBody] UpsertStockRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");
            await this._editorValidator.ValidateAccessToCanteen(request.CanteenId);

            StockEntity entity = await this._stocksService.CreateStock(request);

            // TODO Move to service layer once upsert endpoint is deprecated
            var res = StockAssembler.Convert_StockEntity_To_Stock(entity);

            return new OkObjectResult(res);
        }

        [Route("Stock")]
        [HttpPut]
        [ProducesResponseType(typeof(Stock), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateStock([FromBody] UpsertStockRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");
            await this._editorValidator.ValidateAccessToCanteen(request.CanteenId);

            StockEntity entity = await this._stocksService.UpdateStock(request);

            // TODO Move to service layer once upsert endpoint is deprecated
            var res = StockAssembler.Convert_StockEntity_To_Stock(entity);

            return new OkObjectResult(res);
        }

        [Route("GetStocksByCanteenId/{canteenId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetStocksByCanteenId(int canteenId)
        {
            await this._editorValidator.ValidateAccessToCanteen(canteenId);
            List<Stock> res = await this._stocksService.GetStocksByCanteenId(canteenId);

            return new OkObjectResult(res);
        }

        [Route("GetStocksById/{stockId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetStocksById(int stockId)
        {
            // To Do: validate user access to stock?
            Stock res = await this._stocksService.GetStockById(stockId);

            return new OkObjectResult(res);
        }

        [Route("GetMenuItemsByStockId/{stockId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetMenuItemsByStockId(int stockId)
        {
            List<MenuItem> res = await this._stocksService.GetItemsByStockId(stockId);
            await this._editorValidator.ValidateAccessToMenuItems(res);
            return new OkObjectResult(res);
        }

        [Route("RemoveStockFromItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> RemoveStockFromItem([FromBody] AddRemoveStockRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateUserAccessToMenuItem(request.ItemId);

            await this._dbHelper.ExecSprocByParams("sp_Editor_MenuItems_Remove_Stock",
                                new Dictionary<string, string>() { { "itemId", $"{request.ItemId}" } });

            // Generate Items' Menus
            MenuItem item = new()
            {
                MenuItemId = request.ItemId,
                CanteenId = request.CanteenId
            };

            await this._editorService.GenerateMenusByMenuItem(item);

            return new OkResult();
        }

        [Route("AddStockToItem")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> AddStockToItem([FromBody] AddRemoveStockRequest request)
        {
            if (request == null) throw new ValidationException("Invalid request");

            await this._editorValidator.ValidateUserAccessToMenuItem(request.ItemId);

            await this._stocksService.LinkStockToItem(request.ItemId, (int)request.StockId);

            // Generate Items' Menus
            MenuItem item = new()
            {
                MenuItemId = request.ItemId,
                CanteenId = request.CanteenId
            };

            await this._editorService.GenerateMenusByMenuItem(item);

            return new OkResult();
        }

        [Route("Item/{menuItemId}/MarkOutOfStock")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> MarkItemAsOutOfStock([FromRoute] long menuItemId)
        {
            if (menuItemId <= 0) throw new ValidationException("Invalid request");

            // access check
            await this._editorValidator.ValidateUserAccessToMenuItem(menuItemId);

            // update stock
            await this._stocksService.MarkItemAsOutOfStock(menuItemId);

            return new OkResult();
        }

        #endregion

        [HttpPut("UpdateSchoolPrintOptions")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateSchoolPrintOptions([FromBody] SchoolPrintOptionsRequest request)
        {
            this._editorValidator.ValidateSchoolPrintOptionsRequest(request);
            await this._editorValidator.ValidateAccessToSchool((long)request.SchoolId);
            var schoolPrintOptions = JsonConvert.SerializeObject(request);
            await this._dbHelper.ExecSproc("sp_School_UpdatePrintOptions", schoolPrintOptions);

            return new OkResult();
        }

        [Route("GetAvailabilities/{menuItemId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetActiveSchoolNotices([FromRoute] int menuItemId)
        {
            await this._editorValidator.ValidateUserAccessToMenuItem(menuItemId);

            var response = await this._dbHelper.ExecSprocById<List<MenuItemAvailability>>("sp_MenuItemAvailabilities_GetByMenuItemId", menuItemId);

            return new OkObjectResult(response);
        }

        #region Item Availability Dates Endpoints

        [Route("UpsertItemAvailabilityDates")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [Obsolete]
        public Task<IActionResult> UpsertItemAvailabilityDates([FromBody] MenuItemAvailabilityRequest menuItemAvailabilityRequest)
        {
            throw new DeprecatedApiException();
        }

        [Route("DeleteItemAvailabilityDates")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> DeleteItemAvailabilityDates(long availabilityId, long menuItemId)
        {
            await this._editorValidator.ValidateUserAccessToMenuItem(menuItemId);
            if (availabilityId != 0)
            {
                await this._dbHelper.ExecSprocById("sp_MenuItemAvailability_Delete", availabilityId);
            }

            List<MenuItemAvailability> response = await this._editorService.GetItemAvailableDates(menuItemId);

            return new OkObjectResult(response);
        }

        [Route("GetItemAvailabilityDates/{itemId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [Obsolete]
        public Task<IActionResult> GetItemAvailabilityDates([FromRoute] long itemId)
        {
            throw new DeprecatedApiException();
        }

        #endregion

        [Route("GetItemsByCategoryId/{categoryId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> GetItemsByCategoryId(int categoryId)
        {
            List<MenuItem> listItems = new List<MenuItem>();

            var json = await this._dbHelper.ExecSprocById("sp_Editor_MenuItems_GetBy_Category_ID", categoryId);
            if (!String.IsNullOrEmpty(json))
            {
                listItems = JsonConvert.DeserializeObject<List<MenuItem>>(json);
            }

            await this._editorValidator.ValidateAccessToMenuItems(listItems);

            return new OkObjectResult(listItems);
        }
    }
}
