using Schools.BLL.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Cashless.APIs.Filters
{
    /// <summary>
    /// MVC action filter to add common security headers
    /// to every web response once all processing is done
    /// </summary>
    public class AddHttpHeadersActionFilter : IActionFilter
    {
        private readonly IWebHostEnvironment env;
        private readonly ILogger<AddHttpHeadersActionFilter> logger;

        public AddHttpHeadersActionFilter(IWebHostEnvironment env, ILogger<AddHttpHeadersActionFilter> logger)
        {
            this.env = env;
            this.logger = logger;
        }

        public void OnActionExecuting(ActionExecutingContext context) { }

        /// <summary>
        /// Add additional headers to each response
        /// </summary>
        public void OnActionExecuted(ActionExecutedContext context)
        {
            if (!env.IsDevelopment())
            {
                return;
            }

            // Only do this for localhost:5000!
            var host = context.HttpContext.Request.Host;
            if (host.ToString() != "localhost:5000")
            {
                return;
            }

            // Get HTTP signatures as well as MVC route data
            var method = context.HttpContext.Request.Method;
            var path = context.HttpContext.Request.Path;
            var queryString = context.HttpContext.Request.QueryString;
            var ip = context.HttpContext.GetRequestIp();
            var controller = context.RouteData.Values["controller"];
            var action = context.RouteData.Values["action"];

            // Stop the 403 CORS errors on localhost:5000
            context.HttpContext.Response.Headers["Access-Control-Allow-Origin"] = "http://localhost:4200";
            context.HttpContext.Response.Headers["Access-Control-Allow-Methods"] = "*";

            logger.LogDebug("HTTP {Method} {Path} added 'Access-Control-Allow-Origin' & 'Access-Control-Allow-Methods' headers - Host: {Host} - IP: {IP} - Controller: {Controller} - Action: {Action}",
                            method, path + queryString, host, ip, controller, action);
        }
    }
}