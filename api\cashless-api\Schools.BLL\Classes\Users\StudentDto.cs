﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Users;

public class StudentDto
{
    [JsonProperty(PropertyName = "UserId")]
    public int UserId { get; set; }

    [JsonProperty(PropertyName = "FirstName")]
    public string FirstName { get; set; }

    [JsonProperty(PropertyName = "Lastname")]
    public string Lastname { get; set; }

    // class properties
    [JsonProperty(PropertyName = "ClassId")]
    public int ClassId { get; set; }

    [JsonProperty(PropertyName = "NeedToUpdateClass")]
    public bool NeedToUpdateClass { get; set; }

    // schools properties
    [JsonProperty(PropertyName = "SchoolId")]
    public int SchoolId { get; set; }

    [JsonProperty(PropertyName = "SchoolName")]
    public string SchoolName { get; set; }

    [JsonProperty(PropertyName = "SchoolWeeksPreOrder")]
    public int SchoolWeeksPreOrder { get; set; }

    [JsonProperty(PropertyName = "SchoolDeactivatedFilters")]
    public string SchoolDeactivatedFilters { get; set; }

    [JsonProperty(PropertyName = "SchoolOpeningDays")]
    public string SchoolOpeningDays { get; set; }

    [JsonProperty(PropertyName = "SchoolIsMarketingFree")]
    public bool SchoolIsMarketingFree { get; set; }

    [JsonProperty(PropertyName = "SchoolTimeZoneOffSetHours")]
    public decimal SchoolTimeZoneOffSetHours { get; set; }
}

public class EditStudentDto : StudentDto
{
    [JsonProperty(PropertyName = "IsActive")]
    public bool IsActive { get; set; }

    [JsonProperty(PropertyName = "SchoolCutOffTime")]
    public string SchoolCutOffTime { get; set; }

    [JsonProperty(PropertyName = "ParentId")]
    public int? ParentId { get; set; }

    [JsonProperty(PropertyName = "ClassName")]
    public string ClassName { get; set; }

    [JsonProperty(PropertyName = "Email")]
    public string Email { get; set; }

    [JsonProperty(PropertyName = "DateOfBirth")]
    public DateTime? DateOfBirth { get; set; }

    [JsonProperty(PropertyName = "Role")]
    public string Role { get; set; }

    [JsonProperty(PropertyName = "Mobile")]
    public string Mobile { get; set; }

    [JsonProperty(PropertyName = "AllowCanteenToOrder")]
    public bool? AllowCanteenToOrder { get; set; }

    [JsonProperty(PropertyName = "FavouriteColour")]
    public string FavouriteColour { get; set; }

    [JsonProperty(PropertyName = "Allergies")]
    public string Allergies { get; set; }
}
