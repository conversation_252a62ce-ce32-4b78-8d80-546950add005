using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Schools.BLL.Services;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Attributes;

/// <summary>
/// Action filter attribute to check if the current user has
/// one of the roles specified.
/// 
/// To use, you need to decorate the target API method:
/// [HttpPost]
/// [Route("SomeApiMethod")]
/// [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
/// public IActionResult SomeApiMethod(...
/// </summary>
public class CheckUserRoleAttribute : ActionFilterAttribute
{
    /// <summary>
    /// Roles to check against
    /// </summary>
    protected UserRole[] roles { get; set; }

    public CheckUserRoleAttribute(params UserRole[] roles)
    {
        this.roles = roles;
    }

    /// <summary>
    /// Check a user's role before executing the next action in the HTTP context chain
    /// </summary>
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        await CheckUserRoles(context, next);
    }

    /// <summary>
    /// Continue with the other attributes and filters in the middleware chain for the given action 
    /// </summary>
    protected async Task Continue(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        await base.OnActionExecutionAsync(context, next);
    }

    /// <summary>
    /// Fetch the current user using their Firebase token and check that they have
    /// one of the required roles
    /// </summary>
    protected async Task CheckUserRoles(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        if (this.roles == null || this.roles.Length == 0)
        {
            // Results in a 500 response
            throw new AttributeConfigurationException($"Role check on API: {context.HttpContext.Request.Path} is not configured correctly");
        }

        var securityService = context.HttpContext.RequestServices.GetRequiredService<ISecurityService>();

        await securityService.ValidateUserRole(context.HttpContext, this.roles);

        // Check complete, keep going
        await Continue(context, next);
    }
}
