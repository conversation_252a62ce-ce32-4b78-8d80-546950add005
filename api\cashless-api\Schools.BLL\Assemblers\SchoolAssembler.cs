﻿using System;
using Schools.BLL.Classes;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public class SchoolAssembler
{
    public SchoolAssembler()
    {
    }

    /// <summary>
    /// Convert from SchoolFormDto to SchoolEntity
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public SchoolEntity ConvertSchoolFormDtoToSchoolEntity(SchoolDto request)
    {
        SchoolEntity entity = new()
        {
            SchoolId = Convert.ToInt32(request.SchoolId),
            IsActive = request.IsActive,
            Name = request.Name,
            StateId = request.StateId,
            IsMarketingFree = request.IsMarketingFree,
            DeactivatedFilters = request.DeactivatedFilters,
            AddressId = 0,
            TotalStudentEst = request.TotalStudentEst

        };

        return entity;
    }

    /// <summary>
    /// Merge DTO updates into the entity
    /// </summary>
    /// <param name="request"></param>
    /// <param name="entity"></param>
    /// <returns></returns>
    public SchoolEntity MergeSchoolFormDtoIntoSchoolEntity(SchoolDto request, SchoolEntity entity)
    {
        entity.IsActive = request.IsActive;
        entity.Name = request.Name;
        entity.StateId = request.StateId;
        entity.IsMarketingFree = request.IsMarketingFree;
        entity.DeactivatedFilters = request.DeactivatedFilters;
        entity.TotalStudentEst = request.TotalStudentEst;

        return entity;
    }

    /// <summary>
    /// Convert from DTO to database entity
    /// </summary>
    public SchoolMerchantEntity ConvertToSchoolMerchantEntity(UpsertSchoolBillingDto request)
    {
        return new()
        {
            SchoolId = request.SchoolId,
            CanteenId = request.CanteenId,
            CanteenType = request.CanteenType,
            BillingStatus = request.BillingStatus,
            CanteenFee = request.CanteenFee,
            StartDate = request.StartDate,
            BillingStartDate = request.BillingStartDate,
            BillingEndDate = request.BillingEndDate,
            SpecialInstructions = request.SpecialInstructions,
            WaiveEventOrderFee = request.WaiveEventOrderFee,
        };
    }
}
