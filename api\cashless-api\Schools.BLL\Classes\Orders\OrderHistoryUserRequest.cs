using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class OrderHistoryUserRequest
    {
        // replace with DateTime
        [JsonProperty(PropertyName = "StartOrderDate")]
        [Required]
        public string StartOrderDate;

        // replace with DateTime
        [JsonProperty(PropertyName = "EndOrderDate")]
        [Required]
        public string EndOrderDate;

        [JsonProperty(PropertyName = "UserId")]
        public int UserId;

        [JsonProperty(PropertyName = "SchoolId")]
        public int? SchoolId;
    }

    public class OrderHistoryStudentRequest
    {
        [Required]
        public string StartOrderDate;

        [Required]
        public string EndOrderDate;

        [Required]
        public int StudentId;
    }

    public class OrderHistoryRequestMobile
    {
        // replace with DateTime
        [JsonProperty(PropertyName = "StartDate")]
        [Required]
        public string StartDate;

        // replace with DateTime
        [JsonProperty(PropertyName = "EndDate")]
        [Required]
        public string EndDate;
    }

    public class EventUniformHistoryRequest
    {
        [JsonProperty(PropertyName = "StartIndex")]
        public Int16 StartIndex;

        [JsonProperty(PropertyName = "NumberOfOrders")]
        public Int16 NumberOfOrders;

        [JsonProperty(PropertyName = "MenuType")]
        public string MenuType;
    }
}
