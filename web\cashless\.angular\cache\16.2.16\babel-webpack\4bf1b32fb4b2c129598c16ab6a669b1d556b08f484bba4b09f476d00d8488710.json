{"ast": null, "code": "// Validation patterns\nexport const CARD_NUMBER_PATTERN = /^[0-9\\s]{13,19}$/;\nexport const CVV_PATTERN = /^[0-9]{3,4}$/;\nexport const EXPIRY_MONTH_PATTERN = /^(0[1-9]|1[0-2])$/;\nexport const EXPIRY_YEAR_PATTERN = /^20[2-9][0-9]$/;\n// Card type detection\nexport const CARD_TYPES = {\n  VISA: 'Visa',\n  MASTERCARD: 'Mastercard',\n  AMEX: 'American Express',\n  UNKNOWN: 'Unknown'\n};\nexport function detectCardType(cardNumber) {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  if (/^4/.test(cleanNumber)) {\n    return CARD_TYPES.VISA;\n  }\n  if (/^5[1-5]/.test(cleanNumber)) {\n    return CARD_TYPES.MASTERCARD;\n  }\n  if (/^3[47]/.test(cleanNumber)) {\n    return CARD_TYPES.AMEX;\n  }\n  return CARD_TYPES.UNKNOWN;\n}\nexport function formatCardNumber(cardNumber) {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  const groups = cleanNumber.match(/.{1,4}/g);\n  return groups ? groups.join(' ') : cleanNumber;\n}\nexport function maskCardNumber(cardNumber) {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  if (cleanNumber.length < 4) {\n    return cardNumber;\n  }\n  const lastFour = cleanNumber.slice(-4);\n  const masked = '*'.repeat(cleanNumber.length - 4);\n  return formatCardNumber(masked + lastFour);\n}\nexport function validateCardNumber(cardNumber) {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  // Check if all digits\n  if (!/^\\d+$/.test(cleanNumber)) {\n    return false;\n  }\n  // Luhn algorithm\n  let sum = 0;\n  let alternate = false;\n  for (let i = cleanNumber.length - 1; i >= 0; i--) {\n    let digit = parseInt(cleanNumber[i]);\n    if (alternate) {\n      digit *= 2;\n      if (digit > 9) {\n        digit = digit % 10 + 1;\n      }\n    }\n    sum += digit;\n    alternate = !alternate;\n  }\n  return sum % 10 === 0;\n}\nexport function validateExpiryDate(month, year) {\n  const now = new Date();\n  const expiry = new Date(year, month - 1);\n  return expiry >= now;\n}\nexport function validateCVV(cvv) {\n  return CVV_PATTERN.test(cvv);\n}", "map": {"version": 3, "names": ["CARD_NUMBER_PATTERN", "CVV_PATTERN", "EXPIRY_MONTH_PATTERN", "EXPIRY_YEAR_PATTERN", "CARD_TYPES", "VISA", "MASTERCARD", "AMEX", "UNKNOWN", "detectCardType", "cardNumber", "cleanNumber", "replace", "test", "formatCardNumber", "groups", "match", "join", "maskCardNumber", "length", "lastFour", "slice", "masked", "repeat", "validateCardNumber", "sum", "alternate", "i", "digit", "parseInt", "validateExpiryDate", "month", "year", "now", "Date", "expiry", "validateCVV", "cvv"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\sharedModels\\guest-payment\\guest-payment.models.ts"], "sourcesContent": ["export interface GuestPaymentRequest {\n  cardNumber: string;\n  expiryMonth: number;\n  expiryYear: number;\n  cvv: string;\n  cardholderName: string;\n  amount: number;\n  canteenId: number;\n  guestUserId: number;\n  items: GuestOrderItem[];\n  orderDate: string;\n  menuId: number;\n  menuType: string;\n}\n\nexport interface GuestOrderItem {\n  menuItemId: number;\n  quantity: number;\n  price: number;\n  itemName: string;\n  itemDescription: string;\n}\n\nexport interface GuestPaymentResponse {\n  isSuccess: boolean;\n  orderId: string;\n  message: string;\n  errorCode: string;\n  transactionId: string;\n  processedAt: string;\n  amountCharged: number;\n  paymentReference: string;\n}\n\nexport interface GuestCardValidationRequest {\n  cardNumber: string;\n  expiryMonth: number;\n  expiryYear: number;\n  cvv: string;\n}\n\nexport interface GuestCardValidationResponse {\n  isValid: boolean;\n  errorMessage: string;\n  cardType: string;\n  maskedCardNumber: string;\n}\n\nexport interface GuestPaymentForm {\n  cardNumber: string;\n  expiryMonth: string;\n  expiryYear: string;\n  cvv: string;\n  cardholderName: string;\n}\n\nexport interface GuestPaymentDialogData {\n  orders: any[];\n  selectedStudent: any;\n  totalAmount: number;\n  canteenId: number;\n  menuId: number;\n  menuType: string;\n  orderDate: string;\n  viewType: string;\n  guid: string;\n}\n\nexport interface GuestPaymentDialogResult {\n  success: boolean;\n  orderId?: string;\n  message?: string;\n  errorCode?: string;\n}\n\n// Validation patterns\nexport const CARD_NUMBER_PATTERN = /^[0-9\\s]{13,19}$/;\nexport const CVV_PATTERN = /^[0-9]{3,4}$/;\nexport const EXPIRY_MONTH_PATTERN = /^(0[1-9]|1[0-2])$/;\nexport const EXPIRY_YEAR_PATTERN = /^20[2-9][0-9]$/;\n\n// Card type detection\nexport const CARD_TYPES = {\n  VISA: 'Visa',\n  MASTERCARD: 'Mastercard',\n  AMEX: 'American Express',\n  UNKNOWN: 'Unknown'\n};\n\nexport function detectCardType(cardNumber: string): string {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  \n  if (/^4/.test(cleanNumber)) {\n    return CARD_TYPES.VISA;\n  }\n  if (/^5[1-5]/.test(cleanNumber)) {\n    return CARD_TYPES.MASTERCARD;\n  }\n  if (/^3[47]/.test(cleanNumber)) {\n    return CARD_TYPES.AMEX;\n  }\n  \n  return CARD_TYPES.UNKNOWN;\n}\n\nexport function formatCardNumber(cardNumber: string): string {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  const groups = cleanNumber.match(/.{1,4}/g);\n  return groups ? groups.join(' ') : cleanNumber;\n}\n\nexport function maskCardNumber(cardNumber: string): string {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  if (cleanNumber.length < 4) {\n    return cardNumber;\n  }\n  \n  const lastFour = cleanNumber.slice(-4);\n  const masked = '*'.repeat(cleanNumber.length - 4);\n  return formatCardNumber(masked + lastFour);\n}\n\nexport function validateCardNumber(cardNumber: string): boolean {\n  const cleanNumber = cardNumber.replace(/\\s/g, '');\n  \n  // Check if all digits\n  if (!/^\\d+$/.test(cleanNumber)) {\n    return false;\n  }\n  \n  // Luhn algorithm\n  let sum = 0;\n  let alternate = false;\n  \n  for (let i = cleanNumber.length - 1; i >= 0; i--) {\n    let digit = parseInt(cleanNumber[i]);\n    \n    if (alternate) {\n      digit *= 2;\n      if (digit > 9) {\n        digit = (digit % 10) + 1;\n      }\n    }\n    \n    sum += digit;\n    alternate = !alternate;\n  }\n  \n  return sum % 10 === 0;\n}\n\nexport function validateExpiryDate(month: number, year: number): boolean {\n  const now = new Date();\n  const expiry = new Date(year, month - 1);\n  return expiry >= now;\n}\n\nexport function validateCVV(cvv: string): boolean {\n  return CVV_PATTERN.test(cvv);\n}\n"], "mappings": "AA2EA;AACA,OAAO,MAAMA,mBAAmB,GAAG,kBAAkB;AACrD,OAAO,MAAMC,WAAW,GAAG,cAAc;AACzC,OAAO,MAAMC,oBAAoB,GAAG,mBAAmB;AACvD,OAAO,MAAMC,mBAAmB,GAAG,gBAAgB;AAEnD;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,YAAY;EACxBC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAE;CACV;AAED,OAAM,SAAUC,cAAcA,CAACC,UAAkB;EAC/C,MAAMC,WAAW,GAAGD,UAAU,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAEjD,IAAI,IAAI,CAACC,IAAI,CAACF,WAAW,CAAC,EAAE;IAC1B,OAAOP,UAAU,CAACC,IAAI;;EAExB,IAAI,SAAS,CAACQ,IAAI,CAACF,WAAW,CAAC,EAAE;IAC/B,OAAOP,UAAU,CAACE,UAAU;;EAE9B,IAAI,QAAQ,CAACO,IAAI,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOP,UAAU,CAACG,IAAI;;EAGxB,OAAOH,UAAU,CAACI,OAAO;AAC3B;AAEA,OAAM,SAAUM,gBAAgBA,CAACJ,UAAkB;EACjD,MAAMC,WAAW,GAAGD,UAAU,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACjD,MAAMG,MAAM,GAAGJ,WAAW,CAACK,KAAK,CAAC,SAAS,CAAC;EAC3C,OAAOD,MAAM,GAAGA,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGN,WAAW;AAChD;AAEA,OAAM,SAAUO,cAAcA,CAACR,UAAkB;EAC/C,MAAMC,WAAW,GAAGD,UAAU,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACjD,IAAID,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAOT,UAAU;;EAGnB,MAAMU,QAAQ,GAAGT,WAAW,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC;EACtC,MAAMC,MAAM,GAAG,GAAG,CAACC,MAAM,CAACZ,WAAW,CAACQ,MAAM,GAAG,CAAC,CAAC;EACjD,OAAOL,gBAAgB,CAACQ,MAAM,GAAGF,QAAQ,CAAC;AAC5C;AAEA,OAAM,SAAUI,kBAAkBA,CAACd,UAAkB;EACnD,MAAMC,WAAW,GAAGD,UAAU,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAEjD;EACA,IAAI,CAAC,OAAO,CAACC,IAAI,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAO,KAAK;;EAGd;EACA,IAAIc,GAAG,GAAG,CAAC;EACX,IAAIC,SAAS,GAAG,KAAK;EAErB,KAAK,IAAIC,CAAC,GAAGhB,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,IAAIC,KAAK,GAAGC,QAAQ,CAAClB,WAAW,CAACgB,CAAC,CAAC,CAAC;IAEpC,IAAID,SAAS,EAAE;MACbE,KAAK,IAAI,CAAC;MACV,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAIA,KAAK,GAAG,EAAE,GAAI,CAAC;;;IAI5BH,GAAG,IAAIG,KAAK;IACZF,SAAS,GAAG,CAACA,SAAS;;EAGxB,OAAOD,GAAG,GAAG,EAAE,KAAK,CAAC;AACvB;AAEA,OAAM,SAAUK,kBAAkBA,CAACC,KAAa,EAAEC,IAAY;EAC5D,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;EACtB,MAAMC,MAAM,GAAG,IAAID,IAAI,CAACF,IAAI,EAAED,KAAK,GAAG,CAAC,CAAC;EACxC,OAAOI,MAAM,IAAIF,GAAG;AACtB;AAEA,OAAM,SAAUG,WAAWA,CAACC,GAAW;EACrC,OAAOpC,WAAW,CAACY,IAAI,CAACwB,GAAG,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}