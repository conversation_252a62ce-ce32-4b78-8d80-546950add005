using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes.Fees;
using Schools.BLL.Classes.Fees.Requests;
using Schools.BLL.Classes.Fees.Responses;
using Schools.DAL.Enums;
using Schools.DAL.Extensions.Entities;

namespace Schools.BLL.Assemblers;

public static class FeeMapper
{
    public static FeeUsedResponse MapFeeEntityToFeeUsed(this FeeEntity feeEntity)
    {
        return new FeeUsedResponse
        {
            ChildId = feeEntity.ChildId,
            FeeCalculatorId = feeEntity.FeeCalculatorId,
            MenuId = feeEntity.MenuId,
            OrderId = feeEntity.OrderId,
            FeeAmount = feeEntity.Amount,
            OrderType = feeEntity.OrderType,
            OrderDate = feeEntity.OrderDate,
            ParentId = feeEntity.ParentId
        };
    }

    public static FeeEntity MapFeeUsedToFeeEntity(this AddFeeUsedRequest request)
    {
        return new FeeEntity()
        {
            Amount = request.FeeAmount,
            ChildId = request.ChildId,
            FeeCalculatorId = request.FeeCalculatorId,
            MenuId = request.MenuId,
            OrderId = request.OrderId,
            OrderType = request.OrderType,
            ParentId = request.ParentId,
            OrderTotal = request.OrderTotal,
            OrderDate = request.OrderDate,
            Status = FeeStatus.Active
        };
    }

    public static IEnumerable<FeeEntity> MapFeesUsedToFeeEntities(this IEnumerable<AddFeeUsedRequest> request)
    {
        var response = new List<FeeEntity>();
        if (request == null || !request.Any())
        {
            return response;
        }

        foreach (var r in request)
        {
            if (r == null)
            {
                continue;
            }

            response.Add(r.MapFeeUsedToFeeEntity());
        }

        return response;
    }
}
