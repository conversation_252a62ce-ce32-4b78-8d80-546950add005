﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Threading.Tasks;
using Schools.DAL.Entities;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;
using Schools.DAL.QueryResults;

namespace Schools.DAL.Repositories;

public class UserRepository : BaseRepository<UserRepository>, IUserRepository
{
    private readonly IDBHelper _dbHelper;

    public UserRepository(IConfiguration config, IDBHelper dbHelper, ILogger<UserRepository> logger) : base(config, logger)
    {
        _dbHelper = dbHelper;
    }

    /// <summary>
    /// Get user by Id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// To Do:
    /// - replace all using with dapper method and remove
    public async Task<User> GetUserById(int id)
    {
        var sql = @"SELECT 
                        u.UserId,
                        u.IsActive,
                        u.IsGuest,
                        u.SchoolId,
                        u.ClassId,
                        u.ParentId,
                        u.FirstName,
                        u.Lastname,
                        u.Email,
                        u.DateOfBirth,
                        u.[Role],
                        u.Mobile,
                        u.SpriggyUserId,
                        u.SpriggyBalance AS 'SpriggyBalance', 
                        u.FirebaseUserId,
                        u.ExternalUserId,
                        u.StripeCustomerId,
                        Address.AddressId AS 'Address.AddressId',
                        Address.LongAddress AS 'Address.LongAddress',
                        Address.StreetNumber AS 'Address.StreetNumber',
                        Address.StreetName AS 'Address.StreetName',
                        Address.StreetType AS 'Address.StreetType',
                        Address.City AS 'Address.City',
                        Address.Postcode AS 'Address.Postcode',
                        Address.State AS 'Address.State'
                    FROM tblUsers u
                    LEFT JOIN dbo.tblLUAddresses Address ON Address.AddressId = u.AddressId
                    WHERE u.UserId=@id";

        User user;

        using (var connection = GetConnString())
        {
            user = await connection.QuerySingleAsync<User>(sql, new { id });
        }

        return user;
    }

    /// <summary>
    /// Get User by the external user Id
    /// </summary>
    /// <param name="externalUserId"></param>
    /// <returns></returns>
    public async Task<User> GetUserByExternalUserId(string externalUserId)
    {
        var sql = @"SELECT 
                        u.UserId,
                        u.IsActive,
                        u.SchoolId,
                        u.ClassId,
                        u.ParentId,
                        u.FirstName,
                        u.Lastname,
                        u.Email,
                        u.DateOfBirth,
                        u.[Role],
                        u.Mobile,
                        u.SpriggyUserId,
                        u.SpriggyAccountId,
                        u.SpriggyLoginId,
                        u.SpriggyPassword,
                        u.SpriggyBalance, 
                        u.FirebaseUserId,
                        u.ExternalUserId,
                        u.StripeCustomerId
                    FROM 
                        tblUsers u (NOLOCK)
                    WHERE 
                        u.ExternalUserId = @externalUserId ";

        User user;

        using (var connection = GetConnString())
        {
            user = await connection.QuerySingleAsync<User>(sql, new { externalUserId });
        }

        return user;
    }

    public async Task<User> GetUserByStripeCustomerId(string stripeCustomerId)
    {
        var sql = @"SELECT TOP 1
                    u.UserId,
                    u.IsActive,
                    u.SchoolId,
                    u.ClassId,
                    u.ParentId,
                    u.FirstName,
                    u.Lastname,
                    u.Email,
                    u.DateOfBirth,
                    u.[Role],
                    u.Mobile,
                    u.SpriggyUserId,
                    u.SpriggyAccountId,
                    u.SpriggyLoginId,
                    u.SpriggyPassword,
                    u.SpriggyBalance, 
                    u.FirebaseUserId,
                    u.ExternalUserId,
                    u.StripeCustomerId
                FROM 
                    tblUsers u (NOLOCK)
                WHERE 
                    u.StripeCustomerId = @stripeCustomerId";

        User user;

        using (var connection = GetConnString())
        {
            user = await connection.QuerySingleAsync<User>(sql, new { stripeCustomerId });
        }

        return user;
    }

    /// <summary>
    /// Archive User
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="reason"></param>
    /// <returns></returns>
    public async Task ArchiveUser(int userId, string reason)
    {
        await _dbHelper.ExecSprocByParams("sp_User_Archive",
                            new Dictionary<string, string>() {
                                { "userId", $"{userId}" },
                                { "reason", $"{reason}" }
                            });
    }

    /// <summary>
    /// Update user role
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="role"></param>
    /// <returns></returns>
    public async Task UpdateUserRole(int userId, string strRole)
    {
        await _dbHelper.ExecSprocByParams("sp_User_Update_Role",
                            new Dictionary<string, string>() {
                                { "userId", $"{userId}" },
                                { "role", $"{strRole}" }
                            });
    }

    public async Task<bool> MoveStripeCustomerIdForMerchant(int userId)
    {
        var sql = @"UPDATE
                        tblUsers
                    SET
                        StripeCustomerId_BeforeMigration = StripeCustomerId,
                        StripeCustomerId = NULL,
                        DateModified = GETDATE()
                    WHERE
                        UserId = @userId AND Role = 1 AND StripeCustomerId IS NOT NULL AND StripeCustomerId_BeforeMigration IS NULL";

        using (var connection = this.GetConnString())
        {
            var rowsUpdated = await connection.ExecuteAsync(sql, new
            {
                userId
            });

            if (rowsUpdated == 1)
            {
                return true;
            }

            return false;
        }
    }

    public async Task<bool> MoveStripeCustomerIdForParent(int userId)
    {
        var sql = @"UPDATE
                        tblUsers
                    SET
                        StripeCustomerId_BeforeMigration = StripeCustomerId,
                        StripeCustomerId = NULL,
                        DateModified = GETDATE()
                    WHERE
                        UserId = @userId AND Role = 2 AND StripeCustomerId IS NOT NULL AND StripeCustomerId_BeforeMigration IS NULL";

        using (var connection = this.GetConnString())
        {
            var rowsUpdated = await connection.ExecuteAsync(sql, new
            {
                userId
            });

            if (rowsUpdated == 1)
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Update user details
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="firstName"></param>
    /// <param name="lastName"></param>
    /// <param name="mobile"></param>
    /// <returns></returns>
    public async Task UpdateUserDetails(int userId, string firstName, string lastName, string mobile)
    {
        await _dbHelper.ExecSprocByParams("sp_User_Update_Details",
                            new Dictionary<string, string>() {
                                { "userId", $"{userId}" },
                                { "firstName", $"{firstName}" },
                                { "lastName", $"{lastName}" },
                                { "mobile", $"{mobile}" }
                            });
    }

    /// <summary>
    ///  Get active students by aprentId
    /// </summary>
    /// <param name="parentId"></param>
    public async Task<List<StudentByParentResult>> GetStudentsByParentId(int parentId)
    {
        using var connection = GetConnString();

        var sql = @"SELECT u.UserId,
                        u.IsActive,
                        u.SchoolId,
                        s.Name  as 'SchoolName',
                        CONVERT(VARCHAR,s.CutOffTime, 8) 'SchoolCutOffTime',
                        s.DeactivatedFilters as 'SchoolDeactivatedFilters',
                        s.WeeksPreOrder as 'SchoolWeeksPreOrder',
                        s.OpeningDays as 'SchoolOpeningDays',
                        u.ClassId,
                        c.Name as 'ClassName',
                        u.ParentId,
                        u.FirstName,
                        u.Lastname,
                        u.Email,
                        u.DateOfBirth,
                        u.Role,
                        u.Mobile,
                        u.AllowCanteenToOrder,
                        u.FavouriteColour,
                        u.Allergies,
                        u.NeedToUpdateClass,
                        s.IsMarketingFree as 'SchoolIsMarketingFree',
                        s.TimeZone
		            FROM dbo.tblUsers u 
                    INNER JOIN dbo.tblLUClasses c on c.ClassId = u.ClassId
                    INNER JOIN tblLUSchools s on s.SchoolId = u.SchoolId
	                WHERE u.ParentId = @id
                    AND u.IsActive = 1
                    ORDER BY u.FirstName";

        var list = await connection.QueryAsync<StudentByParentResult>(sql,
                                new
                                {
                                    id = parentId
                                });

        return list.ToList();
    }

    /// <summary>
    /// Get active students (with minimal information) by parendId
    /// </summary>
    /// <param name="parentId"></param>
    /// <returns></returns>
    public async Task<List<StudentByParentResultV2>> GetStudentsByParentIdV2(int parentId)
    {
        using var connection = GetConnString();

        var sql = @"SELECT u.UserId,
                           u.SchoolId,
                           u.ClassId,
                           u.FirstName,
                           u.Lastname,
                           u.NeedToUpdateClass,
                           s.Name  as 'SchoolName',
                           s.DeactivatedFilters as 'SchoolDeactivatedFilters',
                           s.WeeksPreOrder as 'SchoolWeeksPreOrder',
                           s.OpeningDays as 'SchoolOpeningDays',
                           s.IsMarketingFree as 'SchoolIsMarketingFree',
                           s.TimeZone
		                 FROM dbo.tblUsers u 
                         INNER JOIN tblLUSchools s on s.SchoolId = u.SchoolId
	                WHERE u.ParentId=@id
                    AND u.IsActive = 1
                    ORDER BY u.FirstName";

        var list = await connection.QueryAsync<StudentByParentResultV2>(sql,
                                new
                                {
                                    id = parentId
                                });

        return list.ToList();
    }

    /// <summary>
    /// Search tbUsers by FirebaseUserId
    /// </summary>
    public async Task<User> GetUserByFirebaseUserId(string firebaseUserId)
    {
        using (var connection = GetConnString())
        {
            var sql = @"SELECT
                            u.*,
                            a.AddressId,
                            a.LongAddress,
                            a.StreetNumber,
                            a.StreetName,
                            a.StreetType,
                            a.City,
                            a.Postcode,
                            a.State
                        FROM 
                            tblUsers u
                        LEFT JOIN 
                            tblLUAddresses a ON a.AddressId = u.AddressId
                        WHERE
                            FirebaseUserId = @firebaseUserId
                        ORDER BY
                            UserId";

            var list = await connection.QueryAsync<User, AddressDto, User>(sql,
                                    (user, address) =>
                                    {
                                        user.Address = address;

                                        return user;
                                    },
                                    new
                                    {
                                        firebaseUserId = firebaseUserId
                                    },
                                    splitOn: "AddressId");

            // Always return the oldest User record
            return list.FirstOrDefault();
        }
    }

    /// <summary>
    /// Search tbUsers for User by Email!
    /// </summary>
    public async Task<User> GetUserByEmail(string email)
    {
        using (var connection = GetConnString())
        {
            var sql = @"SELECT
                            u.*,
                            a.AddressId,
                            a.LongAddress,
                            a.StreetNumber,
                            a.StreetName,
                            a.StreetType,
                            a.City,
                            a.Postcode,
                            a.State
                        FROM 
                            tblUsers u
                        LEFT JOIN 
                            tblLUAddresses a ON a.AddressId = u.AddressId
                        WHERE
                            Email = @email
                        ORDER BY
                            UserId";

            var list = await connection.QueryAsync<User, AddressDto, User>(sql,
                                    (user, address) =>
                                    {
                                        user.Address = address;

                                        return user;
                                    },
                                    new
                                    {
                                        email = email
                                    },
                                    splitOn: "AddressId");

            // Always return the oldest User record
            return list.FirstOrDefault();
        }
    }

    /// <summary>
    /// Get the allergies for the given student
    /// </summary>
    /// <param name="studentId"></param>
    /// <param name="schoolId"></param>
    /// <returns></returns>
    public async Task<UserAllergies> GetAllergiesForUser(int studentId, int schoolId)
    {
        return await _dbHelper.ExecSprocByParams<UserAllergies>("sp_User_GetAllergies_By_StudentId",
                                    new Dictionary<string, string>() {
                                        { "studentId", $"{studentId}" },
                                        { "schoolId", $"{schoolId}" }
                                    });
    }

    /// <summary>
    /// Gets the user details from a give firebaseid
    /// </summary>
    /// <param name="firebaseid"></param>
    /// <returns></returns>
    public async Task<User> GetUserFromFireBaseId(string fireBaseId)
    {
        return await _dbHelper.ExecSprocByParams<User>("sp_getUserByFireBaseId",
                                    new Dictionary<string, string>() { { "firebaseUserId", $"{fireBaseId}" } });
    }

    /// <summary>
    /// Return the canteen users link to the given canteen
    /// </summary>
    /// <param name="canteenId"></param>
    /// <returns></returns>
    public async Task<IEnumerable<CanteenUser>> GetCanteenUsers(long canteenId)
    {
        string sql = @"
            SELECT 
                u.UserId,
                u.IsActive,
                uc.CanteenId,
                u.FirstName,
                u.Lastname,
                u.Email,
                u.DateOfBirth,
                u.[Role],
                uc.IsAdmin,
                u.Mobile,
                uc.IsMenuEditorAvailable,
                uc.IsSaleReportsAvailable,
                uc.IsOrdersNotPrintedReportsAvailable,
                uc.IsEventManagementAvailable,
                uc.IsEditEventManagementAvailable,
                uc.NotifyOrdersNotPrinted
            FROM 
                dbo.tblUsers u
            INNER JOIN 
                tblUsersCanteens uc ON u.UserId = uc.UserId
            WHERE 
                uc.CanteenId=@id
                ";

        using IDbConnection connection = this.GetConnString();
        return await connection.QueryAsync<CanteenUser>(sql,
                            new
                            {
                                id = canteenId
                            });
    }

    /// <summary>
    /// Get the list of canteen users or parent without children
    /// </summary>
    /// <param name="search"></param>
    /// <returns></returns>
    public async Task<List<User>> GetCanteenUsersOrParentWithoutChild(string search)
    {
        return await _dbHelper.ExecSprocByParams<List<User>>("sp_Users_CanteenRole_Or_Parent_Without_Child",
                                    new Dictionary<string, string>() { { "search ", $"{search}" } });
    }

    /// <summary>
    /// search the users that can be added to the given canteen
    /// </summary>
    /// <param name="canteenId"></param>
    /// <param name="search"></param>
    /// <returns></returns>
    public async Task<List<User>> SearchUsersAvailableForCanteen(long canteenId, string search)
    {
        return await _dbHelper.ExecSprocByParams<List<User>>("sp_Users_Available_For_Canteen",
                                    new Dictionary<string, string>() {
                                        { "canteenId", $"{canteenId}" },
                                        { "search ", $"{search}" }
                                    });
    }

    public async Task<List<User>> GetUsersBySchoolId(string schooldId)
    {
        return await _dbHelper.ExecSprocById<List<User>>("sp_Users_GetBy_SchoolId", Convert.ToInt64(schooldId));
    }

    public async Task<User> GetUserDetailsByUserId(int userId)
    {
        return await _dbHelper.ExecSprocByParams<User>("sp_User_GetDetails_By_UserId",
                            new Dictionary<string, string>() { { "userId", $"{userId}" } });
    }

    public async Task UpdateUserClass(User user)
    {
        var userToUpsert = JsonConvert.SerializeObject(user);
        await _dbHelper.ExecSproc("sp_User_Update_Class", userToUpsert);
    }

    public async Task<string> RegisterUser(User user)
    {
        var userToUpsert = JsonConvert.SerializeObject(user);

        return await _dbHelper.ExecSproc("sp_User_Register", userToUpsert);
    }

    public async Task<string> UpsertStudent(Student student)
    {
        var studentToUpsert = JsonConvert.SerializeObject(student);

        return await _dbHelper.ExecSproc("sp_User_Student_Upsert", studentToUpsert);
    }

    public async Task UpsertProfile(UserUpdateProfile profile)
    {
        var request = JsonConvert.SerializeObject(profile);
        await _dbHelper.ExecSproc("sp_User_PROFILE_UPSERT", request);
    }

    public async Task<List<User>> GetAllStudentWithFilters(ArrayFilter filter)
    {
        if (string.IsNullOrEmpty(filter.MultipleFilterId))
        {
            throw new Exception("MultipleFilterId is requried");
        }

        var input = new
        {
            filter = filter.Filter,
            userRole = UserRole.Student,
            multipleFilterId = filter.MultipleFilterId.Split(',').ToArray(),
            startRow = filter.NumberRows * filter.PageIndex,
            numberRows = filter.NumberRows
        };

        var sql = new StringBuilder(@"
                    SELECT COUNT(*) OVER() as 'TotalRows',
                        u.UserId,
                        u.IsActive,
                        u.isGuest,
                        u.SchoolId,
                        Schools.Name as 'SchoolName',
                        u.ClassId,
                        Classes.Name as 'ClassName',
                        u.ParentId,
                        u.FirstName,
                        u.Lastname,
                        u.DateOfBirth,
                        u.Role,
                        u.Mobile,
                        u.SpriggyUserId,
                        u.SpriggyBalance AS 'SpriggyBalance',
                        u.FirebaseUserId
                    FROM tblUsers u
                    LEFT JOIN dbo.tblLUSchools Schools 
                        ON Schools.SchoolId = u.SchoolId
                    LEFT JOIN dbo.tblLUClasses Classes 
                        ON Classes.ClassId = u.ClassId
                    WHERE 
                        u.Role = @userRole
                        AND u.IsActive = 1
                        AND u.SchoolId IN @multipleFilterId");
        if (!string.IsNullOrEmpty(filter.Filter))
        {
            sql.Append(@" 
                        AND (
                            u.FirstName + ' ' + u.Lastname like '%' + @filter + '%'
                            OR Schools.Name like '%' + @filter + '%' 
                            OR Classes.Name like '%' + @filter + '%')");
            sql.Append(@" 
                    ORDER BY UserId
                    OFFSET @startRow ROWS FETCH NEXT @numberRows ROWS ONLY");
        }

        using (var connection = this.GetReplicaConnString())
        {
            var response = await connection.QueryAsync<User>(sql.ToString(), input);
            return response?.ToList();
        }
    }

    public async Task<List<User>> GetAllUsersWithFilter(ArrayFilter filter)
    {
        var input = new
        {
            filter = filter.Filter,
            startRow = filter.NumberRows * filter.PageIndex,
            numberRows = filter.NumberRows
        };

        var sql = new StringBuilder(@"
                SELECT COUNT(*) OVER() as 'TotalRows',
                    u.UserId,
                    u.IsActive,
                    u.SchoolId,
                    Schools.Name as 'SchoolName',
                    u.ClassId,
                    Classes.Name as 'ClassName',
                    u.ParentId,
                    u.FirstName,
                    u.Lastname,
                    u.Email,
                    u.DateOfBirth,
                    u.Role,
                    u.Mobile,
                    u.SpriggyUserId,
                    u.SpriggyBalance AS 'SpriggyBalance',
                    u.FirebaseUserId
                FROM tblUsers u
                LEFT JOIN dbo.tblLUSchools Schools 
                    ON Schools.SchoolId = u.SchoolId
                LEFT JOIN dbo.tblLUClasses Classes 
                    ON Classes.ClassId = u.ClassId");

        if (!string.IsNullOrEmpty(filter.Filter))
        {
            sql.Append(@" 
                    WHERE
                        CAST(u.UserId as varchar(20)) like '%' + @filter + '%'
                        OR u.FirstName + ' ' + u.Lastname like '%' + @filter + '%'
                        OR u.Email like '%' + @filter + '%'
                        OR u.StripeCustomerId like '%' + @filter + '%'
                        ");
        }

        sql.Append(@"  
                ORDER BY UserId
                OFFSET @startRow ROWS FETCH NEXT @numberRows ROWS ONLY");

        using (var connection = this.GetReplicaConnString())
        {
            var response = await connection.QueryAsync<User>(sql.ToString(), input);
            return response?.ToList();
        }
    }

    /// <summary>
    /// Fetch all active Users with UserId > given ID
    ///
    /// NOTE - Skip Users without a FirebaseUserId or Email
    /// </summary>
    public async Task<IEnumerable<User>> GetUsersFromId(long minUserId, int numRecords)
    {
        using (var connection = GetConnString())
        {
            var sql = @"SELECT " + (numRecords > 0 ? " TOP(@numRecords) " : string.Empty) + @"
                            u.* 
                        FROM
                            tblUsers u
                        WHERE
                            UserId > @minUserId AND
                            FirebaseUserId IS NOT NULL AND
                            Email IS NOT NULL AND
                            IsActive = 1
                        ORDER BY
                            UserId";

            return await connection.QueryAsync<User>(sql,
                                    new
                                    {
                                        numRecords = numRecords,
                                        minUserId = minUserId,
                                    });
        }
    }

    /// <summary>
    /// Fetch all active Users with UserId > given ID
    /// </summary>
    public async Task<IEnumerable<User>> GetParentUsersFromId(long minUserId, int numRecords)
    {
        using (var connection = GetConnString())
        {
            var sql = @"SELECT " + (numRecords > 0 ? " TOP(@numRecords) " : string.Empty) + @"
                            u.* 
                        FROM
                            tblUsers u
                        WHERE
                            UserId > @minUserId AND
                            Role = @parentRole
                        ORDER BY
                            UserId";

            return await connection.QueryAsync<User>(sql,
                                    new
                                    {
                                        numRecords = numRecords,
                                        minUserId = minUserId,
                                        parentRole = UserRole.Parent,
                                    });
        }
    }

    /// <summary>
    /// Fetch all Users with a UserId > given ID that are missing a value for SpriggyAccountId
    ///
    /// NOTE - Skip Users without an ExtneralUserId
    /// </summary>
    public async Task<IEnumerable<User>> GetParentUsersWithoutSpriggyAccountIdFromId(long minUserId, int numRecords)
    {
        using (var connection = GetConnString())
        {
            var sql = @"SELECT " + (numRecords > 0 ? " TOP(@numRecords) " : string.Empty) + @"
                            u.* 
                        FROM
                            tblUsers u
                        WHERE
                            UserId > @minUserId AND
                            Role = @role AND
                            SpriggyAccountId IS NULL AND
                            ExternalUserId IS NOT NULL
                        ORDER BY
                            UserId";

            return await connection.QueryAsync<User>(sql,
                                    new
                                    {
                                        numRecords = numRecords,
                                        minUserId = minUserId,
                                        role = UserRole.Parent,
                                    });
        }
    }

    /// <summary>
    /// Update the Spriggy Account ID for a given User if their Spriggy Account ID was not set
    /// </summary>
    public async Task<bool> UpdateSpriggyAccountId(User user, string spriggyAccountId)
    {
        using (var connection = GetConnString())
        {
            var sql = @"UPDATE 
                            tblUsers 
                        SET
                            SpriggyAccountId = @spriggyAccountId
                        WHERE
                            UserId = @userId AND
                            ExternalUserId = @externalUserId AND
                            SpriggyAccountId IS NULL";

            var rowsUpdated = await connection.ExecuteAsync(sql, new
            {
                userId = user.UserId,
                externalUserId = user.ExternalUserId,
                spriggyAccountId = spriggyAccountId
            });

            if (rowsUpdated == 1)
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// Fetch a Parent associated with a School that is serviced by the given Merchant
    /// </summary>
    public async Task<User> GetParentForMerchant(User merchantUser, UserRequest parentRequest)
    {
        // Set the primary search field
        var searchParamName = string.Empty;
        var searchParamValue = string.Empty;
        if (parentRequest.UserId.HasValue)
        {
            searchParamName = "parent.UserId";
            searchParamValue = parentRequest.UserId.ToString();
        }
        else if (string.IsNullOrEmpty(parentRequest.FirebaseUserId))
        {
            searchParamName = "parent.FirebaseUserId";
            searchParamValue = parentRequest.FirebaseUserId;
        }
        else if (string.IsNullOrEmpty(parentRequest.ExternalUserId))
        {
            searchParamName = "parent.ExternalUserId";
            searchParamValue = parentRequest.ExternalUserId;
        }
        else if (string.IsNullOrEmpty(parentRequest.Email))
        {
            searchParamName = "parent.Email";
            searchParamValue = parentRequest.Email;
        }

        // This will fetch a parent record for each current
        // Student associated with a current School serviced
        // by the given Merchant
        var sql = @"SELECT TOP 1
                        parent.*
                    FROM 
                        tblUsers parent,
                        tblUsers student,
                        tblSchoolsCanteens sc,
                        tblSchoolsUsers su,
                        tblUsersCanteens uc,
                        tblLUSchools s,
                        tblLUCanteens c
                    WHERE
                        " + searchParamName + @" = @searchParamValue AND
                        parent.Role = @parentRole AND
                        student.ParentId = parent.UserId AND
                        student.Role = @studentRole AND
                        s.SchoolId = student.SchoolId AND
                        sc.SchoolId = student.SchoolId AND
                        su.UserId = @merchantUserId AND
                        uc.UserId = @merchantUserId AND
                        uc.CanteenId = c.CanteenId AND
                        c.CanteenId = sc.CanteenId AND
                        sc.BillingStatus <> @billingStatusChurned AND
                        su.SchoolId = s.SchoolId AND
                        c.IsActive = 1 AND
                        s.IsActive = 1 AND
                        student.IsActive = 1
                    ORDER BY
                        student.UserId";

        using (var connection = GetConnString())
        {
            return await connection.QuerySingleOrDefaultAsync<User>(sql,
                                    new
                                    {
                                        searchParamValue = searchParamValue,
                                        parentRole = UserRole.Parent,
                                        studentRole = UserRole.Student,
                                        merchantUserId = merchantUser.UserId,
                                        billingStatusChurned = BillingStatusEnum.Churned,
                                    });
        }
    }

    public async Task<IEnumerable<User>> GetChildrenForParent(long parentId)
    {
        var sql = @"SELECT users.*
                    FROM dbo.tblUsers users
                    WHERE users.ParentId = @parentId and users.IsActive=1";

        using (var connection = GetConnString())
        {
            return await connection.QueryAsync<User>(sql,
                                    new
                                    {
                                        parentId = parentId,
                                    });
        }
    }

    /// <summary>
    /// Set 'NeedToUpdateClass' field for students from a specific class to true
    /// </summary>
    public async Task SetStudentsNeedToUpdateClass(int classId)
    {
        var sql = @" UPDATE 
                        tblUsers
                    SET
                        NeedToUpdateClass = 1
                    WHERE 
                        IsActive = 1 AND
                        ClassId = @classId
                    ";

        using (var connection = GetConnString())
        {
            await connection.QueryAsync<int>(sql,
                                    new
                                    {
                                        classId = classId
                                    });
        }
    }

    public async Task<IEnumerable<User>> GetUsersByIds(IEnumerable<long> ids)
    {
        var sql = @"
            SELECT users.*
            FROM dbo.tblUsers users
            WHERE users.UserId IN @ids AND users.IsActive=1
        ";

        using (var connection = GetConnString())
        {
            return await connection.QueryAsync<User>(sql,
                                    new
                                    {
                                        ids = ids.ToArray(),
                                    });
        }
    }

    /// <summary>
    /// Delete a given User record by their FirebaseUserId
    /// </summary>
    public async Task<bool> DeleteUser(string firebaseUserId)
    {
        var parameters = new
        {
            firebaseUserId = firebaseUserId,
        };

        var sql = @"DELETE FROM dbo.tblUsers WHERE FirebaseUserId = @firebaseUserId";

        using (var connection = GetConnString())
        {
            var rowsUpdated = await connection.ExecuteAsync(sql, parameters);

            if (rowsUpdated == 1)
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    // Fetch user with matching database type on id and return Entity Model instead of DTO
    /// </summary>
    public async Task<UserEntity> GetUserById(long userId)
    {
        if (userId < 1)
        {
            throw new Exception("User id not provided");
        }

        using (var connection = GetConnString())
        {
            return await connection.GetAsync<UserEntity>(userId);
        }
    }

    public async Task<UserEntity> CreateUser(UserEntity user)
    {
        if (user == null)
        {
            throw new Exception("User not provided");
        }

        var currentDateTime = DateTime.UtcNow;
        user.DateCreated = currentDateTime;
        user.DateModified = currentDateTime;

        var externalIdSql = @"SELECT * FROM tblUsers WHERE ExternalUserId = @externalUserId";

        using (var connection = GetConnString())
        {
            UserEntity existingUser = null;
            int attempts = 0;
            do
            {
                // Add unique constraints to DB and generate on insert
                user.ExternalUserId = Guid.NewGuid().ToString();

                // Ensure external user id is unique
                existingUser = await connection.QueryFirstOrDefaultAsync<UserEntity>(externalIdSql, new { externalUserId = user.ExternalUserId });

                // Fail after too many attempts
                attempts++;
                if (attempts > 9)
                {
                    throw new Exception("Unable to create user");
                }
            }
            while (existingUser != null);

            user.UserId = await connection.InsertAsync<UserEntity>(user);
            if (user.UserId < 1)
            {
                throw new Exception("Unable to create user");
            }

            return user;
        }
    }

    public async Task<bool> UpdateUser(UserEntity user)
    {
        if (user == null || user.UserId < 1)
        {
            throw new Exception("User not provided");
        }

        user.DateModified = DateTime.UtcNow;
        using (var connection = GetConnString())
        {
            return await connection.UpdateAsync<UserEntity>(user);
        }
    }

    public async Task<bool> DeleteUser(long userId)
    {
        using (var connection = GetConnString())
        {
            var user = await connection.GetAsync<UserEntity>(userId);

            if (user != null)
            {
                return await connection.DeleteAsync<UserEntity>(user);
            }
        }

        return true;
    }

    public async Task UpdateBalance(int userId, decimal balance)
    {
        var sql = @"UPDATE tblUsers
                    SET SpriggyBalance = @balance
                    where UserId = @userId";

        using (var connection = GetConnString())
        {
            var results = await connection.ExecuteAsync(sql, new { userId, balance });
        }
    }

    public async Task UpdateStripeCustomerId(int userId, string stripeCustomerId)
    {
        var sql = @"UPDATE tblUsers
                    SET StripeCustomerId = @stripeCustomerId
                    where UserId = @userId";

        using (var connection = GetConnString())
        {
            var results = await connection.ExecuteAsync(sql, new { userId, stripeCustomerId });
        }
    }

    public async Task<decimal> GetUserBalance(long userId)
    {
        var sql = @"SELECT SpriggyBalance FROM tblUsers
                    WHERE UserId = @userId";

        decimal balance;

        using (var connection = GetConnString())
        {
            balance = await connection.QuerySingleAsync<decimal>(sql, new { userId });
        }

        return balance;
    }

    public async Task<decimal> AddToUserBalance(long userId, decimal amount)
    {
        var sql = @"UPDATE tblUsers
                    SET SpriggyBalance = SpriggyBalance + @amount
                    OUTPUT INSERTED.SpriggyBalance
                    WHERE UserId = @userId";

        decimal updatedBalance;

        using (var connection = GetConnString())
        {
            updatedBalance = await connection.QuerySingleAsync<decimal>(sql, new { userId, amount });
        }

        return updatedBalance;
    }

    public async Task<decimal> DeductFromUserBalance(long userId, decimal amount)
    {
        return await AddToUserBalance(userId, -1 * amount);
    }

    public async Task<IEnumerable<User>> FindTopNUsersNOTInStripe(int n)
    {
        var sql = @"SELECT TOP(@n) u.* 
                        FROM
                            tblUsers u
                        WHERE
                            Role = 2 AND StripeCustomerId IS NULL AND StripeCustomerId_BeforeMigration IS NULL
                        ORDER BY
                            IsActive DESC";

        using (var connection = GetConnString())
        {
            return await connection.QueryAsync<User>(sql, new { n });
        }
    }

    public async Task<bool> PrepareUserForStripeMigration(int userId, string stripeCustomerId)
    {
        var sql = @"UPDATE
                        tblUsers
                    SET
                        StripeCustomerId_BeforeMigration = @stripeCustomerId,
                        DateModified = GETDATE()
                    WHERE
                        UserId = @userId";

        using (var connection = this.GetConnString())
        {
            var rowsUpdated = await connection.ExecuteAsync(sql, new
            {
                stripeCustomerId,
                userId
            });

            if (rowsUpdated == 1)
            {
                return true;
            }

            return false;
        }
    }

    public async Task<IEnumerable<long>> GetParentIdsBySchoolAndClass(long schooldId, string classIds)
    {
        var sql = @"SELECT u.ParentId
                FROM 
                    tblUsers u (NOLOCK)
                INNER JOIN 
                    tblLUClasses c ON c.ClassId = u.ClassId
                WHERE 
                    u.SchoolId = @schooldId
                AND 
                    u.IsActive = 1
                AND
                    c.IsActive = 1  
                    ";

        if (!String.IsNullOrEmpty(classIds))
        {
            sql += @" 
                AND 
                    u.ClassId in (@classIds)";
        }

        using var connection = GetConnString();

        return await connection.QueryAsync<long>(sql, new
        {
            schooldId,
            classIds
        });
    }
}
