"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_Push_request-push-permission_js"],{

/***/ 70698:
/*!*************************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Push/request-push-permission.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   requestPushPermission: () => (/* binding */ requestPushPermission)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _push_manager_factory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./push-manager-factory.js */ 80297);


function requestPushPermission(r, n) {
  if (_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return _push_manager_factory_js__WEBPACK_IMPORTED_MODULE_1__["default"].m().subscribe((n, o, t) => {
    const s = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].cr();
    s && s.requestImmediateDataFlush(), "function" == typeof r && r(n, o, t);
  }, n);
}

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_Push_request-push-permission_js.js.map