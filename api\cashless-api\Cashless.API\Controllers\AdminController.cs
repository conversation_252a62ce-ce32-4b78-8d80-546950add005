using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes.Notification;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.DAL.Interfaces;
using Schools.DAL.Enums;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Cashless.API.Controllers;

/// <summary>
/// Controller to house the various admin functions for Admin processes within the system.
/// 
/// The steps in the current Logic App based ordering process are as follows:
/// Step 1 - Create Order by calling sp_Order_Upsert to insert tblOrders records
/// Step 2 - Call /api/Pay/MakePayment to call Spriggy Core and make payments
/// Step 3 - Call /api/Billing/UpdateBillingState to tramsfer the Order fee to Fee Acount if **payment succeeded**
/// Step 4 - Call sp_Order_Set_Completed with OrderStatusId and PaymentId to update Order status and create tblUserPayments records
/// Step 5 - Call /api/Order/SaveItemsOrder to save Order JSON to tblOrderItems
/// Step 6 - Call /api/Notification/PlaceOrderNotification to send Order confirmation emails
/// Step 7 - Call /api/Stock/OrderUpdateStocks/ to do stock management
/// </summary>
[Authorize]
[ApiController]
[Route("api/[controller]")]
[CheckUserRole(UserRole.Admin)]
public class AdminController : ControllerBase
{
    private readonly IOrderService _orderService;
    private readonly IOrderRepository _orderRepository;
    private readonly IStocksService _stockService;
    private readonly INotificationService _notificationService;
    private readonly IBillingService _billingService;
    private readonly ILogger<AdminController> _logger;

    /// <summary>
    /// Destination states for stuck Orders
    /// </summary>
    private static OrderStatusEnum[] DestinationStatuses = { OrderStatusEnum.Error, OrderStatusEnum.Confirmed };

    /// <summary>
    /// Values of tblOrders.ErrorMessage
    /// </summary>
    private const string MESSAGE_ERROR = "Failed to process order";
    private const string MESSAGE_CONFIRM = "Order processed successfully";

    public AdminController(IOrderService orderService, IOrderRepository orderRepository, IStocksService stocksService,
                    INotificationService notificationService, IBillingService billingService, ILogger<AdminController> logger)
    {
        _orderService = orderService;
        _orderRepository = orderRepository;
        _stockService = stocksService;
        _notificationService = notificationService;
        _billingService = billingService;
        _logger = logger;
    }

    /// <summary>
    /// Push a stuck Order to Error and carry out any steps required
    /// </summary>
    [Route("MoveDraftOrderToError/{orderId}")]
    [HttpPost]
    public async Task<IActionResult> MoveDraftOrderToError(long orderId)
    {
        long[] orderIds = { orderId };

        await ProcessOrders(orderIds, OrderStatusEnum.Error);

        return new OkObjectResult(string.Empty);
    }

    /// <summary>
    /// Push stuck Orders to Error and carry out any steps required
    /// </summary>
    [Route("MoveDraftOrderToError")]
    [HttpPost]
    public async Task<IActionResult> MoveDraftOrderToError([FromBody] List<long> orderIds)
    {
        await ProcessOrders(orderIds, OrderStatusEnum.Error);

        return new OkObjectResult(string.Empty);
    }

    /// <summary>
    /// Push a stuck Order to Confirmed and carry out any steps that were not carried out
    /// </summary>
    [Route("MoveDraftOrderToConfirmed/{orderId}")]
    [HttpPost]
    public async Task<IActionResult> MoveDraftOrderToConfirmed(long orderId)
    {
        long[] orderIds = { orderId };

        await ProcessOrders(orderIds, OrderStatusEnum.Confirmed);

        return new OkObjectResult(string.Empty);
    }

    /// <summary>
    /// Push stuck Orders to Confirmed and carry out any steps that were not carried out
    /// </summary>
    [Route("MoveDraftOrderToConfirmed")]
    [HttpPost]
    public async Task<IActionResult> MoveDraftOrderToConfirmed([FromBody] List<long> orderIds)
    {
        await ProcessOrders(orderIds, OrderStatusEnum.Confirmed);

        return new OkObjectResult(string.Empty);
    }

    #region Helpers

    /// <summary>
    /// Fetch Orders identified and push them to the Error or Confirmed state
    /// </summary>
    private async Task ProcessOrders(IEnumerable<long> orderIds, OrderStatusEnum status)
    {
        if (orderIds == null || !orderIds.Any())
        {
            _logger.LogError("Invalid list of OrderIds: {OrderIds}", orderIds);

            return;
        }

        if (!DestinationStatuses.Contains(status))
        {
            _logger.LogError("Invalid destination status: {Status}", status);

            return;
        }

        foreach (int orderId in orderIds)
        {
            _logger.LogDebug("Moving Order #{OrderId} to status: {Status}", orderId, status);

            var order = await _orderService.GetOrderById(orderId);

            // Skip if the Order is no longer in Draft
            if (order.GetOrderStatus() != OrderStatusEnum.Draft)
            {
                _logger.LogWarning("Order #{OrderId} is currently in status: {Status}", orderId, order.GetOrderStatus());

                continue;
            }

            if (status == OrderStatusEnum.Confirmed)
            {
                await ConfirmOrder(order);
            }
            else
            {
                await ErrorOrder(order);
            }
        }
    }

    /// <summary>
    /// Confirm the Order and move on
    /// </summary>
    private async Task ConfirmOrder(Order order)
    {
        // Move the items into tblOrderItems for reports
        await _orderService.SaveOrderItems(order);

        // Update the stock
        await _stockService.OrderUpdateStocks(order);

        // Set the status & error message
        var status = OrderStatusEnum.Confirmed;
        await _orderService.UpsertOrderAsync(order, status, null);
        await _orderRepository.UpdateErrorMessage(new List<int>() { order.OrderId.Value }, MESSAGE_CONFIRM);

        // Send Order confirmation email
        var list = GetNotificationRequests(order, status);
        //await _notificationService.SendNotification(list, (int)status);
    }

    /// <summary>
    /// Move the Order to error and let Parents order again
    /// </summary>
    private async Task ErrorOrder(Order order)
    {

        // Set the status & error message
        var status = OrderStatusEnum.Error;
        await _orderService.UpsertOrderAsync(order, status, null);
        await _orderRepository.UpdateErrorMessage(new List<int>() { order.OrderId.Value }, MESSAGE_ERROR);

        // Send failed notification
        var list = GetNotificationRequests(order, status);
        //await _notificationService.SendNotification(list, (int)status);
    }

    /// <summary>
    /// Convert the given Order into a List to call the Notification Service
    /// </summary>
    private List<NotificationRequest> GetNotificationRequests(Order order, OrderStatusEnum status)
    {
        var list = new List<NotificationRequest>();

        list.Add(new NotificationRequest()
        {
            OrderId = order.OrderId.Value,
            Error = null,
            StudentId = order.StudentId.ToString(),
            OrderStatusId = ((int)status).ToString(),
            OrderDate = order.OrderDate,
            MenuType = order.MenuType
        });

        return list;
    }

    #endregion
}
