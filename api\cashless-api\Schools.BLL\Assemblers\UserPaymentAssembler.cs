﻿using System;
using System.Collections.Generic;
using Schools.BLL.Classes;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class UserPaymentAssembler
{
    /// <summary>
    /// Convert a single UserPaymentEntity to a PaymentHistoryDto
    /// </summary>
    public static PaymentHistoryDto Convert_PaymentEntity_To_UserPaymentDto(UserPaymentEntity entity)
    {
        if (entity == null)
        {
            return new();
        }

        return new PaymentHistoryDto()
        {
            PaymentId = Convert.ToInt32(entity.PaymentId),
            Description = entity.Description,
            OrderAmount = entity.Amount,
            TopupAmount = entity.TopupAmount,
            PaymentType = entity.PaymentType.ToString(),
            Declined = entity.Declined,
            DateCreatedUtc = entity.DateCreatedUtc.ToString(),
            UpdatedBalance = Convert.ToDecimal(entity.UpdatedBalance),
            UserId = Convert.ToInt32(entity.UserId)
        };
    }

    /// <summary>
    /// Convert a list of UserPaymentEntity to a list of PaymentHistoryDto
    /// </summary>
    public static List<PaymentHistoryDto> Convert_ListPaymentEntity_To_ListUserPaymentDto(IEnumerable<UserPaymentEntity> request)
    {
        List<PaymentHistoryDto> dto = new();

        if (request != null)
        {
            foreach (var payment in request)
            {
                dto.Add(Convert_PaymentEntity_To_UserPaymentDto(payment));
            }
        }

        return dto;
    }
}