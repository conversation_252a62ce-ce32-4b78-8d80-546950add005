using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    public class OrderRequest : ArrayFilter
    {
        [Required]
        public int MerchantId;
        [Required]
        public string SchoolIds;
        public string CategoryIds;
        public string CanteenStatus;
        public string OrderType;
        public string Date;
        public bool? Printed;
        public bool GetAll;
        public string LabelPrintChoice;
        public string SortBy;
        public string SortDirection;
    }

    public class SetPrintedRequest
    {
        // To Do: replace with IEnumerable<long>
        public string Orders;
        public List<PrintedWithRunNumberRequest> LabelWithRunNumber;
    }

    public class PrintedWithRunNumberRequest
    {
        public int OrderId;
        public int? ItemId;
        public string PrintedRunNumber;
    }

    public class PrintGuid
    {
        public string Guid;
    }
}