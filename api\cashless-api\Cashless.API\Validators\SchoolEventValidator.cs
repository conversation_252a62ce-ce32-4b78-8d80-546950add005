using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Microsoft.Extensions.Logging;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface ISchoolEventValidator
{
    Task ValidateAccessToSchoolEvent(SchoolEvent schoolEvent);
    Task ValidateAccessToStudent(int studentId);
    Task ValidateAccessToSchool(long schoolId);
}

public class SchoolEventValidator : ISchoolEventValidator
{
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<SchoolEventValidator> _logger;

    public SchoolEventValidator(IAuthenticationValidator authenticationValidator, ILogger<SchoolEventValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _logger = logger;
    }

    public async Task ValidateAccessToSchoolEvent(SchoolEvent schoolEvent)
    {
        if (schoolEvent == null || schoolEvent.EventOrder == null || !schoolEvent.EventOrder.Any()) return;

        var studentIds = schoolEvent.EventOrder.Select(e => (long)e.StudentId).ToList();
        await _authenticationValidator.ValidateAccessToStudents(studentIds);
    }

    public async Task ValidateAccessToStudent(int studentId)
    {
        await _authenticationValidator.ValidateAccessToStudent(studentId);
    }

    public async Task ValidateAccessToSchool(long schoolId)
    {
        await _authenticationValidator.ValidateAccessToSchool(schoolId);
    }
}