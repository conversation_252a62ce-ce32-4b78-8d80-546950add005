# Actual Keda object that creates hpa config based off prometheus metrics
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: schools-api
  namespace: default
spec:
  pollingInterval: 30  # Optional. Default: 30 seconds
  cooldownPeriod:  600 # Optional. Default: 300 seconds
  scaleTargetRef:
    name: schools-api
  triggers:
  - type: prometheus
    useCachedMetrics: true # https://keda.sh/docs/2.14/concepts/scaling-deployments/#caching-metrics
    metadata:
      # Required fields:
      serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
      query: sum(rate(istio_requests_total{destination_workload="schools-api"}[2m])) # Note: query must return a vector/scalar single element response
      threshold: '30'
      unsafeSsl: "true"
  # No need to scale of these for the time being
  # - type: prometheus
  #   useCachedMetrics: true # https://keda.sh/docs/2.14/concepts/scaling-deployments/#caching-metrics
  #   metadata:
  #     # Required fields:
  #     serverAddress: http://prometheus.monitoring.svc.cluster.local:9090
  #     query: sum(rate(istio_request_duration_milliseconds_sum{destination_workload="schools-api"}[2m])) / sum(rate(istio_request_duration_milliseconds_count{destination_workload="schools-api"}[2m]))
  #     threshold: '500'
  #     unsafeSsl: "true"
  # - type: cpu
  #   metricType: Utilization # Allowed types are 'Utilization' or 'AverageValue'
  #   metadata:
  #     value: "80"
  #     containerName: "schools-api" # Optional. You can use this to target a specific container in a pod