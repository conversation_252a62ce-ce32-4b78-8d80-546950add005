is_global = true
build_property.FunctionsEnableMetadataSourceGen = true
build_property.FunctionsEnableExecutorSourceGen = true
build_property.FunctionsAutoRegisterGeneratedFunctionsExecutor = true
build_property.FunctionsAutoRegisterGeneratedMetadataProvider = true
build_property.FunctionsGeneratedCodeNamespace = Schools.Orders
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.FunctionsExecutionModel = isolated
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Schools.Orders
build_property.ProjectDir = D:\projects\spriggy\git-spriggy-latest\api\cashless-api\OldProjects\Schools.Orders\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 
