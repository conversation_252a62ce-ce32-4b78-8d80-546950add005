{"version": 3, "file": "node_modules_firebase_compat_performance_dist_esm_index_esm_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAA0F;AAC1C;AACa;AAChC;AAE7B,MAAMQ,IAAI,GAAG,yBAAyB;AACtC,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,KAAK;AAChC,MAAMC,eAAe,GAAI,KAAIF,OAAQ,EAAC;AACtC,MAAMG,qBAAqB,GAAG,QAAQ;AACtC,MAAMC,qBAAqB,GAAG,iDAAiD;AAC/E,MAAMC,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAChD,MAAMC,OAAO,GAAG,eAAe;AAC/B,MAAMC,YAAY,GAAG,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1B,CAAC,2BAA2B,CAAC,4CAA4C,iDAAiD;EAC1H,CAAC,gBAAgB,CAAC,iCAAiC,0CAA0C;EAC7F,CAAC,wBAAwB,CAAC,yCAAyC,kCAAkC;EACrG,CAAC,gBAAgB,CAAC,iCAAiC,4FAA4F;EAC/I,CAAC,aAAa,CAAC,8BAA8B,iDAAiD;EAC9F,CAAC,6BAA6B,CAAC,8CAA8C;AACjF,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIb,wDAAY,CAACU,OAAO,EAAEC,YAAY,EAAEC,qBAAqB,CAAC;AACpF;AACA,SAASE,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAQA,KAAK,YAAYd,yDAAa,IAClCc,KAAK,CAACC,IAAI,CAACC,QAAQ,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAAC;EAAEC;AAAU,CAAC,EAAE;EAC7C,OAAQ,GAAEX,qBAAsB,aAAYW,SAAU,gBAAe;AACzE;AACA,SAASC,gCAAgCA,CAACC,QAAQ,EAAE;EAChD,OAAO;IACHC,KAAK,EAAED,QAAQ,CAACC,KAAK;IACrBC,aAAa,EAAE,CAAC,CAAC;IACjBC,SAAS,EAAEC,iCAAiC,CAACJ,QAAQ,CAACG,SAAS,CAAC;IAChEE,YAAY,EAAEC,IAAI,CAACC,GAAG,CAAC;EAC3B,CAAC;AACL;AAAC,SACcC,oBAAoBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,yJAAA,CAAnC,WAAoCC,WAAW,EAAEf,QAAQ,EAAE;IACvD,MAAMgB,YAAY,SAAShB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IAC1C,MAAMC,SAAS,GAAGF,YAAY,CAACtB,KAAK;IACpC,OAAOF,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,gCAAgC;MACzEJ,WAAW;MACXK,UAAU,EAAEF,SAAS,CAACvB,IAAI;MAC1B0B,aAAa,EAAEH,SAAS,CAACI,OAAO;MAChCC,YAAY,EAAEL,SAAS,CAACM;IAC5B,CAAC,CAAC;EACN,CAAC;EAAA,OAAAb,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASY,UAAUA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAAC;IACf,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,kBAAkB;IAC1B,gBAAgB,EAAEF;EACtB,CAAC,CAAC;AACN;AACA,SAASG,kBAAkBA,CAACC,SAAS,EAAE;EAAEC;AAAa,CAAC,EAAE;EACrD,MAAMC,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;EACrCE,OAAO,CAACC,MAAM,CAAC,eAAe,EAAEC,sBAAsB,CAACH,YAAY,CAAC,CAAC;EACrE,OAAOC,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeG,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwB,oBAAA;EAAAA,mBAAA,GAAAvB,yJAAA,CAAjC,WAAkCwB,EAAE,EAAE;IAClC,MAAMC,MAAM,SAASD,EAAE,CAAC,CAAC;IACzB,IAAIC,MAAM,CAACf,MAAM,IAAI,GAAG,IAAIe,MAAM,CAACf,MAAM,GAAG,GAAG,EAAE;MAC7C;MACA,OAAOc,EAAE,CAAC,CAAC;IACf;IACA,OAAOC,MAAM;EACjB,CAAC;EAAA,OAAAF,mBAAA,CAAAzB,KAAA,OAAAC,SAAA;AAAA;AACD,SAAST,iCAAiCA,CAACoC,iBAAiB,EAAE;EAC1D;EACA,OAAOC,MAAM,CAACD,iBAAiB,CAACE,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACxD;AACA,SAASR,sBAAsBA,CAACH,YAAY,EAAE;EAC1C,OAAQ,GAAE7C,qBAAsB,IAAG6C,YAAa,EAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeY,yBAAyBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,0BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAwCxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAiC,2BAAA;EAAAA,0BAAA,GAAAhC,yJAAA,CAxDA,WAAyC;IAAEgB,SAAS;IAAEiB;EAAyB,CAAC,EAAE;IAAEC;EAAI,CAAC,EAAE;IACvF,MAAMC,QAAQ,GAAGpD,wBAAwB,CAACiC,SAAS,CAAC;IACpD,MAAME,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;IACrC;IACA,MAAMoB,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,CAAC,CAAC;MACrE,IAAID,gBAAgB,EAAE;QAClBrB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEoB,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTP,GAAG;MACHQ,WAAW,EAAEtE,qBAAqB;MAClCuE,KAAK,EAAE3B,SAAS,CAAC2B,KAAK;MACtBC,UAAU,EAAEzE;IAChB,CAAC;IACD,MAAM0E,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACd5B,OAAO;MACPuB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAMvD,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI3D,QAAQ,CAACgE,EAAE,EAAE;MACb,MAAMC,aAAa,SAASjE,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAC3C,MAAMiD,2BAA2B,GAAG;QAChClB,GAAG,EAAEiB,aAAa,CAACjB,GAAG,IAAIA,GAAG;QAC7BmB,kBAAkB,EAAE,CAAC,CAAC;QACtBpC,YAAY,EAAEkC,aAAa,CAAClC,YAAY;QACxCqC,SAAS,EAAErE,gCAAgC,CAACkE,aAAa,CAACG,SAAS;MACvE,CAAC;MACD,OAAOF,2BAA2B;IACtC,CAAC,MACI;MACD,YAAY1D,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAA8C,0BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAmBD,SAASwD,KAAKA,CAACC,EAAE,EAAE;EACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1BC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACC,KAAK,EAAE;EAClC,MAAMC,GAAG,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAGJ,KAAK,CAAC,CAAC;EAC/C,OAAOC,GAAG,CAAClC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,iBAAiB,GAAG,mBAAmB;AAC7C,MAAMC,WAAW,GAAG,EAAE;AACtB;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,IAAI;IACA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IACvC,MAAMC,MAAM,GAAGC,IAAI,CAACD,MAAM,IAAIC,IAAI,CAACC,QAAQ;IAC3CF,MAAM,CAACG,eAAe,CAACL,YAAY,CAAC;IACpC;IACAA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,GAAIA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAW;IAC7D,MAAMnC,GAAG,GAAGyC,MAAM,CAACN,YAAY,CAAC;IAChC,OAAOH,iBAAiB,CAACU,IAAI,CAAC1C,GAAG,CAAC,GAAGA,GAAG,GAAGiC,WAAW;EAC1D,CAAC,CACD,OAAOU,EAAE,EAAE;IACP;IACA,OAAOV,WAAW;EACtB;AACJ;AACA;AACA,SAASQ,MAAMA,CAACN,YAAY,EAAE;EAC1B,MAAMS,SAAS,GAAGlB,qBAAqB,CAACS,YAAY,CAAC;EACrD;EACA;EACA,OAAOS,SAAS,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAAChE,SAAS,EAAE;EACvB,OAAQ,GAAEA,SAAS,CAACiE,OAAQ,IAAGjE,SAAS,CAAC2B,KAAM,EAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACpE,SAAS,EAAEkB,GAAG,EAAE;EAChC,MAAMmD,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7BsE,sBAAsB,CAACD,GAAG,EAAEnD,GAAG,CAAC;EAChCqD,kBAAkB,CAACF,GAAG,EAAEnD,GAAG,CAAC;AAChC;AACA,SAASsD,WAAWA,CAACxE,SAAS,EAAEyE,QAAQ,EAAE;EACtC;EACA;EACAC,mBAAmB,CAAC,CAAC;EACrB,MAAML,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7B,IAAI2E,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACM,WAAW,EAAE;IACdA,WAAW,GAAG,IAAIE,GAAG,CAAC,CAAC;IACvBX,kBAAkB,CAACY,GAAG,CAACT,GAAG,EAAEM,WAAW,CAAC;EAC5C;EACAA,WAAW,CAACI,GAAG,CAACN,QAAQ,CAAC;AAC7B;AACA,SAASO,cAAcA,CAAChF,SAAS,EAAEyE,QAAQ,EAAE;EACzC,MAAMJ,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;EAC7B,MAAM2E,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC/C,IAAI,CAACM,WAAW,EAAE;IACd;EACJ;EACAA,WAAW,CAACM,MAAM,CAACR,QAAQ,CAAC;EAC5B,IAAIE,WAAW,CAACO,IAAI,KAAK,CAAC,EAAE;IACxBhB,kBAAkB,CAACe,MAAM,CAACZ,GAAG,CAAC;EAClC;EACA;EACAc,qBAAqB,CAAC,CAAC;AAC3B;AACA,SAASb,sBAAsBA,CAACD,GAAG,EAAEnD,GAAG,EAAE;EACtC,MAAMkE,SAAS,GAAGlB,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACe,SAAS,EAAE;IACZ;EACJ;EACA,KAAK,MAAMX,QAAQ,IAAIW,SAAS,EAAE;IAC9BX,QAAQ,CAACvD,GAAG,CAAC;EACjB;AACJ;AACA,SAASqD,kBAAkBA,CAACF,GAAG,EAAEnD,GAAG,EAAE;EAClC,MAAMmE,OAAO,GAAGX,mBAAmB,CAAC,CAAC;EACrC,IAAIW,OAAO,EAAE;IACTA,OAAO,CAACC,WAAW,CAAC;MAAEjB,GAAG;MAAEnD;IAAI,CAAC,CAAC;EACrC;EACAiE,qBAAqB,CAAC,CAAC;AAC3B;AACA,IAAII,gBAAgB,GAAG,IAAI;AAC3B;AACA,SAASb,mBAAmBA,CAAA,EAAG;EAC3B,IAAI,CAACa,gBAAgB,IAAI,kBAAkB,IAAI/B,IAAI,EAAE;IACjD+B,gBAAgB,GAAG,IAAIC,gBAAgB,CAAC,uBAAuB,CAAC;IAChED,gBAAgB,CAACE,SAAS,GAAGC,CAAC,IAAI;MAC9BpB,sBAAsB,CAACoB,CAAC,CAACC,IAAI,CAACtB,GAAG,EAAEqB,CAAC,CAACC,IAAI,CAACzE,GAAG,CAAC;IAClD,CAAC;EACL;EACA,OAAOqE,gBAAgB;AAC3B;AACA,SAASJ,qBAAqBA,CAAA,EAAG;EAC7B,IAAIjB,kBAAkB,CAACgB,IAAI,KAAK,CAAC,IAAIK,gBAAgB,EAAE;IACnDA,gBAAgB,CAACK,KAAK,CAAC,CAAC;IACxBL,gBAAgB,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAG,iCAAiC;AACvD,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,iBAAiB,GAAG,8BAA8B;AACxD,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGjJ,2CAAM,CAAC8I,aAAa,EAAEC,gBAAgB,EAAE;MAChDI,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACN,iBAAiB,CAAC;QAC/C;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAOC,SAAS;AACpB;AACA;AAAA,SACelB,GAAGA,CAAAwB,GAAA,EAAAC,GAAA;EAAA,OAAAC,IAAA,CAAA1H,KAAA,OAAAC,SAAA;AAAA;AAalB;AAAA,SAAAyH,KAAA;EAAAA,IAAA,GAAAxH,yJAAA,CAbA,WAAmBgB,SAAS,EAAEyG,KAAK,EAAE;IACjC,MAAMpC,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMa,WAAW,GAAGF,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC;IACrD,MAAMc,QAAQ,SAAUD,WAAW,CAAChC,GAAG,CAACP,GAAG,CAAE;IAC7C,MAAMuC,WAAW,CAACE,GAAG,CAACL,KAAK,EAAEpC,GAAG,CAAC;IACjC,MAAMqC,EAAE,CAACK,IAAI;IACb,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAAC3F,GAAG,KAAKuF,KAAK,CAACvF,GAAG,EAAE;MACzCkD,UAAU,CAACpE,SAAS,EAAEyG,KAAK,CAACvF,GAAG,CAAC;IACpC;IACA,OAAOuF,KAAK;EAChB,CAAC;EAAA,OAAAD,IAAA,CAAA1H,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEciI,MAAMA,CAAAC,GAAA;EAAA,OAAAC,OAAA,CAAApI,KAAA,OAAAC,SAAA;AAAA;AAOrB;AACA;AACA;AACA;AACA;AACA;AALA,SAAAmI,QAAA;EAAAA,OAAA,GAAAlI,yJAAA,CAPA,WAAsBgB,SAAS,EAAE;IAC7B,MAAMqE,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMW,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC,CAACd,MAAM,CAACZ,GAAG,CAAC;IACnD,MAAMqC,EAAE,CAACK,IAAI;EACjB,CAAC;EAAA,OAAAG,OAAA,CAAApI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcoI,MAAMA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,OAAA,CAAAxI,KAAA,OAAAC,SAAA;AAAA;AAoBrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA,SAAAuI,QAAA;EAAAA,OAAA,GAAAtI,yJAAA,CApCA,WAAsBgB,SAAS,EAAEuH,QAAQ,EAAE;IACvC,MAAMlD,GAAG,GAAGL,MAAM,CAAChE,SAAS,CAAC;IAC7B,MAAMmG,EAAE,SAASF,YAAY,CAAC,CAAC;IAC/B,MAAMS,EAAE,GAAGP,EAAE,CAACQ,WAAW,CAACZ,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMyB,KAAK,GAAGd,EAAE,CAACE,WAAW,CAACb,iBAAiB,CAAC;IAC/C,MAAMc,QAAQ,SAAUW,KAAK,CAAC5C,GAAG,CAACP,GAAG,CAAE;IACvC,MAAMoD,QAAQ,GAAGF,QAAQ,CAACV,QAAQ,CAAC;IACnC,IAAIY,QAAQ,KAAKC,SAAS,EAAE;MACxB,MAAMF,KAAK,CAACvC,MAAM,CAACZ,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,MAAMmD,KAAK,CAACV,GAAG,CAACW,QAAQ,EAAEpD,GAAG,CAAC;IAClC;IACA,MAAMqC,EAAE,CAACK,IAAI;IACb,IAAIU,QAAQ,KAAK,CAACZ,QAAQ,IAAIA,QAAQ,CAAC3F,GAAG,KAAKuG,QAAQ,CAACvG,GAAG,CAAC,EAAE;MAC1DkD,UAAU,CAACpE,SAAS,EAAEyH,QAAQ,CAACvG,GAAG,CAAC;IACvC;IACA,OAAOuG,QAAQ;EACnB,CAAC;EAAA,OAAAH,OAAA,CAAAxI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAsBc4I,oBAAoBA,CAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAA/I,KAAA,OAAAC,SAAA;AAAA;AAiBnC;AACA;AACA;AACA;AAHA,SAAA8I,sBAAA;EAAAA,qBAAA,GAAA7I,yJAAA,CAjBA,WAAoC8I,aAAa,EAAE;IAC/C,IAAIC,mBAAmB;IACvB,MAAMC,iBAAiB,SAASb,MAAM,CAACW,aAAa,CAAC9H,SAAS,EAAEiI,QAAQ,IAAI;MACxE,MAAMD,iBAAiB,GAAGE,+BAA+B,CAACD,QAAQ,CAAC;MACnE,MAAME,gBAAgB,GAAGC,8BAA8B,CAACN,aAAa,EAAEE,iBAAiB,CAAC;MACzFD,mBAAmB,GAAGI,gBAAgB,CAACJ,mBAAmB;MAC1D,OAAOI,gBAAgB,CAACH,iBAAiB;IAC7C,CAAC,CAAC;IACF,IAAIA,iBAAiB,CAAC9G,GAAG,KAAKiC,WAAW,EAAE;MACvC;MACA,OAAO;QAAE6E,iBAAiB,QAAQD;MAAoB,CAAC;IAC3D;IACA,OAAO;MACHC,iBAAiB;MACjBD;IACJ,CAAC;EACL,CAAC;EAAA,OAAAF,qBAAA,CAAA/I,KAAA,OAAAC,SAAA;AAAA;AAKD,SAASmJ,+BAA+BA,CAACD,QAAQ,EAAE;EAC/C,MAAMI,KAAK,GAAGJ,QAAQ,IAAI;IACtB/G,GAAG,EAAEkC,WAAW,CAAC,CAAC;IAClBf,kBAAkB,EAAE,CAAC,CAAC;EAC1B,CAAC;;EACD,OAAOiG,oBAAoB,CAACD,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,8BAA8BA,CAACN,aAAa,EAAEE,iBAAiB,EAAE;EACtE,IAAIA,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IAC5E,IAAI,CAACkG,SAAS,CAACC,MAAM,EAAE;MACnB;MACA,MAAMC,4BAA4B,GAAGhG,OAAO,CAACiG,MAAM,CAAChL,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;MACpH,OAAO;QACH2I,iBAAiB;QACjBD,mBAAmB,EAAEU;MACzB,CAAC;IACL;IACA;IACA,MAAME,eAAe,GAAG;MACpBzH,GAAG,EAAE8G,iBAAiB,CAAC9G,GAAG;MAC1BmB,kBAAkB,EAAE,CAAC,CAAC;MACtBuG,gBAAgB,EAAEpK,IAAI,CAACC,GAAG,CAAC;IAC/B,CAAC;IACD,MAAMsJ,mBAAmB,GAAGc,oBAAoB,CAACf,aAAa,EAAEa,eAAe,CAAC;IAChF,OAAO;MAAEX,iBAAiB,EAAEW,eAAe;MAAEZ;IAAoB,CAAC;EACtE,CAAC,MACI,IAAIC,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IACjF,OAAO;MACH2F,iBAAiB;MACjBD,mBAAmB,EAAEe,wBAAwB,CAAChB,aAAa;IAC/D,CAAC;EACL,CAAC,MACI;IACD,OAAO;MAAEE;IAAkB,CAAC;EAChC;AACJ;AACA;AAAA,SACea,oBAAoBA,CAAAE,IAAA,EAAAC,IAAA;EAAA,OAAAC,qBAAA,CAAAnK,KAAA,OAAAC,SAAA;AAAA;AAqBnC;AAAA,SAAAkK,sBAAA;EAAAA,qBAAA,GAAAjK,yJAAA,CArBA,WAAoC8I,aAAa,EAAEE,iBAAiB,EAAE;IAClE,IAAI;MACA,MAAM5F,2BAA2B,SAASvB,yBAAyB,CAACiH,aAAa,EAAEE,iBAAiB,CAAC;MACrG,OAAOlD,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEoC,2BAA2B,CAAC;IACpE,CAAC,CACD,OAAOsD,CAAC,EAAE;MACN,IAAI/H,aAAa,CAAC+H,CAAC,CAAC,IAAIA,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,EAAE;QACrD;QACA;QACA,MAAM0H,MAAM,CAACc,aAAa,CAAC9H,SAAS,CAAC;MACzC,CAAC,MACI;QACD;QACA,MAAM8E,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAE;UAC/BkB,GAAG,EAAE8G,iBAAiB,CAAC9G,GAAG;UAC1BmB,kBAAkB,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC;MACN;;MACA,MAAMqD,CAAC;IACX;EACJ,CAAC;EAAA,OAAAuD,qBAAA,CAAAnK,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEc+J,wBAAwBA,CAAAK,IAAA;EAAA,OAAAC,yBAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AAuBvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAqK,0BAAA;EAAAA,yBAAA,GAAApK,yJAAA,CAvBA,WAAwC8I,aAAa,EAAE;IACnD;IACA;IACA;IACA,IAAIO,KAAK,SAASgB,yBAAyB,CAACvB,aAAa,CAAC9H,SAAS,CAAC;IACpE,OAAOqI,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MACnE;MACA,MAAME,KAAK,CAAC,GAAG,CAAC;MAChB8F,KAAK,SAASgB,yBAAyB,CAACvB,aAAa,CAAC9H,SAAS,CAAC;IACpE;IACA,IAAIqI,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MAChE;MACA,MAAM;QAAE2F,iBAAiB;QAAED;MAAoB,CAAC,SAASJ,oBAAoB,CAACG,aAAa,CAAC;MAC5F,IAAIC,mBAAmB,EAAE;QACrB,OAAOA,mBAAmB;MAC9B,CAAC,MACI;QACD;QACA,OAAOC,iBAAiB;MAC5B;IACJ;IACA,OAAOK,KAAK;EAChB,CAAC;EAAA,OAAAe,yBAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AASD,SAASsK,yBAAyBA,CAACrJ,SAAS,EAAE;EAC1C,OAAOmH,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;IACjC,IAAI,CAACA,QAAQ,EAAE;MACX,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,wBAAwB,CAAC,sCAAsC,CAAC;IAC/F;;IACA,OAAOiJ,oBAAoB,CAACL,QAAQ,CAAC;EACzC,CAAC,CAAC;AACN;AACA,SAASK,oBAAoBA,CAACD,KAAK,EAAE;EACjC,IAAIiB,8BAA8B,CAACjB,KAAK,CAAC,EAAE;IACvC,OAAO;MACHnH,GAAG,EAAEmH,KAAK,CAACnH,GAAG;MACdmB,kBAAkB,EAAE,CAAC,CAAC;IAC1B,CAAC;EACL;;EACA,OAAOgG,KAAK;AAChB;AACA,SAASiB,8BAA8BA,CAACtB,iBAAiB,EAAE;EACvD,OAAQA,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC,mCAC/C2F,iBAAiB,CAACY,gBAAgB,GAAG1L,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBe8K,wBAAwBA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,yBAAA,CAAA5K,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2K,0BAAA;EAAAA,yBAAA,GAAA1K,yJAAA,CAAvC,WAAwC;IAAEgB,SAAS;IAAEiB;EAAyB,CAAC,EAAE+G,iBAAiB,EAAE;IAChG,MAAM7G,QAAQ,GAAGwI,4BAA4B,CAAC3J,SAAS,EAAEgI,iBAAiB,CAAC;IAC3E,MAAM9H,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEgI,iBAAiB,CAAC;IAChE;IACA,MAAM5G,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,CAAC,CAAC;MACrE,IAAID,gBAAgB,EAAE;QAClBrB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEoB,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTmI,YAAY,EAAE;QACVhI,UAAU,EAAEzE,eAAe;QAC3BwE,KAAK,EAAE3B,SAAS,CAAC2B;MACrB;IACJ,CAAC;IACD,MAAME,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACd5B,OAAO;MACPuB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAMvD,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI3D,QAAQ,CAACgE,EAAE,EAAE;MACb,MAAMC,aAAa,SAASjE,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAC3C,MAAM0K,kBAAkB,GAAG5L,gCAAgC,CAACkE,aAAa,CAAC;MAC1E,OAAO0H,kBAAkB;IAC7B,CAAC,MACI;MACD,YAAYnL,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAAwL,yBAAA,CAAA5K,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS4K,4BAA4BA,CAAC3J,SAAS,EAAE;EAAEkB;AAAI,CAAC,EAAE;EACtD,OAAQ,GAAEnD,wBAAwB,CAACiC,SAAS,CAAE,IAAGkB,GAAI,sBAAqB;AAC9E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMe4I,gBAAgBA,CAAAC,IAAA;EAAA,OAAAC,iBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AA+B/B;AACA;AACA;AACA;AACA;AACA;AALA,SAAAiL,kBAAA;EAAAA,iBAAA,GAAAhL,yJAAA,CA/BA,WAAgC8I,aAAa,EAAEmC,YAAY,GAAG,KAAK,EAAE;IACjE,IAAIC,YAAY;IAChB,MAAM7B,KAAK,SAASlB,MAAM,CAACW,aAAa,CAAC9H,SAAS,EAAEiI,QAAQ,IAAI;MAC5D,IAAI,CAACkC,iBAAiB,CAAClC,QAAQ,CAAC,EAAE;QAC9B,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;MAC/E;;MACA,MAAM+K,YAAY,GAAGnC,QAAQ,CAAC3F,SAAS;MACvC,IAAI,CAAC2H,YAAY,IAAII,gBAAgB,CAACD,YAAY,CAAC,EAAE;QACjD;QACA,OAAOnC,QAAQ;MACnB,CAAC,MACI,IAAImC,YAAY,CAAChM,aAAa,KAAK,CAAC,CAAC,iCAAiC;QACvE;QACA8L,YAAY,GAAGI,yBAAyB,CAACxC,aAAa,EAAEmC,YAAY,CAAC;QACrE,OAAOhC,QAAQ;MACnB,CAAC,MACI;QACD;QACA,IAAI,CAACM,SAAS,CAACC,MAAM,EAAE;UACnB,MAAM9K,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC;QACzE;;QACA,MAAMsJ,eAAe,GAAG4B,mCAAmC,CAACtC,QAAQ,CAAC;QACrEiC,YAAY,GAAGM,wBAAwB,CAAC1C,aAAa,EAAEa,eAAe,CAAC;QACvE,OAAOA,eAAe;MAC1B;IACJ,CAAC,CAAC;IACF,MAAMrG,SAAS,GAAG4H,YAAY,SAClBA,YAAY,GAClB7B,KAAK,CAAC/F,SAAS;IACrB,OAAOA,SAAS;EACpB,CAAC;EAAA,OAAA0H,iBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcuL,yBAAyBA,CAAAG,IAAA,EAAAC,IAAA;EAAA,OAAAC,0BAAA,CAAA7L,KAAA,OAAAC,SAAA;AAAA;AAmBxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAA4L,2BAAA;EAAAA,0BAAA,GAAA3L,yJAAA,CAnBA,WAAyC8I,aAAa,EAAEmC,YAAY,EAAE;IAClE;IACA;IACA;IACA,IAAI5B,KAAK,SAASuC,sBAAsB,CAAC9C,aAAa,CAAC9H,SAAS,CAAC;IACjE,OAAOqI,KAAK,CAAC/F,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAAiC;MACxE;MACA,MAAMmE,KAAK,CAAC,GAAG,CAAC;MAChB8F,KAAK,SAASuC,sBAAsB,CAAC9C,aAAa,CAAC9H,SAAS,CAAC;IACjE;IACA,MAAMsC,SAAS,GAAG+F,KAAK,CAAC/F,SAAS;IACjC,IAAIA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAAiC;MAC/D;MACA,OAAO0L,gBAAgB,CAAChC,aAAa,EAAEmC,YAAY,CAAC;IACxD,CAAC,MACI;MACD,OAAO3H,SAAS;IACpB;EACJ,CAAC;EAAA,OAAAqI,0BAAA,CAAA7L,KAAA,OAAAC,SAAA;AAAA;AASD,SAAS6L,sBAAsBA,CAAC5K,SAAS,EAAE;EACvC,OAAOmH,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;IACjC,IAAI,CAACkC,iBAAiB,CAAClC,QAAQ,CAAC,EAAE;MAC9B,MAAMvK,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;IAC/E;;IACA,MAAM+K,YAAY,GAAGnC,QAAQ,CAAC3F,SAAS;IACvC,IAAIuI,2BAA2B,CAACT,YAAY,CAAC,EAAE;MAC3C,OAAOU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC,EAAE;QAAE3F,SAAS,EAAE;UAAElE,aAAa,EAAE,CAAC,CAAC;QAAgC;MAAE,CAAC,CAAC;IAC1H;;IACA,OAAO6J,QAAQ;EACnB,CAAC,CAAC;AACN;AAAC,SACcuC,wBAAwBA,CAAAQ,IAAA,EAAAC,IAAA;EAAA,OAAAC,yBAAA,CAAApM,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmM,0BAAA;EAAAA,yBAAA,GAAAlM,yJAAA,CAAvC,WAAwC8I,aAAa,EAAEE,iBAAiB,EAAE;IACtE,IAAI;MACA,MAAM1F,SAAS,SAASiH,wBAAwB,CAACzB,aAAa,EAAEE,iBAAiB,CAAC;MAClF,MAAMmD,wBAAwB,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/C,iBAAiB,CAAC,EAAE;QAAE1F;MAAU,CAAC,CAAC;MACnG,MAAMwC,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEmL,wBAAwB,CAAC;MAC5D,OAAO7I,SAAS;IACpB,CAAC,CACD,OAAOoD,CAAC,EAAE;MACN,IAAI/H,aAAa,CAAC+H,CAAC,CAAC,KACfA,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,IAAIoG,CAAC,CAACwD,UAAU,CAAC5J,UAAU,KAAK,GAAG,CAAC,EAAE;QACtE;QACA;QACA,MAAM0H,MAAM,CAACc,aAAa,CAAC9H,SAAS,CAAC;MACzC,CAAC,MACI;QACD,MAAMmL,wBAAwB,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/C,iBAAiB,CAAC,EAAE;UAAE1F,SAAS,EAAE;YAAElE,aAAa,EAAE,CAAC,CAAC;UAAgC;QAAE,CAAC,CAAC;QACzJ,MAAM0G,GAAG,CAACgD,aAAa,CAAC9H,SAAS,EAAEmL,wBAAwB,CAAC;MAChE;MACA,MAAMzF,CAAC;IACX;EACJ,CAAC;EAAA,OAAAwF,yBAAA,CAAApM,KAAA,OAAAC,SAAA;AAAA;AACD,SAASoL,iBAAiBA,CAACnC,iBAAiB,EAAE;EAC1C,OAAQA,iBAAiB,KAAKN,SAAS,IACnCM,iBAAiB,CAAC3F,kBAAkB,KAAK,CAAC,CAAC;AACnD;;AACA,SAASgI,gBAAgBA,CAAC/H,SAAS,EAAE;EACjC,OAAQA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,iCAClC,CAACgN,kBAAkB,CAAC9I,SAAS,CAAC;AACtC;AACA,SAAS8I,kBAAkBA,CAAC9I,SAAS,EAAE;EACnC,MAAM7D,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,OAAQA,GAAG,GAAG6D,SAAS,CAAC/D,YAAY,IAChC+D,SAAS,CAAC/D,YAAY,GAAG+D,SAAS,CAACjE,SAAS,GAAGI,GAAG,GAAGnB,uBAAuB;AACpF;AACA;AACA,SAASiN,mCAAmCA,CAACtC,QAAQ,EAAE;EACnD,MAAMoD,mBAAmB,GAAG;IACxBjN,aAAa,EAAE,CAAC,CAAC;IACjBkN,WAAW,EAAE9M,IAAI,CAACC,GAAG,CAAC;EAC1B,CAAC;EACD,OAAOqM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC,EAAE;IAAE3F,SAAS,EAAE+I;EAAoB,CAAC,CAAC;AACzF;AACA,SAASR,2BAA2BA,CAACvI,SAAS,EAAE;EAC5C,OAAQA,SAAS,CAAClE,aAAa,KAAK,CAAC,CAAC,mCAClCkE,SAAS,CAACgJ,WAAW,GAAGpO,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOe8M,KAAKA,CAAAC,IAAA;EAAA,OAAAC,MAAA,CAAA3M,KAAA,OAAAC,SAAA;AAAA;AAcpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAA0M,OAAA;EAAAA,MAAA,GAAAzM,yJAAA,CA9BA,WAAqB8I,aAAa,EAAE;IAChC,MAAM4D,iBAAiB,GAAG5D,aAAa;IACvC,MAAM;MAAEE,iBAAiB;MAAED;IAAoB,CAAC,SAASJ,oBAAoB,CAAC+D,iBAAiB,CAAC;IAChG,IAAI3D,mBAAmB,EAAE;MACrBA,mBAAmB,CAAC4D,KAAK,CAACC,OAAO,CAAChO,KAAK,CAAC;IAC5C,CAAC,MACI;MACD;MACA;MACAkM,gBAAgB,CAAC4B,iBAAiB,CAAC,CAACC,KAAK,CAACC,OAAO,CAAChO,KAAK,CAAC;IAC5D;IACA,OAAOoK,iBAAiB,CAAC9G,GAAG;EAChC,CAAC;EAAA,OAAAuK,MAAA,CAAA3M,KAAA,OAAAC,SAAA;AAAA;AAAA,SA0Bc8M,QAAQA,CAAAC,IAAA;EAAA,OAAAC,SAAA,CAAAjN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgN,UAAA;EAAAA,SAAA,GAAA/M,yJAAA,CAAvB,WAAwB8I,aAAa,EAAEmC,YAAY,GAAG,KAAK,EAAE;IACzD,MAAMyB,iBAAiB,GAAG5D,aAAa;IACvC,MAAMkE,gCAAgC,CAACN,iBAAiB,CAAC;IACzD;IACA;IACA,MAAMpJ,SAAS,SAASwH,gBAAgB,CAAC4B,iBAAiB,EAAEzB,YAAY,CAAC;IACzE,OAAO3H,SAAS,CAACnE,KAAK;EAC1B,CAAC;EAAA,OAAA4N,SAAA,CAAAjN,KAAA,OAAAC,SAAA;AAAA;AAAA,SACciN,gCAAgCA,CAAAC,IAAA;EAAA,OAAAC,iCAAA,CAAApN,KAAA,OAAAC,SAAA;AAAA;AAQ/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAmN,kCAAA;EAAAA,iCAAA,GAAAlN,yJAAA,CARA,WAAgD8I,aAAa,EAAE;IAC3D,MAAM;MAAEC;IAAoB,CAAC,SAASJ,oBAAoB,CAACG,aAAa,CAAC;IACzE,IAAIC,mBAAmB,EAAE;MACrB;MACA,MAAMA,mBAAmB;IAC7B;EACJ,CAAC;EAAA,OAAAmE,iCAAA,CAAApN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAkBcoN,yBAAyBA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,0BAAA,CAAAxN,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAuN,2BAAA;EAAAA,0BAAA,GAAAtN,yJAAA,CAAxC,WAAyCgB,SAAS,EAAEgI,iBAAiB,EAAE;IACnE,MAAM7G,QAAQ,GAAGoL,iBAAiB,CAACvM,SAAS,EAAEgI,iBAAiB,CAAC;IAChE,MAAM9H,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEgI,iBAAiB,CAAC;IAChE,MAAMnG,OAAO,GAAG;MACZC,MAAM,EAAE,QAAQ;MAChB5B;IACJ,CAAC;IACD,MAAMhC,QAAQ,SAASmC,kBAAkB,CAAC,MAAM4B,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI,CAAC3D,QAAQ,CAACgE,EAAE,EAAE;MACd,YAAYxD,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA,OAAAoO,0BAAA,CAAAxN,KAAA,OAAAC,SAAA;AAAA;AACD,SAASwN,iBAAiBA,CAACvM,SAAS,EAAE;EAAEkB;AAAI,CAAC,EAAE;EAC3C,OAAQ,GAAEnD,wBAAwB,CAACiC,SAAS,CAAE,IAAGkB,GAAI,EAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMesL,mBAAmBA,CAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAA5N,KAAA,OAAAC,SAAA;AAAA;AA0BlC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAA2N,qBAAA;EAAAA,oBAAA,GAAA1N,yJAAA,CA1CA,WAAmC8I,aAAa,EAAE;IAC9C,MAAM;MAAE9H;IAAU,CAAC,GAAG8H,aAAa;IACnC,MAAMO,KAAK,SAASlB,MAAM,CAACnH,SAAS,EAAEiI,QAAQ,IAAI;MAC9C,IAAIA,QAAQ,IAAIA,QAAQ,CAAC5F,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAC/E;QACA,OAAOqF,SAAS;MACpB;MACA,OAAOO,QAAQ;IACnB,CAAC,CAAC;IACF,IAAII,KAAK,EAAE;MACP,IAAIA,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAChE;QACA,MAAM3E,aAAa,CAAC2B,MAAM,CAAC,6BAA6B,CAAC,2CAA2C,CAAC;MACzG,CAAC,MACI,IAAIgJ,KAAK,CAAChG,kBAAkB,KAAK,CAAC,CAAC,+BAA+B;QACnE,IAAI,CAACkG,SAAS,CAACC,MAAM,EAAE;UACnB,MAAM9K,aAAa,CAAC2B,MAAM,CAAC,aAAa,CAAC,2BAA2B,CAAC;QACzE,CAAC,MACI;UACD,MAAM8M,yBAAyB,CAACnM,SAAS,EAAEqI,KAAK,CAAC;UACjD,MAAMrB,MAAM,CAAChH,SAAS,CAAC;QAC3B;MACJ;IACJ;EACJ,CAAC;EAAA,OAAA0M,oBAAA,CAAA5N,KAAA,OAAAC,SAAA;AAAA;AA2BD,SAAS4N,UAAUA,CAAC7E,aAAa,EAAErD,QAAQ,EAAE;EACzC,MAAM;IAAEzE;EAAU,CAAC,GAAG8H,aAAa;EACnCtD,WAAW,CAACxE,SAAS,EAAEyE,QAAQ,CAAC;EAChC,OAAO,MAAM;IACTO,cAAc,CAAChF,SAAS,EAAEyE,QAAQ,CAAC;EACvC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmI,gBAAgBA,CAACC,GAAG,GAAGpQ,qDAAM,CAAC,CAAC,EAAE;EACtC,MAAMiP,iBAAiB,GAAGlP,2DAAY,CAACqQ,GAAG,EAAE,eAAe,CAAC,CAACxL,YAAY,CAAC,CAAC;EAC3E,OAAOqK,iBAAiB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,gBAAgBA,CAACD,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACE,OAAO,EAAE;IACtB,MAAMC,oBAAoB,CAAC,mBAAmB,CAAC;EACnD;EACA,IAAI,CAACH,GAAG,CAAC7P,IAAI,EAAE;IACX,MAAMgQ,oBAAoB,CAAC,UAAU,CAAC;EAC1C;EACA;EACA,MAAMC,UAAU,GAAG,CACf,WAAW,EACX,QAAQ,EACR,OAAO,CACV;EACD,KAAK,MAAMC,OAAO,IAAID,UAAU,EAAE;IAC9B,IAAI,CAACJ,GAAG,CAACE,OAAO,CAACG,OAAO,CAAC,EAAE;MACvB,MAAMF,oBAAoB,CAACE,OAAO,CAAC;IACvC;EACJ;EACA,OAAO;IACHjJ,OAAO,EAAE4I,GAAG,CAAC7P,IAAI;IACjBgB,SAAS,EAAE6O,GAAG,CAACE,OAAO,CAAC/O,SAAS;IAChC4B,MAAM,EAAEiN,GAAG,CAACE,OAAO,CAACnN,MAAM;IAC1B+B,KAAK,EAAEkL,GAAG,CAACE,OAAO,CAACpL;EACvB,CAAC;AACL;AACA,SAASqL,oBAAoBA,CAACG,SAAS,EAAE;EACrC,OAAOzP,aAAa,CAAC2B,MAAM,CAAC,2BAA2B,CAAC,2CAA2C;IAC/F8N;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,eAAe;AAC1C,MAAMC,2BAA2B,GAAG,wBAAwB;AAC5D,MAAMC,aAAa,GAAIC,SAAS,IAAK;EACjC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvD;EACA,MAAMrB,SAAS,GAAG8M,gBAAgB,CAACD,GAAG,CAAC;EACvC,MAAM5L,wBAAwB,GAAGzE,2DAAY,CAACqQ,GAAG,EAAE,WAAW,CAAC;EAC/D,MAAMnB,iBAAiB,GAAG;IACtBmB,GAAG;IACH7M,SAAS;IACTiB,wBAAwB;IACxBwM,OAAO,EAAEA,CAAA,KAAMhL,OAAO,CAACC,OAAO,CAAC;EACnC,CAAC;EACD,OAAOgJ,iBAAiB;AAC5B,CAAC;AACD,MAAMgC,eAAe,GAAIH,SAAS,IAAK;EACnC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvD;EACA,MAAMyG,aAAa,GAAGtL,2DAAY,CAACqQ,GAAG,EAAEO,kBAAkB,CAAC,CAAC/L,YAAY,CAAC,CAAC;EAC1E,MAAMsM,qBAAqB,GAAG;IAC1BpC,KAAK,EAAEA,CAAA,KAAMA,KAAK,CAACzD,aAAa,CAAC;IACjC+D,QAAQ,EAAG5B,YAAY,IAAK4B,QAAQ,CAAC/D,aAAa,EAAEmC,YAAY;EACpE,CAAC;EACD,OAAO0D,qBAAqB;AAChC,CAAC;AACD,SAASC,qBAAqBA,CAAA,EAAG;EAC7BlR,iEAAkB,CAAC,IAAIE,0DAAS,CAACwQ,kBAAkB,EAAEE,aAAa,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACzG5Q,iEAAkB,CAAC,IAAIE,0DAAS,CAACyQ,2BAA2B,EAAEK,eAAe,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AAC1H;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAE,qBAAqB,CAAC,CAAC;AACvBjR,8DAAe,CAACK,IAAI,EAAEC,OAAO,CAAC;AAC9B;AACAN,8DAAe,CAACK,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;;;;;;;;;;;;;;;AC/nCG;AACI;AACF;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8Q,qBAAqB,CAAC;EACxBC,WAAWA,CAACnB,GAAG,EAAEoB,SAAS,EAAE;IACxB,IAAI,CAACpB,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoB,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIC,sBAAsBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACD,SAAS,CAACC,sBAAsB;EAChD;EACA,IAAIA,sBAAsBA,CAACC,GAAG,EAAE;IAC5B,IAAI,CAACF,SAAS,CAACC,sBAAsB,GAAGC,GAAG;EAC/C;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACH,SAAS,CAACG,qBAAqB;EAC/C;EACA,IAAIA,qBAAqBA,CAACD,GAAG,EAAE;IAC3B,IAAI,CAACF,SAAS,CAACG,qBAAqB,GAAGD,GAAG;EAC9C;EACAL,KAAKA,CAACO,SAAS,EAAE;IACb,OAAOP,4DAAK,CAAC,IAAI,CAACG,SAAS,EAAEI,SAAS,CAAC;EAC3C;AACJ;AAEA,MAAMrR,IAAI,GAAG,8BAA8B;AAC3C,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqR,yBAAyBA,CAACC,gBAAgB,EAAE;EACjDA,gBAAgB,CAACC,QAAQ,CAACC,iBAAiB,CAAC,IAAI7R,0DAAS,CAAC,oBAAoB,EAAE8R,kBAAkB,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACzIH,gBAAgB,CAAC5R,eAAe,CAACK,IAAI,EAAEC,OAAO,CAAC;AACnD;AACA,SAASyR,kBAAkBA,CAACnB,SAAS,EAAE;EACnC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,YAAY,CAAC,CAACnM,YAAY,CAAC,CAAC;EAC9D;EACA,MAAMsN,WAAW,GAAGpB,SAAS,CAACC,WAAW,CAAC,aAAa,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvE,OAAO,IAAI0M,qBAAqB,CAAClB,GAAG,EAAE8B,WAAW,CAAC;AACtD;AACAL,yBAAyB,CAACT,4DAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;ACvE8G;AAC7F;AACsC;AAC1C;AACf;AAEjC,MAAM7Q,IAAI,GAAG,uBAAuB;AACpC,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkS,WAAW,GAAGlS,OAAO;AAC3B;AACA,MAAMmS,uBAAuB,GAAG,qBAAqB;AACrD;AACA,MAAMC,sBAAsB,GAAG,oBAAoB;AACnD;AACA,MAAMC,oBAAoB,GAAG,uBAAuB;AACpD;AACA,MAAMC,0BAA0B,GAAG,MAAM;AACzC,MAAMC,wBAAwB,GAAG,KAAK;AACtC,MAAMC,mCAAmC,GAAG,MAAM;AAClD,MAAMC,8BAA8B,GAAG,MAAM;AAC7C,MAAMC,wBAAwB,GAAG,8BAA8B;AAC/D,MAAMC,+BAA+B,GAAG,oCAAoC;AAC5E,MAAMrS,OAAO,GAAG,aAAa;AAC7B,MAAMC,YAAY,GAAG,aAAa;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1B,CAAC,eAAe,CAAC,uCAAuC,wCAAwC;EAChG,CAAC,eAAe,CAAC,uCAAuC,oCAAoC;EAC5F,CAAC,6BAA6B,CAAC,+CAA+C,kDAAkD;EAChI,CAAC,4BAA4B,CAAC,6CAA6C,iDAAiD;EAC5H,CAAC,WAAW,CAAC,4BAA4B,0BAA0B;EACnE,CAAC,WAAW,CAAC,4BAA4B,0BAA0B;EACnE,CAAC,eAAe,CAAC,gCAAgC,8BAA8B;EAC/E,CAAC,YAAY,CAAC,6BAA6B,2BAA2B;EACtE,CAAC,gBAAgB,CAAC,iCAAiC,qCAAqC;EACxF,CAAC,gBAAgB,CAAC,iCAAiC,2EAA2E;EAC9H,CAAC,oBAAoB,CAAC,4BAA4B,uBAAuB;EACzE,CAAC,wBAAwB,CAAC,yCAAyC,6CAA6C;EAChH,CAAC,yBAAyB,CAAC,0CAA0C,+CAA+C;EACpH,CAAC,4BAA4B,CAAC,6CAA6C,mDAAmD;EAC9H,CAAC,6BAA6B,CAAC,kDAAkD,sEAAsE;EACvJ,CAAC,qBAAqB,CAAC,sCAAsC,uDAAuD,GAChH,gFAAgF,GAChF,uFAAuF,GACvF;AACR,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIb,wDAAY,CAACU,OAAO,EAAEC,YAAY,EAAEC,qBAAqB,CAAC;;AAEpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoS,aAAa,GAAG,IAAIZ,oDAAM,CAACzR,YAAY,CAAC;AAC9CqS,aAAa,CAACC,QAAQ,GAAGZ,sDAAQ,CAACa,IAAI;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf,IAAIC,cAAc;AAClB;AACA;AACA;AACA;AACA,MAAMC,GAAG,CAAC;EACNlC,WAAWA,CAACmC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACA,MAAM,EAAE;MACT,MAAMzS,aAAa,CAAC2B,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC;IACrE;;IACA,IAAI,CAACsP,WAAW,GAAGwB,MAAM,CAACxB,WAAW;IACrC,IAAI,CAACyB,mBAAmB,GAAGD,MAAM,CAACC,mBAAmB;IACrD,IAAI,CAACC,cAAc,GAAGF,MAAM,CAACG,QAAQ;IACrC,IAAI,CAAC/H,SAAS,GAAG4H,MAAM,CAAC5H,SAAS;IACjC,IAAI,CAACgI,QAAQ,GAAGJ,MAAM,CAACI,QAAQ;IAC/B,IAAI,IAAI,CAAChI,SAAS,IAAI,IAAI,CAACA,SAAS,CAACiI,aAAa,EAAE;MAChD;MACA;MACA,IAAI,CAACC,YAAY,GAAGN,MAAM,CAACM,YAAY;IAC3C;IACA,IAAIN,MAAM,CAACO,WAAW,IAAIP,MAAM,CAACO,WAAW,CAACC,iBAAiB,EAAE;MAC5D,IAAI,CAACA,iBAAiB,GAAGR,MAAM,CAACO,WAAW,CAACC,iBAAiB;IACjE;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL;IACA,OAAO,IAAI,CAACP,cAAc,CAACQ,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACjD;EACAC,IAAIA,CAAC/T,IAAI,EAAE;IACP,IAAI,CAAC,IAAI,CAAC2R,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACoC,IAAI,EAAE;MAC7C;IACJ;IACA,IAAI,CAACpC,WAAW,CAACoC,IAAI,CAAC/T,IAAI,CAAC;EAC/B;EACAgU,OAAOA,CAACC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACxC,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACqC,OAAO,EAAE;MAChD;IACJ;IACA,IAAI,CAACrC,WAAW,CAACqC,OAAO,CAACC,WAAW,EAAEC,KAAK,EAAEC,KAAK,CAAC;EACvD;EACAC,gBAAgBA,CAACC,IAAI,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC1C,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACyC,gBAAgB,EAAE;MACzD,OAAO,EAAE;IACb;IACA,OAAO,IAAI,CAACzC,WAAW,CAACyC,gBAAgB,CAACC,IAAI,CAAC;EAClD;EACAC,gBAAgBA,CAACtU,IAAI,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC2R,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAAC2C,gBAAgB,EAAE;MACzD,OAAO,EAAE;IACb;IACA,OAAO,IAAI,CAAC3C,WAAW,CAAC2C,gBAAgB,CAACtU,IAAI,CAAC;EAClD;EACAuU,aAAaA,CAAA,EAAG;IACZ;IACA,OAAQ,IAAI,CAAC5C,WAAW,KACnB,IAAI,CAACA,WAAW,CAAC6C,UAAU,IAAI,IAAI,CAAC7C,WAAW,CAAC8C,MAAM,CAACC,eAAe,CAAC;EAChF;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC1P,KAAK,IAAI,CAACQ,OAAO,IAAI,CAACmM,iEAAiB,CAAC,CAAC,EAAE;MAC5CiB,aAAa,CAAC+B,IAAI,CAAC,wGAAwG,CAAC;MAC5H,OAAO,KAAK;IAChB;IACA,IAAI,CAAC/C,oEAAoB,CAAC,CAAC,EAAE;MACzBgB,aAAa,CAAC+B,IAAI,CAAC,+CAA+C,CAAC;MACnE,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAC,aAAaA,CAACC,SAAS,EAAErN,QAAQ,EAAE;IAC/B,IAAI,CAAC,IAAI,CAAC2L,mBAAmB,EAAE;MAC3B;IACJ;IACA,MAAM2B,QAAQ,GAAG,IAAI,IAAI,CAAC3B,mBAAmB,CAAC4B,IAAI,IAAI;MAClD,KAAK,MAAM3J,KAAK,IAAI2J,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;QACnC;QACAxN,QAAQ,CAAC4D,KAAK,CAAC;MACnB;IACJ,CAAC,CAAC;IACF;IACA0J,QAAQ,CAACG,OAAO,CAAC;MAAEC,UAAU,EAAE,CAACL,SAAS;IAAE,CAAC,CAAC;EACjD;EACA,OAAOM,WAAWA,CAAA,EAAG;IACjB,IAAIpC,WAAW,KAAKtI,SAAS,EAAE;MAC3BsI,WAAW,GAAG,IAAIE,GAAG,CAACD,cAAc,CAAC;IACzC;IACA,OAAOD,WAAW;EACtB;AACJ;AACA,SAASqC,QAAQA,CAAClC,MAAM,EAAE;EACtBF,cAAc,GAAGE,MAAM;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAImC,GAAG;AACP,SAASC,aAAaA,CAACC,oBAAoB,EAAE;EACzC,MAAMC,UAAU,GAAGD,oBAAoB,CAACjH,KAAK,CAAC,CAAC;EAC/C;EACAkH,UAAU,CAACC,IAAI,CAAEC,MAAM,IAAK;IACxBL,GAAG,GAAGK,MAAM;EAChB,CAAC,CAAC;EACF,OAAOF,UAAU;AACrB;AACA;AACA,SAASG,MAAMA,CAAA,EAAG;EACd,OAAON,GAAG;AACd;AACA,SAASO,mBAAmBA,CAACL,oBAAoB,EAAE;EAC/C,MAAMM,gBAAgB,GAAGN,oBAAoB,CAAC3G,QAAQ,CAAC,CAAC;EACxD;EACAiH,gBAAgB,CAACJ,IAAI,CAAEK,YAAY,IAAK,CACxC,CAAC,CAAC;EACF,OAAOD,gBAAgB;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAChC,MAAMC,QAAQ,GAAGF,KAAK,CAACG,MAAM,GAAGF,KAAK,CAACE,MAAM;EAC5C,IAAID,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAC9B,MAAMzV,aAAa,CAAC2B,MAAM,CAAC,6BAA6B,CAAC,+CAA+C,CAAC;EAC7G;;EACA,MAAMgU,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;IACnCD,WAAW,CAACE,IAAI,CAACN,KAAK,CAACO,MAAM,CAACF,CAAC,CAAC,CAAC;IACjC,IAAIJ,KAAK,CAACE,MAAM,GAAGE,CAAC,EAAE;MAClBD,WAAW,CAACE,IAAI,CAACL,KAAK,CAACM,MAAM,CAACF,CAAC,CAAC,CAAC;IACrC;EACJ;EACA,OAAOD,WAAW,CAACI,IAAI,CAAC,EAAE,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,uBAAuB;AAC3B,MAAMC,eAAe,CAAC;EAClB3F,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACE,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACE,qBAAqB,GAAG,IAAI;IACjC;IACA,IAAI,CAACwF,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,2BAA2B,GAAG,CAAC;IACpC;IACA,IAAI,CAACC,cAAc,GAAG,mEAAmE;IACzF;IACA;IACA,IAAI,CAACC,sBAAsB,GAAGhB,YAAY,CAAC,kCAAkC,EAAE,iCAAiC,CAAC;IACjH,IAAI,CAACiB,YAAY,GAAGjB,YAAY,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;IAC/E;IACA,IAAI,CAACkB,SAAS,GAAG,GAAG;IACpB;IACA,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC9B;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACN,sBAAsB,CAACO,MAAM,CAAC,OAAO,EAAE,IAAI,CAACN,YAAY,CAAC;EACzE;EACA,OAAO7B,WAAWA,CAAA,EAAG;IACjB,IAAIsB,uBAAuB,KAAKhM,SAAS,EAAE;MACvCgM,uBAAuB,GAAG,IAAIC,eAAe,CAAC,CAAC;IACnD;IACA,OAAOD,uBAAuB;EAClC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIc,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC3DA,eAAe,CAACA,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC3DA,eAAe,CAACA,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC7D,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAMC,2BAA2B,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC;AACnE,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,gBAAgB,CAAC;AAC3D,MAAMC,yBAAyB,GAAG,EAAE;AACpC,MAAMC,0BAA0B,GAAG,GAAG;AACtC,SAASC,sBAAsBA,CAAA,EAAG;EAC9B,MAAMvM,SAAS,GAAG2H,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC7J,SAAS;EAC7C,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACwM,aAAa,EAAE;IAC/E,IAAIxM,SAAS,CAACwM,aAAa,CAACC,UAAU,EAAE;MACpC,OAAO,CAAC,CAAC;IACb,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ,CAAC,MACI;IACD,OAAO,CAAC,CAAC;EACb;AACJ;;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,MAAM1E,QAAQ,GAAGL,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC7B,QAAQ;EAC3C,MAAM2E,eAAe,GAAG3E,QAAQ,CAAC2E,eAAe;EAChD,QAAQA,eAAe;IACnB,KAAK,SAAS;MACV,OAAOV,eAAe,CAACW,OAAO;IAClC,KAAK,QAAQ;MACT,OAAOX,eAAe,CAACY,MAAM;IACjC;MACI,OAAOZ,eAAe,CAACa,OAAO;EACtC;AACJ;AACA,SAASC,0BAA0BA,CAAA,EAAG;EAClC,MAAM/M,SAAS,GAAG2H,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC7J,SAAS;EAC7C,MAAMgN,mBAAmB,GAAGhN,SAAS,CAACiN,UAAU;EAChD,MAAMC,aAAa,GAAGF,mBAAmB,IAAIA,mBAAmB,CAACE,aAAa;EAC9E,QAAQA,aAAa;IACjB,KAAK,SAAS;MACV,OAAO,CAAC,CAAC;IACb,KAAK,IAAI;MACL,OAAO,CAAC,CAAC;IACb,KAAK,IAAI;MACL,OAAO,CAAC,CAAC;IACb,KAAK,IAAI;MACL,OAAO,CAAC,CAAC;IACb;MACI,OAAO,CAAC,CAAC;EACjB;AACJ;;AACA,SAASC,0BAA0BA,CAAC1Y,IAAI,EAAE;EACtC,IAAIA,IAAI,CAACoW,MAAM,KAAK,CAAC,IAAIpW,IAAI,CAACoW,MAAM,GAAGwB,yBAAyB,EAAE;IAC9D,OAAO,KAAK;EAChB;EACA,MAAMe,qBAAqB,GAAGlB,2BAA2B,CAACmB,IAAI,CAACC,MAAM,IAAI7Y,IAAI,CAAC8Y,UAAU,CAACD,MAAM,CAAC,CAAC;EACjG,OAAO,CAACF,qBAAqB,IAAI,CAAC,CAAC3Y,IAAI,CAAC+Y,KAAK,CAACrB,sBAAsB,CAAC;AACzE;AACA,SAASsB,2BAA2BA,CAACvP,KAAK,EAAE;EACxC,OAAOA,KAAK,CAAC2M,MAAM,KAAK,CAAC,IAAI3M,KAAK,CAAC2M,MAAM,IAAIyB,0BAA0B;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,QAAQA,CAACC,WAAW,EAAE;EAC3B,IAAIrS,EAAE;EACN,MAAMlC,KAAK,GAAG,CAACkC,EAAE,GAAGqS,WAAW,CAACnJ,OAAO,MAAM,IAAI,IAAIlJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClC,KAAK;EACtF,IAAI,CAACA,KAAK,EAAE;IACR,MAAMjE,aAAa,CAAC2B,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC;EACrE;;EACA,OAAOsC,KAAK;AAChB;AACA,SAASwU,YAAYA,CAACD,WAAW,EAAE;EAC/B,IAAIrS,EAAE;EACN,MAAM7F,SAAS,GAAG,CAAC6F,EAAE,GAAGqS,WAAW,CAACnJ,OAAO,MAAM,IAAI,IAAIlJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,SAAS;EAC9F,IAAI,CAACA,SAAS,EAAE;IACZ,MAAMN,aAAa,CAAC2B,MAAM,CAAC,eAAe,CAAC,6BAA6B,CAAC;EAC7E;;EACA,OAAOrB,SAAS;AACpB;AACA,SAASoY,SAASA,CAACF,WAAW,EAAE;EAC5B,IAAIrS,EAAE;EACN,MAAMjE,MAAM,GAAG,CAACiE,EAAE,GAAGqS,WAAW,CAACnJ,OAAO,MAAM,IAAI,IAAIlJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjE,MAAM;EACxF,IAAI,CAACA,MAAM,EAAE;IACT,MAAMlC,aAAa,CAAC2B,MAAM,CAAC,YAAY,CAAC,0BAA0B,CAAC;EACvE;;EACA,OAAOO,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyW,yBAAyB,GAAG,OAAO;AACzC;AACA;AACA,MAAMC,eAAe,GAAG;EACpB1C,cAAc,EAAE;AACpB,CAAC;AACD,MAAM2C,eAAe,GAAG,6BAA6B;AACrD,SAASC,SAASA,CAACC,qBAAqB,EAAEnE,GAAG,EAAE;EAC3C,MAAMoE,MAAM,GAAGC,eAAe,CAAC,CAAC;EAChC,IAAID,MAAM,EAAE;IACRE,aAAa,CAACF,MAAM,CAAC;IACrB,OAAOjU,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACA,OAAOmU,eAAe,CAACJ,qBAAqB,EAAEnE,GAAG,CAAC,CAC7CI,IAAI,CAACkE,aAAa,CAAC,CACnBlE,IAAI,CAACgE,MAAM,IAAII,WAAW,CAACJ,MAAM,CAAC,EACvC;EACA,MAAM,CAAE,CAAC,CAAC;AACd;AACA,SAASC,eAAeA,CAAA,EAAG;EACvB,MAAMlG,YAAY,GAAGP,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC3B,YAAY;EACnD,IAAI,CAACA,YAAY,EAAE;IACf;EACJ;EACA,MAAMsG,YAAY,GAAGtG,YAAY,CAACuG,OAAO,CAACpH,+BAA+B,CAAC;EAC1E,IAAI,CAACmH,YAAY,IAAI,CAACE,WAAW,CAACF,YAAY,CAAC,EAAE;IAC7C;EACJ;EACA,MAAMG,iBAAiB,GAAGzG,YAAY,CAACuG,OAAO,CAACrH,wBAAwB,CAAC;EACxE,IAAI,CAACuH,iBAAiB,EAAE;IACpB;EACJ;EACA,IAAI;IACA,MAAMC,cAAc,GAAGpV,IAAI,CAACqV,KAAK,CAACF,iBAAiB,CAAC;IACpD,OAAOC,cAAc;EACzB,CAAC,CACD,OAAOtT,EAAE,EAAE;IACP;EACJ;AACJ;AACA,SAASiT,WAAWA,CAACJ,MAAM,EAAE;EACzB,MAAMjG,YAAY,GAAGP,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC3B,YAAY;EACnD,IAAI,CAACiG,MAAM,IAAI,CAACjG,YAAY,EAAE;IAC1B;EACJ;EACAA,YAAY,CAAC4G,OAAO,CAAC1H,wBAAwB,EAAE5N,IAAI,CAACC,SAAS,CAAC0U,MAAM,CAAC,CAAC;EACtEjG,YAAY,CAAC4G,OAAO,CAACzH,+BAA+B,EAAE5M,MAAM,CAACxE,IAAI,CAACC,GAAG,CAAC,CAAC,GACnEkV,eAAe,CAACvB,WAAW,CAAC,CAAC,CAACiC,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACzE;AACA,MAAMiD,wBAAwB,GAAG,kDAAkD;AACnF,SAAST,eAAeA,CAACJ,qBAAqB,EAAEnE,GAAG,EAAE;EACjD;EACA,OAAOO,mBAAmB,CAAC4D,qBAAqB,CAAC3O,aAAa,CAAC,CAC1D4K,IAAI,CAACpQ,SAAS,IAAI;IACnB,MAAMtE,SAAS,GAAGmY,YAAY,CAACM,qBAAqB,CAAC5J,GAAG,CAAC;IACzD,MAAMjN,MAAM,GAAGwW,SAAS,CAACK,qBAAqB,CAAC5J,GAAG,CAAC;IACnD,MAAM0K,cAAc,GAAI,2DAA0DvZ,SAAU,kCAAiC4B,MAAO,EAAC;IACrI,MAAMiC,OAAO,GAAG,IAAI2V,OAAO,CAACD,cAAc,EAAE;MACxCzV,MAAM,EAAE,MAAM;MACd5B,OAAO,EAAE;QAAEuX,aAAa,EAAG,GAAElB,eAAgB,IAAGjU,SAAU;MAAE,CAAC;MAC7D;MACAb,IAAI,EAAEM,IAAI,CAACC,SAAS,CAAC;QACjB0V,eAAe,EAAEpF,GAAG;QACpBqF,qBAAqB,EAAErV,SAAS;QAChCsV,MAAM,EAAE3B,QAAQ,CAACQ,qBAAqB,CAAC5J,GAAG,CAAC;QAC3CgL,WAAW,EAAE1I,WAAW;QACxB2I,WAAW,EAAEzB;MACjB,CAAC;MACD;IACJ,CAAC,CAAC;;IACF,OAAOpU,KAAK,CAACJ,OAAO,CAAC,CAAC6Q,IAAI,CAACxU,QAAQ,IAAI;MACnC,IAAIA,QAAQ,CAACgE,EAAE,EAAE;QACb,OAAOhE,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAC1B;MACA;MACA,MAAMzB,aAAa,CAAC2B,MAAM,CAAC,oBAAoB,CAAC,yBAAyB,CAAC;IAC9E,CAAC,CAAC;EACN,CAAC,CAAC,CACGsM,KAAK,CAAC,MAAM;IACbkE,aAAa,CAAC+B,IAAI,CAAC0F,wBAAwB,CAAC;IAC5C,OAAO5P,SAAS;EACpB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASkP,aAAaA,CAACF,MAAM,EAAE;EAC3B,IAAI,CAACA,MAAM,EAAE;IACT,OAAOA,MAAM;EACjB;EACA,MAAMhD,uBAAuB,GAAGC,eAAe,CAACvB,WAAW,CAAC,CAAC;EAC7D,MAAM2F,OAAO,GAAGrB,MAAM,CAACqB,OAAO,IAAI,CAAC,CAAC;EACpC,IAAIA,OAAO,CAACC,WAAW,KAAKtQ,SAAS,EAAE;IACnC;IACA;IACAgM,uBAAuB,CAACE,cAAc,GAClC5Q,MAAM,CAAC+U,OAAO,CAACC,WAAW,CAAC,KAAK,MAAM;EAC9C,CAAC,MACI;IACD;IACA;IACAtE,uBAAuB,CAACE,cAAc,GAAG0C,eAAe,CAAC1C,cAAc;EAC3E;EACA,IAAImE,OAAO,CAACE,cAAc,EAAE;IACxBvE,uBAAuB,CAACQ,SAAS,GAAGvT,MAAM,CAACoX,OAAO,CAACE,cAAc,CAAC;EACtE,CAAC,MACI,IAAI3B,eAAe,CAACpC,SAAS,EAAE;IAChCR,uBAAuB,CAACQ,SAAS,GAAGoC,eAAe,CAACpC,SAAS;EACjE;EACA,IAAI6D,OAAO,CAACG,oBAAoB,EAAE;IAC9BxE,uBAAuB,CAACK,cAAc,GAAGgE,OAAO,CAACG,oBAAoB;EACzE,CAAC,MACI,IAAI5B,eAAe,CAACvC,cAAc,EAAE;IACrCL,uBAAuB,CAACK,cAAc,GAAGuC,eAAe,CAACvC,cAAc;EAC3E;EACA;EACA,IAAIgE,OAAO,CAACI,qBAAqB,EAAE;IAC/BzE,uBAAuB,CAACO,YAAY,GAAG8D,OAAO,CAACI,qBAAqB;EACxE,CAAC,MACI,IAAI7B,eAAe,CAACrC,YAAY,EAAE;IACnCP,uBAAuB,CAACO,YAAY,GAAGqC,eAAe,CAACrC,YAAY;EACvE;EACA,IAAI8D,OAAO,CAACK,oCAAoC,KAAK1Q,SAAS,EAAE;IAC5DgM,uBAAuB,CAACI,2BAA2B,GAAGnT,MAAM,CAACoX,OAAO,CAACK,oCAAoC,CAAC;EAC9G,CAAC,MACI,IAAI9B,eAAe,CAACxC,2BAA2B,KAAKpM,SAAS,EAAE;IAChEgM,uBAAuB,CAACI,2BAA2B,GAC/CwC,eAAe,CAACxC,2BAA2B;EACnD;EACA,IAAIiE,OAAO,CAACM,0BAA0B,KAAK3Q,SAAS,EAAE;IAClDgM,uBAAuB,CAACG,kBAAkB,GAAGlT,MAAM,CAACoX,OAAO,CAACM,0BAA0B,CAAC;EAC3F,CAAC,MACI,IAAI/B,eAAe,CAACzC,kBAAkB,KAAKnM,SAAS,EAAE;IACvDgM,uBAAuB,CAACG,kBAAkB,GACtCyC,eAAe,CAACzC,kBAAkB;EAC1C;EACA;EACAH,uBAAuB,CAACS,qBAAqB,GAAGmE,sBAAsB,CAAC5E,uBAAuB,CAACG,kBAAkB,CAAC;EAClHH,uBAAuB,CAACU,uBAAuB,GAAGkE,sBAAsB,CAAC5E,uBAAuB,CAACI,2BAA2B,CAAC;EAC7H,OAAO4C,MAAM;AACjB;AACA,SAASO,WAAWA,CAACsB,MAAM,EAAE;EACzB,OAAO5X,MAAM,CAAC4X,MAAM,CAAC,GAAG/Z,IAAI,CAACC,GAAG,CAAC,CAAC;AACtC;AACA,SAAS6Z,sBAAsBA,CAACE,YAAY,EAAE;EAC1C,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIF,YAAY;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,oBAAoB,GAAG,CAAC,CAAC;AAC7B,IAAIC,qBAAqB;AACzB,SAASC,wBAAwBA,CAACpC,qBAAqB,EAAE;EACrDkC,oBAAoB,GAAG,CAAC,CAAC;EACzBC,qBAAqB,GACjBA,qBAAqB,IAAIE,cAAc,CAACrC,qBAAqB,CAAC;EAClE,OAAOmC,qBAAqB;AAChC;AACA,SAASG,iBAAiBA,CAAA,EAAG;EACzB,OAAOJ,oBAAoB,KAAK,CAAC,CAAC;AACtC;;AACA,SAASG,cAAcA,CAACrC,qBAAqB,EAAE;EAC3C,OAAOuC,wBAAwB,CAAC,CAAC,CAC5BtG,IAAI,CAAC,MAAMH,aAAa,CAACkE,qBAAqB,CAAC3O,aAAa,CAAC,CAAC,CAC9D4K,IAAI,CAACJ,GAAG,IAAIkE,SAAS,CAACC,qBAAqB,EAAEnE,GAAG,CAAC,CAAC,CAClDI,IAAI,CAAC,MAAMuG,0BAA0B,CAAC,CAAC,EAAE,MAAMA,0BAA0B,CAAC,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,SAASD,wBAAwBA,CAAA,EAAG;EAChC,MAAMzI,QAAQ,GAAGL,GAAG,CAACkC,WAAW,CAAC,CAAC,CAAC7B,QAAQ;EAC3C,OAAO,IAAI9N,OAAO,CAACC,OAAO,IAAI;IAC1B,IAAI6N,QAAQ,IAAIA,QAAQ,CAAC2I,UAAU,KAAK,UAAU,EAAE;MAChD,MAAMC,OAAO,GAAGA,CAAA,KAAM;QAClB,IAAI5I,QAAQ,CAAC2I,UAAU,KAAK,UAAU,EAAE;UACpC3I,QAAQ,CAAC6I,mBAAmB,CAAC,kBAAkB,EAAED,OAAO,CAAC;UACzDzW,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MACD6N,QAAQ,CAAC8I,gBAAgB,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IAC1D,CAAC,MACI;MACDzW,OAAO,CAAC,CAAC;IACb;EACJ,CAAC,CAAC;AACN;AACA,SAASuW,0BAA0BA,CAAA,EAAG;EAClCN,oBAAoB,GAAG,CAAC,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,wBAAwB,GAAG,EAAE,GAAG,IAAI;AAC1C,MAAMC,0BAA0B,GAAG,GAAG,GAAG,IAAI;AAC7C;AACA,MAAMC,uBAAuB,GAAG,CAAC;AACjC,MAAMC,2BAA2B,GAAG,IAAI;AACxC,IAAIC,cAAc,GAAGF,uBAAuB;AAC5C;AACA,IAAIG,KAAK,GAAG,EAAE;AACd,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,SAASC,qBAAqBA,CAAA,EAAG;EAC7B,IAAI,CAACD,gBAAgB,EAAE;IACnBE,YAAY,CAACP,0BAA0B,CAAC;IACxCK,gBAAgB,GAAG,IAAI;EAC3B;AACJ;AACA,SAASE,YAAYA,CAACC,UAAU,EAAE;EAC9BpX,UAAU,CAAC,MAAM;IACb;IACA,IAAI+W,cAAc,KAAK,CAAC,EAAE;MACtB;IACJ;IACA;IACA,IAAI,CAACC,KAAK,CAACvG,MAAM,EAAE;MACf,OAAO0G,YAAY,CAACR,wBAAwB,CAAC;IACjD;IACAU,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAED,UAAU,CAAC;AAClB;AACA,SAASC,mBAAmBA,CAAA,EAAG;EAC3B;EACA;EACA;EACA,MAAMC,MAAM,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC,EAAET,2BAA2B,CAAC;EAC3D;EACA;EACA,MAAMU,SAAS,GAAGF,MAAM,CAACG,GAAG,CAACC,GAAG,KAAK;IACjCC,4BAA4B,EAAED,GAAG,CAAC7a,OAAO;IACzC+a,aAAa,EAAEvX,MAAM,CAACqX,GAAG,CAACG,SAAS;EACvC,CAAC,CAAC,CAAC;EACH,MAAM7U,IAAI,GAAG;IACT8U,eAAe,EAAEzX,MAAM,CAACxE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACnCic,WAAW,EAAE;MACTC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;IACrB,CAAC;IACDC,UAAU,EAAElH,eAAe,CAACvB,WAAW,CAAC,CAAC,CAAC8B,SAAS;IACnDiG;EACJ,CAAC;EACD;EACAW,cAAc,CAACnV,IAAI,EAAEsU,MAAM,CAAC,CAACtO,KAAK,CAAC,MAAM;IACrC;IACA;IACAgO,KAAK,GAAG,CAAC,GAAGM,MAAM,EAAE,GAAGN,KAAK,CAAC;IAC7BD,cAAc,EAAE;IAChB7J,aAAa,CAAC+B,IAAI,CAAE,eAAc8H,cAAe,GAAE,CAAC;IACpDI,YAAY,CAACR,wBAAwB,CAAC;EAC1C,CAAC,CAAC;AACN;AACA,SAASwB,cAAcA,CAACnV,IAAI,EAAEsU,MAAM,EAAE;EAClC,OAAOc,gBAAgB,CAACpV,IAAI,CAAC,CACxB+M,IAAI,CAACsI,GAAG,IAAI;IACb,IAAI,CAACA,GAAG,CAAC9Y,EAAE,EAAE;MACT2N,aAAa,CAAC+B,IAAI,CAAC,kCAAkC,CAAC;IAC1D;IACA,OAAOoJ,GAAG,CAAC7b,IAAI,CAAC,CAAC;EACrB,CAAC,CAAC,CACGuT,IAAI,CAACsI,GAAG,IAAI;IACb;IACA,MAAMC,aAAa,GAAGta,MAAM,CAACqa,GAAG,CAACE,qBAAqB,CAAC;IACvD,IAAIC,aAAa,GAAG7B,wBAAwB;IAC5C,IAAI,CAAC8B,KAAK,CAACH,aAAa,CAAC,EAAE;MACvBE,aAAa,GAAG1C,IAAI,CAAC4C,GAAG,CAACJ,aAAa,EAAEE,aAAa,CAAC;IAC1D;IACA;IACA;IACA,MAAMG,kBAAkB,GAAGN,GAAG,CAACM,kBAAkB;IACjD,IAAIC,KAAK,CAACC,OAAO,CAACF,kBAAkB,CAAC,IACjCA,kBAAkB,CAAClI,MAAM,GAAG,CAAC,IAC7BkI,kBAAkB,CAAC,CAAC,CAAC,CAACG,cAAc,KAAK,qBAAqB,EAAE;MAChE9B,KAAK,GAAG,CAAC,GAAGM,MAAM,EAAE,GAAGN,KAAK,CAAC;MAC7B9J,aAAa,CAAC+B,IAAI,CAAE,gCAA+B,CAAC;IACxD;IACA8H,cAAc,GAAGF,uBAAuB;IACxC;IACAM,YAAY,CAACqB,aAAa,CAAC;EAC/B,CAAC,CAAC;AACN;AACA,SAASJ,gBAAgBA,CAACpV,IAAI,EAAE;EAC5B,MAAM+V,kBAAkB,GAAG/H,eAAe,CAACvB,WAAW,CAAC,CAAC,CAACkC,qBAAqB,CAAC,CAAC;EAChF,OAAOrS,KAAK,CAACyZ,kBAAkB,EAAE;IAC7B5Z,MAAM,EAAE,MAAM;IACdL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAAC2D,IAAI;EAC7B,CAAC,CAAC;AACN;AACA,SAASgW,UAAUA,CAACtB,GAAG,EAAE;EACrB,IAAI,CAACA,GAAG,CAACG,SAAS,IAAI,CAACH,GAAG,CAAC7a,OAAO,EAAE;IAChC,MAAM9B,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;EAC/E;EACA;EACAsa,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAEU,GAAG,CAAC;AAC3B;AACA;AACA,SAASuB,gBAAgBA;AACzB;AACAC,UAAU,EAAE;EACR,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChB,MAAMtc,OAAO,GAAGqc,UAAU,CAAC,GAAGC,IAAI,CAAC;IACnCH,UAAU,CAAC;MACPnc,OAAO;MACPgb,SAAS,EAAEhc,IAAI,CAACC,GAAG,CAAC;IACxB,CAAC,CAAC;EACN,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsd,MAAM;AACV;AACA,SAASC,OAAOA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EACrC,IAAI,CAACH,MAAM,EAAE;IACTA,MAAM,GAAGH,gBAAgB,CAACC,UAAU,CAAC;EACzC;EACAE,MAAM,CAACE,QAAQ,EAAEC,YAAY,CAAC;AAClC;AACA,SAASC,QAAQA,CAACrO,KAAK,EAAE;EACrB,MAAMsO,eAAe,GAAGzI,eAAe,CAACvB,WAAW,CAAC,CAAC;EACrD;EACA,IAAI,CAACgK,eAAe,CAAClO,sBAAsB,IAAIJ,KAAK,CAACuO,MAAM,EAAE;IACzD;EACJ;EACA;EACA,IAAI,CAACD,eAAe,CAAChO,qBAAqB,IAAI,CAACN,KAAK,CAACuO,MAAM,EAAE;IACzD;EACJ;EACA;EACA,IAAI,CAACnM,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACT,qBAAqB,CAAC,CAAC,EAAE;IAC5C;EACJ;EACA;EACA,IAAI7D,KAAK,CAACuO,MAAM,IAAIpH,kBAAkB,CAAC,CAAC,KAAKT,eAAe,CAACW,OAAO,EAAE;IAClE;EACJ;EACA,IAAI4D,iBAAiB,CAAC,CAAC,EAAE;IACrBuD,YAAY,CAACxO,KAAK,CAAC;EACvB,CAAC,MACI;IACD;IACA;IACA+K,wBAAwB,CAAC/K,KAAK,CAAC2I,qBAAqB,CAAC,CAAC/D,IAAI,CAAC,MAAM4J,YAAY,CAACxO,KAAK,CAAC,EAAE,MAAMwO,YAAY,CAACxO,KAAK,CAAC,CAAC;EACpH;AACJ;AACA,SAASwO,YAAYA,CAACxO,KAAK,EAAE;EACzB,IAAI,CAAC8E,MAAM,CAAC,CAAC,EAAE;IACX;EACJ;EACA,MAAMwJ,eAAe,GAAGzI,eAAe,CAACvB,WAAW,CAAC,CAAC;EACrD,IAAI,CAACgK,eAAe,CAACxI,cAAc,IAC/B,CAACwI,eAAe,CAACjI,qBAAqB,EAAE;IACxC;EACJ;EACAxR,UAAU,CAAC,MAAMqZ,OAAO,CAAClO,KAAK,EAAE,CAAC,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;AACnE;AACA,SAASyO,iBAAiBA,CAACC,cAAc,EAAE;EACvC,MAAMJ,eAAe,GAAGzI,eAAe,CAACvB,WAAW,CAAC,CAAC;EACrD;EACA,IAAI,CAACgK,eAAe,CAAClO,sBAAsB,EAAE;IACzC;EACJ;EACA;EACA;EACA,MAAMuO,iBAAiB,GAAGD,cAAc,CAACE,GAAG;EAC5C;EACA;EACA,MAAMC,cAAc,GAAGP,eAAe,CAACrI,cAAc,CAACjD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACnE,MAAM8L,aAAa,GAAGR,eAAe,CAACpI,sBAAsB,CAAClD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1E,IAAI2L,iBAAiB,KAAKE,cAAc,IACpCF,iBAAiB,KAAKG,aAAa,EAAE;IACrC;EACJ;EACA,IAAI,CAACR,eAAe,CAACxI,cAAc,IAC/B,CAACwI,eAAe,CAAChI,uBAAuB,EAAE;IAC1C;EACJ;EACAzR,UAAU,CAAC,MAAMqZ,OAAO,CAACQ,cAAc,EAAE,CAAC,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC;AACrF;AACA,SAASX,UAAUA,CAACI,QAAQ,EAAEC,YAAY,EAAE;EACxC,IAAIA,YAAY,KAAK,CAAC,CAAC,mCAAmC;IACtD,OAAOW,uBAAuB,CAACZ,QAAQ,CAAC;EAC5C;EACA,OAAOa,cAAc,CAACb,QAAQ,CAAC;AACnC;AACA,SAASY,uBAAuBA,CAACL,cAAc,EAAE;EAC7C,MAAMO,oBAAoB,GAAG;IACzBL,GAAG,EAAEF,cAAc,CAACE,GAAG;IACvBM,WAAW,EAAER,cAAc,CAACS,UAAU,IAAI,CAAC;IAC3CC,kBAAkB,EAAE,GAAG;IACvBC,sBAAsB,EAAEX,cAAc,CAACY,oBAAoB;IAC3DC,oBAAoB,EAAEb,cAAc,CAACc,WAAW;IAChDC,6BAA6B,EAAEf,cAAc,CAACgB,yBAAyB;IACvEC,6BAA6B,EAAEjB,cAAc,CAACkB;EAClD,CAAC;EACD,MAAMC,UAAU,GAAG;IACfC,gBAAgB,EAAEC,kBAAkB,CAACrB,cAAc,CAAC/F,qBAAqB,CAAC5J,GAAG,CAAC;IAC9EiR,sBAAsB,EAAEf;EAC5B,CAAC;EACD,OAAOhb,IAAI,CAACC,SAAS,CAAC2b,UAAU,CAAC;AACrC;AACA,SAASb,cAAcA,CAAChP,KAAK,EAAE;EAC3B,MAAMiQ,WAAW,GAAG;IAChB/gB,IAAI,EAAE8Q,KAAK,CAAC9Q,IAAI;IAChBghB,OAAO,EAAElQ,KAAK,CAACuO,MAAM;IACrBgB,oBAAoB,EAAEvP,KAAK,CAACwP,WAAW;IACvCW,WAAW,EAAEnQ,KAAK,CAACoQ;EACvB,CAAC;EACD,IAAIpT,MAAM,CAACqT,IAAI,CAACrQ,KAAK,CAACsQ,QAAQ,CAAC,CAAChL,MAAM,KAAK,CAAC,EAAE;IAC1C2K,WAAW,CAACK,QAAQ,GAAGtQ,KAAK,CAACsQ,QAAQ;EACzC;EACA,MAAMC,gBAAgB,GAAGvQ,KAAK,CAACwQ,aAAa,CAAC,CAAC;EAC9C,IAAIxT,MAAM,CAACqT,IAAI,CAACE,gBAAgB,CAAC,CAACjL,MAAM,KAAK,CAAC,EAAE;IAC5C2K,WAAW,CAACQ,iBAAiB,GAAGF,gBAAgB;EACpD;EACA,MAAMV,UAAU,GAAG;IACfC,gBAAgB,EAAEC,kBAAkB,CAAC/P,KAAK,CAAC2I,qBAAqB,CAAC5J,GAAG,CAAC;IACrE2R,YAAY,EAAET;EAClB,CAAC;EACD,OAAOhc,IAAI,CAACC,SAAS,CAAC2b,UAAU,CAAC;AACrC;AACA,SAASE,kBAAkBA,CAAC3H,WAAW,EAAE;EACrC,OAAO;IACHuI,aAAa,EAAExI,QAAQ,CAACC,WAAW,CAAC;IACpCwB,eAAe,EAAE9E,MAAM,CAAC,CAAC;IACzB8L,YAAY,EAAE;MACV5G,WAAW,EAAE3I,WAAW;MACxBwP,QAAQ,EAAEzO,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACxB,MAAM,CAAC,CAAC;MACpCgO,qBAAqB,EAAE9J,sBAAsB,CAAC,CAAC;MAC/C+J,gBAAgB,EAAE5J,kBAAkB,CAAC,CAAC;MACtC6J,yBAAyB,EAAExJ,0BAA0B,CAAC;IAC1D,CAAC;IACDyJ,yBAAyB,EAAE;EAC/B,CAAC;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC,MAAMC,oBAAoB,GAAG,GAAG;AAChC,MAAMC,UAAU,GAAG,CACf1P,wBAAwB,EACxBC,mCAAmC,EACnCC,8BAA8B,CACjC;AACD;AACA;AACA;AACA;AACA,SAASyP,iBAAiBA,CAACniB,IAAI,EAAEqR,SAAS,EAAE;EACxC,IAAIrR,IAAI,CAACoW,MAAM,KAAK,CAAC,IAAIpW,IAAI,CAACoW,MAAM,GAAG4L,sBAAsB,EAAE;IAC3D,OAAO,KAAK;EAChB;EACA,OAAS3Q,SAAS,IACdA,SAAS,CAACyH,UAAU,CAACvG,0BAA0B,CAAC,IAChD2P,UAAU,CAACE,OAAO,CAACpiB,IAAI,CAAC,GAAG,CAAC,CAAC,IAC7B,CAACA,IAAI,CAAC8Y,UAAU,CAACmJ,oBAAoB,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,2BAA2BA,CAACC,aAAa,EAAE;EAChD,MAAMC,cAAc,GAAG9G,IAAI,CAAC+G,KAAK,CAACF,aAAa,CAAC;EAChD,IAAIC,cAAc,GAAGD,aAAa,EAAE;IAChCzP,aAAa,CAAC+B,IAAI,CAAE,6DAA4D2N,cAAe,GAAE,CAAC;EACtG;EACA,OAAOA,cAAc;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,KAAK,CAAC;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIzR,WAAWA,CAACyI,qBAAqB,EAAEzZ,IAAI,EAAEqf,MAAM,GAAG,KAAK,EAAEqD,gBAAgB,EAAE;IACvE,IAAI,CAACjJ,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACzZ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqf,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACsD,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACtB,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACwB,GAAG,GAAG1P,GAAG,CAACkC,WAAW,CAAC,CAAC;IAC5B,IAAI,CAACyN,QAAQ,GAAGpH,IAAI,CAAC+G,KAAK,CAAC/G,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;IACnD,IAAI,CAAC,IAAI,CAAC2D,MAAM,EAAE;MACd,IAAI,CAACyD,cAAc,GAAI,GAAE1Q,uBAAwB,IAAG,IAAI,CAACyQ,QAAS,IAAG,IAAI,CAAC7iB,IAAK,EAAC;MAChF,IAAI,CAAC+iB,aAAa,GAAI,GAAE1Q,sBAAuB,IAAG,IAAI,CAACwQ,QAAS,IAAG,IAAI,CAAC7iB,IAAK,EAAC;MAC9E,IAAI,CAACgjB,YAAY,GACbN,gBAAgB,IACX,GAAEpQ,oBAAqB,IAAG,IAAI,CAACuQ,QAAS,IAAG,IAAI,CAAC7iB,IAAK,EAAC;MAC/D,IAAI0iB,gBAAgB,EAAE;QAClB;QACA;QACA,IAAI,CAACO,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;AACJ;AACA;EACIC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACP,KAAK,KAAK,CAAC,CAAC,gCAAgC;MACjD,MAAMjiB,aAAa,CAAC2B,MAAM,CAAC,eAAe,CAAC,sCAAsC;QAC7EgP,SAAS,EAAE,IAAI,CAACrR;MACpB,CAAC,CAAC;IACN;IACA,IAAI,CAAC4iB,GAAG,CAAC7O,IAAI,CAAC,IAAI,CAAC+O,cAAc,CAAC;IAClC,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACIQ,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACR,KAAK,KAAK,CAAC,CAAC,0BAA0B;MAC3C,MAAMjiB,aAAa,CAAC2B,MAAM,CAAC,eAAe,CAAC,sCAAsC;QAC7EgP,SAAS,EAAE,IAAI,CAACrR;MACpB,CAAC,CAAC;IACN;IACA,IAAI,CAAC2iB,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,GAAG,CAAC7O,IAAI,CAAC,IAAI,CAACgP,aAAa,CAAC;IACjC,IAAI,CAACH,GAAG,CAAC5O,OAAO,CAAC,IAAI,CAACgP,YAAY,EAAE,IAAI,CAACF,cAAc,EAAE,IAAI,CAACC,aAAa,CAAC;IAC5E,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAC5B9D,QAAQ,CAAC,IAAI,CAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiE,MAAMA,CAACC,SAAS,EAAEC,QAAQ,EAAEvT,OAAO,EAAE;IACjC,IAAIsT,SAAS,IAAI,CAAC,EAAE;MAChB,MAAM3iB,aAAa,CAAC2B,MAAM,CAAC,6BAA6B,CAAC,8CAA8C;QACnGgP,SAAS,EAAE,IAAI,CAACrR;MACpB,CAAC,CAAC;IACN;IACA,IAAIsjB,QAAQ,IAAI,CAAC,EAAE;MACf,MAAM5iB,aAAa,CAAC2B,MAAM,CAAC,4BAA4B,CAAC,4CAA4C;QAChGgP,SAAS,EAAE,IAAI,CAACrR;MACpB,CAAC,CAAC;IACN;IACA,IAAI,CAACkhB,UAAU,GAAGzF,IAAI,CAAC+G,KAAK,CAACc,QAAQ,GAAG,IAAI,CAAC;IAC7C,IAAI,CAAChD,WAAW,GAAG7E,IAAI,CAAC+G,KAAK,CAACa,SAAS,GAAG,IAAI,CAAC;IAC/C,IAAItT,OAAO,IAAIA,OAAO,CAACwT,UAAU,EAAE;MAC/B,IAAI,CAAClC,gBAAgB,GAAGvT,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgC,OAAO,CAACwT,UAAU,CAAC;IACjE;IACA,IAAIxT,OAAO,IAAIA,OAAO,CAACyT,OAAO,EAAE;MAC5B,KAAK,MAAMC,UAAU,IAAI3V,MAAM,CAACqT,IAAI,CAACpR,OAAO,CAACyT,OAAO,CAAC,EAAE;QACnD,IAAI,CAACpF,KAAK,CAACza,MAAM,CAACoM,OAAO,CAACyT,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE;UAC7C,IAAI,CAACrC,QAAQ,CAACqC,UAAU,CAAC,GAAGhI,IAAI,CAAC+G,KAAK,CAAC7e,MAAM,CAACoM,OAAO,CAACyT,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC;QAC/E;MACJ;IACJ;IACAtE,QAAQ,CAAC,IAAI,CAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuE,eAAeA,CAACC,OAAO,EAAEC,YAAY,GAAG,CAAC,EAAE;IACvC,IAAI,IAAI,CAACxC,QAAQ,CAACuC,OAAO,CAAC,KAAKjZ,SAAS,EAAE;MACtC,IAAI,CAACmZ,SAAS,CAACF,OAAO,EAAEC,YAAY,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACC,SAAS,CAACF,OAAO,EAAE,IAAI,CAACvC,QAAQ,CAACuC,OAAO,CAAC,GAAGC,YAAY,CAAC;IAClE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACF,OAAO,EAAEC,YAAY,EAAE;IAC7B,IAAIzB,iBAAiB,CAACwB,OAAO,EAAE,IAAI,CAAC3jB,IAAI,CAAC,EAAE;MACvC,IAAI,CAACohB,QAAQ,CAACuC,OAAO,CAAC,GAAGtB,2BAA2B,CAACuB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC,CAAC;IAC7H,CAAC,MACI;MACD,MAAMljB,aAAa,CAAC2B,MAAM,CAAC,4BAA4B,CAAC,4CAA4C;QAChGyhB,gBAAgB,EAAEH;MACtB,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACII,SAASA,CAACJ,OAAO,EAAE;IACf,OAAO,IAAI,CAACvC,QAAQ,CAACuC,OAAO,CAAC,IAAI,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIK,YAAYA,CAACC,IAAI,EAAExa,KAAK,EAAE;IACtB,MAAMya,WAAW,GAAGxL,0BAA0B,CAACuL,IAAI,CAAC;IACpD,MAAME,YAAY,GAAGnL,2BAA2B,CAACvP,KAAK,CAAC;IACvD,IAAIya,WAAW,IAAIC,YAAY,EAAE;MAC7B,IAAI,CAAC9C,gBAAgB,CAAC4C,IAAI,CAAC,GAAGxa,KAAK;MACnC;IACJ;IACA;IACA,IAAI,CAACya,WAAW,EAAE;MACd,MAAMxjB,aAAa,CAAC2B,MAAM,CAAC,wBAAwB,CAAC,wCAAwC;QACxF+hB,aAAa,EAAEH;MACnB,CAAC,CAAC;IACN;IACA,IAAI,CAACE,YAAY,EAAE;MACf,MAAMzjB,aAAa,CAAC2B,MAAM,CAAC,yBAAyB,CAAC,yCAAyC;QAC1FgiB,cAAc,EAAE5a;MACpB,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACI6a,YAAYA,CAACL,IAAI,EAAE;IACf,OAAO,IAAI,CAAC5C,gBAAgB,CAAC4C,IAAI,CAAC;EACtC;EACAM,eAAeA,CAACN,IAAI,EAAE;IAClB,IAAI,IAAI,CAAC5C,gBAAgB,CAAC4C,IAAI,CAAC,KAAKvZ,SAAS,EAAE;MAC3C;IACJ;IACA,OAAO,IAAI,CAAC2W,gBAAgB,CAAC4C,IAAI,CAAC;EACtC;EACA3C,aAAaA,CAAA,EAAG;IACZ,OAAOxT,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsT,gBAAgB,CAAC;EACnD;EACAmD,YAAYA,CAACnB,SAAS,EAAE;IACpB,IAAI,CAAC/C,WAAW,GAAG+C,SAAS;EAChC;EACAoB,WAAWA,CAACnB,QAAQ,EAAE;IAClB,IAAI,CAACpC,UAAU,GAAGoC,QAAQ;EAC9B;EACA;AACJ;AACA;AACA;EACIL,qBAAqBA,CAAA,EAAG;IACpB,MAAMyB,kBAAkB,GAAG,IAAI,CAAC9B,GAAG,CAACtO,gBAAgB,CAAC,IAAI,CAAC0O,YAAY,CAAC;IACvE,MAAM2B,gBAAgB,GAAGD,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;IACpE,IAAIC,gBAAgB,EAAE;MAClB,IAAI,CAACzD,UAAU,GAAGzF,IAAI,CAAC+G,KAAK,CAACmC,gBAAgB,CAACrB,QAAQ,GAAG,IAAI,CAAC;MAC9D,IAAI,CAAChD,WAAW,GAAG7E,IAAI,CAAC+G,KAAK,CAAC,CAACmC,gBAAgB,CAACtB,SAAS,GAAG,IAAI,CAACT,GAAG,CAACrO,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC;IACjG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOqQ,cAAcA,CAACnL,qBAAqB,EAAEoL,iBAAiB,EAAEC,YAAY,EAAEC,eAAe,EAAE;IAC3F,MAAMC,KAAK,GAAG9R,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACxB,MAAM,CAAC,CAAC;IACxC,IAAI,CAACoR,KAAK,EAAE;MACR;IACJ;IACA,MAAMlU,KAAK,GAAG,IAAI2R,KAAK,CAAChJ,qBAAqB,EAAElH,0BAA0B,GAAGyS,KAAK,EAAE,IAAI,CAAC;IACxF,MAAMC,YAAY,GAAGxJ,IAAI,CAAC+G,KAAK,CAACtP,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACb,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC;IACzEzD,KAAK,CAAC0T,YAAY,CAACS,YAAY,CAAC;IAChC;IACA,IAAIJ,iBAAiB,IAAIA,iBAAiB,CAAC,CAAC,CAAC,EAAE;MAC3C/T,KAAK,CAAC2T,WAAW,CAAChJ,IAAI,CAAC+G,KAAK,CAACqC,iBAAiB,CAAC,CAAC,CAAC,CAACvB,QAAQ,GAAG,IAAI,CAAC,CAAC;MACnExS,KAAK,CAAC+S,SAAS,CAAC,gBAAgB,EAAEpI,IAAI,CAAC+G,KAAK,CAACqC,iBAAiB,CAAC,CAAC,CAAC,CAACK,cAAc,GAAG,IAAI,CAAC,CAAC;MACzFpU,KAAK,CAAC+S,SAAS,CAAC,0BAA0B,EAAEpI,IAAI,CAAC+G,KAAK,CAACqC,iBAAiB,CAAC,CAAC,CAAC,CAACM,wBAAwB,GAAG,IAAI,CAAC,CAAC;MAC7GrU,KAAK,CAAC+S,SAAS,CAAC,cAAc,EAAEpI,IAAI,CAAC+G,KAAK,CAACqC,iBAAiB,CAAC,CAAC,CAAC,CAACO,YAAY,GAAG,IAAI,CAAC,CAAC;IACzF;IACA,MAAMC,WAAW,GAAG,aAAa;IACjC,MAAMC,sBAAsB,GAAG,wBAAwB;IACvD,IAAIR,YAAY,EAAE;MACd,MAAMS,UAAU,GAAGT,YAAY,CAACU,IAAI,CAACC,WAAW,IAAIA,WAAW,CAACzlB,IAAI,KAAKqlB,WAAW,CAAC;MACrF,IAAIE,UAAU,IAAIA,UAAU,CAAClC,SAAS,EAAE;QACpCvS,KAAK,CAAC+S,SAAS,CAACrR,wBAAwB,EAAEiJ,IAAI,CAAC+G,KAAK,CAAC+C,UAAU,CAAClC,SAAS,GAAG,IAAI,CAAC,CAAC;MACtF;MACA,MAAMqC,oBAAoB,GAAGZ,YAAY,CAACU,IAAI,CAACC,WAAW,IAAIA,WAAW,CAACzlB,IAAI,KAAKslB,sBAAsB,CAAC;MAC1G,IAAII,oBAAoB,IAAIA,oBAAoB,CAACrC,SAAS,EAAE;QACxDvS,KAAK,CAAC+S,SAAS,CAACpR,mCAAmC,EAAEgJ,IAAI,CAAC+G,KAAK,CAACkD,oBAAoB,CAACrC,SAAS,GAAG,IAAI,CAAC,CAAC;MAC3G;MACA,IAAI0B,eAAe,EAAE;QACjBjU,KAAK,CAAC+S,SAAS,CAACnR,8BAA8B,EAAE+I,IAAI,CAAC+G,KAAK,CAACuC,eAAe,GAAG,IAAI,CAAC,CAAC;MACvF;IACJ;IACA5F,QAAQ,CAACrO,KAAK,CAAC;EACnB;EACA,OAAO6U,qBAAqBA,CAAClM,qBAAqB,EAAExF,WAAW,EAAE;IAC7D,MAAMnD,KAAK,GAAG,IAAI2R,KAAK,CAAChJ,qBAAqB,EAAExF,WAAW,EAAE,KAAK,EAAEA,WAAW,CAAC;IAC/EkL,QAAQ,CAACrO,KAAK,CAAC;EACnB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8U,yBAAyBA,CAACnM,qBAAqB,EAAEpO,KAAK,EAAE;EAC7D,MAAMwa,gBAAgB,GAAGxa,KAAK;EAC9B,IAAI,CAACwa,gBAAgB,IAAIA,gBAAgB,CAACC,aAAa,KAAKpb,SAAS,EAAE;IACnE;EACJ;EACA,MAAM8J,UAAU,GAAGtB,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACb,aAAa,CAAC,CAAC;EACpD,MAAM+L,WAAW,GAAG7E,IAAI,CAAC+G,KAAK,CAAC,CAACqD,gBAAgB,CAACxC,SAAS,GAAG7O,UAAU,IAAI,IAAI,CAAC;EAChF,MAAMgM,yBAAyB,GAAGqF,gBAAgB,CAACC,aAAa,GAC1DrK,IAAI,CAAC+G,KAAK,CAAC,CAACqD,gBAAgB,CAACC,aAAa,GAAGD,gBAAgB,CAACxC,SAAS,IAAI,IAAI,CAAC,GAChF3Y,SAAS;EACf,MAAMgW,yBAAyB,GAAGjF,IAAI,CAAC+G,KAAK,CAAC,CAACqD,gBAAgB,CAACE,WAAW,GAAGF,gBAAgB,CAACxC,SAAS,IAAI,IAAI,CAAC;EAChH;EACA,MAAM3D,GAAG,GAAGmG,gBAAgB,CAAC7lB,IAAI,IAAI6lB,gBAAgB,CAAC7lB,IAAI,CAAC8T,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM0L,cAAc,GAAG;IACnB/F,qBAAqB;IACrBiG,GAAG;IACHU,oBAAoB,EAAEyF,gBAAgB,CAACG,YAAY;IACnD1F,WAAW;IACXE,yBAAyB;IACzBE;EACJ,CAAC;EACDnB,iBAAiB,CAACC,cAAc,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyG,gBAAgB,GAAG,IAAI;AAC7B,SAASC,iBAAiBA,CAACzM,qBAAqB,EAAE;EAC9C;EACA,IAAI,CAAC7D,MAAM,CAAC,CAAC,EAAE;IACX;EACJ;EACA;EACA;EACAjQ,UAAU,CAAC,MAAMwgB,cAAc,CAAC1M,qBAAqB,CAAC,EAAE,CAAC,CAAC;EAC1D9T,UAAU,CAAC,MAAMygB,oBAAoB,CAAC3M,qBAAqB,CAAC,EAAE,CAAC,CAAC;EAChE9T,UAAU,CAAC,MAAM0gB,qBAAqB,CAAC5M,qBAAqB,CAAC,EAAE,CAAC,CAAC;AACrE;AACA,SAAS2M,oBAAoBA,CAAC3M,qBAAqB,EAAE;EACjD,MAAMmJ,GAAG,GAAG1P,GAAG,CAACkC,WAAW,CAAC,CAAC;EAC7B,MAAMkR,SAAS,GAAG1D,GAAG,CAACxO,gBAAgB,CAAC,UAAU,CAAC;EAClD,KAAK,MAAM6K,QAAQ,IAAIqH,SAAS,EAAE;IAC9BV,yBAAyB,CAACnM,qBAAqB,EAAEwF,QAAQ,CAAC;EAC9D;EACA2D,GAAG,CAAC/N,aAAa,CAAC,UAAU,EAAExJ,KAAK,IAAIua,yBAAyB,CAACnM,qBAAqB,EAAEpO,KAAK,CAAC,CAAC;AACnG;AACA,SAAS8a,cAAcA,CAAC1M,qBAAqB,EAAE;EAC3C,MAAMmJ,GAAG,GAAG1P,GAAG,CAACkC,WAAW,CAAC,CAAC;EAC7B,MAAMyP,iBAAiB,GAAGjC,GAAG,CAACxO,gBAAgB,CAAC,YAAY,CAAC;EAC5D,MAAM0Q,YAAY,GAAGlC,GAAG,CAACxO,gBAAgB,CAAC,OAAO,CAAC;EAClD;EACA;EACA,IAAIwO,GAAG,CAACjP,iBAAiB,EAAE;IACvB;IACA;IACA,IAAI4S,SAAS,GAAG5gB,UAAU,CAAC,MAAM;MAC7B8c,KAAK,CAACmC,cAAc,CAACnL,qBAAqB,EAAEoL,iBAAiB,EAAEC,YAAY,CAAC;MAC5EyB,SAAS,GAAG7b,SAAS;IACzB,CAAC,EAAEub,gBAAgB,CAAC;IACpBrD,GAAG,CAACjP,iBAAiB,CAAEzP,GAAG,IAAK;MAC3B,IAAIqiB,SAAS,EAAE;QACXC,YAAY,CAACD,SAAS,CAAC;QACvB9D,KAAK,CAACmC,cAAc,CAACnL,qBAAqB,EAAEoL,iBAAiB,EAAEC,YAAY,EAAE5gB,GAAG,CAAC;MACrF;IACJ,CAAC,CAAC;EACN,CAAC,MACI;IACDue,KAAK,CAACmC,cAAc,CAACnL,qBAAqB,EAAEoL,iBAAiB,EAAEC,YAAY,CAAC;EAChF;AACJ;AACA,SAASuB,qBAAqBA,CAAC5M,qBAAqB,EAAE;EAClD,MAAMmJ,GAAG,GAAG1P,GAAG,CAACkC,WAAW,CAAC,CAAC;EAC7B;EACA,MAAMqR,QAAQ,GAAG7D,GAAG,CAACxO,gBAAgB,CAAC,SAAS,CAAC;EAChD,KAAK,MAAMJ,OAAO,IAAIyS,QAAQ,EAAE;IAC5Bd,qBAAqB,CAAClM,qBAAqB,EAAEzF,OAAO,CAAC;EACzD;EACA;EACA4O,GAAG,CAAC/N,aAAa,CAAC,SAAS,EAAExJ,KAAK,IAAIsa,qBAAqB,CAAClM,qBAAqB,EAAEpO,KAAK,CAAC,CAAC;AAC9F;AACA,SAASsa,qBAAqBA,CAAClM,qBAAqB,EAAEzF,OAAO,EAAE;EAC3D,MAAMC,WAAW,GAAGD,OAAO,CAAChU,IAAI;EAChC;EACA,IAAIiU,WAAW,CAACyS,SAAS,CAAC,CAAC,EAAEpU,oBAAoB,CAAC8D,MAAM,CAAC,KACrD9D,oBAAoB,EAAE;IACtB;EACJ;EACAmQ,KAAK,CAACkD,qBAAqB,CAAClM,qBAAqB,EAAExF,WAAW,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0S,qBAAqB,CAAC;EACxB3V,WAAWA,CAACnB,GAAG,EAAE/E,aAAa,EAAE;IAC5B,IAAI,CAAC+E,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC/E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC8b,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,IAAI,CAACF,WAAW,EAAE;MAClB;IACJ;IACA,IAAI,CAACE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC1V,qBAAqB,MAAM1G,SAAS,EAAE;MACpG,IAAI,CAAC0G,qBAAqB,GAAG0V,QAAQ,CAAC1V,qBAAqB;IAC/D;IACA,IAAI,CAAC0V,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC5V,sBAAsB,MAAMxG,SAAS,EAAE;MACrG,IAAI,CAACwG,sBAAsB,GAAG4V,QAAQ,CAAC5V,sBAAsB;IACjE;IACA,IAAIgC,GAAG,CAACkC,WAAW,CAAC,CAAC,CAACT,qBAAqB,CAAC,CAAC,EAAE;MAC3C7C,yEAAyB,CAAC,CAAC,CACtB4D,IAAI,CAACqR,WAAW,IAAI;QACrB,IAAIA,WAAW,EAAE;UACblK,qBAAqB,CAAC,CAAC;UACvBhB,wBAAwB,CAAC,IAAI,CAAC,CAACnG,IAAI,CAAC,MAAMwQ,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAMA,iBAAiB,CAAC,IAAI,CAAC,CAAC;UACjG,IAAI,CAACU,WAAW,GAAG,IAAI;QAC3B;MACJ,CAAC,CAAC,CACGjY,KAAK,CAAC/N,KAAK,IAAI;QAChBiS,aAAa,CAAC+B,IAAI,CAAE,0CAAyChU,KAAM,EAAC,CAAC;MACzE,CAAC,CAAC;IACN,CAAC,MACI;MACDiS,aAAa,CAAC+B,IAAI,CAAC,oEAAoE,GACnF,iDAAiD,CAAC;IAC1D;EACJ;EACA,IAAI1D,sBAAsBA,CAACC,GAAG,EAAE;IAC5BwF,eAAe,CAACvB,WAAW,CAAC,CAAC,CAAClE,sBAAsB,GAAGC,GAAG;EAC9D;EACA,IAAID,sBAAsBA,CAAA,EAAG;IACzB,OAAOyF,eAAe,CAACvB,WAAW,CAAC,CAAC,CAAClE,sBAAsB;EAC/D;EACA,IAAIE,qBAAqBA,CAACD,GAAG,EAAE;IAC3BwF,eAAe,CAACvB,WAAW,CAAC,CAAC,CAAChE,qBAAqB,GAAGD,GAAG;EAC7D;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAOuF,eAAe,CAACvB,WAAW,CAAC,CAAC,CAAChE,qBAAqB;EAC9D;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4V,kBAAkB,GAAG,WAAW;AACtC;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACpX,GAAG,GAAGpQ,qDAAM,CAAC,CAAC,EAAE;EACpCoQ,GAAG,GAAGkC,kEAAkB,CAAClC,GAAG,CAAC;EAC7B,MAAMqX,QAAQ,GAAG1nB,2DAAY,CAACqQ,GAAG,EAAE,aAAa,CAAC;EACjD,MAAMsX,YAAY,GAAGD,QAAQ,CAAC7iB,YAAY,CAAC,CAAC;EAC5C,OAAO8iB,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACvX,GAAG,EAAEiX,QAAQ,EAAE;EAC1CjX,GAAG,GAAGkC,kEAAkB,CAAClC,GAAG,CAAC;EAC7B,MAAMqX,QAAQ,GAAG1nB,2DAAY,CAACqQ,GAAG,EAAE,aAAa,CAAC;EACjD;EACA;EACA,IAAIqX,QAAQ,CAACG,aAAa,CAAC,CAAC,EAAE;IAC1B,MAAMC,gBAAgB,GAAGJ,QAAQ,CAAC7iB,YAAY,CAAC,CAAC;IAChD,MAAMkjB,eAAe,GAAGL,QAAQ,CAACM,UAAU,CAAC,CAAC;IAC7C,IAAIxV,yDAAS,CAACuV,eAAe,EAAET,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE;MACtF,OAAOQ,gBAAgB;IAC3B,CAAC,MACI;MACD,MAAM5mB,aAAa,CAAC2B,MAAM,CAAC,qBAAqB,CAAC,mCAAmC,CAAC;IACzF;EACJ;;EACA,MAAM8kB,YAAY,GAAGD,QAAQ,CAACO,UAAU,CAAC;IACrC1X,OAAO,EAAE+W;EACb,CAAC,CAAC;EACF,OAAOK,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrW,KAAKA,CAACa,WAAW,EAAE3R,IAAI,EAAE;EAC9B2R,WAAW,GAAGI,kEAAkB,CAACJ,WAAW,CAAC;EAC7C,OAAO,IAAI8Q,KAAK,CAAC9Q,WAAW,EAAE3R,IAAI,CAAC;AACvC;AACA,MAAM0nB,OAAO,GAAGA,CAACnX,SAAS,EAAE;EAAER,OAAO,EAAE+W;AAAS,CAAC,KAAK;EAClD;EACA,MAAMjX,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAACnM,YAAY,CAAC,CAAC;EACvD,MAAMyG,aAAa,GAAGyF,SAAS,CAC1BC,WAAW,CAAC,wBAAwB,CAAC,CACrCnM,YAAY,CAAC,CAAC;EACnB,IAAIwL,GAAG,CAAC7P,IAAI,KAAKgnB,kBAAkB,EAAE;IACjC,MAAMtmB,aAAa,CAAC2B,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC;EAC/E;;EACA,IAAI,OAAO8Q,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMzS,aAAa,CAAC2B,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC;EACrE;;EACAgT,QAAQ,CAAClC,MAAM,CAAC;EAChB,MAAMgU,YAAY,GAAG,IAAIR,qBAAqB,CAAC9W,GAAG,EAAE/E,aAAa,CAAC;EAClEqc,YAAY,CAACN,KAAK,CAACC,QAAQ,CAAC;EAC5B,OAAOK,YAAY;AACvB,CAAC;AACD,SAASQ,mBAAmBA,CAAA,EAAG;EAC3BjoB,iEAAkB,CAAC,IAAIE,0DAAS,CAAC,aAAa,EAAE8nB,OAAO,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EAC9F/nB,8DAAe,CAACK,IAAI,EAAEC,OAAO,CAAC;EAC9B;EACAN,8DAAe,CAACK,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;AAC7C;AACA0nB,mBAAmB,CAAC,CAAC", "sources": ["./node_modules/@firebase/installations/dist/esm/index.esm2017.js", "./node_modules/@firebase/performance-compat/dist/esm/index.esm2017.js", "./node_modules/@firebase/performance/dist/esm/index.esm2017.js", "./node_modules/firebase/compat/performance/dist/esm/index.esm.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\nconst name = \"@firebase/installations\";\nconst version = \"0.6.9\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst PENDING_TIMEOUT_MS = 10000;\r\nconst PACKAGE_VERSION = `w:${version}`;\r\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\r\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\r\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\r\nconst SERVICE = 'installations';\r\nconst SERVICE_NAME = 'Installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_DESCRIPTION_MAP = {\r\n    [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\r\n    [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\r\n    [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\r\n    [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\r\n    [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\r\n    [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\r\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\r\nfunction isServerError(error) {\r\n    return (error instanceof FirebaseError &&\r\n        error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getInstallationsEndpoint({ projectId }) {\r\n    return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\r\n}\r\nfunction extractAuthTokenInfoFromResponse(response) {\r\n    return {\r\n        token: response.token,\r\n        requestStatus: 2 /* RequestStatus.COMPLETED */,\r\n        expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\r\n        creationTime: Date.now()\r\n    };\r\n}\r\nasync function getErrorFromResponse(requestName, response) {\r\n    const responseJson = await response.json();\r\n    const errorData = responseJson.error;\r\n    return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\r\n        requestName,\r\n        serverCode: errorData.code,\r\n        serverMessage: errorData.message,\r\n        serverStatus: errorData.status\r\n    });\r\n}\r\nfunction getHeaders({ apiKey }) {\r\n    return new Headers({\r\n        'Content-Type': 'application/json',\r\n        Accept: 'application/json',\r\n        'x-goog-api-key': apiKey\r\n    });\r\n}\r\nfunction getHeadersWithAuth(appConfig, { refreshToken }) {\r\n    const headers = getHeaders(appConfig);\r\n    headers.append('Authorization', getAuthorizationHeader(refreshToken));\r\n    return headers;\r\n}\r\n/**\r\n * Calls the passed in fetch wrapper and returns the response.\r\n * If the returned response has a status of 5xx, re-runs the function once and\r\n * returns the response.\r\n */\r\nasync function retryIfServerError(fn) {\r\n    const result = await fn();\r\n    if (result.status >= 500 && result.status < 600) {\r\n        // Internal Server Error. Retry request.\r\n        return fn();\r\n    }\r\n    return result;\r\n}\r\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\r\n    // This works because the server will never respond with fractions of a second.\r\n    return Number(responseExpiresIn.replace('s', '000'));\r\n}\r\nfunction getAuthorizationHeader(refreshToken) {\r\n    return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function createInstallationRequest({ appConfig, heartbeatServiceProvider }, { fid }) {\r\n    const endpoint = getInstallationsEndpoint(appConfig);\r\n    const headers = getHeaders(appConfig);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        fid,\r\n        authVersion: INTERNAL_AUTH_VERSION,\r\n        appId: appConfig.appId,\r\n        sdkVersion: PACKAGE_VERSION\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const registeredInstallationEntry = {\r\n            fid: responseValue.fid || fid,\r\n            registrationStatus: 2 /* RequestStatus.COMPLETED */,\r\n            refreshToken: responseValue.refreshToken,\r\n            authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\r\n        };\r\n        return registeredInstallationEntry;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Create Installation', response);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a promise that resolves after given time passes. */\r\nfunction sleep(ms) {\r\n    return new Promise(resolve => {\r\n        setTimeout(resolve, ms);\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction bufferToBase64UrlSafe(array) {\r\n    const b64 = btoa(String.fromCharCode(...array));\r\n    return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\r\nconst INVALID_FID = '';\r\n/**\r\n * Generates a new FID using random values from Web Crypto API.\r\n * Returns an empty string if FID generation fails for any reason.\r\n */\r\nfunction generateFid() {\r\n    try {\r\n        // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\r\n        // bytes. our implementation generates a 17 byte array instead.\r\n        const fidByteArray = new Uint8Array(17);\r\n        const crypto = self.crypto || self.msCrypto;\r\n        crypto.getRandomValues(fidByteArray);\r\n        // Replace the first 4 random bits with the constant FID header of 0b0111.\r\n        fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\r\n        const fid = encode(fidByteArray);\r\n        return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\r\n    }\r\n    catch (_a) {\r\n        // FID generation errored\r\n        return INVALID_FID;\r\n    }\r\n}\r\n/** Converts a FID Uint8Array to a base64 string representation. */\r\nfunction encode(fidByteArray) {\r\n    const b64String = bufferToBase64UrlSafe(fidByteArray);\r\n    // Remove the 23rd character that was added because of the extra 4 bits at the\r\n    // end of our 17 byte array, and the '=' padding.\r\n    return b64String.substr(0, 22);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a string key that can be used to identify the app. */\r\nfunction getKey(appConfig) {\r\n    return `${appConfig.appName}!${appConfig.appId}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst fidChangeCallbacks = new Map();\r\n/**\r\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\r\n * change to other tabs.\r\n */\r\nfunction fidChanged(appConfig, fid) {\r\n    const key = getKey(appConfig);\r\n    callFidChangeCallbacks(key, fid);\r\n    broadcastFidChange(key, fid);\r\n}\r\nfunction addCallback(appConfig, callback) {\r\n    // Open the broadcast channel if it's not already open,\r\n    // to be able to listen to change events from other tabs.\r\n    getBroadcastChannel();\r\n    const key = getKey(appConfig);\r\n    let callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        callbackSet = new Set();\r\n        fidChangeCallbacks.set(key, callbackSet);\r\n    }\r\n    callbackSet.add(callback);\r\n}\r\nfunction removeCallback(appConfig, callback) {\r\n    const key = getKey(appConfig);\r\n    const callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        return;\r\n    }\r\n    callbackSet.delete(callback);\r\n    if (callbackSet.size === 0) {\r\n        fidChangeCallbacks.delete(key);\r\n    }\r\n    // Close broadcast channel if there are no more callbacks.\r\n    closeBroadcastChannel();\r\n}\r\nfunction callFidChangeCallbacks(key, fid) {\r\n    const callbacks = fidChangeCallbacks.get(key);\r\n    if (!callbacks) {\r\n        return;\r\n    }\r\n    for (const callback of callbacks) {\r\n        callback(fid);\r\n    }\r\n}\r\nfunction broadcastFidChange(key, fid) {\r\n    const channel = getBroadcastChannel();\r\n    if (channel) {\r\n        channel.postMessage({ key, fid });\r\n    }\r\n    closeBroadcastChannel();\r\n}\r\nlet broadcastChannel = null;\r\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\r\nfunction getBroadcastChannel() {\r\n    if (!broadcastChannel && 'BroadcastChannel' in self) {\r\n        broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\r\n        broadcastChannel.onmessage = e => {\r\n            callFidChangeCallbacks(e.data.key, e.data.fid);\r\n        };\r\n    }\r\n    return broadcastChannel;\r\n}\r\nfunction closeBroadcastChannel() {\r\n    if (fidChangeCallbacks.size === 0 && broadcastChannel) {\r\n        broadcastChannel.close();\r\n        broadcastChannel = null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DATABASE_NAME = 'firebase-installations-database';\r\nconst DATABASE_VERSION = 1;\r\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\r\n            upgrade: (db, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(OBJECT_STORE_NAME);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\n/** Assigns or overwrites the record for the given key with the given value. */\r\nasync function set(appConfig, value) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const objectStore = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await objectStore.get(key));\r\n    await objectStore.put(value, key);\r\n    await tx.done;\r\n    if (!oldValue || oldValue.fid !== value.fid) {\r\n        fidChanged(appConfig, value.fid);\r\n    }\r\n    return value;\r\n}\r\n/** Removes record(s) from the objectStore that match the given key. */\r\nasync function remove(appConfig) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    await tx.objectStore(OBJECT_STORE_NAME).delete(key);\r\n    await tx.done;\r\n}\r\n/**\r\n * Atomically updates a record with the result of updateFn, which gets\r\n * called with the current value. If newValue is undefined, the record is\r\n * deleted instead.\r\n * @return Updated value\r\n */\r\nasync function update(appConfig, updateFn) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const store = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await store.get(key));\r\n    const newValue = updateFn(oldValue);\r\n    if (newValue === undefined) {\r\n        await store.delete(key);\r\n    }\r\n    else {\r\n        await store.put(newValue, key);\r\n    }\r\n    await tx.done;\r\n    if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\r\n        fidChanged(appConfig, newValue.fid);\r\n    }\r\n    return newValue;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Updates and returns the InstallationEntry from the database.\r\n * Also triggers a registration request if it is necessary and possible.\r\n */\r\nasync function getInstallationEntry(installations) {\r\n    let registrationPromise;\r\n    const installationEntry = await update(installations.appConfig, oldEntry => {\r\n        const installationEntry = updateOrCreateInstallationEntry(oldEntry);\r\n        const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\r\n        registrationPromise = entryWithPromise.registrationPromise;\r\n        return entryWithPromise.installationEntry;\r\n    });\r\n    if (installationEntry.fid === INVALID_FID) {\r\n        // FID generation failed. Waiting for the FID from the server.\r\n        return { installationEntry: await registrationPromise };\r\n    }\r\n    return {\r\n        installationEntry,\r\n        registrationPromise\r\n    };\r\n}\r\n/**\r\n * Creates a new Installation Entry if one does not exist.\r\n * Also clears timed out pending requests.\r\n */\r\nfunction updateOrCreateInstallationEntry(oldEntry) {\r\n    const entry = oldEntry || {\r\n        fid: generateFid(),\r\n        registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n    };\r\n    return clearTimedOutRequest(entry);\r\n}\r\n/**\r\n * If the Firebase Installation is not registered yet, this will trigger the\r\n * registration and return an InProgressInstallationEntry.\r\n *\r\n * If registrationPromise does not exist, the installationEntry is guaranteed\r\n * to be registered.\r\n */\r\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\r\n    if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        if (!navigator.onLine) {\r\n            // Registration required but app is offline.\r\n            const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\r\n            return {\r\n                installationEntry,\r\n                registrationPromise: registrationPromiseWithError\r\n            };\r\n        }\r\n        // Try registering. Change status to IN_PROGRESS.\r\n        const inProgressEntry = {\r\n            fid: installationEntry.fid,\r\n            registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n            registrationTime: Date.now()\r\n        };\r\n        const registrationPromise = registerInstallation(installations, inProgressEntry);\r\n        return { installationEntry: inProgressEntry, registrationPromise };\r\n    }\r\n    else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        return {\r\n            installationEntry,\r\n            registrationPromise: waitUntilFidRegistration(installations)\r\n        };\r\n    }\r\n    else {\r\n        return { installationEntry };\r\n    }\r\n}\r\n/** This will be executed only once for each new Firebase Installation. */\r\nasync function registerInstallation(installations, installationEntry) {\r\n    try {\r\n        const registeredInstallationEntry = await createInstallationRequest(installations, installationEntry);\r\n        return set(installations.appConfig, registeredInstallationEntry);\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) && e.customData.serverCode === 409) {\r\n            // Server returned a \"FID cannot be used\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            // Registration failed. Set FID as not registered.\r\n            await set(installations.appConfig, {\r\n                fid: installationEntry.fid,\r\n                registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n            });\r\n        }\r\n        throw e;\r\n    }\r\n}\r\n/** Call if FID registration is pending in another request. */\r\nasync function waitUntilFidRegistration(installations) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateInstallationRequest(installations.appConfig);\r\n    while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // createInstallation request still in progress.\r\n        await sleep(100);\r\n        entry = await updateInstallationRequest(installations.appConfig);\r\n    }\r\n    if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        const { installationEntry, registrationPromise } = await getInstallationEntry(installations);\r\n        if (registrationPromise) {\r\n            return registrationPromise;\r\n        }\r\n        else {\r\n            // if there is no registrationPromise, entry is registered.\r\n            return installationEntry;\r\n        }\r\n    }\r\n    return entry;\r\n}\r\n/**\r\n * Called only if there is a CreateInstallation request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * CreateInstallation request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateInstallationRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!oldEntry) {\r\n            throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\r\n        }\r\n        return clearTimedOutRequest(oldEntry);\r\n    });\r\n}\r\nfunction clearTimedOutRequest(entry) {\r\n    if (hasInstallationRequestTimedOut(entry)) {\r\n        return {\r\n            fid: entry.fid,\r\n            registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n        };\r\n    }\r\n    return entry;\r\n}\r\nfunction hasInstallationRequestTimedOut(installationEntry) {\r\n    return (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function generateAuthTokenRequest({ appConfig, heartbeatServiceProvider }, installationEntry) {\r\n    const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        installation: {\r\n            sdkVersion: PACKAGE_VERSION,\r\n            appId: appConfig.appId\r\n        }\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\r\n        return completedAuthToken;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Generate Auth Token', response);\r\n    }\r\n}\r\nfunction getGenerateAuthTokenEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a valid authentication token for the installation. Generates a new\r\n * token if one doesn't exist, is expired or about to expire.\r\n *\r\n * Should only be called if the Firebase Installation is registered.\r\n */\r\nasync function refreshAuthToken(installations, forceRefresh = false) {\r\n    let tokenPromise;\r\n    const entry = await update(installations.appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\r\n            // There is a valid token in the DB.\r\n            return oldEntry;\r\n        }\r\n        else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // There already is a token request in progress.\r\n            tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\r\n            return oldEntry;\r\n        }\r\n        else {\r\n            // No token or token expired.\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\r\n            tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\r\n            return inProgressEntry;\r\n        }\r\n    });\r\n    const authToken = tokenPromise\r\n        ? await tokenPromise\r\n        : entry.authToken;\r\n    return authToken;\r\n}\r\n/**\r\n * Call only if FID is registered and Auth Token request is in progress.\r\n *\r\n * Waits until the current pending request finishes. If the request times out,\r\n * tries once in this thread as well.\r\n */\r\nasync function waitUntilAuthTokenRequest(installations, forceRefresh) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateAuthTokenRequest(installations.appConfig);\r\n    while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // generateAuthToken still in progress.\r\n        await sleep(100);\r\n        entry = await updateAuthTokenRequest(installations.appConfig);\r\n    }\r\n    const authToken = entry.authToken;\r\n    if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        return refreshAuthToken(installations, forceRefresh);\r\n    }\r\n    else {\r\n        return authToken;\r\n    }\r\n}\r\n/**\r\n * Called only if there is a GenerateAuthToken request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * GenerateAuthToken request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateAuthTokenRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\r\n            return Object.assign(Object.assign({}, oldEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n        }\r\n        return oldEntry;\r\n    });\r\n}\r\nasync function fetchAuthTokenFromServer(installations, installationEntry) {\r\n    try {\r\n        const authToken = await generateAuthTokenRequest(installations, installationEntry);\r\n        const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken });\r\n        await set(installations.appConfig, updatedInstallationEntry);\r\n        return authToken;\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) &&\r\n            (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\r\n            // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n            await set(installations.appConfig, updatedInstallationEntry);\r\n        }\r\n        throw e;\r\n    }\r\n}\r\nfunction isEntryRegistered(installationEntry) {\r\n    return (installationEntry !== undefined &&\r\n        installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */);\r\n}\r\nfunction isAuthTokenValid(authToken) {\r\n    return (authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ &&\r\n        !isAuthTokenExpired(authToken));\r\n}\r\nfunction isAuthTokenExpired(authToken) {\r\n    const now = Date.now();\r\n    return (now < authToken.creationTime ||\r\n        authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER);\r\n}\r\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\r\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\r\n    const inProgressAuthToken = {\r\n        requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n        requestTime: Date.now()\r\n    };\r\n    return Object.assign(Object.assign({}, oldEntry), { authToken: inProgressAuthToken });\r\n}\r\nfunction hasAuthTokenRequestTimedOut(authToken) {\r\n    return (authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        authToken.requestTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Creates a Firebase Installation if there isn't one for the app and\r\n * returns the Installation ID.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function getId(installations) {\r\n    const installationsImpl = installations;\r\n    const { installationEntry, registrationPromise } = await getInstallationEntry(installationsImpl);\r\n    if (registrationPromise) {\r\n        registrationPromise.catch(console.error);\r\n    }\r\n    else {\r\n        // If the installation is already registered, update the authentication\r\n        // token if needed.\r\n        refreshAuthToken(installationsImpl).catch(console.error);\r\n    }\r\n    return installationEntry.fid;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a Firebase Installations auth token, identifying the current\r\n * Firebase Installation.\r\n * @param installations - The `Installations` instance.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\r\nasync function getToken(installations, forceRefresh = false) {\r\n    const installationsImpl = installations;\r\n    await completeInstallationRegistration(installationsImpl);\r\n    // At this point we either have a Registered Installation in the DB, or we've\r\n    // already thrown an error.\r\n    const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\r\n    return authToken.token;\r\n}\r\nasync function completeInstallationRegistration(installations) {\r\n    const { registrationPromise } = await getInstallationEntry(installations);\r\n    if (registrationPromise) {\r\n        // A createInstallation request is in progress. Wait until it finishes.\r\n        await registrationPromise;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function deleteInstallationRequest(appConfig, installationEntry) {\r\n    const endpoint = getDeleteEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    const request = {\r\n        method: 'DELETE',\r\n        headers\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (!response.ok) {\r\n        throw await getErrorFromResponse('Delete Installation', response);\r\n    }\r\n}\r\nfunction getDeleteEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Deletes the Firebase Installation and all associated data.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function deleteInstallations(installations) {\r\n    const { appConfig } = installations;\r\n    const entry = await update(appConfig, oldEntry => {\r\n        if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n            // Delete the unregistered entry without sending a deleteInstallation request.\r\n            return undefined;\r\n        }\r\n        return oldEntry;\r\n    });\r\n    if (entry) {\r\n        if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // Can't delete while trying to register.\r\n            throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\r\n        }\r\n        else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            else {\r\n                await deleteInstallationRequest(appConfig, entry);\r\n                await remove(appConfig);\r\n            }\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Sets a new callback that will get called when Installation ID changes.\r\n * Returns an unsubscribe function that will remove the callback when called.\r\n * @param installations - The `Installations` instance.\r\n * @param callback - The callback function that is invoked when FID changes.\r\n * @returns A function that can be called to unsubscribe.\r\n *\r\n * @public\r\n */\r\nfunction onIdChange(installations, callback) {\r\n    const { appConfig } = installations;\r\n    addCallback(appConfig, callback);\r\n    return () => {\r\n        removeCallback(appConfig, callback);\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns an instance of {@link Installations} associated with the given\r\n * {@link @firebase/app#FirebaseApp} instance.\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * @public\r\n */\r\nfunction getInstallations(app = getApp()) {\r\n    const installationsImpl = _getProvider(app, 'installations').getImmediate();\r\n    return installationsImpl;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction extractAppConfig(app) {\r\n    if (!app || !app.options) {\r\n        throw getMissingValueError('App Configuration');\r\n    }\r\n    if (!app.name) {\r\n        throw getMissingValueError('App Name');\r\n    }\r\n    // Required app config keys\r\n    const configKeys = [\r\n        'projectId',\r\n        'apiKey',\r\n        'appId'\r\n    ];\r\n    for (const keyName of configKeys) {\r\n        if (!app.options[keyName]) {\r\n            throw getMissingValueError(keyName);\r\n        }\r\n    }\r\n    return {\r\n        appName: app.name,\r\n        projectId: app.options.projectId,\r\n        apiKey: app.options.apiKey,\r\n        appId: app.options.appId\r\n    };\r\n}\r\nfunction getMissingValueError(valueName) {\r\n    return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\r\n        valueName\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst INSTALLATIONS_NAME = 'installations';\r\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\r\nconst publicFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Throws if app isn't configured properly.\r\n    const appConfig = extractAppConfig(app);\r\n    const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\r\n    const installationsImpl = {\r\n        app,\r\n        appConfig,\r\n        heartbeatServiceProvider,\r\n        _delete: () => Promise.resolve()\r\n    };\r\n    return installationsImpl;\r\n};\r\nconst internalFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Internal FIS instance relies on public FIS instance.\r\n    const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\r\n    const installationsInternal = {\r\n        getId: () => getId(installations),\r\n        getToken: (forceRefresh) => getToken(installations, forceRefresh)\r\n    };\r\n    return installationsInternal;\r\n};\r\nfunction registerInstallations() {\r\n    _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n}\n\n/**\r\n * The Firebase Installations Web SDK.\r\n * This SDK does not work in a Node.js environment.\r\n *\r\n * @packageDocumentation\r\n */\r\nregisterInstallations();\r\nregisterVersion(name, version);\r\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\nregisterVersion(name, version, 'esm2017');\n\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };\n", "import firebase from '@firebase/app-compat';\nimport { Component } from '@firebase/component';\nimport { trace } from '@firebase/performance';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass PerformanceCompatImpl {\r\n    constructor(app, _delegate) {\r\n        this.app = app;\r\n        this._delegate = _delegate;\r\n    }\r\n    get instrumentationEnabled() {\r\n        return this._delegate.instrumentationEnabled;\r\n    }\r\n    set instrumentationEnabled(val) {\r\n        this._delegate.instrumentationEnabled = val;\r\n    }\r\n    get dataCollectionEnabled() {\r\n        return this._delegate.dataCollectionEnabled;\r\n    }\r\n    set dataCollectionEnabled(val) {\r\n        this._delegate.dataCollectionEnabled = val;\r\n    }\r\n    trace(traceName) {\r\n        return trace(this._delegate, traceName);\r\n    }\r\n}\n\nconst name = \"@firebase/performance-compat\";\nconst version = \"0.2.9\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction registerPerformanceCompat(firebaseInstance) {\r\n    firebaseInstance.INTERNAL.registerComponent(new Component('performance-compat', performanceFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    firebaseInstance.registerVersion(name, version);\r\n}\r\nfunction performanceFactory(container) {\r\n    const app = container.getProvider('app-compat').getImmediate();\r\n    // The following call will always succeed.\r\n    const performance = container.getProvider('performance').getImmediate();\r\n    return new PerformanceCompatImpl(app, performance);\r\n}\r\nregisterPerformanceCompat(firebase);\n", "import { ErrorFactory, areCookiesEnabled, isIndexedDBAvailable, validateIndexedDBOpenable, getModularInstance, deepEqual } from '@firebase/util';\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport '@firebase/installations';\n\nconst name = \"@firebase/performance\";\nconst version = \"0.6.9\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst SDK_VERSION = version;\r\n/** The prefix for start User Timing marks used for creating Traces. */\r\nconst TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\r\n/** The prefix for stop User Timing marks used for creating Traces. */\r\nconst TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\r\n/** The prefix for User Timing measure used for creating Traces. */\r\nconst TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\r\n/** The prefix for out of the box page load Trace name. */\r\nconst OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\r\nconst FIRST_PAINT_COUNTER_NAME = '_fp';\r\nconst FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\r\nconst FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\r\nconst CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\r\nconst CONFIG_EXPIRY_LOCAL_STORAGE_KEY = '@firebase/performance/configexpire';\r\nconst SERVICE = 'performance';\r\nconst SERVICE_NAME = 'Performance';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_DESCRIPTION_MAP = {\r\n    [\"trace started\" /* ErrorCode.TRACE_STARTED_BEFORE */]: 'Trace {$traceName} was started before.',\r\n    [\"trace stopped\" /* ErrorCode.TRACE_STOPPED_BEFORE */]: 'Trace {$traceName} is not running.',\r\n    [\"nonpositive trace startTime\" /* ErrorCode.NONPOSITIVE_TRACE_START_TIME */]: 'Trace {$traceName} startTime should be positive.',\r\n    [\"nonpositive trace duration\" /* ErrorCode.NONPOSITIVE_TRACE_DURATION */]: 'Trace {$traceName} duration should be positive.',\r\n    [\"no window\" /* ErrorCode.NO_WINDOW */]: 'Window is not available.',\r\n    [\"no app id\" /* ErrorCode.NO_APP_ID */]: 'App id is not available.',\r\n    [\"no project id\" /* ErrorCode.NO_PROJECT_ID */]: 'Project id is not available.',\r\n    [\"no api key\" /* ErrorCode.NO_API_KEY */]: 'Api key is not available.',\r\n    [\"invalid cc log\" /* ErrorCode.INVALID_CC_LOG */]: 'Attempted to queue invalid cc event',\r\n    [\"FB not default\" /* ErrorCode.FB_NOT_DEFAULT */]: 'Performance can only start when Firebase app instance is the default one.',\r\n    [\"RC response not ok\" /* ErrorCode.RC_NOT_OK */]: 'RC response is not ok',\r\n    [\"invalid attribute name\" /* ErrorCode.INVALID_ATTRIBUTE_NAME */]: 'Attribute name {$attributeName} is invalid.',\r\n    [\"invalid attribute value\" /* ErrorCode.INVALID_ATTRIBUTE_VALUE */]: 'Attribute value {$attributeValue} is invalid.',\r\n    [\"invalid custom metric name\" /* ErrorCode.INVALID_CUSTOM_METRIC_NAME */]: 'Custom metric name {$customMetricName} is invalid',\r\n    [\"invalid String merger input\" /* ErrorCode.INVALID_STRING_MERGER_PARAMETER */]: 'Input for String merger is invalid, contact support team to resolve.',\r\n    [\"already initialized\" /* ErrorCode.ALREADY_INITIALIZED */]: 'initializePerformance() has already been called with ' +\r\n        'different options. To avoid this error, call initializePerformance() with the ' +\r\n        'same options as when it was originally called, or call getPerformance() to return the' +\r\n        ' already initialized instance.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst consoleLogger = new Logger(SERVICE_NAME);\r\nconsoleLogger.logLevel = LogLevel.INFO;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nlet apiInstance;\r\nlet windowInstance;\r\n/**\r\n * This class holds a reference to various browser related objects injected by\r\n * set methods.\r\n */\r\nclass Api {\r\n    constructor(window) {\r\n        this.window = window;\r\n        if (!window) {\r\n            throw ERROR_FACTORY.create(\"no window\" /* ErrorCode.NO_WINDOW */);\r\n        }\r\n        this.performance = window.performance;\r\n        this.PerformanceObserver = window.PerformanceObserver;\r\n        this.windowLocation = window.location;\r\n        this.navigator = window.navigator;\r\n        this.document = window.document;\r\n        if (this.navigator && this.navigator.cookieEnabled) {\r\n            // If user blocks cookies on the browser, accessing localStorage will\r\n            // throw an exception.\r\n            this.localStorage = window.localStorage;\r\n        }\r\n        if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\r\n            this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\r\n        }\r\n    }\r\n    getUrl() {\r\n        // Do not capture the string query part of url.\r\n        return this.windowLocation.href.split('?')[0];\r\n    }\r\n    mark(name) {\r\n        if (!this.performance || !this.performance.mark) {\r\n            return;\r\n        }\r\n        this.performance.mark(name);\r\n    }\r\n    measure(measureName, mark1, mark2) {\r\n        if (!this.performance || !this.performance.measure) {\r\n            return;\r\n        }\r\n        this.performance.measure(measureName, mark1, mark2);\r\n    }\r\n    getEntriesByType(type) {\r\n        if (!this.performance || !this.performance.getEntriesByType) {\r\n            return [];\r\n        }\r\n        return this.performance.getEntriesByType(type);\r\n    }\r\n    getEntriesByName(name) {\r\n        if (!this.performance || !this.performance.getEntriesByName) {\r\n            return [];\r\n        }\r\n        return this.performance.getEntriesByName(name);\r\n    }\r\n    getTimeOrigin() {\r\n        // Polyfill the time origin with performance.timing.navigationStart.\r\n        return (this.performance &&\r\n            (this.performance.timeOrigin || this.performance.timing.navigationStart));\r\n    }\r\n    requiredApisAvailable() {\r\n        if (!fetch || !Promise || !areCookiesEnabled()) {\r\n            consoleLogger.info('Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.');\r\n            return false;\r\n        }\r\n        if (!isIndexedDBAvailable()) {\r\n            consoleLogger.info('IndexedDB is not supported by current browser');\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n    setupObserver(entryType, callback) {\r\n        if (!this.PerformanceObserver) {\r\n            return;\r\n        }\r\n        const observer = new this.PerformanceObserver(list => {\r\n            for (const entry of list.getEntries()) {\r\n                // `entry` is a PerformanceEntry instance.\r\n                callback(entry);\r\n            }\r\n        });\r\n        // Start observing the entry types you care about.\r\n        observer.observe({ entryTypes: [entryType] });\r\n    }\r\n    static getInstance() {\r\n        if (apiInstance === undefined) {\r\n            apiInstance = new Api(windowInstance);\r\n        }\r\n        return apiInstance;\r\n    }\r\n}\r\nfunction setupApi(window) {\r\n    windowInstance = window;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nlet iid;\r\nfunction getIidPromise(installationsService) {\r\n    const iidPromise = installationsService.getId();\r\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n    iidPromise.then((iidVal) => {\r\n        iid = iidVal;\r\n    });\r\n    return iidPromise;\r\n}\r\n// This method should be used after the iid is retrieved by getIidPromise method.\r\nfunction getIid() {\r\n    return iid;\r\n}\r\nfunction getAuthTokenPromise(installationsService) {\r\n    const authTokenPromise = installationsService.getToken();\r\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n    authTokenPromise.then((authTokenVal) => {\r\n    });\r\n    return authTokenPromise;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction mergeStrings(part1, part2) {\r\n    const sizeDiff = part1.length - part2.length;\r\n    if (sizeDiff < 0 || sizeDiff > 1) {\r\n        throw ERROR_FACTORY.create(\"invalid String merger input\" /* ErrorCode.INVALID_STRING_MERGER_PARAMETER */);\r\n    }\r\n    const resultArray = [];\r\n    for (let i = 0; i < part1.length; i++) {\r\n        resultArray.push(part1.charAt(i));\r\n        if (part2.length > i) {\r\n            resultArray.push(part2.charAt(i));\r\n        }\r\n    }\r\n    return resultArray.join('');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nlet settingsServiceInstance;\r\nclass SettingsService {\r\n    constructor() {\r\n        // The variable which controls logging of automatic traces and HTTP/S network monitoring.\r\n        this.instrumentationEnabled = true;\r\n        // The variable which controls logging of custom traces.\r\n        this.dataCollectionEnabled = true;\r\n        // Configuration flags set through remote config.\r\n        this.loggingEnabled = false;\r\n        // Sampling rate between 0 and 1.\r\n        this.tracesSamplingRate = 1;\r\n        this.networkRequestsSamplingRate = 1;\r\n        // Address of logging service.\r\n        this.logEndPointUrl = 'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\r\n        // Performance event transport endpoint URL which should be compatible with proto3.\r\n        // New Address for transport service, not configurable via Remote Config.\r\n        this.flTransportEndpointUrl = mergeStrings('hts/frbslgigp.ogepscmv/ieo/eaylg', 'tp:/ieaeogn-agolai.o/1frlglgc/o');\r\n        this.transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\r\n        // Source type for performance event logs.\r\n        this.logSource = 462;\r\n        // Flags which control per session logging of traces and network requests.\r\n        this.logTraceAfterSampling = false;\r\n        this.logNetworkAfterSampling = false;\r\n        // TTL of config retrieved from remote config in hours.\r\n        this.configTimeToLive = 12;\r\n    }\r\n    getFlTransportFullUrl() {\r\n        return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\r\n    }\r\n    static getInstance() {\r\n        if (settingsServiceInstance === undefined) {\r\n            settingsServiceInstance = new SettingsService();\r\n        }\r\n        return settingsServiceInstance;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nvar VisibilityState;\r\n(function (VisibilityState) {\r\n    VisibilityState[VisibilityState[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\r\n    VisibilityState[VisibilityState[\"VISIBLE\"] = 1] = \"VISIBLE\";\r\n    VisibilityState[VisibilityState[\"HIDDEN\"] = 2] = \"HIDDEN\";\r\n})(VisibilityState || (VisibilityState = {}));\r\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\r\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\r\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\r\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\r\nfunction getServiceWorkerStatus() {\r\n    const navigator = Api.getInstance().navigator;\r\n    if (navigator === null || navigator === void 0 ? void 0 : navigator.serviceWorker) {\r\n        if (navigator.serviceWorker.controller) {\r\n            return 2 /* ServiceWorkerStatus.CONTROLLED */;\r\n        }\r\n        else {\r\n            return 3 /* ServiceWorkerStatus.UNCONTROLLED */;\r\n        }\r\n    }\r\n    else {\r\n        return 1 /* ServiceWorkerStatus.UNSUPPORTED */;\r\n    }\r\n}\r\nfunction getVisibilityState() {\r\n    const document = Api.getInstance().document;\r\n    const visibilityState = document.visibilityState;\r\n    switch (visibilityState) {\r\n        case 'visible':\r\n            return VisibilityState.VISIBLE;\r\n        case 'hidden':\r\n            return VisibilityState.HIDDEN;\r\n        default:\r\n            return VisibilityState.UNKNOWN;\r\n    }\r\n}\r\nfunction getEffectiveConnectionType() {\r\n    const navigator = Api.getInstance().navigator;\r\n    const navigatorConnection = navigator.connection;\r\n    const effectiveType = navigatorConnection && navigatorConnection.effectiveType;\r\n    switch (effectiveType) {\r\n        case 'slow-2g':\r\n            return 1 /* EffectiveConnectionType.CONNECTION_SLOW_2G */;\r\n        case '2g':\r\n            return 2 /* EffectiveConnectionType.CONNECTION_2G */;\r\n        case '3g':\r\n            return 3 /* EffectiveConnectionType.CONNECTION_3G */;\r\n        case '4g':\r\n            return 4 /* EffectiveConnectionType.CONNECTION_4G */;\r\n        default:\r\n            return 0 /* EffectiveConnectionType.UNKNOWN */;\r\n    }\r\n}\r\nfunction isValidCustomAttributeName(name) {\r\n    if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\r\n        return false;\r\n    }\r\n    const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix => name.startsWith(prefix));\r\n    return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\r\n}\r\nfunction isValidCustomAttributeValue(value) {\r\n    return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getAppId(firebaseApp) {\r\n    var _a;\r\n    const appId = (_a = firebaseApp.options) === null || _a === void 0 ? void 0 : _a.appId;\r\n    if (!appId) {\r\n        throw ERROR_FACTORY.create(\"no app id\" /* ErrorCode.NO_APP_ID */);\r\n    }\r\n    return appId;\r\n}\r\nfunction getProjectId(firebaseApp) {\r\n    var _a;\r\n    const projectId = (_a = firebaseApp.options) === null || _a === void 0 ? void 0 : _a.projectId;\r\n    if (!projectId) {\r\n        throw ERROR_FACTORY.create(\"no project id\" /* ErrorCode.NO_PROJECT_ID */);\r\n    }\r\n    return projectId;\r\n}\r\nfunction getApiKey(firebaseApp) {\r\n    var _a;\r\n    const apiKey = (_a = firebaseApp.options) === null || _a === void 0 ? void 0 : _a.apiKey;\r\n    if (!apiKey) {\r\n        throw ERROR_FACTORY.create(\"no api key\" /* ErrorCode.NO_API_KEY */);\r\n    }\r\n    return apiKey;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\r\n// These values will be used if the remote config object is successfully\r\n// retrieved, but the template does not have these fields.\r\nconst DEFAULT_CONFIGS = {\r\n    loggingEnabled: true\r\n};\r\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\r\nfunction getConfig(performanceController, iid) {\r\n    const config = getStoredConfig();\r\n    if (config) {\r\n        processConfig(config);\r\n        return Promise.resolve();\r\n    }\r\n    return getRemoteConfig(performanceController, iid)\r\n        .then(processConfig)\r\n        .then(config => storeConfig(config), \r\n    /** Do nothing for error, use defaults set in settings service. */\r\n    () => { });\r\n}\r\nfunction getStoredConfig() {\r\n    const localStorage = Api.getInstance().localStorage;\r\n    if (!localStorage) {\r\n        return;\r\n    }\r\n    const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\r\n    if (!expiryString || !configValid(expiryString)) {\r\n        return;\r\n    }\r\n    const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\r\n    if (!configStringified) {\r\n        return;\r\n    }\r\n    try {\r\n        const configResponse = JSON.parse(configStringified);\r\n        return configResponse;\r\n    }\r\n    catch (_a) {\r\n        return;\r\n    }\r\n}\r\nfunction storeConfig(config) {\r\n    const localStorage = Api.getInstance().localStorage;\r\n    if (!config || !localStorage) {\r\n        return;\r\n    }\r\n    localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\r\n    localStorage.setItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY, String(Date.now() +\r\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000));\r\n}\r\nconst COULD_NOT_GET_CONFIG_MSG = 'Could not fetch config, will use default configs';\r\nfunction getRemoteConfig(performanceController, iid) {\r\n    // Perf needs auth token only to retrieve remote config.\r\n    return getAuthTokenPromise(performanceController.installations)\r\n        .then(authToken => {\r\n        const projectId = getProjectId(performanceController.app);\r\n        const apiKey = getApiKey(performanceController.app);\r\n        const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\r\n        const request = new Request(configEndPoint, {\r\n            method: 'POST',\r\n            headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\r\n            /* eslint-disable camelcase */\r\n            body: JSON.stringify({\r\n                app_instance_id: iid,\r\n                app_instance_id_token: authToken,\r\n                app_id: getAppId(performanceController.app),\r\n                app_version: SDK_VERSION,\r\n                sdk_version: REMOTE_CONFIG_SDK_VERSION\r\n            })\r\n            /* eslint-enable camelcase */\r\n        });\r\n        return fetch(request).then(response => {\r\n            if (response.ok) {\r\n                return response.json();\r\n            }\r\n            // In case response is not ok. This will be caught by catch.\r\n            throw ERROR_FACTORY.create(\"RC response not ok\" /* ErrorCode.RC_NOT_OK */);\r\n        });\r\n    })\r\n        .catch(() => {\r\n        consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\r\n        return undefined;\r\n    });\r\n}\r\n/**\r\n * Processes config coming either from calling RC or from local storage.\r\n * This method only runs if call is successful or config in storage\r\n * is valid.\r\n */\r\nfunction processConfig(config) {\r\n    if (!config) {\r\n        return config;\r\n    }\r\n    const settingsServiceInstance = SettingsService.getInstance();\r\n    const entries = config.entries || {};\r\n    if (entries.fpr_enabled !== undefined) {\r\n        // TODO: Change the assignment of loggingEnabled once the received type is\r\n        // known.\r\n        settingsServiceInstance.loggingEnabled =\r\n            String(entries.fpr_enabled) === 'true';\r\n    }\r\n    else {\r\n        // Config retrieved successfully, but there is no fpr_enabled in template.\r\n        // Use secondary configs value.\r\n        settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\r\n    }\r\n    if (entries.fpr_log_source) {\r\n        settingsServiceInstance.logSource = Number(entries.fpr_log_source);\r\n    }\r\n    else if (DEFAULT_CONFIGS.logSource) {\r\n        settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\r\n    }\r\n    if (entries.fpr_log_endpoint_url) {\r\n        settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\r\n    }\r\n    else if (DEFAULT_CONFIGS.logEndPointUrl) {\r\n        settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\r\n    }\r\n    // Key from Remote Config has to be non-empty string, otherwise use local value.\r\n    if (entries.fpr_log_transport_key) {\r\n        settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\r\n    }\r\n    else if (DEFAULT_CONFIGS.transportKey) {\r\n        settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\r\n    }\r\n    if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\r\n        settingsServiceInstance.networkRequestsSamplingRate = Number(entries.fpr_vc_network_request_sampling_rate);\r\n    }\r\n    else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\r\n        settingsServiceInstance.networkRequestsSamplingRate =\r\n            DEFAULT_CONFIGS.networkRequestsSamplingRate;\r\n    }\r\n    if (entries.fpr_vc_trace_sampling_rate !== undefined) {\r\n        settingsServiceInstance.tracesSamplingRate = Number(entries.fpr_vc_trace_sampling_rate);\r\n    }\r\n    else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\r\n        settingsServiceInstance.tracesSamplingRate =\r\n            DEFAULT_CONFIGS.tracesSamplingRate;\r\n    }\r\n    // Set the per session trace and network logging flags.\r\n    settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(settingsServiceInstance.tracesSamplingRate);\r\n    settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(settingsServiceInstance.networkRequestsSamplingRate);\r\n    return config;\r\n}\r\nfunction configValid(expiry) {\r\n    return Number(expiry) > Date.now();\r\n}\r\nfunction shouldLogAfterSampling(samplingRate) {\r\n    return Math.random() <= samplingRate;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nlet initializationStatus = 1 /* InitializationStatus.notInitialized */;\r\nlet initializationPromise;\r\nfunction getInitializationPromise(performanceController) {\r\n    initializationStatus = 2 /* InitializationStatus.initializationPending */;\r\n    initializationPromise =\r\n        initializationPromise || initializePerf(performanceController);\r\n    return initializationPromise;\r\n}\r\nfunction isPerfInitialized() {\r\n    return initializationStatus === 3 /* InitializationStatus.initialized */;\r\n}\r\nfunction initializePerf(performanceController) {\r\n    return getDocumentReadyComplete()\r\n        .then(() => getIidPromise(performanceController.installations))\r\n        .then(iid => getConfig(performanceController, iid))\r\n        .then(() => changeInitializationStatus(), () => changeInitializationStatus());\r\n}\r\n/**\r\n * Returns a promise which resolves whenever the document readystate is complete or\r\n * immediately if it is called after page load complete.\r\n */\r\nfunction getDocumentReadyComplete() {\r\n    const document = Api.getInstance().document;\r\n    return new Promise(resolve => {\r\n        if (document && document.readyState !== 'complete') {\r\n            const handler = () => {\r\n                if (document.readyState === 'complete') {\r\n                    document.removeEventListener('readystatechange', handler);\r\n                    resolve();\r\n                }\r\n            };\r\n            document.addEventListener('readystatechange', handler);\r\n        }\r\n        else {\r\n            resolve();\r\n        }\r\n    });\r\n}\r\nfunction changeInitializationStatus() {\r\n    initializationStatus = 3 /* InitializationStatus.initialized */;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\r\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\r\n// If end point does not work, the call will be tried for these many times.\r\nconst DEFAULT_REMAINING_TRIES = 3;\r\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\r\nlet remainingTries = DEFAULT_REMAINING_TRIES;\r\n/* eslint-enable camelcase */\r\nlet queue = [];\r\nlet isTransportSetup = false;\r\nfunction setupTransportService() {\r\n    if (!isTransportSetup) {\r\n        processQueue(INITIAL_SEND_TIME_DELAY_MS);\r\n        isTransportSetup = true;\r\n    }\r\n}\r\nfunction processQueue(timeOffset) {\r\n    setTimeout(() => {\r\n        // If there is no remainingTries left, stop retrying.\r\n        if (remainingTries === 0) {\r\n            return;\r\n        }\r\n        // If there are no events to process, wait for DEFAULT_SEND_INTERVAL_MS and try again.\r\n        if (!queue.length) {\r\n            return processQueue(DEFAULT_SEND_INTERVAL_MS);\r\n        }\r\n        dispatchQueueEvents();\r\n    }, timeOffset);\r\n}\r\nfunction dispatchQueueEvents() {\r\n    // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\r\n    // The staged events will be used for current logRequest attempt, remaining events will be kept\r\n    // for next attempt.\r\n    const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\r\n    /* eslint-disable camelcase */\r\n    // We will pass the JSON serialized event to the backend.\r\n    const log_event = staged.map(evt => ({\r\n        source_extension_json_proto3: evt.message,\r\n        event_time_ms: String(evt.eventTime)\r\n    }));\r\n    const data = {\r\n        request_time_ms: String(Date.now()),\r\n        client_info: {\r\n            client_type: 1,\r\n            js_client_info: {}\r\n        },\r\n        log_source: SettingsService.getInstance().logSource,\r\n        log_event\r\n    };\r\n    /* eslint-enable camelcase */\r\n    sendEventsToFl(data, staged).catch(() => {\r\n        // If the request fails for some reason, add the events that were attempted\r\n        // back to the primary queue to retry later.\r\n        queue = [...staged, ...queue];\r\n        remainingTries--;\r\n        consoleLogger.info(`Tries left: ${remainingTries}.`);\r\n        processQueue(DEFAULT_SEND_INTERVAL_MS);\r\n    });\r\n}\r\nfunction sendEventsToFl(data, staged) {\r\n    return postToFlEndpoint(data)\r\n        .then(res => {\r\n        if (!res.ok) {\r\n            consoleLogger.info('Call to Firebase backend failed.');\r\n        }\r\n        return res.json();\r\n    })\r\n        .then(res => {\r\n        // Find the next call wait time from the response.\r\n        const transportWait = Number(res.nextRequestWaitMillis);\r\n        let requestOffset = DEFAULT_SEND_INTERVAL_MS;\r\n        if (!isNaN(transportWait)) {\r\n            requestOffset = Math.max(transportWait, requestOffset);\r\n        }\r\n        // Delete request if response include RESPONSE_ACTION_UNKNOWN or DELETE_REQUEST action.\r\n        // Otherwise, retry request using normal scheduling if response include RETRY_REQUEST_LATER.\r\n        const logResponseDetails = res.logResponseDetails;\r\n        if (Array.isArray(logResponseDetails) &&\r\n            logResponseDetails.length > 0 &&\r\n            logResponseDetails[0].responseAction === 'RETRY_REQUEST_LATER') {\r\n            queue = [...staged, ...queue];\r\n            consoleLogger.info(`Retry transport request later.`);\r\n        }\r\n        remainingTries = DEFAULT_REMAINING_TRIES;\r\n        // Schedule the next process.\r\n        processQueue(requestOffset);\r\n    });\r\n}\r\nfunction postToFlEndpoint(data) {\r\n    const flTransportFullUrl = SettingsService.getInstance().getFlTransportFullUrl();\r\n    return fetch(flTransportFullUrl, {\r\n        method: 'POST',\r\n        body: JSON.stringify(data)\r\n    });\r\n}\r\nfunction addToQueue(evt) {\r\n    if (!evt.eventTime || !evt.message) {\r\n        throw ERROR_FACTORY.create(\"invalid cc log\" /* ErrorCode.INVALID_CC_LOG */);\r\n    }\r\n    // Add the new event to the queue.\r\n    queue = [...queue, evt];\r\n}\r\n/** Log handler for cc service to send the performance logs to the server. */\r\nfunction transportHandler(\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nserializer) {\r\n    return (...args) => {\r\n        const message = serializer(...args);\r\n        addToQueue({\r\n            message,\r\n            eventTime: Date.now()\r\n        });\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nlet logger;\r\n// This method is not called before initialization.\r\nfunction sendLog(resource, resourceType) {\r\n    if (!logger) {\r\n        logger = transportHandler(serializer);\r\n    }\r\n    logger(resource, resourceType);\r\n}\r\nfunction logTrace(trace) {\r\n    const settingsService = SettingsService.getInstance();\r\n    // Do not log if trace is auto generated and instrumentation is disabled.\r\n    if (!settingsService.instrumentationEnabled && trace.isAuto) {\r\n        return;\r\n    }\r\n    // Do not log if trace is custom and data collection is disabled.\r\n    if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\r\n        return;\r\n    }\r\n    // Do not log if required apis are not available.\r\n    if (!Api.getInstance().requiredApisAvailable()) {\r\n        return;\r\n    }\r\n    // Only log the page load auto traces if page is visible.\r\n    if (trace.isAuto && getVisibilityState() !== VisibilityState.VISIBLE) {\r\n        return;\r\n    }\r\n    if (isPerfInitialized()) {\r\n        sendTraceLog(trace);\r\n    }\r\n    else {\r\n        // Custom traces can be used before the initialization but logging\r\n        // should wait until after.\r\n        getInitializationPromise(trace.performanceController).then(() => sendTraceLog(trace), () => sendTraceLog(trace));\r\n    }\r\n}\r\nfunction sendTraceLog(trace) {\r\n    if (!getIid()) {\r\n        return;\r\n    }\r\n    const settingsService = SettingsService.getInstance();\r\n    if (!settingsService.loggingEnabled ||\r\n        !settingsService.logTraceAfterSampling) {\r\n        return;\r\n    }\r\n    setTimeout(() => sendLog(trace, 1 /* ResourceType.Trace */), 0);\r\n}\r\nfunction logNetworkRequest(networkRequest) {\r\n    const settingsService = SettingsService.getInstance();\r\n    // Do not log network requests if instrumentation is disabled.\r\n    if (!settingsService.instrumentationEnabled) {\r\n        return;\r\n    }\r\n    // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\r\n    // Need to blacklist both old and new endpoints to avoid migration gap.\r\n    const networkRequestUrl = networkRequest.url;\r\n    // Blacklist old log endpoint and new transport endpoint.\r\n    // Because Performance SDK doesn't instrument requests sent from SDK itself.\r\n    const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\r\n    const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\r\n    if (networkRequestUrl === logEndpointUrl ||\r\n        networkRequestUrl === flEndpointUrl) {\r\n        return;\r\n    }\r\n    if (!settingsService.loggingEnabled ||\r\n        !settingsService.logNetworkAfterSampling) {\r\n        return;\r\n    }\r\n    setTimeout(() => sendLog(networkRequest, 0 /* ResourceType.NetworkRequest */), 0);\r\n}\r\nfunction serializer(resource, resourceType) {\r\n    if (resourceType === 0 /* ResourceType.NetworkRequest */) {\r\n        return serializeNetworkRequest(resource);\r\n    }\r\n    return serializeTrace(resource);\r\n}\r\nfunction serializeNetworkRequest(networkRequest) {\r\n    const networkRequestMetric = {\r\n        url: networkRequest.url,\r\n        http_method: networkRequest.httpMethod || 0,\r\n        http_response_code: 200,\r\n        response_payload_bytes: networkRequest.responsePayloadBytes,\r\n        client_start_time_us: networkRequest.startTimeUs,\r\n        time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\r\n        time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\r\n    };\r\n    const perfMetric = {\r\n        application_info: getApplicationInfo(networkRequest.performanceController.app),\r\n        network_request_metric: networkRequestMetric\r\n    };\r\n    return JSON.stringify(perfMetric);\r\n}\r\nfunction serializeTrace(trace) {\r\n    const traceMetric = {\r\n        name: trace.name,\r\n        is_auto: trace.isAuto,\r\n        client_start_time_us: trace.startTimeUs,\r\n        duration_us: trace.durationUs\r\n    };\r\n    if (Object.keys(trace.counters).length !== 0) {\r\n        traceMetric.counters = trace.counters;\r\n    }\r\n    const customAttributes = trace.getAttributes();\r\n    if (Object.keys(customAttributes).length !== 0) {\r\n        traceMetric.custom_attributes = customAttributes;\r\n    }\r\n    const perfMetric = {\r\n        application_info: getApplicationInfo(trace.performanceController.app),\r\n        trace_metric: traceMetric\r\n    };\r\n    return JSON.stringify(perfMetric);\r\n}\r\nfunction getApplicationInfo(firebaseApp) {\r\n    return {\r\n        google_app_id: getAppId(firebaseApp),\r\n        app_instance_id: getIid(),\r\n        web_app_info: {\r\n            sdk_version: SDK_VERSION,\r\n            page_url: Api.getInstance().getUrl(),\r\n            service_worker_status: getServiceWorkerStatus(),\r\n            visibility_state: getVisibilityState(),\r\n            effective_connection_type: getEffectiveConnectionType()\r\n        },\r\n        application_process_state: 0\r\n    };\r\n}\r\n/* eslint-enable camelcase */\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst MAX_METRIC_NAME_LENGTH = 100;\r\nconst RESERVED_AUTO_PREFIX = '_';\r\nconst oobMetrics = [\r\n    FIRST_PAINT_COUNTER_NAME,\r\n    FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\r\n    FIRST_INPUT_DELAY_COUNTER_NAME\r\n];\r\n/**\r\n * Returns true if the metric is custom and does not start with reserved prefix, or if\r\n * the metric is one of out of the box page load trace metrics.\r\n */\r\nfunction isValidMetricName(name, traceName) {\r\n    if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\r\n        return false;\r\n    }\r\n    return ((traceName &&\r\n        traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\r\n        oobMetrics.indexOf(name) > -1) ||\r\n        !name.startsWith(RESERVED_AUTO_PREFIX));\r\n}\r\n/**\r\n * Converts the provided value to an integer value to be used in case of a metric.\r\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\r\n *\r\n * @returns Converted integer number to be set for the metric.\r\n */\r\nfunction convertMetricValueToInteger(providedValue) {\r\n    const valueAsInteger = Math.floor(providedValue);\r\n    if (valueAsInteger < providedValue) {\r\n        consoleLogger.info(`Metric value should be an Integer, setting the value as : ${valueAsInteger}.`);\r\n    }\r\n    return valueAsInteger;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass Trace {\r\n    /**\r\n     * @param performanceController The performance controller running.\r\n     * @param name The name of the trace.\r\n     * @param isAuto If the trace is auto-instrumented.\r\n     * @param traceMeasureName The name of the measure marker in user timing specification. This field\r\n     * is only set when the trace is built for logging when the user directly uses the user timing\r\n     * api (performance.mark and performance.measure).\r\n     */\r\n    constructor(performanceController, name, isAuto = false, traceMeasureName) {\r\n        this.performanceController = performanceController;\r\n        this.name = name;\r\n        this.isAuto = isAuto;\r\n        this.state = 1 /* TraceState.UNINITIALIZED */;\r\n        this.customAttributes = {};\r\n        this.counters = {};\r\n        this.api = Api.getInstance();\r\n        this.randomId = Math.floor(Math.random() * 1000000);\r\n        if (!this.isAuto) {\r\n            this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\r\n            this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\r\n            this.traceMeasure =\r\n                traceMeasureName ||\r\n                    `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\r\n            if (traceMeasureName) {\r\n                // For the case of direct user timing traces, no start stop will happen. The measure object\r\n                // is already available.\r\n                this.calculateTraceMetrics();\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Starts a trace. The measurement of the duration starts at this point.\r\n     */\r\n    start() {\r\n        if (this.state !== 1 /* TraceState.UNINITIALIZED */) {\r\n            throw ERROR_FACTORY.create(\"trace started\" /* ErrorCode.TRACE_STARTED_BEFORE */, {\r\n                traceName: this.name\r\n            });\r\n        }\r\n        this.api.mark(this.traceStartMark);\r\n        this.state = 2 /* TraceState.RUNNING */;\r\n    }\r\n    /**\r\n     * Stops the trace. The measurement of the duration of the trace stops at this point and trace\r\n     * is logged.\r\n     */\r\n    stop() {\r\n        if (this.state !== 2 /* TraceState.RUNNING */) {\r\n            throw ERROR_FACTORY.create(\"trace stopped\" /* ErrorCode.TRACE_STOPPED_BEFORE */, {\r\n                traceName: this.name\r\n            });\r\n        }\r\n        this.state = 3 /* TraceState.TERMINATED */;\r\n        this.api.mark(this.traceStopMark);\r\n        this.api.measure(this.traceMeasure, this.traceStartMark, this.traceStopMark);\r\n        this.calculateTraceMetrics();\r\n        logTrace(this);\r\n    }\r\n    /**\r\n     * Records a trace with predetermined values. If this method is used a trace is created and logged\r\n     * directly. No need to use start and stop methods.\r\n     * @param startTime Trace start time since epoch in millisec\r\n     * @param duration The duration of the trace in millisec\r\n     * @param options An object which can optionally hold maps of custom metrics and custom attributes\r\n     */\r\n    record(startTime, duration, options) {\r\n        if (startTime <= 0) {\r\n            throw ERROR_FACTORY.create(\"nonpositive trace startTime\" /* ErrorCode.NONPOSITIVE_TRACE_START_TIME */, {\r\n                traceName: this.name\r\n            });\r\n        }\r\n        if (duration <= 0) {\r\n            throw ERROR_FACTORY.create(\"nonpositive trace duration\" /* ErrorCode.NONPOSITIVE_TRACE_DURATION */, {\r\n                traceName: this.name\r\n            });\r\n        }\r\n        this.durationUs = Math.floor(duration * 1000);\r\n        this.startTimeUs = Math.floor(startTime * 1000);\r\n        if (options && options.attributes) {\r\n            this.customAttributes = Object.assign({}, options.attributes);\r\n        }\r\n        if (options && options.metrics) {\r\n            for (const metricName of Object.keys(options.metrics)) {\r\n                if (!isNaN(Number(options.metrics[metricName]))) {\r\n                    this.counters[metricName] = Math.floor(Number(options.metrics[metricName]));\r\n                }\r\n            }\r\n        }\r\n        logTrace(this);\r\n    }\r\n    /**\r\n     * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\r\n     * custom metric if one with the given name does not exist. The value will be floored down to an\r\n     * integer.\r\n     * @param counter Name of the custom metric\r\n     * @param numAsInteger Increment by value\r\n     */\r\n    incrementMetric(counter, numAsInteger = 1) {\r\n        if (this.counters[counter] === undefined) {\r\n            this.putMetric(counter, numAsInteger);\r\n        }\r\n        else {\r\n            this.putMetric(counter, this.counters[counter] + numAsInteger);\r\n        }\r\n    }\r\n    /**\r\n     * Sets a custom metric to a specified value. Will create a new custom metric if one with the\r\n     * given name does not exist. The value will be floored down to an integer.\r\n     * @param counter Name of the custom metric\r\n     * @param numAsInteger Set custom metric to this value\r\n     */\r\n    putMetric(counter, numAsInteger) {\r\n        if (isValidMetricName(counter, this.name)) {\r\n            this.counters[counter] = convertMetricValueToInteger(numAsInteger !== null && numAsInteger !== void 0 ? numAsInteger : 0);\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"invalid custom metric name\" /* ErrorCode.INVALID_CUSTOM_METRIC_NAME */, {\r\n                customMetricName: counter\r\n            });\r\n        }\r\n    }\r\n    /**\r\n     * Returns the value of the custom metric by that name. If a custom metric with that name does\r\n     * not exist will return zero.\r\n     * @param counter\r\n     */\r\n    getMetric(counter) {\r\n        return this.counters[counter] || 0;\r\n    }\r\n    /**\r\n     * Sets a custom attribute of a trace to a certain value.\r\n     * @param attr\r\n     * @param value\r\n     */\r\n    putAttribute(attr, value) {\r\n        const isValidName = isValidCustomAttributeName(attr);\r\n        const isValidValue = isValidCustomAttributeValue(value);\r\n        if (isValidName && isValidValue) {\r\n            this.customAttributes[attr] = value;\r\n            return;\r\n        }\r\n        // Throw appropriate error when the attribute name or value is invalid.\r\n        if (!isValidName) {\r\n            throw ERROR_FACTORY.create(\"invalid attribute name\" /* ErrorCode.INVALID_ATTRIBUTE_NAME */, {\r\n                attributeName: attr\r\n            });\r\n        }\r\n        if (!isValidValue) {\r\n            throw ERROR_FACTORY.create(\"invalid attribute value\" /* ErrorCode.INVALID_ATTRIBUTE_VALUE */, {\r\n                attributeValue: value\r\n            });\r\n        }\r\n    }\r\n    /**\r\n     * Retrieves the value a custom attribute of a trace is set to.\r\n     * @param attr\r\n     */\r\n    getAttribute(attr) {\r\n        return this.customAttributes[attr];\r\n    }\r\n    removeAttribute(attr) {\r\n        if (this.customAttributes[attr] === undefined) {\r\n            return;\r\n        }\r\n        delete this.customAttributes[attr];\r\n    }\r\n    getAttributes() {\r\n        return Object.assign({}, this.customAttributes);\r\n    }\r\n    setStartTime(startTime) {\r\n        this.startTimeUs = startTime;\r\n    }\r\n    setDuration(duration) {\r\n        this.durationUs = duration;\r\n    }\r\n    /**\r\n     * Calculates and assigns the duration and start time of the trace using the measure performance\r\n     * entry.\r\n     */\r\n    calculateTraceMetrics() {\r\n        const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\r\n        const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\r\n        if (perfMeasureEntry) {\r\n            this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\r\n            this.startTimeUs = Math.floor((perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000);\r\n        }\r\n    }\r\n    /**\r\n     * @param navigationTimings A single element array which contains the navigationTIming object of\r\n     * the page load\r\n     * @param paintTimings A array which contains paintTiming object of the page load\r\n     * @param firstInputDelay First input delay in millisec\r\n     */\r\n    static createOobTrace(performanceController, navigationTimings, paintTimings, firstInputDelay) {\r\n        const route = Api.getInstance().getUrl();\r\n        if (!route) {\r\n            return;\r\n        }\r\n        const trace = new Trace(performanceController, OOB_TRACE_PAGE_LOAD_PREFIX + route, true);\r\n        const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\r\n        trace.setStartTime(timeOriginUs);\r\n        // navigationTimings includes only one element.\r\n        if (navigationTimings && navigationTimings[0]) {\r\n            trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\r\n            trace.putMetric('domInteractive', Math.floor(navigationTimings[0].domInteractive * 1000));\r\n            trace.putMetric('domContentLoadedEventEnd', Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000));\r\n            trace.putMetric('loadEventEnd', Math.floor(navigationTimings[0].loadEventEnd * 1000));\r\n        }\r\n        const FIRST_PAINT = 'first-paint';\r\n        const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\r\n        if (paintTimings) {\r\n            const firstPaint = paintTimings.find(paintObject => paintObject.name === FIRST_PAINT);\r\n            if (firstPaint && firstPaint.startTime) {\r\n                trace.putMetric(FIRST_PAINT_COUNTER_NAME, Math.floor(firstPaint.startTime * 1000));\r\n            }\r\n            const firstContentfulPaint = paintTimings.find(paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT);\r\n            if (firstContentfulPaint && firstContentfulPaint.startTime) {\r\n                trace.putMetric(FIRST_CONTENTFUL_PAINT_COUNTER_NAME, Math.floor(firstContentfulPaint.startTime * 1000));\r\n            }\r\n            if (firstInputDelay) {\r\n                trace.putMetric(FIRST_INPUT_DELAY_COUNTER_NAME, Math.floor(firstInputDelay * 1000));\r\n            }\r\n        }\r\n        logTrace(trace);\r\n    }\r\n    static createUserTimingTrace(performanceController, measureName) {\r\n        const trace = new Trace(performanceController, measureName, false, measureName);\r\n        logTrace(trace);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction createNetworkRequestEntry(performanceController, entry) {\r\n    const performanceEntry = entry;\r\n    if (!performanceEntry || performanceEntry.responseStart === undefined) {\r\n        return;\r\n    }\r\n    const timeOrigin = Api.getInstance().getTimeOrigin();\r\n    const startTimeUs = Math.floor((performanceEntry.startTime + timeOrigin) * 1000);\r\n    const timeToResponseInitiatedUs = performanceEntry.responseStart\r\n        ? Math.floor((performanceEntry.responseStart - performanceEntry.startTime) * 1000)\r\n        : undefined;\r\n    const timeToResponseCompletedUs = Math.floor((performanceEntry.responseEnd - performanceEntry.startTime) * 1000);\r\n    // Remove the query params from logged network request url.\r\n    const url = performanceEntry.name && performanceEntry.name.split('?')[0];\r\n    const networkRequest = {\r\n        performanceController,\r\n        url,\r\n        responsePayloadBytes: performanceEntry.transferSize,\r\n        startTimeUs,\r\n        timeToResponseInitiatedUs,\r\n        timeToResponseCompletedUs\r\n    };\r\n    logNetworkRequest(networkRequest);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst FID_WAIT_TIME_MS = 5000;\r\nfunction setupOobResources(performanceController) {\r\n    // Do not initialize unless iid is available.\r\n    if (!getIid()) {\r\n        return;\r\n    }\r\n    // The load event might not have fired yet, and that means performance navigation timing\r\n    // object has a duration of 0. The setup should run after all current tasks in js queue.\r\n    setTimeout(() => setupOobTraces(performanceController), 0);\r\n    setTimeout(() => setupNetworkRequests(performanceController), 0);\r\n    setTimeout(() => setupUserTimingTraces(performanceController), 0);\r\n}\r\nfunction setupNetworkRequests(performanceController) {\r\n    const api = Api.getInstance();\r\n    const resources = api.getEntriesByType('resource');\r\n    for (const resource of resources) {\r\n        createNetworkRequestEntry(performanceController, resource);\r\n    }\r\n    api.setupObserver('resource', entry => createNetworkRequestEntry(performanceController, entry));\r\n}\r\nfunction setupOobTraces(performanceController) {\r\n    const api = Api.getInstance();\r\n    const navigationTimings = api.getEntriesByType('navigation');\r\n    const paintTimings = api.getEntriesByType('paint');\r\n    // If First Input Delay polyfill is added to the page, report the fid value.\r\n    // https://github.com/GoogleChromeLabs/first-input-delay\r\n    if (api.onFirstInputDelay) {\r\n        // If the fid call back is not called for certain time, continue without it.\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        let timeoutId = setTimeout(() => {\r\n            Trace.createOobTrace(performanceController, navigationTimings, paintTimings);\r\n            timeoutId = undefined;\r\n        }, FID_WAIT_TIME_MS);\r\n        api.onFirstInputDelay((fid) => {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n                Trace.createOobTrace(performanceController, navigationTimings, paintTimings, fid);\r\n            }\r\n        });\r\n    }\r\n    else {\r\n        Trace.createOobTrace(performanceController, navigationTimings, paintTimings);\r\n    }\r\n}\r\nfunction setupUserTimingTraces(performanceController) {\r\n    const api = Api.getInstance();\r\n    // Run through the measure performance entries collected up to this point.\r\n    const measures = api.getEntriesByType('measure');\r\n    for (const measure of measures) {\r\n        createUserTimingTrace(performanceController, measure);\r\n    }\r\n    // Setup an observer to capture the measures from this point on.\r\n    api.setupObserver('measure', entry => createUserTimingTrace(performanceController, entry));\r\n}\r\nfunction createUserTimingTrace(performanceController, measure) {\r\n    const measureName = measure.name;\r\n    // Do not create a trace, if the user timing marks and measures are created by the sdk itself.\r\n    if (measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\r\n        TRACE_MEASURE_PREFIX) {\r\n        return;\r\n    }\r\n    Trace.createUserTimingTrace(performanceController, measureName);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass PerformanceController {\r\n    constructor(app, installations) {\r\n        this.app = app;\r\n        this.installations = installations;\r\n        this.initialized = false;\r\n    }\r\n    /**\r\n     * This method *must* be called internally as part of creating a\r\n     * PerformanceController instance.\r\n     *\r\n     * Currently it's not possible to pass the settings object through the\r\n     * constructor using Components, so this method exists to be called with the\r\n     * desired settings, to ensure nothing is collected without the user's\r\n     * consent.\r\n     */\r\n    _init(settings) {\r\n        if (this.initialized) {\r\n            return;\r\n        }\r\n        if ((settings === null || settings === void 0 ? void 0 : settings.dataCollectionEnabled) !== undefined) {\r\n            this.dataCollectionEnabled = settings.dataCollectionEnabled;\r\n        }\r\n        if ((settings === null || settings === void 0 ? void 0 : settings.instrumentationEnabled) !== undefined) {\r\n            this.instrumentationEnabled = settings.instrumentationEnabled;\r\n        }\r\n        if (Api.getInstance().requiredApisAvailable()) {\r\n            validateIndexedDBOpenable()\r\n                .then(isAvailable => {\r\n                if (isAvailable) {\r\n                    setupTransportService();\r\n                    getInitializationPromise(this).then(() => setupOobResources(this), () => setupOobResources(this));\r\n                    this.initialized = true;\r\n                }\r\n            })\r\n                .catch(error => {\r\n                consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\r\n            });\r\n        }\r\n        else {\r\n            consoleLogger.info('Firebase Performance cannot start if the browser does not support ' +\r\n                '\"Fetch\" and \"Promise\", or cookies are disabled.');\r\n        }\r\n    }\r\n    set instrumentationEnabled(val) {\r\n        SettingsService.getInstance().instrumentationEnabled = val;\r\n    }\r\n    get instrumentationEnabled() {\r\n        return SettingsService.getInstance().instrumentationEnabled;\r\n    }\r\n    set dataCollectionEnabled(val) {\r\n        SettingsService.getInstance().dataCollectionEnabled = val;\r\n    }\r\n    get dataCollectionEnabled() {\r\n        return SettingsService.getInstance().dataCollectionEnabled;\r\n    }\r\n}\n\n/**\r\n * The Firebase Performance Monitoring Web SDK.\r\n * This SDK does not work in a Node.js environment.\r\n *\r\n * @packageDocumentation\r\n */\r\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\r\n/**\r\n * Returns a {@link FirebasePerformance} instance for the given app.\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n * @public\r\n */\r\nfunction getPerformance(app = getApp()) {\r\n    app = getModularInstance(app);\r\n    const provider = _getProvider(app, 'performance');\r\n    const perfInstance = provider.getImmediate();\r\n    return perfInstance;\r\n}\r\n/**\r\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\r\n * @public\r\n */\r\nfunction initializePerformance(app, settings) {\r\n    app = getModularInstance(app);\r\n    const provider = _getProvider(app, 'performance');\r\n    // throw if an instance was already created.\r\n    // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\r\n    if (provider.isInitialized()) {\r\n        const existingInstance = provider.getImmediate();\r\n        const initialSettings = provider.getOptions();\r\n        if (deepEqual(initialSettings, settings !== null && settings !== void 0 ? settings : {})) {\r\n            return existingInstance;\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"already initialized\" /* ErrorCode.ALREADY_INITIALIZED */);\r\n        }\r\n    }\r\n    const perfInstance = provider.initialize({\r\n        options: settings\r\n    });\r\n    return perfInstance;\r\n}\r\n/**\r\n * Returns a new `PerformanceTrace` instance.\r\n * @param performance - The {@link FirebasePerformance} instance to use.\r\n * @param name - The name of the trace.\r\n * @public\r\n */\r\nfunction trace(performance, name) {\r\n    performance = getModularInstance(performance);\r\n    return new Trace(performance, name);\r\n}\r\nconst factory = (container, { options: settings }) => {\r\n    // Dependencies\r\n    const app = container.getProvider('app').getImmediate();\r\n    const installations = container\r\n        .getProvider('installations-internal')\r\n        .getImmediate();\r\n    if (app.name !== DEFAULT_ENTRY_NAME) {\r\n        throw ERROR_FACTORY.create(\"FB not default\" /* ErrorCode.FB_NOT_DEFAULT */);\r\n    }\r\n    if (typeof window === 'undefined') {\r\n        throw ERROR_FACTORY.create(\"no window\" /* ErrorCode.NO_WINDOW */);\r\n    }\r\n    setupApi(window);\r\n    const perfInstance = new PerformanceController(app, installations);\r\n    perfInstance._init(settings);\r\n    return perfInstance;\r\n};\r\nfunction registerPerformance() {\r\n    _registerComponent(new Component('performance', factory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    registerVersion(name, version);\r\n    // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\n    registerVersion(name, version, 'esm2017');\r\n}\r\nregisterPerformance();\n\nexport { getPerformance, initializePerformance, trace };\n", "import '@firebase/performance-compat';\n"], "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "Component", "ErrorFactory", "FirebaseError", "openDB", "name", "version", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "SERVICE", "SERVICE_NAME", "ERROR_DESCRIPTION_MAP", "ERROR_FACTORY", "isServerError", "error", "code", "includes", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "getExpiresInFromResponseExpiresIn", "creationTime", "Date", "now", "getErrorFromResponse", "_x", "_x2", "_getErrorFromResponse", "apply", "arguments", "_asyncToGenerator", "requestName", "responseJson", "json", "errorData", "create", "serverCode", "serverMessage", "message", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "_x3", "_retryIfServerError", "fn", "result", "responseExpiresIn", "Number", "replace", "createInstallationRequest", "_x4", "_x5", "_createInstallationRequest", "heartbeatServiceProvider", "fid", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "appId", "sdkVersion", "request", "method", "JSON", "stringify", "fetch", "ok", "responseValue", "registeredInstallationEntry", "registrationStatus", "authToken", "sleep", "ms", "Promise", "resolve", "setTimeout", "bufferToBase64UrlSafe", "array", "b64", "btoa", "String", "fromCharCode", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "crypto", "self", "msCrypto", "getRandomValues", "encode", "test", "_a", "b64String", "substr", "<PERSON><PERSON><PERSON>", "appName", "fidChangeCallbacks", "Map", "fidChanged", "key", "callFidChangeCallbacks", "broadcastFidChange", "addCallback", "callback", "getBroadcastChannel", "callbackSet", "get", "Set", "set", "add", "removeCallback", "delete", "size", "closeBroadcastChannel", "callbacks", "channel", "postMessage", "broadcastChannel", "BroadcastChannel", "onmessage", "e", "data", "close", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "_x6", "_x7", "_set", "value", "tx", "transaction", "objectStore", "oldValue", "put", "done", "remove", "_x8", "_remove", "update", "_x9", "_x0", "_update", "updateFn", "store", "newValue", "undefined", "getInstallationEntry", "_x1", "_getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entryWithPromise", "triggerRegistrationIfNecessary", "entry", "clearTimedOutRequest", "navigator", "onLine", "registrationPromiseWithError", "reject", "inProgressEntry", "registrationTime", "registerInstallation", "waitUntilFidRegistration", "_x10", "_x11", "_registerInstallation", "customData", "_x12", "_waitUntilFidRegistration", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "_x13", "_x14", "_generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "completedAuthToken", "refreshAuthToken", "_x15", "_refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "waitUntilAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "fetchAuthTokenFromServer", "_x16", "_x17", "_waitUntilAuthTokenRequest", "updateAuthTokenRequest", "hasAuthTokenRequestTimedOut", "Object", "assign", "_x18", "_x19", "_fetchAuthTokenFromServer", "updatedInstallationEntry", "isAuthTokenExpired", "inProgressAuthToken", "requestTime", "getId", "_x20", "_getId", "installationsImpl", "catch", "console", "getToken", "_x21", "_getToken", "completeInstallationRegistration", "_x22", "_completeInstallationRegistration", "deleteInstallationRequest", "_x23", "_x24", "_deleteInstallationRequest", "getDeleteEndpoint", "deleteInstallations", "_x25", "_deleteInstallations", "onIdChange", "getInstallations", "app", "extractAppConfig", "options", "getMissingValueError", "config<PERSON><PERSON><PERSON>", "keyName", "valueName", "INSTALLATIONS_NAME", "INSTALLATIONS_NAME_INTERNAL", "publicFactory", "container", "get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "installationsInternal", "registerInstallations", "firebase", "trace", "PerformanceCompatImpl", "constructor", "_delegate", "instrumentationEnabled", "val", "dataCollectionEnabled", "traceName", "registerPerformanceCompat", "firebaseInstance", "INTERNAL", "registerComponent", "performanceFactory", "performance", "areCookiesEnabled", "isIndexedDBAvailable", "validateIndexedDBOpenable", "getModularInstance", "deepEqual", "<PERSON><PERSON>", "LogLevel", "SDK_VERSION", "TRACE_START_MARK_PREFIX", "TRACE_STOP_MARK_PREFIX", "TRACE_MEASURE_PREFIX", "OOB_TRACE_PAGE_LOAD_PREFIX", "FIRST_PAINT_COUNTER_NAME", "FIRST_CONTENTFUL_PAINT_COUNTER_NAME", "FIRST_INPUT_DELAY_COUNTER_NAME", "CONFIG_LOCAL_STORAGE_KEY", "CONFIG_EXPIRY_LOCAL_STORAGE_KEY", "consoleLogger", "logLevel", "INFO", "apiInstance", "windowInstance", "Api", "window", "PerformanceObserver", "windowLocation", "location", "document", "cookieEnabled", "localStorage", "perfMetrics", "onFirstInputDelay", "getUrl", "href", "split", "mark", "measure", "measureName", "mark1", "mark2", "getEntriesByType", "type", "getEntriesByName", "getTime<PERSON>rigin", "<PERSON><PERSON><PERSON><PERSON>", "timing", "navigationStart", "requiredApisAvailable", "info", "setupObserver", "entryType", "observer", "list", "getEntries", "observe", "entryTypes", "getInstance", "setupApi", "iid", "getIidPromise", "installationsService", "iidPromise", "then", "iidVal", "getIid", "getAuthTokenPromise", "authTokenPromise", "authTokenVal", "mergeStrings", "part1", "part2", "sizeDiff", "length", "resultArray", "i", "push", "char<PERSON>t", "join", "settingsServiceInstance", "SettingsService", "loggingEnabled", "tracesSamplingRate", "networkRequestsSamplingRate", "logEndPointUrl", "flTransportEndpointUrl", "transportKey", "logSource", "logTraceAfterSampling", "logNetworkAfterSampling", "configTimeToLive", "getFlTransportFullUrl", "concat", "VisibilityState", "RESERVED_ATTRIBUTE_PREFIXES", "ATTRIBUTE_FORMAT_REGEX", "RegExp", "MAX_ATTRIBUTE_NAME_LENGTH", "MAX_ATTRIBUTE_VALUE_LENGTH", "getServiceWorkerStatus", "serviceWorker", "controller", "getVisibilityState", "visibilityState", "VISIBLE", "HIDDEN", "UNKNOWN", "getEffectiveConnectionType", "navigatorConnection", "connection", "effectiveType", "isValidCustomAttributeName", "matchesReservedPrefix", "some", "prefix", "startsWith", "match", "isValidCustomAttributeValue", "getAppId", "firebaseApp", "getProjectId", "getApi<PERSON>ey", "REMOTE_CONFIG_SDK_VERSION", "DEFAULT_CONFIGS", "FIS_AUTH_PREFIX", "getConfig", "performanceController", "config", "getStoredConfig", "processConfig", "getRemoteConfig", "storeConfig", "expiryString", "getItem", "config<PERSON><PERSON><PERSON>", "configStringified", "configResponse", "parse", "setItem", "COULD_NOT_GET_CONFIG_MSG", "configEndPoint", "Request", "Authorization", "app_instance_id", "app_instance_id_token", "app_id", "app_version", "sdk_version", "entries", "fpr_enabled", "fpr_log_source", "fpr_log_endpoint_url", "fpr_log_transport_key", "fpr_vc_network_request_sampling_rate", "fpr_vc_trace_sampling_rate", "shouldLogAfterSampling", "expiry", "samplingRate", "Math", "random", "initializationStatus", "initializationPromise", "getInitializationPromise", "initializePerf", "isPerfInitialized", "getDocumentReadyComplete", "changeInitializationStatus", "readyState", "handler", "removeEventListener", "addEventListener", "DEFAULT_SEND_INTERVAL_MS", "INITIAL_SEND_TIME_DELAY_MS", "DEFAULT_REMAINING_TRIES", "MAX_EVENT_COUNT_PER_REQUEST", "remainingTries", "queue", "isTransportSetup", "setupTransportService", "processQueue", "timeOffset", "dispatchQueueEvents", "staged", "splice", "log_event", "map", "evt", "source_extension_json_proto3", "event_time_ms", "eventTime", "request_time_ms", "client_info", "client_type", "js_client_info", "log_source", "sendEventsToFl", "postToFlEndpoint", "res", "transportWait", "nextRequestWaitMillis", "requestOffset", "isNaN", "max", "logResponseDetails", "Array", "isArray", "responseAction", "flTransportFullUrl", "addToQueue", "transportHandler", "serializer", "args", "logger", "sendLog", "resource", "resourceType", "logTrace", "settingsService", "isAuto", "sendTraceLog", "logNetworkRequest", "networkRequest", "networkRequestUrl", "url", "logEndpointUrl", "flEndpointUrl", "serializeNetworkRequest", "serializeTrace", "networkRequestMetric", "http_method", "httpMethod", "http_response_code", "response_payload_bytes", "responsePayloadBytes", "client_start_time_us", "startTimeUs", "time_to_response_initiated_us", "timeToResponseInitiatedUs", "time_to_response_completed_us", "timeToResponseCompletedUs", "perfMetric", "application_info", "getApplicationInfo", "network_request_metric", "traceMetric", "is_auto", "duration_us", "durationUs", "keys", "counters", "customAttributes", "getAttributes", "custom_attributes", "trace_metric", "google_app_id", "web_app_info", "page_url", "service_worker_status", "visibility_state", "effective_connection_type", "application_process_state", "MAX_METRIC_NAME_LENGTH", "RESERVED_AUTO_PREFIX", "oobMetrics", "isValidMetricName", "indexOf", "convertMetricValueToInteger", "providedValue", "valueAsInteger", "floor", "Trace", "traceMeasureName", "state", "api", "randomId", "traceStartMark", "traceStopMark", "traceMeasure", "calculateTraceMetrics", "start", "stop", "record", "startTime", "duration", "attributes", "metrics", "metricName", "incrementMetric", "counter", "numAsInteger", "putMetric", "customMetricName", "getMetric", "putAttribute", "attr", "isValidName", "isValidValue", "attributeName", "attributeValue", "getAttribute", "removeAttribute", "setStartTime", "setDuration", "perfMeasureEntries", "perfMeasureEntry", "createOobTrace", "navigationTimings", "paintTimings", "firstInputDelay", "route", "timeOriginUs", "domInteractive", "domContentLoadedEventEnd", "loadEventEnd", "FIRST_PAINT", "FIRST_CONTENTFUL_PAINT", "<PERSON><PERSON><PERSON><PERSON>", "find", "paintObject", "firstContentful<PERSON><PERSON>t", "createUserTimingTrace", "createNetworkRequestEntry", "performanceEntry", "responseStart", "responseEnd", "transferSize", "FID_WAIT_TIME_MS", "setupOobResources", "setupOobTraces", "setupNetworkRequests", "setupUserTimingTraces", "resources", "timeoutId", "clearTimeout", "measures", "substring", "PerformanceController", "initialized", "_init", "settings", "isAvailable", "DEFAULT_ENTRY_NAME", "getPerformance", "provider", "perfInstance", "initializePerformance", "isInitialized", "existingInstance", "initialSettings", "getOptions", "initialize", "factory", "registerPerformance"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3]}