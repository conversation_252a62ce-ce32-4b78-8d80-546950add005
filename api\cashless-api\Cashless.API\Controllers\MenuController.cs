using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Menus;
using Schools.BLL.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Schools.DAL.Entities;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class MenuController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMenuValidator _menuValidator;
    private readonly IUserValidator _userValidator;
    private readonly IMenuService _menuService;
    private readonly ITelemetryService _telemetryService;

    public MenuController(ITelemetryService telemetryService, IUnitOfWork unitOfWork, IMenuValidator menuValidator, IMenuService menuService, IUserValidator userValidator)
    {
        _telemetryService = telemetryService;
        _unitOfWork = unitOfWork;
        _menuValidator = menuValidator;
        _userValidator = userValidator;
        _menuService = menuService;
    }

    [Route("GetMenuList/{schoolId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetMenuList(long schoolId)
    {
        ListMenus listMenus = new();

        if (schoolId < 1) throw new ValidationException("schoolId", "Invalid request");

        await _menuValidator.ValidateAccessToSchool(schoolId);

        listMenus.Menus = await _unitOfWork.MenuRepository.GetMenuList(schoolId);

        return new OkObjectResult(listMenus);
    }

    [Route("GetMenuNamesList/{schoolId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetMenuNamesList(long schoolId)
    {
        if (schoolId < 1) throw new ValidationException("schoolId", "Invalid request");

        await _menuValidator.ValidateAccessToSchool(schoolId);

        var res = await _menuService.GetMenusNameBySchoolId(schoolId);

        return new OkObjectResult(res);
    }

    [Route("GetConfiguredMenuList/{merchantId}/{schoolId}")]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> GetConfiguredMenuList(int merchantId, int schoolId)
    {
        if (merchantId <= 0) throw new ValidationException("merchantId", "Invalid request");
        if (schoolId <= 0) throw new ValidationException("schoolId", "Invalid request");

        await _menuValidator.ValidateAccessToSchool(schoolId);

        ListMenus listMenus = new()
        {
            Menus = await _unitOfWork.MenuRepository.GetConfiguredMenuList(merchantId, schoolId)
        };

        return new OkObjectResult(listMenus);
    }

    [Route("GetMenu/{menuId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetMenu(int menuId)
    {
        ListMenus listMenus = new();

        if (menuId <= 0) throw new ValidationException("menuId", "Invalid request");

        Menu menuResponse = await _unitOfWork.MenuRepository.GetMenuById(menuId);

        await _menuValidator.ValidateUserAccessToMenu(menuResponse);

        return new OkObjectResult(menuResponse);
    }

    [Route("GetItemByCategory/{category}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    // Service wired up to UI but not used
    [Obsolete]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetItemByCategory(string category)
    {
        ListItems listItems = new();

        if (category == null) throw new ValidationException("category", "Invalid request");

        listItems.Items = await _unitOfWork.MenuRepository.GetMenuItemsByCategory(Convert.ToInt64(category));
        await _menuValidator.ValidateAccessToMenuItems(listItems.Items);

        return new OkObjectResult(listItems);
    }

    [Route("GetItemByCategoryAndMenu/{category}/{menu}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    // Service wired up to UI but not used
    [Obsolete]
    public async Task<IActionResult> GetItemByCategoryAndMenu(string category, string menu)
    {
        ListItems listItems = new();

        if (category == null) throw new ValidationException("category", "Invalid request");
        if (menu == null) throw new ValidationException("menu", "Invalid request");

        listItems.Items = await _unitOfWork.MenuRepository.GetMenuItemsByCategoryAndMenu(Convert.ToInt64(category), Convert.ToInt64(menu));

        await _menuValidator.ValidateAccessToMenuItems(listItems.Items);

        return new OkObjectResult(listItems);
    }

    [Route("GetItemById/{id}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetItemById(string id)
    {
        MenuItem menuItemResponse = new();

        if (id == null) throw new ValidationException("id", "Invalid request");

        menuItemResponse = await _unitOfWork.MenuRepository.GetMenuItemsById(Convert.ToInt64(id));

        if (menuItemResponse != null)
        {
            // include FriendlyName to this.menuItemResponse.Menus 
            menuItemResponse.Menus = await _unitOfWork.MenuRepository.GetMenusListByCanteenAndMenuItem(menuItemResponse.CanteenId, Convert.ToInt64(id));
        }

        await _menuValidator.ValidateAccessToCanteen((long)menuItemResponse.CanteenId);

        return new OkObjectResult(menuItemResponse);
    }

    [Route("GetMenuItemListBySchool/{schoolId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetMenuItemListBySchool(string schoolId)
    {
        ListItems listItems = new();
        var isValidId = long.TryParse(schoolId, out long validSchoolId);

        if (!isValidId) throw new ValidationException("schoolId", "Invalid request");

        await _menuValidator.ValidateAccessToSchool(validSchoolId);

        listItems.Items = await _unitOfWork.MenuRepository.GetMenuItemsBySchoolId(validSchoolId);

        return new OkObjectResult(listItems);
    }

    [Route("GetMenuItemList/{menuId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetMenuItemList(string menuId)
    {
        var isValidId = int.TryParse(menuId, out int validMenuId);
        if (!isValidId) throw new ValidationException("menuId", "Invalid request");

        ListItems listItems = new();
        listItems.Items = await _unitOfWork.MenuRepository.GetMenuItemsByMenuId(validMenuId);

        await _menuValidator.ValidateAccessToMenuItems(listItems.Items);

        return new OkObjectResult(listItems);
    }


    [Route("GetMenuItemListBySchoolDate")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    [Obsolete]
    public Task<IActionResult> GetMenuItemListBySchoolDate([FromBody] AvailableMenuItemsRequest request)
    {
        throw new DeprecatedApiException();
    }

    [Route("UpdateFoodBreakSettings")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> UpdateFoodBreakSettings([FromBody] FoodBreakSettingsUpdateRequest request)
    {
        if (request.MenuId == null) throw new ValidationException("Invalid request");

        // log request to investiggate future errors
        this._telemetryService.TrackTrace("UpdateFoodBreakSettings_Request",
                    new Dictionary<string, string>() {
                                            {"request", JsonSerializer.Serialize(request)}
                    });

        // get the menu by his ID
        Menu res = await _unitOfWork.MenuRepository.GetMenuById(Convert.ToInt32(request.MenuId));
        await _menuValidator.ValidateUserAccessToMenu(res);

        if (res != null && res.MenuId > 0)
        {
            await _menuService.UpdateFoodBreakSettings(request);
        }

        return new OkResult();
    }

    [Route("GetMenuBySchoolAndType/{schoolId}/{type}")]
    [Route("GetMenuBySchoolAndTypeMobile/{schoolId}/{type}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent, UserRole.Merchant)]
    public async Task<IActionResult> GetMenuBySchoolAndType(int schoolId, string type)
    {
        if (schoolId <= 0) throw new ValidationException("schoolId", "Invalid request");
        if (String.IsNullOrEmpty(type)) throw new ValidationException("type", "Invalid request");

        await _menuValidator.ValidateAccessToSchool(schoolId);
        var res = await _menuService.GetMenuBySchoolAndType(schoolId, type);

        return new OkObjectResult(res);
    }

    [Route("GetMenuByStudentAndType/{type}/{studentId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Parent, UserRole.Merchant)]
    public async Task<IActionResult> GetMenuBySchoolAndType2(string type, int studentId)
    {
        if (studentId <= 0) throw new ValidationException("studentId", "Invalid request");
        if (String.IsNullOrEmpty(type)) throw new ValidationException("type", "Invalid request");

        await _userValidator.ValidateAccessToStudent(studentId);

        var res = await _menuService.GetMenuWithItemsFrequentlyOrdered(studentId, type);

        return new OkObjectResult(res);
    }

}
