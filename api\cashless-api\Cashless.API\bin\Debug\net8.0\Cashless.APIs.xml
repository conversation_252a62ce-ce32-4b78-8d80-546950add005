<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Cashless.APIs</name>
    </assembly>
    <members>
        <member name="T:Cashless.APIs.Attributes.CheckUserRoleAndAllowNewUserAttribute">
            <summary>
            Action filter attribute to check if the current user has one
            of the roles specified... If the current user is not found
            because we are still in the process of creating them a record, 
            we should check the request for validity and continue
            
            To use this attribute, you decorate the target API method:
            [HttpPost]
            [Route("SomeApiMethod")]
            [CheckUserRoleAndAllowNewUser(UserRole.Parent)]
            public IActionResult SomeApiMethod(...
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.CheckUserRoleAndAllowNewUserAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Check a user's role before executing the next action in the HTTP context chain. If this fails
            check that the request payload is a valid upsert User request
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.CheckUserRoleAndAllowNewUserAttribute.CheckCreateUserRequest(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Check the request is a valid User upsert request that will result in a new User being created 
            with one of the target roles
            </summary>
        </member>
        <member name="T:Cashless.APIs.Attributes.CheckUserRoleAttribute">
            <summary>
            Action filter attribute to check if the current user has
            one of the roles specified.
            
            To use, you need to decorate the target API method:
            [HttpPost]
            [Route("SomeApiMethod")]
            [CheckUserRole(UserRole.Merchant, UserRole.Admin)]
            public IActionResult SomeApiMethod(...
            </summary>
        </member>
        <member name="P:Cashless.APIs.Attributes.CheckUserRoleAttribute.roles">
            <summary>
            Roles to check against
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.CheckUserRoleAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Check a user's role before executing the next action in the HTTP context chain
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.CheckUserRoleAttribute.Continue(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Continue with the other attributes and filters in the middleware chain for the given action 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.CheckUserRoleAttribute.CheckUserRoles(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Fetch the current user using their Firebase token and check that they have
            one of the required roles
            </summary>
        </member>
        <member name="T:Cashless.APIs.Attributes.HandleExceptionAttribute">
            <summary>
            Custom middleware to handle exceptions. This is implmented as an exception 
            filter attribute. It needs to be added to the startup class as per:
            https://docs.microsoft.com/en-us/aspnet/core/web-api/handle-errors?view=aspnetcore-6.0#use-exceptions-to-modify-the-response
            
            Return a response similar to the response returned by the .NET MVC framework. 
            For example, when validation errors are encountered, the following JSON is 
            returned:
            {
                "errors": {
                    "StartDate": [
                        "The StartDate field is required."
                    ]
                },
                "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                "title": "One or more validation errors occurred.",
                "status": 400
            }
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.HandleExceptionAttribute.OnExceptionAsync(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
            <summary>
            Handle an exception in the request chain, and return a HTTP 500 response
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.HandleExceptionAttribute.GetObjectResult(System.Exception)">
            <summary>
            Convert the exception into a response object depending
            on the type of exception
            </summary>
        </member>
        <member name="T:Cashless.APIs.Attributes.LogRequestBodyAttribute">
            <summary>
            Action filter attribute to log request body
            </summary>
        </member>
        <member name="M:Cashless.APIs.Attributes.LogRequestBodyAttribute.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Log the body of the request if one was found
            </summary>
        </member>
        <member name="T:Cashless.APIs.Controllers.AppVersionController">
            <summary>
            Controller for returning app version information
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.AppVersionController.Index">
            <summary>
            Return the AppVersion settings in the appsettings.json file
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.AppVersionController.GetSupportedVersions">
            <summary>
            Return the SupportedVersions from Azure App Configuration
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.BillingController.UpdateBillingState(Schools.BLL.Classes.MultipleOrders,System.Int32)">
            <summary>
            This is used by the cashless-process-order logic app. 
            
            NOTE: PaymentId is no longer needed. We no longer have subscription billing. 
                  It is kept as a param to avoid changing the logic app 
            
            Needs anonymous access, but we will check if the request
            contains the required cashless-api-secret header.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.EditorController.GenerateAllMenus">
            <summary>
            Generate all the menus
            This will create a huge load on the DB, we need to be carefull when we run it and also be sure to not run it too many times
            (best to run it between 4 and 5pm during the week or during a weekend)
            </summary>
        </member>
        <member name="T:Cashless.APIs.Controllers.SampleException">
            <summary>
            Sample exception
            </summary>
        </member>
        <member name="T:Cashless.APIs.Controllers.ErrorController">
            <summary>
            Controller for handling error. This is not included in Swagger docs
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.ErrorController.ThrowError">
            <summary>
            Throw an exception to simulate application errors and and test 
            the exception processing chain  
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.ErrorController.CheckRole">
            <summary>
            Check the user's role is admin or merchant
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.ErrorController.HandleError">
            <summary>
            Error handling middleware registered in the startup class:             
                app.UseExceptionHandler("/api/Error/HandleError");
                
            This has limitations in that it may not have the full context
            of the request and controller where the error occured
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.FeeController.GetFees(System.Collections.Generic.IEnumerable{Schools.BLL.Classes.Fees.Requests.FeesRequestNoMerchantId})">
            TODO: make obsolete when not being used in mobile app*
        </member>
        <member name="T:Cashless.APIs.Controllers.GuestPaymentController">
            <summary>
            Controller for handling guest user payments in POS system
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.ProcessPayment(Schools.BLL.Classes.Payments.GuestPaymentRequest)">
            <summary>
            Process a guest payment with card details
            </summary>
            <param name="request">Guest payment request</param>
            <returns>Payment response</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.ValidateCard(Schools.BLL.Classes.Payments.GuestCardValidationRequest)">
            <summary>
            Validate guest card details
            </summary>
            <param name="request">Card validation request</param>
            <returns>Validation response</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.GetPaymentStatus(System.String)">
            <summary>
            Get payment status for a guest order
            </summary>
            <param name="orderId">Order ID</param>
            <returns>Payment status response</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.ProcessPaymentWithMethodAndCreateOrder(Schools.BLL.Classes.Payments.GuestPaymentWithMethodRequest)">
            <summary>
            Process guest payment with payment method and create order in one step (PRODUCTION READY)
            </summary>
            <param name="request">Guest payment request with payment method ID</param>
            <returns>Combined payment and order response</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.ProcessPaymentAndCreateOrder(Schools.BLL.Classes.Payments.GuestPaymentRequest)">
            <summary>
            Process guest payment and create order in one step (TEST ONLY - uses test tokens)
            </summary>
            <param name="request">Guest payment and order request</param>
            <returns>Combined payment and order response</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.GuestPaymentController.ValidatePermissions(System.Int32,System.Int32)">
            <summary>
            Validate guest user permissions for payment
            </summary>
            <param name="guestUserId">Guest user ID</param>
            <param name="canteenId">Canteen ID</param>
            <returns>Permission validation result</returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.GetAll">
            <summary>
            Get all merchants
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.SearchUserForCreation(System.String)">
            <summary>
            Return the list of users with a canteen Role or a parent role without children
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.Get(System.Int32)">
            <summary>
            Get merchant by id
            </summary>
            <param name="canteenId"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.EditMerchant(System.Int32,Schools.BLL.Classes.Canteens.UpdateMerchantDetailsRequest)">
            <summary>
            Update merchant details
            </summary>
            <param name="canteenId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.EditMerchantContact(System.Int32,Schools.BLL.Classes.Canteens.UpdateMerchantOwnerRequest)">
            <summary>
            Edit the owner
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.Update(System.Int32,Schools.BLL.Classes.Canteens.UpdateMerchantFeeRequest)">
            <summary>
            Update merchant fee information
            </summary>
            <param name="canteenId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.SearchSchoolForMerchant(System.Int32,System.String)">
            <summary>
            Return the list of schools that can be link to the given merchant
            </summary>
            <param name="canteenId"></param>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantSchools(System.Int32)">
            <summary>
            Return the schools currently linked to the given merchant
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.LinkSchoolToMerchant(System.Int32,System.Int32)">
            <summary>
            link the school to the merchant
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.UnlinkSchoolFromMerchant(System.Int32,System.Int32)">
            <summary>
            unlink the school from the merchant.
            Also delete the existing permission
            </summary>
            <param name="canteenId"></param>
            <param name="schoolId"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.UpdateSchoolMerchantSettings(Schools.BLL.Classes.UpsertSchoolBillingDto)">
            <summary>
            Update School - Merchant settings
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.UpdateSchoolMerchantStatus(System.Int64,Schools.DAL.Entities.InternalSchoolMerchantStatusEnum)">
            <summary>
            Update School - Merchant settings
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantUsers(System.Int32)">
            <summary>
            Get the canteen users linked to the given canteenId
            </summary>
            <param name="canteenId"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantUsersSearch(System.Int32,System.String)">
            <summary>
            Get the users that we can add to the canteen
            </summary>
            <param name="canteenId"></param>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantAddUser(System.Int32,Schools.BLL.Classes.Canteens.UpsertMerchantUserRequest)">
            <summary>
            Add user permission to the canteen and schools
            </summary>
            <param name="canteenId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantGetUser(System.Int32,System.Int32)">
            <summary>
            Get the user permission for the given canteen Id
            </summary>
            <param name="canteenId"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantEditUser(System.Int32,System.Int32,Schools.BLL.Classes.Canteens.UpsertMerchantUserRequest)">
            <summary>
            Edit User permission on the canteen and schools
            </summary>
            <param name="canteenId"></param>
            <param name="userId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.MerchantRemoveUser(System.Int32,System.Int32)">
            <summary>
            Remove User permission on the canteen and schools
            </summary>
            <param name="canteenId"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.ExportInvoiceData(Schools.BLL.Classes.Invoices.InvoiceExportRequestDto)">
            <summary>
            Export the invoicing information for a given period in CSV format
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.GetFilename(Schools.DAL.Enums.InvoiceExportTypeEnum)">
            <summary>
            Return the name of the export
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.MerchantController.GetInvoiceData(Schools.BLL.Classes.Invoices.InvoiceExportRequestDto)">
            <summary>
            Fetch settlement or invoice records
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.NoticeController.GetAllSchoolNotices(System.Int32,System.Int32)">
            <summary>
            Get all notice for the given merchant and school
            Used in the canteen side only
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.NoticeController.GetActiveSchoolNotices(System.Int32)">
            <summary>
            Used to get all the notices for the given school
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.NoticeController.GetAllNoticeWaitingForValidation">
            <summary>
            Get all the notice waiting validation from an admin
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.NotificationController.PlaceOrderNotification(System.Collections.Generic.List{Schools.BLL.Classes.OrderUpsertResponse},System.Int32)">
            <summary>
            This is used by the cashless-process-order logic app. 
            
            Needs anonymous access, but we will check if the request
            contains the required cashless-api-secret header.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderListBySchoolandMerchant(Schools.BLL.Classes.OrderRequest)">
            <summary>
            Get all orders for a given school.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.SetOrdersToPrint(Schools.BLL.Classes.OrderRequest)">
            <summary>
            Save the orders to print
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.SetOrdersHasPrinted(Schools.BLL.Classes.SetPrintedRequest)">
            <summary>
            Get all orders for a given school.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.SaveItemsOrder(System.Int32)">
             <summary>
             This is used by the cashless-process-order logic app.
            
             Needs anonymous access, but we will check if the request
             contains the required cashless-api-secret header.
             </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetReportClassItem(Schools.DAL.DtosToMoveToBLL.ReportRequest)">
            <summary>
            Get report for Hybrid class/item list
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetReportOrdersUnprintedBySchool(Schools.DAL.DtosToMoveToBLL.ReportRequest)">
            <summary>
            Get a report of all labels that haven't been printed for the given day.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetReportOrdersUnprintedBySchoolV4(Schools.DAL.DtosToMoveToBLL.ReportRequest)">
            <summary>
            Get a report of all labels that haven't been printed for the given day.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetReportOrdersByMenuBySchool(Schools.DAL.DtosToMoveToBLL.ReportRequest)">
            <summary>
            Get report for canteen's orders group by MenuType
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderListByParent(Schools.BLL.Classes.OrderHistoryUserRequest)">
            <summary>
            Angular only: Get all orders for a given parent.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrdersHistoryByParent(Schools.BLL.Classes.OrderHistoryUserRequest)">
            <summary>
            Rebuild: get orders history
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderListByPayment(System.Int32)">
            <summary>
            Get all orders for a given payment transaction
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderUniformListByStudent(Schools.BLL.Classes.OrderHistoryUserRequest)">
            <summary>
            Get all uniform orders for a given student.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.CancelOrder(Schools.BLL.Classes.OrderWithPayment)">
            <summary>
            Cancel an Order (start at Client version => 2019-12-03)
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.CheckIfOrderExist(Schools.DAL.DtosToMoveToBLL.CheckExistingOrderRequest)">
            <summary>
             Check if a child already have an existing order
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrdersByStudentWithFilters(Schools.BLL.Classes.OrderFilter,System.Int32)">
            <summary>
            Fetch orders for a given student
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderByStudentOrderDateAndMenuType(Schools.BLL.Classes.Orders.OrderByStudentOrderDateAndMenuTypeRequest)">
             <summary>
             Return the newest non cancelled order for the given order date and menu type,
             as well as whether the school is closed or not!
            
             Inputs
             -- Student Id
             -- Order Date in yyyy-MM-dd format
             -- Menu Type
            
             Outputs
             -- Order
             -- Is School Closed
             </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderListByStudentMobile(Schools.BLL.Classes.OrderHistoryUserRequest)">
            <summary>
            Get all orders for a given student.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderListByStudent(Schools.BLL.Classes.OrderHistoryUserRequest)">
            <summary>
            Get all orders for a given student. This is called from the web app
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.RefundPartialOrder(Schools.BLL.Classes.OrderWithPayment)">
            <summary>
            Make a payment and place / edit order
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.MakePaymentAndMultipleOrdersMobile(Schools.BLL.Classes.MultipleOrdersWithPayment)">
            <summary>
            Make a payment and place / edit order
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.CreateOrders(Schools.BLL.Classes.MultipleOrderRequests)">
            <summary>
            Create Order
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.EditOrder(Schools.BLL.Classes.OrderEditRequest)">
            <summary>
            Edit Orders
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.SaveDraftOrders(System.String,Schools.BLL.Classes.MultipleOrders)">
            <summary>
            Insert or update draft Orders on submit or edit
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.UpdateDraftOrders(System.String,Schools.BLL.Classes.Orders.UpdateDraftOrdersRequest)">
            <summary>
            Update draft Orders following payment processing
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.UpdateFailedOrders(System.String,Schools.BLL.Classes.Orders.FailedOrdersRequest)">
            <summary>
            Update Failed Orders following payment processing
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrdersHistoryByParentMobile(Schools.BLL.Classes.OrderHistoryRequestMobile)">
             <summary>
             Get the order history for a given parent
            
             TODO - Combine with the "GetOrdersHistoryByParent" endpoint... The difference being
                    the original endpoint returns a List of Order objects and this returns a List
                    of OrderHistory objects
             </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.GetOrderItemStudentList(Schools.DAL.DtosToMoveToBLL.ReportRequest)">
            <summary>
            Get student list for a report menu item
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderController.CommonValidateRquest(Schools.DAL.DtosToMoveToBLL.ReportRequest,System.Boolean,System.Boolean)">
            <summary>
            Common validation for the Reports endpoints
            </summary>
            <param name="request"></param>
            <param name="checkDate"></param>
            <param name="checkMenuType"></param>
        </member>
        <member name="M:Cashless.APIs.Controllers.OrderStatusController.CreateError(Schools.BLL.Classes.Orders.CreateOrderErrorDto)">
            <summary>
            Called from the queues to add errors
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.PayController.GetNewToken(System.String,System.Boolean)">
            <summary>
            Gets a new token to initiate checkout
            </summary>
            <returns></returns>
            This endpoint will work for ONLY SCore customers
        </member>
        <member name="M:Cashless.APIs.Controllers.PayController.GetNewToken2(System.Decimal,System.Boolean)">
            <summary>
            Gets a new token to initiate checkout
            </summary>
            <returns></returns>
            This endpoint will work for BOTH SCore and Stripe customers
        </member>
        <member name="M:Cashless.APIs.Controllers.PayController.TopupAmount(Schools.BLL.ThirdParty.General.TopupRequest)">
            <summary>
            Top Up the balance of the user
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.PayController.MakePayment(Schools.BLL.Classes.Payments.PaymentRequest)">
            <summary>
            Top up the account and pay an order in the same time.
            
            This is used by the cashless-process-order logic app. 
            
            This needs anonymous access - there is no firebase token, but there 
            should be an API secret in one of the request headers
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.PayController.OrdersPaymentLink(Schools.BLL.Classes.Payments.OrdersPaymentLinkRequest)">
            <summary>
            Link orders to transactions
            
            This needs anonymous access - there is no firebase token, but there 
            should be an API secret in one of the request headers
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.PrintingController.SetOrdersToPrint(Schools.BLL.Classes.OrderRequest)">
            <summary>
            Save the orders to print
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.PrintingController.SetItemsToPrint(Schools.BLL.Classes.OrderRequest)">
            <summary>
            Set items to print
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.PrintingController.SetOrdersHasPrinted(Schools.BLL.Classes.SetPrintedRequest)">
            <summary>
            Get all orders for a given school.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.GetClassList(Schools.BLL.Classes.ClassRequest)">
            <summary>
            Get class list for a school
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.GetClassList(System.Int32)">
            <summary>
            Get class list for a school
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.UpdateClass(Schools.BLL.Classes.SchoolClassUpdateRequest)">
            <summary>
            Update school Class
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.UpdateClass(System.Int32,Schools.BLL.Classes.SchoolClassUpdateRequest)">
            <summary>
            Update school Class
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.CreateClass(Schools.BLL.Classes.SchoolClassCreateRequest)">
            <summary>
            Create school class
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.ArchiveClass(Schools.BLL.Classes.SchoolClassUpdateRequest)">
            <summary>
            archive school class 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolClassController.ArchiveClass(System.Int32)">
            <summary>
            archive school class 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolController.NotifyNotPrintedOrdersListByCanteen">
            <summary>
            This is used by the sps-cas-alert-order-not-printed logic app. Needs anonymous access
            
            Needs anonymous access, but we will check if the request
            contains the required cashless-api-secret header.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolController.CreateSchool(Schools.BLL.Classes.CreateSchoolRequest)">
            <summary>
            Create a school and associate it with the given canteen
            Obsolete => new endpoint had been created to use in replacement of this one 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolController.Create(Schools.BLL.Classes.CreateSchoolDto)">
            <summary>
            Create a new School
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolController.EditSchool(System.Int32,Schools.BLL.Classes.SchoolDto)">
            <summary>
            Edit School information
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolController.GetMerchantsPerSchool(System.Int32)">
            <summary>
            Get the active merchants link to the school
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventController.GetEvent(System.Int32)">
            <summary>
            Get event
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventController.GetEventsBySchoolMobile(Schools.BLL.Classes.SchoolEventsRequest)">
            <summary>
            Get all events available to parents. Not the same as GetEventsBySchool
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.GetEventView(System.Int32)">
            <summary>
            Get event
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.GetEventsBySchool(System.Int32)">
            <summary>
            Get all events(not archived) for the given school (admin only)
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.GetEventsBySchoolMerchantStudent(SchoolEventMerchantStudentRequest)">
            <summary>
            Get all events(not archived) for the given school with the event orders for the given student
            Used for Walk-up orders
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.EditEvent(System.Int32,Schools.BLL.Classes.SchoolEventUpdateRequest)">
            <summary>
            Edit and existing event
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.CreateEvent(SchoolEventCreateRequest)">
            <summary>
            Create a new event
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolEventManagerController.DeleteEvent(System.Int32)">
            <summary>
            Archive event
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolFeatureController.GetFeaturesBySchool(System.Int32)">
            <summary>
            Get all active features for the given school
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.SchoolFeatureController.CreateFeature(System.Int32,System.Collections.Generic.List{Schools.BLL.Classes.SchoolFeature.SchoolFeatureDto})">
            <summary>
            update feature
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.StockController.OrderUpdateStocks(System.Int32)">
            <summary>
            This is used by the cashless-process-order logic app. 
            
            Needs anonymous access, but we will check if the request
            contains the required cashless-api-secret header.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpsertUser(Schools.DAL.DtosToMoveToBLL.User)">
            <summary>
            This API call is used in the registration flow on the Web app. The app calls the 
            Firebase SDK to create a new account, and then calls this method to create the User 
            in the Schools platform.
            
            This is also used to create and update Students. All new users start as parents. 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.CreateUser(Schools.BLL.Classes.CreateSchoolsUserRequest)">
            <summary>
            Create a new User
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpdateUser(Schools.BLL.Classes.UpdateSchoolsUserRequest)">
            <summary>
            Update an existing user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpsertStudent(Schools.DAL.DtosToMoveToBLL.Student)">
            <summary>
            Rebuild: Upsert student
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.GetUsersByCanteen(System.Int64)">
            <summary>
            Retrieves list of canteen users by canteen ID
            </summary>
            <param name="canteenId">ID of the canteen</param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.GetCurrentUser(Schools.DAL.DtosToMoveToBLL.UserRequest)">
            <summary>
            Get user details. Only used in Web apps this is used in the auth flow only. 
            The User fetching their own details
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UserUpdateProfileMobile(Schools.DAL.DtosToMoveToBLL.UserUpdateProfile)">
            <summary>
            Rebuild: Edit user profile 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UseReactNativeBuild">
            <summary>
            Check if the current parent should use the React Native build
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.RegisterUser(Schools.BLL.Classes.CreateUserRequest)">
            <summary>
            This API call is used in the registration flow on the Mobile app. The app calls the 
            Firebase SDK to create a new account, and then calls this method to create the User 
            in the Schools platform.
            
            All new users start as parents. 
            Below endpoint will create User including SCore Customer
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpsertStudentMobile(Schools.DAL.DtosToMoveToBLL.Student)">
            <summary>
            Update a student.. Not the same as UpsertStudent
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.GetSpriggyAccountId(System.Int32)">
            <summary>
            Fetch the Account ID for a given user
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpdateSpriggyAccountId(System.Int32)">
            <summary>
            Update the Spriggy Account ID for a given User
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.GetCustomClaims(System.Int32)">
            <summary>
            Get a given User's custom claims in Firebase
            </summary>
        </member>
        <member name="M:Cashless.APIs.Controllers.UserController.UpdateCustomClaims(System.Int32)">
            <summary>
            Update a given User's custom claims in Firebase
            </summary>
        </member>
        <member name="T:Cashless.APIs.Extensions.HealthChecksBuilderExtension">
            <summary>
            Extension to register custom IHealthCheck instances that
            require parameters and a DI container. This is achieved by
            passing a `Func` that returns the istance of the IHealthCheck
            to add
            </summary>
        </member>
        <member name="F:Cashless.APIs.Extensions.HealthChecksBuilderExtension.ScoreApi">
            <summary>
            Default name of the spriggy-core API health check
            </summary>
        </member>
        <member name="F:Cashless.APIs.Extensions.HealthChecksBuilderExtension.SchoolsAudit">
            <summary>
            Default name of the schools-audit health check
            </summary>
        </member>
        <member name="F:Cashless.APIs.Extensions.HealthChecksBuilderExtension.SchoolsOrders">
            <summary>
            Default name of the schools-orders health check
            </summary>
        </member>
        <member name="F:Cashless.APIs.Extensions.HealthChecksBuilderExtension.SpriggyCore">
            <summary>
            Default name of the spriggy-core health check
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddScoreApi(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,Cashless.APIs.HealthChecks.ScoreApiOperation,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Create and configure a new Core API healthcheck
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddScoreApi(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.Func{System.IServiceProvider,Cashless.APIs.HealthChecks.ScoreApiOperation},Cashless.APIs.HealthChecks.ScoreApiOperation,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Create and configure a new Core API healthcheck
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddSchoolsAudit(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Configure a new an Audit function app health check
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddSchoolsAudit(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.Func{System.Net.Http.IHttpClientFactory,System.String},System.String,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Configure a new an Audit function app health check
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddSpriggyCore(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.String,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Configure a new a Spriggy Core health check
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HealthChecksBuilderExtension.AddSpriggyCore(Microsoft.Extensions.DependencyInjection.IHealthChecksBuilder,System.Func{System.Net.Http.IHttpClientFactory,System.String},System.String,System.String,System.Nullable{Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus},System.Collections.Generic.IEnumerable{System.String},System.Nullable{System.TimeSpan})">
            <summary>
            Configure a new a Spriggy Core health check
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HostEnvironmentExtension.IsUAT(Microsoft.Extensions.Hosting.IHostEnvironment)">
            <summary>
            Check if the env. is UAT
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.HostEnvironmentExtension.GetEnvironmentCode(Microsoft.Extensions.Hosting.IHostEnvironment)">
            <summary>
            Provide a 3-4 letter code in place of the env. name
            </summary>
        </member>
        <member name="F:Cashless.APIs.Extensions.ServiceCollectionExtension.HttpMessageHandlerLifetime">
            <summary>
            How long HttpMessageHandler objects will live for in the pool of 
            HttpMessageHandler objects (in minutes)
            
            https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#httpclient-lifetimes
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.ServiceCollectionExtension.GetRetryPolicy(System.Int32,System.Int32)">
            <summary>
            Use Polly to create a policy to retry HTTP requests after a transient 
            failure (HTTP Status = 408 or >= 500)
            
            NOTE - We will try this on read transactions for the time being
            
            https://learn.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/implement-http-call-retries-exponential-backoff-polly
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.ServiceCollectionExtension.GetBulkheadPolicy(System.Int32,System.Int32)">
            <summary>
            Use bulkhead to reduce the number of concurrent requests being sent
            to a given API. This throws an exception once the queue of requests
            is full
            </summary>
        </member>
        <member name="M:Cashless.APIs.Extensions.ServiceCollectionExtension.AddApiServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Register the various services with the DI container
            </summary>
        </member>
        <member name="T:Cashless.APIs.Filters.AddHttpHeadersActionFilter">
            <summary>
            MVC action filter to add common security headers
            to every web response once all processing is done
            </summary>
        </member>
        <member name="M:Cashless.APIs.Filters.AddHttpHeadersActionFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)">
            <summary>
            Add additional headers to each response
            </summary>
        </member>
        <member name="T:Cashless.APIs.Filters.CheckApiSecretHeaderActionFilter">
            <summary>
            MVC action filter attribute to check the presence of a valid 
            api secret header on given controller actions
            </summary>
        </member>
        <member name="M:Cashless.APIs.Filters.CheckApiSecretHeaderActionFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            Check the cashless-api-secret on a given controller action
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.HealthCheckResponseWriter.WriteResponse(Microsoft.AspNetCore.Http.HttpContext,Microsoft.Extensions.Diagnostics.HealthChecks.HealthReport)">
            <summary>
            Write the HealthCheck report to output stream
            </summary>
        </member>
        <member name="T:Cashless.APIs.HealthChecks.ScoreApiOperation">
            <summary>
            Operations to use when checking if the Spriggy 
            Core API is up and running
            </summary>
        </member>
        <member name="T:Cashless.APIs.HealthChecks.ScoreApiHealthCheck">
            <summary>
            Check the Spriggy Core API
            </summary>
        </member>
        <member name="F:Cashless.APIs.HealthChecks.ScoreApiHealthCheck._serviceProvider">
            <summary>
            Services to inject
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.CheckHealthAsync(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext,System.Threading.CancellationToken)">
            <summary>
            Check Spriggy API Calls
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.SendCreateCustomer(System.Diagnostics.Stopwatch)">
            <summary>
            Call CreateCustomer with an empty response
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.SendGetCustomer(System.Diagnostics.Stopwatch)">
            <summary>
            Get the customer balance for the Fee Account
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.GetData">
            <summary>
            Get telemetry for logging
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.GetHealthyResult(System.Diagnostics.Stopwatch,System.Object)">
            <summary>
            Return a healthy result
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.GetDegradedResult(System.Diagnostics.Stopwatch,System.Object)">
            <summary>
            Return a degraded result
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.ScoreApiHealthCheck.GetUnhealthyResult(System.Diagnostics.Stopwatch,System.Object)">
            <summary>
            Return an unhealthy result
            </summary>
        </member>
        <member name="T:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck">
            <summary>
            Check the health of Spriggy Core
            </summary>
        </member>
        <member name="T:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck.HealthResult">
            <summary>
            Result from https://api.sandbox.spriggy.io/health
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck.HealthResult.IsHealthy">
            <summary>
            Check if the response is healthy
            </summary>
        </member>
        <member name="F:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck._url">
            <summary>
            URL for the health endpoint on the spriggy-core infrastructure
            </summary>
        </member>
        <member name="F:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck._httpClientFactory">
            <summary>
            HTTP client factory
            </summary>
        </member>
        <member name="M:Cashless.APIs.HealthChecks.SpriggyCoreHealthCheck.CheckHealthAsync(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext,System.Threading.CancellationToken)">
            <summary>
            Check spriggy-core is available
            </summary>
        </member>
        <member name="T:Cashless.APIs.Hydrators.ItemCategoryHydrator">
            <summary>
            This class provides a method to hydrate a list of item categories (DTO) from meny categories (db entity). 
            The reason for this implementation is so that we have full control over what is send to any frontend or mobile app user interface, with a clear separation to database entity models.
            This had benefits in terms of security, but the down-side is that there is extra coding overhead.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Program.Main(System.String[])">
            <summary>
            Build and run the API server
            </summary>
        </member>
        <member name="M:Cashless.APIs.Program.CreateWebHostBuilder(System.String[])">
            <summary>
            Build an API server and push the configuration of services
            into the Startup class
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Hosting.IWebHostEnvironment,Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.Extensions.Logging.ILogger{Cashless.APIs.Startup})">
            <summary>
            Create a Startup class to configure the application
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            This method gets called by the runtime. Use this method to add services to the container
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.ConfigureSwagger(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Configure Swagger documentation
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.Configure(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.ConfigAuthentication(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Read the Firebase settings from App Configuration and initilise the FirebaseApp instance
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.GetFirebaseSettingsFromAppConfiguration">
            <summary>
            Fetch the settings from App Configuration and write them to a local file
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.GetFirebaseSettingsFromFileSystem">
            <summary>
            Fetch the path to the file to load from the file system
            </summary>
        </member>
        <member name="M:Cashless.APIs.Startup.ConfigureIntegrations">
             <summary>
             Load configuration parameters from App Configuration in Azure and update
             different services ahead of using them
            
             TODO -  Load all settings into an injectable IConfigService that other
                     components can fetch these settings from
             </summary>
        </member>
        <member name="T:Cashless.APIs.Validators.BillingValidator">
            <summary>
            Validate requests into the Order API
            </summary>
        </member>
        <member name="T:Cashless.APIs.Validators.StudentValidator">
            <summary>
            Validator to check various User API calls
            </summary>
        </member>
        <member name="T:Cashless.APIs.Validators.UserValidator">
            <summary>
            Validator to check various User API calls
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateRequest(Schools.DAL.DtosToMoveToBLL.Student)">
            <summary>
            Check a Student insert / update 
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateUpdate(Schools.DAL.DtosToMoveToBLL.Student,Schools.DAL.DtosToMoveToBLL.User)">
            <summary>
            Check a Student update request
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateInsert(Schools.DAL.DtosToMoveToBLL.Student,Schools.DAL.DtosToMoveToBLL.User)">
            <summary>
            Check a Student insert request
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateRequest(Schools.DAL.DtosToMoveToBLL.UserUpdateProfile)">
            <summary>
            Validate the user profile update request
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateRequest(Schools.DAL.DtosToMoveToBLL.UserRequest)">
            <summary>
            Check that the user requested is the logged in user!
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.CheckTransferUserBalance(Schools.BLL.Classes.Users.TransferUserBalanceRequest,Schools.DAL.DtosToMoveToBLL.User,Schools.DAL.DtosToMoveToBLL.User)">
            <summary>
            Check the transfer user balance operation
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateRequest(Schools.BLL.Classes.ResetPasswordRequest)">
            <summary>
            Ensure request contains valid looking details
            </summary>
        </member>
        <member name="M:Cashless.APIs.Validators.UserValidator.ValidateRequest(Schools.BLL.Classes.CreateUserRequest)">
            <summary>
            Validate the request to create a new User before attempting the create
            </summary>
        </member>
        <member name="T:Cashless.API.Controllers.AdminController">
            <summary>
            Controller to house the various admin functions for Admin processes within the system.
            
            The steps in the current Logic App based ordering process are as follows:
            Step 1 - Create Order by calling sp_Order_Upsert to insert tblOrders records
            Step 2 - Call /api/Pay/MakePayment to call Spriggy Core and make payments
            Step 3 - Call /api/Billing/UpdateBillingState to tramsfer the Order fee to Fee Acount if **payment succeeded**
            Step 4 - Call sp_Order_Set_Completed with OrderStatusId and PaymentId to update Order status and create tblUserPayments records
            Step 5 - Call /api/Order/SaveItemsOrder to save Order JSON to tblOrderItems
            Step 6 - Call /api/Notification/PlaceOrderNotification to send Order confirmation emails
            Step 7 - Call /api/Stock/OrderUpdateStocks/ to do stock management
            </summary>
        </member>
        <member name="F:Cashless.API.Controllers.AdminController.DestinationStatuses">
            <summary>
            Destination states for stuck Orders
            </summary>
        </member>
        <member name="F:Cashless.API.Controllers.AdminController.MESSAGE_ERROR">
            <summary>
            Values of tblOrders.ErrorMessage
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.MoveDraftOrderToError(System.Int64)">
            <summary>
            Push a stuck Order to Error and carry out any steps required
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.MoveDraftOrderToError(System.Collections.Generic.List{System.Int64})">
            <summary>
            Push stuck Orders to Error and carry out any steps required
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.MoveDraftOrderToConfirmed(System.Int64)">
            <summary>
            Push a stuck Order to Confirmed and carry out any steps that were not carried out
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.MoveDraftOrderToConfirmed(System.Collections.Generic.List{System.Int64})">
            <summary>
            Push stuck Orders to Confirmed and carry out any steps that were not carried out
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.ProcessOrders(System.Collections.Generic.IEnumerable{System.Int64},Schools.DAL.Enums.OrderStatusEnum)">
            <summary>
            Fetch Orders identified and push them to the Error or Confirmed state
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.ConfirmOrder(Schools.DAL.DtosToMoveToBLL.Order)">
            <summary>
            Confirm the Order and move on
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.ErrorOrder(Schools.DAL.DtosToMoveToBLL.Order)">
            <summary>
            Move the Order to error and let Parents order again
            </summary>
        </member>
        <member name="M:Cashless.API.Controllers.AdminController.GetNotificationRequests(Schools.DAL.DtosToMoveToBLL.Order,Schools.DAL.Enums.OrderStatusEnum)">
            <summary>
            Convert the given Order into a List to call the Notification Service
            </summary>
        </member>
        <member name="T:Cashless.API.HealthChecks.BaseHealthCheck">
            <summary>
            Class containing common functionality for health checks
            </summary>
        </member>
        <member name="T:Cashless.API.HealthChecks.BaseHealthCheck.HealthCheckOutcome">
            <summary>
            Outcomes of a health check
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.BaseHealthCheck.GetUrl(System.String,System.String)">
            <summary>
            Combine a base URL and a URI
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.BaseHealthCheck.GetResult(System.Diagnostics.Stopwatch,System.Object,Cashless.API.HealthChecks.BaseHealthCheck.HealthCheckOutcome,System.Object,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Return an Health / Degraded / Unhealthy response with some telemetry
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.BaseHealthCheck.GetHealthyResult(System.Diagnostics.Stopwatch,System.Object,System.Object,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Return a healthy result with associated telemetry
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.BaseHealthCheck.GetDegradedResult(System.Diagnostics.Stopwatch,System.Object,System.Object,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Return a degraded result with associated telemetry
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.BaseHealthCheck.GetUnhealthyResult(System.Diagnostics.Stopwatch,System.Object,System.Object,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Return an unhealthy result with associated telemetry
            </summary>
        </member>
        <member name="T:Cashless.API.HealthChecks.PingHealthCheck">
            <summary>
            A health check to parse the responses produced by instances of IHealthCheckService
            </summary>
        </member>
        <member name="F:Cashless.API.HealthChecks.PingHealthCheck._logger">
            <summary>
            Logger!
            </summary>
        </member>
        <member name="F:Cashless.API.HealthChecks.PingHealthCheck._url">
            <summary>
            URL for the health-check endpoint on the schools orders functions app
            </summary>
        </member>
        <member name="F:Cashless.API.HealthChecks.PingHealthCheck._httpClientFactory">
            <summary>
            HTTP client factory
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.PingHealthCheck.GetFailureResult(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext,System.Diagnostics.Stopwatch,System.Object)">
            <summary>
            Return a failure result based on the configuration of the health check
            </summary>
        </member>
        <member name="M:Cashless.API.HealthChecks.PingHealthCheck.CheckHealthAsync(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext,System.Threading.CancellationToken)">
            <summary>
            Check Schools Orders functions app is running. The status returned depends on the configuration context passed in
            </summary>
        </member>
    </members>
</doc>
