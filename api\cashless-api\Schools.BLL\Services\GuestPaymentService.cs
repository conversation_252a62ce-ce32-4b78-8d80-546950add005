using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Schools.BLL.Classes.Payments;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.ThirdParty.General;
using Schools.DAL.Enums;
using Schools.DAL.Interfaces;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Services;

/// <summary>
/// Service for handling guest user payments
/// </summary>
public class GuestPaymentService : IGuestPaymentService
{
    private readonly IStripeService _stripeService;
    private readonly IUserService _userService;
    private readonly ICanteenService _canteenService;
    private readonly IOrderService _orderService;
    private readonly ITelemetryService _telemetryService;
    private readonly ILogger<GuestPaymentService> _logger;

    public GuestPaymentService(
        IStripeService stripeService,
        IUserService userService,
        ICanteenService canteenService,
        IOrderService orderService,
        ITelemetryService telemetryService,
        ILogger<GuestPaymentService> logger)
    {
        _stripeService = stripeService;
        _userService = userService;
        _canteenService = canteenService;
        _orderService = orderService;
        _telemetryService = telemetryService;
        _logger = logger;
    }

    /// <summary>
    /// Process a guest payment with card details
    /// </summary>
    public async Task<GuestPaymentResponse> ProcessGuestPayment(GuestPaymentRequest request)
    {
        try
        {
            // Validate input request
            var validationResult = ValidatePaymentRequest(request);
            if (!validationResult.IsValid)
            {
                return new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "VALIDATION_ERROR",
                    Message = validationResult.ErrorMessage
                };
            }

            _logger.LogInformation("Processing guest payment for user {GuestUserId}, amount {Amount}",
                request.GuestUserId, request.Amount);

            // Validate guest permissions
            var hasPermission = await ValidateGuestPermissions(request.GuestUserId, request.CanteenId);
            if (!hasPermission)
            {
                return new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "PERMISSION_DENIED",
                    Message = "Guest user does not have permission to make payments"
                };
            }

            // Validate card details
            var cardValidation = await ValidateGuestCard(new GuestCardValidationRequest
            {
                CardNumber = request.CardNumber,
                ExpiryMonth = request.ExpiryMonth,
                ExpiryYear = request.ExpiryYear,
                CVV = request.CVV
            });

            if (!cardValidation.IsValid)
            {
                return new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "INVALID_CARD",
                    Message = cardValidation.ErrorMessage
                };
            }

            // Get canteen details for payment processing
            var canteen = await _canteenService.GetCanteenById(request.CanteenId);
            if (canteen == null)
            {
                return new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "CANTEEN_NOT_FOUND",
                    Message = "Canteen not found"
                };
            }

            // Create temporary Stripe customer for guest payment
            var guestCustomerId = await CreateTemporaryStripeCustomer(request);
            
            // Process payment through Stripe
            var paymentSuccess = await _stripeService.CreateAndConfirmPaymentIntent(
                guestCustomerId, 
                (long)Math.Round(request.Amount * 100, 0), // Convert to cents
                null // We'll use the card details directly
            );

            if (!paymentSuccess)
            {
                return new GuestPaymentResponse
                {
                    IsSuccess = false,
                    ErrorCode = "PAYMENT_FAILED",
                    Message = "Payment processing failed"
                };
            }

            // Create order after successful payment
            var orderId = await CreateGuestOrder(request, canteen.ExternalUserId);

            return new GuestPaymentResponse
            {
                IsSuccess = true,
                OrderId = orderId,
                Message = "Payment processed successfully",
                TransactionId = Guid.NewGuid().ToString(),
                ProcessedAt = DateTime.UtcNow,
                AmountCharged = request.Amount,
                PaymentReference = $"guest_{request.GuestUserId}_{DateTime.UtcNow:yyyyMMddHHmmss}"
            };
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument in guest payment for user {GuestUserId}", request.GuestUserId);
            return new GuestPaymentResponse
            {
                IsSuccess = false,
                ErrorCode = "INVALID_REQUEST",
                Message = "Invalid payment request data"
            };
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized guest payment attempt for user {GuestUserId}", request.GuestUserId);
            return new GuestPaymentResponse
            {
                IsSuccess = false,
                ErrorCode = "UNAUTHORIZED",
                Message = "Unauthorized payment attempt"
            };
        }
        catch (TimeoutException ex)
        {
            _logger.LogWarning(ex, "Payment timeout for guest user {GuestUserId}", request.GuestUserId);
            _telemetryService.TrackException(ex);
            return new GuestPaymentResponse
            {
                IsSuccess = false,
                ErrorCode = "PAYMENT_TIMEOUT",
                Message = "Payment processing timed out. Please try again."
            };
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation in guest payment for user {GuestUserId}", request.GuestUserId);
            return new GuestPaymentResponse
            {
                IsSuccess = false,
                ErrorCode = "INVALID_OPERATION",
                Message = "Payment operation is not valid at this time"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing guest payment for user {GuestUserId}", request.GuestUserId);
            _telemetryService.TrackException(ex);

            return new GuestPaymentResponse
            {
                IsSuccess = false,
                ErrorCode = "PROCESSING_ERROR",
                Message = "An unexpected error occurred while processing the payment"
            };
        }
    }

    /// <summary>
    /// Validate guest card details
    /// </summary>
    public async Task<GuestCardValidationResponse> ValidateGuestCard(GuestCardValidationRequest request)
    {
        try
        {
            // Basic card number validation (Luhn algorithm)
            if (!IsValidCardNumber(request.CardNumber))
            {
                return new GuestCardValidationResponse
                {
                    IsValid = false,
                    ErrorMessage = "Invalid card number"
                };
            }

            // Validate expiry date
            if (!IsValidExpiryDate(request.ExpiryMonth, request.ExpiryYear))
            {
                return new GuestCardValidationResponse
                {
                    IsValid = false,
                    ErrorMessage = "Card has expired or invalid expiry date"
                };
            }

            // Validate CVV
            if (!IsValidCVV(request.CVV))
            {
                return new GuestCardValidationResponse
                {
                    IsValid = false,
                    ErrorMessage = "Invalid CVV"
                };
            }

            var cardType = GetCardType(request.CardNumber);
            var maskedNumber = MaskCardNumber(request.CardNumber);

            return new GuestCardValidationResponse
            {
                IsValid = true,
                CardType = cardType,
                MaskedCardNumber = maskedNumber
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating guest card");
            return new GuestCardValidationResponse
            {
                IsValid = false,
                ErrorMessage = "Card validation failed"
            };
        }
    }

    /// <summary>
    /// Get payment status for a guest order
    /// </summary>
    public async Task<GuestPaymentStatusResponse> GetGuestPaymentStatus(GuestPaymentStatusRequest request)
    {
        try
        {
            // This would typically query the order/payment database
            // For now, return a basic response
            return new GuestPaymentStatusResponse
            {
                Status = "COMPLETED",
                OrderId = request.OrderId,
                IsCompleted = true,
                ProcessedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting guest payment status for order {OrderId}", request.OrderId);
            return new GuestPaymentStatusResponse
            {
                Status = "ERROR",
                ErrorMessage = "Failed to retrieve payment status"
            };
        }
    }

    /// <summary>
    /// Validate guest user permissions for payment
    /// </summary>
    public async Task<bool> ValidateGuestPermissions(int guestUserId, int canteenId)
    {
        try
        {
            var user = await _userService.GetUserById(guestUserId);
            if (user == null || !user.IsGuest.GetValueOrDefault(false))
            {
                return false;
            }

            // Check if user is active and associated with the canteen's school
            return user.IsActive && user.SchoolId.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating guest permissions for user {GuestUserId}", guestUserId);
            return false;
        }
    }

    #region Private Helper Methods

    private (bool IsValid, string ErrorMessage) ValidatePaymentRequest(GuestPaymentRequest request)
    {
        if (request == null)
            return (false, "Payment request cannot be null");

        if (request.GuestUserId <= 0)
            return (false, "Invalid guest user ID");

        if (request.CanteenId <= 0)
            return (false, "Invalid canteen ID");

        if (request.Amount <= 0 || request.Amount > 10000)
            return (false, "Invalid payment amount");

        if (string.IsNullOrWhiteSpace(request.CardNumber))
            return (false, "Card number is required");

        if (string.IsNullOrWhiteSpace(request.CVV))
            return (false, "CVV is required");

        if (string.IsNullOrWhiteSpace(request.CardholderName))
            return (false, "Cardholder name is required");

        if (request.ExpiryMonth < 1 || request.ExpiryMonth > 12)
            return (false, "Invalid expiry month");

        if (request.ExpiryYear < DateTime.Now.Year || request.ExpiryYear > DateTime.Now.Year + 20)
            return (false, "Invalid expiry year");

        if (request.Items == null || request.Items.Count == 0)
            return (false, "Order items are required");

        // Validate each order item
        foreach (var item in request.Items)
        {
            if (item.MenuItemId <= 0)
                return (false, "Invalid menu item ID");

            if (item.Quantity <= 0 || item.Quantity > 100)
                return (false, "Invalid item quantity");

            if (item.Price <= 0 || item.Price > 1000)
                return (false, "Invalid item price");
        }

        return (true, string.Empty);
    }

    private async Task<string> CreateTemporaryStripeCustomer(GuestPaymentRequest request)
    {
        // Create a temporary customer reference for the guest payment
        var customerRef = $"guest_{request.GuestUserId}_{DateTime.UtcNow:yyyyMMddHHmmss}";
        return await _stripeService.CreateCustomer(customerRef);
    }

    private async Task<string> CreateGuestOrder(GuestPaymentRequest request, string canteenExternalId)
    {
        try
        {
            // Create order request in the format expected by the existing system
            var orderRequest = new MultipleOrderRequests
            {
                Orders = new List<OrderPlacement>
                {
                    new OrderPlacement
                    {
                        StudentId = request.GuestUserId,
                        MenuId = request.MenuId,
                        MenuType = request.MenuType,
                        OrderDate = DateTime.Parse(request.OrderDate),
                        Items = request.Items.Select(item => new OrderPlacementItem
                        {
                            MenuItemId = item.MenuItemId,
                            Quantity = item.Quantity,
                            Price = item.Price
                        }).ToList()
                    }
                }
            };

            // Use the existing order service to create the order
            // Note: This bypasses some validation that requires authenticated users
            // We'll need to create a special method for guest orders
            var orderResponse = await CreateGuestOrderDirect(orderRequest, request.GuestUserId, request.CanteenId);

            return orderResponse?.FirstOrDefault()?.OrderId?.ToString() ??
                   $"GUEST_ORDER_{DateTime.UtcNow:yyyyMMddHHmmss}_{request.GuestUserId}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating guest order for user {GuestUserId}", request.GuestUserId);
            // Return a fallback order ID for tracking purposes
            return $"GUEST_ORDER_ERROR_{DateTime.UtcNow:yyyyMMddHHmmss}_{request.GuestUserId}";
        }
    }

    private async Task<IEnumerable<PlaceOrderResponse>> CreateGuestOrderDirect(
        MultipleOrderRequests request,
        int guestUserId,
        int canteenId)
    {
        // This is a simplified version of the order creation process for guest users
        // It bypasses some of the authentication and validation checks

        var orderEntities = new List<OrderEntity>();
        var guestUser = await _userService.GetUserById(guestUserId);

        if (guestUser == null || !guestUser.IsGuest.GetValueOrDefault(false))
        {
            throw new InvalidOperationException("Invalid guest user");
        }

        foreach (var order in request.Orders)
        {
            var orderEntity = new OrderEntity
            {
                StudentId = order.StudentId,
                MenuId = order.MenuId,
                MenuType = order.MenuType,
                OrderDate = order.GetOrderDate(),
                CanteenId = canteenId,
                SchoolId = guestUser.SchoolId.Value,
                StudentName = $"{guestUser.FirstName} {guestUser.Lastname}",
                OrderAmountIncGst = order.Items.Sum(i => i.Price * i.Quantity),
                OrderStatus = OrderStatusEnum.Confirmed,
                CreatedDate = DateTime.UtcNow,
                Items = order.Items.Select(item => new OrderItemEntity
                {
                    MenuItemId = item.MenuItemId,
                    Quantity = item.Quantity,
                    Price = item.Price
                }).ToList()
            };

            orderEntities.Add(orderEntity);
        }

        // Save orders to database (simplified version)
        // In a full implementation, this would use the existing order repository
        var responses = orderEntities.Select(o => new PlaceOrderResponse
        {
            OrderId = Guid.NewGuid().ToString(), // Generate a unique order ID
            StudentId = o.StudentId.Value,
            MenuType = o.MenuType,
            OrderDate = o.OrderDate,
            TotalAmount = o.OrderAmountIncGst
        });

        return responses;
    }

    private bool IsValidCardNumber(string cardNumber)
    {
        // Remove spaces and dashes
        cardNumber = Regex.Replace(cardNumber, @"[\s-]", "");
        
        // Check if all digits
        if (!Regex.IsMatch(cardNumber, @"^\d+$"))
            return false;

        // Luhn algorithm
        int sum = 0;
        bool alternate = false;
        
        for (int i = cardNumber.Length - 1; i >= 0; i--)
        {
            int digit = int.Parse(cardNumber[i].ToString());
            
            if (alternate)
            {
                digit *= 2;
                if (digit > 9)
                    digit = (digit % 10) + 1;
            }
            
            sum += digit;
            alternate = !alternate;
        }
        
        return sum % 10 == 0;
    }

    private bool IsValidExpiryDate(int month, int year)
    {
        var now = DateTime.Now;
        var expiryDate = new DateTime(year, month, DateTime.DaysInMonth(year, month));
        return expiryDate >= now.Date;
    }

    private bool IsValidCVV(string cvv)
    {
        return Regex.IsMatch(cvv, @"^\d{3,4}$");
    }

    private string GetCardType(string cardNumber)
    {
        cardNumber = Regex.Replace(cardNumber, @"[\s-]", "");
        
        if (Regex.IsMatch(cardNumber, @"^4"))
            return "Visa";
        if (Regex.IsMatch(cardNumber, @"^5[1-5]"))
            return "Mastercard";
        if (Regex.IsMatch(cardNumber, @"^3[47]"))
            return "American Express";
        
        return "Unknown";
    }

    private string MaskCardNumber(string cardNumber)
    {
        cardNumber = Regex.Replace(cardNumber, @"[\s-]", "");
        if (cardNumber.Length < 4)
            return cardNumber;
        
        return "**** **** **** " + cardNumber.Substring(cardNumber.Length - 4);
    }

    #endregion
}
