using System;
using System.ComponentModel.DataAnnotations;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Fees.Requests;

public class AddFeeUsedRequest
{
    [Required]
    [Range(1, long.MaxValue, ErrorMessage = "Fee calculator id must be supplied")]
    public long FeeCalculatorId { get; set; }

    [Required]
    [Range(1, long.MaxValue, ErrorMessage = "Parent id must be supplied")]
    public long ParentId { get; set; }

    [Required]
    [Range(1, long.MaxValue, ErrorMessage = "Child id must be supplied")]
    public long ChildId { get; set; }

    [Required]
    public long MenuId { get; set; }

    [Required]
    public long OrderId { get; set; }

    [Required]
    [Range(0.0, double.MaxValue, ErrorMessage = "Fee cannot be negative")]
    public decimal FeeAmount { get; set; }

    [Required]
    [Range(0.0, double.MaxValue, ErrorMessage = "Order total cannot be negative")]
    public decimal OrderTotal { get; set; }

    [Required]
    public OrderType OrderType { get; set; }

    [Required]
    public DateTime OrderDate { get; set; }
}
