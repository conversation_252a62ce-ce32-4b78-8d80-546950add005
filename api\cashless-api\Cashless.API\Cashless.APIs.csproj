<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <!-- Ignore doc strings on public class members -->
    <NoWarn>CS1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>Cashless.APIs.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Cashless.API (DEV)' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Cashless.API (UAT)' " />
  <ItemGroup>
    <Folder Include="wwwroot\" />
    <Folder Include="Services\Fees\" />
    <Folder Include="Services\Fees\Calculators\" />
    <Folder Include="Validators\Fees\" />
    <Folder Include="Validators\Fees\Interfaces\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.SendGrid" Version="6.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
    <PackageReference Include="Azure.Messaging.EventGrid" Version="4.15.0" />
    <PackageReference Include="Datadog.Trace.Bundle" Version="3.3.1" />
    <PackageReference Include="MassTransit" Version="8.0.15" />
    <PackageReference Include="MassTransit.AmazonSQS" Version="8.0.15" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.300" />
    <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.300.64" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.300.66" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.8" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.5" />
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="6.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="6.0.10" />
    <PackageReference Include="Polly" Version="7.2.3" />
    <PackageReference Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
    <PackageReference Include="Microsoft.Azure.KeyVault.Core" Version="3.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.5" />
    <PackageReference Include="FirebaseAdmin" Version="2.3.0" />
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="72.1.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../Schools.BLL/Schools.BLL.csproj" />
    <!-- <ProjectReference Include="..\Schools.DAL\Schools.DAL.csproj" /> -->
  </ItemGroup>

  <ItemGroup>
    <None Remove="FirebaseAdmin" />
    <None Remove="Azure.Messaging.ServiceBus" />
    <None Remove="Services\Fees\" />
    <None Remove="Services\Fees\Calculators\" />
    <None Remove="Validators\Fees\" />
    <None Remove="Validators\Fees\Interfaces\" />
  </ItemGroup>
</Project>
