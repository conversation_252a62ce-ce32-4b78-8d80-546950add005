﻿using Microsoft.Extensions.DependencyInjection;
using Schools.BLL.Assemblers;
using Schools.BLL.Helpers;
using Schools.BLL.Services;
using Schools.BLL.Services.Calculators;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;

namespace Schools.BLL;

public static class ServiceCollectionExtension
{
        public static IServiceCollection AddBllServices(this IServiceCollection services)
        {
                // services
                services.AddTransient<ICanteenService, CanteenService>();
                services.AddTransient<ICategoryService, CategoryService>();
                services.AddTransient<ISchoolClassService, SchoolClassService>();
                services.AddTransient<IEditorService, EditorService>();
                services.AddTransient<IMenuService, MenuService>();
                services.AddTransient<INotificationService, NotificationService>();
                services.AddTransient<ISchoolEventService, EventService>();
                services.AddTransient<INoticeService, NoticeService>();
                services.AddTransient<IPrintingService, PrintingService>();
                services.AddTransient<IFirebaseService, FirebaseService>();
                services.AddTransient<IXeroService, XeroService>();
                services.AddTransient<IFileStorageService, AWSS3StorageService>();
                services.AddScoped<ISendGridService, SendGridService>();
                services.AddTransient<IAddressService, AddressService>();
                services.AddScoped<ISendGridService, SendGridService>();
                services.AddScoped<ILogicAppService, LogicAppService>();
                services.AddScoped<IAppVersionService, AppVersionService>();
                services.AddScoped<ITelemetryService, TelemetryService>();
                services.AddScoped<IScoreApiClient, ScoreApiClient>();
                services.AddScoped<IStripeService, StripeService>();
                services.AddScoped<ISchoolMerchantService, SchoolMerchantService>();
                services.AddScoped<ISchoolFeatureService, SchoolFeatureService>();
                services.AddTransient<ISchoolFeatureAssembler, SchoolFeatureAssembler>();
                services.AddTransient<IAuditService, AuditService>();
                services.AddTransient<IAuditCommonService, AuditCommonService>();
                services.AddTransient<ITransactionHistoryService, TransactionHistoryService>();
                services.AddScoped<ISupportedVersionsService, SupportedVersionsService>();
                services.AddScoped<IReconciliationService, ReconciliationService>();
                services.AddScoped<IFeatureFlagService, FeatureFlagService>();
                services.AddScoped<IInvoiceService, InvoiceService>();
                services.AddScoped<IPaymentResultProcessor, PaymentResultProcessor>();
                services.AddScoped<IBrazeApiClient, BrazeApiClient>();

                services.AddScoped<ISecurityService, SecurityService>();
                services.AddScoped<IExceptionHandlerService, ExceptionHandlerService>();
                services.AddScoped<IUserService, UserService>();
                services.AddTransient<IIntercomUserService, IntercomUserService>();
                services.AddTransient<IPaymentService, PaymentService>();
                services.AddTransient<IPaymentService2, PaymentService2>();
                services.AddTransient<IGuestPaymentService, GuestPaymentService>();
                services.AddTransient<IOrderService, OrderService>();
                services.AddTransient<IOrderStatusService, OrderStatusService>();
                services.AddTransient<IBillingService, BillingService>();
                services.AddTransient<ISchoolService, SchoolService>();
                services.AddTransient<IStocksService, StocksService>();
                services.AddTransient<ICanteenService, CanteenService>();
                services.AddTransient<ILaunchDarklyService, LaunchDarklyService>();
                services.AddTransient<IFrequentlyOrderedService, FrequentlyOrderedService>();

                // helper
                services.AddTransient<ICurrentUserHelper, CurrentUserHelper>();

                // validators
                services.AddScoped<IEditorValidator, EditorValidator>();
                services.AddScoped<INoticeValidator, NoticeValidator>();
                services.AddScoped<IOrderServiceValidator, OrderServiceValidator>();
                services.AddScoped<IPaymentValidator, PaymentValidator>();
                services.AddScoped<IAuthenticationValidator, AuthenticationValidator>();
                services.AddScoped<IOrderValidator, OrderValidator>();

                // Assembler
                services.AddTransient<IAddressAssembler, AddressAssembler>();

                // Fees Services
                services.AddScoped<IFeeApiSerice, FeeApiService>();
                services.AddTransient<IFeeService, FeeService>();
                services.AddTransient<IFeeCalculatorService, FeeCalculatorService>();
                services.AddTransient<IFeeCalculatorFactory, FeeCalculatorFactory>();
                services.AddTransient<IFeeValidator, FeeValidator>();

                // Food calculators
                services.AddTransient<DefaultRecessCalculator>()
                        .AddScoped<IFeeCalculator, DefaultRecessCalculator>(v => v.GetService<DefaultRecessCalculator>());
                services.AddTransient<NoChargeRecessCalculator>()
                        .AddScoped<IFeeCalculator, NoChargeRecessCalculator>(v => v.GetService<NoChargeRecessCalculator>());
                services.AddTransient<DefaultLunchCalculator>()
                        .AddScoped<IFeeCalculator, DefaultLunchCalculator>(v => v.GetService<DefaultLunchCalculator>());
                services.AddTransient<NoChargeLunchCalculator>()
                        .AddScoped<IFeeCalculator, NoChargeLunchCalculator>(v => v.GetService<NoChargeLunchCalculator>());

                // School Event calculators
                services.AddTransient<DefaultSchoolEventCalculator>()
                        .AddScoped<IFeeCalculator, DefaultSchoolEventCalculator>(v => v.GetService<DefaultSchoolEventCalculator>());

                // Uniform Calculators
                services.AddTransient<DefaultUniformCalculator>()
                        .AddScoped<IFeeCalculator, DefaultUniformCalculator>(v => v.GetService<DefaultUniformCalculator>());
                services.AddTransient<NoChargeUniformCalculator>()
                        .AddScoped<IFeeCalculator, NoChargeUniformCalculator>(v => v.GetService<NoChargeUniformCalculator>());

                return services;
        }
}