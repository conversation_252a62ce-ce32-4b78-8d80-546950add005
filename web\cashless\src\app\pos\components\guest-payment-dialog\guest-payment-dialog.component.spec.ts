import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of, throwError } from 'rxjs';

import { GuestPaymentDialogComponent } from './guest-payment-dialog.component';
import { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';
import { CashlessAppInsightsService } from '../../../sharedServices';
import {
  GuestPaymentDialogData,
  GuestPaymentResponse
} from '../../../sharedModels/guest-payment/guest-payment.models';

describe('GuestPaymentDialogComponent', () => {
  let component: GuestPaymentDialogComponent;
  let fixture: ComponentFixture<GuestPaymentDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<GuestPaymentDialogComponent>>;
  let mockGuestPaymentService: jasmine.SpyObj<GuestPaymentService>;
  let mockAppInsightsService: jasmine.SpyObj<CashlessAppInsightsService>;

  const mockDialogData: GuestPaymentDialogData = {
    orders: [
      {
        menuItemId: 1,
        quantity: 2,
        price: 5.50,
        name: 'Test Item',
        description: 'Test Description'
      }
    ],
    selectedStudent: {
      UserId: 123,
      FirstName: 'Test',
      Lastname: 'Guest',
      IsGuest: true
    },
    totalAmount: 11.00,
    canteenId: 1,
    menuId: 1,
    menuType: 'lunch',
    orderDate: '2024-01-15T00:00:00.000Z',
    viewType: 'merchant',
    guid: 'test-guid'
  };

  beforeEach(async () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close']);
    const guestPaymentServiceSpy = jasmine.createSpyObj('GuestPaymentService', [
      'processPaymentAndCreateOrder',
      'getExpiryMonths',
      'getExpiryYears'
    ]);
    const appInsightsServiceSpy = jasmine.createSpyObj('CashlessAppInsightsService', ['TrackEvent']);

    await TestBed.configureTestingModule({
      declarations: [GuestPaymentDialogComponent],
      imports: [
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatIconModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: dialogRefSpy },
        { provide: MAT_DIALOG_DATA, useValue: mockDialogData },
        { provide: GuestPaymentService, useValue: guestPaymentServiceSpy },
        { provide: CashlessAppInsightsService, useValue: appInsightsServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(GuestPaymentDialogComponent);
    component = fixture.componentInstance;
    mockDialogRef = TestBed.inject(MatDialogRef) as jasmine.SpyObj<MatDialogRef<GuestPaymentDialogComponent>>;
    mockGuestPaymentService = TestBed.inject(GuestPaymentService) as jasmine.SpyObj<GuestPaymentService>;
    mockAppInsightsService = TestBed.inject(CashlessAppInsightsService) as jasmine.SpyObj<CashlessAppInsightsService>;

    // Setup service method returns
    mockGuestPaymentService.getExpiryMonths.and.returnValue([
      { value: '01', label: '01' },
      { value: '02', label: '02' }
    ]);
    mockGuestPaymentService.getExpiryYears.and.returnValue([
      { value: '2024', label: '2024' },
      { value: '2025', label: '2025' }
    ]);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.paymentForm.get('cardNumber')?.value).toBe('');
    expect(component.paymentForm.get('expiryMonth')?.value).toBe('');
    expect(component.paymentForm.get('expiryYear')?.value).toBe('');
    expect(component.paymentForm.get('cvv')?.value).toBe('');
    expect(component.paymentForm.get('cardholderName')?.value).toBe('');
  });

  it('should initialize order data from dialog data', () => {
    expect(component.totalAmount).toBe(11.00);
    expect(component.orderItems).toEqual(mockDialogData.orders);
    expect(component.guestUserId).toBe(123);
    expect(component.canteenId).toBe(1);
  });

  it('should validate card number correctly', () => {
    // Valid Visa card number (test number)
    component.paymentForm.get('cardNumber')?.setValue('4111 1111 1111 1111');
    fixture.detectChanges();
    expect(component.cardNumberValid()).toBe(true);

    // Invalid card number
    component.paymentForm.get('cardNumber')?.setValue('1234 5678 9012 3456');
    fixture.detectChanges();
    expect(component.cardNumberValid()).toBe(false);
  });

  it('should validate expiry date correctly', () => {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    // Valid future date
    component.paymentForm.get('expiryMonth')?.setValue('12');
    component.paymentForm.get('expiryYear')?.setValue(nextYear.toString());
    component['validateExpiry']();
    expect(component.expiryValid()).toBe(true);

    // Invalid past date
    component.paymentForm.get('expiryMonth')?.setValue('01');
    component.paymentForm.get('expiryYear')?.setValue('2020');
    component['validateExpiry']();
    expect(component.expiryValid()).toBe(false);
  });

  it('should validate CVV correctly', () => {
    // Valid 3-digit CVV
    component.paymentForm.get('cvv')?.setValue('123');
    fixture.detectChanges();
    expect(component.cvvValid()).toBe(true);

    // Valid 4-digit CVV
    component.paymentForm.get('cvv')?.setValue('1234');
    fixture.detectChanges();
    expect(component.cvvValid()).toBe(true);

    // Invalid CVV
    component.paymentForm.get('cvv')?.setValue('12');
    fixture.detectChanges();
    expect(component.cvvValid()).toBe(false);
  });

  it('should validate cardholder name correctly', () => {
    // Valid name
    component.paymentForm.get('cardholderName')?.setValue('John Smith');
    fixture.detectChanges();
    expect(component.nameValid()).toBe(true);

    // Invalid short name
    component.paymentForm.get('cardholderName')?.setValue('J');
    fixture.detectChanges();
    expect(component.nameValid()).toBe(false);
  });

  it('should process payment successfully', async () => {
    // Setup valid form data
    component.paymentForm.patchValue({
      cardNumber: '4111 1111 1111 1111',
      expiryMonth: '12',
      expiryYear: '2025',
      cvv: '123',
      cardholderName: 'John Smith'
    });

    // Force validation signals to be true
    component.cardNumberValid.set(true);
    component.expiryValid.set(true);
    component.cvvValid.set(true);
    component.nameValid.set(true);

    const mockResponse: GuestPaymentResponse = {
      isSuccess: true,
      orderId: 'ORDER_123',
      message: 'Payment successful',
      errorCode: '',
      transactionId: 'TXN_123',
      processedAt: new Date().toISOString(),
      amountCharged: 11.00,
      paymentReference: 'REF_123'
    };

    mockGuestPaymentService.processPaymentAndCreateOrder.and.returnValue(of(mockResponse));

    await component.processPayment();

    expect(mockGuestPaymentService.processPaymentAndCreateOrder).toHaveBeenCalled();
    expect(component.paymentSuccess()).toBe(true);
    expect(mockAppInsightsService.TrackEvent).toHaveBeenCalledWith('GuestPaymentSuccess', jasmine.any(Object));
  });

  it('should handle payment failure', async () => {
    // Setup valid form data
    component.paymentForm.patchValue({
      cardNumber: '4111 1111 1111 1111',
      expiryMonth: '12',
      expiryYear: '2025',
      cvv: '123',
      cardholderName: 'John Smith'
    });

    // Force validation signals to be true
    component.cardNumberValid.set(true);
    component.expiryValid.set(true);
    component.cvvValid.set(true);
    component.nameValid.set(true);

    const mockResponse: GuestPaymentResponse = {
      isSuccess: false,
      orderId: '',
      message: 'Payment failed',
      errorCode: 'PAYMENT_DECLINED',
      transactionId: '',
      processedAt: new Date().toISOString(),
      amountCharged: 0,
      paymentReference: ''
    };

    mockGuestPaymentService.processPaymentAndCreateOrder.and.returnValue(of(mockResponse));

    await component.processPayment();

    expect(component.paymentSuccess()).toBe(false);
    expect(component.errorMessage()).toBe('Payment failed');
    expect(mockAppInsightsService.TrackEvent).toHaveBeenCalledWith('GuestPaymentFailure', jasmine.any(Object));
  });

  it('should handle network error', async () => {
    // Setup valid form data
    component.paymentForm.patchValue({
      cardNumber: '4111 1111 1111 1111',
      expiryMonth: '12',
      expiryYear: '2025',
      cvv: '123',
      cardholderName: 'John Smith'
    });

    // Force validation signals to be true
    component.cardNumberValid.set(true);
    component.expiryValid.set(true);
    component.cvvValid.set(true);
    component.nameValid.set(true);

    mockGuestPaymentService.processPaymentAndCreateOrder.and.returnValue(
      throwError(() => new Error('Network error'))
    );

    await component.processPayment();

    expect(component.paymentSuccess()).toBe(false);
    expect(component.errorMessage()).toBe('Network error occurred. Please try again.');
  });

  it('should close dialog with success result', () => {
    component.paymentSuccess.set(true);
    component.closeDialog();

    expect(mockDialogRef.close).toHaveBeenCalledWith({
      success: false,
      message: 'Payment cancelled'
    });
  });

  it('should format currency correctly', () => {
    const formatted = component.formatCurrency(11.50);
    expect(formatted).toContain('11.50');
    expect(formatted).toContain('$');
  });
});
