using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Orders;
using Schools.BLL.Helpers;
using Schools.DAL.Entities;
using Microsoft.IdentityModel.Tokens;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Assemblers;

public static class OrderMapper
{
    public static IEnumerable<PlaceOrderResponse> MapToPlaceOrderResponses(this IEnumerable<OrderEntity> source)
    {
        return MapMany(source, MapToPlaceOrderResponse);
    }

    public static PlaceOrderResponse MapToPlaceOrderResponse(this OrderEntity order)
    {
        return new PlaceOrderResponse()
        {
            OrderId = (int)order.OrderId,
            Error = string.IsNullOrWhiteSpace(order.ErrorMessage) ? "" : order.ErrorMessage,
            StudentId = (int)order.StudentId,
            OrderStatusId = order.OrderStatusId,
            OrderDate = order.OrderDate.ToString(),
            MenuType = order.MenuType
        };
    }

    public static PlaceOrderResponse MapToPlaceOrderResponse(this OrderView order)
    {
        return new PlaceOrderResponse()
        {
            OrderId = (int)order.OrderId,
            Error = string.IsNullOrWhiteSpace(order.ErrorMessage) ? "" : order.ErrorMessage,
            StudentId = (int)order.StudentId,
            OrderStatusId = order.OrderStatusId,
            OrderDate = order.OrderDate.ToString(),
            MenuType = order.MenuType
        };
    }

    private static IEnumerable<target> MapMany<source, target>(IEnumerable<source> input, Func<source, target> mapper)
    {
        var response = new List<target>();
        if (input == null || !input.Any())
        {
            return response;
        }

        foreach (var s in input)
        {
            response.Add(mapper(s));
        }

        return response;
    }
}