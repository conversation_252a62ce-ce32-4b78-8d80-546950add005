# Guest Payment Feature - Setup Guide

## Quick Start

This guide helps developers set up and test the guest payment feature in the POS module.

## Prerequisites

1. **Development Environment**:
   - .NET Core API project running
   - Angular frontend project running
   - Stripe test account configured
   - Database with guest users created

2. **Required Permissions**:
   - Merchant or Admin role for API access
   - Access to POS module in frontend

## Setup Steps

### 1. API Setup

**Verify Service Registration**:
```csharp
// In Schools.BLL/Extensions/DependencyInjection.cs
services.AddTransient<IGuestPaymentService, GuestPaymentService>();
```

**Check Controller Registration**:
- Ensure `GuestPaymentController.cs` is in the Controllers folder
- Verify `[CheckUserRole]` attributes are present

### 2. Frontend Setup

**Verify Module Registration**:
```typescript
// In pos.module.ts
import { GuestPaymentDialogComponent } from './components';

@NgModule({
  declarations: [
    // ... other components
    GuestPaymentDialogComponent,
  ],
  // ...
})
```

**Check Component Export**:
```typescript
// In pos/components/index.ts
export * from './guest-payment-dialog/guest-payment-dialog.component';
```

### 3. Database Setup

**Create Guest User** (if not exists):
```sql
INSERT INTO Users (FirstName, Lastname, Email, IsGuest, IsActive, SchoolId)
VALUES ('Guest', 'User', '<EMAIL>', 1, 1, [YOUR_SCHOOL_ID]);
```

## Testing the Feature

### 1. Basic Flow Test

1. **Login** as Merchant/Admin
2. **Navigate** to POS module
3. **Select** a guest user from dropdown
4. **Add items** to cart
5. **Click** "Pay with Card" button
6. **Fill** card details in dialog:
   - Card: `************** 1111` (test Visa)
   - Expiry: Any future date
   - CVV: `123`
   - Name: Any name
7. **Submit** payment
8. **Verify** success message and order creation

### 2. Error Scenarios

**Invalid Card Test**:
- Use card number: `1234 5678 9012 3456`
- Should show "Invalid card number" error

**Expired Card Test**:
- Use expiry date in the past
- Should show "Card has expired" error

**Rate Limiting Test**:
- Make 6+ payment attempts quickly
- Should show rate limit error

### 3. Security Tests

**Input Sanitization**:
- Try entering special characters in name field
- Should be sanitized automatically

**Network Error Simulation**:
- Disconnect network during payment
- Should show retry options

## API Testing

### Using Postman/Curl

**Validate Card Endpoint**:
```bash
POST /api/GuestPayment/ValidateCard
Content-Type: application/json
Authorization: Bearer [YOUR_TOKEN]

{
  "cardNumber": "****************",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "cvv": "123"
}
```

**Process Payment Endpoint**:
```bash
POST /api/GuestPayment/ProcessPaymentAndCreateOrder
Content-Type: application/json
Authorization: Bearer [YOUR_TOKEN]

{
  "cardNumber": "****************",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "cvv": "123",
  "cardholderName": "Test User",
  "amount": 10.50,
  "canteenId": 1,
  "guestUserId": 123,
  "items": [
    {
      "menuItemId": 1,
      "quantity": 1,
      "price": 10.50,
      "itemName": "Test Item",
      "itemDescription": "Test Description"
    }
  ],
  "orderDate": "2024-01-15T00:00:00.000Z",
  "menuId": 1,
  "menuType": "lunch"
}
```

## Common Issues & Solutions

### Issue: "Guest users cannot place orders"
**Solution**: Ensure you're clicking "Pay with Card" not "Pay with Spriggy"

### Issue: Rate limit errors
**Solution**: Wait 1 minute or restart application to clear memory cache

### Issue: Card validation fails
**Solution**: Use test card numbers:
- Visa: `************** 1111`
- Mastercard: `************** 4444`
- Amex: `3782 822463 10005`

### Issue: Permission denied
**Solution**: Ensure user has Merchant or Admin role

### Issue: Network timeout
**Solution**: Check API is running and accessible

## Development Tips

### Debugging

**Frontend Debugging**:
```typescript
// Add to guest-payment-dialog.component.ts
console.log('Payment request:', this.createPaymentRequest());
```

**Backend Debugging**:
```csharp
// Add to GuestPaymentService.cs
_logger.LogDebug("Processing payment for user {UserId}", request.GuestUserId);
```

### Testing with Mock Data

**Mock Successful Payment**:
```typescript
// In guest-payment.service.ts
return of({
  isSuccess: true,
  orderId: 'MOCK_ORDER_123',
  message: 'Mock payment successful'
});
```

**Mock Failed Payment**:
```typescript
return of({
  isSuccess: false,
  errorCode: 'PAYMENT_DECLINED',
  message: 'Mock payment failed'
});
```

## Configuration

### Environment Variables

**Development**:
```json
{
  "Stripe": {
    "PublishableKey": "pk_test_...",
    "SecretKey": "sk_test_..."
  },
  "RateLimit": {
    "PaymentAttemptsPerMinute": 5,
    "PaymentAttemptsPerHour": 20
  }
}
```

**Production**:
```json
{
  "Stripe": {
    "PublishableKey": "pk_live_...",
    "SecretKey": "sk_live_..."
  },
  "RateLimit": {
    "PaymentAttemptsPerMinute": 3,
    "PaymentAttemptsPerHour": 10
  }
}
```

## Monitoring

### Key Metrics to Monitor

1. **Payment Success Rate**: Track successful vs failed payments
2. **Rate Limit Hits**: Monitor rate limiting frequency
3. **Error Patterns**: Watch for common error codes
4. **Performance**: Track payment processing times

### Logging

**Important Log Events**:
- Guest payment attempts
- Rate limit violations
- Payment failures
- Security events

**Log Levels**:
- `Information`: Normal payment flow
- `Warning`: Rate limits, validation failures
- `Error`: Payment processing errors
- `Critical`: Security violations

## Support

### Troubleshooting Steps

1. **Check Logs**: Review application logs for errors
2. **Verify Configuration**: Ensure all services are registered
3. **Test API**: Use Postman to test endpoints directly
4. **Check Database**: Verify guest user exists and is active
5. **Network**: Ensure API is accessible from frontend

### Contact Information

For technical issues:
- Check implementation documentation
- Review error logs
- Test with provided test data
- Verify all setup steps completed

## Next Steps

After successful setup:
1. **Run Unit Tests**: Execute test suites to verify functionality
2. **Performance Testing**: Test with multiple concurrent users
3. **Security Review**: Verify all security measures are working
4. **User Acceptance Testing**: Test with real guest user scenarios
5. **Production Deployment**: Follow deployment checklist
