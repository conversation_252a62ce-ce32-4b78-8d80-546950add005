using System.Collections.Generic;
using Schools.BLL.Classes;
using Schools.DAL.Entities;

namespace Cashless.APIs.Hydrators
{
    /// <summary>
    /// This class provides a method to hydrate a list of item categories (DTO) from meny categories (db entity). 
    /// The reason for this implementation is so that we have full control over what is send to any frontend or mobile app user interface, with a clear separation to database entity models.
    /// This had benefits in terms of security, but the down-side is that there is extra coding overhead.
    /// </summary>
    public class ItemCategoryHydrator : IHydrator<List<MenuCategoryEntity>, List<ItemCategory>>
    {
        public List<ItemCategory> Hydrate(List<MenuCategoryEntity> menuCategories)
        {
            var itemCategories = new List<ItemCategory>();
            for (int i = 0; i < menuCategories.Count; ++i)
            {
                var menuCategory = menuCategories[i];

                itemCategories.Add(new ItemCategory
                {
                    MenuCategoryId = menuCategory.MenuCategoryId,
                    IsActive = menuCategory.IsActive,
                    SchoolId = menuCategory.SchoolId,
                    CanteenId = menuCategory.CanteenId,
                    CategoryName = menuCategory.CategoryName,
                    CategoryUrl = menuCategory.CategoryUrl,
                    SortOrder = menuCategory.SortOrder,
                    Description = menuCategory.Description,
                    SubDescription = menuCategory.SubDescription
                });
            }

            return itemCategories;
        }
    }
}