"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["default-src_app_manage-order_manage-order_module_ts"],{

/***/ 71950:
/*!****************************************************************************!*\
  !*** ./src/app/manage-order/components/base-filter-menu-date.component.ts ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseMenuDateFilterComponent: () => (/* binding */ BaseMenuDateFilterComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/states/children/children.selectors */ 54186);
/* harmony import */ var src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/states/family/family.selectors */ 61548);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);

// models





class BaseMenuDateFilterComponent {
  constructor(store) {
    this.store = store;
    this.isUniformOrder = false;
    this.isEventOrder = false;
  }
  OnInitFunction() {
    this.subscriptionStudent$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.select)(src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_1__.selectedChild)).subscribe(student => {
      this.student = student;
      this.schoolWeeksPreOrder = student?.SchoolWeeksPreOrder ? student.SchoolWeeksPreOrder : 3;
    });
    this.subscriptionDate$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_2__.datePickerSelect)).subscribe(date => {
      this.date = date;
    });
    this.subscriptionMenu$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_2__.MenuPickerSelect)).subscribe(menuType => {
      if (!this.menuType) {
        this.isUniformOrder = menuType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MenuTypeEnum.Uniform;
        this.isEventOrder = menuType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MenuTypeEnum.Event;
      }
      this.menuType = menuType;
    });
  }
  OnDestroyFunction() {
    this.subscriptionDate$?.unsubscribe();
    this.subscriptionStudent$?.unsubscribe();
    this.subscriptionMenu$?.unsubscribe();
  }
  static {
    this.ɵfac = function BaseMenuDateFilterComponent_Factory(t) {
      return new (t || BaseMenuDateFilterComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.Store));
    };
  }
  static {
    this.ɵdir = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineDirective"]({
      type: BaseMenuDateFilterComponent,
      inputs: {
        title: "title",
        isEdit: "isEdit"
      }
    });
  }
}

/***/ }),

/***/ 70900:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/category-icon/category-icon.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CategoryIconComponent: () => (/* binding */ CategoryIconComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class CategoryIconComponent {
  constructor() {
    this.ICON_PATH = 'assets/menuIcons';
    this.DEFAULT_ICON = 'default.svg';
    this.iconSource = `${this.ICON_PATH}/${this.DEFAULT_ICON}`;
  }
  ngOnChanges(simpleChanges) {
    const iconFileName = this.getIconName(simpleChanges.iconName.currentValue);
    this.iconSource = `${this.ICON_PATH}/${iconFileName}`;
  }
  getIconName(imageName) {
    const iconFileName = this.getImageNameFromFileName(imageName);
    return this.validCategoryName(iconFileName) ? `${iconFileName}.svg` : this.DEFAULT_ICON;
  }
  validCategoryName(imageName) {
    const allCategories = [...src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CANTEEN_CATEGORY_ICON_ARRAY, ...src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.UNIFORM_CATEGORY_ICON_ARRAY];
    return allCategories.includes(imageName);
  }
  getImageNameFromFileName(imageName) {
    return imageName ? imageName.replace('.jpg', '') : null;
  }
  static {
    this.ɵfac = function CategoryIconComponent_Factory(t) {
      return new (t || CategoryIconComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: CategoryIconComponent,
      selectors: [["category-icon"]],
      inputs: {
        iconName: "iconName"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵNgOnChangesFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵStandaloneFeature"]],
      decls: 1,
      vars: 1,
      consts: [["width", "50", 3, "src"]],
      template: function CategoryIconComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "img", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("src", ctx.iconSource, _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsanitizeUrl"]);
        }
      },
      styles: ["img[_ngcontent-%COMP%] {\n  pointer-events: none;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWFuYWdlLW9yZGVyL2NvbXBvbmVudHMvY2F0ZWdvcnktaWNvbi9jYXRlZ29yeS1pY29uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usb0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbImltZyB7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 76032:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/category-tile/category-tile.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CategoryTileComponent: () => (/* binding */ CategoryTileComponent)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../category-icon/category-icon.component */ 70900);





function CategoryTileComponent_p_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "p", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r0.name);
  }
}
const _c0 = function (a0) {
  return {
    selected: a0
  };
};
class CategoryTileComponent {
  constructor() {
    this.isSelected = false;
    this.clicked = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
  }
  onPress() {
    this.clicked.emit(this.iconName);
  }
  static {
    this.ɵfac = function CategoryTileComponent_Factory(t) {
      return new (t || CategoryTileComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: CategoryTileComponent,
      selectors: [["category-tile"]],
      inputs: {
        name: "name",
        iconName: "iconName",
        isSelected: "isSelected"
      },
      outputs: {
        clicked: "clicked"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 5,
      consts: [[1, "container", "mat-elevation-z2", "p-1", 3, "ngClass", "click"], [3, "iconName"], ["class", "m-0 pt-1 text", 4, "ngIf"], [1, "m-0", "pt-1", "text"]],
      template: function CategoryTileComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function CategoryTileComponent_Template_div_click_0_listener() {
            return ctx.onPress();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "category-icon", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, CategoryTileComponent_p_2_Template, 2, 1, "p", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](3, _c0, ctx.isSelected));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("iconName", ctx.iconName);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.name);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf, _category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_0__.CategoryIconComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.container[_ngcontent-%COMP%] {\n  margin: 0;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: -moz-fit-content;\n  width: fit-content;\n  cursor: pointer;\n  border-radius: 8px;\n  color: #ff7a00;\n  background-color: white;\n  border: 2px transparent solid;\n}\n.container.selected[_ngcontent-%COMP%] {\n  border: 2px #ff9e00 solid;\n}\n.container[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\n  width: 80px;\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  text-align: center;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 42580:
/*!******************************************************************************************!*\
  !*** ./src/app/manage-order/components/clear-cart-button/clear-cart-button.component.ts ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClearCartButtonComponent: () => (/* binding */ ClearCartButtonComponent)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/material/icon */ 93840);






function ClearCartButtonComponent_h4_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "h4", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function ClearCartButtonComponent_h4_0_Template_h4_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r1.buttonPressed());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, " Clear ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "mat-icon", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3, "remove_shopping_cart");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("inline", true);
  }
}
class ClearCartButtonComponent {
  constructor() {
    this.pressed = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
  }
  buttonPressed() {
    this.pressed.emit();
  }
  static {
    this.ɵfac = function ClearCartButtonComponent_Factory(t) {
      return new (t || ClearCartButtonComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: ClearCartButtonComponent,
      selectors: [["clear-cart-button"]],
      inputs: {
        showButton: "showButton"
      },
      outputs: {
        pressed: "pressed"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵStandaloneFeature"]],
      decls: 1,
      vars: 1,
      consts: [["class", "clearCart", 3, "click", 4, "ngIf"], [1, "clearCart", 3, "click"], ["aria-hidden", "false", "aria-label", "Remove from cart", 3, "inline"]],
      template: function ClearCartButtonComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](0, ClearCartButtonComponent_h4_0_Template, 4, 1, "h4", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.showButton);
        }
      },
      dependencies: [_angular_material_icon__WEBPACK_IMPORTED_MODULE_1__.MatIconModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_1__.MatIcon, _angular_common__WEBPACK_IMPORTED_MODULE_2__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf],
      styles: [".clearCart[_ngcontent-%COMP%] {\n  font-size: 18px;\n  text-align: right;\n  cursor: pointer;\n}\n.clearCart[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  vertical-align: middle;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWFuYWdlLW9yZGVyL2NvbXBvbmVudHMvY2xlYXItY2FydC1idXR0b24vY2xlYXItY2FydC1idXR0b24uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxlQUFBO0FBQ0Y7QUFDRTtFQUNFLHNCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuY2xlYXJDYXJ0IHtcbiAgZm9udC1zaXplOiAxOHB4O1xuICB0ZXh0LWFsaWduOiByaWdodDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICYgbWF0LWljb24ge1xuICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 73908:
/*!********************************************************************************************!*\
  !*** ./src/app/manage-order/components/dialog-place-order/dialog-place-order.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DialogPlaceOrderComponent: () => (/* binding */ DialogPlaceOrderComponent)
/* harmony export */ });
/* harmony import */ var D_projects_spriggy_git_spriggy_latest_web_cashless_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_states_family_family_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/states/family/family.actions */ 88189);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var src_app_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/states/shoppingCart/shopping-cart.actions */ 81860);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../shared/components/spinner/spinner.component */ 71517);
/* harmony import */ var _payment_components_top_up_form_top_up_form_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../payment/components/top-up-form/top-up-form.component */ 53890);
/* harmony import */ var _payment_components_top_up_choices_top_up_choices_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../payment/components/top-up-choices/top-up-choices.component */ 42836);
/* harmony import */ var _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../schools-button/components/primary-button/primary-button.component */ 58666);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _orders_details_orders_details_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../orders-details/orders-details.component */ 43830);
/* harmony import */ var _orders_placed_orders_placed_component__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../orders-placed/orders-placed.component */ 24100);
/* harmony import */ var _sharedPipes_money_button_display_pipe__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../sharedPipes/money-button-display.pipe */ 96329);
/* harmony import */ var _sharedPipes_place_order_button_text_pipe__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../sharedPipes/place-order-button-text.pipe */ 40796);



// Model






















function DialogPlaceOrderComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("click", function DialogPlaceOrderComponent_div_4_Template_div_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r5);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r4.closeDialog());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](1, "mat-icon", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](2, "close");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelement"](2, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
  }
}
function DialogPlaceOrderComponent_ng_container_5_h3_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "h3", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](1, "Order Details");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
}
function DialogPlaceOrderComponent_ng_container_5_h3_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "h3", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](1, "We are processing your order");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_8_payment_top_up_choices_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "payment-top-up-choices", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("choiceChanged", function DialogPlaceOrderComponent_ng_container_5_div_8_payment_top_up_choices_2_Template_payment_top_up_choices_choiceChanged_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r17);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r16.TopUpAmountChanged($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("chargeAmount", ctx_r11.topUpMinimumAmount())("isNestedTopUp", true);
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_8_p_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "p")(1, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtextInterpolate"](ctx_r12.errorMessage);
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_8_ng_container_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](1, "button", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("click", function DialogPlaceOrderComponent_ng_container_5_div_8_ng_container_6_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r19);
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r18.TopUpClick());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵpipe"](3, "moneyButtonDisplay");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("disabled", !ctx_r13.topUpAmount);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵpipeBind2"](3, 2, ctx_r13.topUpAmount, "Top up"), " ");
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_8_ng_template_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "primary-button", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("onPress", function DialogPlaceOrderComponent_ng_container_5_div_8_ng_template_7_Template_primary_button_onPress_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r20.confirmOrder());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵpipe"](1, "placeOrderButtonText");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("text", _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵpipeBind2"](1, 3, ctx_r15.editOrderId > 0, ctx_r15.totalPrice))("disabled", ctx_r15.canteenOrAdminInsufficientWalletBalance)("loading", ctx_r15.buttonLoading);
  }
}
function DialogPlaceOrderComponent_ng_container_5_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r23 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelement"](1, "app-orders-details", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](2, DialogPlaceOrderComponent_ng_container_5_div_8_payment_top_up_choices_2_Template, 1, 2, "payment-top-up-choices", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](3, DialogPlaceOrderComponent_ng_container_5_div_8_p_3_Template, 3, 1, "p", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](4, "button", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("click", function DialogPlaceOrderComponent_ng_container_5_div_8_Template_button_click_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r23);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r22.closeDialog());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](5, " Go back ");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](6, DialogPlaceOrderComponent_ng_container_5_div_8_ng_container_6_Template, 4, 5, "ng-container", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](7, DialogPlaceOrderComponent_ng_container_5_div_8_ng_template_7_Template, 2, 6, "ng-template", null, 18, _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵreference"](8);
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("createOrderSummary", ctx_r9.createOrderSummary)("editOrderSummary", ctx_r9.editOrderSummary)("totalFees", ctx_r9.totalFees)("accountBalance", ctx_r9.accountBalance);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r9.needToTopUp());
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r9.errorMessage);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r9.needToTopUp())("ngIfElse", _r14);
  }
}
function DialogPlaceOrderComponent_ng_container_5_app_orders_placed_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "app-orders-placed", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("goToOrders", function DialogPlaceOrderComponent_ng_container_5_app_orders_placed_9_Template_app_orders_placed_goToOrders_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r25);
      const ctx_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r24.GotToOrders());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
}
function DialogPlaceOrderComponent_ng_container_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](1, DialogPlaceOrderComponent_ng_container_5_div_1_Template, 3, 0, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](2, "div", 0)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](4, DialogPlaceOrderComponent_ng_container_5_h3_4_Template, 2, 0, "h3", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](5, DialogPlaceOrderComponent_ng_container_5_h3_5_Template, 2, 0, "h3", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](6, "div", 10)(7, "div", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](8, DialogPlaceOrderComponent_ng_container_5_div_8_Template, 9, 8, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](9, DialogPlaceOrderComponent_ng_container_5_app_orders_placed_9_Template, 1, 0, "app-orders-placed", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r1.orderPlaced);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", !ctx_r1.orderPlaced);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r1.orderPlaced);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", !ctx_r1.orderPlaced && !ctx_r1.summaryLoading);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx_r1.orderPlaced);
  }
}
function DialogPlaceOrderComponent_ng_container_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r27 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](1, "div", 10)(2, "div", 1)(3, "top-up-form", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵlistener"]("PaymentSucceed", function DialogPlaceOrderComponent_ng_container_6_Template_top_up_form_PaymentSucceed_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵrestoreView"](_r27);
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵresetView"](ctx_r26.closeTopUp());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](4, "p", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](5, " Your security is important to us. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](6, "a", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtext"](7, "Find out more");
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("topUpAmount", ctx_r2.topUpAmount)("isNestedTopUp", true)("userBalance", ctx_r2.accountBalance);
  }
}
function DialogPlaceOrderComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelement"](1, "app-spinner", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("manual", true);
  }
}
class DialogPlaceOrderComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_2__.BaseComponent {
  constructor(dialogRef, data, dialog, store, router, orderApiService, userService, location, payService, adminService, appInsightsService) {
    super();
    this.dialogRef = dialogRef;
    this.data = data;
    this.dialog = dialog;
    this.store = store;
    this.router = router;
    this.orderApiService = orderApiService;
    this.userService = userService;
    this.location = location;
    this.payService = payService;
    this.adminService = adminService;
    this.appInsightsService = appInsightsService;
    this.errorMessage = null;
    this.orderPlaced = false;
    this.isProcessing = false;
    this.isTopUp = false;
    this.sufficientWalletBalance = false;
    this.canteenOrAdminInsufficientWalletBalance = false;
    this.buttonLoading = false;
    this.totalFees = 0;
    this.createOrderSummary = null;
    this.editOrderSummary = null;
    this.feesToDisplay = [];
    this.summaryLoading = false;
    // POS-specific properties
    this.paymentMethod = 'spriggy';
    this.isPosOrder = false;
    this.placedOrderId = null;
    //error Messages
    this.insufficientFundsError = 'Sorry, this order cannot be completed due to insufficient funds in the user wallet.';
    this.cannotRetrieveFundsError = 'We are having an issue retrieving your balance, please contact support team.';
    this.outOfStockError = 'Sorry, one or more items in your order are out of stock and could not be processed.';
  }
  ngOnInit() {
    this.connectedUser = this.userService.GetUserConnected();
    this.editOrderId = this.data.editOrderId;
    this.orders = lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep(this.data.groupedCarts);
    this.getUpdatedWalletBalance();
    if (this.editOrderId) {
      this.getEditOrderSummaryAPI();
    } else {
      this.getOrderSummaryAPI();
    }
  }
  getUpdatedWalletBalance() {
    if (this.userService.IsCanteenOrAdmin()) {
      this.adminOrMerchantGetParentBalance();
      return;
    }
    this.getParentBalance();
  }
  adminOrMerchantGetParentBalance() {
    const parent = this.adminService.GetParent();
    this.accountBalance = +parent.SpriggyBalance;
  }
  getParentBalance() {
    this.payService.UpdateBalance();
    //gets updated user balance after top up
    this.subscriptionBalance$ = this.payService.SubscribeBalanceUpdate().subscribe({
      next: response => {
        this.accountBalance = response;
      },
      error: error => {
        this.handleErrorFromService(error);
        this.isProcessing = false;
      }
    });
  }
  ngOnDestroy() {
    this.subscriptionBalance$?.unsubscribe();
  }
  GetEditSummaryRequest(carts) {
    const orderData = carts.map(cartItem => {
      return this.getOrderItemSummary(cartItem);
    });
    return {
      OrderId: this.editOrderId,
      Items: orderData[0]
    };
  }
  topUpMinimumAmount() {
    return this.editOrderSummary ? this.editOrderSummary.priceDiff : this.createOrderSummary.totalAmount - this.createOrderSummary.balance;
  }
  getOrderSummaryRequest(carts) {
    const orderData = carts.map(cartItem => {
      return {
        OrderId: 0,
        StudentId: cartItem[0].studentId,
        OrderDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_4__.formatDateToUniversal)(cartItem[0].date),
        MenuId: cartItem[0].menuId,
        Items: this.getOrderItemSummary(cartItem)
      };
    });
    return {
      Orders: orderData
    };
  }
  getOrderItemSummary(order) {
    return order.map(orderItem => ({
      MenuItemId: orderItem.menuItemId,
      MenuItemOptionIds: this.getOrderItemOptionsSummary(orderItem.selectedOptions),
      Quantity: orderItem.quantity
    }));
  }
  getOrderItemOptionsSummary(selectedOptions) {
    if (selectedOptions?.length === 0) {
      return [];
    }
    return selectedOptions.map(option => option.menuItemOptionId);
  }
  getOrderSummaryAPI() {
    this.summaryLoading = true;
    const request = this.getOrderSummaryRequest(this.orders);
    this.orderApiService.getOrderSummary(request).subscribe({
      next: res => {
        this.createOrderSummary = res;
        this.totalFees = this.createOrderSummary.createOrdersInfo.reduce((prev, next) => prev + next.fee, 0);
        this.totalPrice = this.createOrderSummary.totalAmount + this.totalFees;
        this.summaryLoading = false;
        this.confirmSufficientUserBalance();
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      }
    });
  }
  getEditOrderSummaryAPI() {
    this.summaryLoading = true;
    const request = this.GetEditSummaryRequest(this.orders);
    this.orderApiService.getEditOrderSummary(request).subscribe({
      next: res => {
        this.editOrderSummary = res;
        this.summaryLoading = false;
        this.totalPrice = this.editOrderSummary.price;
      },
      error: error => {
        this.handleOrderSummaryApiError(error);
      }
    });
  }
  handleOrderSummaryApiError(error) {
    this.closeDialog(true);
    this.handleErrorFromService(error);
  }
  closeDialog(error = false) {
    return this.isTopUp ? this.closeTopUp() : this.dialogRef.close(error);
  }
  TopUpAmountChanged(newAmount) {
    this.topUpAmount = newAmount;
  }
  //////////////////////////////////////////////////
  // View
  //////////////////////////////////////////////////
  GotToOrders() {
    this.dialogRef.close();
    this.userService.IsCanteenOrAdmin() ? this.location.back() : this.router.navigate(['family/home']);
  }
  TopUpClick() {
    var _this = this;
    return (0,D_projects_spriggy_git_spriggy_latest_web_cashless_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.isTopUp = true;
    })();
  }
  closeTopUp() {
    this.isTopUp = false;
    this.topUpAmount = null;
  }
  confirmTopUp() {
    this.isTopUp = true;
  }
  /**
   * For by Admin/Canteen creating orders
   * Check if user balance is enough to complete payment after order fee is added
   */
  confirmSufficientUserBalance() {
    if (!this.userService.IsCanteenOrAdmin() || this.editOrderId) {
      return;
    }
    this.canteenOrAdminInsufficientWalletBalance = this.accountBalance < this.totalPrice;
    this.errorMessage = this.canteenOrAdminInsufficientWalletBalance ? this.insufficientFundsError : null;
  }
  //////////////////////////////////////////////////
  // Place order
  //////////////////////////////////////////////////
  confirmOrder() {
    this.appInsightsService.TrackEvent('ClickPlaceOrder', {
      Orders: JSON.stringify(this.orders)
    });
    this.buttonLoading = true;
    this.isProcessing = true;
    this.errorMessage = null;
    if (this.editOrderId) {
      this.placeEditedOrder();
    } else {
      this.placeNewOrder();
    }
  }
  placeNewOrder() {
    const request = this.getCreateOrdersRequest(this.orders);
    this.orderApiService.CreateOrders(request).subscribe({
      next: res => {
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      }
    });
  }
  placeEditedOrder() {
    const orderId = this.editOrderId;
    const request = {
      OrderId: orderId,
      Items: this.processOrderItems(this.orders[0])
    };
    this.orderApiService.EditOrder(request).subscribe({
      next: res => {
        this.orderSuccessApiResponse();
      },
      error: error => {
        this.orderErrorApiResponse(error);
      }
    });
  }
  orderSuccessApiResponse() {
    this.orderPlaced = true;
    this._clearCart();
    this.store.dispatch((0,src_app_states_family_family_actions__WEBPACK_IMPORTED_MODULE_3__.ClearDayDetail)());
    this.isProcessing = false;
    this.buttonLoading = false;
  }
  orderErrorApiResponse(error) {
    this.handleErrorFromService(error);
    this.orderPlaced = false;
    this.errorMessage = this.WriteError();
    this.isProcessing = false;
    this.buttonLoading = false;
  }
  needToTopUp() {
    return this.accountBalance < this.totalPrice && !this.userService.IsCanteenOrAdmin();
  }
  _clearCart() {
    this.store.dispatch((0,src_app_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_5__.clearAll)());
  }
  ///////////////////////
  // PLACE ORDER REQUEST
  ///////////////////////
  getEditOrderRequest(cartItems, orderId) {
    const groupedCartItems = this.groupCartItems(cartItems);
    return {
      OrderId: orderId,
      Items: this.processOrderItems(groupedCartItems[0])
    };
  }
  getCreateOrdersRequest(cartItems) {
    const groupedCartItems = this.groupCartItems(cartItems);
    const ordersRequestList = groupedCartItems.map(item => {
      return this.processOrders(item);
    });
    return {
      Orders: ordersRequestList
    };
  }
  groupCartItems(cartData) {
    return Object.values(cartData).map(cartItems => {
      return cartItems;
    });
  }
  processOrderItems(cartItems) {
    return cartItems.map(item => {
      return {
        MenuItemId: item.menuItemId,
        Quantity: item.quantity,
        MenuItemOptionIds: this.getSelectedOptionIds(item.selectedOptions)
      };
    });
  }
  processOrders(cartItems) {
    const itemList = this.processOrderItems(cartItems);
    const firstCartItem = cartItems[0];
    return {
      StudentId: firstCartItem.studentId,
      OrderDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_4__.formatDateToUniversal)(firstCartItem.date),
      MenuId: firstCartItem.menuId,
      Items: lodash__WEBPACK_IMPORTED_MODULE_1__.clone(itemList)
    };
  }
  getSelectedOptionIds(selectedOptions) {
    return selectedOptions.map(option => option.menuItemOptionId);
  }
  static {
    this.ɵfac = function DialogPlaceOrderComponent_Factory(t) {
      return new (t || DialogPlaceOrderComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MatDialogRef), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MAT_DIALOG_DATA), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_17__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_18__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__.OrderApiService), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_19__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__.PayService), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__.AdminService), _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_6__.CashlessAppInsightsService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵdefineComponent"]({
      type: DialogPlaceOrderComponent,
      selectors: [["family-dialog-place-order"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵInheritDefinitionFeature"]],
      decls: 8,
      vars: 4,
      consts: [[1, "row", "no-gutters"], [1, "col-12"], ["class", "modalClose noBackground closeButton", 3, "click", 4, "ngIf"], [4, "ngIf"], ["class", "spinnerDialog", 4, "ngIf"], [1, "modalClose", "noBackground", "closeButton", 3, "click"], ["matTooltip", "Close modal"], ["class", "row no-gutters", 4, "ngIf"], [1, "col-12", "headerContainer"], ["class", "titleDialog", 4, "ngIf"], [1, "row", "no-gutters", "paddingLine"], [3, "goToOrders", 4, "ngIf"], [1, "closeButtonFake"], [1, "titleDialog"], [3, "createOrderSummary", "editOrderSummary", "totalFees", "accountBalance"], [3, "chargeAmount", "isNestedTopUp", "choiceChanged", 4, "ngIf"], ["mat-flat-button", "", "type", "button", 1, "SecondaryButton", "cancelButton", 3, "click"], [4, "ngIf", "ngIfElse"], ["confirmOrderButton", ""], [3, "chargeAmount", "isNestedTopUp", "choiceChanged"], ["type", "button", "id", "top-up-click-button", 1, "PrimaryButton", 3, "disabled", "click"], ["id", "create-purchase-button", 3, "text", "disabled", "loading", "onPress"], [3, "goToOrders"], [3, "topUpAmount", "isNestedTopUp", "userBalance", "PaymentSucceed"], [1, "securityLink"], ["href", "https://intercom.help/spriggyschools/en/articles/3297670-is-spriggy-schools-safe", "target", "_blank"], [1, "spinnerDialog"], [3, "manual"]],
      template: function DialogPlaceOrderComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](0, "mat-dialog-content");
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerStart"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementStart"](2, "div", 0)(3, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](4, DialogPlaceOrderComponent_div_4_Template, 3, 0, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](5, DialogPlaceOrderComponent_ng_container_5_Template, 10, 5, "ng-container", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](6, DialogPlaceOrderComponent_ng_container_6_Template, 8, 3, "ng-container", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵtemplate"](7, DialogPlaceOrderComponent_div_7_Template, 2, 1, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", !ctx.orderPlaced);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", !ctx.isProcessing && !ctx.isTopUp);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", !ctx.isProcessing && ctx.isTopUp);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_15__["ɵɵproperty"]("ngIf", ctx.isProcessing || ctx.summaryLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_19__.NgIf, _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_7__.SpinnerComponent, _payment_components_top_up_form_top_up_form_component__WEBPACK_IMPORTED_MODULE_8__.TopUpFormComponent, _payment_components_top_up_choices_top_up_choices_component__WEBPACK_IMPORTED_MODULE_9__.TopUpChoicesComponent, _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_10__.PrimaryButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_20__.MatError, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MatDialogContent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_21__.MatIcon, _angular_material_button__WEBPACK_IMPORTED_MODULE_22__.MatButton, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__.MatTooltip, _orders_details_orders_details_component__WEBPACK_IMPORTED_MODULE_11__.OrdersDetailsComponent, _orders_placed_orders_placed_component__WEBPACK_IMPORTED_MODULE_12__.OrdersPlacedComponent, _sharedPipes_money_button_display_pipe__WEBPACK_IMPORTED_MODULE_13__.MoneyButtonDisplayPipe, _sharedPipes_place_order_button_text_pipe__WEBPACK_IMPORTED_MODULE_14__.PlaceOrderButtonTextPipe],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n\n\nmat-dialog-content[_ngcontent-%COMP%] {\n  overflow: inherit;\n  padding: 0;\n  margin: 0;\n}\n\nh4[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\nh4.totalOrder[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\nh4[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.headerContainer[_ngcontent-%COMP%] {\n  height: auto;\n}\n\n.closeButton[_ngcontent-%COMP%] {\n  margin-top: 5px;\n  margin-left: 5px;\n}\n\n.closeButtonFake[_ngcontent-%COMP%] {\n  height: 60px;\n}\n\n.paddingLine[_ngcontent-%COMP%] {\n  padding-top: 5px;\n  padding-bottom: 5px;\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n.titleDialog[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 24px;\n  margin-bottom: 15px;\n  margin-top: 0px;\n}\n\n.subHeader[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.itemList[_ngcontent-%COMP%] {\n  font-style: italic;\n}\n\n.chargeDesc[_ngcontent-%COMP%] {\n  font-style: italic;\n  margin-top: 5px;\n}\n\n.warning[_ngcontent-%COMP%] {\n  padding-bottom: 24px;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n}\n\n.securityLink[_ngcontent-%COMP%] {\n  padding-top: 10px;\n}\n@media (max-width: 767px) {\n  .securityLink[_ngcontent-%COMP%] {\n    padding-bottom: 70px;\n  }\n}\n\n.feesList[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n}\n\n.detailList[_ngcontent-%COMP%] {\n  list-style: none;\n  margin-top: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%] {\n  padding-left: 0px;\n  margin-top: 20px;\n  margin-bottom: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #ff7a00;\n}\n\n.walletBalance[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  margin-bottom: 5px;\n  font-family: \"bariol_bold\";\n}\n.walletBalance[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 47258:
/*!********************************************************************************************!*\
  !*** ./src/app/manage-order/components/edit-order-details/edit-order-details.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EditOrderDetailsComponent: () => (/* binding */ EditOrderDetailsComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);


class EditOrderDetailsComponent {
  static {
    this.ɵfac = function EditOrderDetailsComponent_Factory(t) {
      return new (t || EditOrderDetailsComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: EditOrderDetailsComponent,
      selectors: [["edit-order-details"]],
      inputs: {
        editOrderSummary: "editOrderSummary"
      },
      decls: 10,
      vars: 6,
      consts: [[1, "subHeader"], [1, "totalOrder"]],
      template: function EditOrderDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "h4");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, " Original order: ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "span", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](4, "currency");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "h4", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, " New order: ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "span", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](9, "currency");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](4, 2, ctx.editOrderSummary.previousPrice + ctx.editOrderSummary.fee));
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](9, 4, ctx.editOrderSummary.price + ctx.editOrderSummary.fee));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.CurrencyPipe],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n\n\nmat-dialog-content[_ngcontent-%COMP%] {\n  overflow: inherit;\n  padding: 0;\n  margin: 0;\n}\n\nh4[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\nh4.totalOrder[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\nh4[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.headerContainer[_ngcontent-%COMP%] {\n  height: auto;\n}\n\n.closeButton[_ngcontent-%COMP%] {\n  margin-top: 5px;\n  margin-left: 5px;\n}\n\n.closeButtonFake[_ngcontent-%COMP%] {\n  height: 60px;\n}\n\n.paddingLine[_ngcontent-%COMP%] {\n  padding-top: 5px;\n  padding-bottom: 5px;\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n.titleDialog[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 24px;\n  margin-bottom: 15px;\n  margin-top: 0px;\n}\n\n.subHeader[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.itemList[_ngcontent-%COMP%] {\n  font-style: italic;\n}\n\n.chargeDesc[_ngcontent-%COMP%] {\n  font-style: italic;\n  margin-top: 5px;\n}\n\n.warning[_ngcontent-%COMP%] {\n  padding-bottom: 24px;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n}\n\n.securityLink[_ngcontent-%COMP%] {\n  padding-top: 10px;\n}\n@media (max-width: 767px) {\n  .securityLink[_ngcontent-%COMP%] {\n    padding-bottom: 70px;\n  }\n}\n\n.feesList[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n}\n\n.detailList[_ngcontent-%COMP%] {\n  list-style: none;\n  margin-top: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%] {\n  padding-left: 0px;\n  margin-top: 20px;\n  margin-bottom: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #ff7a00;\n}\n\n.walletBalance[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  margin-bottom: 5px;\n  font-family: \"bariol_bold\";\n}\n.walletBalance[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 52340:
/*!****************************************************************************************************!*\
  !*** ./src/app/manage-order/components/filter-menu-date-sheet/filter-menu-date-sheet.component.ts ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FilterMenuDateSheetComponent: () => (/* binding */ FilterMenuDateSheetComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var _base_filter_menu_date_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-filter-menu-date.component */ 71950);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_select_date_select_date_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/select-date/select-date.component */ 85269);
/* harmony import */ var _shared_components_select_menu_type_select_menu_type_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/select-menu-type/select-menu-type.component */ 57005);









function FilterMenuDateSheetComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 2)(1, "div", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "select-date", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("noShadow", true)("selectedDate", ctx_r0.date)("preOrderWeeksNumber", ctx_r0.schoolWeeksPreOrder);
  }
}
class FilterMenuDateSheetComponent extends _base_filter_menu_date_component__WEBPACK_IMPORTED_MODULE_0__.BaseMenuDateFilterComponent {
  constructor(_bottomSheetRef, dateService, data, store) {
    super(store);
    this._bottomSheetRef = _bottomSheetRef;
    this.data = data;
    this.store = store;
  }
  ngOnInit() {
    this.menuType = this.data.menuType;
    this.OnInitFunction();
  }
  CloseSheet() {
    this._bottomSheetRef.dismiss();
  }
  ngOnDestroy() {
    this.OnDestroyFunction();
  }
  static {
    this.ɵfac = function FilterMenuDateSheetComponent_Factory(t) {
      return new (t || FilterMenuDateSheetComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_5__.MatBottomSheetRef), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.DateTimeService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_5__.MAT_BOTTOM_SHEET_DATA), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_6__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: FilterMenuDateSheetComponent,
      selectors: [["filter-menu-date-sheet"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 12,
      vars: 4,
      consts: [[1, "row"], [1, "col-12"], [1, "row", "rowSheet"], ["label", "Ordering", 3, "noShadow", "menuType", "schoolId"], ["class", "row rowSheet", 4, "ngIf"], [1, "row", "no-gutters", "paddingLine", "sheetButton"], ["type", "button", 1, "PrimaryButton", 3, "click"], ["label", "For", 3, "noShadow", "selectedDate", "preOrderWeeksNumber"]],
      template: function FilterMenuDateSheetComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "Menu & Date");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 2)(5, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "select-menu-type", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, FilterMenuDateSheetComponent_div_7_Template, 3, 3, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 5)(9, "div", 1)(10, "button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function FilterMenuDateSheetComponent_Template_button_click_10_listener() {
            return ctx.CloseSheet();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, "Done");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("noShadow", true)("menuType", ctx.menuType)("schoolId", ctx.student == null ? null : ctx.student.SchoolId);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.menuType != "Event");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _shared_components_select_date_select_date_component__WEBPACK_IMPORTED_MODULE_2__.SelectDateComponent, _shared_components_select_menu_type_select_menu_type_component__WEBPACK_IMPORTED_MODULE_3__.SelectMenuTypeComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  margin-bottom: 40px;\n  margin-top: 5px;\n}\n\n.rowSheet[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.sheetButton[_ngcontent-%COMP%] {\n  margin-bottom: 200px;\n}\n\n.errorContainer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  flex-direction: row;\n  gap: 10px;\n  margin-bottom: 20px;\n  margin-top: 25px;\n}\n\n.errorText[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 24px;\n  text-align: center;\n  color: #4f4f4f;\n  margin: 0;\n  margin-top: 9px;\n}\n\n.spinner[_ngcontent-%COMP%] {\n  justify-content: center;\n  padding-top: 100px;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  left: 0;\n  background-color: rgba(79, 79, 79, 0.4);\n  align-self: center;\n  display: flex;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 44066:
/*!****************************************************************************************!*\
  !*** ./src/app/manage-order/components/filters-items/filters-items-sheet.component.ts ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FiltersItemsSheetComponent: () => (/* binding */ FiltersItemsSheetComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);

// models








function FiltersItemsSheetComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 6)(1, "div", 1)(2, "mat-checkbox", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("ngModelChange", function FiltersItemsSheetComponent_div_4_Template_mat_checkbox_ngModelChange_2_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r3);
      const filter_r1 = restoredCtx.$implicit;
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](filter_r1.selected = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const filter_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngModel", filter_r1.selected);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](filter_r1.title);
  }
}
/** Sheet Filter component */
class FiltersItemsSheetComponent {
  constructor(_bottomSheetRef, filtersService, data) {
    this._bottomSheetRef = _bottomSheetRef;
    this.filtersService = filtersService;
    this.data = data;
    this.availableFilters = [];
    //active filter = filter option the canteen has enabled
    this.getActiveFilters = (deactivatedFilters, previouslySelectedFilters) => {
      if (deactivatedFilters === '') {
        return this.getAllMenuFilterOptions(previouslySelectedFilters);
      }
      return this.getActiveMenuFilterOptions(deactivatedFilters, previouslySelectedFilters);
    };
  }
  ngOnInit() {
    const previouslySelectedFilters = this.filtersService.GetSelectedFilterOptions();
    this.availableFilters = this.getActiveFilters(this.data, previouslySelectedFilters);
    this.filtersService.SetFilterOptions(this.availableFilters);
  }
  clickFilter() {
    const filtersActive = this.areAnyFiltersSelected();
    this.filtersService.SetSelectedFilterOptions(filtersActive);
    this._bottomSheetRef.dismiss(filtersActive?.length);
  }
  clearAllSelectedFilters() {
    this.availableFilters.map(filter => filter.selected = false);
  }
  areAnyFiltersSelected() {
    return this.availableFilters?.filter(filter => filter.selected);
  }
  getActiveMenuFilterOptions(deactivatedFilters, previouslySelectedFilters) {
    const availableFilters = [];
    const arrayDeactivatedFilters = deactivatedFilters.split(',');
    src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MENU_FILTERS.forEach(initFilter => {
      if (this.filterHasBeenDeactivated(arrayDeactivatedFilters, initFilter)) {
        return;
      }
      availableFilters.push(this.getFilterCheckboxObject(initFilter, previouslySelectedFilters));
    });
    return availableFilters;
  }
  getAllMenuFilterOptions(previouslySelectedFilters) {
    return src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MENU_FILTERS.map(initFilter => {
      return this.getFilterCheckboxObject(initFilter, previouslySelectedFilters);
    });
  }
  getFilterCheckboxObject(initFilter, previouslySelectedFilters) {
    return {
      ...initFilter,
      selected: this.isFilterSelected(initFilter, previouslySelectedFilters)
    };
  }
  isFilterSelected(filter, previouslySelectedFilters) {
    const index = previouslySelectedFilters?.findIndex(prevFilter => prevFilter.code === filter.code);
    return index >= 0;
  }
  filterHasBeenDeactivated(arrayDeactivatedFilters, menuFilter) {
    const indexFound = arrayDeactivatedFilters.find(filter => filter === menuFilter.deactivateName);
    return Boolean(indexFound);
  }
  static {
    this.ɵfac = function FiltersItemsSheetComponent_Factory(t) {
      return new (t || FiltersItemsSheetComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_3__.MatBottomSheetRef), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.ItemsFilterService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_3__.MAT_BOTTOM_SHEET_DATA));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: FiltersItemsSheetComponent,
      selectors: [["filters-items-sheet"]],
      decls: 11,
      vars: 1,
      consts: [[1, "row"], [1, "col-12"], [0, "trackby", "filter.code", "", "class", "row rowSheet", 4, "ngFor", "ngForOf"], [1, "row", "no-gutters", "paddingLine", "sheetButton"], ["mat-flat-button", "", "type", "button", 1, "SecondaryButton", 3, "click"], ["type", "button", 1, "PrimaryButton", 3, "click"], [0, "trackby", "filter.code", "", 1, "row", "rowSheet"], [3, "ngModel", "ngModelChange"]],
      template: function FiltersItemsSheetComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Filters");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, FiltersItemsSheetComponent_div_4_Template, 4, 2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 3)(6, "div", 1)(7, "button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function FiltersItemsSheetComponent_Template_button_click_7_listener() {
            return ctx.clearAllSelectedFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8, " Clear all ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function FiltersItemsSheetComponent_Template_button_click_9_listener() {
            return ctx.clickFilter();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, "Apply");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.availableFilters);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgModel, _angular_material_button__WEBPACK_IMPORTED_MODULE_6__.MatButton, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_7__.MatCheckbox],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.iconFilter[_ngcontent-%COMP%] {\n  padding: 8px;\n  height: 40px;\n  width: 40px;\n  background-color: white;\n  border-radius: 50px;\n  margin-top: 5px;\n  cursor: pointer;\n}\n.iconFilter.activeFilter[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  margin-bottom: 40px;\n  margin-top: 5px;\n}\n\n.rowSheet[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.sheetButton[_ngcontent-%COMP%] {\n  margin-bottom: 80px;\n}\n@media (min-width: 767px) {\n  .sheetButton[_ngcontent-%COMP%] {\n    margin-bottom: 20px;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 42992:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/filters-items/filters-items.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FiltersItemsComponent: () => (/* binding */ FiltersItemsComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _filters_items_sheet_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./filters-items-sheet.component */ 44066);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
// models

// components






const _c0 = function (a0) {
  return {
    activeFilter: a0
  };
};
function FiltersItemsComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div")(1, "mat-icon", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function FiltersItemsComponent_div_0_Template_mat_icon_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r1.ClickIconFilter());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "tune");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction1"](1, _c0, ctx_r0.hasActiveSettings));
  }
}
class FiltersItemsComponent {
  constructor(_bottomSheet) {
    this._bottomSheet = _bottomSheet;
    this.isUniformOrder = false;
  }
  ngOnInit() {
    this.isUniformOrder = this.menuType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MenuTypeEnum.Uniform;
  }
  ClickIconFilter() {
    let bottomSheetRef = this._bottomSheet.open(_filters_items_sheet_component__WEBPACK_IMPORTED_MODULE_1__.FiltersItemsSheetComponent, {
      data: this.deactivatedFilters
    });
    bottomSheetRef.afterDismissed().subscribe(result => {
      this.hasActiveSettings = result;
    });
  }
  static {
    this.ɵfac = function FiltersItemsComponent_Factory(t) {
      return new (t || FiltersItemsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_3__.MatBottomSheet));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: FiltersItemsComponent,
      selectors: [["filters-items"]],
      inputs: {
        deactivatedFilters: "deactivatedFilters",
        menuType: "menuType"
      },
      decls: 1,
      vars: 1,
      consts: [[4, "ngIf"], ["matTooltip", "Filters", "matTooltip", "Filters", 1, "iconFilter", "mat-elevation-z3", 3, "ngClass", "click"]],
      template: function FiltersItemsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](0, FiltersItemsComponent_div_0_Template, 3, 3, "div", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.isUniformOrder);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_material_icon__WEBPACK_IMPORTED_MODULE_5__.MatIcon, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_6__.MatTooltip],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.iconFilter[_ngcontent-%COMP%] {\n  padding: 8px;\n  height: 40px;\n  width: 40px;\n  background-color: white;\n  border-radius: 50px;\n  margin-top: 5px;\n  cursor: pointer;\n}\n.iconFilter.activeFilter[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  margin-bottom: 40px;\n  margin-top: 5px;\n}\n\n.rowSheet[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.sheetButton[_ngcontent-%COMP%] {\n  margin-bottom: 80px;\n}\n@media (min-width: 767px) {\n  .sheetButton[_ngcontent-%COMP%] {\n    margin-bottom: 20px;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 18768:
/*!**********************************************************************!*\
  !*** ./src/app/manage-order/components/filters/filters.component.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FiltersComponent: () => (/* binding */ FiltersComponent)
/* harmony export */ });
/* harmony import */ var _base_filter_menu_date_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-filter-menu-date.component */ 71950);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_select_date_select_date_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared/components/select-date/select-date.component */ 85269);
/* harmony import */ var _shared_components_select_menu_type_select_menu_type_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/select-menu-type/select-menu-type.component */ 57005);
/* harmony import */ var _shared_components_children_list_children_list_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/children-list/children-list.component */ 16917);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
// models








function FiltersComponent_div_6_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "select-menu-type", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("noShadow", true)("menuType", ctx_r2.menuType)("schoolId", ctx_r2.student == null ? null : ctx_r2.student.SchoolId);
  }
}
function FiltersComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "div", 2)(2, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "children-list", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, FiltersComponent_div_6_div_4_Template, 2, 3, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "select-date", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("noShadow", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r0.menuType);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("noShadow", true)("selectedDate", ctx_r0.date)("preOrderWeeksNumber", ctx_r0.schoolWeeksPreOrder);
  }
}
function FiltersComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 2)(1, "div", 9)(2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "You can't change the selection when editing an order");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
}
class FiltersComponent extends _base_filter_menu_date_component__WEBPACK_IMPORTED_MODULE_0__.BaseMenuDateFilterComponent {
  constructor(store) {
    super(store);
    this.store = store;
  }
  ngOnInit() {
    this.OnInitFunction();
  }
  ngOnDestroy() {
    this.OnDestroyFunction();
  }
  showOrderFilters() {
    const notCanteenOrder = !(this.isUniformOrder || this.isEventOrder);
    return !this.isEdit && notCanteenOrder;
  }
  static {
    this.ɵfac = function FiltersComponent_Factory(t) {
      return new (t || FiltersComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_5__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: FiltersComponent,
      selectors: [["order-filters"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 8,
      vars: 3,
      consts: [[4, "ngIf"], ["class", "row", 4, "ngIf"], [1, "row"], [1, "d-none", "d-md-block", "col-md-6", "col-lg-4", "noPadding"], ["label", "Child", 3, "noShadow"], ["class", "col-12 col-md-12 col-lg-4 noPadding", 4, "ngIf"], [1, "col-12", "col-md-12", "col-lg-4", "noPadding"], ["label", "For", 3, "noShadow", "selectedDate", "preOrderWeeksNumber"], ["label", "Ordering", 3, "noShadow", "menuType", "schoolId"], [1, "col-12"]],
      template: function FiltersComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-accordion")(1, "mat-expansion-panel")(2, "mat-expansion-panel-header")(3, "mat-panel-title");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "mat-panel-description");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, FiltersComponent_div_6_Template, 7, 5, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, FiltersComponent_div_7_Template, 4, 0, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx.title, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showOrderFilters());
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.isEdit);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _shared_components_select_date_select_date_component__WEBPACK_IMPORTED_MODULE_1__.SelectDateComponent, _shared_components_select_menu_type_select_menu_type_component__WEBPACK_IMPORTED_MODULE_2__.SelectMenuTypeComponent, _shared_components_children_list_children_list_component__WEBPACK_IMPORTED_MODULE_3__.ChildrenListComponent, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatAccordion, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanel, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanelHeader, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanelTitle, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_7__.MatExpansionPanelDescription],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\nmat-panel-title[_ngcontent-%COMP%] {\n  color: #333b44;\n  font-family: \"bariol_bold\";\n}\n\n.row[_ngcontent-%COMP%]   .noPadding[_ngcontent-%COMP%] {\n  padding: 0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 3386:
/*!**************************************************!*\
  !*** ./src/app/manage-order/components/index.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DialogPlaceOrderComponent: () => (/* reexport safe */ _dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_4__.DialogPlaceOrderComponent),
/* harmony export */   FilterMenuDateSheetComponent: () => (/* reexport safe */ _filter_menu_date_sheet_filter_menu_date_sheet_component__WEBPACK_IMPORTED_MODULE_7__.FilterMenuDateSheetComponent),
/* harmony export */   FiltersComponent: () => (/* reexport safe */ _filters_filters_component__WEBPACK_IMPORTED_MODULE_0__.FiltersComponent),
/* harmony export */   FiltersItemsComponent: () => (/* reexport safe */ _filters_items_filters_items_component__WEBPACK_IMPORTED_MODULE_8__.FiltersItemsComponent),
/* harmony export */   FiltersItemsSheetComponent: () => (/* reexport safe */ _filters_items_filters_items_sheet_component__WEBPACK_IMPORTED_MODULE_9__.FiltersItemsSheetComponent),
/* harmony export */   ManageOrderComponent: () => (/* reexport safe */ _manage_order_manage_order_component__WEBPACK_IMPORTED_MODULE_1__.ManageOrderComponent),
/* harmony export */   OrdersDetailsComponent: () => (/* reexport safe */ _orders_details_orders_details_component__WEBPACK_IMPORTED_MODULE_5__.OrdersDetailsComponent),
/* harmony export */   OrdersPlacedComponent: () => (/* reexport safe */ _orders_placed_orders_placed_component__WEBPACK_IMPORTED_MODULE_6__.OrdersPlacedComponent),
/* harmony export */   ReorderFilterComponent: () => (/* reexport safe */ _reorder_filter_reorder_filter_component__WEBPACK_IMPORTED_MODULE_10__.ReorderFilterComponent),
/* harmony export */   SelectedOrderHistoryComponent: () => (/* reexport safe */ _selected_order_history_selected_order_history_component__WEBPACK_IMPORTED_MODULE_2__.SelectedOrderHistoryComponent),
/* harmony export */   ShoppingCartComponent: () => (/* reexport safe */ _shopping_cart_shopping_cart_component__WEBPACK_IMPORTED_MODULE_3__.ShoppingCartComponent)
/* harmony export */ });
/* harmony import */ var _filters_filters_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./filters/filters.component */ 18768);
/* harmony import */ var _manage_order_manage_order_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./manage-order/manage-order.component */ 78346);
/* harmony import */ var _selected_order_history_selected_order_history_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./selected-order-history/selected-order-history.component */ 26000);
/* harmony import */ var _shopping_cart_shopping_cart_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./shopping-cart/shopping-cart.component */ 11576);
/* harmony import */ var _dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dialog-place-order/dialog-place-order.component */ 73908);
/* harmony import */ var _orders_details_orders_details_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./orders-details/orders-details.component */ 43830);
/* harmony import */ var _orders_placed_orders_placed_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./orders-placed/orders-placed.component */ 24100);
/* harmony import */ var _filter_menu_date_sheet_filter_menu_date_sheet_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./filter-menu-date-sheet/filter-menu-date-sheet.component */ 52340);
/* harmony import */ var _filters_items_filters_items_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./filters-items/filters-items.component */ 42992);
/* harmony import */ var _filters_items_filters_items_sheet_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./filters-items/filters-items-sheet.component */ 44066);
/* harmony import */ var _reorder_filter_reorder_filter_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./reorder-filter/reorder-filter.component */ 12598);












/***/ }),

/***/ 78346:
/*!********************************************************************************!*\
  !*** ./src/app/manage-order/components/manage-order/manage-order.component.ts ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageOrderComponent: () => (/* binding */ ManageOrderComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../environments/environment */ 45312);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! rxjs */ 19999);
/* harmony import */ var _states_shoppingCart_shopping_cart_selectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../states/shoppingCart/shopping-cart.selectors */ 88225);
/* harmony import */ var src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/states/family/family.selectors */ 61548);
/* harmony import */ var _states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../states/shoppingCart/shopping-cart.actions */ 81860);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _filter_menu_date_sheet_filter_menu_date_sheet_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../filter-menu-date-sheet/filter-menu-date-sheet.component */ 52340);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/app/states/children/children.selectors */ 54186);
/* harmony import */ var _functions_menu_item_sort_helper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../functions/menu-item-sort-helper */ 67412);
/* harmony import */ var _functions_calculate_price__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../functions/calculate-price */ 87255);
/* harmony import */ var _functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../functions/convert-to-cart-items */ 7366);
/* harmony import */ var src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! src/app/utility/timezone-helper */ 92471);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../shared/components/spinner/spinner.component */ 71517);
/* harmony import */ var _shared_components_item_item_component__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../../shared/components/item/item.component */ 1285);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _category_tile_category_tile_component__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../category-tile/category-tile.component */ 76032);
/* harmony import */ var _shopping_cart_shopping_cart_component__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../shopping-cart/shopping-cart.component */ 11576);
/* harmony import */ var _filters_filters_component__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../filters/filters.component */ 18768);
/* harmony import */ var _filters_items_filters_items_component__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../filters-items/filters-items.component */ 42992);



// Ngrx





// Models

// Components





















function ManageOrderComponent_filters_items_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelement"](0, "filters-items", 14);
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("deactivatedFilters", ctx_r0.deactivatedFilters);
  }
}
function ManageOrderComponent_filters_items_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelement"](0, "filters-items", 14);
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("deactivatedFilters", ctx_r1.deactivatedFilters);
  }
}
function ManageOrderComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelement"](1, "app-spinner", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("manual", true);
  }
}
function ManageOrderComponent_ng_template_13_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div")(1, "div", 1)(2, "div", 19)(3, "p", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtextInterpolate"](ctx_r6.noMenuMessage);
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_div_0_li_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "li", 25)(1, "category-tile", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵlistener"]("click", function ManageOrderComponent_ng_template_13_ng_template_1_div_0_li_3_Template_category_tile_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵrestoreView"](_r15);
      const cat_r12 = restoredCtx.$implicit;
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](4);
      return _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵresetView"](ctx_r14.SetCategory(cat_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const cat_r12 = ctx.$implicit;
    const i_r13 = ctx.index;
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵpropertyInterpolate1"]("id", "menu-category-", i_r13, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("name", cat_r12.CatName)("iconName", cat_r12.CatUrl)("isSelected", ctx_r11.IsCurrentCategory(cat_r12));
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div", 1)(1, "div", 22)(2, "ul", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](3, ManageOrderComponent_ng_template_13_ng_template_1_div_0_li_3_Template, 2, 4, "li", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngForOf", ctx_r9.currentMenu.MenuJSON);
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div", 33)(1, "mat-card", 34)(2, "product-item", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵlistener"]("clickItem", function ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_div_6_Template_product_item_clickItem_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](4);
      return _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵresetView"](ctx_r20.AddToCart($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const item_r18 = ctx.$implicit;
    const i_r19 = ctx.index;
    const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵpropertyInterpolate1"]("id", "product-item-", i_r19, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("category", ctx_r16.currentCategoryToDisplay)("item", item_r18)("dateOrder", ctx_r16.selectedOrderDate)("currentMenuType", ctx_r16.selectedMenuType)("schoolCutOffTime", ctx_r16.menuCutOffTime);
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div", 36)(1, "div", 37)(2, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtext"](3, "No items for this category");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtext"](5, "Try changing your filters");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()()();
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](1, "div", 27)(2, "div", 28)(3, "h4", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](5, "div", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](6, ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_div_6_Template, 3, 6, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](7, ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_div_7_Template, 6, 0, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](8, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelement"](9, "shopping-cart");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtextInterpolate"](ctx_r10.currentCategoryToDisplay.CatName);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngForOf", ctx_r10.currentCategoryToDisplay.item);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx_r10.currentCategoryToDisplay.item && ctx_r10.currentCategoryToDisplay.item.length == 0);
  }
}
function ManageOrderComponent_ng_template_13_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](0, ManageOrderComponent_ng_template_13_ng_template_1_div_0_Template, 4, 1, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](1, ManageOrderComponent_ng_template_13_ng_template_1_ng_container_1_Template, 10, 3, "ng-container", 13);
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx_r8.currentMenu == null ? null : ctx_r8.currentMenu.MenuJSON);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx_r8.currentCategoryToDisplay);
  }
}
function ManageOrderComponent_ng_template_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](0, ManageOrderComponent_ng_template_13_div_0_Template, 5, 1, "div", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](1, ManageOrderComponent_ng_template_13_ng_template_1_Template, 2, 2, "ng-template", null, 18, _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplateRefExtractor"]);
  }
  if (rf & 2) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵreference"](2);
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx_r4.noMenuMessage)("ngIfElse", _r7);
  }
}
function ManageOrderComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div")(1, "div", 27)(2, "div", 36)(3, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵpipe"](5, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtextInterpolate1"](" Go to Cart (", _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵpipeBind1"](5, 1, ctx_r5.priceCart()), ") ");
  }
}
class ManageOrderComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.BaseComponent {
  constructor(store, location, _bottomSheet, itemsFiltersService, payService, orderApiService, debounceService, menuService, userService) {
    super();
    this.store = store;
    this.location = location;
    this._bottomSheet = _bottomSheet;
    this.itemsFiltersService = itemsFiltersService;
    this.payService = payService;
    this.orderApiService = orderApiService;
    this.debounceService = debounceService;
    this.menuService = menuService;
    this.userService = userService;
    this.menuLoading = (0,_angular_core__WEBPACK_IMPORTED_MODULE_21__.signal)(true);
    this.GENERIC_ERROR_MESSAGE = 'Something went wrong, Please try again';
    this.titlePage = (0,_angular_core__WEBPACK_IMPORTED_MODULE_21__.signal)('');
    this.titlePageMobile = (0,_angular_core__WEBPACK_IMPORTED_MODULE_21__.signal)('');
    this.itemsFilters = [];
    this.showMobilePlaceOrder = false;
    this.priceCart = (0,_angular_core__WEBPACK_IMPORTED_MODULE_21__.signal)(0);
    this.menuTypeEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum;
    this.noMenuMessage = null;
    this.shoppingCart = [];
    this.loadMenuDebounce = this.debounceService.callDebounce(this.refreshMenu, 350, false, true);
  }
  ngOnInit() {
    this.IsAdminOrMerchant = this.userService.IsCanteenOrAdmin();
    this.itemsFiltersService.SetSelectedFilterOptions(null); //removed selected menu filters from last order
    this.orderFilterSubscription$ = (0,rxjs__WEBPACK_IMPORTED_MODULE_22__.combineLatest)([this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_3__.MenuPickerSelect)), this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_8__.selectedChild)), this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_3__.datePickerSelect))]).subscribe(([menuType, student, date]) => {
      this.orderFilterChange(menuType, student, date);
    });
    this.subscriptionMenuName$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_3__.MenuNameSelect)).subscribe(menuName => {
      this.menuName = menuName;
      this.setPageTitle();
    });
    this.subscriptionDayDetail$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_3__.dayDetail)).subscribe(dayDetail => {
      this.menuId = dayDetail.MenuId;
      this.orderToEdit = dayDetail?.OrderToEdit ? dayDetail.OrderToEdit : null;
      this.menuCutOffTime = dayDetail?.CutOffDate ? dayDetail.CutOffDate.toString() : null;
    });
    // filters management
    this.itemsFilters = this.itemsFiltersService.GetSelectedFilterOptions();
    this.subscriptionItemsFilters$ = this.itemsFiltersService.filtersUpdatedEvent$.subscribe(filters => {
      this.itemsFilters = filters;
      this.FilterItems();
    });
    this.subscriptionShoppingCart$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.select)(_states_shoppingCart_shopping_cart_selectors__WEBPACK_IMPORTED_MODULE_2__.getCartItems)).subscribe(cartItems => {
      this.showMobilePlaceOrder = cartItems.length > 0;
      this.priceCart.set((0,_functions_calculate_price__WEBPACK_IMPORTED_MODULE_10__.GetCartItemsPrice)(cartItems));
    });
    // refresh balance
    this.payService.UpdateBalance();
  }
  orderFilterChange(menuType, student, date) {
    if (!this.selectedStudent?.UserId || student?.UserId !== this.selectedStudent?.UserId) {
      this.deactivatedFilters = student?.SchoolDeactivatedFilters || null;
    }
    this.selectedStudent = student;
    this.selectedOrderDate = date;
    this.selectedMenuType = menuType;
    this.loadMenuDebounce();
    this.setPageTitle();
  }
  convertWeekDays(openingDays) {
    const days = openingDays?.split(',');
    return days?.map(day => src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.WeekDayAbbreviation[day]) || [];
  }
  refreshMenu() {
    if (!this.selectedMenuType || !this.selectedStudent || !this.selectedOrderDate) {
      return;
    }
    this.menuLoading.set(true);
    this.currentMenu = null;
    if (this.orderToEdit) {
      this.loadMenu();
      return;
    }
    this.canteenMenuAvailableCheck();
  }
  canteenMenuAvailableCheck() {
    const request = {
      studentId: this.selectedStudent.UserId,
      orderDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_7__.ConvertToUniversalDateFormat)(this.selectedOrderDate),
      menuType: this.selectedMenuType
    };
    this.orderApiService.GetOrderByStudentOrderDateAndMenuType(request).subscribe({
      next: res => {
        this.processPreMenuCheck(res);
      },
      error: error => {
        this.menuLoading.set(false);
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.handleErrorFromService(error);
      }
    });
  }
  processPreMenuCheck(res) {
    if (!res) {
      this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
    }
    if (this.isSchoolClosedForCanteenOrders(res)) {
      this.setNoMenuMessage(`Sorry, the canteen's closed right now`);
      return;
    }
    if (this.orderAlreadyPlaced(res)) {
      const message = `You have already placed an Order for: ${this.menuName} - ${this.getFormattedDate()}`;
      this.setNoMenuMessage(message);
      return;
    }
    this.loadMenu();
  }
  orderAlreadyPlaced(res) {
    if (this.selectedMenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Event) {
      return this.eventOrderAlreadyPlaced(res);
    }
    const isUniformOrder = this.selectedMenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Uniform;
    return !isUniformOrder && Boolean(res?.order);
  }
  eventOrderAlreadyPlaced(res) {
    //check existing order matches current menuId - this is to cover same day school event cases
    return res?.order?.some(order => order.MenuId === this.menuId);
  }
  isSchoolClosedForCanteenOrders(res) {
    const isDayClosed = this.isSchoolDayClosed(this.selectedStudent.SchoolOpeningDays);
    const schoolIsClosed = isDayClosed || res?.isSchoolClosed;
    return this.isCanteenOrder() && schoolIsClosed;
  }
  isCanteenOrder() {
    return this.selectedMenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Recess || this.selectedMenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Lunch;
  }
  getFormattedDate() {
    return moment__WEBPACK_IMPORTED_MODULE_1__(this.selectedOrderDate).format('dddd Do MMMM');
  }
  setNoMenuMessage(message) {
    this.noMenuMessage = message;
    this.menuLoading.set(false);
  }
  isSchoolDayClosed(openDays) {
    if (!openDays) {
      return false;
    }
    const schoolDays = this.convertWeekDays(this.selectedStudent.SchoolOpeningDays);
    return schoolDays.findIndex(openDay => openDay === moment__WEBPACK_IMPORTED_MODULE_1__(this.selectedOrderDate).format('ddd')) < 0;
  }
  loadMenu() {
    if (!this.selectedMenuType || !this.selectedStudent) {
      return;
    }
    this.menuService.GetMenuBySchoolAndType(this.selectedStudent.SchoolId, this.selectedMenuType).subscribe({
      next: res => {
        this.menuLoading.set(false);
        const menuToDisplay = this.menuDataExists(res) ? this.getMenuToDisplay(res) : null;
        if (menuToDisplay && menuToDisplay?.MenuJSON) {
          this.processMenuResult(menuToDisplay);
          return;
        }
        this.noMenuMessage = `No ${this.menuName} Menu Available`;
      },
      error: error => {
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.menuLoading.set(false);
        this.handleErrorFromService(error);
      }
    });
  }
  processMenuResult(menuResult) {
    this.noMenuMessage = null;
    this.currentMenu = this.addStockToMenu(menuResult);
    this.menuCutOffTime = this.getMenuCutOffTime();
    this.SetCategory(this.currentMenu.MenuJSON[0]);
    this.FilterItems();
    if (this.selectedMenuType == this.menuTypeEnum.Uniform) {
      this.itemsFiltersService.SetSelectedFilterOptions(null);
    }
    if (this.orderToEdit) {
      this._InitEditCartInShoppingCartState();
    }
  }
  addStockToMenu(menuResult) {
    menuResult.MenuJSON = menuResult.MenuJSON.map(category => {
      return {
        ...category,
        item: this.addStockToCategoryItems(category, menuResult.StocksJSON)
      };
    });
    return menuResult;
  }
  addStockToCategoryItems(category, menuStockData) {
    return category.item.map(item => {
      return this.addStockToMenuItem(item, menuStockData);
    });
  }
  addStockToMenuItem(item, menuStockData) {
    const stockIndex = menuStockData?.findIndex(stock => stock.MenuItemId === item.MenuItemId);
    if (stockIndex >= 0) {
      const stock = menuStockData[stockIndex];
      item.Stocks = stock.Stocks;
    }
    return item;
  }
  menuDataExists(menuRes) {
    return menuRes && menuRes?.length > 0;
  }
  getMenuToDisplay(menuData) {
    //If we know exactly what menu we want, find it, if not use the default
    return menuData.find(menu => menu.MenuId === this.menuId) || menuData[0];
  }
  setPageTitle() {
    this.titlePageMobile.set(`${this.menuName} - ${moment__WEBPACK_IMPORTED_MODULE_1__(this.selectedOrderDate).format('ddd - D/MM')}`);
    this.titlePage.set(`${this.selectedStudent.FirstName} - ${this.titlePageMobile()}`);
  }
  getMenuCutOffTime() {
    return this.selectedMenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Event ? moment__WEBPACK_IMPORTED_MODULE_1__(this.menuCutOffTime).format() // the event cut off has already been converted to local time in selected-order-history
    : this.getCanteenCutOffTime();
  }
  getCanteenCutOffTime() {
    const dateTime = (0,src_app_utility__WEBPACK_IMPORTED_MODULE_7__.AddTimeToDate)(this.selectedOrderDate, this.currentMenu.CutOffTime);
    return (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_12__.convertSchoolDateTimeToLocalDateTime)(dateTime, this.selectedStudent.SchoolTimeZoneOffSetHours);
  }
  /** Filter the items for the current Category */
  FilterItems() {
    this.currentCategoryToDisplay = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.Category();
    this.currentCategoryToDisplay.item = [];
    const selectedFiltersList = this.itemsFilters?.filter(filter => filter.selected);
    // filter items only if the filters are activated
    if (this.currentCategory && selectedFiltersList?.length) {
      this.filterCurrentMenuCategory(selectedFiltersList);
    } else {
      // if no filter display the complete items list
      this.currentCategoryToDisplay = this.currentCategory;
    }
  }
  filterCurrentMenuCategory(selectedFiltersList) {
    this.currentCategory.item.forEach(menuItem => {
      if (this.hasFilters(selectedFiltersList, menuItem)) {
        this.currentCategoryToDisplay.item.push(menuItem);
      }
    });
  }
  hasFilters(selectedFiltersList, menuItem) {
    let shouldInclude = true;
    selectedFiltersList.map(filter => {
      const itemDoesNotHaveSelectedFilter = !menuItem[filter.code];
      if (itemDoesNotHaveSelectedFilter) {
        shouldInclude = false;
      }
    });
    return shouldInclude;
  }
  ngOnDestroy() {
    this.subscriptionShoppingCart$?.unsubscribe();
    this.subscriptionDayDetail$?.unsubscribe();
    this.subscriptionItemsFilters$?.unsubscribe();
    this.subscriptionMenuName$?.unsubscribe();
    this.orderFilterSubscription$?.unsubscribe();
  }
  //////////////////////////////////////////////
  // Functions
  /////////////////////////////////////////////
  GetUrlCategory(large) {
    const imageUrl = large ? src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.ImageUrlEnum.MenusLG : src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.ImageUrlEnum.MenusSM;
    return _environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.blobStorage + imageUrl + this.currentCategory.CatUrl;
  }
  UniformCategoriesChanged(catName) {
    let cat = this.currentMenu.MenuJSON.find(x => x.CatName == catName);
    this.SetCategory(cat);
  }
  SetCategory(category) {
    this.currentCategory = category;
    this.FilterItems();
  }
  IsCurrentCategory(cat) {
    return this.currentCategory?.CatName == cat.CatName;
  }
  AddToCart(item) {
    const cartItem = this.convertMenuItemToCartItem(item);
    this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_4__.addToCart({
      cartItem
    }));
  }
  convertMenuItemToCartItem(item) {
    const options = item?.SelectedOptions.map(option => {
      return {
        menuItemOptionId: option.MenuItemOptionId,
        optionName: option.OptionName,
        optionCost: option.OptionCost,
        parentOptionId: option.MenuItemOptionsCategoryId
      };
    });
    return {
      date: this.selectedOrderDate,
      studentId: this.selectedStudent.UserId,
      studentName: this.selectedStudent.FirstName,
      schoolId: this.selectedStudent.SchoolId,
      menuType: this.selectedMenuType,
      menuName: this.menuName,
      menuId: this.currentMenu.MenuId,
      menuCutOffDateTime: this.menuCutOffTime,
      canteenId: this.currentMenu.CanteenId,
      itemCartId: moment__WEBPACK_IMPORTED_MODULE_1__().unix(),
      menuItemId: item.MenuItemId,
      name: item.Name,
      itemPriceIncGst: item.ItemPriceIncGst,
      selectedOptions: options || [],
      quantity: item.Quantity
    };
  }
  GoBackClick() {
    this.location.back();
  }
  /** Show Menu & Date Filters */
  ShowMenusFilters() {
    let dataSheet = {
      menuType: this.selectedMenuType,
      orderDate: this.selectedOrderDate
    };
    this._bottomSheet.open(_filter_menu_date_sheet_filter_menu_date_sheet_component__WEBPACK_IMPORTED_MODULE_6__.FilterMenuDateSheetComponent, {
      data: dataSheet
    });
  }
  _InitEditCartInShoppingCartState() {
    //match edited orders items with menu items
    let updatedOrder = this.updateOrderItemsWithMenuData(this.currentMenu.MenuJSON);
    const cartItems = updatedOrder.Items.forEach((item, index) => {
      const cartItemToAdd = (0,_functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_11__.ConvertOrderItemToCartType)(this.menuCutOffTime, updatedOrder, index, this.selectedStudent.FirstName);
      this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_4__.addToEditCart({
        cartItem: cartItemToAdd
      }));
      this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_4__.addToCart({
        cartItem: cartItemToAdd
      }));
    });
    return cartItems;
  }
  updateOrderItemsWithMenuData(menuJSON) {
    const order = this.orderToEdit;
    const originalOrderItems = order.Items;
    const orderItemIdList = originalOrderItems.map(item => item.MenuItemId);
    const matchingMenuItems = (0,_functions_menu_item_sort_helper__WEBPACK_IMPORTED_MODULE_9__.GetMenuItemsThatMatchOrderItemMenuId)(menuJSON, orderItemIdList);
    const updatedOrderItems = originalOrderItems.map(x => {
      const match = matchingMenuItems.find(menuItem => menuItem.MenuItemId === x.MenuItemId);
      if (match) {
        return {
          ...x,
          Name: match.Name,
          ItemPriceIncGst: match.Price
        };
      }
    });
    return {
      ...order,
      Items: updatedOrderItems
    };
  }
  disableOrderFilters() {
    const isEventOrUniformOrder = this.selectedMenuType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Event || this.selectedMenuType == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_5__.MenuTypeEnum.Uniform;
    return isEventOrUniformOrder || Boolean(this.orderToEdit);
  }
  static {
    this.ɵfac = function ManageOrderComponent_Factory(t) {
      return new (t || ManageOrderComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_23__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_24__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_25__.MatBottomSheet), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.ItemsFilterService), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.PayService), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.OrderApiService), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.DebounceService), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.MenuService), _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_13__.UserService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdefineComponent"]({
      type: ManageOrderComponent,
      selectors: [["family-manage-order"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵInheritDefinitionFeature"]],
      decls: 17,
      vars: 9,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-9"], [1, "d-block", "d-md-none", 3, "enableClick", "text", "navBack", "clicked"], [1, "d-none", "d-md-block", 3, "navBack"], [1, "col-3", "d-block", "d-md-none", "mobileFilterCol"], [3, "deactivatedFilters", 4, "ngIf"], [1, "d-none", "d-md-block", "col-md-8", "col-lg-8", "col-xl-7", "offset-xl-1"], [3, "title", "isEdit"], [1, "d-none", "d-md-block", "col-md-2"], ["class", "col-12 d-flex justify-content-center pt-4", 4, "ngIf", "ngIfElse"], ["result", ""], [1, "d-block", "d-md-none"], [4, "ngIf"], [3, "deactivatedFilters"], [1, "col-12", "d-flex", "justify-content-center", "pt-4"], [3, "manual"], [4, "ngIf", "ngIfElse"], ["menu", ""], [1, "col-12", "col-md-8", "col-lg-8", "col-xl-7", "offset-xl-1"], [1, "noMenuAvailable"], ["class", "row", 4, "ngIf"], [1, "col-12", "col-md-8", "col-lg-8", "col-xl-7", "offset-xl-1", "itemsContainers"], [1, "listMenu", "scrolling-horizontal-wrapper"], ["class", "mr-2", 0, "trackBy", "cat.CategoryId", "", 3, "id", 4, "ngFor", "ngForOf"], [0, "trackBy", "cat.CategoryId", "", 1, "mr-2", 3, "id"], [3, "name", "iconName", "isSelected", "click"], [1, "row", "noMarginRight"], [1, "col-12", "col-md-8", "col-lg-8", "col-xl-7", "offset-xl-1", "categoryContainers"], [1, "title"], ["class", "col-12 col-lg-6 item", 4, "ngFor", "ngForOf"], ["class", "col-12", 4, "ngIf"], [1, "d-none", "d-md-block", "col-md-4", "col-lg-4"], [1, "col-12", "col-lg-6", "item"], ["appearance", "outlined", 1, "card"], [3, "category", "item", "dateOrder", "currentMenuType", "schoolCutOffTime", "id", "clickItem"], [1, "col-12"], [1, "noItemAfterFilter"], ["routerLink", "../shoppingCart", 1, "placeOrderMobileLink"]],
      template: function ManageOrderComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "nav-back-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵlistener"]("navBack", function ManageOrderComponent_Template_nav_back_button_navBack_3_listener() {
            return ctx.GoBackClick();
          })("clicked", function ManageOrderComponent_Template_nav_back_button_clicked_3_listener() {
            return ctx.ShowMenusFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](4, "nav-back-button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵlistener"]("navBack", function ManageOrderComponent_Template_nav_back_button_navBack_4_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](6, ManageOrderComponent_filters_items_6_Template, 1, 1, "filters-items", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](7, "div", 1)(8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelement"](9, "order-filters", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](10, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](11, ManageOrderComponent_filters_items_11_Template, 1, 1, "filters-items", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](12, ManageOrderComponent_div_12_Template, 2, 1, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](13, ManageOrderComponent_ng_template_13_Template, 3, 2, "ng-template", null, 11, _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementStart"](15, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵtemplate"](16, ManageOrderComponent_div_16_Template, 6, 3, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵreference"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("enableClick", !ctx.disableOrderFilters())("text", ctx.titlePageMobile());
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx.selectedMenuType !== ctx.menuTypeEnum.Uniform);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("title", ctx.titlePage())("isEdit", ctx.orderToEdit);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx.selectedMenuType !== ctx.menuTypeEnum.Uniform);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx.menuLoading())("ngIfElse", _r3);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵproperty"]("ngIf", ctx.showMobilePlaceOrder);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_24__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_24__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_26__.RouterLink, _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_14__.SpinnerComponent, _shared_components_item_item_component__WEBPACK_IMPORTED_MODULE_15__.ItemComponent, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_16__.NavBackButtonComponent, _angular_material_card__WEBPACK_IMPORTED_MODULE_27__.MatCard, _category_tile_category_tile_component__WEBPACK_IMPORTED_MODULE_17__.CategoryTileComponent, _shopping_cart_shopping_cart_component__WEBPACK_IMPORTED_MODULE_18__.ShoppingCartComponent, _filters_filters_component__WEBPACK_IMPORTED_MODULE_19__.FiltersComponent, _filters_items_filters_items_component__WEBPACK_IMPORTED_MODULE_20__.FiltersItemsComponent, _angular_common__WEBPACK_IMPORTED_MODULE_24__.CurrencyPipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\nform[_ngcontent-%COMP%] {\n  display: inline-block;\n}\n\nlabel[_ngcontent-%COMP%] {\n  display: inline-block;\n  width: 50px;\n}\n\n.cashlessSelect[_ngcontent-%COMP%] {\n  display: inline-block;\n  padding-left: 4px;\n  padding-right: 8px;\n  background-color: white;\n  border: 1px solid #dddddd;\n  border-radius: 30px;\n  background-image: url('arrow_down.png');\n  background-repeat: no-repeat;\n  background-position: right center;\n  background-position-x: 95%;\n  background-position-y: 35%;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n.cashlessSelect.noShadow[_ngcontent-%COMP%] {\n  box-shadow: none;\n  border: none;\n}\n.cashlessSelect[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\n  border: none;\n  background-color: transparent;\n  vertical-align: middle;\n  padding: 5px;\n  padding-right: 20px; \n \n\n  appearance: none;\n  width: 170px;\n  font-size: 18px;\n  font-family: \"bariol_regular\";\n  color: #333b44;\n}\n.cashlessSelect[_ngcontent-%COMP%]   select.bold[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n.cashlessSelect[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\n  outline: none;\n}\n.cashlessSelect[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  vertical-align: middle;\n  color: #ff7a00;\n}\n\nh3[_ngcontent-%COMP%] {\n  margin-left: 20px;\n  vertical-align: middle;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  margin: 10px 10px 16px 6px;\n  padding: 0;\n}\n\n.listMenu[_ngcontent-%COMP%] {\n  margin: 0;\n  margin-bottom: 8px;\n  padding: 14px 0;\n}\n@media (max-width: 576px) {\n  .listMenu[_ngcontent-%COMP%] {\n    margin-top: 0px;\n    margin-bottom: 5px;\n  }\n}\n.listMenu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: inline-block;\n  text-decoration: none;\n}\n@media (max-width: 767px) {\n  .listMenu[_ngcontent-%COMP%]   .categoryContainers[_ngcontent-%COMP%] {\n    padding: 0;\n    padding-left: 10px;\n  }\n}\n\n@media (max-width: 767px) {\n  .itemsContainers[_ngcontent-%COMP%] {\n    padding: 0;\n  }\n}\n\n@media (min-width: 767px) {\n  .item[_ngcontent-%COMP%] {\n    margin-bottom: 14px;\n  }\n}\n@media (max-width: 767px) {\n  .item[_ngcontent-%COMP%] {\n    padding: 0;\n  }\n}\n.item[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\n  height: auto;\n  cursor: pointer;\n  padding: 0;\n}\n\n.noItemAfterFilter[_ngcontent-%COMP%] {\n  padding-left: 10px;\n}\n.noItemAfterFilter[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 18px;\n  color: #ff7a00;\n}\n\n.placeOrderMobileLink[_ngcontent-%COMP%] {\n  display: inline-block;\n  width: 100%;\n  height: 70px;\n  position: fixed;\n  bottom: 0px;\n  background: linear-gradient(96.62deg, #ff9e00 0%, #ff4b17 100%);\n  color: white;\n  text-align: center;\n  padding-top: 20px;\n  text-decoration: none;\n}\n\n.noMarginRight[_ngcontent-%COMP%] {\n  margin-right: 0px;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  font-size: 20px;\n  vertical-align: middle;\n}\n\n.noMenuAvailable[_ngcontent-%COMP%] {\n  padding-top: 50px;\n  text-align: center;\n}\n\n.mobileFilterCol[_ngcontent-%COMP%] {\n  text-align: center;\n}\n\n.uniformSelectList[_ngcontent-%COMP%] {\n  margin: 15px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 77460:
/*!******************************************************************************************!*\
  !*** ./src/app/manage-order/components/new-order-details/new-order-details.component.ts ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NewOrderDetailsComponent: () => (/* binding */ NewOrderDetailsComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/expansion */ 19322);



function NewOrderDetailsComponent_mat_accordion_0_li_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](2, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](5, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const order_r5 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate3"](" ", order_r5.studentName, " - ", order_r5.menuFriendlyName, " - ", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](2, 4, order_r5.orderDate, "EE dd/LL"), ": ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](5, 7, order_r5.price));
  }
}
function NewOrderDetailsComponent_mat_accordion_0_li_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](2, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](4, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const fee_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"](" Order Fee (", fee_r6.name, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate1"]("+", _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](4, 2, fee_r6.fee), "");
  }
}
function NewOrderDetailsComponent_mat_accordion_0_ng_template_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "p", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, "A simple transaction fee applies on each order per child.");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
}
function NewOrderDetailsComponent_mat_accordion_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "mat-accordion")(1, "mat-expansion-panel")(2, "mat-expansion-panel-header")(3, "mat-panel-title")(4, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](5, " Orders: (");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](6, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](8, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](9, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](10, "mat-panel-description");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](11, "ul", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](12, NewOrderDetailsComponent_mat_accordion_0_li_12_Template, 6, 9, "li", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](13, "mat-expansion-panel")(14, "mat-expansion-panel-header")(15, "mat-panel-title")(16, "h4");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](17, " Order Fees: ( ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](18, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](20, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](21, " ) ");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](22, "ul", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](23, NewOrderDetailsComponent_mat_accordion_0_li_23_Template, 5, 4, "li", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](24, NewOrderDetailsComponent_mat_accordion_0_ng_template_24_Template, 2, 0, "ng-template", null, 5, _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](8, 4, ctx_r0.createOrderSummary.totalAmount));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx_r0.createOrderSummary.createOrdersInfo);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](20, 6, ctx_r0.totalFees));
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngForOf", ctx_r0.feesGroupedByStudent);
  }
}
class NewOrderDetailsComponent {
  ngOnInit() {}
  static {
    this.ɵfac = function NewOrderDetailsComponent_Factory(t) {
      return new (t || NewOrderDetailsComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: NewOrderDetailsComponent,
      selectors: [["new-order-details"]],
      inputs: {
        createOrderSummary: "createOrderSummary",
        totalFees: "totalFees",
        feesGroupedByStudent: "feesGroupedByStudent"
      },
      decls: 6,
      vars: 4,
      consts: [[4, "ngIf"], [1, "totalOrder"], [1, "subHeader"], [1, "detailList"], [4, "ngFor", "ngForOf"], ["perOrderModel", ""], [1, "feeInformationLink"]],
      template: function NewOrderDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](0, NewOrderDetailsComponent_mat_accordion_0_Template, 26, 8, "mat-accordion", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](1, "h4", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2, " Total: ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "span", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](5, "currency");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("ngIf", ctx.createOrderSummary);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind1"](5, 2, ctx.createOrderSummary.totalAmount + ctx.totalFees));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_1__.NgIf, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__.MatAccordion, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__.MatExpansionPanel, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__.MatExpansionPanelHeader, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__.MatExpansionPanelTitle, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_2__.MatExpansionPanelDescription, _angular_common__WEBPACK_IMPORTED_MODULE_1__.CurrencyPipe, _angular_common__WEBPACK_IMPORTED_MODULE_1__.DatePipe],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n\n\nmat-dialog-content[_ngcontent-%COMP%] {\n  overflow: inherit;\n  padding: 0;\n  margin: 0;\n}\n\nh4[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\nh4.totalOrder[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\nh4[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.headerContainer[_ngcontent-%COMP%] {\n  height: auto;\n}\n\n.closeButton[_ngcontent-%COMP%] {\n  margin-top: 5px;\n  margin-left: 5px;\n}\n\n.closeButtonFake[_ngcontent-%COMP%] {\n  height: 60px;\n}\n\n.paddingLine[_ngcontent-%COMP%] {\n  padding-top: 5px;\n  padding-bottom: 5px;\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n.titleDialog[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 24px;\n  margin-bottom: 15px;\n  margin-top: 0px;\n}\n\n.subHeader[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.itemList[_ngcontent-%COMP%] {\n  font-style: italic;\n}\n\n.chargeDesc[_ngcontent-%COMP%] {\n  font-style: italic;\n  margin-top: 5px;\n}\n\n.warning[_ngcontent-%COMP%] {\n  padding-bottom: 24px;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n}\n\n.securityLink[_ngcontent-%COMP%] {\n  padding-top: 10px;\n}\n@media (max-width: 767px) {\n  .securityLink[_ngcontent-%COMP%] {\n    padding-bottom: 70px;\n  }\n}\n\n.feesList[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n}\n\n.detailList[_ngcontent-%COMP%] {\n  list-style: none;\n  margin-top: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%] {\n  padding-left: 0px;\n  margin-top: 20px;\n  margin-bottom: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #ff7a00;\n}\n\n.walletBalance[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  margin-bottom: 5px;\n  font-family: \"bariol_bold\";\n}\n.walletBalance[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.panelDescription[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\n\n.mat-expansion-panel-header-title[_ngcontent-%COMP%] {\n  flex-grow: 2;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 7152:
/*!************************************************************************************************!*\
  !*** ./src/app/manage-order/components/order-again-checkbox/order-again-checkbox.component.ts ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderAgainCheckboxComponent: () => (/* binding */ OrderAgainCheckboxComponent)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-button/schools-button.module */ 33373);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);









function OrderAgainCheckboxComponent_div_2_span_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "One or more items not available");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a0) {
  return {
    disabled: a0
  };
};
function OrderAgainCheckboxComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 3)(1, "div")(2, "span", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](4, OrderAgainCheckboxComponent_div_2_span_4_Template, 2, 0, "span", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](5, "mat-checkbox", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const i_r2 = ctx.index;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](5, _c0, !ctx_r0.dateList[i_r2].OrderDateAvailable || !ctx_r0.dateList[i_r2].AllItemsAvailable));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate2"]("", ctx_r0.dateList[i_r2].Title, " (", ctx_r0.menuName, ")");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx_r0.dateList[i_r2].AllItemsAvailable && ctx_r0.dateList[i_r2].OrderDateAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formControlName", i_r2);
  }
}
class OrderAgainCheckboxComponent {
  constructor() {
    this.disabled = false;
  }
  static {
    this.ɵfac = function OrderAgainCheckboxComponent_Factory(t) {
      return new (t || OrderAgainCheckboxComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: OrderAgainCheckboxComponent,
      selectors: [["order-again-checkbox"]],
      inputs: {
        dateFormArray: "dateFormArray",
        dateList: "dateList",
        menuName: "menuName",
        form: "form"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 2,
      consts: [[3, "formGroup"], ["formArrayName", "dates"], ["class", "checkboxContainer", 4, "ngFor", "ngForOf"], [1, "checkboxContainer"], [3, "ngClass"], ["class", "unavailableMessage", 4, "ngIf"], [3, "formControlName"], [1, "unavailableMessage"]],
      template: function OrderAgainCheckboxComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "form", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, OrderAgainCheckboxComponent_div_2_Template, 6, 7, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.dateFormArray.controls);
        }
      },
      dependencies: [_angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__.MatCheckboxModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_2__.MatCheckbox, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_3__.MatFormFieldModule, src_app_schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_0__.SchoolsButtonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormArrayName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_common__WEBPACK_IMPORTED_MODULE_5__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.checkboxContainer[_ngcontent-%COMP%] {\n  padding: 5px 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 10px;\n  border-bottom: 1px solid #b9b9c8;\n}\n.checkboxContainer[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%] {\n  color: #b9b9c8;\n}\n.checkboxContainer[_ngcontent-%COMP%]   .unavailableMessage[_ngcontent-%COMP%] {\n  display: block;\n  color: #c04545;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 43830:
/*!************************************************************************************!*\
  !*** ./src/app/manage-order/components/orders-details/orders-details.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrdersDetailsComponent: () => (/* binding */ OrdersDetailsComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _states_children_children_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../states/children/children.selectors */ 54186);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _new_order_details_new_order_details_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../new-order-details/new-order-details.component */ 77460);
/* harmony import */ var _edit_order_details_edit_order_details_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../edit-order-details/edit-order-details.component */ 47258);
/* harmony import */ var _sharedPipes_absolute_money_value_pipe__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../sharedPipes/absolute-money-value.pipe */ 41337);
// ngrx


// Model








function OrdersDetailsComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "edit-order-details", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("editOrderSummary", ctx_r0.editOrderSummary);
  }
}
function OrdersDetailsComponent_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "new-order-details", 4);
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("createOrderSummary", ctx_r2.createOrderSummary)("totalFees", ctx_r2.totalFees)("feesGroupedByStudent", ctx_r2.feesGroupedByStudent);
  }
}
function OrdersDetailsComponent_div_3_span_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "total orders amount ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function OrdersDetailsComponent_div_3_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](2, "absoluteMoneyValue");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](2, 1, ctx_r6.editOrderSummary.priceDiff), "");
  }
}
function OrdersDetailsComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div")(1, "p", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " Wallet Balance: ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](5, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "p", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, " We'll deduct the ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, OrdersDetailsComponent_div_3_span_8_Template, 2, 0, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](9, OrdersDetailsComponent_div_3_span_9_Template, 3, 3, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](10, " from your wallet balance. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](5, 3, ctx_r3.accountBalance));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r3.createOrderSummary);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r3.editOrderSummary);
  }
}
function OrdersDetailsComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div")(1, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " We'll refund your wallet with ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "span", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](5, "absoluteMoneyValue");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](5, 1, ctx_r4.editOrderSummary.priceDiff));
  }
}
class OrdersDetailsComponent {
  constructor(store, userService) {
    this.store = store;
    this.userService = userService;
    this.listChildren = [];
    this.feesGroupedByStudent = [];
    this.groupFeesByStudent = feesData => {
      const feesGroupedByStudent = feesData.reduce((feeArray, index) => {
        const currentStudentFee = feeArray[index.studentId] ?? 0;
        return {
          ...feeArray,
          [index.studentId]: {
            fee: (currentStudentFee?.fee || 0) + index.fee,
            name: index.studentName
          }
        };
      }, {});
      return Object.values(feesGroupedByStudent);
    };
  }
  ngOnInit() {
    let connectedUser = this.userService.GetUserConnected();
    this.isAdminMerchant = connectedUser.Role == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.Roles.Admin || connectedUser.Role == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.Roles.Canteen;
    this.subscriptionChildren$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_7__.select)(_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_0__.children)).subscribe(children => {
      this.listChildren = children.list;
      this.currentStudentFirstName = children.selected.FirstName;
    });
    if (this.createOrderSummary) {
      this.feesGroupedByStudent = this.groupFeesByStudent(this.createOrderSummary.createOrdersInfo);
    }
  }
  ngOnDestroy() {
    this.subscriptionChildren$?.unsubscribe();
  }
  GetTextTotalOrder() {
    return this.editOrderSummary ? 'New order' : 'Total';
  }
  isRefund() {
    return this.editOrderSummary?.priceDiff < 0;
  }
  static {
    this.ɵfac = function OrdersDetailsComponent_Factory(t) {
      return new (t || OrdersDetailsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_7__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.UserService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: OrdersDetailsComponent,
      selectors: [["app-orders-details"]],
      inputs: {
        editOrderSummary: "editOrderSummary",
        createOrderSummary: "createOrderSummary",
        totalFees: "totalFees",
        accountBalance: "accountBalance"
      },
      decls: 5,
      vars: 4,
      consts: [[4, "ngIf", "ngIfElse"], ["newOrder", ""], [4, "ngIf"], [3, "editOrderSummary"], [3, "createOrderSummary", "totalFees", "feesGroupedByStudent"], [1, "walletBalance"], [1, "subHeader"], [1, "chargeDesc"], ["class", "subHeader", 4, "ngIf"]],
      template: function OrdersDetailsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](0, OrdersDetailsComponent_ng_container_0_Template, 2, 1, "ng-container", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, OrdersDetailsComponent_ng_template_1_Template, 1, 3, "ng-template", null, 1, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, OrdersDetailsComponent_div_3_Template, 11, 5, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, OrdersDetailsComponent_div_4_Template, 6, 3, "div", 2);
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.editOrderSummary)("ngIfElse", _r1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isRefund());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.editOrderSummary && ctx.isRefund());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _new_order_details_new_order_details_component__WEBPACK_IMPORTED_MODULE_3__.NewOrderDetailsComponent, _edit_order_details_edit_order_details_component__WEBPACK_IMPORTED_MODULE_4__.EditOrderDetailsComponent, _angular_common__WEBPACK_IMPORTED_MODULE_8__.CurrencyPipe, _sharedPipes_absolute_money_value_pipe__WEBPACK_IMPORTED_MODULE_5__.AbsoluteMoneyValuePipe],
      styles: ["@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n\n\nmat-dialog-content[_ngcontent-%COMP%] {\n  overflow: inherit;\n  padding: 0;\n  margin: 0;\n}\n\nh4[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\nh4.totalOrder[_ngcontent-%COMP%] {\n  margin-top: 30px;\n}\nh4[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.headerContainer[_ngcontent-%COMP%] {\n  height: auto;\n}\n\n.closeButton[_ngcontent-%COMP%] {\n  margin-top: 5px;\n  margin-left: 5px;\n}\n\n.closeButtonFake[_ngcontent-%COMP%] {\n  height: 60px;\n}\n\n.paddingLine[_ngcontent-%COMP%] {\n  padding-top: 5px;\n  padding-bottom: 5px;\n  padding-left: 12px;\n  padding-right: 12px;\n}\n\n.titleDialog[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 24px;\n  margin-bottom: 15px;\n  margin-top: 0px;\n}\n\n.subHeader[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.itemList[_ngcontent-%COMP%] {\n  font-style: italic;\n}\n\n.chargeDesc[_ngcontent-%COMP%] {\n  font-style: italic;\n  margin-top: 5px;\n}\n\n.warning[_ngcontent-%COMP%] {\n  padding-bottom: 24px;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n}\n\n.securityLink[_ngcontent-%COMP%] {\n  padding-top: 10px;\n}\n@media (max-width: 767px) {\n  .securityLink[_ngcontent-%COMP%] {\n    padding-bottom: 70px;\n  }\n}\n\n.feesList[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: inline-block;\n}\n\n.detailList[_ngcontent-%COMP%] {\n  list-style: none;\n  margin-top: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%] {\n  padding-left: 0px;\n  margin-top: 20px;\n  margin-bottom: 0;\n}\n.detailList.remainingList[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #ff7a00;\n}\n\n.walletBalance[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  margin-bottom: 5px;\n  font-family: \"bariol_bold\";\n}\n.walletBalance[_ngcontent-%COMP%]   .subHeader[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.panelDescription[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\n\n.mat-expansion-panel-header-title[_ngcontent-%COMP%] {\n  flex-grow: 2;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 24100:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/orders-placed/orders-placed.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrdersPlacedComponent: () => (/* binding */ OrdersPlacedComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);


class OrdersPlacedComponent {
  constructor() {
    this.goToOrders = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
  }
  ngOnInit() {}
  GotToOrders() {
    this.goToOrders.emit(true);
  }
  static {
    this.ɵfac = function OrdersPlacedComponent_Factory(t) {
      return new (t || OrdersPlacedComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: OrdersPlacedComponent,
      selectors: [["app-orders-placed"]],
      outputs: {
        goToOrders: "goToOrders"
      },
      decls: 11,
      vars: 0,
      consts: [[1, "row", "no-gutters", "paddingLine"], [1, "col-12"], [1, "containerCheck"], [1, "circle-loader", "load-complete"], [1, "checkmark", "draw"], ["type", "button", "id", "go-to-orders-button", 1, "PrimaryButton", 3, "click"]],
      template: function OrdersPlacedComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](5, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](6, " Your order is now processing and will typically be processed within a few moments. An email will be sent to confirm your order. ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](7, "div", 0)(8, "div", 1)(9, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function OrdersPlacedComponent_Template_button_click_9_listener() {
            return ctx.GotToOrders();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](10, " Go to Orders ");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()()();
        }
      },
      styles: [".containerCheck[_ngcontent-%COMP%] {\n  text-align: center;\n}\n\n.circle-loader[_ngcontent-%COMP%] {\n  margin-bottom: 3.5em;\n  border: 3px solid rgba(0, 0, 0, 0.2);\n  border-left-color: #5cb85c;\n  animation: _ngcontent-%COMP%_loader-spin 1.2s infinite linear;\n  position: relative;\n  display: inline-block;\n  vertical-align: top;\n  border-radius: 50%;\n  width: 7em;\n  height: 7em;\n}\n\n.load-complete[_ngcontent-%COMP%] {\n  animation: none;\n  border-color: #5cb85c;\n  transition: border 500ms ease-out;\n}\n\n.checkmark.draw[_ngcontent-%COMP%]:after {\n  animation-duration: 800ms;\n  animation-timing-function: ease;\n  animation-name: _ngcontent-%COMP%_checkmark;\n  transform: scaleX(-1) rotate(135deg);\n}\n.checkmark[_ngcontent-%COMP%]:after {\n  opacity: 1;\n  height: 3.5em;\n  width: 1.75em;\n  transform-origin: left top;\n  border-right: 4px solid #5cb85c;\n  border-top: 4px solid #5cb85c;\n  content: \"\";\n  left: 1.75em;\n  top: 3.5em;\n  position: absolute;\n}\n\n@keyframes _ngcontent-%COMP%_loader-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes _ngcontent-%COMP%_checkmark {\n  0% {\n    height: 0;\n    width: 0;\n    opacity: 1;\n  }\n  20% {\n    height: 0;\n    width: 1.75em;\n    opacity: 1;\n  }\n  40% {\n    height: 3.5em;\n    width: 1.75em;\n    opacity: 1;\n  }\n  100% {\n    height: 3.5em;\n    width: 1.75em;\n    opacity: 1;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 12598:
/*!************************************************************************************!*\
  !*** ./src/app/manage-order/components/reorder-filter/reorder-filter.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReorderFilterComponent: () => (/* binding */ ReorderFilterComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var src_app_schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/schools-button/schools-button.module */ 33373);
/* harmony import */ var _order_again_checkbox_order_again_checkbox_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../order-again-checkbox/order-again-checkbox.component */ 7152);
/* harmony import */ var src_app_shared_shared_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared/shared.module */ 93887);
/* harmony import */ var _functions_menu_item_sort_helper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../functions/menu-item-sort-helper */ 67412);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
/* harmony import */ var src_app_sharedPipes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/sharedPipes */ 52151);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../schools-button/components/primary-button/primary-button.component */ 58666);
/* harmony import */ var _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../shared/components/spinner/spinner.component */ 71517);

// models

// Ngrx



















function ReorderFilterComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](1, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](2, "app-spinner", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("manual", true);
  }
}
function ReorderFilterComponent_ng_template_1_div_5_li_6_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipe"](2, "orderOptionsString");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate1"](" (", _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipeBind1"](2, 1, item_r6.SelectedOptions), ") ");
  }
}
const _c0 = function (a0) {
  return [a0];
};
function ReorderFilterComponent_ng_template_1_div_5_li_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](2, ReorderFilterComponent_ng_template_1_div_5_li_6_span_2_Template, 3, 3, "span", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipe"](4, "calculateOrderItemsPrice");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate2"](" ", item_r6.Quantity, " x ", item_r6.Name, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", (item_r6.SelectedOptions == null ? null : item_r6.SelectedOptions.length) > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtextInterpolate1"](" (", _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpipeBind1"](4, 4, _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵpureFunction1"](6, _c0, item_r6)), ") ");
  }
}
function ReorderFilterComponent_ng_template_1_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div", 12)(1, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](2, "img", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](3, "p", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](4, "One or more items have had a price update since your last order");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](5, "ul", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](6, ReorderFilterComponent_ng_template_1_div_5_li_6_Template, 5, 8, "li", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngForOf", ctx_r3.updatedOrderItems);
  }
}
function ReorderFilterComponent_ng_template_1_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelement"](1, "order-again-checkbox", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("form", ctx_r4.form)("dateFormArray", ctx_r4.dateFormArray)("menuName", ctx_r4.data.menuName)("dateList", ctx_r4.dateList);
  }
}
function ReorderFilterComponent_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](0, "div", 4)(1, "div", 5)(2, "div", 6)(3, "h2", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtext"](4, "Which days do you want to repeat this order?");
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](5, ReorderFilterComponent_ng_template_1_div_5_Template, 7, 1, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](6, ReorderFilterComponent_ng_template_1_div_6_Template, 2, 4, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementStart"](7, "div", 10)(8, "primary-button", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵlistener"]("onPress", function ReorderFilterComponent_ng_template_1_Template_primary_button_onPress_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵresetView"](ctx_r9.closeSheet());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", ctx_r2.priceUpdate);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", ctx_r2.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("disabled", ctx_r2.anyDatesSelected());
  }
}
class ReorderFilterComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_bottomSheetRef, data, formBuilder, orderApiService) {
    super();
    this._bottomSheetRef = _bottomSheetRef;
    this.data = data;
    this.formBuilder = formBuilder;
    this.orderApiService = orderApiService;
    this.AMOUNT_OF_WEEKS = 3;
    this.dates = [];
    this.loading = true;
    this.dateValues = [];
    this.dateList = null;
    this.priceUpdate = false;
    this.updatedOrderItems = null;
    this.anyDatesSelected = () => {
      const anyDatesSelected = this.form?.controls?.dates?.value.some(dateIsSelected => dateIsSelected);
      return !anyDatesSelected;
    };
  }
  ngOnInit() {
    const request = this.data.dateRequest;
    this.getOrderAgainDataInfo(request);
  }
  getOrderAgainDataInfo(request) {
    this.orderApiService.getReOrderInfo(request).subscribe({
      next: response => {
        if (response) {
          this.processResponse(response);
          this.generateDateForm();
        }
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      }
    });
  }
  processResponse(response) {
    this.updatedOrderItems = (0,_functions_menu_item_sort_helper__WEBPACK_IMPORTED_MODULE_5__.UpdateOrderItemsWithMenuData)(response.Menu.MenuJSON, this.data.OrderItems);
    this.priceUpdate = this.orderPiceUpdated(this.updatedOrderItems);
    this.processDateAvailability(response);
  }
  processDateAvailability(response) {
    const dateInfo = response?.ReOrderDailyInfos || [];
    const menu = response?.Menu || null;
    this.dateList = dateInfo?.map(day => {
      return {
        Date: day.OrderDate,
        Title: moment__WEBPACK_IMPORTED_MODULE_1__(day.OrderDate).format('ddd, DD/MM'),
        AllItemsAvailable: this.allItemsAvailable(day.OrderDate, menu),
        OrderDateAvailable: this.isOrderDateAvailable(day)
      };
    });
  }
  orderPiceUpdated(updatedOrderItems) {
    const originalOrderPrice = this.getOrderItemsPrice(this.data.OrderItems);
    const newOrderPrice = this.getOrderItemsPrice(this.updatedOrderItems);
    return originalOrderPrice !== newOrderPrice;
  }
  getOrderItemsPrice(orderItems) {
    return orderItems.reduce((accumlator, value) => accumlator + +value.ItemPriceIncGst, 0);
  }
  isOrderDateAvailable(day) {
    return day.IsSchoolOpen && !day.IsOrderPlaced && !this.cutOffTimePassed(day.OrderDate);
  }
  cutOffTimePassed(date) {
    const time = moment__WEBPACK_IMPORTED_MODULE_1__(this.data.EarlyCutOffTime).format('HH:mm:ss');
    const cutOffDateTime = (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.AddTimeToDate)(date, time);
    return (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.DateHasPassed)(cutOffDateTime);
  }
  allItemsAvailableOnDate(menuItems, date) {
    return menuItems.every(item => {
      return !item.Availabilities?.length || this.isItemAvailableOnOrderDate(item.Availabilities, date.toString());
    });
  }
  isItemAvailableOnOrderDate(availableDates, orderDate) {
    return availableDates.some(availability => {
      return (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.formatDateToUniversal)(orderDate) >= (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.formatDateToUniversal)(availability.StartDate) && (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.formatDateToUniversal)(orderDate) <= (0,src_app_utility__WEBPACK_IMPORTED_MODULE_6__.formatDateToUniversal)(availability.EndDate);
    });
  }
  allItemsAvailable(date, menu) {
    const orderItemIdList = this.data.OrderItems.map(item => item.MenuItemId);
    const matchingMenuItems = (0,_functions_menu_item_sort_helper__WEBPACK_IMPORTED_MODULE_5__.GetMenuItemsThatMatchOrderItemMenuId)(menu.MenuJSON, orderItemIdList);
    const availableWeekDays = this.getOrderDayAvailability(matchingMenuItems);
    const currentDay = moment__WEBPACK_IMPORTED_MODULE_1__(date).day();
    const isAvailableOnWeekDay = availableWeekDays.includes(currentDay);
    return this.allItemsAvailableOnDate(matchingMenuItems, date) && isAvailableOnWeekDay;
  }
  getOrderDayAvailability(itemList) {
    let availabilities = [];
    itemList.forEach(item => {
      if (!item.AvailabilityDays) return;
      if (availabilities.length === 0) {
        availabilities = item.AvailabilityDays.split(',');
        return;
      }
      availabilities = this.getTwoArrayIntersection(availabilities, item.AvailabilityDays.split(','));
    });
    return this.shortWeekNameToIndexConverter(availabilities);
  }
  shortWeekNameToIndexConverter(array) {
    const weekToIndex = {
      M: 1,
      T: 2,
      W: 3,
      Th: 4,
      F: 5
    };
    return array.map(item => weekToIndex[item]);
  }
  getTwoArrayIntersection(firstArray, secondArray) {
    const intersection = [];
    firstArray.forEach(item => {
      if (secondArray.find(item2 => item === item2)) {
        intersection.push(item);
      }
    });
    return intersection;
  }
  generateDateForm() {
    // Generate dynamic date form
    this.form = this.formBuilder.group({
      dates: new _angular_forms__WEBPACK_IMPORTED_MODULE_12__.FormArray([])
    });
    // Create form control for each date checkbox value
    this.dateList.forEach(date => {
      const cannotPlaceOrder = !date.OrderDateAvailable || !date.AllItemsAvailable;
      this.dateFormArray.push(new _angular_forms__WEBPACK_IMPORTED_MODULE_12__.FormControl({
        value: false,
        disabled: cannotPlaceOrder
      }));
    });
  }
  get dateFormArray() {
    if (this.form) {
      return this.form?.get('dates');
    }
  }
  closeSheet() {
    const selectedDateArray = this.getSelectedDatesFromForm();
    this._bottomSheetRef.dismiss(selectedDateArray);
  }
  getSelectedDatesFromForm() {
    let dateArray = [];
    this.dateFormArray.controls.forEach((formControl, index) => {
      if (formControl.value) {
        const dateKeyValue = this.dateList[index];
        dateArray.push(dateKeyValue.Date);
      }
    });
    return dateArray;
  }
  ngOnDestroy() {}
  static {
    this.ɵfac = function ReorderFilterComponent_Factory(t) {
      return new (t || ReorderFilterComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_13__.MatBottomSheetRef), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_13__.MAT_BOTTOM_SHEET_DATA), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_12__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_8__.OrderApiService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineComponent"]({
      type: ReorderFilterComponent,
      selectors: [["reorder-filter"]],
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵStandaloneFeature"]],
      decls: 3,
      vars: 2,
      consts: [[4, "ngIf", "ngIfElse"], ["showForm", ""], [1, "col-12", "d-flex", "align-items-center", "justify-content-center"], [3, "manual"], [1, "result"], [1, "row", "justify-content-lg-center"], [1, "col-12", "col-lg-8", "pt-3"], [1, "center-text"], ["class", "infoContainer", 4, "ngIf"], ["class", "formContainer mt-3", 4, "ngIf"], [1, "col-12", "pt-4"], ["text", "Go to checkout", 3, "disabled", "onPress"], [1, "infoContainer"], [1, "groupMessage"], ["src", "assets/icons/black-error.svg", "alt", "error"], [1, "m-0", "pb-2"], [1, "m-0"], [4, "ngFor", "ngForOf"], [4, "ngIf"], [1, "formContainer", "mt-3"], [3, "form", "dateFormArray", "menuName", "dateList"]],
      template: function ReorderFilterComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](0, ReorderFilterComponent_ng_container_0_Template, 3, 1, "ng-container", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplate"](1, ReorderFilterComponent_ng_template_1_Template, 9, 3, "ng-template", null, 1, _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵtemplateRefExtractor"]);
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵreference"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵproperty"]("ngIf", ctx.loading)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_material_checkbox__WEBPACK_IMPORTED_MODULE_14__.MatCheckboxModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_15__.MatFormFieldModule, src_app_schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_2__.SchoolsButtonModule, _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_9__.PrimaryButtonComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_12__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_12__.FormsModule, _angular_common__WEBPACK_IMPORTED_MODULE_16__.CommonModule, _angular_common__WEBPACK_IMPORTED_MODULE_16__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_16__.NgIf, _order_again_checkbox_order_again_checkbox_component__WEBPACK_IMPORTED_MODULE_3__.OrderAgainCheckboxComponent, src_app_shared_shared_module__WEBPACK_IMPORTED_MODULE_4__.SharedModule, _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_10__.SpinnerComponent, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_17__.MatExpansionModule, src_app_sharedPipes__WEBPACK_IMPORTED_MODULE_7__.CalculateOrderItemsPricePipe, src_app_sharedPipes__WEBPACK_IMPORTED_MODULE_7__.OrderOptionsStringPipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.center-text[_ngcontent-%COMP%] {\n  text-align: center;\n}\n\n.infoContainer[_ngcontent-%COMP%] {\n  border: 1px solid #88949f;\n  border-radius: 8px;\n  padding: 12px;\n}\n.infoContainer[_ngcontent-%COMP%]   .groupMessage[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  gap: 10px;\n}\n\n.result[_ngcontent-%COMP%] {\n  height: 60%;\n}\n.result[_ngcontent-%COMP%]   .formContainer[_ngcontent-%COMP%] {\n  max-height: 60%;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n.spinner[_ngcontent-%COMP%] {\n  justify-content: center;\n  padding-top: 100px;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  left: 0;\n  background-color: rgba(79, 79, 79, 0.4);\n  align-self: center;\n  display: flex;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 26000:
/*!****************************************************************************************************!*\
  !*** ./src/app/manage-order/components/selected-order-history/selected-order-history.component.ts ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SelectedOrderHistoryComponent: () => (/* binding */ SelectedOrderHistoryComponent)
/* harmony export */ });
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/states/family/family.selectors */ 61548);
/* harmony import */ var src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/states/children/children.selectors */ 54186);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _shared_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components */ 2691);
/* harmony import */ var _dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dialog-place-order/dialog-place-order.component */ 73908);
/* harmony import */ var _reorder_filter_reorder_filter_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../reorder-filter/reorder-filter.component */ 12598);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../functions/convert-to-cart-items */ 7366);
/* harmony import */ var src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! src/app/sharedServices/menu/menu-custom-name */ 76573);
/* harmony import */ var src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! src/app/utility/timezone-helper */ 92471);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var ngx_device_detector__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ngx-device-detector */ 50565);
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices_order_create_order_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! src/app/sharedServices/order/create-order.service */ 66733);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../../schools-button/components/primary-button/primary-button.component */ 58666);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _sharedPipes_calculate_order_items_price_pipe__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../../sharedPipes/calculate-order-items-price.pipe */ 30985);


// Ngrx



// Models

// components




















function SelectedOrderHistoryComponent_div_9_span_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](2, "strong", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](3, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const option_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate1"](" ", option_r9.OptionName, " ");
  }
}
const _c0 = function (a0) {
  return [a0];
};
function SelectedOrderHistoryComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 14)(1, "div", 15)(2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](4, "div", 16)(5, "div", 3)(6, "div", 4)(7, "h6", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](9, "div", 3)(10, "div", 4)(11, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](12, SelectedOrderHistoryComponent_div_9_span_12_Template, 4, 1, "span", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipe"](14, "calculateOrderItemsPrice");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const item_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate1"]("", item_r7.Quantity, " x");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate"](item_r7.Name);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngForOf", item_r7.SelectedOptions);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipeBind1"](14, 4, _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpureFunction1"](6, _c0, item_r7)), " ");
  }
}
function SelectedOrderHistoryComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 14)(1, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelement"](2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](3, "div", 16)(4, "div", 3)(5, "div", 4)(6, "h6", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](7, "Order fee");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](8, "div", 3)(9, "div", 4)(10, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipe"](12, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipeBind1"](12, 1, ctx_r1.orderFee));
  }
}
function SelectedOrderHistoryComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div")(1, "div", 11)(2, "div", 4)(3, "p", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate1"](" ", ctx_r2.errorMessage, " ");
  }
}
function SelectedOrderHistoryComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 21)(1, "primary-button", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵlistener"]("onPress", function SelectedOrderHistoryComponent_div_19_Template_primary_button_onPress_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵrestoreView"](_r11);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵresetView"](ctx_r10.ClickEditOrder());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
  }
}
function SelectedOrderHistoryComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 21)(1, "button", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵlistener"]("click", function SelectedOrderHistoryComponent_div_20_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵresetView"](ctx_r12.ClickEditOrder());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](2, "Try again");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
  }
}
function SelectedOrderHistoryComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 21)(1, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵlistener"]("click", function SelectedOrderHistoryComponent_div_21_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵrestoreView"](_r15);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵresetView"](ctx_r14.ClickCancel());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](2, "Cancel Order");
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
  }
}
function SelectedOrderHistoryComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 11)(1, "div", 25)(2, "primary-button", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵlistener"]("onPress", function SelectedOrderHistoryComponent_div_22_Template_primary_button_onPress_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵrestoreView"](_r17);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵresetView"](ctx_r16.ClickOrderAgain());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()();
  }
}
const _c1 = function (a0, a1, a2) {
  return {
    completedOrder: a0,
    processingOrder: a1,
    errorOrder: a2
  };
};
class SelectedOrderHistoryComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.BaseComponent {
  constructor(familyStore, location, dialog, deviceService, _bottomSheet, appInsightsService, featureFlagService, route, createOrderService) {
    super();
    this.familyStore = familyStore;
    this.location = location;
    this.dialog = dialog;
    this.deviceService = deviceService;
    this._bottomSheet = _bottomSheet;
    this.appInsightsService = appInsightsService;
    this.featureFlagService = featureFlagService;
    this.route = route;
    this.createOrderService = createOrderService;
    this.listProducts = [];
    this.disableOrderAgainButton = true;
  }
  ngOnInit() {
    this.featureFlagService.getFlag(src_constants__WEBPACK_IMPORTED_MODULE_1__.FeatureFlags.disableOrderAgainButton, false).then(res => {
      this.disableOrderAgainButton = res;
    });
    this.subscriptionStudentsState$ = this.familyStore.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_18__.select)(src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_3__.childrenState)).subscribe(studentState => {
      this.selectedUser = studentState.selected;
    });
    this.subscriptionFamilyState$ = this.familyStore.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_18__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_2__.familyState)).subscribe(familyState => {
      this.day = Object.assign(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.FamilyDayOrders(), familyState.dayDetail);
    });
    // get order data from resolver
    this.route.data.subscribe(data => {
      if (data?.orderWithCutOffTimes) {
        this.orderData = {
          Order: data.orderWithCutOffTimes.Order,
          CutOffTimes: this.convertCutOffTimeToLocalTime(data.orderWithCutOffTimes.CutOffTimes)
        };
        this.processOrderData(this.orderData.Order);
      }
    });
  }
  convertCutOffTimeToLocalTime(cutOffTimes) {
    const utcOffSet = this.selectedUser.SchoolTimeZoneOffSetHours;
    return {
      EarlyCutOffTime: (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_11__.convertSchoolDateTimeToLocalDateTime)(cutOffTimes.EarlyCutOffTime, utcOffSet),
      LateCutOffTime: (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_11__.convertSchoolDateTimeToLocalDateTime)(cutOffTimes.LateCutOffTime, utcOffSet),
      MenuCutOffTime: (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_11__.convertSchoolDateTimeToLocalDateTime)(cutOffTimes.MenuCutOffTime, utcOffSet)
    };
  }
  getPageTitle() {
    const menuName = (0,src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_10__.GetCustomMenuName)(this.orderData.Order.MenuType, this.orderData.Order.MenuName);
    return `${moment__WEBPACK_IMPORTED_MODULE_0__(this.orderData.Order.OrderDate).format('dddd DD/MM')} ${menuName} Order`;
  }
  ngOnDestroy() {
    if (this.subscriptionFamilyState$) {
      this.subscriptionFamilyState$.unsubscribe();
    }
    if (this.subscriptionStudentsState$) {
      this.subscriptionStudentsState$.unsubscribe();
    }
  }
  processOrderData(order) {
    if (!order || order?.Items?.length <= 0) {
      this.GoBackClick();
      return;
    }
    this.isProcessing = this.orderData.Order.OrderStatusId == src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.OrderStatusEnum.Draft;
    this.isUniformOrder = this.orderData.Order.MenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.MenuTypeEnum.Uniform;
    this.hasError = this.orderData.Order.OrderStatusId === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.OrderStatusEnum.Error;
    this.orderStatusDisplay = this.getOrderStatusToDisplay();
    this.titlePage = this.getPageTitle();
    this.listProducts = order.Items;
    this.orderFee = order.OrderFee;
    this.orderPrice = this.getTotalPrice();
  }
  getOrderStatusToDisplay() {
    if (this.hasError) {
      this.errorMessage = this.orderData.Order.ErrorMessage;
      return 'Error Occurred';
    }
    return this.isProcessing ? 'Processing' : this.orderHasBeenCancelled() ? 'Cancelled' : this.getOrderStatusText();
  }
  getOrderStatusText() {
    return this.isUniformOrder ? this.getUniformStatusToDisplay(this.orderData.Order.CanteenStatus) : 'Completed';
  }
  orderHasBeenCancelled() {
    return this.orderData.Order.OrderStatusId === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.OrderStatusEnum.Cancelled;
  }
  getUniformStatusToDisplay(status) {
    return status === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.CanteenStatusEnum.New ? 'Ordered' : status;
  }
  getTotalPrice() {
    const orderPrice = this.orderData.Order?.Price || 0;
    const orderFee = this.orderData.Order?.OrderFee || 0;
    return orderPrice + orderFee;
  }
  GetPriceItem(item) {
    let temp = Object.assign(new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.MenuItem(), item);
    return temp.GetPriceItemWithOption();
  }
  GoBackClick() {
    this.location.back();
  }
  ClickEditOrder() {
    if (this.cutOffTimePassed()) {
      return;
    }
    this.createOrderService.getAndSetDayDetail(this.day.MenuType, this.day.MenuName, this.day.MenuId, this.day.Date, this.selectedUser, moment__WEBPACK_IMPORTED_MODULE_0__(this.orderData.CutOffTimes.MenuCutOffTime).toDate(), this.orderData.Order);
    this.createOrderService.parentCreateOrder();
  }
  cutOffTimePassed() {
    const showCutOffWarning = !this.isUniformOrder && this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
    if (showCutOffWarning) {
      this.showCutOffTimeDialog();
    }
    return showCutOffWarning;
  }
  ClickCancel() {
    if (this.cutOffTimePassed()) {
      return;
    }
    this.dialog.open(_shared_components__WEBPACK_IMPORTED_MODULE_5__.DialogCancelOrderComponent, {
      width: '500px',
      disableClose: true,
      data: this.orderData.Order
    });
  }
  orderAgainSheetRequest() {
    const order = this.orderData.Order;
    const earlyCutOffTime = this.orderData.CutOffTimes.EarlyCutOffTime;
    const orderAaginData = {
      StudentId: order.StudentId,
      MenuId: order.MenuId
    };
    const request = {
      Orders: [orderAaginData]
    };
    return {
      menuName: this.day.MenuName,
      dateRequest: request,
      OrderItems: order.Items,
      EarlyCutOffTime: earlyCutOffTime
    };
  }
  ClickOrderAgain() {
    const sheetData = this.orderAgainSheetRequest();
    const dateSelectionSheetRef = this._bottomSheet.open(_reorder_filter_reorder_filter_component__WEBPACK_IMPORTED_MODULE_7__.ReorderFilterComponent, {
      data: sheetData
    });
    dateSelectionSheetRef.afterDismissed().subscribe(selectedDates => {
      if (selectedDates) {
        this.goToOrderAgainCheckout(selectedDates);
      }
    });
  }
  goToOrderAgainCheckout(dateList) {
    const cartItems = this.getAllCartsData(dateList);
    const groupedCarts = (0,_functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_9__.GetGroupedShoppingCart)(cartItems);
    const data = {
      editOrderId: null,
      groupedCarts
    };
    this.openCheckoutDialog(data);
  }
  openCheckoutDialog(placeOrderData) {
    let dialogRef;
    if (this.deviceService.isMobile()) {
      dialogRef = this.showFullScreenCheckoutDialog(placeOrderData);
    } else {
      dialogRef = this.dialog.open(_dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_6__.DialogPlaceOrderComponent, {
        width: '500px',
        disableClose: true,
        data: placeOrderData
      });
    }
    dialogRef.afterClosed().subscribe(errorResult => {
      if (errorResult) {
        this.showErrorDialog();
        return;
      }
    });
  }
  showFullScreenCheckoutDialog(placeOrderData) {
    return this.dialog.open(_dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_6__.DialogPlaceOrderComponent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '100%',
      width: '100%',
      panelClass: 'custom-dialog-container',
      disableClose: true,
      data: placeOrderData
    });
  }
  getAllCartsData(dateList) {
    const cutOffTime = this.orderData.CutOffTimes.MenuCutOffTime;
    const order = this.orderData.Order;
    const cartItemsWithOrderAgainDates = order.Items.map((item, index) => dateList.map(date => (0,_functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_9__.ConvertOrderItemToCartType)(cutOffTime, order, index, this.selectedUser.FirstName, date)));
    return cartItemsWithOrderAgainDates.flat();
  }
  showErrorDialog() {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `Try again in a minute. If it’s still not working give Member Help a shout.`;
    data.ConfirmButton = 'Ok';
    this.dialog.open(_shared_components__WEBPACK_IMPORTED_MODULE_5__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
  }
  canCancelOrder() {
    if (this.orderHasBeenCancelled()) return false;
    return this.isUniformOrder ? this.canCancelUniformOrder() : this.canCancelEventOrCanteenOrder();
  }
  canCancelEventOrCanteenOrder() {
    return !this.isProcessing && !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }
  canCancelUniformOrder() {
    return this.orderData.Order.CanteenStatus === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.CanteenStatusEnum.New || this.orderData.Order.OrderStatusId === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.OrderStatusEnum.Error;
  }
  canTryAgainOrder() {
    return this.hasError && !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }
  canEditOrder() {
    if (this.orderCannotBeChanged()) return false;
    if (this.isUniformOrder) return this.orderStatusDisplay === 'Ordered';
    return !this.hasOrderEarlyCutOffTimePassed(this.orderData.CutOffTimes);
  }
  hasOrderEarlyCutOffTimePassed(cutOffTimes) {
    if (!cutOffTimes?.EarlyCutOffTime) {
      return false;
    }
    return (0,src_app_utility__WEBPACK_IMPORTED_MODULE_8__.DateHasPassed)(cutOffTimes.EarlyCutOffTime);
  }
  isBeforeOrderAgainCutOff() {
    const orderDate = moment__WEBPACK_IMPORTED_MODULE_0__(this.orderData.Order.OrderDate);
    const cutOffDate = moment__WEBPACK_IMPORTED_MODULE_0__().set({
      year: src_constants__WEBPACK_IMPORTED_MODULE_1__.ORDER_AGAIN_CUT_OFF_YEAR,
      month: src_constants__WEBPACK_IMPORTED_MODULE_1__.ORDER_AGAIN_CUT_OFF_MONTH,
      date: src_constants__WEBPACK_IMPORTED_MODULE_1__.ORDER_AGAIN_CUT_OFF_DAY,
      hour: 0,
      minute: 0,
      second: 0
    });
    return orderDate.isBefore(cutOffDate);
  }
  canOrderAgain() {
    if (this.orderCannotBeChanged()) return false;
    const hideOrderAgain = this.disableOrderAgainButton || this.isBeforeOrderAgainCutOff();
    return this.isCanteenOrder() && !hideOrderAgain;
  }
  orderCannotBeChanged() {
    return this.hasError || this.isProcessing || this.orderHasBeenCancelled();
  }
  isCanteenOrder() {
    return this.orderData.Order.MenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.MenuTypeEnum.Recess || this.orderData.Order.MenuType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.MenuTypeEnum.Lunch;
  }
  showCutOffTimeDialog() {
    const data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_4__.checkoutErrorModal();
    data.Title = 'Order cannot be changed';
    data.Text = 'Changes cannot be made to an order after the cut off time.';
    this.dialog.open(_shared_components__WEBPACK_IMPORTED_MODULE_5__.DialogCutOffTimeComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
  }
  static {
    this.ɵfac = function SelectedOrderHistoryComponent_Factory(t) {
      return new (t || SelectedOrderHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_18__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_19__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_20__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](ngx_device_detector__WEBPACK_IMPORTED_MODULE_21__.DeviceDetectorService), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](_angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_22__.MatBottomSheet), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_12__.CashlessAppInsightsService), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_12__.FeatureFlagService), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_23__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdirectiveInject"](src_app_sharedServices_order_create_order_service__WEBPACK_IMPORTED_MODULE_13__.CreateOrderService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdefineComponent"]({
      type: SelectedOrderHistoryComponent,
      selectors: [["selected-order-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵInheritDefinitionFeature"]],
      decls: 23,
      vars: 17,
      consts: [[1, "container-fluid"], [3, "text", "navBack"], [1, "container"], [1, "row"], [1, "col-12"], [1, "StatusOrder"], [3, "ngClass"], ["class", "row itemRow", 4, "ngFor", "ngForOf"], ["class", "row itemRow", 4, "ngIf"], [1, "row", "totalRow"], [4, "ngIf"], [1, "row", "justify-content-center"], ["class", "col-12 col-md-6 col-lg-3 buttonCol", 4, "ngIf"], ["class", "row justify-content-center", 4, "ngIf"], [1, "row", "itemRow"], [1, "col-2", "colQuantity"], [1, "col-10"], [1, "itemName"], [4, "ngFor", "ngForOf"], [1, "spacerDescription"], [1, "errorOrder"], [1, "col-12", "col-md-6", "col-lg-3", "buttonCol"], ["text", "Edit Order", 3, "onPress"], ["type", "button", 1, "PrimaryButton", "smaller", 3, "click"], ["mat-button", "", "color", "warn", 1, "WarnLink", 3, "click"], [1, "col-12", "col-md-6", "col-lg-4", "buttonCol"], ["text", "Order Again", 3, "onPress"]],
      template: function SelectedOrderHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](0, "div", 0)(1, "nav-back-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵlistener"]("navBack", function SelectedOrderHistoryComponent_Template_nav_back_button_navBack_1_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "h4", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](6, " Status: ");
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](7, "span", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](9, SelectedOrderHistoryComponent_div_9_Template, 15, 8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](10, SelectedOrderHistoryComponent_div_10_Template, 13, 3, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](11, "div", 9)(12, "div", 4)(13, "h5");
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtext"](14);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipe"](15, "currency");
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](16, SelectedOrderHistoryComponent_div_16_Template, 5, 1, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementStart"](17, "div")(18, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](19, SelectedOrderHistoryComponent_div_19_Template, 2, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](20, SelectedOrderHistoryComponent_div_20_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](21, SelectedOrderHistoryComponent_div_21_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtemplate"](22, SelectedOrderHistoryComponent_div_22_Template, 3, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("text", ctx.titlePage);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpureFunction3"](13, _c1, !ctx.hasError && !ctx.isProcessing, ctx.isProcessing, ctx.hasError));
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate"](ctx.orderStatusDisplay);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngForOf", ctx.listProducts);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.orderFee);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵtextInterpolate1"]("Total: ", _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵpipeBind1"](15, 11, ctx.orderPrice), "");
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.hasError);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.canEditOrder());
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.canTryAgainOrder());
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.canCancelOrder());
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵproperty"]("ngIf", ctx.canOrderAgain());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_19__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_19__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_19__.NgIf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_14__.NavBackButtonComponent, _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_15__.PrimaryButtonComponent, _angular_material_button__WEBPACK_IMPORTED_MODULE_24__.MatButton, _angular_common__WEBPACK_IMPORTED_MODULE_19__.CurrencyPipe, _sharedPipes_calculate_order_items_price_pipe__WEBPACK_IMPORTED_MODULE_16__.CalculateOrderItemsPricePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.StatusOrder[_ngcontent-%COMP%] {\n  font-size: 20px;\n  text-align: center;\n  margin-top: 15px;\n  margin-bottom: 15px;\n}\n.StatusOrder[_ngcontent-%COMP%]   .completedOrder[_ngcontent-%COMP%] {\n  color: #00ba6b;\n}\n.StatusOrder[_ngcontent-%COMP%]   .processingOrder[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n.StatusOrder[_ngcontent-%COMP%]   .errorOrder[_ngcontent-%COMP%] {\n  color: red;\n}\n\n.SelectItems[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n\n.itemRow[_ngcontent-%COMP%] {\n  background-color: white;\n  border-bottom: 1px solid #dddddd;\n  padding-top: 16px;\n  padding-bottom: 16px;\n  color: #333b44;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n.itemRow[_ngcontent-%COMP%]   .colQuantity[_ngcontent-%COMP%] {\n  text-align: center;\n  padding-right: 0;\n}\n.itemRow[_ngcontent-%COMP%]   .colQuantity[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 20px;\n}\n.itemRow[_ngcontent-%COMP%]   .itemName[_ngcontent-%COMP%] {\n  font-size: 20px;\n  margin: 0;\n}\n.itemRow[_ngcontent-%COMP%]   .spacerDescription[_ngcontent-%COMP%] {\n  font-size: 30px;\n  line-height: 16px;\n  font-weight: bold;\n}\n\n.totalRow[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  background-color: white;\n  border-bottom: 1px solid #dddddd;\n  text-align: right;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n.totalRow[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  margin-top: 14px;\n  margin-bottom: 14px;\n  font-size: 20px;\n}\n\n.buttonCol[_ngcontent-%COMP%] {\n  margin-top: 24px;\n}\n\n.processingOrder[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #ff7a00;\n  font-size: 18px;\n}\n\n.errorOrder[_ngcontent-%COMP%] {\n  text-align: center;\n  color: red;\n  font-size: 18px;\n}\n\n  .mat-bottom-sheet-container {\n  padding: 8px 16px;\n  min-width: 100vw;\n  box-sizing: border-box;\n  display: block;\n  outline: 0;\n  max-height: 80vh;\n  overflow: auto;\n  height: 80vh;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 11576:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/shopping-cart/shopping-cart.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ShoppingCartComponent: () => (/* binding */ ShoppingCartComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../states/shoppingCart/shopping-cart.actions */ 81860);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dialog-place-order/dialog-place-order.component */ 73908);
/* harmony import */ var _shared_components___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/ */ 2691);
/* harmony import */ var src_app_states_shoppingCart_shopping_cart_selectors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/states/shoppingCart/shopping-cart.selectors */ 88225);
/* harmony import */ var _functions_calculate_price__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../functions/calculate-price */ 87255);
/* harmony import */ var _functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../functions/convert-to-cart-items */ 7366);
/* harmony import */ var src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/states/family/family.selectors */ 61548);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var ngx_device_detector__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ngx-device-detector */ 50565);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../schools-button/components/primary-button/primary-button.component */ 58666);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _clear_cart_button_clear_cart_button_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../clear-cart-button/clear-cart-button.component */ 42580);
/* harmony import */ var _shared_components_menu_custom_menu_name_pipe__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../shared/components/menu/custom-menu-name.pipe */ 94552);
/* harmony import */ var _sharedPipes_calculate_cart_items_price_pipe__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../sharedPipes/calculate-cart-items-price.pipe */ 81169);
// Ngrx


// Models

// Components























function ShoppingCartComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 4)(1, "div", 6)(2, "nav-back-button", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("navBack", function ShoppingCartComponent_div_1_Template_nav_back_button_navBack_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r8);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r7.GoBackClick());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](3, "div", 8)(4, "clear-cart-button", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("pressed", function ShoppingCartComponent_div_1_Template_clear_cart_button_pressed_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r8);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r9.confirmClearCart());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("showButton", ctx_r0.showPlaceOrder && !ctx_r0.orderToEdit);
  }
}
function ShoppingCartComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 4)(1, "div", 6)(2, "h4", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](3, "Shopping Cart");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](4, "div", 8)(5, "clear-cart-button", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("pressed", function ShoppingCartComponent_div_2_Template_clear_cart_button_pressed_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r11);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r10.confirmClearCart());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("showButton", ctx_r1.showPlaceOrder && !ctx_r1.orderToEdit);
  }
}
function ShoppingCartComponent_ng_container_3_div_7_option_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "option", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, "1");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_ng_container_3_div_7_option_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "option", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, "2");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_ng_container_3_div_7_option_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "option", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, "3");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_ng_container_3_div_7_option_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "option", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, "4");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_ng_container_3_div_7_option_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "option", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, "5");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_ng_container_3_div_7_span_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](2, "strong", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](3, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const option_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate1"](" ", option_r21.optionName, " ");
  }
}
const _c0 = function (a0) {
  return [a0];
};
function ShoppingCartComponent_ng_container_3_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r23 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 15)(1, "div", 16)(2, "mat-form-field", 17)(3, "select", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("change", function ShoppingCartComponent_ng_container_3_div_7_Template_select_change_3_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r23);
      const item_r14 = restoredCtx.$implicit;
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r22.InputChanged(item_r14.itemCartId, $event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](4, ShoppingCartComponent_ng_container_3_div_7_option_4_Template, 2, 0, "option", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](5, ShoppingCartComponent_ng_container_3_div_7_option_5_Template, 2, 0, "option", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](6, ShoppingCartComponent_ng_container_3_div_7_option_6_Template, 2, 0, "option", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](7, ShoppingCartComponent_ng_container_3_div_7_option_7_Template, 2, 0, "option", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](8, ShoppingCartComponent_ng_container_3_div_7_option_8_Template, 2, 0, "option", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](9, "div", 24)(10, "div", 4)(11, "div", 11)(12, "h6", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](14, "div", 4)(15, "div", 11)(16, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](17, ShoppingCartComponent_ng_container_3_div_7_span_17_Template, 4, 1, "span", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](19, "calculateCartItemsPrice");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](20, "div", 26)(21, "div", 27)(22, "mat-icon", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("click", function ShoppingCartComponent_ng_container_3_div_7_Template_mat_icon_click_22_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r23);
      const item_r14 = restoredCtx.$implicit;
      const ctx_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r24.RemoveFromCart(item_r14.itemCartId));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](23, "clear");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const item_r14 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngModel", item_r14.quantity);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !item_r14.MaxQuantity || item_r14.MaxQuantity >= 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !item_r14.MaxQuantity || item_r14.MaxQuantity >= 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !item_r14.MaxQuantity || item_r14.MaxQuantity >= 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !item_r14.MaxQuantity || item_r14.MaxQuantity >= 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !item_r14.MaxQuantity || item_r14.MaxQuantity >= 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate"](item_r14.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngForOf", item_r14.selectedOptions);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind2"](19, 9, _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpureFunction1"](12, _c0, item_r14), true), " ");
  }
}
function ShoppingCartComponent_ng_container_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](1, "div", 4)(2, "div", 11)(3, "h5", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](5, "customMenuName");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](6, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](7, ShoppingCartComponent_ng_container_3_div_7_Template, 24, 14, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](8, "div", 14)(9, "div", 11)(10, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](12, "calculateCartItemsPrice");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const cart_r12 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate3"](" ", cart_r12[0].studentName, " - ", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind2"](5, 5, cart_r12[0].menuType, cart_r12[0].menuName), " - ", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind2"](6, 8, cart_r12[0].date, "EE dd/LL"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngForOf", cart_r12);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind1"](12, 11, cart_r12));
  }
}
function ShoppingCartComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 14)(1, "div", 11)(2, "h5");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](4, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind1"](4, 1, 0));
  }
}
function ShoppingCartComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r26 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 11)(1, "primary-button", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("onPress", function ShoppingCartComponent_div_7_Template_primary_button_onPress_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r26);
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r25.OrderClick());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](2, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpropertyInterpolate2"]("text", "", ctx_r4.textPlaceOrder, " ( ", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind1"](2, 3, ctx_r4.cartPrice), " )");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("disabled", ctx_r4.disableConfirmButton());
  }
}
function ShoppingCartComponent_div_8_button_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r29 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "button", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵlistener"]("click", function ShoppingCartComponent_div_8_button_2_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵrestoreView"](_r29);
      const ctx_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵresetView"](ctx_r28.ClickCancelChanges());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, " Cancel Changes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 4)(1, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](2, ShoppingCartComponent_div_8_button_2_Template, 2, 0, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx_r5.orderToEdit);
  }
}
function ShoppingCartComponent_div_9_span_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "span", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](1, " - Not enough funds");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
  }
}
function ShoppingCartComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 4)(1, "div", 11)(2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipe"](4, "currency");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](5, ShoppingCartComponent_div_9_span_5_Template, 2, 0, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtextInterpolate1"](" Remaining balance in parent wallet : ", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpipeBind1"](4, 2, ctx_r6.parentBalanceRemaining - ctx_r6.cartPrice), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx_r6.cartPrice > ctx_r6.parentBalanceRemaining);
  }
}
const _c1 = function (a0) {
  return {
    "container-fluid": a0
  };
};
class ShoppingCartComponent {
  constructor(store, spinnerService, route, location, dialog, deviceService, userService, adminService) {
    this.store = store;
    this.spinnerService = spinnerService;
    this.route = route;
    this.location = location;
    this.dialog = dialog;
    this.deviceService = deviceService;
    this.userService = userService;
    this.adminService = adminService;
    //this component shouldn't receive @Input because the inputs are not received during on mobile view (different screen layout)
    this.shoppingCartForDisplay = [];
    this.isMobile = false;
    this.isAdminCanteenUser = false;
    this.showPlaceOrder = false;
    this.priceCart = 0;
    this.fullScreenDialog = false;
    this.textPlaceOrder = 'Place Order';
    this.menuTypeEnum = _sharedModels__WEBPACK_IMPORTED_MODULE_1__.MenuTypeEnum;
    this.editCartNotChanged = true;
  }
  ngOnInit() {
    this.connectedUser = this.userService.GetUserConnected();
    this.route.data.subscribe(data => {
      this.isMobile = data.isMobile;
    });
    this.subscriptionCart$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_15__.select)(src_app_states_shoppingCart_shopping_cart_selectors__WEBPACK_IMPORTED_MODULE_4__.getCartItems)).subscribe(cartItems => {
      this.showPlaceOrder = cartItems?.length > 0;
      this.shoppingCartForDisplay = (0,_functions_convert_to_cart_items__WEBPACK_IMPORTED_MODULE_6__.GetGroupedShoppingCart)(cartItems);
      this.cartPrice = (0,_functions_calculate_price__WEBPACK_IMPORTED_MODULE_5__.GetCartItemsPrice)(cartItems);
    });
    this.subscriptionEditCart$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_15__.select)(src_app_states_shoppingCart_shopping_cart_selectors__WEBPACK_IMPORTED_MODULE_4__.editCartNotChanged)).subscribe(editCartNotChanged => {
      this.editCartNotChanged = editCartNotChanged;
    });
    this.subscriptionDayDetail$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_15__.select)(src_app_states_family_family_selectors__WEBPACK_IMPORTED_MODULE_7__.dayDetail)).subscribe(dayDetail => {
      if (dayDetail?.OrderToEdit) {
        this.textPlaceOrder = 'Edit Order';
        this.orderToEdit = dayDetail.OrderToEdit;
      }
    });
    if (this.userService.IsCanteenOrAdmin()) {
      this.setUpOrderForAdminOrMerchant();
    }
  }
  ngOnDestroy() {
    this.subscriptionCart$?.unsubscribe();
    this.subscriptionEditCart$?.unsubscribe();
    this.subscriptionDayDetail$?.unsubscribe();
  }
  setUpOrderForAdminOrMerchant() {
    this.isAdminCanteenUser = true;
    this.studentParent = this.adminService.GetParent();
    if (!this.studentParent) {
      this.GoBackClick();
    } else {
      const parentBalance = Number(this.studentParent.SpriggyBalance);
      const orderToEditPrice = this.orderToEdit ? this.orderToEdit.Price : 0;
      this.parentBalanceRemaining = parentBalance + orderToEditPrice;
    }
  }
  GoBackClick() {
    this.location.back();
  }
  ClickCancelChanges() {
    this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_0__.restoreOldShoppingCart());
  }
  isEditOrderWithNoChanges() {
    return this.orderToEdit && this.editCartNotChanged;
  }
  canCancelChanges() {
    return !this.isMobile && !this.isEditOrderWithNoChanges();
  }
  disableConfirmButton() {
    const orderPriceMoreThanWalletBalance = this.cartPrice > this.parentBalanceRemaining;
    const adminNotEnoughFunds = this.isAdminCanteenUser && orderPriceMoreThanWalletBalance;
    return this.isEditOrderWithNoChanges() || adminNotEnoughFunds;
  }
  //////////////////////////////////////////////////
  // Cart
  //////////////////////////////////////////////////
  ClearCart() {
    this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_0__.clearCart());
  }
  confirmClearCart() {
    const data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = 'Clear all?';
    data.Text = 'This will remove all items from your cart. Do you want to continue?';
    data.CancelButton = 'Cancel';
    data.ConfirmButton = 'Yes, clear all';
    const dialogRef = this.dialog.open(_shared_components___WEBPACK_IMPORTED_MODULE_3__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ClearCart();
      }
    });
  }
  RemoveFromCart(itemCartId) {
    this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_0__.removeItem({
      itemCartId: itemCartId
    }));
  }
  InputChanged(itemCartId, event) {
    const quantity = event.target.value;
    this.store.dispatch(_states_shoppingCart_shopping_cart_actions__WEBPACK_IMPORTED_MODULE_0__.updateQuantity({
      itemCartId,
      quantity: quantity
    }));
  }
  //////////////////////////////////////////////////
  // Order
  //////////////////////////////////////////////////
  OrderClick() {
    let dialogRef;
    this.fullScreenDialog = this.deviceService.isMobile();
    this.spinnerService.start();
    let placeOrderData = {
      groupedCarts: this.shoppingCartForDisplay,
      editOrderId: this.orderToEdit ? this.orderToEdit.OrderId : 0
    };
    if (this.fullScreenDialog) {
      dialogRef = this.dialog.open(_dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_2__.DialogPlaceOrderComponent, {
        maxWidth: '100vw',
        maxHeight: '100vh',
        height: '100%',
        width: '100%',
        panelClass: 'custom-dialog-container',
        disableClose: true,
        data: placeOrderData
      });
    } else {
      dialogRef = this.dialog.open(_dialog_place_order_dialog_place_order_component__WEBPACK_IMPORTED_MODULE_2__.DialogPlaceOrderComponent, {
        width: '500px',
        disableClose: true,
        data: placeOrderData
      });
    }
    this.spinnerService.stop();
    dialogRef.afterClosed().subscribe(errorOccurred => {
      if (errorOccurred) {
        this.showErrorDialog();
      }
    });
  }
  showErrorDialog() {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `Try again in a minute. If it’s still not working give Member Help a shout.`;
    data.ConfirmButton = 'Ok';
    this.dialog.open(_shared_components___WEBPACK_IMPORTED_MODULE_3__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
  }
  static {
    this.ɵfac = function ShoppingCartComponent_Factory(t) {
      return new (t || ShoppingCartComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_15__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_8__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_16__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_17__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_18__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](ngx_device_detector__WEBPACK_IMPORTED_MODULE_19__.DeviceDetectorService), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_8__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_8__.AdminService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵdefineComponent"]({
      type: ShoppingCartComponent,
      selectors: [["shopping-cart"]],
      decls: 10,
      vars: 10,
      consts: [[3, "ngClass"], ["class", "row", 4, "ngIf"], [4, "ngFor", "ngForOf"], ["class", "row totalRow", 4, "ngIf"], [1, "row"], ["class", "col-12", 4, "ngIf"], [1, "col-8"], ["text", "Shopping Cart", 3, "navBack"], [1, "col-4"], [3, "showButton", "pressed"], [1, "title"], [1, "col-12"], [1, "cartTitle"], ["class", "row itemRow pt-2", 4, "ngFor", "ngForOf"], [1, "row", "totalRow"], [1, "row", "itemRow", "pt-2"], [1, "col-3", "col-sm-5", "col-md-4", "col-lg-3"], ["appearance", "outline"], ["matNativeControl", "", "id", "cart-item-quantity-picker", 3, "ngModel", "change"], ["value", "1", 4, "ngIf"], ["value", "2", 4, "ngIf"], ["value", "3", 4, "ngIf"], ["value", "4", 4, "ngIf"], ["value", "5", 4, "ngIf"], [1, "col-7", "col-sm-5", "col-md-6", "col-lg-7"], [1, "itemName"], [1, "col-1"], [1, "removeFromCart"], ["aria-hidden", "false", "aria-label", "Remove from cart", "id", "remove-cart-item-button", 3, "click"], ["value", "1"], ["value", "2"], ["value", "3"], ["value", "4"], ["value", "5"], [1, "spacerDescription"], [3, "text", "disabled", "onPress"], [1, "col-12", "pt-2"], ["mat-button", "", "color", "warn", "class", "WarnLink", 3, "click", 4, "ngIf"], ["mat-button", "", "color", "warn", 1, "WarnLink", 3, "click"], ["class", "noFunds", 4, "ngIf"], [1, "noFunds"]],
      template: function ShoppingCartComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](1, ShoppingCartComponent_div_1_Template, 5, 1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](2, ShoppingCartComponent_div_2_Template, 6, 1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](3, ShoppingCartComponent_ng_container_3_Template, 13, 13, "ng-container", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](4, ShoppingCartComponent_div_4_Template, 5, 3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementStart"](5, "div")(6, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](7, ShoppingCartComponent_div_7_Template, 3, 5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](8, ShoppingCartComponent_div_8_Template, 3, 1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵtemplate"](9, ShoppingCartComponent_div_9_Template, 6, 4, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵpureFunction1"](8, _c1, ctx.isMobile));
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx.isMobile);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !ctx.isMobile);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngForOf", ctx.shoppingCartForDisplay);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", !ctx.showPlaceOrder && !ctx.shoppingCartForDisplay.length);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx.showPlaceOrder);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx.canCancelChanges());
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_14__["ɵɵproperty"]("ngIf", ctx.isAdminCanteenUser && ctx.studentParent);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_17__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_17__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_17__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_20__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_20__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_20__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_20__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_20__.NgModel, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_9__.NavBackButtonComponent, _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_10__.PrimaryButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_21__.MatFormField, _angular_material_icon__WEBPACK_IMPORTED_MODULE_22__.MatIcon, _angular_material_input__WEBPACK_IMPORTED_MODULE_23__.MatInput, _angular_material_button__WEBPACK_IMPORTED_MODULE_24__.MatButton, _clear_cart_button_clear_cart_button_component__WEBPACK_IMPORTED_MODULE_11__.ClearCartButtonComponent, _angular_common__WEBPACK_IMPORTED_MODULE_17__.CurrencyPipe, _angular_common__WEBPACK_IMPORTED_MODULE_17__.DatePipe, _shared_components_menu_custom_menu_name_pipe__WEBPACK_IMPORTED_MODULE_12__.CustomMenuNamePipe, _sharedPipes_calculate_cart_items_price_pipe__WEBPACK_IMPORTED_MODULE_13__.CalculateCartItemsPricePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  vertical-align: center;\n}\n\n.itemRow[_ngcontent-%COMP%] {\n  background-color: white;\n  border-bottom: 1px solid #dddddd;\n  display: flex;\n}\n.itemRow[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  max-width: 100%;\n}\n.itemRow[_ngcontent-%COMP%]   .itemName[_ngcontent-%COMP%] {\n  font-size: 20px;\n  margin: 0;\n  padding: 0;\n}\n.itemRow[_ngcontent-%COMP%]   .spacerDescription[_ngcontent-%COMP%] {\n  font-size: 30px;\n  line-height: 16px;\n  font-weight: bold;\n}\n\n.totalRow[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n  background-color: white;\n  border-bottom: 1px solid #dddddd;\n  text-align: right;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n.totalRow[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  margin-top: 14px;\n  margin-bottom: 14px;\n  font-size: 20px;\n}\n\n.cartTitle[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n\n.removeFromCart[_ngcontent-%COMP%] {\n  padding-top: 15px;\n}\n.removeFromCart[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n\n.noFunds[_ngcontent-%COMP%] {\n  color: #f14762;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 7366:
/*!*****************************************************************!*\
  !*** ./src/app/manage-order/functions/convert-to-cart-items.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConvertOrderItemToCartType: () => (/* binding */ ConvertOrderItemToCartType),
/* harmony export */   GetGroupedShoppingCart: () => (/* binding */ GetGroupedShoppingCart)
/* harmony export */ });
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices/menu/menu-custom-name */ 76573);




function ConvertOrderItemToCartType(menuCutOffDateTime, order, index = 0, studentFirstName, date = null) {
  const cartDate = date ? date : order.OrderDate;
  const orderItem = order.Items[index];
  const menuName = (0,src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_3__.GetCustomMenuName)(order.MenuType, order.MenuName);
  return {
    menuItemId: orderItem.MenuItemId,
    quantity: orderItem.Quantity,
    itemPriceIncGst: orderItem.ItemPriceIncGst,
    name: orderItem.Name,
    date: new Date((0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.formatDateToUniversal)(cartDate)),
    studentId: order.StudentId,
    studentName: studentFirstName,
    schoolId: order.SchoolId,
    menuType: order.MenuType,
    menuId: order.MenuId,
    menuName: menuName,
    menuCutOffDateTime,
    canteenId: order.CanteenId,
    itemCartId: index + moment__WEBPACK_IMPORTED_MODULE_0___default()().unix(),
    selectedOptions: getSelectedOptions(orderItem.SelectedOptions)
  };
}
function getSelectedOptions(selectedOptions) {
  if (selectedOptions?.length === 0) {
    return [];
  }
  return selectedOptions.map(option => {
    return {
      menuItemOptionId: option.MenuItemOptionId,
      optionName: option.OptionName,
      optionCost: option.OptionCost,
      parentOptionId: option.MenuItemOptionsCategoryId
    };
  });
}
function GetGroupedShoppingCart(cartItems) {
  const groupedCartItems = SortCartItems(cartItems);
  return Object.values(groupedCartItems).map(cartArray => cartArray);
}
function SortCartItems(items) {
  return lodash__WEBPACK_IMPORTED_MODULE_2__.groupBy(items, item => [item.studentId, item.menuId, item.menuType, (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.formatDateToUniversal)(item.date), item.menuName]);
}

/***/ }),

/***/ 67412:
/*!*****************************************************************!*\
  !*** ./src/app/manage-order/functions/menu-item-sort-helper.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GetMenuItemsThatMatchOrderItemMenuId: () => (/* binding */ GetMenuItemsThatMatchOrderItemMenuId),
/* harmony export */   UpdateOrderItemsWithMenuData: () => (/* binding */ UpdateOrderItemsWithMenuData)
/* harmony export */ });
function GetMenuItemsThatMatchOrderItemMenuId(menuJSON, menuItemIdsToFind) {
  const allMenuItems = menuJSON.reduce((accumulator, currentValue) => [...accumulator, ...currentValue.item], []);
  return allMenuItems.filter(x => menuItemIdsToFind.includes(x.MenuItemId));
}
function UpdateOrderItemsWithMenuData(menuJSON, orderItems) {
  const orderItemIdList = orderItems.map(item => item.MenuItemId);
  const matchingMenuItems = GetMenuItemsThatMatchOrderItemMenuId(menuJSON, orderItemIdList);
  const updatedOrderItems = orderItems.map(x => {
    const match = matchingMenuItems.find(menuItem => menuItem.MenuItemId === x.MenuItemId);
    if (match) {
      return {
        ...x,
        Name: match.Name,
        ItemPriceIncGst: match.Price
      };
    }
  });
  return updatedOrderItems;
}

/***/ }),

/***/ 88900:
/*!*************************************************************!*\
  !*** ./src/app/manage-order/manage-order-routing.module.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageOrderRoutingModule: () => (/* binding */ ManageOrderRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices_order_orderWithCutOffTimes_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sharedServices/order/orderWithCutOffTimes.resolver */ 98522);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components */ 3386);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);


// components



const routes = [{
  path: '',
  children: [{
    path: '',
    pathMatch: 'full',
    redirectTo: 'place'
  }, {
    path: 'place',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.ManageOrderComponent
  }, {
    path: 'shoppingCart',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.ShoppingCartComponent,
    data: {
      isMobile: true
    }
  }, {
    path: 'history',
    loadChildren: () => Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ../order-history/order-history.module */ 15319)).then(m => m.OrderHistoryModule)
  }, {
    path: 'selectedOrderHistory/:orderId',
    component: _components__WEBPACK_IMPORTED_MODULE_1__.SelectedOrderHistoryComponent,
    resolve: {
      orderWithCutOffTimes: _sharedServices_order_orderWithCutOffTimes_resolver__WEBPACK_IMPORTED_MODULE_0__.OrderWithCutOffTimesResolver
    }
  }]
}];
class ManageOrderRoutingModule {
  static {
    this.ɵfac = function ManageOrderRoutingModule_Factory(t) {
      return new (t || ManageOrderRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: ManageOrderRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](ManageOrderRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 32261:
/*!*****************************************************!*\
  !*** ./src/app/manage-order/manage-order.module.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageOrderModule: () => (/* binding */ ManageOrderModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/bottom-sheet */ 15244);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _manage_order_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./manage-order-routing.module */ 88900);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../payment/payment.module */ 42047);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ 3386);
/* harmony import */ var _components_new_order_details_new_order_details_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/new-order-details/new-order-details.component */ 77460);
/* harmony import */ var _components_edit_order_details_edit_order_details_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/edit-order-details/edit-order-details.component */ 47258);
/* harmony import */ var _order_history_order_history_module__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../order-history/order-history.module */ 15319);
/* harmony import */ var _sharedPipes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../sharedPipes */ 52151);
/* harmony import */ var _components_clear_cart_button_clear_cart_button_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/clear-cart-button/clear-cart-button.component */ 42580);
/* harmony import */ var _components_category_tile_category_tile_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/category-tile/category-tile.component */ 76032);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/core */ 37580);


// material










// module




// components




//pipes




class ManageOrderModule {
  static {
    this.ɵfac = function ManageOrderModule_Factory(t) {
      return new (t || ManageOrderModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineNgModule"]({
      type: ManageOrderModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵdefineInjector"]({
      imports: [_components__WEBPACK_IMPORTED_MODULE_4__.ReorderFilterComponent, _angular_common__WEBPACK_IMPORTED_MODULE_12__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormsModule, _manage_order_routing_module__WEBPACK_IMPORTED_MODULE_0__.ManageOrderRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__.PaymentModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsButtonModule,
      // material
      _angular_material_form_field__WEBPACK_IMPORTED_MODULE_14__.MatFormFieldModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_15__.MatCardModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MatDialogModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_18__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_19__.MatButtonModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_20__.MatExpansionModule, _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_21__.MatBottomSheetModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_22__.MatCheckboxModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__.MatTooltipModule, _order_history_order_history_module__WEBPACK_IMPORTED_MODULE_7__.OrderHistoryModule, _components_clear_cart_button_clear_cart_button_component__WEBPACK_IMPORTED_MODULE_9__.ClearCartButtonComponent, _components_category_tile_category_tile_component__WEBPACK_IMPORTED_MODULE_10__.CategoryTileComponent]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_11__["ɵɵsetNgModuleScope"](ManageOrderModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_4__.ManageOrderComponent, _components__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartComponent, _components__WEBPACK_IMPORTED_MODULE_4__.SelectedOrderHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_4__.DialogPlaceOrderComponent, _components__WEBPACK_IMPORTED_MODULE_4__.OrdersDetailsComponent, _components__WEBPACK_IMPORTED_MODULE_4__.OrdersPlacedComponent, _components__WEBPACK_IMPORTED_MODULE_4__.FiltersComponent, _components__WEBPACK_IMPORTED_MODULE_4__.FilterMenuDateSheetComponent, _components__WEBPACK_IMPORTED_MODULE_4__.FiltersItemsComponent, _components__WEBPACK_IMPORTED_MODULE_4__.FiltersItemsSheetComponent, _components_new_order_details_new_order_details_component__WEBPACK_IMPORTED_MODULE_5__.NewOrderDetailsComponent, _components_edit_order_details_edit_order_details_component__WEBPACK_IMPORTED_MODULE_6__.EditOrderDetailsComponent],
    imports: [_components__WEBPACK_IMPORTED_MODULE_4__.ReorderFilterComponent, _angular_common__WEBPACK_IMPORTED_MODULE_12__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_13__.FormsModule, _manage_order_routing_module__WEBPACK_IMPORTED_MODULE_0__.ManageOrderRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__.PaymentModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsButtonModule,
    // material
    _angular_material_form_field__WEBPACK_IMPORTED_MODULE_14__.MatFormFieldModule, _angular_material_card__WEBPACK_IMPORTED_MODULE_15__.MatCardModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.MatDialogModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_17__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_18__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_19__.MatButtonModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_20__.MatExpansionModule, _angular_material_bottom_sheet__WEBPACK_IMPORTED_MODULE_21__.MatBottomSheetModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_22__.MatCheckboxModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__.MatTooltipModule, _order_history_order_history_module__WEBPACK_IMPORTED_MODULE_7__.OrderHistoryModule, _sharedPipes__WEBPACK_IMPORTED_MODULE_8__.MoneyButtonDisplayPipe, _sharedPipes__WEBPACK_IMPORTED_MODULE_8__.CalculateOrderItemsPricePipe, _sharedPipes__WEBPACK_IMPORTED_MODULE_8__.PlaceOrderButtonTextPipe, _sharedPipes__WEBPACK_IMPORTED_MODULE_8__.AbsoluteMoneyValuePipe, _components_clear_cart_button_clear_cart_button_component__WEBPACK_IMPORTED_MODULE_9__.ClearCartButtonComponent, _sharedPipes__WEBPACK_IMPORTED_MODULE_8__.CalculateCartItemsPricePipe, _components_category_tile_category_tile_component__WEBPACK_IMPORTED_MODULE_10__.CategoryTileComponent]
  });
})();

/***/ }),

/***/ 68871:
/*!***************************************************************************************!*\
  !*** ./src/app/order-history/components/canteen-history/canteen-history.component.ts ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenHistoryComponent: () => (/* binding */ CanteenHistoryComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var _common_order_history_group_order_history_helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../common-order-history/group-order-history-helper */ 83777);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _shared_components_weeks_list_weeks_list_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared/components/weeks-list/weeks-list.component */ 70919);
/* harmony import */ var _shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components/responsive-header/responsive-header.component */ 24613);
/* harmony import */ var _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../order-history-list/order-history-list.component */ 72577);
// Models









class CanteenHistoryComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(location, orderApiService) {
    super();
    this.location = location;
    this.orderApiService = orderApiService;
    this.listOrders = [];
  }
  ngOnInit() {}
  weekChanged(weekNumber) {
    this.loading = true;
    const request = this.getOrderHistoryRequest(weekNumber);
    this.orderApiService.getCanteenOrderHistoryByParent(request).subscribe({
      next: res => {
        this.listOrders = (0,_common_order_history_group_order_history_helper__WEBPACK_IMPORTED_MODULE_2__.SortGroupOrderHistory)(res);
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        this.handleErrorFromService(error);
      }
    });
  }
  goBackClick() {
    this.location.back();
  }
  getOrderHistoryRequest(weekNumber) {
    const startDate = this.getWeekDate(weekNumber, true);
    const endDate = this.getWeekDate(weekNumber, false);
    return {
      StartDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.formatDateToUniversal)(startDate),
      EndDate: (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.formatDateToUniversal)(endDate)
    };
  }
  getWeekDate(weekNumber, getStartOfWeekDate) {
    const date = getStartOfWeekDate ? (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.GetWeekStartDate)(weekNumber) : (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.GetWeekEndDate)(weekNumber);
    //Subtract day to get start of week date
    //Add date to get end of week date
    return getStartOfWeekDate ? (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.SubtractDayToDate)(date, 1) : (0,src_app_utility__WEBPACK_IMPORTED_MODULE_1__.addDayToDate)(date, 1);
  }
  static {
    this.ɵfac = function CanteenHistoryComponent_Factory(t) {
      return new (t || CanteenHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.OrderApiService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: CanteenHistoryComponent,
      selectors: [["app-canteen-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"]],
      decls: 6,
      vars: 4,
      consts: [["text", "Canteen History"], [1, "container-fluid"], [1, "row", "filter"], [1, "col-12", "pb-2"], [3, "pastWeeks", "noShadow", "weekChanged"], [3, "listOrders", "loading"]],
      template: function CanteenHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](0, "responsive-header", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "cashless-weeks-list", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("weekChanged", function CanteenHistoryComponent_Template_cashless_weeks_list_weekChanged_4_listener($event) {
            return ctx.weekChanged($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](5, "order-history-list", 5);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("pastWeeks", true)("noShadow", true);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("listOrders", ctx.listOrders)("loading", ctx.loading);
        }
      },
      dependencies: [_shared_components_weeks_list_weeks_list_component__WEBPACK_IMPORTED_MODULE_4__.WeeksListComponent, _shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_5__.ResponsiveHeaderComponent, _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_6__.OrderHistoryListComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 52690:
/*!***************************************************************************************!*\
  !*** ./src/app/order-history/components/common-order-history/common-order-history.ts ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CommonOrderHistoryClass: () => (/* binding */ CommonOrderHistoryClass)
/* harmony export */ });
/* harmony import */ var _group_order_history_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./group-order-history-helper */ 83777);

class CommonOrderHistoryClass {
  constructor(route) {
    this.route = route;
    this.listOrders = [];
    this.loading = false;
  }
  onInitFunction() {
    const orders = this.route.snapshot.data['orders'];
    this.listOrders = (0,_group_order_history_helper__WEBPACK_IMPORTED_MODULE_0__.SortGroupOrderHistory)(orders);
  }
}

/***/ }),

/***/ 21467:
/*!***********************************************************************************!*\
  !*** ./src/app/order-history/components/event-history/event-history.component.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EventHistoryComponent: () => (/* binding */ EventHistoryComponent)
/* harmony export */ });
/* harmony import */ var _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common-order-history/common-order-history */ 52690);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared/components/responsive-header/responsive-header.component */ 24613);
/* harmony import */ var _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../order-history-list/order-history-list.component */ 72577);





class EventHistoryComponent extends _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_0__.CommonOrderHistoryClass {
  constructor(route) {
    super(route);
    this.route = route;
    this.listOrders = [];
    this.loading = false;
  }
  ngOnInit() {
    this.onInitFunction();
  }
  static {
    this.ɵfac = function EventHistoryComponent_Factory(t) {
      return new (t || EventHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: EventHistoryComponent,
      selectors: [["app-event-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 2,
      vars: 2,
      consts: [["text", "Event History"], [3, "listOrders", "loading"]],
      template: function EventHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "responsive-header", 0)(1, "order-history-list", 1);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("listOrders", ctx.listOrders)("loading", ctx.loading);
        }
      },
      dependencies: [_shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_1__.ResponsiveHeaderComponent, _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_2__.OrderHistoryListComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 85341:
/*!***************************************************!*\
  !*** ./src/app/order-history/components/index.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenHistoryComponent: () => (/* reexport safe */ _canteen_history_canteen_history_component__WEBPACK_IMPORTED_MODULE_0__.CanteenHistoryComponent),
/* harmony export */   CommonOrderHistoryClass: () => (/* reexport safe */ _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_5__.CommonOrderHistoryClass),
/* harmony export */   EventHistoryComponent: () => (/* reexport safe */ _event_history_event_history_component__WEBPACK_IMPORTED_MODULE_2__.EventHistoryComponent),
/* harmony export */   OrderHistoryComponent: () => (/* reexport safe */ _order_history_order_history_component__WEBPACK_IMPORTED_MODULE_1__.OrderHistoryComponent),
/* harmony export */   OrderHistoryRowComponent: () => (/* reexport safe */ _order_history_row_order_history_row_component__WEBPACK_IMPORTED_MODULE_4__.OrderHistoryRowComponent),
/* harmony export */   UniformHistoryComponent: () => (/* reexport safe */ _uniform_history_uniform_history_component__WEBPACK_IMPORTED_MODULE_3__.UniformHistoryComponent)
/* harmony export */ });
/* harmony import */ var _canteen_history_canteen_history_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canteen-history/canteen-history.component */ 68871);
/* harmony import */ var _order_history_order_history_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./order-history/order-history.component */ 69420);
/* harmony import */ var _event_history_event_history_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./event-history/event-history.component */ 21467);
/* harmony import */ var _uniform_history_uniform_history_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uniform-history/uniform-history.component */ 923);
/* harmony import */ var _order_history_row_order_history_row_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./order-history-row/order-history-row.component */ 10639);
/* harmony import */ var _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./common-order-history/common-order-history */ 52690);







/***/ }),

/***/ 72577:
/*!*********************************************************************************************!*\
  !*** ./src/app/order-history/components/order-history-list/order-history-list.component.ts ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderHistoryListComponent: () => (/* binding */ OrderHistoryListComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/states/children/children.selectors */ 54186);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared/components/spinner/spinner.component */ 71517);
/* harmony import */ var _order_history_row_order_history_row_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../order-history-row/order-history-row.component */ 10639);







function OrderHistoryListComponent_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0, 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "app-spinner", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("manual", true);
  }
}
function OrderHistoryListComponent_ng_template_2_ng_container_0_div_1_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "order-history-row", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const order_r9 = ctx.$implicit;
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("order", order_r9)("student", ctx_r8.getOrderChild(order_r9.StudentId));
  }
}
function OrderHistoryListComponent_ng_template_2_ng_container_0_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 10)(1, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, OrderHistoryListComponent_ng_template_2_ng_container_0_div_1_div_3_Template, 2, 2, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const groupedOrders_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](groupedOrders_r7.date);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", groupedOrders_r7.items);
  }
}
function OrderHistoryListComponent_ng_template_2_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, OrderHistoryListComponent_ng_template_2_ng_container_0_div_1_Template, 4, 2, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "That's all of your history");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r3.listOrders);
  }
}
function OrderHistoryListComponent_ng_template_2_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "p", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "You have no order history");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function OrderHistoryListComponent_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](0, OrderHistoryListComponent_ng_template_2_ng_container_0_Template, 4, 1, "ng-container", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, OrderHistoryListComponent_ng_template_2_ng_template_1_Template, 2, 0, "ng-template", null, 7, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplateRefExtractor"]);
  }
  if (rf & 2) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](2);
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", (ctx_r2.listOrders == null ? null : ctx_r2.listOrders.length) > 0)("ngIfElse", _r4);
  }
}
class OrderHistoryListComponent {
  constructor(store) {
    this.store = store;
    this.listOrders = [];
    this.loading = false;
    this.listChildren = [];
  }
  ngOnInit() {
    this.subscriptionChildren$ = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_4__.select)(src_app_states_children_children_selectors__WEBPACK_IMPORTED_MODULE_0__.children)).subscribe(children => {
      this.listChildren = children.list;
    });
  }
  ngOnDestroy() {
    this.subscriptionChildren$?.unsubscribe();
  }
  getOrderChild(studentId) {
    return this.listChildren?.find(f => f.UserId == studentId);
  }
  static {
    this.ɵfac = function OrderHistoryListComponent_Factory(t) {
      return new (t || OrderHistoryListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_4__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: OrderHistoryListComponent,
      selectors: [["order-history-list"]],
      inputs: {
        listOrders: "listOrders",
        loading: "loading"
      },
      decls: 4,
      vars: 2,
      consts: [[1, "col-lg-6", "col-md-8", "col-sm-12"], ["class", "row justify-content-center", 4, "ngIf", "ngIfElse"], ["displayOrders", ""], [1, "row", "justify-content-center"], [1, "col-3", "col-sm-3", "col-lg-1", "justify-content-center"], [3, "manual"], [4, "ngIf", "ngIfElse"], ["noOrders", ""], ["class", "pb-2", 4, "ngFor", "ngForOf"], [1, "message"], [1, "pb-2"], [3, "order", "student"]],
      template: function OrderHistoryListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, OrderHistoryListComponent_ng_container_1_Template, 3, 1, "ng-container", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, OrderHistoryListComponent_ng_template_2_Template, 3, 2, "ng-template", null, 2, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loading)("ngIfElse", _r1);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _shared_components_spinner_spinner_component__WEBPACK_IMPORTED_MODULE_1__.SpinnerComponent, _order_history_row_order_history_row_component__WEBPACK_IMPORTED_MODULE_2__.OrderHistoryRowComponent],
      styles: [".message[_ngcontent-%COMP%] {\n  text-align: center;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvb3JkZXItaGlzdG9yeS9jb21wb25lbnRzL29yZGVyLWhpc3RvcnktbGlzdC9vcmRlci1oaXN0b3J5LWxpc3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxrQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLm1lc3NhZ2Uge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 10639:
/*!*******************************************************************************************!*\
  !*** ./src/app/order-history/components/order-history-row/order-history-row.component.ts ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderHistoryRowComponent: () => (/* binding */ OrderHistoryRowComponent)
/* harmony export */ });
/* harmony import */ var src_app_utility__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/utility */ 31437);
/* harmony import */ var src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/utility/timezone-helper */ 92471);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var src_app_sharedServices_order_create_order_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices/order/create-order.service */ 66733);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_menu_custom_menu_name_pipe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/menu/custom-menu-name.pipe */ 94552);
/* harmony import */ var _sharedPipes_order_history_status_pipe__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../sharedPipes/order-history-status.pipe */ 52183);








class OrderHistoryRowComponent {
  constructor(router, createOrderService) {
    this.router = router;
    this.createOrderService = createOrderService;
  }
  ngOnChanges(changes) {
    if (changes.order?.currentValue) {
      const menuCutOff = this.order?.MenuCutOffTime ? (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_1__.convertSchoolTimeToLocalTime)(this.order.MenuCutOffTime, this.student.SchoolTimeZoneOffSetHours) : null;
      const eventCutOff = this.order.EventCutOffDate ? (0,src_app_utility_timezone_helper__WEBPACK_IMPORTED_MODULE_1__.convertSchoolDateTimeToLocalDateTime)(this.order.EventCutOffDate, this.student.SchoolTimeZoneOffSetHours) : null;
      this.orderToShow = {
        ...this.order,
        MenuCutOffTime: menuCutOff,
        EventCutOffDate: eventCutOff
      };
    }
  }
  viewSelectedOrder() {
    this.createOrderService.getAndSetDayDetail(this.order.MenuType, this.order.MenuName, this.order.MenuId, (0,src_app_utility__WEBPACK_IMPORTED_MODULE_0__.ConvertStringToDate)(this.order.OrderDate), this.student, null,
    // will get cut off time in selected order history screen
    this.order);
    this.router.navigate(['family/order/selectedOrderHistory/', this.order.OrderId]);
  }
  static {
    this.ɵfac = function OrderHistoryRowComponent_Factory(t) {
      return new (t || OrderHistoryRowComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_sharedServices_order_create_order_service__WEBPACK_IMPORTED_MODULE_2__.CreateOrderService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: OrderHistoryRowComponent,
      selectors: [["order-history-row"]],
      inputs: {
        order: "order",
        student: "student"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵNgOnChangesFeature"]],
      decls: 14,
      vars: 12,
      consts: [[1, "row-container", 3, "click"], [1, "left-info"], [1, "title"], [1, "subtitle"], [1, "right-info"], [1, "title", "price", "align-right"], [1, "subtitle", "align-right"]],
      template: function OrderHistoryRowComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function OrderHistoryRowComponent_Template_div_click_0_listener() {
            return ctx.viewSelectedOrder();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 1)(2, "p", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "p", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](6, "customMenuName");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "div", 4)(8, "p", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](10, "currency");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](13, "orderHistoryStatus");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx.order.StudentName);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](6, 5, ctx.order.MenuType, ctx.order.MenuName), " - ", ctx.order == null ? null : ctx.order.Items == null ? null : ctx.order.Items.length, " items ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](10, 8, ctx.order.Price + ctx.order.OrderFee));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](13, 10, ctx.orderToShow), " ");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.CurrencyPipe, _shared_components_menu_custom_menu_name_pipe__WEBPACK_IMPORTED_MODULE_3__.CustomMenuNamePipe, _sharedPipes_order_history_status_pipe__WEBPACK_IMPORTED_MODULE_4__.OrderHistoryStatusPipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.row-container[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  padding: 16px;\n  border-radius: 8px;\n  display: flex;\n  justify-content: space-between;\n  align-self: center;\n  cursor: pointer;\n  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.row-container[_ngcontent-%COMP%]:hover {\n  background-color: #ffead6;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 700;\n  margin: 0;\n  padding-bottom: 8px;\n}\n\n.subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 14px;\n}\n\n.align-right[_ngcontent-%COMP%] {\n  text-align: right;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvb3JkZXItaGlzdG9yeS9jb21wb25lbnRzL29yZGVyLWhpc3Rvcnktcm93L29yZGVyLWhpc3Rvcnktcm93LmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vc3JjL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUtBO0VBQ0UsYUFBQTtBQ0pGO0FES0U7RUFGRjtJQUdJLGNBQUE7RUNGRjtBQUNGOztBREtBO0VBQ0UsYUFBQTtBQ0ZGO0FER0U7RUFGRjtJQUdJLGNBQUE7RUNBRjtBQUNGOztBQWRBO0VBQ0UseUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSwyQ0FBQTtBQWlCRjs7QUFkQTtFQUNFLHlCQ2dCUztBRENYOztBQWRBO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBaUJGOztBQWRBO0VBQ0UsU0FBQTtFQUNBLGVBQUE7QUFpQkY7O0FBZEE7RUFDRSxpQkFBQTtBQWlCRiIsInNvdXJjZXNDb250ZW50IjpbIiRicmVha3BvaW50LXNtOiA1NzZweDtcbiRicmVha3BvaW50LW1kOiA3NjdweDtcbiRicmVha3BvaW50LWxnOiA5OTJweDtcbiRicmVha3BvaW50LXhsOiAxMjAwcHg7XG5cbi5tb2JpbGUge1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1heC13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuLy8gTk9URSBDdXJyZW50bHkgdGFibGV0IGFuZCBtb2JpbGUgaXMgdGhlIHNhbWUuIENoYW5nZSB0byAkYnJlYWtwb2ludC1sZyBsYXRlciBpZiB3ZSBnZXQgYSBwcm9wZXIgdGFibGV0IGRlc2lnbi5cbi5kZXNrdG9wIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtaW4td2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbiIsIkBpbXBvcnQgJy4uLy4uLy4uLy4uL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzJztcblxuLnJvdy1jb250YWluZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBwYWRkaW5nOiAxNnB4O1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24tc2VsZjogY2VudGVyO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGJveC1zaGFkb3c6IDBweCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi5yb3ctY29udGFpbmVyOmhvdmVyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogJG9yYW5nZS03O1xufVxuXG4udGl0bGUge1xuICBmb250LXNpemU6IDE2cHg7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIG1hcmdpbjogMDtcbiAgcGFkZGluZy1ib3R0b206IDhweDtcbn1cblxuLnN1YnRpdGxlIHtcbiAgbWFyZ2luOiAwO1xuICBmb250LXNpemU6IDE0cHg7XG59XG5cbi5hbGlnbi1yaWdodCB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xufVxuIiwiQGltcG9ydCAnY2FzaGxlc3MtYnJlYWtwb2ludHMnO1xuXG4vLyBQcmltYXJ5IGNvbG91cnNcblxuJGJsdWUtMTogIzFlYTNjZTtcbiRibHVlLTI6ICM0NzU0YjA7XG4kYmx1ZS0zOiAjMTQ0Y2RjO1xuXG4kcmVkLTE6ICNmMTQ3NjI7XG4kcmVkLTI6ICNmZmViZWI7XG4kcmVkLTM6ICNjMDQ1NDU7XG4kcmVkLTQ6ICNmZmNmY2M7XG5cbiRwdXJwbGUtMTogIzdmM2RiMztcbiRuYXZ5LTE6ICMxYzQyNzA7XG4kY2hhcmNvYWwtMTogIzMzM2I0NDtcblxuJGdyZWVuLTE6ICMwMGJhNmI7XG4kZ3JlZW4tMjogI2Q5ZjVlOTtcbiRncmVlbi0zOiAjMDA2ZjQ5O1xuJGdyZWVuLTQ6ICNlM2Y1ZWY7XG4kZ3JlZW4tNTogI2RmZmZmMDtcblxuLy8gT3JhbmdlXG4kb3JhbmdlLTE6ICNmZjllMDA7XG4kb3JhbmdlLTM6ICNmZjdhMDA7XG4kb3JhbmdlLTI6ICNmZjRiMTc7XG4kb3JhbmdlLTQ6ICNmZmUyYzc7XG4kb3JhbmdlLTU6ICNmZmYyZTY7XG4kb3JhbmdlLTY6ICNmZjgwMDA7XG4kb3JhbmdlLTc6ICNmZmVhZDY7XG4kb3JhbmdlLTg6ICNmZWYwZTA7XG4kb3JhbmdlLTk6ICNmZmYwZTA7XG4kb3JhbmdlLTEwOiAjZjM2NjAwO1xuJG9yYW5nZS0xMTogI2ZmZTNiYjtcbiRtb2JpbGUtZGFyay1vcmFuZ2U6ICNEOTVCMDA7XG4kbW9iaWxlLWxpZ2h0LW9yYW5nZTogI0ZGRUFENjtcblxuLy8gZ3JleVxuJGdyZXktMTogIzg4OTQ5ZjtcbiRncmV5LTI6ICNlMGUxZTI7XG4kZ3JleS0zOiAjZGRkZGRkO1xuJGdyZXktNDogI2YyZjJmMjtcbiRncmV5LTU6ICMzMzNiNDQ7XG4kZ3JleS02OiAjZTVlNWU1O1xuJGdyZXktNzogI2I5YjljODtcbiRncmV5LTg6ICM4Nzg3ODc7XG4kZ3JleS05OiAjZTBlMGUwO1xuJGdyZXktMTA6ICNiZGJkYmQ7XG4kZ3JleS0xMTogIzgyODI4MjtcbiRncmV5LTEyOiAjMWIxZjM5O1xuJGdyZXktMTM6ICNiOGI4Yjg7XG4kZ3JleS0xNDogIzI3MmM1MDtcbiRncmV5LTE1OiAjZjZmNWYzO1xuJGdyZXktMTY6ICNmYWY5Zjg7XG4kZ3JleS0xNzogIzZiNmM4OTtcblxuLy8gU2Vjb25kYXJ5IGNvbG91cnNcbiRibHVlLXNlY29uZGFyeS0xOiByZ2JhKDI1NSwgMjQzLCAyMTksIDEpO1xuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 69420:
/*!***********************************************************************************!*\
  !*** ./src/app/order-history/components/order-history/order-history.component.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderHistoryComponent: () => (/* binding */ OrderHistoryComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../shared/components/responsive-header/responsive-header.component */ 24613);
/* harmony import */ var _shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared-tools/components/settings-row/settings-row.component */ 7429);



class OrderHistoryComponent {
  static {
    this.ɵfac = function OrderHistoryComponent_Factory(t) {
      return new (t || OrderHistoryComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: OrderHistoryComponent,
      selectors: [["app-order-history"]],
      decls: 8,
      vars: 0,
      consts: [["text", "Order History"], [1, "container-fluid"], [1, "row"], [1, "col-md-8", "col-sm-12"], [1, "settingsWrapper"], ["text", "Canteen history", "route", "/family/order/history/canteen"], ["text", "Event history", "route", "/family/order/history/event"], ["text", "Uniform history", "route", "/family/order/history/uniform", "lastRow", "true"]],
      template: function OrderHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "responsive-header", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](5, "settings-row", 5)(6, "settings-row", 6)(7, "settings-row", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
        }
      },
      dependencies: [_shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_0__.ResponsiveHeaderComponent, _shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_1__.SettingsRowComponent],
      styles: [".settingsWrapper[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  overflow: hidden;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvb3JkZXItaGlzdG9yeS9jb21wb25lbnRzL29yZGVyLWhpc3Rvcnkvb3JkZXItaGlzdG9yeS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5zZXR0aW5nc1dyYXBwZXIge1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 923:
/*!***************************************************************************************!*\
  !*** ./src/app/order-history/components/uniform-history/uniform-history.component.ts ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UniformHistoryComponent: () => (/* binding */ UniformHistoryComponent)
/* harmony export */ });
/* harmony import */ var _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common-order-history/common-order-history */ 52690);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared/components/responsive-header/responsive-header.component */ 24613);
/* harmony import */ var _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../order-history-list/order-history-list.component */ 72577);





class UniformHistoryComponent extends _common_order_history_common_order_history__WEBPACK_IMPORTED_MODULE_0__.CommonOrderHistoryClass {
  constructor(route) {
    super(route);
    this.route = route;
  }
  ngOnInit() {
    this.onInitFunction();
  }
  static {
    this.ɵfac = function UniformHistoryComponent_Factory(t) {
      return new (t || UniformHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: UniformHistoryComponent,
      selectors: [["app-uniform-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 2,
      vars: 2,
      consts: [["text", "Uniform History"], [3, "listOrders", "loading"]],
      template: function UniformHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "responsive-header", 0)(1, "order-history-list", 1);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("listOrders", ctx.listOrders)("loading", ctx.loading);
        }
      },
      dependencies: [_shared_components_responsive_header_responsive_header_component__WEBPACK_IMPORTED_MODULE_1__.ResponsiveHeaderComponent, _order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_2__.OrderHistoryListComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 23278:
/*!***************************************************************!*\
  !*** ./src/app/order-history/order-history-routing.module.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderHistoryRoutingModule: () => (/* binding */ OrderHistoryRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 85341);
/* harmony import */ var _resolvers_uniform_event_history_resolver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolvers/uniform-event-history.resolver */ 6054);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);

// components




const routes = [{
  path: '',
  children: [{
    path: '',
    pathMatch: 'full',
    redirectTo: ''
  }, {
    path: '',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.OrderHistoryComponent
  }, {
    path: 'uniform',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.UniformHistoryComponent,
    resolve: {
      orders: _resolvers_uniform_event_history_resolver__WEBPACK_IMPORTED_MODULE_1__.UniformEventHistoryResolver
    }
  }, {
    path: 'canteen',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.CanteenHistoryComponent
  }, {
    path: 'event',
    component: _components__WEBPACK_IMPORTED_MODULE_0__.EventHistoryComponent,
    resolve: {
      orders: _resolvers_uniform_event_history_resolver__WEBPACK_IMPORTED_MODULE_1__.UniformEventHistoryResolver
    }
  }]
}];
class OrderHistoryRoutingModule {
  static {
    this.ɵfac = function OrderHistoryRoutingModule_Factory(t) {
      return new (t || OrderHistoryRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: OrderHistoryRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](OrderHistoryRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 15319:
/*!*******************************************************!*\
  !*** ./src/app/order-history/order-history.module.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderHistoryModule: () => (/* binding */ OrderHistoryModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _order_history_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order-history-routing.module */ 23278);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../payment/payment.module */ 42047);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components */ 85341);
/* harmony import */ var _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared-tools/shared-tools.module */ 23879);
/* harmony import */ var _components_order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/order-history-list/order-history-list.component */ 72577);
/* harmony import */ var _sharedPipes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../sharedPipes */ 52151);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);


// module




// components



//pipes


class OrderHistoryModule {
  static {
    this.ɵfac = function OrderHistoryModule_Factory(t) {
      return new (t || OrderHistoryModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: OrderHistoryModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _order_history_routing_module__WEBPACK_IMPORTED_MODULE_0__.OrderHistoryRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__.PaymentModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsButtonModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_5__.SharedToolsModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _order_history_routing_module__WEBPACK_IMPORTED_MODULE_0__.OrderHistoryRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](OrderHistoryModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_4__.OrderHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_4__.CanteenHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_4__.EventHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_4__.UniformHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_4__.OrderHistoryRowComponent, _components__WEBPACK_IMPORTED_MODULE_4__.OrderHistoryRowComponent, _components_order_history_list_order_history_list_component__WEBPACK_IMPORTED_MODULE_6__.OrderHistoryListComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _order_history_routing_module__WEBPACK_IMPORTED_MODULE_0__.OrderHistoryRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_2__.PaymentModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsButtonModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_5__.SharedToolsModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule, _sharedPipes__WEBPACK_IMPORTED_MODULE_7__.OrderHistoryStatusPipe],
    exports: [_order_history_routing_module__WEBPACK_IMPORTED_MODULE_0__.OrderHistoryRoutingModule]
  });
})();

/***/ }),

/***/ 6054:
/*!***************************************************************************!*\
  !*** ./src/app/order-history/resolvers/uniform-event-history.resolver.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UniformEventHistoryResolver: () => (/* binding */ UniformEventHistoryResolver)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);


// services

const UniformEventHistoryResolver = route => {
  const schoolService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_2__.inject)(src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.OrderApiService);
  const menuTypeFromPath = route.routeConfig.path;
  const menuType = getMenuTypeFromPath(menuTypeFromPath);
  const request = {
    StartIndex: 0,
    NumberOfOrders: 30,
    MenuType: menuType
  };
  return schoolService.getUniformOrEventOrderHistoryByParent(request);
};
function getMenuTypeFromPath(pathValue) {
  const capitalizeValue = pathValue[0].toUpperCase() + pathValue.slice(1);
  return src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MenuTypeEnum[capitalizeValue];
}

/***/ }),

/***/ 98522:
/*!***********************************************************************!*\
  !*** ./src/app/sharedServices/order/orderWithCutOffTimes.resolver.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderWithCutOffTimesResolver: () => (/* binding */ OrderWithCutOffTimesResolver)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _order_api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order-api.service */ 59893);


const OrderWithCutOffTimesResolver = route => {
  const orderService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(_order_api_service__WEBPACK_IMPORTED_MODULE_0__.OrderApiService);
  const orderId = route.params['orderId'];
  if (!orderId) {
    return;
  }
  return orderService.getOrderByIdWithCutOffTimes(orderId);
};

/***/ }),

/***/ 88225:
/*!****************************************************************!*\
  !*** ./src/app/states/shoppingCart/shopping-cart.selectors.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   editCartNotChanged: () => (/* binding */ editCartNotChanged),
/* harmony export */   getCartItems: () => (/* binding */ getCartItems),
/* harmony export */   shoppingCart: () => (/* binding */ shoppingCart)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ngrx/store */ 81383);

const appState = state => state;
const getCartItems = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(appState, state => {
  return state.shoppingCart.cartItems;
});
const shoppingCart = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(appState, state => state.shoppingCart);
const editCartNotChanged = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(appState, state => {
  return cartsAreTheSame(state.shoppingCart.prevItems, state.shoppingCart.cartItems);
});
function cartsAreTheSame(prevItems, cartItems) {
  const initCartLength = prevItems.length;
  if (initCartLength === 0) return false;
  if (cartItems.length !== initCartLength) return false;
  let itemsAreTheSame = true;
  prevItems.forEach((prevItem, index) => {
    if (cartItems[index].quantity !== prevItem.quantity) itemsAreTheSame = false;
    if (cartItems[index].name !== prevItem.name) itemsAreTheSame = false;
  });
  return itemsAreTheSame;
}

/***/ }),

/***/ 15244:
/*!******************************************************************!*\
  !*** ./node_modules/@angular/material/fesm2022/bottom-sheet.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MAT_BOTTOM_SHEET_DATA: () => (/* binding */ MAT_BOTTOM_SHEET_DATA),
/* harmony export */   MAT_BOTTOM_SHEET_DEFAULT_OPTIONS: () => (/* binding */ MAT_BOTTOM_SHEET_DEFAULT_OPTIONS),
/* harmony export */   MatBottomSheet: () => (/* binding */ MatBottomSheet),
/* harmony export */   MatBottomSheetConfig: () => (/* binding */ MatBottomSheetConfig),
/* harmony export */   MatBottomSheetContainer: () => (/* binding */ MatBottomSheetContainer),
/* harmony export */   MatBottomSheetModule: () => (/* binding */ MatBottomSheetModule),
/* harmony export */   MatBottomSheetRef: () => (/* binding */ MatBottomSheetRef),
/* harmony export */   matBottomSheetAnimations: () => (/* binding */ matBottomSheetAnimations)
/* harmony export */ });
/* harmony import */ var _angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/cdk/dialog */ 93482);
/* harmony import */ var _angular_cdk_portal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/cdk/portal */ 9168);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/cdk/a11y */ 72102);
/* harmony import */ var _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/cdk/layout */ 87912);
/* harmony import */ var _angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/cdk/overlay */ 81570);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/animations */ 47172);
/* harmony import */ var _angular_cdk_keycodes__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/cdk/keycodes */ 74879);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs */ 10819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs */ 63617);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 51567);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ 64334);

















/** Animations used by the Material bottom sheet. */
function MatBottomSheetContainer_ng_template_0_Template(rf, ctx) {}
const matBottomSheetAnimations = {
  /** Animation that shows and hides a bottom sheet. */
  bottomSheetState: (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.trigger)('state', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.state)('void, hidden', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.style)({
    transform: 'translateY(100%)'
  })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.state)('visible', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.style)({
    transform: 'translateY(0%)'
  })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.transition)('visible => void, visible => hidden', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.group)([(0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.animate)(`${_angular_material_core__WEBPACK_IMPORTED_MODULE_1__.AnimationDurations.COMPLEX} ${_angular_material_core__WEBPACK_IMPORTED_MODULE_1__.AnimationCurves.ACCELERATION_CURVE}`), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.query)('@*', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.animateChild)(), {
    optional: true
  })])), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.transition)('void => visible', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.group)([(0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.animate)(`${_angular_material_core__WEBPACK_IMPORTED_MODULE_1__.AnimationDurations.EXITING} ${_angular_material_core__WEBPACK_IMPORTED_MODULE_1__.AnimationCurves.DECELERATION_CURVE}`), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.query)('@*', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_0__.animateChild)(), {
    optional: true
  })]))])
};

/**
 * Internal component that wraps user-provided bottom sheet content.
 * @docs-private
 */
class MatBottomSheetContainer extends _angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.CdkDialogContainer {
  constructor(elementRef, focusTrapFactory, document, config, checker, ngZone, overlayRef, breakpointObserver, _changeDetectorRef, focusMonitor) {
    super(elementRef, focusTrapFactory, document, config, checker, ngZone, overlayRef, focusMonitor);
    this._changeDetectorRef = _changeDetectorRef;
    /** The state of the bottom sheet animations. */
    this._animationState = 'void';
    /** Emits whenever the state of the animation changes. */
    this._animationStateChanged = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this._breakpointSubscription = breakpointObserver.observe([_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.Medium, _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.Large, _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.XLarge]).subscribe(() => {
      this._toggleClass('mat-bottom-sheet-container-medium', breakpointObserver.isMatched(_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.Medium));
      this._toggleClass('mat-bottom-sheet-container-large', breakpointObserver.isMatched(_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.Large));
      this._toggleClass('mat-bottom-sheet-container-xlarge', breakpointObserver.isMatched(_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.Breakpoints.XLarge));
    });
  }
  /** Begin animation of bottom sheet entrance into view. */
  enter() {
    if (!this._destroyed) {
      this._animationState = 'visible';
      this._changeDetectorRef.detectChanges();
    }
  }
  /** Begin animation of the bottom sheet exiting from view. */
  exit() {
    if (!this._destroyed) {
      this._animationState = 'hidden';
      this._changeDetectorRef.markForCheck();
    }
  }
  ngOnDestroy() {
    super.ngOnDestroy();
    this._breakpointSubscription.unsubscribe();
    this._destroyed = true;
  }
  _onAnimationDone(event) {
    if (event.toState === 'visible') {
      this._trapFocus();
    }
    this._animationStateChanged.emit(event);
  }
  _onAnimationStart(event) {
    this._animationStateChanged.emit(event);
  }
  _captureInitialFocus() {}
  _toggleClass(cssClass, add) {
    this._elementRef.nativeElement.classList.toggle(cssClass, add);
  }
  static {
    this.ɵfac = function MatBottomSheetContainer_Factory(t) {
      return new (t || MatBottomSheetContainer)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_3__.ElementRef), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.FocusTrapFactory), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_6__.DOCUMENT, 8), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogConfig), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.InteractivityChecker), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_3__.NgZone), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_7__.OverlayRef), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.BreakpointObserver), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_3__.ChangeDetectorRef), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.FocusMonitor));
    };
  }
  static {
    this.ɵcmp = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: MatBottomSheetContainer,
      selectors: [["mat-bottom-sheet-container"]],
      hostAttrs: ["tabindex", "-1", 1, "mat-bottom-sheet-container"],
      hostVars: 4,
      hostBindings: function MatBottomSheetContainer_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsyntheticHostListener"]("@state.start", function MatBottomSheetContainer_animation_state_start_HostBindingHandler($event) {
            return ctx._onAnimationStart($event);
          })("@state.done", function MatBottomSheetContainer_animation_state_done_HostBindingHandler($event) {
            return ctx._onAnimationDone($event);
          });
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵattribute"]("role", ctx._config.role)("aria-modal", ctx._config.ariaModal)("aria-label", ctx._config.ariaLabel);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsyntheticHostProperty"]("@state", ctx._animationState);
        }
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 1,
      vars: 0,
      consts: [["cdkPortalOutlet", ""]],
      template: function MatBottomSheetContainer_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](0, MatBottomSheetContainer_ng_template_0_Template, 0, 0, "ng-template", 0);
        }
      },
      dependencies: [_angular_cdk_portal__WEBPACK_IMPORTED_MODULE_8__.CdkPortalOutlet],
      styles: [".mat-bottom-sheet-container{--mat-bottom-sheet-container-shape:4px;box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);padding:8px 16px;min-width:100vw;box-sizing:border-box;display:block;outline:0;max-height:80vh;overflow:auto;background:var(--mat-bottom-sheet-container-background-color);color:var(--mat-bottom-sheet-container-text-color);font-family:var(--mat-bottom-sheet-container-text-font);font-size:var(--mat-bottom-sheet-container-text-size);line-height:var(--mat-bottom-sheet-container-text-line-height);font-weight:var(--mat-bottom-sheet-container-text-weight);letter-spacing:var(--mat-bottom-sheet-container-text-tracking)}.cdk-high-contrast-active .mat-bottom-sheet-container{outline:1px solid}.mat-bottom-sheet-container-xlarge,.mat-bottom-sheet-container-large,.mat-bottom-sheet-container-medium{border-top-left-radius:var(--mat-bottom-sheet-container-shape);border-top-right-radius:var(--mat-bottom-sheet-container-shape)}.mat-bottom-sheet-container-medium{min-width:384px;max-width:calc(100vw - 128px)}.mat-bottom-sheet-container-large{min-width:512px;max-width:calc(100vw - 256px)}.mat-bottom-sheet-container-xlarge{min-width:576px;max-width:calc(100vw - 384px)}"],
      encapsulation: 2,
      data: {
        animation: [matBottomSheetAnimations.bottomSheetState]
      }
    });
  }
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵsetClassMetadata"](MatBottomSheetContainer, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Component,
    args: [{
      selector: 'mat-bottom-sheet-container',
      changeDetection: _angular_core__WEBPACK_IMPORTED_MODULE_3__.ChangeDetectionStrategy.Default,
      encapsulation: _angular_core__WEBPACK_IMPORTED_MODULE_3__.ViewEncapsulation.None,
      animations: [matBottomSheetAnimations.bottomSheetState],
      host: {
        'class': 'mat-bottom-sheet-container',
        'tabindex': '-1',
        '[attr.role]': '_config.role',
        '[attr.aria-modal]': '_config.ariaModal',
        '[attr.aria-label]': '_config.ariaLabel',
        '[@state]': '_animationState',
        '(@state.start)': '_onAnimationStart($event)',
        '(@state.done)': '_onAnimationDone($event)'
      },
      template: "<ng-template cdkPortalOutlet></ng-template>\r\n",
      styles: [".mat-bottom-sheet-container{--mat-bottom-sheet-container-shape:4px;box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);padding:8px 16px;min-width:100vw;box-sizing:border-box;display:block;outline:0;max-height:80vh;overflow:auto;background:var(--mat-bottom-sheet-container-background-color);color:var(--mat-bottom-sheet-container-text-color);font-family:var(--mat-bottom-sheet-container-text-font);font-size:var(--mat-bottom-sheet-container-text-size);line-height:var(--mat-bottom-sheet-container-text-line-height);font-weight:var(--mat-bottom-sheet-container-text-weight);letter-spacing:var(--mat-bottom-sheet-container-text-tracking)}.cdk-high-contrast-active .mat-bottom-sheet-container{outline:1px solid}.mat-bottom-sheet-container-xlarge,.mat-bottom-sheet-container-large,.mat-bottom-sheet-container-medium{border-top-left-radius:var(--mat-bottom-sheet-container-shape);border-top-right-radius:var(--mat-bottom-sheet-container-shape)}.mat-bottom-sheet-container-medium{min-width:384px;max-width:calc(100vw - 128px)}.mat-bottom-sheet-container-large{min-width:512px;max-width:calc(100vw - 256px)}.mat-bottom-sheet-container-xlarge{min-width:576px;max-width:calc(100vw - 384px)}"]
    }]
  }], function () {
    return [{
      type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.ElementRef
    }, {
      type: _angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.FocusTrapFactory
    }, {
      type: undefined,
      decorators: [{
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Optional
      }, {
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Inject,
        args: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.DOCUMENT]
      }]
    }, {
      type: _angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogConfig
    }, {
      type: _angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.InteractivityChecker
    }, {
      type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.NgZone
    }, {
      type: _angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_7__.OverlayRef
    }, {
      type: _angular_cdk_layout__WEBPACK_IMPORTED_MODULE_4__.BreakpointObserver
    }, {
      type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.ChangeDetectorRef
    }, {
      type: _angular_cdk_a11y__WEBPACK_IMPORTED_MODULE_5__.FocusMonitor
    }];
  }, null);
})();
class MatBottomSheetModule {
  static {
    this.ɵfac = function MatBottomSheetModule_Factory(t) {
      return new (t || MatBottomSheetModule)();
    };
  }
  static {
    this.ɵmod = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: MatBottomSheetModule
    });
  }
  static {
    this.ɵinj = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      imports: [_angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_1__.MatCommonModule, _angular_cdk_portal__WEBPACK_IMPORTED_MODULE_8__.PortalModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_1__.MatCommonModule]
    });
  }
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵsetClassMetadata"](MatBottomSheetModule, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.NgModule,
    args: [{
      imports: [_angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_1__.MatCommonModule, _angular_cdk_portal__WEBPACK_IMPORTED_MODULE_8__.PortalModule],
      exports: [MatBottomSheetContainer, _angular_material_core__WEBPACK_IMPORTED_MODULE_1__.MatCommonModule],
      declarations: [MatBottomSheetContainer]
    }]
  }], null, null);
})();

/** Injection token that can be used to access the data that was passed in to a bottom sheet. */
const MAT_BOTTOM_SHEET_DATA = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.InjectionToken('MatBottomSheetData');
/**
 * Configuration used when opening a bottom sheet.
 */
class MatBottomSheetConfig {
  constructor() {
    /** Data being injected into the child component. */
    this.data = null;
    /** Whether the bottom sheet has a backdrop. */
    this.hasBackdrop = true;
    /** Whether the user can use escape or clicking outside to close the bottom sheet. */
    this.disableClose = false;
    /** Aria label to assign to the bottom sheet element. */
    this.ariaLabel = null;
    /** Whether this is a modal bottom sheet. Used to set the `aria-modal` attribute. */
    this.ariaModal = true;
    /**
     * Whether the bottom sheet should close when the user goes backwards/forwards in history.
     * Note that this usually doesn't include clicking on links (unless the user is using
     * the `HashLocationStrategy`).
     */
    this.closeOnNavigation = true;
    // Note that this is set to 'dialog' by default, because while the a11y recommendations
    // are to focus the first focusable element, doing so prevents screen readers from reading out the
    // rest of the bottom sheet content.
    /**
     * Where the bottom sheet should focus on open.
     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or
     * AutoFocusTarget instead.
     */
    this.autoFocus = 'dialog';
    /**
     * Whether the bottom sheet should restore focus to the
     * previously-focused element, after it's closed.
     */
    this.restoreFocus = true;
  }
}

/**
 * Reference to a bottom sheet dispatched from the bottom sheet service.
 */
class MatBottomSheetRef {
  /** Instance of the component making up the content of the bottom sheet. */
  get instance() {
    return this._ref.componentInstance;
  }
  /**
   * `ComponentRef` of the component opened into the bottom sheet. Will be
   * null when the bottom sheet is opened using a `TemplateRef`.
   */
  get componentRef() {
    return this._ref.componentRef;
  }
  constructor(_ref, config, containerInstance) {
    this._ref = _ref;
    /** Subject for notifying the user that the bottom sheet has opened and appeared. */
    this._afterOpened = new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subject();
    this.containerInstance = containerInstance;
    this.disableClose = config.disableClose;
    // Emit when opening animation completes
    containerInstance._animationStateChanged.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.filter)(event => event.phaseName === 'done' && event.toState === 'visible'), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.take)(1)).subscribe(() => {
      this._afterOpened.next();
      this._afterOpened.complete();
    });
    // Dispose overlay when closing animation is complete
    containerInstance._animationStateChanged.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.filter)(event => event.phaseName === 'done' && event.toState === 'hidden'), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.take)(1)).subscribe(() => {
      clearTimeout(this._closeFallbackTimeout);
      this._ref.close(this._result);
    });
    _ref.overlayRef.detachments().subscribe(() => {
      this._ref.close(this._result);
    });
    (0,rxjs__WEBPACK_IMPORTED_MODULE_12__.merge)(this.backdropClick(), this.keydownEvents().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.filter)(event => event.keyCode === _angular_cdk_keycodes__WEBPACK_IMPORTED_MODULE_13__.ESCAPE))).subscribe(event => {
      if (!this.disableClose && (event.type !== 'keydown' || !(0,_angular_cdk_keycodes__WEBPACK_IMPORTED_MODULE_13__.hasModifierKey)(event))) {
        event.preventDefault();
        this.dismiss();
      }
    });
  }
  /**
   * Dismisses the bottom sheet.
   * @param result Data to be passed back to the bottom sheet opener.
   */
  dismiss(result) {
    if (!this.containerInstance) {
      return;
    }
    // Transition the backdrop in parallel to the bottom sheet.
    this.containerInstance._animationStateChanged.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.filter)(event => event.phaseName === 'start'), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.take)(1)).subscribe(event => {
      // The logic that disposes of the overlay depends on the exit animation completing, however
      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback
      // timeout which will clean everything up if the animation hasn't fired within the specified
      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the
      // vast majority of cases the timeout will have been cleared before it has fired.
      this._closeFallbackTimeout = setTimeout(() => {
        this._ref.close(this._result);
      }, event.totalTime + 100);
      this._ref.overlayRef.detachBackdrop();
    });
    this._result = result;
    this.containerInstance.exit();
    this.containerInstance = null;
  }
  /** Gets an observable that is notified when the bottom sheet is finished closing. */
  afterDismissed() {
    return this._ref.closed;
  }
  /** Gets an observable that is notified when the bottom sheet has opened and appeared. */
  afterOpened() {
    return this._afterOpened;
  }
  /**
   * Gets an observable that emits when the overlay's backdrop has been clicked.
   */
  backdropClick() {
    return this._ref.backdropClick;
  }
  /**
   * Gets an observable that emits when keydown events are targeted on the overlay.
   */
  keydownEvents() {
    return this._ref.keydownEvents;
  }
}

/** Injection token that can be used to specify default bottom sheet options. */
const MAT_BOTTOM_SHEET_DEFAULT_OPTIONS = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.InjectionToken('mat-bottom-sheet-default-options');
/**
 * Service to trigger Material Design bottom sheets.
 */
class MatBottomSheet {
  /** Reference to the currently opened bottom sheet. */
  get _openedBottomSheetRef() {
    const parent = this._parentBottomSheet;
    return parent ? parent._openedBottomSheetRef : this._bottomSheetRefAtThisLevel;
  }
  set _openedBottomSheetRef(value) {
    if (this._parentBottomSheet) {
      this._parentBottomSheet._openedBottomSheetRef = value;
    } else {
      this._bottomSheetRefAtThisLevel = value;
    }
  }
  constructor(_overlay, injector, _parentBottomSheet, _defaultOptions) {
    this._overlay = _overlay;
    this._parentBottomSheet = _parentBottomSheet;
    this._defaultOptions = _defaultOptions;
    this._bottomSheetRefAtThisLevel = null;
    this._dialog = injector.get(_angular_cdk_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog);
  }
  open(componentOrTemplateRef, config) {
    const _config = {
      ...(this._defaultOptions || new MatBottomSheetConfig()),
      ...config
    };
    let ref;
    this._dialog.open(componentOrTemplateRef, {
      ..._config,
      // Disable closing since we need to sync it up to the animation ourselves.
      disableClose: true,
      // Disable closing on detachments so that we can sync up the animation.
      closeOnOverlayDetachments: false,
      maxWidth: '100%',
      container: MatBottomSheetContainer,
      scrollStrategy: _config.scrollStrategy || this._overlay.scrollStrategies.block(),
      positionStrategy: this._overlay.position().global().centerHorizontally().bottom('0'),
      templateContext: () => ({
        bottomSheetRef: ref
      }),
      providers: (cdkRef, _cdkConfig, container) => {
        ref = new MatBottomSheetRef(cdkRef, _config, container);
        return [{
          provide: MatBottomSheetRef,
          useValue: ref
        }, {
          provide: MAT_BOTTOM_SHEET_DATA,
          useValue: _config.data
        }];
      }
    });
    // When the bottom sheet is dismissed, clear the reference to it.
    ref.afterDismissed().subscribe(() => {
      // Clear the bottom sheet ref if it hasn't already been replaced by a newer one.
      if (this._openedBottomSheetRef === ref) {
        this._openedBottomSheetRef = null;
      }
    });
    if (this._openedBottomSheetRef) {
      // If a bottom sheet is already in view, dismiss it and enter the
      // new bottom sheet after exit animation is complete.
      this._openedBottomSheetRef.afterDismissed().subscribe(() => ref.containerInstance?.enter());
      this._openedBottomSheetRef.dismiss();
    } else {
      // If no bottom sheet is in view, enter the new bottom sheet.
      ref.containerInstance.enter();
    }
    this._openedBottomSheetRef = ref;
    return ref;
  }
  /**
   * Dismisses the currently-visible bottom sheet.
   * @param result Data to pass to the bottom sheet instance.
   */
  dismiss(result) {
    if (this._openedBottomSheetRef) {
      this._openedBottomSheetRef.dismiss(result);
    }
  }
  ngOnDestroy() {
    if (this._bottomSheetRefAtThisLevel) {
      this._bottomSheetRefAtThisLevel.dismiss();
    }
  }
  static {
    this.ɵfac = function MatBottomSheet_Factory(t) {
      return new (t || MatBottomSheet)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_7__.Overlay), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_core__WEBPACK_IMPORTED_MODULE_3__.Injector), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](MatBottomSheet, 12), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](MAT_BOTTOM_SHEET_DEFAULT_OPTIONS, 8));
    };
  }
  static {
    this.ɵprov = /* @__PURE__ */_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: MatBottomSheet,
      factory: MatBottomSheet.ɵfac,
      providedIn: MatBottomSheetModule
    });
  }
}
(function () {
  (typeof ngDevMode === "undefined" || ngDevMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵsetClassMetadata"](MatBottomSheet, [{
    type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Injectable,
    args: [{
      providedIn: MatBottomSheetModule
    }]
  }], function () {
    return [{
      type: _angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_7__.Overlay
    }, {
      type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Injector
    }, {
      type: MatBottomSheet,
      decorators: [{
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Optional
      }, {
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.SkipSelf
      }]
    }, {
      type: MatBottomSheetConfig,
      decorators: [{
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Optional
      }, {
        type: _angular_core__WEBPACK_IMPORTED_MODULE_3__.Inject,
        args: [MAT_BOTTOM_SHEET_DEFAULT_OPTIONS]
      }]
    }];
  }, null);
})();

/**
 * Generated bundle index. Do not edit.
 */



/***/ })

}]);
//# sourceMappingURL=default-src_app_manage-order_manage-order_module_ts.js.map