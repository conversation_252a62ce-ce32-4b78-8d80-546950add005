using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class BillingHistory
    {
        [JsonProperty(PropertyName = "OrderId")]
        public int OrderId { get; set; }

        [JsonProperty(PropertyName = "OrderDate")]
        public string OrderDate { get; set; }

        [JsonProperty(PropertyName = "OrderStatusId")]
        public Int16 OrderStatusId { get; set; }

        [JsonProperty(PropertyName = "StudentName")]
        public string StudentName { get; set; }

        [JsonProperty(PropertyName = "StudentId")]
        public int StudentId { get; set; }

        [JsonProperty(PropertyName = "OrderAmount")]
        public decimal? OrderAmount { get; set; }

        [JsonProperty(PropertyName = "Status")]
        public string Status { get; set; }

        [JsonProperty(PropertyName = "StartDate")]
        public string StartDate { get; set; }

        [JsonProperty(PropertyName = "EndDate")]
        public string EndDate { get; set; }

        [JsonProperty(PropertyName = "TermName")]
        public string TermName { get; set; }
    }
}
