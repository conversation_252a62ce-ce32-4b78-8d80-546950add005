using System;
using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes.Menus;
using Schools.DAL.QueryResults;

namespace Schools.BLL.Assemblers.Menus;

public class MenuItemAssembler
{
    public MenuItemSelectListDto Convert_ItemSelectListResult_To_ItemSelectListDto(MenuItemForSelectListQueryResult result)
    {
        MenuItemSelectListDto dto = new()
        {
            MenuItemId = result.MenuItemId,
            Name = result.Name,
            IsActive = Convert.ToBoolean(result.IsActive),
            CategoryId = result.CategoryId,
            ImageUrl = result.ImageUrl,
            Price = result.Price
        };

        return dto;
    }

    public List<MenuItemSelectListDto> Convert_EnumarableItemSelectListResult_To_ListItemSelectListDto(IEnumerable<MenuItemForSelectListQueryResult> results)
    {
        List<MenuItemSelectListDto> dto = new();

        if (results != null && results.Any())
        {
            foreach (var res in results)
            {
                dto.Add(this.Convert_ItemSelectListResult_To_ItemSelectListDto(res));
            }
        }

        return dto;
    }
}
