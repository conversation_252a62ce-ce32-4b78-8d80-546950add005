using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Schools.BLL.Classes.Payments
{
    /// <summary>
    /// Request model for guest payment using pre-created payment method (PRODUCTION READY)
    /// This approach is secure and PCI compliant as it uses payment methods created on the frontend
    /// </summary>
    public class GuestPaymentWithMethodRequest
    {
        /// <summary>
        /// The guest user ID for this transaction
        /// </summary>
        [Required]
        public int GuestUserId { get; set; }

        /// <summary>
        /// The canteen ID where the order is being placed
        /// </summary>
        [Required]
        public int CanteenId { get; set; }

        /// <summary>
        /// The Stripe payment method ID created on the frontend using Stripe Elements
        /// This is secure and PCI compliant
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PaymentMethodId { get; set; }

        /// <summary>
        /// Total amount to charge (in dollars, will be converted to cents)
        /// </summary>
        [Required]
        [Range(0.01, 10000.00)]
        public decimal Amount { get; set; }

        /// <summary>
        /// List of items being ordered
        /// </summary>
        [Required]
        public List<GuestOrderItem> Items { get; set; }

        /// <summary>
        /// Optional notes for the order
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }
    }
}
