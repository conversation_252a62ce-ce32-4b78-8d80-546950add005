﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    // TODO change this class name once we have refactored the events request on the parent side
    public class SchoolEvent : SchoolEventDto
    {
        [JsonProperty(PropertyName = "EventOrder")]
        public List<Order> EventOrder { get; set; }
    }

    public class SchoolEventDto
    {
        [JsonProperty(PropertyName = "SchoolEventId")]
        public int? SchoolEventId { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        [JsonProperty(PropertyName = "MenuId")]
        public int MenuId { get; set; }

        [JsonProperty(PropertyName = "SchoolId")]
        public int SchoolId { get; set; }

        [JsonProperty(PropertyName = "EventDate")]
        public string EventDate { get; set; }

        [JsonProperty(PropertyName = "CutOffDate")]
        public string CutOffDate { get; set; }

        [JsonProperty(PropertyName = "IsActive")]
        public bool? IsActive { get; set; }

        [JsonProperty(PropertyName = "SpecificClasses")]
        public string SpecificClasses { get; set; }

        [JsonProperty(PropertyName = "WeeksPreOrder")]
        public int? WeeksPreOrder { get; set; }

        [JsonProperty(PropertyName = "MerchantId")]
        public int? MerchantId { get; set; } // using nullable here to avoid side errors on the parent side => TODO refactor once

        [JsonProperty(PropertyName = "MerchantName")]
        public string MerchantName { get; set; }

        [JsonProperty(PropertyName = "ImageUrl")]
        public string ImageUrl { get; set; }

        [JsonProperty(PropertyName = "WaiveEventOrderFee")]
        public bool? WaiveEventOrderFee { get; set; }

        [JsonProperty(PropertyName = "EventTemplateId")]
        public long? EventTemplateId { get; set; }
    }

    public class SchoolEventsResponse : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Events")]
        public List<SchoolEvent> Events { get; set; }
    }

    public class SchoolEventsRequest
    {
        [JsonProperty(PropertyName = "SchoolId")]
        public int SchoolId { get; set; }

        [JsonProperty(PropertyName = "StudentId")]
        public int StudentId { get; set; }
    }

    public class StudentEventDto
    {
        public int SchoolEventId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int MenuId { get; set; }
        public int SchoolId { get; set; }
        public int MerchantId { get; set; }
        public string ImageUrl { get; set; }
        public DateTime EventDate { get; set; }
        public DateTime CutOffDate { get; set; }
        public int WeeksPreOrder { get; set; }
        public int OrderId { get; set; }
        public int OrderStatusId { get; set; }
    }
}
