using System;

namespace Schools.BLL.Classes
{
    public class SchoolClassCreateRequest : BaseResponse.Response
    {
        public int SchoolId { get; set; }
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public string Name { get; set; }
        public string Teacher { get; set; }
        public bool? IsArchived { get; set; }
        public string YearGroup { get; set; }
        public Int32? SortOrder { get; set; }
    }
}
