using System;
using System.ComponentModel.DataAnnotations;
public class SchoolEventCreateRequest
{
    [Required]
    public string Name { get; set; }

    public string Description { get; set; }

    [Required]
    public int SchoolId { get; set; }

    [Required]
    public int MerchantId { get; set; }

    [Required]
    public DateTime EventDate { get; set; }

    [Required]
    public DateTime CutOffDate { get; set; }

    public string SpecificClasses { get; set; }

    public int? WeeksPreOrder { get; set; }
    public long? EventTemplateId { get; set; }
}