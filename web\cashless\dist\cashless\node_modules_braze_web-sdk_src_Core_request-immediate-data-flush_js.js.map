{"version": 3, "file": "node_modules_braze_web-sdk_src_Core_request-immediate-data-flush_js.js", "mappings": ";;;;;;;;;;;;;;AAA8C;AACvC,SAASC,yBAAyBA,CAACC,CAAC,EAAE;EAC3C,IAAI,CAACF,mEAAC,CAACG,EAAE,CAAC,CAAC,EAAE;EACb,MAAMC,CAAC,GAAGJ,mEAAC,CAACK,EAAE,CAAC,CAAC;EAChBD,CAAC,IAAIA,CAAC,CAACH,yBAAyB,CAACC,CAAC,CAAC;AACrC", "sources": ["./node_modules/@braze/web-sdk/src/Core/request-immediate-data-flush.js"], "sourcesContent": ["import e from \"../managers/braze-instance.js\";\nexport function requestImmediateDataFlush(t) {\n  if (!e.rr()) return;\n  const r = e.cr();\n  r && r.requestImmediateDataFlush(t);\n}\n"], "names": ["e", "requestImmediateDataFlush", "t", "rr", "r", "cr"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}