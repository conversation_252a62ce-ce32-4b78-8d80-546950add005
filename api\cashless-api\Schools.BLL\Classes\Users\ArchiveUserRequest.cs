﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Users
{
    public class ArchiveUserRequest
    {
        [JsonProperty(PropertyName = "UserId")]
        public int UserId { get; set; }

        [JsonProperty(PropertyName = "Reason")]
        public string Reason { get; set; }
    }

    public class ArchiveUserResponse
    {
        [JsonProperty(PropertyName = "BalanceRefunded")]
        public bool BalanceRefunded { get; set; }

        [JsonProperty(PropertyName = "Balance")]
        public string Balance { get; set; }
    }
}
