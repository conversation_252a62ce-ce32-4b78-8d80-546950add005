﻿using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    public class MultipleOrders : BaseResponse.Response
    {
        public MultipleOrders()
        {
            this.Orders = new List<Order>();
        }

        [JsonProperty(PropertyName = "Orders")]
        public List<Order> Orders { get; set; }

        [JsonProperty(PropertyName = "Price")]
        public decimal? Price { get; set; }

        [JsonProperty(PropertyName = "PaymentId")]
        public int? PaymentId { get; set; }
    }
}
