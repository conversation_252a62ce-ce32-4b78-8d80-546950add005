﻿using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes
{
    public partial class CanteenWithRightsResponce : CanteenEntity
    {
        [JsonProperty(PropertyName = "IsAdmin")]
        public bool? IsAdmin { get; set; }

        [JsonProperty(PropertyName = "IsMenuEditorAvailable")]
        public bool? IsMenuEditorAvailable { get; set; }

        [JsonProperty(PropertyName = "IsSaleReportsAvailable")]
        public bool? IsSaleReportsAvailable { get; set; }

        [JsonProperty(PropertyName = "IsOrdersNotPrintedReportsAvailable")]
        public bool? IsOrdersNotPrintedReportsAvailable { get; set; }

        [JsonProperty(PropertyName = "IsEventManagementAvailable")]
        public bool? IsEventManagementAvailable { get; set; }

        [JsonProperty(PropertyName = "IsEditEventManagementAvailable")]
        public bool? IsEditEventManagementAvailable { get; set; }

        [JsonProperty(PropertyName = "Schools")]
        public List<CanteenSchool> Schools { get; set; }
    }

    public partial class CanteenSchool
    {
        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "UsePrintingApp")]
        public bool? UsePrintingApp { get; set; }

        [JsonProperty(PropertyName = "LabelFormat")]
        public string LabelFormat { get; set; }

        [JsonProperty(PropertyName = "LabelPrintChoice")]
        public string LabelPrintChoice { get; set; }

        [JsonProperty(PropertyName = "LabelTypeId")]
        public int? LabelTypeId { get; set; }

        [JsonProperty(PropertyName = "StateId")]
        public int? StateId { get; set; }
    }
}
