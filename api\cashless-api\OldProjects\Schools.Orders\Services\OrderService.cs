using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Schools.BLL.Classes.Orders;
using Schools.BLL.Exceptions;
using Schools.BLL.ThirdParty.General;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Schools.Orders.Services;

public class OrderService : IOrderService
{
    private readonly ILogger<OrderService> _logger;
    private readonly Schools.BLL.Services.Interfaces.IOrderService _orderService;

    public OrderService(ILogger<OrderService> logger, Schools.BLL.Services.Interfaces.IOrderService orderService)
    {
        _logger = logger;
        _orderService = orderService;
    }

    public async Task UpdateOrdersAfterPayment(CashlessMakePaymentResponse paymentResult, IEnumerable<int> orderIds)
    {
        if (paymentResult == null || !orderIds.Any())
        {
            throw new OrderFunctionException("Invalid parameters for updating Orders after Payment");
        }

        await _orderService.UpdateErrorMessageOnOrders(orderIds, paymentResult.Message);
    }
}