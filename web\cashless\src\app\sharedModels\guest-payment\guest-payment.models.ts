export interface GuestPaymentRequest {
  cardNumber: string;
  expiryMonth: number;
  expiryYear: number;
  cvv: string;
  cardholderName: string;
  amount: number;
  canteenId: number;
  guestUserId: number;
  items: GuestOrderItem[];
  orderDate: string;
  menuId: number;
  menuType: string;
}

export interface GuestOrderItem {
  menuItemId: number;
  quantity: number;
  price: number;
  itemName: string;
  itemDescription: string;
}

export interface GuestPaymentResponse {
  isSuccess: boolean;
  orderId: string;
  message: string;
  errorCode: string;
  transactionId: string;
  processedAt: string;
  amountCharged: number;
  paymentReference: string;
}

export interface GuestCardValidationRequest {
  cardNumber: string;
  expiryMonth: number;
  expiryYear: number;
  cvv: string;
}

export interface GuestCardValidationResponse {
  isValid: boolean;
  errorMessage: string;
  cardType: string;
  maskedCardNumber: string;
}

export interface GuestPaymentForm {
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  cardholderName: string;
}

export interface GuestPaymentDialogData {
  orders: any[];
  selectedStudent: any;
  totalAmount: number;
  canteenId: number;
  menuId: number;
  menuType: string;
  orderDate: string;
  viewType: string;
  guid: string;
}

export interface GuestPaymentDialogResult {
  success: boolean;
  orderId?: string;
  message?: string;
  errorCode?: string;
}

// Validation patterns
export const CARD_NUMBER_PATTERN = /^[0-9\s]{13,19}$/;
export const CVV_PATTERN = /^[0-9]{3,4}$/;
export const EXPIRY_MONTH_PATTERN = /^(0[1-9]|1[0-2])$/;
export const EXPIRY_YEAR_PATTERN = /^20[2-9][0-9]$/;

// Card type detection
export const CARD_TYPES = {
  VISA: 'Visa',
  MASTERCARD: 'Mastercard',
  AMEX: 'American Express',
  UNKNOWN: 'Unknown'
};

export function detectCardType(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\s/g, '');
  
  if (/^4/.test(cleanNumber)) {
    return CARD_TYPES.VISA;
  }
  if (/^5[1-5]/.test(cleanNumber)) {
    return CARD_TYPES.MASTERCARD;
  }
  if (/^3[47]/.test(cleanNumber)) {
    return CARD_TYPES.AMEX;
  }
  
  return CARD_TYPES.UNKNOWN;
}

export function formatCardNumber(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\s/g, '');
  const groups = cleanNumber.match(/.{1,4}/g);
  return groups ? groups.join(' ') : cleanNumber;
}

export function maskCardNumber(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\s/g, '');
  if (cleanNumber.length < 4) {
    return cardNumber;
  }
  
  const lastFour = cleanNumber.slice(-4);
  const masked = '*'.repeat(cleanNumber.length - 4);
  return formatCardNumber(masked + lastFour);
}

export function validateCardNumber(cardNumber: string): boolean {
  const cleanNumber = cardNumber.replace(/\s/g, '');
  
  // Check if all digits
  if (!/^\d+$/.test(cleanNumber)) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let alternate = false;
  
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i]);
    
    if (alternate) {
      digit *= 2;
      if (digit > 9) {
        digit = (digit % 10) + 1;
      }
    }
    
    sum += digit;
    alternate = !alternate;
  }
  
  return sum % 10 === 0;
}

export function validateExpiryDate(month: number, year: number): boolean {
  const now = new Date();
  const expiry = new Date(year, month - 1);
  return expiry >= now;
}

export function validateCVV(cvv: string): boolean {
  return CVV_PATTERN.test(cvv);
}
