﻿using System;
using Schools.BLL.Classes;
using Schools.BLL.Helpers;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers.Users;

public static class UserAssembler
{
    public static UserDto ConvertUserEntityToUserDto(User entity)
    {
        UserDto dto = new()
        {
            UserId = entity.UserId,
            FirstName = entity.FirstName,
            Lastname = entity.Lastname,
            Email = entity.Email,
            ExternalUserId = entity.ExternalUserId,
            FirebaseUserId = entity.FirebaseUserId,
            Mobile = entity.Mobile,
            SpriggyBalance = entity.SpriggyBalance,
            Role = entity.Role,
            DateCreated = entity.DateCreated?.ToString("yyyy-MM-dd HH:mm:ss"),
            IntercomUserHMAC = "" // not used anymore => keep it to avoid App crash
        };

        if (entity.IntercomUserHMAC != null)
        {
            dto.IntercomHMAC = new();
            dto.IntercomHMAC.HmacAndroid = entity.IntercomUserHMAC.HmacAndroid;
            dto.IntercomHMAC.HmacIos = entity.IntercomUserHMAC.HmacIos;
            dto.IntercomHMAC.HmacWeb = entity.IntercomUserHMAC.HmacWeb;
        }

        return dto;
    }
}

public static class UserMapper
{
    public static UserEntity MapToUserEntity(CreateSchoolsUserRequest request)
    {
        if (request == null) return new();

        return new UserEntity()
        {
            IsActive = request.IsActive,
            SchoolId = request.SchoolId,
            ClassId = request.ClassId,
            ParentId = request.ParentId,
            FirstName = request.FirstName,
            Lastname = request.Lastname,
            Email = request.Email,
            DateOfBirth = request.DateOfBirth,
            Role = Utils.UserRoleToString(request.Role),
            Mobile = request.Mobile,
            AllowCanteenToOrder = request.AllowCanteenToOrder,
            FavouriteColour = request.FavouriteColour,
            Allergies = request.Allergies,
            SpriggyUserId = request.SpriggyUserId,
            FirebaseUserId = request.FirebaseUserId,
        };
    }

    public static User MapToUser(UserEntity entity)
    {
        if (entity == null) return new();

        return new User()
        {
            UserId = Convert.ToInt32(entity.UserId),
            IsActive = entity.IsActive ?? false,
            SchoolId = entity.SchoolId != null ? Convert.ToInt32(entity.SchoolId) : null,
            ClassId = entity.ClassId != null ? Convert.ToInt32(entity.ClassId) : null,
            ParentId = entity.ParentId != null ? Convert.ToInt32(entity.ParentId) : null,
            FirstName = entity.FirstName,
            Lastname = entity.Lastname,
            Email = entity.Email,
            DateOfBirth = entity.DateOfBirth,
            Role = entity.Role,
            Mobile = entity.Mobile,
            ExternalUserId = entity.ExternalUserId,
            SpriggyUserId = entity.SpriggyUserId,
            FirebaseUserId = entity.FirebaseUserId,
            SpriggyAccountId = entity.SpriggyAccountId,
            SpriggyLoginId = entity.SpriggyLoginId,
            AllowCanteenToOrder = entity.AllowCanteenToOrder,
            FavouriteColour = entity.FavouriteColour,
            NeedToUpdateClass = entity.NeedToUpdateClass,
            Allergies = entity.Allergies
        };
    }
}
