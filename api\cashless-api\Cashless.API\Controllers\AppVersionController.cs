﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Controllers;

/// <summary>
/// Controller for returning app version information
/// </summary>
[AllowAnonymous]
[Route("api/[controller]")]
[ApiController]
public class AppVersionController : Controller
{
    private readonly IAppVersionService _appVersionService;
    private readonly ISupportedVersionsService _supportedVersionsService;
    private readonly ITelemetryService _telemetryService;
    private readonly ILogger<AppVersionController> _logger;

    public AppVersionController(IAppVersionService appVersionService, ISupportedVersionsService supportedVersionsService,
                                ITelemetryService telemetryService, ILogger<AppVersionController> logger)
    {
        _appVersionService = appVersionService;
        _supportedVersionsService = supportedVersionsService;
        _telemetryService = telemetryService;
        _logger = logger;

        // Show what has been loaded!
        // _logger.LogInformation("Loaded config: {Config}", Utils.ConfigToString(config));
    }

    /// <summary>
    /// Return the AppVersion settings in the appsettings.json file
    /// </summary>
    [Route("")]
    [HttpGet]
    public IActionResult Index()
    {
        var response = _appVersionService.GetVersionInfo();

        _logger.LogDebug("Fetched AppVersion: {AppVersion}", response);
        _logger.LogInformation("Fetched AppVersion: {AppVersion}", response);
        _logger.LogWarning("Fetched AppVersion warning: {AppVersion}", response);
        _logger.LogTrace("Fetched AppVersion trace: {AppVersion}", response);


        TraceSource _source = new("TestLog");
        _source.TraceInformation(TraceEventType.Warning.ToString(), 0, "Trace Warning");
        _source.TraceInformation(TraceEventType.Error.ToString(), 0, "Trace Error");

        // Test telemetry logging
        _telemetryService.TrackEvent($"{HttpContext.Request.Path} was called",
                                new Dictionary<string, string>() {
                                    { "response", response.ToString() }
                                });

        return new OkObjectResult(response);
    }

    /// <summary>
    /// Return the SupportedVersions from Azure App Configuration
    /// </summary>
    [Route("GetSupportedVersions")]
    [HttpGet]
    public IActionResult GetSupportedVersions()
    {
        var response = _supportedVersionsService.GetSupportedVersions();

        return new OkObjectResult(response);
    }
}
