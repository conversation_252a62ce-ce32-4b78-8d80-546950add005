using System;
using System.Threading;
using System.Threading.Tasks;
using MassTransit.Transports;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;

namespace Schools.Orders.Extensions;

/// <summary>
/// Mass Transit middleware to adapt Service Bus messages for use with Azure Functions
///
/// -- Code by Author of Masstransit: <PERSON>(https://github.com/phatboyg)
/// -- Original File: https://github.com/MassTransit/Sample-AzureFunction/blob/v5/src/Sample.AzureFunction/AzureFunctionReceiveEndpointDispatcherExtensions.cs
/// </summary>
public static class IReceiveEndpointDispatcherExtensions
{
    public static Task Dispatch(this IReceiveEndpointDispatcher dispatcher, FunctionContext context, byte[] body, CancellationToken cancellationToken = default)
    {
        return dispatcher.Dispatch(body, context.BindingContext.BindingData, cancellationToken, context.InstanceServices, new FunctionScope(context.InstanceServices));
    }

    public class FunctionScope : IServiceScope
    {
        public IServiceProvider ServiceProvider { get; }

        public FunctionScope(IServiceProvider serviceProvider)
        {
            ServiceProvider = serviceProvider;
        }

        public void Dispose()
        {
        }
    }
}