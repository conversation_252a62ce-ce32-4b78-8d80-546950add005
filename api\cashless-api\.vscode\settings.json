{"editor.formatOnType": true, "editor.formatOnPaste": true, "editor.formatOnSave": true, "[sql]": {"editor.formatOnType": false, "editor.formatOnPaste": false, "editor.formatOnSave": false}, "azureFunctions.deploySubpath": "Schools.Orders/bin/Release/net8.0/publish", "azureFunctions.projectLanguage": "C#", "azureFunctions.projectRuntime": "~4", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.preDeployTask": "publish-functions", "dotnet.defaultSolution": "cashlessApi.sln"}