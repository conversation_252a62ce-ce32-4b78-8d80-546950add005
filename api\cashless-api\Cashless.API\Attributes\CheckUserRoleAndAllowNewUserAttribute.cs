using System;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Exceptions;
using Schools.BLL.Extensions;
using Schools.DAL.Enums;
using Schools.DAL.DtosToMoveToBLL;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Cashless.APIs.Attributes;

/// <summary>
/// Action filter attribute to check if the current user has one
/// of the roles specified... If the current user is not found
/// because we are still in the process of creating them a record, 
/// we should check the request for validity and continue
/// 
/// To use this attribute, you decorate the target API method:
/// [HttpPost]
/// [Route("SomeApiMethod")]
/// [CheckUserRoleAndAllowNewUser(UserRole.Parent)]
/// public IActionResult SomeApiMethod(...
/// </summary>
public class CheckUserRoleAndAllowNewUserAttribute : CheckUserRoleAttribute
{
    public CheckUserRoleAndAllowNewUserAttribute(params UserRole[] roles) : base(roles)
    {
    }

    /// <summary>
    /// Check a user's role before executing the next action in the HTTP context chain. If this fails
    /// check that the request payload is a valid upsert User request
    /// </summary>
    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        try
        {
            await CheckUserRoles(context, next);
        }
        catch (UserNotFoundException)
        {
            await CheckCreateUserRequest(context, next);
        }
    }

    /// <summary>
    /// Check the request is a valid User upsert request that will result in a new User being created 
    /// with one of the target roles
    /// </summary>
    private async Task CheckCreateUserRequest(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var logger = context.HttpContext.RequestServices.GetService<ILogger<CheckUserRoleAndAllowNewUserAttribute>>();

        // Read the JSON payload
        var json = await context.HttpContext.Request.GetRawBodyAsync();
        if (string.IsNullOrEmpty(json))
        {
            throw new RegisterUserException("Unable to process create user request - empty request payload");
        }

        // We need to use Newtonsoft JSON libraries due to formatting issues 
        // with the requests coming from the Web app
        var user = JsonConvert.DeserializeObject<User>(json);
        if (user == null)
        {
            throw new RegisterUserException("Unable to process create user request - invalid request payload");
        }

        if (user.UserId > 0)
        {
            throw new RegisterUserException("Unable to process create user request - existing user");
        }

        if (string.IsNullOrEmpty(user.Role))
        {
            throw new RegisterUserException("Unable to process create user request - missing role");
        }

        if (!this.roles.Contains(user.UserRole))
        {
            throw new RegisterUserException("Unable to process create user request - invalid role");
        }

        logger.LogDebug("Create user request looks OK");

        // Check complete, keep going with the filter chain
        await Continue(context, next);
    }
}
