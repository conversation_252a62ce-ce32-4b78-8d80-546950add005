import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseService } from '../base.service';
import {
  GuestPaymentRequest,
  GuestPaymentResponse,
  GuestCardValidationRequest,
  GuestCardValidationResponse
} from '../../sharedModels/guest-payment/guest-payment.models';

@Injectable({
  providedIn: 'root'
})
export class GuestPaymentService extends BaseService {

  constructor(private http: HttpClient) {
    super('GuestPayment');
  }

  /**
   * Process a guest payment with card details
   */
  processGuestPayment(request: GuestPaymentRequest): Observable<GuestPaymentResponse> {
    return this.http.post<GuestPaymentResponse>(`${this.serviceUrl}/ProcessPayment`, request);
  }

  /**
   * Process guest payment and create order in one step
   */
  processPaymentAndCreateOrder(request: GuestPaymentRequest): Observable<GuestPaymentResponse> {
    return this.http.post<GuestPaymentResponse>(`${this.serviceUrl}/api/ProcessPaymentAndCreateOrder`, request);
  }

  /**
   * Validate guest card details
   */
  validateGuestCard(request: GuestCardValidationRequest): Observable<GuestCardValidationResponse> {
    return this.http.post<GuestCardValidationResponse>(`${this.serviceUrl}/ValidateCard`, request);
  }

  /**
   * Get payment status for a guest order
   */
  getPaymentStatus(orderId: string): Observable<any> {
    return this.http.get<any>(`${this.serviceUrl}/PaymentStatus/${orderId}`);
  }

  /**
   * Validate guest user permissions for payment
   */
  validateGuestPermissions(guestUserId: number, canteenId: number): Observable<{ hasPermission: boolean }> {
    return this.http.get<{ hasPermission: boolean }>(`${this.serviceUrl}/ValidatePermissions/${guestUserId}/${canteenId}`);
  }

  /**
   * Create guest payment request from order data
   */
  createGuestPaymentRequest(
    cardDetails: any,
    orderData: any,
    guestUserId: number,
    canteenId: number
  ): GuestPaymentRequest {
    return {
      cardNumber: cardDetails.cardNumber.replace(/\s/g, ''), // Remove spaces
      expiryMonth: parseInt(cardDetails.expiryMonth),
      expiryYear: parseInt(cardDetails.expiryYear),
      cvv: cardDetails.cvv,
      cardholderName: cardDetails.cardholderName,
      amount: orderData.totalAmount,
      canteenId: canteenId,
      guestUserId: guestUserId,
      items: orderData.items.map((item: any) => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        price: item.price,
        itemName: item.name,
        itemDescription: item.description
      })),
      orderDate: orderData.orderDate,
      menuId: orderData.menuId,
      menuType: orderData.menuType
    };
  }

  /**
   * Format card number for display
   */
  formatCardNumber(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    const groups = cleanNumber.match(/.{1,4}/g);
    return groups ? groups.join(' ') : cleanNumber;
  }

  /**
   * Detect card type from card number
   */
  detectCardType(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    if (/^4/.test(cleanNumber)) {
      return 'Visa';
    }
    if (/^5[1-5]/.test(cleanNumber)) {
      return 'Mastercard';
    }
    if (/^3[47]/.test(cleanNumber)) {
      return 'American Express';
    }
    
    return 'Unknown';
  }

  /**
   * Validate card number using Luhn algorithm
   */
  validateCardNumber(cardNumber: string): boolean {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    // Check if all digits
    if (!/^\d+$/.test(cleanNumber)) {
      return false;
    }
    
    // Luhn algorithm
    let sum = 0;
    let alternate = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 === 0;
  }

  /**
   * Validate expiry date
   */
  validateExpiryDate(month: number, year: number): boolean {
    const now = new Date();
    const expiry = new Date(year, month - 1);
    return expiry >= now;
  }

  /**
   * Validate CVV
   */
  validateCVV(cvv: string): boolean {
    return /^[0-9]{3,4}$/.test(cvv);
  }

  /**
   * Get available expiry months
   */
  getExpiryMonths(): { value: string; label: string }[] {
    const months = [];
    for (let i = 1; i <= 12; i++) {
      const value = i.toString().padStart(2, '0');
      const label = `${value}`;
      months.push({ value, label });
    }
    return months;
  }

  /**
   * Get available expiry years
   */
  getExpiryYears(): { value: string; label: string }[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 10; i++) {
      const year = currentYear + i;
      years.push({ value: year.toString(), label: year.toString() });
    }
    return years;
  }
}
