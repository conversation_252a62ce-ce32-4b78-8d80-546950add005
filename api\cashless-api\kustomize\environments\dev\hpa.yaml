# Actual Keda object that creates hpa config based off prometheus metrics
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: schools-api
  namespace: default
spec:
  minReplicaCount: 1  # Optional. Default: 0
  maxReplicaCount: 6 # Optional. Default: 100
  fallback:                                                 # Optional. Section to specify fallback options
    failureThreshold: 4                                     # Mandatory if fallback section is included
    replicas: 1                                             # Mandatory if fallback section is included