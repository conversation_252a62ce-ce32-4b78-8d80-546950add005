{"version": 3, "file": "node_modules_braze_web-sdk_src_Core_log-purchase_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAA8C;AACuB;AAChB;AACC;AACmC;AAIpD;AACK;AACnC,SAASY,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAI,CAACjB,mEAAC,CAACE,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACtB,IACG,IAAI,IAAIa,CAAC,KAAKA,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,IAAIC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,IAAIH,CAAC,IAAIA,CAAC,CAACK,MAAM,IAAI,CAAC,EAE3E,OACEf,uEAAC,CAACgB,CAAC,CAACC,KAAK,CACN,oDAAmDP,CAAE,cACxD,CAAC,EACD,CAAC,CAAC;EAEN,IAAI,CAACH,+EAAE,CAACG,CAAC,EAAE,cAAc,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC,CAAC;EAC1D,IAAI,IAAI,IAAIC,CAAC,IAAIO,KAAK,CAACC,UAAU,CAACR,CAAC,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC9C,OACEpB,uEAAC,CAACgB,CAAC,CAACC,KAAK,CAAE,6CAA4CN,CAAE,aAAY,CAAC,EAAE,CAAC,CAAC;EAE9E,MAAMU,CAAC,GAAGF,UAAU,CAACR,CAAC,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;EAC7C,IAAI,IAAI,IAAIT,CAAC,IAAIK,KAAK,CAACK,QAAQ,CAACV,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAC5C,OACEpB,uEAAC,CAACgB,CAAC,CAACC,KAAK,CACN,iDAAgDJ,CAAE,aACrD,CAAC,EACD,CAAC,CAAC;EAEN,MAAMW,CAAC,GAAGD,QAAQ,CAACV,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;EAChC,IAAII,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGzB,uEAAE,EACjB,OACEC,uEAAC,CAACgB,CAAC,CAACC,KAAK,CACN,2CAA0ClB,uEAAG,SAAQyB,CAAE,aAC1D,CAAC,EACD,CAAC,CAAC;EAENZ,CAAC,GAAG,IAAI,IAAIA,CAAC,GAAGA,CAAC,CAACa,WAAW,CAAC,CAAC,GAAGb,CAAC;EACnC,IACE,CAAC,CAAC,KACF,CACE,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN,CAACc,OAAO,CAACd,CAAC,CAAC,EAEZ,OACEZ,uEAAC,CAACgB,CAAC,CAACC,KAAK,CACN,kDAAiDL,CAAE,aACtD,CAAC,EACD,CAAC,CAAC;EAEN,MAAM,CAACe,CAAC,EAAEC,CAAC,CAAC,GAAGvB,mFAAE,CACfS,CAAC,EACD,aAAa,EACb,oBAAoB,EACnB,iBAAgBJ,CAAE,GAAE,EACrB,UACF,CAAC;EACD,IAAI,CAACiB,CAAC,EAAE,OAAO,CAAC,CAAC;EACjB,MAAME,CAAC,GAAGhC,mEAAC,CAACiC,EAAE,CAAC,CAAC;EAChB,IAAID,CAAC,IAAIA,CAAC,CAACE,EAAE,CAACrB,CAAC,CAAC,EACd,OAAOV,uEAAC,CAACgB,CAAC,CAACgB,IAAI,CAAE,aAAYtB,CAAE,6BAA4B,CAAC,EAAE,CAAC,CAAC;EAClE,MAAMuB,CAAC,GAAGzB,+DAAC,CAAC0B,CAAC,CAAClC,uEAAC,CAACmC,CAAC,CAACC,EAAE,EAAE;IAAEC,GAAG,EAAE3B,CAAC;IAAEuB,CAAC,EAAErB,CAAC;IAAE0B,CAAC,EAAEjB,CAAC;IAAEc,CAAC,EAAEX,CAAC;IAAEe,EAAE,EAAEX;EAAE,CAAC,CAAC;EAC1D,IAAIK,CAAC,CAACO,CAAC,EAAE;IACPxC,uEAAC,CAACgB,CAAC,CAACgB,IAAI,CACL,UAASR,CAAE,YAAWA,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,QAAOd,CAAE,SAAQE,CAAE,IAAGS,CAAE,GAClE,CAAC;IACD,KAAK,MAAMrB,CAAC,IAAIiC,CAAC,CAACQ,EAAE,EAAEtC,2FAAE,CAACuC,EAAE,CAAC,CAAC,CAACC,EAAE,CAAC1C,0EAAE,CAAC2C,EAAE,EAAE,CAAClC,CAAC,EAAEI,CAAC,CAAC,EAAEd,CAAC,CAAC;EACpD;EACA,OAAOiC,CAAC,CAACO,CAAC;AACZ", "sources": ["./node_modules/@braze/web-sdk/src/Core/log-purchase.js"], "sourcesContent": ["import e from \"../managers/braze-instance.js\";\nimport { MAX_PURCHASE_QUANTITY as rr } from \"../common/constants.js\";\nimport r from \"../../shared-lib/braze-shared-lib.js\";\nimport tt from \"../triggers/models/trigger-events.js\";\nimport { TriggersProviderFactory as et } from \"../triggers/triggers-provider-factory.js\";\nimport {\n  validateCustomProperties as rt,\n  validateCustomString as ot,\n} from \"../util/validation-utils.js\";\nimport s from \"../common/event-logger.js\";\nexport function logPurchase(o, i, n, t, D) {\n  if (!e.rr()) return !1;\n  if (\n    (null == n && (n = \"USD\"), null == t && (t = 1), null == o || o.length <= 0)\n  )\n    return (\n      r.j.error(\n        `logPurchase requires a non-empty productId, got \"${o}\", ignoring.`,\n      ),\n      !1\n    );\n  if (!ot(o, \"log purchase\", \"the purchase name\")) return !1;\n  if (null == i || isNaN(parseFloat(i.toString())))\n    return (\n      r.j.error(`logPurchase requires a numeric price, got ${i}, ignoring.`), !1\n    );\n  const a = parseFloat(i.toString()).toFixed(2);\n  if (null == t || isNaN(parseInt(t.toString())))\n    return (\n      r.j.error(\n        `logPurchase requires an integer quantity, got ${t}, ignoring.`,\n      ),\n      !1\n    );\n  const u = parseInt(t.toString());\n  if (u < 1 || u > rr)\n    return (\n      r.j.error(\n        `logPurchase requires a quantity >1 and <${rr}, got ${u}, ignoring.`,\n      ),\n      !1\n    );\n  n = null != n ? n.toUpperCase() : n;\n  if (\n    -1 ===\n    [\n      \"AED\",\n      \"AFN\",\n      \"ALL\",\n      \"AMD\",\n      \"ANG\",\n      \"AOA\",\n      \"ARS\",\n      \"AUD\",\n      \"AWG\",\n      \"AZN\",\n      \"BAM\",\n      \"BBD\",\n      \"BDT\",\n      \"BGN\",\n      \"BHD\",\n      \"BIF\",\n      \"BMD\",\n      \"BND\",\n      \"BOB\",\n      \"BRL\",\n      \"BSD\",\n      \"BTC\",\n      \"BTN\",\n      \"BWP\",\n      \"BYR\",\n      \"BZD\",\n      \"CAD\",\n      \"CDF\",\n      \"CHF\",\n      \"CLF\",\n      \"CLP\",\n      \"CNY\",\n      \"COP\",\n      \"CRC\",\n      \"CUC\",\n      \"CUP\",\n      \"CVE\",\n      \"CZK\",\n      \"DJF\",\n      \"DKK\",\n      \"DOP\",\n      \"DZD\",\n      \"EEK\",\n      \"EGP\",\n      \"ERN\",\n      \"ETB\",\n      \"EUR\",\n      \"FJD\",\n      \"FKP\",\n      \"GBP\",\n      \"GEL\",\n      \"GGP\",\n      \"GHS\",\n      \"GIP\",\n      \"GMD\",\n      \"GNF\",\n      \"GTQ\",\n      \"GYD\",\n      \"HKD\",\n      \"HNL\",\n      \"HRK\",\n      \"HTG\",\n      \"HUF\",\n      \"IDR\",\n      \"ILS\",\n      \"IMP\",\n      \"INR\",\n      \"IQD\",\n      \"IRR\",\n      \"ISK\",\n      \"JEP\",\n      \"JMD\",\n      \"JOD\",\n      \"JPY\",\n      \"KES\",\n      \"KGS\",\n      \"KHR\",\n      \"KMF\",\n      \"KPW\",\n      \"KRW\",\n      \"KWD\",\n      \"KYD\",\n      \"KZT\",\n      \"LAK\",\n      \"LBP\",\n      \"LKR\",\n      \"LRD\",\n      \"LSL\",\n      \"LTL\",\n      \"LVL\",\n      \"LYD\",\n      \"MAD\",\n      \"MDL\",\n      \"MGA\",\n      \"MKD\",\n      \"MMK\",\n      \"MNT\",\n      \"MOP\",\n      \"MRO\",\n      \"MTL\",\n      \"MUR\",\n      \"MVR\",\n      \"MWK\",\n      \"MXN\",\n      \"MYR\",\n      \"MZN\",\n      \"NAD\",\n      \"NGN\",\n      \"NIO\",\n      \"NOK\",\n      \"NPR\",\n      \"NZD\",\n      \"OMR\",\n      \"PAB\",\n      \"PEN\",\n      \"PGK\",\n      \"PHP\",\n      \"PKR\",\n      \"PLN\",\n      \"PYG\",\n      \"QAR\",\n      \"RON\",\n      \"RSD\",\n      \"RUB\",\n      \"RWF\",\n      \"SAR\",\n      \"SBD\",\n      \"SCR\",\n      \"SDG\",\n      \"SEK\",\n      \"SGD\",\n      \"SHP\",\n      \"SLL\",\n      \"SOS\",\n      \"SRD\",\n      \"STD\",\n      \"SVC\",\n      \"SYP\",\n      \"SZL\",\n      \"THB\",\n      \"TJS\",\n      \"TMT\",\n      \"TND\",\n      \"TOP\",\n      \"TRY\",\n      \"TTD\",\n      \"TWD\",\n      \"TZS\",\n      \"UAH\",\n      \"UGX\",\n      \"USD\",\n      \"UYU\",\n      \"UZS\",\n      \"VEF\",\n      \"VND\",\n      \"VUV\",\n      \"WST\",\n      \"XAF\",\n      \"XAG\",\n      \"XAU\",\n      \"XCD\",\n      \"XDR\",\n      \"XOF\",\n      \"XPD\",\n      \"XPF\",\n      \"XPT\",\n      \"YER\",\n      \"ZAR\",\n      \"ZMK\",\n      \"ZMW\",\n      \"ZWL\",\n    ].indexOf(n)\n  )\n    return (\n      r.j.error(\n        `logPurchase requires a valid currencyCode, got ${n}, ignoring.`,\n      ),\n      !1\n    );\n  const [g, P] = rt(\n    D,\n    \"logPurchase\",\n    \"purchaseProperties\",\n    `log purchase \"${o}\"`,\n    \"purchase\",\n  );\n  if (!g) return !1;\n  const R = e.tr();\n  if (R && R.Dr(o))\n    return r.j.info(`Purchase \"${o}\" is blocklisted, ignoring.`), !1;\n  const c = s.N(r.q.Pr, { pid: o, c: n, p: a, q: u, pr: P });\n  if (c.O) {\n    r.j.info(\n      `Logged ${u} purchase${u > 1 ? \"s\" : \"\"} of \"${o}\" for ${n} ${a}.`,\n    );\n    for (const r of c.ve) et.er().be(tt.Rr, [o, D], r);\n  }\n  return c.O;\n}\n"], "names": ["e", "MAX_PURCHASE_QUANTITY", "rr", "r", "tt", "TriggersProviderFactory", "et", "validateCustomProperties", "rt", "validateCustomString", "ot", "s", "logPurchase", "o", "i", "n", "t", "D", "length", "j", "error", "isNaN", "parseFloat", "toString", "a", "toFixed", "parseInt", "u", "toUpperCase", "indexOf", "g", "P", "R", "tr", "Dr", "info", "c", "N", "q", "Pr", "pid", "p", "pr", "O", "ve", "er", "be", "Rr"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}