{"version": 3, "file": "default-node_modules_angular_material_fesm2022_expansion_mjs.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAoC;AACyF;AAC9E;AACe;AACjB;;AAE7C;AACA,IAAIc,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAId,yDAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA,MAAMe,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,aAAa,GAAG,IAAIN,yCAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAACO,oBAAoB,GAAG,IAAIP,yCAAO,CAAC,CAAC;IACzC;IACA,IAAI,CAACQ,EAAE,GAAI,iBAAgBN,QAAQ,EAAG,EAAC;IACvC,IAAI,CAACO,MAAM,GAAG,KAAK;EACvB;EACA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,MAAM;EACtB;EACA,IAAIC,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACD,MAAM,GAAGV,4EAAqB,CAACW,KAAK,CAAC;EAC9C;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACF,MAAM,EAAE;MACb,IAAI,CAACF,oBAAoB,CAACK,IAAI,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACN,oBAAoB,CAACK,IAAI,CAAC,KAAK,CAAC;EACzC;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACT,aAAa,CAACM,IAAI,CAACG,OAAO,CAAC;EACpC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,aAAa,CAACW,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACV,oBAAoB,CAACU,QAAQ,CAAC,CAAC;EACxC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhB,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACiB,IAAI,kBAD8EjC,+DAAE;MAAAmC,IAAA,EACJnB,YAAY;MAAAoB,SAAA;MAAAC,MAAA;QAAAf,KAAA;MAAA;MAAAgB,QAAA;MAAAC,QAAA,GADVvC,gEAAE,CAC4F,CAAC;QAAEyC,OAAO,EAAE1B,aAAa;QAAE2B,WAAW,EAAE1B;MAAa,CAAC,CAAC,GADrJhB,kEAAE;IAAA,EACqN;EAAE;AAC7T;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KAHoG5C,+DAAE,CAGXgB,YAAY,EAAc,CAAC;IAC1GmB,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCT,QAAQ,EAAE,cAAc;MACxBU,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE1B,aAAa;QAAE2B,WAAW,EAAE1B;MAAa,CAAC;IACrE,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEM,KAAK,EAAE,CAAC;MACtBa,IAAI,EAAEhC,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAI8C,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACA,QAAQ,EAAE;IACnBA,QAAQ,GAAGxC,4EAAqB,CAACwC,QAAQ,CAAC;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,KAAKD,QAAQ,EAAE;MAC7B,IAAI,CAACC,SAAS,GAAGD,QAAQ;MACzB,IAAI,CAACE,cAAc,CAACC,IAAI,CAACH,QAAQ,CAAC;MAClC,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACI,MAAM,CAACD,IAAI,CAAC,CAAC;QAClB;AAChB;AACA;AACA;QACgB,MAAME,WAAW,GAAG,IAAI,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACrC,EAAE,GAAG,IAAI,CAACA,EAAE;QAChE,IAAI,CAACsC,oBAAoB,CAACC,MAAM,CAAC,IAAI,CAACvC,EAAE,EAAEoC,WAAW,CAAC;MAC1D,CAAC,MACI;QACD,IAAI,CAACI,MAAM,CAACN,IAAI,CAAC,CAAC;MACtB;MACA;MACA;MACA,IAAI,CAACO,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,SAAS,GAAGrD,4EAAqB,CAACoD,QAAQ,CAAC;EACpD;EACA9C,WAAWA,CAACwC,SAAS,EAAEI,kBAAkB,EAAEH,oBAAoB,EAAE;IAC7D,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACH,oBAAoB,GAAGA,oBAAoB;IAChD;IACA,IAAI,CAACO,yBAAyB,GAAGpD,8CAAY,CAACqD,KAAK;IACnD;IACA,IAAI,CAACN,MAAM,GAAG,IAAIxD,uDAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACmD,MAAM,GAAG,IAAInD,uDAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC+D,SAAS,GAAG,IAAI/D,uDAAY,CAAC,CAAC;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiD,cAAc,GAAG,IAAIjD,uDAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACgB,EAAE,GAAI,uBAAsB6B,MAAM,EAAG,EAAC;IAC3C,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACY,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACI,8BAA8B,GAAG,MAAM,CAAE,CAAC;IAC/C,IAAI,CAACA,8BAA8B,GAAGV,oBAAoB,CAACW,MAAM,CAAC,CAACjD,EAAE,EAAEoC,WAAW,KAAK;MACnF,IAAI,IAAI,CAACC,SAAS,IACd,CAAC,IAAI,CAACA,SAAS,CAACnC,KAAK,IACrB,IAAI,CAACmC,SAAS,CAACrC,EAAE,KAAKoC,WAAW,IACjC,IAAI,CAACpC,EAAE,KAAKA,EAAE,EAAE;QAChB,IAAI,CAAC+B,QAAQ,GAAG,KAAK;MACzB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,IAAI,CAACM,SAAS,EAAE;MAChB,IAAI,CAACQ,yBAAyB,GAAG,IAAI,CAACK,+BAA+B,CAAC,CAAC;IAC3E;EACJ;EACA;EACA1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2B,MAAM,CAAC1B,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC+B,MAAM,CAAC/B,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACsC,SAAS,CAACb,IAAI,CAAC,CAAC;IACrB,IAAI,CAACa,SAAS,CAACtC,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACuC,8BAA8B,CAAC,CAAC;IACrC,IAAI,CAACH,yBAAyB,CAACM,WAAW,CAAC,CAAC;EAChD;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACT,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAClC;EACJ;EACA;EACAsB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,KAAK;IACzB;EACJ;EACA;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACX,QAAQ,EAAE;MAChB,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACxB;EACJ;EACAmB,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACb,SAAS,CAACtC,oBAAoB,CAACwD,SAAS,CAACxB,QAAQ,IAAI;MAC7D;MACA,IAAI,CAAC,IAAI,CAACY,QAAQ,EAAE;QAChB,IAAI,CAACZ,QAAQ,GAAGA,QAAQ;MAC5B;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACrB,IAAI,YAAA8C,yBAAA5C,CAAA;MAAA,YAAAA,CAAA,IAAwFkB,gBAAgB,EA/H1BlD,+DAAE,CA+H0Ce,aAAa,OA/HzDf,+DAAE,CA+HoGA,4DAAoB,GA/H1HA,+DAAE,CA+HqIU,+EAA4B;IAAA,CAA4C;EAAE;EACjT;IAAS,IAAI,CAACuB,IAAI,kBAhI8EjC,+DAAE;MAAAmC,IAAA,EAgIJe,gBAAgB;MAAAd,SAAA;MAAAC,MAAA;QAAAc,QAAA;QAAAY,QAAA;MAAA;MAAAiB,OAAA;QAAApB,MAAA;QAAAL,MAAA;QAAAY,SAAA;QAAAd,cAAA;MAAA;MAAAf,QAAA;MAAAC,QAAA,GAhIdvC,gEAAE,CAgIgP;MAC1U;MACA;MACA;QAAEyC,OAAO,EAAE1B,aAAa;QAAEkE,QAAQ,EAAEC;MAAU,CAAC,CAClD;IAAA,EAAiD;EAAE;AAC5D;AACA;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KAtIoG5C,+DAAE,CAsIXkD,gBAAgB,EAAc,CAAC;IAC9Gf,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wCAAwC;MAClDT,QAAQ,EAAE,kBAAkB;MAC5BU,SAAS,EAAE;MACP;MACA;MACA;QAAEP,OAAO,EAAE1B,aAAa;QAAEkE,QAAQ,EAAEC;MAAU,CAAC;IAEvD,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/C,IAAI,EAAEnB,YAAY;MAAEmE,UAAU,EAAE,CAAC;QACjEhD,IAAI,EAAE9B,mDAAQA;MAClB,CAAC,EAAE;QACC8B,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAAC/B,aAAa;MACxB,CAAC,EAAE;QACCoB,IAAI,EAAE5B,mDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAE4B,IAAI,EAAEnC,4DAAoB8E;IAAC,CAAC,EAAE;MAAE3C,IAAI,EAAEzB,+EAA4BqE;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEnB,MAAM,EAAE,CAAC;MAC7GzB,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE+C,MAAM,EAAE,CAAC;MACTpB,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE2D,SAAS,EAAE,CAAC;MACZhC,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE6C,cAAc,EAAE,CAAC;MACjBlB,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE2C,QAAQ,EAAE,CAAC;MACXhB,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAE4D,QAAQ,EAAE,CAAC;MACX5B,IAAI,EAAEhC,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiF,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACtD,IAAI,YAAAuD,2BAAArD,CAAA;MAAA,YAAAA,CAAA,IAAwFoD,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBAxK8EtF,8DAAE;MAAAmC,IAAA,EAwKSiD;IAAkB,EAA8F;EAAE;EAC7N;IAAS,IAAI,CAACI,IAAI,kBAzK8ExF,8DAAE,IAyK8B;EAAE;AACtI;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KA3KoG5C,+DAAE,CA2KXoF,kBAAkB,EAAc,CAAC;IAChHjD,IAAI,EAAE1B,mDAAQ;IACdqC,IAAI,EAAE,CAAC;MACC4C,OAAO,EAAE,CAAC1E,YAAY,EAAEkC,gBAAgB,CAAC;MACzCyC,YAAY,EAAE,CAAC3E,YAAY,EAAEkC,gBAAgB;IACjD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxO4F;AAClD;AACyB;AAC7B;AACmB;AACrB;AACsN;AAClL;AACV;AACpB;AACU;AAC2B;AACV;AACQ;AAClB;AACsB;AAClC;;AAE/C;AACA;AACA;AACA;AAHA,MAAA2E,GAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,wCAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgEoG/H,uDAAE,aAsU0uC,CAAC;EAAA;EAAA,IAAA+H,EAAA;IAAA,MAAAM,MAAA,GAtU7uCrI,2DAAE;IAAFA,wDAAE,qBAAAqI,MAAA,CAAAG,iBAAA,EAsUiqC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAlYxwC,MAAMC,aAAa,GAAG,IAAI1I,yDAAc,CAAC,eAAe,CAAC;;AAEzD;AACA;AACA,MAAM2I,gCAAgC,GAAG,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,eAAe,EAAEtB,4DAAO,CAAC,iBAAiB,EAAE,CACxCC,0DAAK,CAAC,iBAAiB,EAAEC,0DAAK,CAAC;IAAEqB,SAAS,EAAE;EAAe,CAAC,CAAC,CAAC,EAC9DtB,0DAAK,CAAC,UAAU,EAAEC,0DAAK,CAAC;IAAEqB,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EACzDpB,+DAAU,CAAC,2CAA2C,EAAEC,4DAAO,CAACgB,gCAAgC,CAAC,CAAC,CACrG,CAAC;EACF;EACAI,aAAa,EAAExB,4DAAO,CAAC,eAAe,EAAE,CACpCC,0DAAK,CAAC,iBAAiB,EAAEC,0DAAK,CAAC;IAAEuB,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAS,CAAC,CAAC,CAAC;EACxE;EACA;EACA;EACAzB,0DAAK,CAAC,UAAU,EAAEC,0DAAK,CAAC;IAAEuB,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC,CAAC,EACzDvB,+DAAU,CAAC,2CAA2C,EAAEC,4DAAO,CAACgB,gCAAgC,CAAC,CAAC,CACrG;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMO,mBAAmB,GAAG,IAAIlJ,yDAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAMmJ,wBAAwB,CAAC;EAC3BnI,WAAWA,CAACoI,SAAS,EAAEC,eAAe,EAAE;IACpC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACA;IAAS,IAAI,CAACxH,IAAI,YAAAyH,iCAAAvH,CAAA;MAAA,YAAAA,CAAA,IAAwFoH,wBAAwB,EAAlCpJ,+DAAE,CAAkDA,sDAAc,GAAlEA,+DAAE,CAA6EmJ,mBAAmB;IAAA,CAA4D;EAAE;EAChQ;IAAS,IAAI,CAAClH,IAAI,kBAD8EjC,+DAAE;MAAAmC,IAAA,EACJiH,wBAAwB;MAAAhH,SAAA;IAAA,EAAoE;EAAE;AAChM;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHoG5C,+DAAE,CAGXoJ,wBAAwB,EAAc,CAAC;IACtHjH,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEZ,IAAI,EAAEnC,sDAAcwJ;IAAC,CAAC,EAAE;MAAErH,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxFhD,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAACqG,mBAAmB;MAC9B,CAAC,EAAE;QACChH,IAAI,EAAE9B,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,IAAIoJ,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAIzJ,yDAAc,CAAC,qCAAqC,CAAC;AACrG;AACA;AACA;AACA;AACA,MAAM0J,iBAAiB,SAASzG,oEAAgB,CAAC;EAC7C;EACA,IAAI0G,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAK,IAAI,CAACpG,SAAS,IAAI,IAAI,CAACA,SAAS,CAACmG,UAAW;EAC5E;EACA,IAAIA,UAAUA,CAACE,KAAK,EAAE;IAClB,IAAI,CAACD,WAAW,GAAGlJ,4EAAqB,CAACmJ,KAAK,CAAC;EACnD;EACA;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,eAAe,IAAK,IAAI,CAACvG,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsG,cAAe;EACpF;EACA,IAAIA,cAAcA,CAACD,KAAK,EAAE;IACtB,IAAI,CAACE,eAAe,GAAGF,KAAK;EAChC;EACA7I,WAAWA,CAACwC,SAAS,EAAEI,kBAAkB,EAAEoG,0BAA0B,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAE;IACjI,KAAK,CAAC5G,SAAS,EAAEI,kBAAkB,EAAEoG,0BAA0B,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACP,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACS,WAAW,GAAG,IAAIlK,uDAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACmK,aAAa,GAAG,IAAInK,uDAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAACoK,aAAa,GAAG,IAAI5J,yCAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAAC6J,SAAS,GAAI,8BAA6BhB,QAAQ,EAAG,EAAC;IAC3D;IACA,IAAI,CAACiB,kBAAkB,GAAG,IAAI9J,yCAAO,CAAC,CAAC;IACvC,IAAI,CAAC6C,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0G,SAAS,GAAGA,SAAS;IAC1B;IACA;IACA,IAAI,CAACO,kBAAkB,CAClBC,IAAI,CAAC5D,oEAAoB,CAAC,CAAC6D,CAAC,EAAEC,CAAC,KAAK;MACrC,OAAOD,CAAC,CAACE,SAAS,KAAKD,CAAC,CAACC,SAAS,IAAIF,CAAC,CAACG,OAAO,KAAKF,CAAC,CAACE,OAAO;IACjE,CAAC,CAAC,CAAC,CACEpG,SAAS,CAACqG,KAAK,IAAI;MACpB,IAAIA,KAAK,CAACF,SAAS,KAAK,MAAM,EAAE;QAC5B,IAAIE,KAAK,CAACD,OAAO,KAAK,UAAU,EAAE;UAC9B,IAAI,CAACT,WAAW,CAAChH,IAAI,CAAC,CAAC;QAC3B,CAAC,MACI,IAAI0H,KAAK,CAACD,OAAO,KAAK,WAAW,EAAE;UACpC,IAAI,CAACR,aAAa,CAACjH,IAAI,CAAC,CAAC;QAC7B;MACJ;IACJ,CAAC,CAAC;IACF,IAAI+G,cAAc,EAAE;MAChB,IAAI,CAACT,UAAU,GAAGS,cAAc,CAACT,UAAU;IAC/C;EACJ;EACA;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxH,SAAS,EAAE;MAChB,OAAO,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACM,SAAS,CAACyH,WAAW,KAAK,SAAS;IACpE;IACA,OAAO,KAAK;EAChB;EACA;EACA1C,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACrF,QAAQ,GAAG,UAAU,GAAG,WAAW;EACnD;EACA;EACAqB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACrB,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAClC;EACA;EACAsB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACtB,QAAQ,GAAG,KAAK;EACzB;EACA;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAI,CAACvB,QAAQ,GAAG,IAAI;EACxB;EACAgI,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC9B,eAAe,KAAK,IAAI,EAAE;MACjE;MACA,IAAI,CAAC/F,MAAM,CACNoH,IAAI,CAAC3D,yDAAS,CAAC,IAAI,CAAC,EAAEC,sDAAM,CAAC,MAAM,IAAI,CAAC9D,QAAQ,IAAI,CAAC,IAAI,CAACkI,OAAO,CAAC,EAAEnE,oDAAI,CAAC,CAAC,CAAC,CAAC,CAC5EvC,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC0G,OAAO,GAAG,IAAIxF,+DAAc,CAAC,IAAI,CAACuF,YAAY,CAAC/B,SAAS,EAAE,IAAI,CAACa,iBAAiB,CAAC;MAC1F,CAAC,CAAC;IACN;EACJ;EACAxI,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAC6I,aAAa,CAAChJ,IAAI,CAACG,OAAO,CAAC;EACpC;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC8I,kBAAkB,CAAC7I,QAAQ,CAAC,CAAC;IAClC,IAAI,CAAC2I,aAAa,CAAC3I,QAAQ,CAAC,CAAC;EACjC;EACA;EACAyJ,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,MAAMC,cAAc,GAAG,IAAI,CAACrB,SAAS,CAACsB,aAAa;MACnD,MAAMC,WAAW,GAAG,IAAI,CAACH,KAAK,CAACI,aAAa;MAC5C,OAAOH,cAAc,KAAKE,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAACJ,cAAc,CAAC;IACjF;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAAC1J,IAAI,YAAA+J,0BAAA7J,CAAA;MAAA,YAAAA,CAAA,IAAwF2H,iBAAiB,EAhI3B3J,+DAAE,CAgI2C2I,aAAa,OAhI1D3I,+DAAE,CAgIqGA,4DAAoB,GAhI3HA,+DAAE,CAgIsIU,gFAA4B,GAhIpKV,+DAAE,CAgI+KA,2DAAmB,GAhIpMA,+DAAE,CAgI+MgG,sDAAQ,GAhIzNhG,+DAAE,CAgIoOsH,gEAAqB,MAhI3PtH,+DAAE,CAgIsR0J,mCAAmC;IAAA,CAA4D;EAAE;EACzd;IAAS,IAAI,CAACqC,IAAI,kBAjI8E/L,+DAAE;MAAAmC,IAAA,EAiIJwH,iBAAiB;MAAAvH,SAAA;MAAA6J,cAAA,WAAAC,iCAAAnE,EAAA,EAAAC,GAAA,EAAAmE,QAAA;QAAA,IAAApE,EAAA;UAjIf/H,4DAAE,CAAAmM,QAAA,EAsIzB/C,wBAAwB;QAAA;QAAA,IAAArB,EAAA;UAAA,IAAAsE,EAAA;UAtIDrM,4DAAE,CAAAqM,EAAA,GAAFrM,yDAAE,QAAAgI,GAAA,CAAAoD,YAAA,GAAAiB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,wBAAA3E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/H,yDAAE,CAAA6H,GAAA;QAAA;QAAA,IAAAE,EAAA;UAAA,IAAAsE,EAAA;UAAFrM,4DAAE,CAAAqM,EAAA,GAAFrM,yDAAE,QAAAgI,GAAA,CAAAuD,KAAA,GAAAc,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,+BAAAhF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/H,yDAAE,iBAAAgI,GAAA,CAAA7E,QAAA,6BAAA6E,GAAA,CAAAoC,cAAA,sDAAApC,GAAA,CAAAiD,WAAA;QAAA;MAAA;MAAA5I,MAAA;QAAA0B,QAAA;QAAAZ,QAAA;QAAAyG,UAAA;QAAAG,cAAA;MAAA;MAAA/E,OAAA;QAAAzB,MAAA;QAAAK,MAAA;QAAAP,cAAA;QAAAiH,WAAA;QAAAC,aAAA;MAAA;MAAAjI,QAAA;MAAAC,QAAA,GAAFvC,gEAAE,CAiIiiB;MAC3nB;MACA;MACA;QAAEyC,OAAO,EAAEkG,aAAa;QAAE1D,QAAQ,EAAEC;MAAU,CAAC,EAC/C;QAAEzC,OAAO,EAAE0G,mBAAmB;QAAEzG,WAAW,EAAEiH;MAAkB,CAAC,CACnE,GAtI2F3J,wEAAE,EAAFA,kEAAE;MAAAkN,kBAAA,EAAAhF,GAAA;MAAAiF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAxF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/H,6DAAE,CAAAiI,GAAA;UAAFjI,0DAAE,EAsI0R,CAAC;UAtI7RA,4DAAE,eAsIqhB,CAAC;UAtIxhBA,wDAAE,iCAAA4N,uEAAAC,MAAA;YAAA,OAsI2a7F,GAAA,CAAA0C,kBAAA,CAAAlJ,IAAA,CAAAqM,MAA8B,CAAC;UAAA,CAAC,CAAC;UAtI9c7N,4DAAE,YAsIikB,CAAC;UAtIpkBA,0DAAE,KAsIgmB,CAAC;UAtInmBA,wDAAE,IAAA8H,wCAAA,wBAsI+pB,CAAC;UAtIlqB9H,0DAAE,CAsIyqB,CAAC;UAtI5qBA,0DAAE,KAsIguB,CAAC;UAtInuBA,0DAAE,CAsIwuB,CAAC;QAAA;QAAA,IAAA+H,EAAA;UAtI3uB/H,uDAAE,EAsI2Y,CAAC;UAtI9YA,wDAAE,mBAAAgI,GAAA,CAAAQ,iBAAA,EAsI2Y,CAAC,OAAAR,GAAA,CAAA5G,EAAD,CAAC;UAtI9YpB,yDAAE,oBAAAgI,GAAA,CAAAyC,SAsIsf,CAAC;UAtIzfzK,uDAAE,EAsIgpB,CAAC;UAtInpBA,wDAAE,oBAAAgI,GAAA,CAAAqD,OAsIgpB,CAAC;QAAA;MAAA;MAAA6C,YAAA,GAA8mFtI,gEAAkB;MAAAwI,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAmI,CAAC1F,sBAAsB,CAACG,aAAa;MAAC;MAAAwF,eAAA;IAAA,EAAiG;EAAE;AACnoH;AACA;EAAA,QAAA5L,SAAA,oBAAAA,SAAA,KAxIoG5C,+DAAE,CAwIX2J,iBAAiB,EAAc,CAAC;IAC/GxH,IAAI,EAAE+D,oDAAS;IACfpD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAET,QAAQ,EAAE,mBAAmB;MAAE+L,aAAa,EAAElI,4DAAiB,CAACsI,IAAI;MAAED,eAAe,EAAEpI,kEAAuB,CAACsI,MAAM;MAAErM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;MAAE2C,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,CAAC;MAAE2J,UAAU,EAAE,CAAC9F,sBAAsB,CAACG,aAAa,CAAC;MAAEhG,SAAS,EAAE;MACvS;MACA;MACA;QAAEP,OAAO,EAAEkG,aAAa;QAAE1D,QAAQ,EAAEC;MAAU,CAAC,EAC/C;QAAEzC,OAAO,EAAE0G,mBAAmB;QAAEzG,WAAW,EAAEiH;MAAkB,CAAC,CACnE;MAAEiF,IAAI,EAAE;QACL,OAAO,EAAE,qBAAqB;QAC9B,sBAAsB,EAAE,UAAU;QAClC,iCAAiC,EAAE,qCAAqC;QACxE,qCAAqC,EAAE;MAC3C,CAAC;MAAEtB,QAAQ,EAAE,ihBAAihB;MAAEc,MAAM,EAAE,CAAC,29EAA29E;IAAE,CAAC;EACnhG,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjM,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DhD,IAAI,EAAE9B,mDAAQA;MAClB,CAAC,EAAE;QACC8B,IAAI,EAAE5B,mDAAQA;MAClB,CAAC,EAAE;QACC4B,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAAC6F,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAExG,IAAI,EAAEnC,4DAAoB8E;IAAC,CAAC,EAAE;MAAE3C,IAAI,EAAEzB,gFAA4BqE;IAAC,CAAC,EAAE;MAAE5C,IAAI,EAAEnC,2DAAmB8L;IAAC,CAAC,EAAE;MAAE3J,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QACzIhD,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAACkD,sDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE7D,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClChD,IAAI,EAAE9B,mDAAQA;MAClB,CAAC,EAAE;QACC8B,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAACwE,gEAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEnF,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClChD,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAAC4G,mCAAmC;MAC9C,CAAC,EAAE;QACCvH,IAAI,EAAE9B,mDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuJ,UAAU,EAAE,CAAC;MACzCzH,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAE4J,cAAc,EAAE,CAAC;MACjB5H,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAEmK,WAAW,EAAE,CAAC;MACdnI,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE+J,aAAa,EAAE,CAAC;MAChBpI,IAAI,EAAE3B,iDAAMA;IAChB,CAAC,CAAC;IAAE4K,YAAY,EAAE,CAAC;MACfjJ,IAAI,EAAEkE,uDAAY;MAClBvD,IAAI,EAAE,CAACsG,wBAAwB;IACnC,CAAC,CAAC;IAAEmC,KAAK,EAAE,CAAC;MACRpJ,IAAI,EAAEmE,oDAAS;MACfxD,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAM+L,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAAC/M,IAAI,YAAAgN,mCAAA9M,CAAA;MAAA,YAAAA,CAAA,IAAwF6M,0BAA0B;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAAC5M,IAAI,kBA7L8EjC,+DAAE;MAAAmC,IAAA,EA6LJ0M,0BAA0B;MAAAzM,SAAA;MAAAwK,SAAA;IAAA,EAAyF;EAAE;AACvN;AACA;EAAA,QAAAhK,SAAA,oBAAAA,SAAA,KA/LoG5C,+DAAE,CA+LX6O,0BAA0B,EAAc,CAAC;IACxH1M,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1B6L,IAAI,EAAE;QACFG,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA,MAAMC,2BAA2B,CAAC;AAElC,MAAMC,iCAAiC,GAAGtI,sEAAa,CAACqI,2BAA2B,CAAC;AACpF;AACA;AACA;AACA,MAAME,uBAAuB,SAASD,iCAAiC,CAAC;EACpEhO,WAAWA,CAACkO,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAExL,kBAAkB,EAAEwG,cAAc,EAAED,cAAc,EAAEkF,QAAQ,EAAE;IACtG,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACxL,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACuG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACmF,yBAAyB,GAAG1O,+CAAY,CAACqD,KAAK;IACnD,MAAMsL,yBAAyB,GAAGL,KAAK,CAAC1L,SAAS,GAC3C0L,KAAK,CAAC1L,SAAS,CAACvC,aAAa,CAACyJ,IAAI,CAAC1D,sDAAM,CAACtF,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC7GuC,wCAAK;IACX,IAAI,CAACoL,QAAQ,GAAGG,QAAQ,CAACH,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;IAC7C;IACA;IACA,IAAI,CAACC,yBAAyB,GAAGhI,4CAAK,CAAC4H,KAAK,CAAC5L,MAAM,EAAE4L,KAAK,CAACvL,MAAM,EAAE4L,yBAAyB,EAAEL,KAAK,CAAC3E,aAAa,CAACG,IAAI,CAAC1D,sDAAM,CAACtF,OAAO,IAAI;MACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAACgD,SAAS,CAAC,MAAM,IAAI,CAACd,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IAC5D;IACAqL,KAAK,CAACvL,MAAM,CACP+G,IAAI,CAAC1D,sDAAM,CAAC,MAAMkI,KAAK,CAAC7D,cAAc,CAAC,CAAC,CAAC,CAAC,CAC1C3G,SAAS,CAAC,MAAM0K,aAAa,CAACK,QAAQ,CAACN,QAAQ,EAAE,SAAS,CAAC,CAAC;IACjE,IAAI/E,cAAc,EAAE;MAChB,IAAI,CAACsF,cAAc,GAAGtF,cAAc,CAACsF,cAAc;MACnD,IAAI,CAACC,eAAe,GAAGvF,cAAc,CAACuF,eAAe;IACzD;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI7L,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoL,KAAK,CAACpL,QAAQ;EAC9B;EACA;EACA8L,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC9L,QAAQ,EAAE;MAChB,IAAI,CAACoL,KAAK,CAAC3K,MAAM,CAAC,CAAC;IACvB;EACJ;EACA;EACAsL,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACX,KAAK,CAAChM,QAAQ;EAC9B;EACA;EACAqF,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2G,KAAK,CAAC3G,iBAAiB,CAAC,CAAC;EACzC;EACA;EACAuH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACZ,KAAK,CAAC/N,EAAE;EACxB;EACA;EACA4O,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACb,KAAK,CAACpF,cAAc;EACpC;EACA;EACAkG,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACd,KAAK,CAACvF,UAAU,IAAI,CAAC,IAAI,CAACuF,KAAK,CAACpL,QAAQ;EACzD;EACA;AACJ;AACA;AACA;EACImM,gBAAgBA,CAAA,EAAG;IACf,MAAMC,UAAU,GAAG,IAAI,CAACL,WAAW,CAAC,CAAC;IACrC,IAAIK,UAAU,IAAI,IAAI,CAACR,cAAc,EAAE;MACnC,OAAO,IAAI,CAACA,cAAc;IAC9B,CAAC,MACI,IAAI,CAACQ,UAAU,IAAI,IAAI,CAACP,eAAe,EAAE;MAC1C,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI;EACf;EACA;EACAQ,QAAQA,CAACpF,KAAK,EAAE;IACZ,QAAQA,KAAK,CAACqF,OAAO;MACjB;MACA,KAAKhJ,yDAAK;MACV,KAAKF,yDAAK;QACN,IAAI,CAACC,sEAAc,CAAC4D,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACsF,cAAc,CAAC,CAAC;UACtB,IAAI,CAACT,OAAO,CAAC,CAAC;QAClB;QACA;MACJ;QACI,IAAI,IAAI,CAACV,KAAK,CAAC1L,SAAS,EAAE;UACtB,IAAI,CAAC0L,KAAK,CAAC1L,SAAS,CAAC8M,oBAAoB,CAACvF,KAAK,CAAC;QACpD;QACA;IACR;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIwF,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAID,MAAM,EAAE;MACR,IAAI,CAACpB,aAAa,CAACK,QAAQ,CAAC,IAAI,CAACN,QAAQ,EAAEqB,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACtB,QAAQ,CAACzD,aAAa,CAAC6E,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACtB,aAAa,CAACuB,OAAO,CAAC,IAAI,CAACxB,QAAQ,CAAC,CAACzK,SAAS,CAAC8L,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI,IAAI,CAACtB,KAAK,CAAC1L,SAAS,EAAE;QAChC,IAAI,CAAC0L,KAAK,CAAC1L,SAAS,CAACoN,kBAAkB,CAAC,IAAI,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACAjP,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2N,yBAAyB,CAAChL,WAAW,CAAC,CAAC;IAC5C,IAAI,CAAC8K,aAAa,CAACyB,cAAc,CAAC,IAAI,CAAC1B,QAAQ,CAAC;EACpD;EACA;IAAS,IAAI,CAACtN,IAAI,YAAAiP,gCAAA/O,CAAA;MAAA,YAAAA,CAAA,IAAwFkN,uBAAuB,EArUjClP,+DAAE,CAqUiD2J,iBAAiB,MArUpE3J,+DAAE,CAqU2FA,qDAAa,GArU1GA,+DAAE,CAqUqH6G,4DAAiB,GArUxI7G,+DAAE,CAqUmJA,4DAAoB,GArUzKA,+DAAE,CAqUoL0J,mCAAmC,MArUzN1J,+DAAE,CAqUoPsH,gEAAqB,MArU3QtH,+DAAE,CAqUsS,UAAU;IAAA,CAA6D;EAAE;EACjd;IAAS,IAAI,CAAC+L,IAAI,kBAtU8E/L,+DAAE;MAAAmC,IAAA,EAsUJ+M,uBAAuB;MAAA9M,SAAA;MAAAwK,SAAA,WAAgL,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAAqE,qCAAApJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtU7M/H,wDAAE,mBAAAoR,iDAAA;YAAA,OAsUJpJ,GAAA,CAAA6H,OAAA,CAAQ,CAAC;UAAA,uBAAAwB,mDAAAxD,MAAA;YAAA,OAAT7F,GAAA,CAAAoI,QAAA,CAAAvC,MAAe,CAAC;UAAA;QAAA;QAAA,IAAA9F,EAAA;UAtUd/H,yDAAE,OAAAgI,GAAA,CAAAmH,KAAA,CAAA1E,SAAA,cAAAzC,GAAA,CAAAsH,QAAA,mBAAAtH,GAAA,CAAA+H,WAAA,qBAAA/H,GAAA,CAAA8H,WAAA,qBAAA9H,GAAA,CAAAmH,KAAA,CAAApL,QAAA;UAAF/D,yDAAE,WAAAgI,GAAA,CAAAkI,gBAAA;UAAFlQ,yDAAE,iBAAAgI,GAAA,CAAA8H,WAAA,4CAAA9H,GAAA,CAAAgI,kBAAA,yDAAAhI,GAAA,CAAAgI,kBAAA,4CAAAhI,GAAA,CAAAoC,cAAA;QAAA;MAAA;MAAA/H,MAAA;QAAAiN,QAAA;QAAAK,cAAA;QAAAC,eAAA;MAAA;MAAArN,QAAA,GAAFvC,wEAAE;MAAAkN,kBAAA,EAAAxE,GAAA;MAAAyE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiE,iCAAAxJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/H,6DAAE,CAAAyI,GAAA;UAAFzI,4DAAE,aAsUm9B,CAAC;UAtUt9BA,0DAAE,EAsU2gC,CAAC;UAtU9gCA,0DAAE,KAsUykC,CAAC;UAtU5kCA,0DAAE,KAsUsmC,CAAC;UAtUzmCA,0DAAE,CAsU+mC,CAAC;UAtUlnCA,wDAAE,IAAAmI,uCAAA,iBAsU0uC,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAtU7uC/H,yDAAE,6BAAAgI,GAAA,CAAAiI,WAAA,EAsUk9B,CAAC;UAtUr9BjQ,uDAAE,EAsUurC,CAAC;UAtU1rCA,wDAAE,SAAAgI,GAAA,CAAAiI,WAAA,EAsUurC,CAAC;QAAA;MAAA;MAAA/B,YAAA,GAA+4GnI,kDAAO;MAAAqI,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAA+E,CAAC1F,sBAAsB,CAACC,eAAe;MAAC;MAAA0F,eAAA;IAAA,EAAiG;EAAE;AAC94J;AACA;EAAA,QAAA5L,SAAA,oBAAAA,SAAA,KAxUoG5C,+DAAE,CAwUXkP,uBAAuB,EAAc,CAAC;IACrH/M,IAAI,EAAE+D,oDAAS;IACfpD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEsL,aAAa,EAAElI,4DAAiB,CAACsI,IAAI;MAAED,eAAe,EAAEpI,kEAAuB,CAACsI,MAAM;MAAErM,MAAM,EAAE,CAAC,UAAU,CAAC;MAAEsM,UAAU,EAAE,CAAC9F,sBAAsB,CAACC,eAAe,CAAC;MAAE8F,IAAI,EAAE;QAC/M,OAAO,EAAE,gDAAgD;QACzD,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,iBAAiB;QAC9B,iBAAiB,EAAE,UAAU;QAC7B,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,gBAAgB;QACxC,sBAAsB,EAAE,eAAe;QACvC,8CAA8C,EAAG,kCAAiC;QAClF,+CAA+C,EAAG,mCAAkC;QACpF,iCAAiC,EAAE,qCAAqC;QACxE,gBAAgB,EAAE,oBAAoB;QACtC,SAAS,EAAE,WAAW;QACtB,WAAW,EAAE;MACjB,CAAC;MAAEtB,QAAQ,EAAE,0WAA0W;MAAEc,MAAM,EAAE,CAAC,iyGAAiyG;IAAE,CAAC;EAClrH,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjM,IAAI,EAAEwH,iBAAiB;MAAExE,UAAU,EAAE,CAAC;QACtEhD,IAAI,EAAEoE,+CAAIA;MACd,CAAC;IAAE,CAAC,EAAE;MAAEpE,IAAI,EAAEnC,qDAAagR;IAAC,CAAC,EAAE;MAAE7O,IAAI,EAAE0E,4DAAiBoK;IAAC,CAAC,EAAE;MAAE9O,IAAI,EAAEnC,4DAAoB8E;IAAC,CAAC,EAAE;MAAE3C,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QACxHhD,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAAC4G,mCAAmC;MAC9C,CAAC,EAAE;QACCvH,IAAI,EAAE9B,mDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAE8B,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClChD,IAAI,EAAE9B,mDAAQA;MAClB,CAAC,EAAE;QACC8B,IAAI,EAAE7B,iDAAM;QACZwC,IAAI,EAAE,CAACwE,gEAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEnF,IAAI,EAAE+C,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClChD,IAAI,EAAEqE,oDAAS;QACf1D,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE6M,cAAc,EAAE,CAAC;MAC7CxN,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAEyP,eAAe,EAAE,CAAC;MAClBzN,IAAI,EAAEhC,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMsR,4BAA4B,CAAC;EAC/B;IAAS,IAAI,CAAC3P,IAAI,YAAA4P,qCAAA1P,CAAA;MAAA,YAAAA,CAAA,IAAwFyP,4BAA4B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAACxP,IAAI,kBAnX8EjC,+DAAE;MAAAmC,IAAA,EAmXJsP,4BAA4B;MAAArP,SAAA;MAAAwK,SAAA;IAAA,EAAwH;EAAE;AACxP;AACA;EAAA,QAAAhK,SAAA,oBAAAA,SAAA,KArXoG5C,+DAAE,CAqXXyR,4BAA4B,EAAc,CAAC;IAC1HtP,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjC6L,IAAI,EAAE;QACFG,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAM4C,sBAAsB,CAAC;EACzB;IAAS,IAAI,CAAC7P,IAAI,YAAA8P,+BAAA5P,CAAA;MAAA,YAAAA,CAAA,IAAwF2P,sBAAsB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAAC1P,IAAI,kBAnY8EjC,+DAAE;MAAAmC,IAAA,EAmYJwP,sBAAsB;MAAAvP,SAAA;MAAAwK,SAAA;IAAA,EAA4G;EAAE;AACtO;AACA;EAAA,QAAAhK,SAAA,oBAAAA,SAAA,KArYoG5C,+DAAE,CAqYX2R,sBAAsB,EAAc,CAAC;IACpHxP,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3B6L,IAAI,EAAE;QACFG,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAM8C,YAAY,SAAS7Q,gEAAY,CAAC;EACpCC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAG6Q,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,WAAW,GAAG,IAAItL,oDAAS,CAAC,CAAC;IAClC,IAAI,CAACoD,WAAW,GAAG,KAAK;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqB,WAAW,GAAG,SAAS;IAC5B;IACA,IAAI,CAACnB,cAAc,GAAG,OAAO;EACjC;EACA;EACA,IAAIH,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACoI,IAAI,EAAE;IACjB,IAAI,CAACnI,WAAW,GAAGlJ,4EAAqB,CAACqR,IAAI,CAAC;EAClD;EACA7G,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC8G,QAAQ,CAACtQ,OAAO,CAChBgJ,IAAI,CAAC3D,yDAAS,CAAC,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAC9BtN,SAAS,CAAEuN,OAAO,IAAK;MACxB,IAAI,CAACH,WAAW,CAACI,KAAK,CAACD,OAAO,CAACjL,MAAM,CAACmL,MAAM,IAAIA,MAAM,CAACjD,KAAK,CAAC1L,SAAS,KAAK,IAAI,CAAC,CAAC;MACjF,IAAI,CAACsO,WAAW,CAACM,eAAe,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACC,WAAW,GAAG,IAAIxL,+DAAe,CAAC,IAAI,CAACiL,WAAW,CAAC,CAACQ,QAAQ,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EACxF;EACA;EACAjC,oBAAoBA,CAACvF,KAAK,EAAE;IACxB,IAAI,CAACsH,WAAW,CAACG,SAAS,CAACzH,KAAK,CAAC;EACrC;EACA6F,kBAAkBA,CAACuB,MAAM,EAAE;IACvB,IAAI,CAACE,WAAW,CAACI,gBAAgB,CAACN,MAAM,CAAC;EAC7C;EACAxQ,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC0Q,WAAW,EAAEK,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACZ,WAAW,CAACY,OAAO,CAAC,CAAC;EAC9B;EACA;IAAS,IAAI,CAAC7Q,IAAI;MAAA,IAAA8Q,yBAAA;MAAA,gBAAAC,qBAAA7Q,CAAA;QAAA,QAAA4Q,yBAAA,KAAAA,yBAAA,GAhc8E5S,mEAAE,CAgcQ6R,YAAY,IAAA7P,CAAA,IAAZ6P,YAAY;MAAA;IAAA,GAAqD;EAAE;EAC7K;IAAS,IAAI,CAAC5P,IAAI,kBAjc8EjC,+DAAE;MAAAmC,IAAA,EAicJ0P,YAAY;MAAAzP,SAAA;MAAA6J,cAAA,WAAA8G,4BAAAhL,EAAA,EAAAC,GAAA,EAAAmE,QAAA;QAAA,IAAApE,EAAA;UAjcV/H,4DAAE,CAAAmM,QAAA,EAsc1C+C,uBAAuB;QAAA;QAAA,IAAAnH,EAAA;UAAA,IAAAsE,EAAA;UAtciBrM,4DAAE,CAAAqM,EAAA,GAAFrM,yDAAE,QAAAgI,GAAA,CAAAiK,QAAA,GAAA5F,EAAA;QAAA;MAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAkG,0BAAAjL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/H,yDAAE,wBAAAgI,GAAA,CAAA1G,KAAA;QAAA;MAAA;MAAAe,MAAA;QAAAf,KAAA;QAAAsI,UAAA;QAAAsB,WAAA;QAAAnB,cAAA;MAAA;MAAAzH,QAAA;MAAAC,QAAA,GAAFvC,gEAAE,CAic0Q,CACpW;QACIyC,OAAO,EAAEkG,aAAa;QACtBjG,WAAW,EAAEmP;MACjB,CAAC,CACJ,GAtc2F7R,wEAAE;IAAA,EAscuE;EAAE;AAC/K;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KAxcoG5C,+DAAE,CAwcX6R,YAAY,EAAc,CAAC;IAC1G1P,IAAI,EAAEjC,oDAAS;IACf4C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBT,QAAQ,EAAE,cAAc;MACxBD,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBW,SAAS,EAAE,CACP;QACIP,OAAO,EAAEkG,aAAa;QACtBjG,WAAW,EAAEmP;MACjB,CAAC,CACJ;MACDjD,IAAI,EAAE;QACFG,KAAK,EAAE,eAAe;QACtB;QACA;QACA,6BAA6B,EAAE;MACnC;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEkD,QAAQ,EAAE,CAAC;MACzB9P,IAAI,EAAEuE,0DAAe;MACrB5D,IAAI,EAAE,CAACoM,uBAAuB,EAAE;QAAE+D,WAAW,EAAE;MAAK,CAAC;IACzD,CAAC,CAAC;IAAErJ,UAAU,EAAE,CAAC;MACbzH,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAE+K,WAAW,EAAE,CAAC;MACd/I,IAAI,EAAEhC,gDAAKA;IACf,CAAC,CAAC;IAAE4J,cAAc,EAAE,CAAC;MACjB5H,IAAI,EAAEhC,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+S,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACpR,IAAI,YAAAqR,2BAAAnR,CAAA;MAAA,YAAAA,CAAA,IAAwFkR,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAAC5N,IAAI,kBAxe8EtF,8DAAE;MAAAmC,IAAA,EAweS+Q;IAAkB,EAYzF;EAAE;EACtC;IAAS,IAAI,CAAC1N,IAAI,kBArf8ExF,8DAAE;MAAAoT,OAAA,GAqfuCnN,0DAAY,EAAEW,oEAAe,EAAExB,sEAAkB,EAAEU,6DAAY;IAAA,EAAI;EAAE;AAClN;AACA;EAAA,QAAAlD,SAAA,oBAAAA,SAAA,KAvfoG5C,+DAAE,CAufXkT,kBAAkB,EAAc,CAAC;IAChH/Q,IAAI,EAAE1B,mDAAQ;IACdqC,IAAI,EAAE,CAAC;MACCsQ,OAAO,EAAE,CAACnN,0DAAY,EAAEW,oEAAe,EAAExB,sEAAkB,EAAEU,6DAAY,CAAC;MAC1EJ,OAAO,EAAE,CACLmM,YAAY,EACZlI,iBAAiB,EACjBkF,0BAA0B,EAC1BK,uBAAuB,EACvByC,sBAAsB,EACtBF,4BAA4B,EAC5BrI,wBAAwB,CAC3B;MACDzD,YAAY,EAAE,CACVkM,YAAY,EACZlI,iBAAiB,EACjBkF,0BAA0B,EAC1BK,uBAAuB,EACvByC,sBAAsB,EACtBF,4BAA4B,EAC5BrI,wBAAwB;IAEhC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./node_modules/@angular/cdk/fesm2022/accordion.mjs", "./node_modules/@angular/material/fesm2022/expansion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, Subscription } from 'rxjs';\n\n/** Used to generate unique ID for each accordion. */\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n    constructor() {\n        /** Emits when the state of the accordion changes */\n        this._stateChanges = new Subject();\n        /** Stream that emits true/false when openAll/closeAll is triggered. */\n        this._openCloseAllActions = new Subject();\n        /** A readonly id value to use for unique selection coordination. */\n        this.id = `cdk-accordion-${nextId$1++}`;\n        this._multi = false;\n    }\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    get multi() {\n        return this._multi;\n    }\n    set multi(multi) {\n        this._multi = coerceBooleanProperty(multi);\n    }\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n    openAll() {\n        if (this._multi) {\n            this._openCloseAllActions.next(true);\n        }\n    }\n    /** Closes all enabled accordion items. */\n    closeAll() {\n        this._openCloseAllActions.next(false);\n    }\n    ngOnChanges(changes) {\n        this._stateChanges.next(changes);\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n        this._openCloseAllActions.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordion, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkAccordion, selector: \"cdk-accordion, [cdkAccordion]\", inputs: { multi: \"multi\" }, providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }], exportAs: [\"cdkAccordion\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion, [cdkAccordion]',\n                    exportAs: 'cdkAccordion',\n                    providers: [{ provide: CDK_ACCORDION, useExisting: CdkAccordion }],\n                }]\n        }], propDecorators: { multi: [{\n                type: Input\n            }] } });\n\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n/**\n * An basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n    /** Whether the AccordionItem is expanded. */\n    get expanded() {\n        return this._expanded;\n    }\n    set expanded(expanded) {\n        expanded = coerceBooleanProperty(expanded);\n        // Only emit events and update the internal value if the value changes.\n        if (this._expanded !== expanded) {\n            this._expanded = expanded;\n            this.expandedChange.emit(expanded);\n            if (expanded) {\n                this.opened.emit();\n                /**\n                 * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n                 * the name value is the id of the accordion.\n                 */\n                const accordionId = this.accordion ? this.accordion.id : this.id;\n                this._expansionDispatcher.notify(this.id, accordionId);\n            }\n            else {\n                this.closed.emit();\n            }\n            // Ensures that the animation will run when the value is set outside of an `@Input`.\n            // This includes cases like the open, close and toggle methods.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the AccordionItem is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        this._disabled = coerceBooleanProperty(disabled);\n    }\n    constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n        this.accordion = accordion;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._expansionDispatcher = _expansionDispatcher;\n        /** Subscription to openAll/closeAll events. */\n        this._openCloseAllSubscription = Subscription.EMPTY;\n        /** Event emitted every time the AccordionItem is closed. */\n        this.closed = new EventEmitter();\n        /** Event emitted every time the AccordionItem is opened. */\n        this.opened = new EventEmitter();\n        /** Event emitted when the AccordionItem is destroyed. */\n        this.destroyed = new EventEmitter();\n        /**\n         * Emits whenever the expanded state of the accordion changes.\n         * Primarily used to facilitate two-way binding.\n         * @docs-private\n         */\n        this.expandedChange = new EventEmitter();\n        /** The unique AccordionItem id. */\n        this.id = `cdk-accordion-child-${nextId++}`;\n        this._expanded = false;\n        this._disabled = false;\n        /** Unregister function for _expansionDispatcher. */\n        this._removeUniqueSelectionListener = () => { };\n        this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n            if (this.accordion &&\n                !this.accordion.multi &&\n                this.accordion.id === accordionId &&\n                this.id !== id) {\n                this.expanded = false;\n            }\n        });\n        // When an accordion item is hosted in an accordion, subscribe to open/close events.\n        if (this.accordion) {\n            this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n        }\n    }\n    /** Emits an event for the accordion item being destroyed. */\n    ngOnDestroy() {\n        this.opened.complete();\n        this.closed.complete();\n        this.destroyed.emit();\n        this.destroyed.complete();\n        this._removeUniqueSelectionListener();\n        this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n    toggle() {\n        if (!this.disabled) {\n            this.expanded = !this.expanded;\n        }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n    close() {\n        if (!this.disabled) {\n            this.expanded = false;\n        }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n    open() {\n        if (!this.disabled) {\n            this.expanded = true;\n        }\n    }\n    _subscribeToOpenCloseAllActions() {\n        return this.accordion._openCloseAllActions.subscribe(expanded => {\n            // Only change expanded state if item is enabled\n            if (!this.disabled) {\n                this.expanded = expanded;\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionItem, deps: [{ token: CDK_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkAccordionItem, selector: \"cdk-accordion-item, [cdkAccordionItem]\", inputs: { expanded: \"expanded\", disabled: \"disabled\" }, outputs: { closed: \"closed\", opened: \"opened\", destroyed: \"destroyed\", expandedChange: \"expandedChange\" }, providers: [\n            // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n            // registering to the same accordion.\n            { provide: CDK_ACCORDION, useValue: undefined },\n        ], exportAs: [\"cdkAccordionItem\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionItem, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-accordion-item, [cdkAccordionItem]',\n                    exportAs: 'cdkAccordionItem',\n                    providers: [\n                        // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n                        // registering to the same accordion.\n                        { provide: CDK_ACCORDION, useValue: undefined },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: CdkAccordion, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_ACCORDION]\n                }, {\n                    type: SkipSelf\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }]; }, propDecorators: { closed: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], expandedChange: [{\n                type: Output\n            }], expanded: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\nclass CdkAccordionModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionModule, declarations: [CdkAccordion, CdkAccordionItem], exports: [CdkAccordion, CdkAccordionItem] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkAccordion, CdkAccordionItem],\n                    declarations: [CdkAccordion, CdkAccordionItem],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n", "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport * as i2 from '@angular/cdk/portal';\nimport { TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { mixinTabIndex, MatCommonModule } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { distinctUntilChanged, startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: trigger('indicatorRotate', [\n        state('collapsed, void', style({ transform: 'rotate(0deg)' })),\n        state('expanded', style({ transform: 'rotate(180deg)' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: trigger('bodyExpansion', [\n        state('collapsed, void', style({ height: '0px', visibility: 'hidden' })),\n        // Clear the `visibility` while open, otherwise the content will be visible when placed in\n        // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n        // that have a `visibility` of their own (see #27436).\n        state('expanded', style({ height: '*', visibility: '' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n};\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    constructor(_template, _expansionPanel) {\n        this._template = _template;\n        this._expansionPanel = _expansionPanel;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelContent, deps: [{ token: i0.TemplateRef }, { token: MAT_EXPANSION_PANEL, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanelContent, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = coerceBooleanProperty(value);\n    }\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n        super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n        this._viewContainerRef = _viewContainerRef;\n        this._animationMode = _animationMode;\n        this._hideToggle = false;\n        /** An event emitted after the body's expansion animation happens. */\n        this.afterExpand = new EventEmitter();\n        /** An event emitted after the body's collapse animation happens. */\n        this.afterCollapse = new EventEmitter();\n        /** Stream that emits for changes in `@Input` properties. */\n        this._inputChanges = new Subject();\n        /** ID for the associated header element. Used for a11y labelling. */\n        this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n        /** Stream of body animation done events. */\n        this._bodyAnimationDone = new Subject();\n        this.accordion = accordion;\n        this._document = _document;\n        // We need a Subject with distinctUntilChanged, because the `done` event\n        // fires twice on some browsers. See https://github.com/angular/angular/issues/24084\n        this._bodyAnimationDone\n            .pipe(distinctUntilChanged((x, y) => {\n            return x.fromState === y.fromState && x.toState === y.toState;\n        }))\n            .subscribe(event => {\n            if (event.fromState !== 'void') {\n                if (event.toState === 'expanded') {\n                    this.afterExpand.emit();\n                }\n                else if (event.toState === 'collapsed') {\n                    this.afterCollapse.emit();\n                }\n            }\n        });\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._bodyAnimationDone.complete();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanel, deps: [{ token: MAT_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanel, selector: \"mat-expansion-panel\", inputs: { disabled: \"disabled\", expanded: \"expanded\", hideToggle: \"hideToggle\", togglePosition: \"togglePosition\" }, outputs: { opened: \"opened\", closed: \"closed\", expandedChange: \"expandedChange\", afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n            // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n            // to the same accordion.\n            { provide: MAT_ACCORDION, useValue: undefined },\n            { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n        ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{--mat-expansion-container-shape:4px;box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: i2.CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matExpansionAnimations.bodyExpansion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled', 'expanded'], outputs: ['opened', 'closed', 'expandedChange'], animations: [matExpansionAnimations.bodyExpansion], providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.done)=\\\"_bodyAnimationDone.next($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{--mat-expansion-container-shape:4px;box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [MAT_ACCORDION]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { hideToggle: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }] } });\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanelActionRow, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                }]\n        }] });\n\n// Boilerplate for applying mixins to MatExpansionPanelHeader.\n/** @docs-private */\nclass MatExpansionPanelHeaderBase {\n}\nconst _MatExpansionPanelHeaderMixinBase = mixinTabIndex(MatExpansionPanelHeaderBase);\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader extends _MatExpansionPanelHeaderMixinBase {\n    constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n        super();\n        this.panel = panel;\n        this._element = _element;\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        this._parentChangeSubscription = Subscription.EMPTY;\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelHeader, deps: [{ token: MatExpansionPanel, host: true }, { token: i0.ElementRef }, { token: i2$1.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanelHeader, selector: \"mat-expansion-panel-header\", inputs: { tabIndex: \"tabIndex\", expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\" }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"], dependencies: [{ kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [matExpansionAnimations.indicatorRotate], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['tabIndex'], animations: [matExpansionAnimations.indicatorRotate], host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n<span [@indicatorRotate]=\\\"_getExpandedState()\\\" *ngIf=\\\"_showToggle()\\\"\\n      class=\\\"mat-expansion-indicator\\\"></span>\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"] }]\n        }], ctorParameters: function () { return [{ type: MatExpansionPanel, decorators: [{\n                    type: Host\n                }] }, { type: i0.ElementRef }, { type: i2$1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; }, propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanelDescription, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatExpansionPanelTitle, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                }]\n        }] });\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    constructor() {\n        super(...arguments);\n        /** Headers belonging to this accordion. */\n        this._ownHeaders = new QueryList();\n        this._hideToggle = false;\n        /**\n         * Display mode used for all expansion panels in the accordion. Currently two display\n         * modes exist:\n         *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n         *     panel at a different elevation from the rest of the accordion.\n         *  flat - no spacing is placed around expanded panels, showing all panels at the same\n         *     elevation.\n         */\n        this.displayMode = 'default';\n        /** The position of the expansion indicator. */\n        this.togglePosition = 'after';\n    }\n    /** Whether the expansion indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle;\n    }\n    set hideToggle(show) {\n        this._hideToggle = coerceBooleanProperty(show);\n    }\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._keyManager?.destroy();\n        this._ownHeaders.destroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatAccordion, selector: \"mat-accordion\", inputs: { multi: \"multi\", hideToggle: \"hideToggle\", displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n            {\n                provide: MAT_ACCORDION,\n                useExisting: MatAccordion,\n            },\n        ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    inputs: ['multi'],\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\nclass MatExpansionModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionModule, declarations: [MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent], imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule], exports: [MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionModule, imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, MatCommonModule, CdkAccordionModule, PortalModule],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    declarations: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "names": ["i0", "InjectionToken", "Directive", "Input", "EventEmitter", "Optional", "Inject", "SkipSelf", "Output", "NgModule", "i1", "coerceBooleanProperty", "Subject", "Subscription", "nextId$1", "CDK_ACCORDION", "CdkAccordion", "constructor", "_stateChanges", "_openCloseAllActions", "id", "_multi", "multi", "openAll", "next", "closeAll", "ngOnChanges", "changes", "ngOnDestroy", "complete", "ɵfac", "CdkAccordion_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "nextId", "CdkAccordionItem", "expanded", "_expanded", "expandedChange", "emit", "opened", "accordionId", "accordion", "_expansionDispatcher", "notify", "closed", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "_disabled", "_openCloseAllSubscription", "EMPTY", "destroyed", "_removeUniqueSelectionListener", "listen", "_subscribeToOpenCloseAllActions", "unsubscribe", "toggle", "close", "open", "subscribe", "CdkAccordionItem_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "UniqueSelectionDispatcher", "outputs", "useValue", "undefined", "decorators", "CdkAccordionModule", "CdkAccordionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "declarations", "i2", "TemplatePortal", "PortalModule", "i3", "DOCUMENT", "CommonModule", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChild", "ViewChild", "Host", "Attribute", "QueryList", "ContentChildren", "mixinTabIndex", "MatCommonModule", "i2$1", "FocusKeyManager", "distinctUntilChanged", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "ANIMATION_MODULE_TYPE", "merge", "trigger", "state", "style", "transition", "animate", "_c0", "MatExpansionPanel_ng_template_5_Template", "rf", "ctx", "_c1", "_c2", "MatExpansionPanelHeader_span_4_Template", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "_getExpandedState", "_c3", "_c4", "MAT_ACCORDION", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "transform", "bodyExpansion", "height", "visibility", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "_template", "_expansionPanel", "MatExpansionPanelContent_Factory", "TemplateRef", "uniqueId", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "hideToggle", "_hideToggle", "value", "togglePosition", "_togglePosition", "_uniqueSelectionDispatcher", "_viewContainerRef", "_document", "_animationMode", "defaultOptions", "afterExpand", "afterCollapse", "_inputChanges", "_headerId", "_bodyAnimationDone", "pipe", "x", "y", "fromState", "toState", "event", "_hasSpacing", "displayMode", "ngAfterContentInit", "_lazyContent", "_portal", "_containsFocus", "_body", "focusedElement", "activeElement", "bodyElement", "nativeElement", "contains", "MatExpansionPanel_Factory", "ViewContainerRef", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatExpansionPanel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatExpansionPanel_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatExpansionPanel_HostBindings", "ɵɵclassProp", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatExpansionPanel_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵelementStart", "ɵɵlistener", "MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener", "$event", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵadvance", "ɵɵattribute", "dependencies", "CdkPortalOutlet", "styles", "encapsulation", "data", "animation", "changeDetection", "None", "OnPush", "animations", "host", "MatExpansionPanelActionRow", "MatExpansionPanelActionRow_Factory", "class", "MatExpansionPanelHeaderBase", "_MatExpansionPanelHeaderMixinBase", "MatExpansionPanelHeader", "panel", "_element", "_focusMonitor", "tabIndex", "_parentChangeSubscription", "accordionHideToggleChange", "parseInt", "focusVia", "expandedHeight", "collapsedHeight", "_toggle", "_isExpanded", "_getPanelId", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "stopMonitoring", "MatExpansionPanelHeader_Factory", "ElementRef", "FocusMonitor", "ɵɵinjectAttribute", "MatExpansionPanelHeader_HostBindings", "MatExpansionPanelHeader_click_HostBindingHandler", "MatExpansionPanelHeader_keydown_HostBindingHandler", "ɵɵstyleProp", "MatExpansionPanelHeader_Template", "NgIf", "MatExpansionPanelDescription", "MatExpansionPanelDescription_Factory", "MatExpansionPanelTitle", "MatExpansionPanelTitle_Factory", "Mat<PERSON><PERSON>rdi<PERSON>", "arguments", "_ownHeaders", "show", "_headers", "headers", "reset", "header", "notifyOn<PERSON><PERSON>es", "_keyManager", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "destroy", "ɵMatAccordion_BaseFactory", "MatAccordion_Factory", "ɵɵgetInheritedFactory", "MatAccordion_ContentQueries", "MatAccordion_HostBindings", "descendants", "MatExpansionModule", "MatExpansionModule_Factory", "imports"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}