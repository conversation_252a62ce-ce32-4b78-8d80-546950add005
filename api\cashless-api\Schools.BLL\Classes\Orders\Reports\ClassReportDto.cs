﻿using System.Collections.Generic;

namespace Schools.BLL.Classes.Orders.Reports;

public class ClassReportSchoolDto
{
    public string School { get; set; }
    public string Class { get; set; }
    public List<ClassReportOrderDto> Orders { get; set; }
}

public class ClassReportOrderDto
{
    public long OrderId { get; set; }
    public string StudentName { get; set; }
    public string MenuType { get; set; }
    public string CanteenStatus { get; set; }
    public long RunNumber { get; set; }
    public List<ClassReportItemDto> Items { get; set; }
}

public class ClassReportItemDto
{
    public string Name { get; set; }
    public int Quantity { get; set; }
    public string SelectedOptions { get; set; }
}