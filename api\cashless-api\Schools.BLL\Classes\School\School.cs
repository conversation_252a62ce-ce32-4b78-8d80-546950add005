using System;
using System.Collections.Generic;
using Schools.DAL.Entities;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    public class CreateSchoolRequest
    {
        [JsonProperty(PropertyName = "School")]
        public School School { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public int CanteenId { get; set; }
    }

    public class SchoolWithPrintingSettings : School
    {
        [JsonProperty("Size")]
        public string Size { get; set; }

        [JsonProperty("Printer")]
        public string Printer { get; set; }

        [JsonProperty(PropertyName = "PrintSortBy")]
        public string PrintSortBy { get; set; }

        [JsonProperty(PropertyName = "PrintSortDirection")]
        public string PrintSortDirection { get; set; }
    }

    /// <summary>
    /// DTO to use when getting School - Merchant records
    /// </summary>
    public class GetSchoolBillingDto
    {
        [JsonProperty(PropertyName = "SchoolId")]
        public long SchoolId { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public long CanteenId { get; set; }

        [JsonProperty(PropertyName = "CanteenType")]
        public string CanteenType { get; set; }
    }

    /// <summary>
    /// DTO to use when inserting or updating School - Merchant records
    /// </summary>
    public class UpsertSchoolBillingDto : GetSchoolBillingDto
    {
        [JsonProperty(PropertyName = "BillingStatus")]
        public BillingStatusEnum BillingStatus { get; set; }

        [JsonProperty(PropertyName = "CanteenFee")]
        public decimal CanteenFee { get; set; }

        [JsonProperty(PropertyName = "WaiveEventOrderFee")]
        public Boolean? WaiveEventOrderFee { get; set; }

        [JsonProperty(PropertyName = "StartDate")]
        public DateTime? StartDate { get; set; }

        [JsonProperty(PropertyName = "BillingStartDate")]
        public DateTime? BillingStartDate { get; set; }

        [JsonProperty(PropertyName = "BillingEndDate")]
        public DateTime? BillingEndDate { get; set; }

        [JsonProperty(PropertyName = "SpecialInstructions")]
        public string SpecialInstructions { get; set; }

        [JsonProperty(PropertyName = "CanteenAbsorbsFees")]
        public bool CanteenAbsorbsFees { get; set; }

        [JsonProperty(PropertyName = "InternalStatus")]
        public InternalSchoolMerchantStatusEnum InternalStatus { get; set; }
    }
}
