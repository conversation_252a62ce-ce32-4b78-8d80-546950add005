name: Tag Image To Production

on:
  push:
    tags:
      - 'v*.*.*'
      - '!v*-rc*'

permissions:
  id-token: write
  contents: read

jobs:
  tag-image-to-production:
    runs-on: prod-runner
    timeout-minutes: 20
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set Environment Variables from Environment Config File
        uses: ./.github/actions/setvars
        with:
          varFilePath: ./.github/variables/docker-config.env
      
      - name: Configure Repo Environment Variables
        run: |  
          export SHORT_COMMIT=$(git log -1 --pretty=format:%h)
          echo SHORT_COMMIT=$SHORT_COMMIT >> $GITHUB_ENV
          export REPO_NAME=$ECR_REPO_PREFIX$SERVICE
          echo REPO_NAME=$REPO_NAME >> $GITHUB_ENV
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ env.ECR_AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name:  Update Tag to Production
        run: |
          echo REPO_NAME = $REPO_NAME
          echo SHORT_COMMIT = $SHORT_COMMIT
          echo GITHUB_REF_NAM = $GITHUB_REF_NAME
          echo DATE = $(date -u '+%Y%m%d%H%S')

          export MANIFEST=$(aws ecr batch-get-image --repository-name $REPO_NAME --image-ids imageTag=$SHORT_COMMIT --output json | jq --raw-output --join-output '.images[0].imageManifest')
          for TAG in $GITHUB_REF_NAME $(date -u '+%Y%m%d%H%S'); do
            aws ecr put-image --repository-name $REPO_NAME --image-tag $TAG --image-manifest "$MANIFEST"
          done