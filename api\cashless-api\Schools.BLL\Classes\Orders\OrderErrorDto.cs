﻿using System.ComponentModel.DataAnnotations;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes.Orders;

public class CreateOrderErrorDto
{
    [Required(ErrorMessage = "Type must be supplied")]
    public OrderStatusProcessStepEnum Type { get; set; }

    [Required(ErrorMessage = "Message must be supplied")]
    [MaxLength(200, ErrorMessage = "Message is too long")]
    public string Message { get; set; }

    public string Details { get; set; }

    [Required(ErrorMessage = "CorrelationId must be supplied")]
    [MaxLength(200, ErrorMessage = "CorrelationId is too long")]
    public string CorrelationId { get; set; }
}