<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>ZipDeploy</WebPublishMethod>
    <ResourceId>/subscriptions/e186ad57-92e6-41b0-92d9-a891425405fb/resourceGroups/rg-clc-np-uri/providers/Microsoft.Web/sites/spriggy-api-uat</ResourceId>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://spriggy-api-uat-dyhbhzhebzc3dwa3.australiasoutheast-01.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ProjectGuid>466e5571-37d9-48f1-9515-2c2a259a89fa</ProjectGuid>
    <PublishUrl>https://spriggy-api-uat-dyhbhzhebzc3dwa3.scm.australiasoutheast-01.azurewebsites.net/</PublishUrl>
    <UserName>$spriggy-api-uat</UserName>
    <_SavePWD>true</_SavePWD>
  </PropertyGroup>
</Project>