﻿using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class SchoolPrintOptionsRequest : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "SchoolId")]
        public long? SchoolId { get; set; }

        [JsonProperty(PropertyName = "LabelTypeId")]
        public long? LabelTypeId { get; set; }

        [JsonProperty(PropertyName = "LabelFormat")]
        public string LabelFormat { get; set; }

        [JsonProperty(PropertyName = "LabelPrintChoice")]
        public string LabelPrintChoice { get; set; }

        [JsonProperty(PropertyName = "UsePrintingApp")]
        public bool UsePrintingApp { get; set; }
    }
}
