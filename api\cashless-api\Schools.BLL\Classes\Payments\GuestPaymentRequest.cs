using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Payments;

/// <summary>
/// Request model for guest user payments
/// </summary>
public class GuestPaymentRequest
{
    [Required]
    public string CardNumber { get; set; }

    [Required]
    [Range(1, 12)]
    public int ExpiryMonth { get; set; }

    [Required]
    [Range(2024, 2050)]
    public int ExpiryYear { get; set; }

    [Required]
    [StringLength(4, MinimumLength = 3)]
    public string CVV { get; set; }

    [Required]
    [StringLength(100, MinimumLength = 2)]
    public string CardholderName { get; set; }

    [Required]
    [Range(0.01, 10000)]
    public decimal Amount { get; set; }

    [Required]
    public int CanteenId { get; set; }

    [Required]
    public int GuestUserId { get; set; }

    [Required]
    public List<GuestOrderItem> Items { get; set; }

    public string OrderDate { get; set; }
    public int MenuId { get; set; }
    public string MenuType { get; set; }
}

/// <summary>
/// Order item for guest payments
/// </summary>
public class GuestOrderItem
{
    [Required]
    public int MenuItemId { get; set; }

    [Required]
    [Range(1, 100)]
    public int Quantity { get; set; }

    [Required]
    [Range(0.01, 1000)]
    public decimal Price { get; set; }

    public string ItemName { get; set; }
    public string ItemDescription { get; set; }
}

/// <summary>
/// Response model for guest payment processing
/// </summary>
public class GuestPaymentResponse
{
    public bool IsSuccess { get; set; }
    public string OrderId { get; set; }
    public string Message { get; set; }
    public string ErrorCode { get; set; }
    public string TransactionId { get; set; }
    public DateTime ProcessedAt { get; set; }
    public decimal AmountCharged { get; set; }
    public string PaymentReference { get; set; }
}

/// <summary>
/// Request model for validating guest card details
/// </summary>
public class GuestCardValidationRequest
{
    [Required]
    public string CardNumber { get; set; }

    [Required]
    [Range(1, 12)]
    public int ExpiryMonth { get; set; }

    [Required]
    [Range(2024, 2050)]
    public int ExpiryYear { get; set; }

    [Required]
    [StringLength(4, MinimumLength = 3)]
    public string CVV { get; set; }
}

/// <summary>
/// Response model for card validation
/// </summary>
public class GuestCardValidationResponse
{
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; }
    public string CardType { get; set; }
    public string MaskedCardNumber { get; set; }
}

/// <summary>
/// Request model for checking guest payment status
/// </summary>
public class GuestPaymentStatusRequest
{
    [Required]
    public string OrderId { get; set; }
    
    public string PaymentReference { get; set; }
}

/// <summary>
/// Response model for payment status
/// </summary>
public class GuestPaymentStatusResponse
{
    public string Status { get; set; }
    public string OrderId { get; set; }
    public decimal Amount { get; set; }
    public DateTime ProcessedAt { get; set; }
    public string PaymentReference { get; set; }
    public bool IsCompleted { get; set; }
    public string ErrorMessage { get; set; }
}
