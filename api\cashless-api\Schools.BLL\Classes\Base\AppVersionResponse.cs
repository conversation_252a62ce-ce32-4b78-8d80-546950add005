using System;
using System.Text.Json;

/// <summary>
/// Application version and release date
/// </summary>
public class AppVersionResponse
{
    public string AppName { get; set; }
    public string EnvironmentName { get; set; }
    public string ReleaseName { get; set; }
    public string BuildDate { get; set; }
    public DateTime Date { get; set; }

    /// <summary>
    /// Return a JSON string of this response!
    /// </summary>
    public override string ToString()
    {
        return JsonSerializer.Serialize(this);
    }
}