using System;
using System.Collections.Generic;

namespace Schools.BLL.Classes;

public class EventTypeDto
{
    public long EventTypeId { get; set; }
    public string Type { get; set; }
    public List<EventTemplateDto> Templates { get; set; }
}

public class EventTemplateDto
{
    public long EventTemplateId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public List<EventTemplateItemDto> Items { get; set; }
}

public class EventTemplateItemDto
{
    public string Name { get; set; }
    public string Description { get; set; }
}