import { Component, Inject, OnInit, signal, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';

import { BaseComponent } from '../../../sharedModels';
import { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';
import { CashlessAppInsightsService } from '../../../sharedServices';
import {
  GuestPaymentDialogData,
  GuestPaymentDialogResult,
  GuestPaymentRequest,
  GuestPaymentWithMethodRequest,
  validateCardNumber,
  validateExpiryDate,
  validateCVV,
  detectCardType,
  formatCardNumber
} from '../../../sharedModels/guest-payment/guest-payment.models';

// Stripe imports
import {
  StripeCardElementChangeEvent,
  StripeCardElementOptions,
  StripeElementsOptions
} from '@stripe/stripe-js';
import {
  injectStripe,
  StripeCardComponent,
  StripeElementsDirective
} from 'ngx-stripe';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-guest-payment-stripe-dialog',
  templateUrl: './guest-payment-stripe-dialog.component.html',
  styleUrls: ['./guest-payment-stripe-dialog.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    StripeElementsDirective,
    StripeCardComponent
  ]
})
export class GuestPaymentStripeDialogComponent extends BaseComponent implements OnInit {
  
  @ViewChild(StripeCardComponent) card: StripeCardComponent;
  
  // Stripe setup
  readonly stripe = injectStripe(environment.stripePublicKey);
  
  // Form and state
  paymentForm: FormGroup;
  isProcessing = signal<boolean>(false);
  paymentSuccess = signal<boolean>(false);
  errorMessage = signal<string | null>(null);
  
  // Stripe Elements state
  stripeCardValid = signal<boolean>(false);
  stripeCardComplete = signal<boolean>(false);
  
  // Environment detection
  isTestEnvironment = signal<boolean>(true);
  
  // Legacy form validation states (for test mode)
  cardNumberValid = signal<boolean>(false);
  expiryValid = signal<boolean>(false);
  cvvValid = signal<boolean>(false);
  nameValid = signal<boolean>(false);
  cardType = signal<string>('');
  
  // Available options for legacy form
  expiryMonths = this.guestPaymentService.getExpiryMonths();
  expiryYears = this.guestPaymentService.getExpiryYears();
  
  // Order details
  totalAmount: number;
  orderItems: any[];
  guestUserId: number;
  canteenId: number;
  menuId: number;
  menuType: string;
  orderDate: string;
  
  // Stripe Elements options
  cardOptions: StripeCardElementOptions = {
    hidePostalCode: true,
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    },
  };
  
  elementsOptions: StripeElementsOptions = {
    locale: 'en',
  };

  constructor(
    private fb: FormBuilder,
    private guestPaymentService: GuestPaymentService,
    private appInsightsService: CashlessAppInsightsService,
    public dialogRef: MatDialogRef<GuestPaymentStripeDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: GuestPaymentDialogData
  ) {
    super();
    this.initializeOrderData();
    this.detectEnvironment();
    this.initializeForm();
  }

  ngOnInit(): void {
    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {
      canteenId: this.canteenId.toString(),
      totalAmount: this.totalAmount.toString(),
      environment: this.isTestEnvironment() ? 'test' : 'production'
    });
  }

  private initializeOrderData(): void {
    this.totalAmount = this.data.totalAmount;
    this.orderItems = this.data.orders;
    this.guestUserId = this.data.selectedStudent?.id || 0;
    this.canteenId = this.data.canteenId;
    this.menuId = this.data.menuId;
    this.menuType = this.data.menuType;
    this.orderDate = this.data.orderDate;
  }

  private detectEnvironment(): void {
    // Detect environment based on Stripe public key
    const isTest = environment.stripePublicKey?.startsWith('pk_test_') || false;
    this.isTestEnvironment.set(isTest);
  }

  private initializeForm(): void {
    if (this.isTestEnvironment()) {
      // Legacy form for test environment
      this.paymentForm = this.fb.group({
        cardNumber: ['', [Validators.required]],
        expiryMonth: ['', [Validators.required]],
        expiryYear: ['', [Validators.required]],
        cvv: ['', [Validators.required]],
        cardholderName: ['', [Validators.required, Validators.minLength(2)]]
      });
      
      this.setupLegacyFormValidation();
    } else {
      // Minimal form for production (Stripe Elements handles card details)
      this.paymentForm = this.fb.group({
        cardholderName: ['', [Validators.required, Validators.minLength(2)]]
      });
    }
  }

  private setupLegacyFormValidation(): void {
    // Card number validation
    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {
      if (value) {
        const isValid = validateCardNumber(value);
        this.cardNumberValid.set(isValid);
        this.cardType.set(detectCardType(value));
        
        // Format card number
        const formatted = formatCardNumber(value);
        if (formatted !== value) {
          this.paymentForm.get('cardNumber')?.setValue(formatted, { emitEvent: false });
        }
      } else {
        this.cardNumberValid.set(false);
        this.cardType.set('');
      }
    });

    // Expiry validation
    const validateExpiry = () => {
      const month = parseInt(this.paymentForm.get('expiryMonth')?.value);
      const year = parseInt(this.paymentForm.get('expiryYear')?.value);
      if (month && year) {
        this.expiryValid.set(validateExpiryDate(month, year));
      } else {
        this.expiryValid.set(false);
      }
    };

    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(validateExpiry);
    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(validateExpiry);

    // CVV validation
    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {
      this.cvvValid.set(value ? validateCVV(value) : false);
    });

    // Name validation
    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {
      this.nameValid.set(value ? value.trim().length >= 2 : false);
    });
  }

  // Stripe Elements event handlers
  onStripeCardChange(event: StripeCardElementChangeEvent): void {
    this.stripeCardValid.set(!event.error && event.complete);
    this.stripeCardComplete.set(event.complete);
    
    if (event.error) {
      this.errorMessage.set(event.error.message);
    } else {
      this.errorMessage.set(null);
    }
  }

  isFormValid(): boolean {
    if (this.isTestEnvironment()) {
      return this.paymentForm.valid && 
             this.cardNumberValid() && 
             this.expiryValid() && 
             this.cvvValid() && 
             this.nameValid();
    } else {
      return this.paymentForm.valid && this.stripeCardValid() && this.stripeCardComplete();
    }
  }

  processPayment(): void {
    if (!this.isFormValid() || this.isProcessing()) {
      return;
    }

    this.isProcessing.set(true);
    this.errorMessage.set(null);

    try {
      if (this.isTestEnvironment()) {
        this.processTestPayment();
      } else {
        this.processProductionPayment();
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      this.errorMessage.set('An unexpected error occurred. Please try again.');
      this.isProcessing.set(false);
    }
  }

  private processTestPayment(): void {
    const formValue = this.paymentForm.value;
    
    const request: GuestPaymentRequest = {
      cardNumber: formValue.cardNumber.replace(/\s/g, ''),
      expiryMonth: parseInt(formValue.expiryMonth),
      expiryYear: parseInt(formValue.expiryYear),
      cvv: formValue.cvv,
      cardholderName: formValue.cardholderName,
      amount: this.totalAmount,
      canteenId: this.canteenId,
      guestUserId: this.guestUserId,
      items: this.orderItems.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        price: item.price,
        itemName: item.name || item.itemName,
        itemDescription: item.description || ''
      })),
      orderDate: this.orderDate,
      menuId: this.menuId,
      menuType: this.menuType
    };

    this.guestPaymentService.processPaymentAndCreateOrder(request).subscribe({
      next: (response) => {
        this.handlePaymentResponse(response);
      },
      error: (error) => {
        this.handlePaymentError(error);
      }
    });
  }

  private processProductionPayment(): void {
    // Create payment method using Stripe Elements
    this.stripe.createPaymentMethod({
      type: 'card',
      card: this.card.element,
      billing_details: {
        name: this.paymentForm.value.cardholderName,
      },
    }).subscribe({
      next: (result) => {
        if (result.error) {
          this.errorMessage.set(result.error.message || 'Failed to create payment method');
          this.isProcessing.set(false);
          return;
        }

        const paymentMethod = result.paymentMethod;

        // Process payment with the backend
        const request: GuestPaymentWithMethodRequest = {
          paymentMethodId: paymentMethod.id,
          amount: this.totalAmount,
          canteenId: this.canteenId,
          guestUserId: this.guestUserId,
          items: this.orderItems.map(item => ({
            menuItemId: item.menuItemId,
            quantity: item.quantity,
            price: item.price,
            itemName: item.name || item.itemName,
            itemDescription: item.description || ''
          })),
          orderDate: this.orderDate,
          menuId: this.menuId,
          menuType: this.menuType
        };

        this.guestPaymentService.processPaymentWithMethodAndCreateOrder(request).subscribe({
          next: (response) => {
            this.handlePaymentResponse(response);
          },
          error: (error) => {
            this.handlePaymentError(error);
          }
        });
      },
      error: (error) => {
        console.error('Stripe payment method creation error:', error);
        this.errorMessage.set('Failed to process payment. Please try again.');
        this.isProcessing.set(false);
      }
    });
  }

  private handlePaymentResponse(response: any): void {
    this.isProcessing.set(false);
    
    if (response.success || response.isSuccess) {
      this.paymentSuccess.set(true);
      
      this.appInsightsService.TrackEvent('GuestPaymentSuccess', {
        orderId: response.orderId,
        transactionId: response.transactionId,
        amount: this.totalAmount.toString(),
        environment: this.isTestEnvironment() ? 'test' : 'production'
      });

      // Close dialog after 2 seconds
      setTimeout(() => {
        this.dialogRef.close({
          success: true,
          orderId: response.orderId,
          message: response.message
        } as GuestPaymentDialogResult);
      }, 2000);
    } else {
      this.errorMessage.set(response.error || response.message || 'Payment failed');
      
      this.appInsightsService.TrackEvent('GuestPaymentFailed', {
        error: response.error || response.message,
        amount: this.totalAmount.toString(),
        environment: this.isTestEnvironment() ? 'test' : 'production'
      });
    }
  }

  private handlePaymentError(error: any): void {
    this.isProcessing.set(false);
    console.error('Payment error:', error);
    
    const errorMessage = error.error?.error || error.message || 'Payment processing failed';
    this.errorMessage.set(errorMessage);
    
    this.appInsightsService.TrackEvent('GuestPaymentError', {
      error: errorMessage,
      amount: this.totalAmount.toString(),
      environment: this.isTestEnvironment() ? 'test' : 'production'
    });
  }

  closeDialog(): void {
    this.dialogRef.close({
      success: false
    } as GuestPaymentDialogResult);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  // Legacy form helper methods
  getCardTypeClass(): string {
    const type = this.cardType().toLowerCase();
    return `card-type-${type}`;
  }

  getCardTypeIcon(): string {
    switch (this.cardType()) {
      case 'Visa': return 'credit_card';
      case 'Mastercard': return 'credit_card';
      case 'American Express': return 'credit_card';
      default: return 'credit_card';
    }
  }
}
