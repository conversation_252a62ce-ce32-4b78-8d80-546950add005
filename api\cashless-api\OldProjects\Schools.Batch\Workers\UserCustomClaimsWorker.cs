using System;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Schools.Batch.Helpers;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Interfaces;

namespace Schools.Batch.Workers;

/// <summary>
/// Batch service to update User's custom claims in Firebase
/// </summary>
public class UserCustomClaimsWorker : BackgroundService
{
    // Parameters
    private const string SettingGroup = "UserCustomClaims";
    private const string SettingName = "UserId";

    // Services
    private readonly IHost host;
    private readonly IConfigService configService;
    private readonly IWorkerHelper workerHelper;
    private readonly IUserRepository userRepository;
    private readonly IFirebaseService firebaseService;
    private readonly ILogger<UserCustomClaimsWorker> logger;

    // Properties
    private int BatchSize;
    private int NumRecords;

    public UserCustomClaimsWorker(IHost host, IConfigService configService, IWorkerHelper workerHelper, IUserRepository userRepository,
                        IFirebaseService firebaseService, ILogger<UserCustomClaimsWorker> logger)
    {
        this.host = host;
        this.configService = configService;
        this.workerHelper = workerHelper;
        this.userRepository = userRepository;
        this.firebaseService = firebaseService;
        this.logger = logger;

        // Load parameters from config
        this.BatchSize = this.configService.GetInt("Workers:UserCustomClaims:BatchSize");
        this.NumRecords = this.configService.GetInt("Workers:UserCustomClaims:NumRecords");
        this.workerHelper.Init(SettingGroup, SettingName);
    }

    /// <summary>
    /// Do the work required and stop the console app once done
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await ProcessUsers();

        await this.host.StopAsync();
    }

    /// <summary>
    /// Look for all Users in the Schools DB and update their custom claims in Firebase
    /// </summary>
    private async Task ProcessUsers()
    {
        var stopwatch = Stopwatch.StartNew();
        var processed = 0;
        var success = 0;

        // Fetch the ID of the last updated User from tblSettings
        var minUserId = await this.workerHelper.GetLastUserId();

        // Fetch all Users with ID > last updated User
        var users = await this.userRepository.GetUsersFromId(minUserId, this.NumRecords);

        this.logger.LogInformation("Processing started - Users: {Users}", users.Count());

        // For each User fetched:
        //  1. Update the User's custom claims in Firebase
        //  2. Update the last Updated User ID in tblSettings
        foreach (var user in users)
        {
            processed++;

            if (await UpdateCustomClaims(user))
            {
                success++;
            }

            if (processed % this.BatchSize == 0)
            {
                this.logger.LogInformation("Processing update - Success: {Success}, Failure: {Failure}, Total: {Processed}, Duration: {Duration:0.00}s",
                                            success, workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);

                await this.workerHelper.SetLastUserId(user.UserId);
            }
        }

        if (users.Any())
        {
            await this.workerHelper.SetLastUserId(users.Last().UserId);
        }

        await workerHelper.ShowErrors();

        this.logger.LogInformation("Processing completed - Success: {Success}, Failure: {Failure}, Total: {Processed}, Duration: {Duration:0.00}s",
                                    success, workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);
    }

    /// <summary>
    /// Update the Firebase custom claims for the given User
    /// </summary>
    private async Task<bool> UpdateCustomClaims(User user)
    {
        try
        {
            await this.firebaseService.UpdateUserCustomClaims(user);

            this.logger.LogDebug("Updated custom claims for User: {User}", user);

            return true;
        }
        catch (Exception ex)
        {
            this.logger.LogError(ex, "Failed to update custom claims for User: {User}", user);

            workerHelper.AddError(user, ex);

            return false;
        }
    }
}
