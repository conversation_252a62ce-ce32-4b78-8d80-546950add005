using System.Text;

namespace Schools.Orders.Extensions;

public static class StringBuilderExtensions
{
    /// <summary>
    /// Format a field for display
    /// </summary>
    public static StringBuilder AddField(this StringBuilder sb, string name, object value)
    {
        if (value.GetType() == typeof(string))
        {
            if (value == null)
            {
                return sb.Append(", " + name + ": " + value);
            }

            return sb.Append(", " + name + ": \"" + value + "\"");
        }

        return sb.Append(", " + name + ": " + value);
    }
}
