# Actual Keda object that creates hpa config based off prometheus metrics
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: schools-api
  namespace: default
spec:
  minReplicaCount: 3  # Optional. Default: 0
  maxReplicaCount: 10 # Optional. Default: 100
  fallback:                                                 # Optional. Section to specify fallback options
    failureThreshold: 4                                     # Mandatory if fallback section is included
    replicas: 6                                             # Mandatory if fallback section is included