using System;
using System.Net.Http;
using System.Threading.Tasks;
using Schools.BLL.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Schools.Orders.Services;

public class SchoolsApiClient
{
    // Content-Type header values
    protected const string ContentTypeJson = "application/json";

    public static string ApimKey { get; set; }
    public static string CashlessApiSecret { get; set; }
    public static string BaseUrl { get; set; }
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _config;
    private readonly ILogger<SchoolsApiClient> _logger;

    public SchoolsApiClient(IHttpClientFactory httpClientFactory, IConfiguration config, ILogger<SchoolsApiClient> logger)
    {
        _httpClientFactory = httpClientFactory;
        _config = config;
        _logger = logger;
    }

    protected Task<HttpClient> GetHttpClient(string callingApiName)
    {
        var client = _httpClientFactory.CreateClient(callingApiName);

        if (string.IsNullOrEmpty(BaseUrl))
        {
            _logger.LogError($"{callingApiName} base url is null or empty");
            throw new OrderFunctionException("Invalid url");
        }

        client.BaseAddress = new Uri(BaseUrl);

        if (string.IsNullOrEmpty(ApimKey))
        {
            _logger.LogError(message: "gateway key is null or empty");
            throw new OrderFunctionException(message: "Invalid API Gateway Key");
        }

        client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", ApimKey);

        if (string.IsNullOrEmpty(CashlessApiSecret))
        {
            _logger.LogError(message: "Cashless api secret is null or empty");
            throw new OrderFunctionException(message: "Invalid cashless api secret");
        }

        client.DefaultRequestHeaders.Add("cashless-api-secret", CashlessApiSecret);

        return Task.FromResult(client);
    }
}