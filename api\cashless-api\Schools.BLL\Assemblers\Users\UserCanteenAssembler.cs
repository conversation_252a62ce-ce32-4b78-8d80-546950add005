using System;
using Schools.BLL.Classes.Canteens;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public class UserCanteenAssembler
{
    public UserCanteenEntity Convert_request_To_UserCanteenEntity(long userId, long canteenId, MerchantPermission permission)
    {
        UserCanteenEntity entity = new()
        {
            UserId = userId,
            CanteenId = canteenId,
            IsAdmin = Convert.ToBoolean(permission.IsAdmin),
            IsEditEventManagementAvailable = Convert.ToBoolean(permission.IsEditEventManagementAvailable),
            IsEventManagementAvailable = Convert.ToBoolean(permission.IsEventManagementAvailable),
            IsMenuEditorAvailable = Convert.ToBoolean(permission.IsMenuEditorAvailable),
            IsOrdersNotPrintedReportsAvailable = Convert.ToBoolean(permission.IsOrdersNotPrintedReportsAvailable),
            IsSaleReportsAvailable = Convert.ToBoolean(permission.IsSaleReportsAvailable),
            NotifyOrdersNotPrinted = Convert.ToBoolean(permission.NotifyOrdersNotPrinted)
        };

        return entity;
    }

    public UserCanteenEntity Convert_request_To_UserCanteenEntity(CanteenUserSettingsRequest request, bool isAdmin)
    {
        UserCanteenEntity entity = new()
        {
            UserId = request.UserId,
            CanteenId = request.CanteenId,
            IsAdmin = isAdmin,
            IsEditEventManagementAvailable = Convert.ToBoolean(request.IsEditEventManagementAvailable),
            IsEventManagementAvailable = Convert.ToBoolean(request.IsEventManagementAvailable),
            IsMenuEditorAvailable = Convert.ToBoolean(request.IsMenuEditorAvailable),
            IsOrdersNotPrintedReportsAvailable = Convert.ToBoolean(request.IsOrdersNotPrintedReportsAvailable),
            IsSaleReportsAvailable = Convert.ToBoolean(request.IsSaleReportsAvailable),
            NotifyOrdersNotPrinted = Convert.ToBoolean(request.NotifyOrdersNotPrinted)
        };

        return entity;
    }
}