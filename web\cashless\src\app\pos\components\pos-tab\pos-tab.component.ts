import { Component, OnInit, OnD<PERSON>roy, signal, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';

// Ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import * as cartSelectors from '../../../states/shoppingCart/shopping-cart.selectors';
import * as cartActions from '../../../states/shoppingCart/shopping-cart.actions';
import { FamilyState } from '../../../states';

// POS Communication
import { PosCommunicationService } from '../../services/pos-communication.service';
import {
  PosMessage,
  PosMessageType,
  OrderPlacedPayload,
  StudentSelectedPayload,
  MenuChangedPayload,
  CartUpdatedPayload,
  ViewRefreshPayload,
  ItemPopupOpenedPayload,
  ItemPopupClosedPayload,
  CategoryChangedPayload,
  BalanceUpdatedPayload,
  CartClearedPayload
} from '../../models/pos-messages.interface';

// Models
import {
  Menu,
  BaseComponent,
  Category,
  MenuTypeEnum,
  UserCashless,
  OrderFilterRequest,
  CheckIfOrderExist,
  CartItem,
  MenuPickerData,
  MenuNamesResponse,
  ImageUrlEnum,
  RefinedOrderItem,
  CartOption,
  ArrayFilter,
} from 'src/app/sharedModels';
import { environment } from 'src/environments/environment';
import * as moment from 'moment';

// Services
import {
  ItemsFilterService,
  OrderApiService,
  DebounceService,
  MenuService,
  UserService,
  AuthService,
} from 'src/app/sharedServices';

// Components
import { PosPlaceOrderDialogComponent } from '../pos-place-order-dialog/pos-place-order-dialog.component';
import { GuestPaymentDialogComponent } from '../guest-payment-dialog/guest-payment-dialog.component';
import { StudentSearchDropdownComponent } from '../student-search-dropdown/student-search-dropdown.component';
import { ConvertToUniversalDateFormat } from 'src/app/utility';
import { GetCartItemsPrice } from '../../../manage-order/functions/calculate-price';

// Guest Payment Models
import {
  GuestPaymentDialogData,
  GuestPaymentDialogResult
} from '../../../sharedModels/guest-payment/guest-payment.models';

@Component({
  selector: 'app-pos-tab',
  templateUrl: './pos-tab.component.html',
  styleUrls: ['./pos-tab.component.scss'],
})
export class PosTabComponent extends BaseComponent implements OnInit, OnDestroy {
  // ViewChild references
  @ViewChild(StudentSearchDropdownComponent) studentSearchDropdown: StudentSearchDropdownComponent;

  // Route parameters
  schoolId: number;
  guid: string;
  viewType: 'merchant' | 'student';

  // Component state
  selectedStudent: UserCashless | null = null;
  selectedMenuType: string = MenuTypeEnum.Recess; // Default to recess
  selectedOrderDate: Date = new Date();
  menuName: string = '';
  menuId: number;
  menuLoading = signal<boolean>(false);
  GENERIC_ERROR_MESSAGE = 'Something went wrong, Please try again';
  IsAdminOrMerchant: boolean = true; // Always true for POS

  // Menu and display
  titlePage = signal<string>('POS - Point of Sale');
  currentMenu: Menu;
  private currentCategory: Category;
  currentCategoryToDisplay: Category;
  showMobilePlaceOrder: boolean = false;
  priceCart = signal<number>(0);
  menuTypeEnum = MenuTypeEnum;
  deactivatedFilters: string;
  menuCutOffTime: string;
  noMenuMessage = null;
  shoppingCart: CartItem[] = [];

  // Form controls
  menuPickerData: MenuPickerData[] = [];
  listDays: Date[] = [];
  orderingForm: FormGroup;

  // Subscriptions
  private subscriptionShoppingCart$: Subscription;
  private subscriptionItemsFilters$: Subscription;
  private subscriptionCrossTabMessages$: Subscription;

  // Cross-tab communication
  isConnectedToOtherTabs = signal<boolean>(false);
  lastSyncTime = signal<Date | null>(null);
  showMenuInStudentView = signal<boolean>(false);

  // Image error handling
  logoImageError = signal<boolean>(false);
  defaultImagePath: string = 'assets/images/spriggy-default-image.png';

  // Student view popup state
  showStudentPopup = signal<boolean>(false);
  studentPopupData = signal<any>(null);

  // Cart synchronization state
  cartSyncInProgress = signal<boolean>(false);
  lastCartSyncTime = signal<string>('');

  // Balance state
  parentBalance = signal<number | null>(null);
  balanceLoading = signal<boolean>(false);
  balanceError = signal<string | null>(null);

  // Favorite color confirmation state
  showColorConfirmation = signal<boolean>(false);
  studentFavoriteColor = signal<string | null>(null);
  colorConfirmationPending = signal<boolean>(false);

  // Payment method selection - Only Spriggy is functional
  selectedPaymentMethod = signal<string>('spriggy');
  showMobilePaymentSelector = signal<boolean>(false);
  paymentMethods = [
    {
      id: 'spriggy',
      name: 'Pay with Spriggy Card / Wallet',
      icon: 'credit_card',
      enabled: true,
      primary: true
    }
    // Note: Other payment methods (Stripe, Cash, Apple Pay, Visa) are disabled
    // as they are not functional in the current system. Only Spriggy payment
    // works with the existing canteen/order/place API infrastructure.
  ];

  constructor(
    private route: ActivatedRoute,
    private location: Location,
    private store: Store<{ family: FamilyState }>,
    private itemsFiltersService: ItemsFilterService,
    private orderApiService: OrderApiService,
    private debounceService: DebounceService,
    private menuService: MenuService,
    private dialog: MatDialog,
    private posCommunicationService: PosCommunicationService,
    private userService: UserService,
    private authService: AuthService
  ) {
    super();
  }

  async ngOnInit() {
    this.initializeFromRoute();
    this.setupSubscriptions();
    this.setupCrossTabCommunication();
    this.initializeForms();
    this.loadMenuNames();
    this.generateDatesList();

    // Only merchant view should load guest user automatically
    // Student view should wait for cross-tab communication from merchant view
    if (this.viewType === 'merchant') {
      console.log(`[POS] Merchant view - loading default guest user`);
      await this.checkAuthenticationAndLoadGuestUser();
    } else {
      console.log(`[POS] Student view - waiting for guest user selection from merchant view`);
      // Student view will receive guest user via handleStudentSelectedMessage()
    }
  }

  ngOnDestroy() {
    this.unsubscribeAll();
  }

  private initializeFromRoute(): void {
    this.route.queryParams.subscribe(params => {
      this.schoolId = +params['schoolId'] || 52243;
      this.guid = params['guid'];
      this.viewType = params['viewType'] || 'merchant';
    });
  }

  private setupSubscriptions(): void {
    // Shopping cart subscription
    this.subscriptionShoppingCart$ = this.store
      .pipe(select(cartSelectors.getCartItems))
      .subscribe((cartItems: CartItem[]) => {
        console.log(`[POS] Cart items updated:`, cartItems);
        this.showMobilePlaceOrder = cartItems.length > 0;
        this.priceCart.set(GetCartItemsPrice(cartItems));
        this.shoppingCart = cartItems;

        // Send cart update message to other tabs (only from merchant view)
        if (this.viewType === 'merchant' && this.selectedStudent) {
          this.sendCartUpdateMessage(cartItems);
        }
      });

    // Filters subscription - simplified for POS
    this.subscriptionItemsFilters$ = this.itemsFiltersService.filtersUpdatedEvent$.subscribe(() => {
      this.FilterItems();
    });
  }

  private setupCrossTabCommunication(): void {
    console.log(`[POS] Setting up cross-tab communication for ${this.viewType} view with GUID: ${this.guid}`);

    // Subscribe to connection status
    this.posCommunicationService.getConnectionStatus().subscribe(isConnected => {
      this.isConnectedToOtherTabs.set(isConnected);
      console.log(`[POS] Connection status changed: ${isConnected}`);
    });

    // Subscribe to messages for this view type
    this.subscriptionCrossTabMessages$ = this.posCommunicationService
      .onMessageForViewType(this.viewType)
      .subscribe((message: PosMessage) => {
        console.log(`[POS] Received message for ${this.viewType} view:`, message);
        this.handleCrossTabMessage(message);
      });
  }

  private handleCrossTabMessage(message: PosMessage): void {
    // Ignore messages from the same tab
    if (message.sourceGuid === this.guid) {
      return;
    }

    this.lastSyncTime.set(new Date());

    switch (message.type) {
      case PosMessageType.ORDER_PLACED:
        this.handleOrderPlacedMessage(message.payload as OrderPlacedPayload);
        break;
      case PosMessageType.STUDENT_SELECTED:
        this.handleStudentSelectedMessage(message.payload as StudentSelectedPayload);
        break;
      case PosMessageType.MENU_CHANGED:
        this.handleMenuChangedMessage(message.payload as MenuChangedPayload);
        break;
      case PosMessageType.CART_UPDATED:
        this.handleCartUpdatedMessage(message.payload as CartUpdatedPayload);
        break;
      case PosMessageType.CART_CLEARED:
        this.handleCartClearedMessage(message.payload as CartClearedPayload);
        break;
      case PosMessageType.VIEW_REFRESH:
        this.handleViewRefreshMessage(message.payload as ViewRefreshPayload);
        break;
      case PosMessageType.ITEM_POPUP_OPENED:
        this.handleItemPopupOpenedMessage(message.payload as ItemPopupOpenedPayload);
        break;
      case PosMessageType.ITEM_POPUP_CLOSED:
        this.handleItemPopupClosedMessage(message.payload as ItemPopupClosedPayload);
        break;
      case PosMessageType.CATEGORY_CHANGED:
        this.handleCategoryChangedMessage(message.payload as CategoryChangedPayload);
        break;
      case PosMessageType.BALANCE_UPDATED:
        this.handleBalanceUpdatedMessage(message.payload as BalanceUpdatedPayload);
        break;
    }
  }

  private initializeForms(): void {
    this.orderingForm = new FormGroup({
      menuType: new FormControl(this.selectedMenuType, [Validators.required]),
      orderDate: new FormControl(this.selectedOrderDate, [Validators.required]),
    });

    // Subscribe to form changes
    this.orderingForm.get('menuType')?.valueChanges.subscribe(value => {
      this.selectedMenuType = value;
      this.loadMenuDebounce();

      // Send menu change message to other tabs (only from merchant view)
      if (this.viewType === 'merchant') {
        this.sendMenuChangedMessage();
      }
    });

    this.orderingForm.get('orderDate')?.valueChanges.subscribe(value => {
      this.selectedOrderDate = value;
      this.loadMenuDebounce();

      // Send menu change message to other tabs (only from merchant view)
      if (this.viewType === 'merchant') {
        this.sendMenuChangedMessage();
      }
    });
  }

  onStudentSelected(student: UserCashless): void {
    console.log(`[POS] Student selected:`, student);

    // Clear cart when changing students (only in merchant view)
    if (this.viewType === 'merchant' && this.selectedStudent && student &&
      this.selectedStudent.UserId !== student.UserId) {
      console.log(`[POS] Clearing cart due to student change from ${this.selectedStudent.FirstName} to ${student.FirstName}`);
      this.store.dispatch(cartActions.clearCart());

      // Send cart cleared message to student view
      this.sendCartClearedMessage('student_changed', this.selectedStudent.UserId, student.UserId);
    }

    this.selectedStudent = student;
    if (student) {
      // Set IsGuest property immediately based on student data
      this.IsGuest = student.IsGuest || false;
      console.log(`[POS] Student selected - IsGuest: ${this.IsGuest}`);

      // Only Spriggy payment is supported - guest users cannot place orders
      if (this.IsGuest) {
        console.log(`[POS] Guest user selected - guest users cannot place orders in POS system`);
        // Keep Spriggy selected but orders will be blocked for guest users
      }

      // Ensure Spriggy is always selected (only functional payment method)
      this.selectedPaymentMethod.set('spriggy');

      this.deactivatedFilters = student.SchoolDeactivatedFilters || null;
      this.loadMenuDebounce();

      // Call GetUsersDetails API first, then load balance (only for non-guest users)
      if (!this.IsGuest) {

      }
      this.loadUserDetailsAndBalance(student);
      //  else {
      //   // For guest users, clear balance information
      //   this.parentBalance.set(null);
      //   this.balanceLoading.set(false);
      //   this.balanceError.set(null);
      //   console.log(`[POS] Guest user selected - skipping balance loading`);
      // }
    } else {
      // Clear menu, categories, filters, and cart when no student selected
      this.currentMenu = null;
      this.currentCategory = null;
      this.currentCategoryToDisplay = null;
      this.noMenuMessage = null;
      this.parentBalance.set(null);
      this.balanceError.set(null);
      this.IsGuest = false;

      // Clear color confirmation dialog
      this.showColorConfirmation.set(false);
      this.studentFavoriteColor.set(null);
      this.colorConfirmationPending.set(false);

      // Clear any active filter states
      this.deactivatedFilters = null;

      console.log(`[POS] No student selected - clearing all UI elements and filters`);

      // Clear cart when no student is selected (only in merchant view)
      if (this.viewType === 'merchant') {
        console.log(`[POS] Clearing cart due to no student selected`);
        this.store.dispatch(cartActions.clearCart());

        // Send cart cleared message to student view
        this.sendCartClearedMessage('no_student_selected');
      }
    }

    // Send student selection message to other tabs (only from merchant view)
    if (this.viewType === 'merchant') {
      this.sendStudentSelectedMessage(student);
    }
  }

  loadMenuNames(): void {
    this.menuService.GetMenuNamesList(this.schoolId).subscribe({
      next: (menuList: MenuNamesResponse[]) => {
        this.processMenuData(menuList);
      },
      error: error => {
        this.handleErrorFromService(error);
      },
    });
  }

  processMenuData(menuList: MenuNamesResponse[]): void {
    this.menuPickerData = [];
    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Recess));
    this.menuPickerData.push(this.getMenuData(menuList, MenuTypeEnum.Lunch));

    // Set default menu name
    const defaultMenu = this.menuPickerData.find(m => m.menuType === this.selectedMenuType);
    if (defaultMenu) {
      this.menuName = defaultMenu.menuName;
    }
  }

  private getMenuData(menuList: MenuNamesResponse[], menuType: string): MenuPickerData {
    const menuIndex = this.getMenuIndex(menuList, menuType);
    return {
      menuType: menuType,
      menuName: menuIndex >= 0 ? menuList[menuIndex].friendlyName : menuType,
    };
  }

  private getMenuIndex(menuList: MenuNamesResponse[], menuType: string): number {
    if (!menuList?.length || !menuType) return -1;
    return menuList?.findIndex(menu => menu.menuType === menuType);
  }

  generateDatesList(): void {
    this.listDays = [];
    const today = new Date();

    // Generate next 14 days
    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      this.listDays.push(date);
    }
  }

  loadMenuDebounce = this.debounceService.callDebounce(this.refreshMenu, 350, false, true);

  refreshMenu(): void {
    if (!this.selectedMenuType || !this.selectedStudent || !this.selectedOrderDate) {
      return;
    }
    this.menuLoading.set(true);
    this.currentMenu = null;
    if (this.IsGuest) {
      this.loadMenu();
      return;
    }
    this.canteenMenuAvailableCheck();

  }

  canteenMenuAvailableCheck(): void {
    const request: OrderFilterRequest = {
      studentId: this.selectedStudent.UserId,
      orderDate: ConvertToUniversalDateFormat(this.selectedOrderDate),
      menuType: this.selectedMenuType,
    };

    this.orderApiService.GetOrderByStudentOrderDateAndMenuType(request).subscribe({
      next: (res: CheckIfOrderExist) => {
        this.processPreMenuCheck(res);
      },
      error: error => {
        this.menuLoading.set(false);
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.handleErrorFromService(error);
      },
    });
  }

  processPreMenuCheck(res: CheckIfOrderExist): void {
    if (!res) {
      this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
      this.menuLoading.set(false);
      return;
    }

    if (this.isSchoolClosedForCanteenOrders(res)) {
      this.setNoMenuMessage(`Sorry, the canteen's closed right now`);
      return;
    }

    if (this.orderAlreadyPlaced(res)) {
      const message = `You have already placed an Order for: ${this.menuName} - ${this.getFormattedDate()}`;
      this.setNoMenuMessage(message);
      return;
    }
    this.loadMenu();
  }

  private isSchoolClosedForCanteenOrders(res: CheckIfOrderExist): boolean {
    return res?.isSchoolClosed;
  }

  private orderAlreadyPlaced(res: CheckIfOrderExist): boolean {
    return Boolean(res?.order);
  }

  private setNoMenuMessage(message: string): void {
    this.noMenuMessage = message;
    this.menuLoading.set(false);
  }

  private getFormattedDate(): string {
    return moment(this.selectedOrderDate).format('dddd DD/MM');
  }

  private loadMenu(): void {
    if (!this.selectedMenuType || !this.selectedStudent) {
      return;
    }

    this.menuService.GetMenuBySchoolAndType(this.selectedStudent.SchoolId, this.selectedMenuType).subscribe({
      next: (res: Menu[]) => {
        this.menuLoading.set(false);
        const menuToDisplay = this.menuDataExists(res) ? this.getMenuToDisplay(res) : null;
        if (menuToDisplay && menuToDisplay?.MenuJSON) {
          this.processMenuResult(menuToDisplay);
          return;
        }
        this.noMenuMessage = `No ${this.menuName} Menu Available`;
      },
      error: error => {
        this.noMenuMessage = this.GENERIC_ERROR_MESSAGE;
        this.menuLoading.set(false);
        this.handleErrorFromService(error);
      },
    });
  }

  private menuDataExists(res: Menu[]): boolean {
    return res && res.length > 0;
  }

  private getMenuToDisplay(res: Menu[]): Menu {
    return res[0];
  }

  processMenuResult(menuResult: Menu): void {
    this.noMenuMessage = null;
    this.currentMenu = menuResult;

    // Set menuId and menuName from the loaded menu
    this.menuId = this.currentMenu.MenuId;
    this.menuName = this.currentMenu.Name;

    this.menuCutOffTime = this.getMenuCutOffTime();

    console.log(`[POS] Menu loaded - ID: ${this.menuId}, Name: ${this.menuName}`);

    // Automatically select the first category
    if (this.currentMenu.MenuJSON && this.currentMenu.MenuJSON.length > 0) {
      this.SetCategory(this.currentMenu.MenuJSON[0]);
    }
  }

  private getMenuCutOffTime(): string {
    // Implementation for cut off time logic
    return '';
  }

  SetCategory(category: Category): void {
    this.currentCategory = category;
    this.currentCategoryToDisplay = category;
    this.FilterItems();

    // Send category change message to other tabs (only from merchant view)
    if (this.viewType === 'merchant') {
      this.sendCategoryChangedMessage(category);
    }
  }

  IsCurrentCategory(cat: Category): boolean {
    return this.currentCategory?.CatName == cat.CatName;
  }

  FilterItems(): void {
    if (!this.currentCategory) {
      return;
    }
    // Apply filters to items - simplified for POS
    this.currentCategoryToDisplay = { ...this.currentCategory };
  }

  AddToCart(item: RefinedOrderItem): void {
    console.log(`[POS] Adding item to cart in ${this.viewType} view:`, item);

    try {
      // Convert RefinedOrderItem to CartItem
      const cartItem: CartItem = this.convertOrderItemToCartItem(item);
      console.log(`[POS] Converted cart item:`, cartItem);

      this.store.dispatch(cartActions.addToCart({ cartItem }));
      console.log(`[POS] Cart action dispatched successfully`);
    } catch (error) {
      console.error(`[POS] Error adding item to cart:`, error);
    }
  }

  // Handle item dialog events for cross-tab communication
  onItemDialogOpened(event: any): void {
    if (this.viewType === 'merchant') {
      console.log(`[POS] Item dialog opened in merchant view:`, event);
      this.sendItemPopupOpenedMessage(event.item, event.category);
    }
  }

  onItemDialogClosed(event: any): void {
    if (this.viewType === 'merchant') {
      console.log(`[POS] Item dialog closed in merchant view:`, event);
      this.sendItemPopupClosedMessage(event.itemAdded, event.item);
    }
  }



  removeFromCart(itemCartId: number): void {
    console.log(`[POS] Removing item from cart: ${itemCartId}`);
    this.store.dispatch(cartActions.removeItem({ itemCartId }));
    // Cart update message will be sent automatically via the cart subscription
  }

  // Convert RefinedOrderItem to CartItem for POS system
  convertOrderItemToCartItem(item: RefinedOrderItem): CartItem {
    if (!this.selectedStudent) {
      throw new Error('No student selected for cart item');
    }

    const options: CartOption[] = item?.SelectedOptions?.map(option => {
      return {
        menuItemOptionId: option.MenuItemOptionId,
        optionName: option.OptionName,
        optionCost: option.OptionCost,
        parentOptionId: option.MenuItemOptionsCategoryId,
      };
    }) || [];

    const cartItem = {
      date: this.selectedOrderDate,
      studentId: this.selectedStudent.UserId,
      studentName: this.selectedStudent.FirstName,
      schoolId: this.selectedStudent.SchoolId,
      menuType: this.selectedMenuType,
      menuName: this.menuName,
      menuId: this.menuId,
      menuCutOffDateTime: this.menuCutOffTime,
      canteenId: this.currentMenu?.CanteenId || 0,
      itemCartId: moment().unix(),
      menuItemId: item.MenuItemId,
      name: item.Name,
      itemPriceIncGst: item.ItemPriceIncGst,
      selectedOptions: options,
      quantity: item.Quantity,
    };

    console.log(`[POS] Created cart item with menuId: ${cartItem.menuId}, menuName: ${cartItem.menuName}`);
    return cartItem;
  }

  // Payment method selection
  selectPaymentMethod(methodId: string): void {
    this.selectedPaymentMethod.set(methodId);
    this.showMobilePaymentSelector.set(false); // Close mobile selector if open
    console.log(`[POS] Payment method selected: ${methodId}`);
  }

  // Mobile payment selector methods
  openMobilePaymentSelector(): void {
    this.showMobilePaymentSelector.set(true);
  }

  closeMobilePaymentSelector(): void {
    this.showMobilePaymentSelector.set(false);
  }

  // Place order with guest payment (card payment)
  GuestOrderClick(): void {
    // Validate prerequisites for guest users
    if (!this.validateGuestOrderPrerequisites()) {
      return;
    }

    console.log(`[POS] Guest order click - opening guest payment dialog`);

    // Open guest payment dialog
    this.openGuestPaymentDialog();
  }

  // Place order with Spriggy payment (only functional payment method)
  OrderClick(): void {
    // Validate prerequisites
    if (!this.validateOrderPrerequisites()) {
      return;
    }

    // Only Spriggy payment is supported in POS
    if (this.selectedPaymentMethod() !== 'spriggy') {
      alert('Only Spriggy payment is currently supported in the POS system.');
      return;
    }

    // Use POS-specific place order dialog
    const groupedCarts = [this.shoppingCart];

    const dialogRef = this.dialog.open(PosPlaceOrderDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        groupedCarts: groupedCarts,
        editOrderId: null,
        // POS-specific properties
        viewType: this.viewType,
        guid: this.guid,
        selectedStudent: this.selectedStudent,
        selectedMenuType: this.selectedMenuType,
        selectedOrderDate: this.selectedOrderDate,
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log(`[POS] Order placed successfully with ID: ${result.orderId}`);

        // Refresh menu to update availability
        this.refreshMenu();

        // Clear cart after successful order (merchant view only)
        if (this.viewType === 'merchant') {
          this.store.dispatch(cartActions.clearCart());

          // Send cart cleared message to student view
          this.sendCartClearedMessage('manual_clear');
        }

        // Order placed message is sent from within the dialog component
        console.log(`[POS] Order placement workflow completed successfully`);

        // Revert to default guest user after order completion (merchant view only)
        if (this.viewType === 'merchant') {
          this.revertToGuestUserAfterOrder();
        }
      } else if (result === true) {
        // Handle error case
        console.error(`[POS] Order placement failed`);
      }
    });
  }

  private validateOrderPrerequisites(): boolean {
    // Check if student is selected
    if (!this.selectedStudent) {
      alert('Please select a student before placing an order.');
      return false;
    }

    // Check if cart has items
    if (!this.shoppingCart || this.shoppingCart.length === 0) {
      alert('Your cart is empty. Please add items before placing an order.');
      return false;
    }

    // Check if menu type and date are selected
    if (!this.selectedMenuType || !this.selectedOrderDate) {
      alert('Please select a menu type and order date.');
      return false;
    }

    // Note: Guest users are allowed to place orders in POS system
    // Payment method restrictions are handled in the payment dialog

    return true;
  }

  private validateGuestOrderPrerequisites(): boolean {
    // Check if guest user is selected
    if (!this.selectedStudent || !this.IsGuest) {
      alert('Please select a guest user before placing an order.');
      return false;
    }

    // Check if cart has items
    if (!this.shoppingCart || this.shoppingCart.length === 0) {
      alert('Your cart is empty. Please add items before placing an order.');
      return false;
    }

    // Check if menu type and date are selected
    if (!this.selectedMenuType || !this.selectedOrderDate) {
      alert('Please select a menu type and order date.');
      return false;
    }

    return true;
  }

  private openGuestPaymentDialog(): void {
    console.log(`[POS] Opening guest payment dialog for user ${this.selectedStudent?.FirstName} ${this.selectedStudent?.Lastname}`);

    const dialogData: GuestPaymentDialogData = {
      orders: this.shoppingCart,
      selectedStudent: this.selectedStudent,
      totalAmount: this.priceCart(),
      canteenId: this.currentMenu?.CanteenId || 0, // Use the current menu's canteen ID
      menuId: this.menuId,
      menuType: this.selectedMenuType,
      orderDate: this.selectedOrderDate.toISOString(),
      viewType: this.viewType,
      guid: this.guid
    };

    const dialogRef = this.dialog.open(GuestPaymentDialogComponent, {
      width: '500px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe((result: GuestPaymentDialogResult) => {
      if (result && result.success) {
        console.log(`[POS] Guest payment successful with order ID: ${result.orderId}`);

        // Refresh menu to update availability
        this.refreshMenu();

        // Clear cart after successful order (merchant view only)
        if (this.viewType === 'merchant') {
          this.store.dispatch(cartActions.clearCart());

          // Send cart cleared message to student view
          this.sendCartClearedMessage('manual_clear');
        }

        // Send order placed message to other tabs
        this.sendOrderPlacedMessage(parseInt(result.orderId || '0'), 'guest_card');

        console.log(`[POS] Guest order placement workflow completed successfully`);

        // Revert to default guest user after order completion (merchant view only)
        if (this.viewType === 'merchant') {
          this.revertToGuestUserAfterOrder();
        }
      } else {
        console.log(`[POS] Guest payment cancelled or failed:`, result?.message);
      }
    });
  }



  GoBackClick(): void {
    this.location.back();
  }

  // Helper method to determine if menu should be shown in student view
  shouldShowMenuInStudentView(): boolean {
    return this.viewType === 'student' && this.showMenuInStudentView();
  }

  // Image error handling
  onLogoImageError(): void {
    this.logoImageError.set(true);
  }

  getLogoImageSrc(): string {
    return this.logoImageError() ? this.defaultImagePath : 'assets/images/spriggy_schools_logo.png';
  }

  getItemImageUrl(imageUrl: string): string {
    if (!imageUrl) return this.defaultImagePath;

    if (imageUrl.indexOf('https:') > -1) {
      return imageUrl;
    } else {
      return environment.blobStorage + ImageUrlEnum.ItemsLG + imageUrl;
    }
  }

  // Cross-tab communication message senders
  private sendStudentSelectedMessage(student: UserCashless | null): void {
    const payload: StudentSelectedPayload = { student };
    console.log(`[POS] Merchant view sending student selected message to student view:`, {
      studentName: student ? `${student.FirstName} ${student.Lastname}` : 'None',
      isGuest: student?.IsGuest || false,
      studentId: student?.UserId,
      guid: this.guid
    });
    this.posCommunicationService.sendStudentSelected(payload, this.guid);
    console.log(`[POS] Student selected message sent successfully`);
  }

  private sendGuestUserReversionMessage(guestUser: UserCashless): void {
    console.log(`[POS] Sending guest user reversion message after order completion:`, {
      studentName: `${guestUser.FirstName} ${guestUser.Lastname}`,
      isGuest: guestUser.IsGuest,
      studentId: guestUser.UserId,
      reason: 'order_completed_reversion_to_guest'
    });

    // Use the same student selected message but with additional context for guest reversion
    const payload: StudentSelectedPayload = { student: guestUser };
    this.posCommunicationService.sendStudentSelected(payload, this.guid);
    console.log(`[POS] Guest user reversion message sent to student view`);
  }

  private sendCartUpdateMessage(cartItems: CartItem[]): void {
    const payload: CartUpdatedPayload = {
      itemCount: cartItems.length,
      totalAmount: GetCartItemsPrice(cartItems),
      studentId: this.selectedStudent?.UserId,
      cartItems: cartItems, // Send full cart items for real-time sync
      timestamp: new Date().toISOString()
    };
    console.log(`[POS] Sending cart update message with ${cartItems.length} items:`, payload);
    this.posCommunicationService.sendCartUpdated(payload, this.guid);
  }

  private sendMenuChangedMessage(): void {
    const payload: MenuChangedPayload = {
      menuType: this.selectedMenuType,
      orderDate: this.selectedOrderDate,
      studentId: this.selectedStudent?.UserId,
      menuName: this.menuName
    };
    this.posCommunicationService.sendMenuChanged(payload, this.guid);
  }

  private sendOrderPlacedMessage(orderId: number, paymentMethod?: string): void {
    if (!this.selectedStudent) return;

    const payload: OrderPlacedPayload = {
      orderId,
      studentId: this.selectedStudent.UserId,
      studentName: `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,
      menuType: this.selectedMenuType,
      orderDate: this.selectedOrderDate.toISOString(),
      totalAmount: this.priceCart(),
      itemCount: this.shoppingCart.length,
      paymentMethod: paymentMethod || this.selectedPaymentMethod()
    };
    this.posCommunicationService.sendOrderPlaced(payload, this.guid);
  }

  private sendItemPopupOpenedMessage(item: any, category: any): void {
    const payload: ItemPopupOpenedPayload = {
      item,
      date: this.selectedOrderDate,
      category
    };
    console.log(`[POS] Sending item popup opened message:`, payload);
    this.posCommunicationService.sendItemPopupOpened(payload, this.guid);
  }



  private sendItemPopupClosedMessage(itemAdded: boolean, item?: any): void {
    const payload: ItemPopupClosedPayload = {
      itemAdded,
      item
    };
    console.log(`[POS] Sending item popup closed message:`, payload);
    this.posCommunicationService.sendItemPopupClosed(payload, this.guid);
  }

  private sendCategoryChangedMessage(category: any): void {
    const payload: CategoryChangedPayload = {
      category,
      categoryId: category.CategoryId,
      categoryName: category.CatName
    };
    console.log(`[POS] Sending category changed message:`, payload);
    this.posCommunicationService.sendCategoryChanged(payload, this.guid);
  }

  private sendBalanceUpdatedMessage(studentId: number, balance: number, studentName: string, favoriteColor?: string, isGuest?: boolean): void {
    const payload: BalanceUpdatedPayload = {
      studentId,
      balance,
      studentName,
      favoriteColor,
      isGuest
    };
    console.log(`[POS] Sending balance updated message:`, payload);
    this.posCommunicationService.sendBalanceUpdated(payload, this.guid);
  }

  private sendCartClearedMessage(reason: 'student_changed' | 'manual_clear' | 'no_student_selected', previousStudentId?: number, newStudentId?: number): void {
    const payload: CartClearedPayload = {
      reason,
      previousStudentId,
      newStudentId
    };
    console.log(`[POS] Sending cart cleared message:`, payload);
    this.posCommunicationService.sendCartCleared(payload, this.guid);
  }

  // Cross-tab communication message handlers
  private handleOrderPlacedMessage(payload: OrderPlacedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Order placed notification received in student view for ${payload.studentName}: $${payload.totalAmount}`);

      // Refresh menu if it's for the same student and menu type
      if (this.selectedStudent?.UserId === payload.studentId &&
        this.selectedMenuType === payload.menuType) {
        console.log(`[POS] Refreshing menu in student view after order completion`);
        this.refreshMenu();
      }

      // Clear any order-related UI state in student view
      this.clearOrderRelatedState();

      console.log(`[POS] ✓ Student view updated after order completion`);
    }
  }

  /**
   * Clear order-related state in student view after order completion
   */
  private clearOrderRelatedState(): void {
    // Clear any color confirmation dialogs
    this.showColorConfirmation.set(false);
    this.colorConfirmationPending.set(false);

    // Reset any temporary UI states
    this.cartSyncInProgress.set(false);

    console.log(`[POS] Order-related state cleared in student view`);
  }

  private handleStudentSelectedMessage(payload: StudentSelectedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Student view received student selected message:`, {
        studentName: payload.student ? `${payload.student.FirstName} ${payload.student.Lastname}` : 'None',
        isGuest: payload.student?.IsGuest || false,
        studentId: payload.student?.UserId,
        currentStudent: this.selectedStudent ? `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}` : 'None'
      });

      if (payload.student) {
        // Clear cart display when changing students in student view
        if (this.selectedStudent && this.selectedStudent.UserId !== payload.student.UserId) {
          console.log(`[POS] Clearing student view cart due to student change from ${this.selectedStudent.FirstName} to ${payload.student.FirstName}`);
          this.shoppingCart = [];
          this.priceCart.set(0);
          this.showMobilePlaceOrder = false;
        }

        // Set the selected student in student view
        this.selectedStudent = payload.student;
        this.deactivatedFilters = payload.student.SchoolDeactivatedFilters || null;

        // Set IsGuest property in student view
        this.IsGuest = payload.student.IsGuest || false;
        console.log(`[POS] Student selected in student view - IsGuest: ${this.IsGuest}, Name: ${payload.student.FirstName} ${payload.student.Lastname}`);

        // Show menu content in student view
        this.showMenuInStudentView.set(true);
        console.log(`[POS] Menu content shown in student view`);

        // Load menu for the selected student
        this.loadMenuDebounce();
        console.log(`[POS] Menu loading triggered for selected student in student view`);

        console.log(`[POS] ✓ Student successfully synchronized in student view: ${payload.student.FirstName} ${payload.student.Lastname}`);
      } else {
        console.log(`[POS] No student selected - clearing student view UI`);

        // Hide menu content and clear all UI elements if no student selected
        this.showMenuInStudentView.set(false);
        this.selectedStudent = null;
        this.currentMenu = null;
        this.currentCategory = null;
        this.currentCategoryToDisplay = null;
        this.IsGuest = false;

        // Clear color confirmation dialog
        this.showColorConfirmation.set(false);
        this.studentFavoriteColor.set(null);
        this.colorConfirmationPending.set(false);

        // Clear balance information
        this.parentBalance.set(null);
        this.balanceLoading.set(false);
        this.balanceError.set(null);

        // Clear any active filter states
        this.deactivatedFilters = null;

        // Clear cart display when no student is selected
        this.shoppingCart = [];
        this.priceCart.set(0);
        this.showMobilePlaceOrder = false;

        console.log('[POS] ✓ Student view cleared - no student selected');
      }
    }
  }

  private handleMenuChangedMessage(payload: MenuChangedPayload): void {
    if (this.viewType === 'student') {
      // Update menu type and date in student view
      if (this.selectedMenuType !== payload.menuType ||
        this.selectedOrderDate.getTime() !== payload.orderDate.getTime()) {
        this.selectedMenuType = payload.menuType;
        this.selectedOrderDate = payload.orderDate;
        this.orderingForm.patchValue({
          menuType: payload.menuType,
          orderDate: payload.orderDate
        });
        console.log(`Menu changed to: ${payload.menuType} for ${payload.orderDate}`);
      }
    }
  }

  private handleCartUpdatedMessage(payload: CartUpdatedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Cart updated in student view: ${payload.itemCount} items, total: $${payload.totalAmount}`);
      console.log(`[POS] Received cart items:`, payload.cartItems);

      // Show sync in progress
      this.cartSyncInProgress.set(true);

      // Update student view cart display with merchant's cart items
      this.shoppingCart = payload.cartItems || [];
      this.priceCart.set(payload.totalAmount);
      this.showMobilePlaceOrder = payload.itemCount > 0;
      this.lastCartSyncTime.set(new Date().toLocaleTimeString());

      console.log(`[POS] Student view cart synchronized with ${this.shoppingCart.length} items`);

      // Hide sync indicator after a brief moment
      setTimeout(() => {
        this.cartSyncInProgress.set(false);
      }, 1000);
    }
  }

  private handleViewRefreshMessage(payload: ViewRefreshPayload): void {
    console.log(`View refresh requested: ${payload.reason}`);
    // Refresh current view based on reason
    if (payload.reason === 'order_completed') {
      this.refreshMenu();
    }
  }

  private handleItemPopupOpenedMessage(payload: ItemPopupOpenedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Item popup opened in student view:`, payload.item.Name);
      this.studentPopupData.set(payload);
      this.showStudentPopup.set(true);
    }
  }

  private handleItemPopupClosedMessage(payload: ItemPopupClosedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Item popup closed in student view. Item added:`, payload.itemAdded);
      this.showStudentPopup.set(false);
      this.studentPopupData.set(null);
    }
  }

  private handleCategoryChangedMessage(payload: CategoryChangedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Category changed in student view:`, payload.categoryName);
      // Find the category in the current menu and set it
      if (this.currentMenu?.MenuJSON) {
        const category = this.currentMenu.MenuJSON.find(cat => cat.CategoryId === payload.categoryId);
        if (category) {
          this.SetCategory(category);
          console.log(`[POS] Student view switched to category: ${payload.categoryName}`);
        }
      }
    }
  }

  private handleBalanceUpdatedMessage(payload: BalanceUpdatedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Balance updated in student view for student ${payload.studentName}: $${payload.balance}, IsGuest: ${payload.isGuest}`);

      // Update balance information in student view
      if (this.selectedStudent?.UserId === payload.studentId) {
        this.parentBalance.set(payload.balance);
        this.balanceLoading.set(false);
        this.balanceError.set(null);

        // Update guest status
        if (payload.isGuest !== undefined) {
          this.IsGuest = payload.isGuest;
        }

        // Update favorite color if provided
        if (payload.favoriteColor) {
          this.studentFavoriteColor.set(payload.favoriteColor);
        }

        console.log(`[POS] Student view balance synchronized: $${payload.balance}, IsGuest: ${this.IsGuest}`);
      }
    }
  }

  private handleCartClearedMessage(payload: CartClearedPayload): void {
    if (this.viewType === 'student') {
      console.log(`[POS] Cart cleared in student view. Reason: ${payload.reason}`);

      // Clear cart display in student view
      this.shoppingCart = [];
      this.priceCart.set(0);
      this.showMobilePlaceOrder = false;

      console.log(`[POS] Student view cart cleared due to: ${payload.reason}`);
    }
  }

  private async checkAuthenticationAndLoadGuestUser(): Promise<void> {
    try {
      const isAuthenticated = await this.authService.IsAuthenticated();
      const connectedUser = this.userService.GetUserConnected();

      if (!isAuthenticated || !connectedUser) {
        console.error(`[POS] User not authenticated. Cannot load guest user or access balance API.`);
        this.balanceError.set('Authentication required. Please log in to access POS features.');
        return;
      }

      console.log(`[POS] User authenticated:`, connectedUser.Email, `Role:`, connectedUser.Role);
      console.log(`[POS] Auth token available:`, !!this.authService.GetToken());

      // Only load guest user if authenticated
      this.loadDefaultGuestUser();
    } catch (error) {
      console.error(`[POS] Authentication check failed:`, error);
      this.balanceError.set('Authentication check failed. Please refresh and try again.');
    }
  }

  private loadDefaultGuestUser(): void {
    console.log(`[POS] Loading default guest user...`);

    // Search for guest user using the existing search functionality
    const filters: ArrayFilter = {
      Filter: 'guest',
      NumberRows: 10,
      PageIndex: 0,
      FilterId: 0,
      SortBy: '',
      SortDirection: '',
      MultipleFilterId: this.schoolId.toString(),
      Role: '',
      MerchantId: 0
    };

    this.userService.GetUsersWithFilterAPI(filters).subscribe({
      next: (response: any) => {
        const users = response.Users || [];
        if (users.length > 0) {
          const guestUser = users[0]; // Select first guest user found
          console.log(`[POS] Default guest user found:`, guestUser);

          // Automatically select the guest user in the dropdown
          this.autoSelectGuestUser(guestUser);
        } else {
          console.log(`[POS] No guest user found for school ${this.schoolId}`);
          this.balanceError.set('No guest user found');
        }
      },
      error: error => {
        console.error(`[POS] Error loading default guest user:`, error);
        this.balanceError.set('Failed to load default guest user');
        this.handleErrorFromService(error);
      },
    });
  }

  private loadDefaultGuestUserAfterOrder(): void {
    console.log(`[POS] Loading default guest user after order completion...`);

    // Search for guest user using the existing search functionality
    const filters: ArrayFilter = {
      Filter: 'guest',
      NumberRows: 10,
      PageIndex: 0,
      FilterId: 0,
      SortBy: '',
      SortDirection: '',
      MultipleFilterId: this.schoolId.toString(),
      Role: '',
      MerchantId: 0
    };

    this.userService.GetUsersWithFilterAPI(filters).subscribe({
      next: (response: any) => {
        const users = response.Users || [];
        if (users.length > 0) {
          const guestUser = users[0]; // Select first guest user found
          console.log(`[POS] Default guest user found for reversion:`, guestUser);

          // Automatically select the guest user in the dropdown
          this.autoSelectGuestUser(guestUser);

          // Send a specific message about guest user reversion for better tracking
          this.sendGuestUserReversionMessage(guestUser);
        } else {
          console.log(`[POS] No guest user found for reversion for school ${this.schoolId}`);
          this.balanceError.set('No guest user found for reversion');
        }
      },
      error: error => {
        console.error(`[POS] Error loading default guest user for reversion:`, error);
        this.balanceError.set('Failed to load default guest user for reversion');
        this.handleErrorFromService(error);
      },
    });
  }

  private autoSelectGuestUser(guestUser: UserCashless): void {
    console.log(`[POS] Auto-selecting guest user in ${this.viewType} view:`, guestUser.FirstName, guestUser.Lastname);

    // Set today's date as the default order date - use the exact date from listDays
    const todayDate = this.findTodayInDatesList();
    if (todayDate) {
      this.selectedOrderDate = todayDate;
      this.orderingForm.get('orderDate')?.setValue(todayDate, { emitEvent: false });
      console.log(`[POS] Order date set to today:`, todayDate);
    } else {
      console.warn(`[POS] Today's date not found in listDays, using first available date`);
      if (this.listDays.length > 0) {
        this.selectedOrderDate = this.listDays[0];
        this.orderingForm.get('orderDate')?.setValue(this.listDays[0], { emitEvent: false });
      }
    }

    // Add a small delay to ensure cross-tab communication is ready
    setTimeout(() => {
      console.log(`[POS] Triggering guest user selection after delay...`);

      // Programmatically select the guest user in the dropdown
      if (this.studentSearchDropdown) {
        this.studentSearchDropdown.selectStudent(guestUser);
        console.log(`[POS] Guest user selected via dropdown component`);
      } else {
        // If ViewChild is not ready yet, trigger the selection manually
        console.log(`[POS] Dropdown not ready, triggering manual selection`);
        this.onStudentSelected(guestUser);
      }
    }, 500); // 500ms delay to ensure cross-tab communication is established
  }



  private findTodayInDatesList(): Date | null {
    const today = new Date();
    const todayDateString = today.toDateString(); // Compare date strings to ignore time

    return this.listDays.find(date => date.toDateString() === todayDateString) || null;
  }

  /**
   * Revert to default guest user after order completion to reset POS to neutral state
   */
  private revertToGuestUserAfterOrder(): void {
    console.log(`[POS] Reverting to default guest user after order completion...`);

    // Store the current student information for logging
    const currentStudent = this.selectedStudent;

    if (!currentStudent) {
      console.log(`[POS] No student was selected, loading default guest user anyway`);
    } else if (currentStudent.IsGuest) {
      console.log(`[POS] Current user is already a guest - no reversion needed`);
      return;
    } else {
      console.log(`[POS] Reverting from student: ${currentStudent.FirstName} ${currentStudent.Lastname} (ID: ${currentStudent.UserId}) back to guest user`);
    }

    // Add a small delay to ensure all post-order cleanup is complete
    setTimeout(() => {
      // Reset order date to today
      this.resetOrderDateToToday();

      // Load and select the default guest user
      this.loadDefaultGuestUserAfterOrder();

      console.log(`[POS] ✓ Reversion to guest user initiated`);
    }, 1000); // 1 second delay to ensure cleanup is complete
  }

  /**
   * Reset the order date to today's date for the next order
   */
  private resetOrderDateToToday(): void {
    const todayDate = this.findTodayInDatesList();
    if (todayDate) {
      this.selectedOrderDate = todayDate;
      this.orderingForm.get('orderDate')?.setValue(todayDate, { emitEvent: false });
      console.log(`[POS] Order date reset to today: ${todayDate}`);
    } else {
      console.warn(`[POS] Today's date not found in listDays, keeping current date`);
    }
  }


  IsGuest: boolean = false;
  private async loadUserDetailsAndBalance(student: UserCashless): Promise<void> {
    if (!student?.UserId) {
      this.balanceError.set('Student ID not found');
      return;
    }

    // Check authentication before making API calls
    const isAuthenticated = await this.authService.IsAuthenticated();
    const connectedUser = this.userService.GetUserConnected();

    if (!isAuthenticated || !connectedUser) {
      this.balanceError.set('Authentication required. Please log in.');
      console.error(`[POS] User not authenticated for balance API call`);
      return;
    }

    console.log(`[POS] Loading user details for student ID: ${student.UserId}`);
    console.log(`[POS] Authenticated user:`, connectedUser.Email, `Role:`, connectedUser.Role);
    this.balanceLoading.set(true);
    this.balanceError.set(null);

    // Call GetUsersDetails API first
    this.userService.GetUserDetailsById(student.UserId).subscribe({
      next: (userDetails: UserCashless) => {
        console.log(`[POS] User details loaded:`, userDetails);
        if (userDetails?.ExternalUserId) {
          // Update the selected student with complete details
          this.selectedStudent = userDetails;

          // Extract balance from user details
          const balance = parseFloat(userDetails.Parents[0].SpriggyBalance);
          this.balanceLoading.set(false);
          this.parentBalance.set(balance);

          // Update IsGuest property from detailed user data (in case it changed)
          this.IsGuest = userDetails.IsGuest;

          // Send balance update message to student view (only from merchant view)
          if (this.viewType === 'merchant') {
            this.sendBalanceUpdatedMessage(
              userDetails.UserId,
              balance,
              `${userDetails.FirstName} ${userDetails.Lastname}`,
              userDetails.FavouriteColour,
              userDetails.IsGuest
            );
          }

          if (userDetails.FavouriteColour) {
            this.studentFavoriteColor.set(userDetails.FavouriteColour);
            this.showColorConfirmation.set(true);
            console.log(`[POS] Showing color confirmation for ${userDetails.FirstName}. Favorite color: ${userDetails.FavouriteColour}`);
          } else {
            // No favorite color set
            console.log(`[POS] No favorite color set for ${userDetails.FirstName}, balance loaded: $${balance}`);
          }
        } else {
          this.balanceLoading.set(false);
          this.balanceError.set('External User ID not found in user details');
          console.error(`[POS] ExternalUserId not found in user details for student: ${student.FirstName}`);
        }
      },
      error: error => {
        this.balanceLoading.set(false);
        this.balanceError.set('Failed to load user details');
        console.error(`[POS] GetUsersDetails API error:`, error);
        this.handleErrorFromService(error);
      },
    });
  }

  // Color confirmation methods
  confirmColorIdentity(): void {
    console.log(`[POS] Color identity confirmed for student`);
    this.showColorConfirmation.set(false);
    this.colorConfirmationPending.set(false);

    // Send balance update message after color confirmation (only from merchant view)
    if (this.viewType === 'merchant' && this.selectedStudent && this.parentBalance() !== null) {
      this.sendBalanceUpdatedMessage(
        this.selectedStudent.UserId,
        this.parentBalance(),
        `${this.selectedStudent.FirstName} ${this.selectedStudent.Lastname}`,
        this.studentFavoriteColor(),
        this.IsGuest
      );
    }
  }

  cancelColorConfirmation(): void {
    console.log(`[POS] Color confirmation cancelled`);
    this.showColorConfirmation.set(false);
    this.colorConfirmationPending.set(false);
    this.balanceLoading.set(false);
    this.balanceError.set('Identity confirmation cancelled');

    // Clear selected student
    this.selectedStudent = null;
    this.parentBalance.set(null);

    // Clear the dropdown selection
    if (this.studentSearchDropdown) {
      this.studentSearchDropdown.clearSelection();
    }
  }





  private unsubscribeAll(): void {
    if (this.subscriptionShoppingCart$) {
      this.subscriptionShoppingCart$.unsubscribe();
    }
    if (this.subscriptionItemsFilters$) {
      this.subscriptionItemsFilters$.unsubscribe();
    }
    if (this.subscriptionCrossTabMessages$) {
      this.subscriptionCrossTabMessages$.unsubscribe();
    }
  }
}
