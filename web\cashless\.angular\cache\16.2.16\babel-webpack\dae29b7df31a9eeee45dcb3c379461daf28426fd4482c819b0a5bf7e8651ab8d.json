{"ast": null, "code": "import _asyncToGenerator from \"D:/projects/spriggy/git-spriggy-latest/web/cashless/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { BaseComponent } from '../../../sharedModels';\nimport { validateCardNumber, validateExpiryDate, validateCVV, detectCardType, formatCardNumber } from '../../../sharedModels/guest-payment/guest-payment.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../sharedServices/guest-payment/guest-payment.service\";\nimport * as i3 from \"../../../sharedServices\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/tooltip\";\nfunction GuestPaymentDialogComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"mat-icon\", 7);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"h2\", 8);\n    i0.ɵɵtext(5, \"Payment Successful!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 9);\n    i0.ɵɵtext(7, \"Your order has been placed successfully.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"p\");\n    i0.ɵɵtext(10, \"Amount: \");\n    i0.ɵɵelementStart(11, \"strong\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"Thank you for your order!\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.totalAmount));\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"span\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r16.name || item_r16.itemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"x\", item_r16.quantity, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatCurrency(item_r16.price * item_r16.quantity));\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_hint_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.cardType());\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Card number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_21_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Invalid card number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentDialogComponent_div_7_mat_error_21_span_1_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(2, GuestPaymentDialogComponent_div_7_mat_error_21_span_2_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r4.paymentForm.get(\"cardNumber\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.cardNumberValid() && ((tmp_1_0 = ctx_r4.paymentForm.get(\"cardNumber\")) == null ? null : tmp_1_0.value));\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const month_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", month_r19.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", month_r19.label, \" \");\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Month is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const year_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", year_r20.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", year_r20.label, \" \");\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Year is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-error\");\n    i0.ɵɵtext(2, \"Card has expired\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_42_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"CVV is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_42_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Invalid CVV\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentDialogComponent_div_7_mat_error_42_span_1_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(2, GuestPaymentDialogComponent_div_7_mat_error_42_span_2_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.paymentForm.get(\"cvv\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.cvvValid() && ((tmp_1_0 = ctx_r10.paymentForm.get(\"cvv\")) == null ? null : tmp_1_0.value));\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_47_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cardholder name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_47_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, GuestPaymentDialogComponent_div_7_mat_error_47_span_1_Template, 2, 0, \"span\", 22);\n    i0.ɵɵtemplate(2, GuestPaymentDialogComponent_div_7_mat_error_47_span_2_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r11.paymentForm.get(\"cardholderName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.nameValid() && ((tmp_1_0 = ctx_r11.paymentForm.get(\"cardholderName\")) == null ? null : tmp_1_0.value));\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-error\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.errorMessage());\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_mat_spinner_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 47);\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Pay \", ctx_r14.formatCurrency(ctx_r14.totalAmount), \"\");\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_span_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Processing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GuestPaymentDialogComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"h2\", 12);\n    i0.ɵɵtext(2, \"Guest Payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 13);\n    i0.ɵɵtext(4, \"Complete your order with card payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14)(6, \"h3\");\n    i0.ɵɵtext(7, \"Order Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtemplate(9, GuestPaymentDialogComponent_div_7_div_9_Template, 7, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"strong\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"form\", 18)(14, \"mat-form-field\", 19)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Card Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 20);\n    i0.ɵɵelementStart(18, \"mat-icon\", 21);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, GuestPaymentDialogComponent_div_7_mat_hint_20_Template, 2, 1, \"mat-hint\", 22);\n    i0.ɵɵtemplate(21, GuestPaymentDialogComponent_div_7_mat_error_21_Template, 3, 2, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 23)(23, \"mat-form-field\", 24)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Month\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-select\", 25);\n    i0.ɵɵtemplate(27, GuestPaymentDialogComponent_div_7_mat_option_27_Template, 2, 2, \"mat-option\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, GuestPaymentDialogComponent_div_7_mat_error_28_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-form-field\", 27)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Year\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"mat-select\", 28);\n    i0.ɵɵtemplate(33, GuestPaymentDialogComponent_div_7_mat_option_33_Template, 2, 2, \"mat-option\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, GuestPaymentDialogComponent_div_7_mat_error_34_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, GuestPaymentDialogComponent_div_7_div_35_Template, 3, 0, \"div\", 29);\n    i0.ɵɵelementStart(36, \"mat-form-field\", 30)(37, \"mat-label\");\n    i0.ɵɵtext(38, \"CVV\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(39, \"input\", 31);\n    i0.ɵɵelementStart(40, \"mat-icon\", 32);\n    i0.ɵɵtext(41, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, GuestPaymentDialogComponent_div_7_mat_error_42_Template, 3, 2, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-form-field\", 19)(44, \"mat-label\");\n    i0.ɵɵtext(45, \"Cardholder Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"input\", 33);\n    i0.ɵɵtemplate(47, GuestPaymentDialogComponent_div_7_mat_error_47_Template, 3, 2, \"mat-error\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(48, GuestPaymentDialogComponent_div_7_div_48_Template, 3, 1, \"div\", 34);\n    i0.ɵɵelementStart(49, \"div\", 35)(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\");\n    i0.ɵɵtext(53, \"Your payment information is secure and encrypted\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 36)(55, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function GuestPaymentDialogComponent_div_7_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.closeDialog());\n    });\n    i0.ɵɵtext(56, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function GuestPaymentDialogComponent_div_7_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.processPayment());\n    });\n    i0.ɵɵtemplate(58, GuestPaymentDialogComponent_div_7_mat_spinner_58_Template, 1, 0, \"mat-spinner\", 39);\n    i0.ɵɵtemplate(59, GuestPaymentDialogComponent_div_7_span_59_Template, 2, 1, \"span\", 22);\n    i0.ɵɵtemplate(60, GuestPaymentDialogComponent_div_7_span_60_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_13_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.orderItems);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Total: \", ctx_r1.formatCurrency(ctx_r1.totalAmount), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.paymentForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r1.getCardTypeClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.getCardTypeIcon());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cardType());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r1.paymentForm.get(\"cardNumber\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r1.paymentForm.get(\"cardNumber\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.expiryMonths);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r1.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r1.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.expiryYears);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r1.paymentForm.get(\"expiryYear\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r1.paymentForm.get(\"expiryYear\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.expiryValid() && ((tmp_11_0 = ctx_r1.paymentForm.get(\"expiryMonth\")) == null ? null : tmp_11_0.value) && ((tmp_11_0 = ctx_r1.paymentForm.get(\"expiryYear\")) == null ? null : tmp_11_0.value));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r1.paymentForm.get(\"cvv\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r1.paymentForm.get(\"cvv\")) == null ? null : tmp_12_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r1.paymentForm.get(\"cardholderName\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r1.paymentForm.get(\"cardholderName\")) == null ? null : tmp_13_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isProcessing());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isFormValid() || ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isProcessing());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isProcessing());\n  }\n}\nexport class GuestPaymentDialogComponent extends BaseComponent {\n  constructor(fb, guestPaymentService, appInsightsService, dialogRef, data) {\n    super();\n    this.fb = fb;\n    this.guestPaymentService = guestPaymentService;\n    this.appInsightsService = appInsightsService;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.isProcessing = signal(false);\n    this.isValidating = signal(false);\n    this.paymentSuccess = signal(false);\n    this.errorMessage = signal(null);\n    this.cardType = signal('');\n    // Form validation states\n    this.cardNumberValid = signal(false);\n    this.expiryValid = signal(false);\n    this.cvvValid = signal(false);\n    this.nameValid = signal(false);\n    // Available options\n    this.expiryMonths = this.guestPaymentService.getExpiryMonths();\n    this.expiryYears = this.guestPaymentService.getExpiryYears();\n    this.initializeFormData();\n    this.createPaymentForm();\n  }\n  ngOnInit() {\n    this.setupFormValidation();\n    this.trackDialogOpened();\n  }\n  initializeFormData() {\n    this.totalAmount = this.data.totalAmount;\n    this.orderItems = this.data.orders;\n    this.guestUserId = this.data.selectedStudent?.UserId;\n    this.canteenId = this.data.canteenId;\n  }\n  createPaymentForm() {\n    this.paymentForm = this.fb.group({\n      cardNumber: ['', [Validators.required, Validators.minLength(13)]],\n      expiryMonth: ['', [Validators.required]],\n      expiryYear: ['', [Validators.required]],\n      cvv: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(4)]],\n      cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n    });\n  }\n  setupFormValidation() {\n    // Card number validation and formatting\n    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {\n      if (value) {\n        const formatted = formatCardNumber(value);\n        const isValid = validateCardNumber(value);\n        const type = detectCardType(value);\n        this.cardNumberValid.set(isValid);\n        this.cardType.set(type);\n        // Update form value without triggering valueChanges again\n        if (formatted !== value) {\n          this.paymentForm.get('cardNumber')?.setValue(formatted, {\n            emitEvent: false\n          });\n        }\n      } else {\n        this.cardNumberValid.set(false);\n        this.cardType.set('');\n      }\n    });\n    // Expiry date validation\n    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(() => this.validateExpiry());\n    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(() => this.validateExpiry());\n    // CVV validation\n    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {\n      this.cvvValid.set(value ? validateCVV(value) : false);\n    });\n    // Name validation\n    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {\n      this.nameValid.set(value ? value.trim().length >= 2 : false);\n    });\n  }\n  validateExpiry() {\n    const month = parseInt(this.paymentForm.get('expiryMonth')?.value);\n    const year = parseInt(this.paymentForm.get('expiryYear')?.value);\n    if (month && year) {\n      this.expiryValid.set(validateExpiryDate(month, year));\n    } else {\n      this.expiryValid.set(false);\n    }\n  }\n  trackDialogOpened() {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {\n      GuestUserId: this.guestUserId,\n      TotalAmount: this.totalAmount,\n      CanteenId: this.canteenId,\n      ItemCount: this.orderItems?.length || 0\n    });\n  }\n  isFormValid() {\n    return this.cardNumberValid() && this.expiryValid() && this.cvvValid() && this.nameValid() && this.paymentForm.valid;\n  }\n  processPayment() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.isFormValid() || _this.isProcessing()) {\n        return;\n      }\n      yield _this.processPaymentWithRetry();\n    })();\n  }\n  processPaymentWithRetry() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (retryCount = 0) {\n      const maxRetries = 2;\n      _this2.isProcessing.set(true);\n      _this2.errorMessage.set(null);\n      try {\n        // Create payment request\n        const paymentRequest = _this2.createPaymentRequest();\n        // Track payment attempt\n        _this2.appInsightsService.TrackEvent('GuestPaymentAttempt', {\n          GuestUserId: _this2.guestUserId,\n          Amount: _this2.totalAmount,\n          CardType: _this2.cardType(),\n          RetryCount: retryCount\n        });\n        // Process payment and create order with timeout\n        const response = yield Promise.race([_this2.guestPaymentService.processPaymentAndCreateOrder(paymentRequest).toPromise(), _this2.createTimeoutPromise(30000) // 30 second timeout\n        ]);\n\n        if (response?.isSuccess) {\n          _this2.paymentSuccess.set(true);\n          // Track success\n          _this2.appInsightsService.TrackEvent('GuestPaymentSuccess', {\n            OrderId: response.orderId,\n            Amount: response.amountCharged,\n            GuestUserId: _this2.guestUserId,\n            RetryCount: retryCount\n          });\n          // Close dialog with success result\n          setTimeout(() => {\n            _this2.dialogRef.close({\n              success: true,\n              orderId: response.orderId,\n              message: response.message\n            });\n          }, 2000);\n        } else {\n          _this2.handlePaymentError(response?.errorCode, response?.message);\n        }\n      } catch (error) {\n        // Check if this is a network error that can be retried\n        const isRetryableError = _this2.isRetryableError(error);\n        if (isRetryableError && retryCount < maxRetries) {\n          // Wait before retrying (exponential backoff)\n          const delay = Math.pow(2, retryCount) * 1000;\n          setTimeout(() => {\n            _this2.processPaymentWithRetry(retryCount + 1);\n          }, delay);\n          return;\n        }\n        // Handle final error\n        if (error.name === 'TimeoutError') {\n          _this2.handlePaymentError('PAYMENT_TIMEOUT', 'Payment processing timed out. Please try again.');\n        } else if (_this2.isNetworkError(error)) {\n          _this2.handlePaymentError('NETWORK_ERROR', 'Network connection issue. Please check your connection and try again.');\n        } else {\n          _this2.handlePaymentError('PROCESSING_ERROR', 'An unexpected error occurred. Please try again.');\n        }\n        _this2.handleErrorFromService(error);\n      } finally {\n        _this2.isProcessing.set(false);\n      }\n    }).apply(this, arguments);\n  }\n  createTimeoutPromise(timeout) {\n    return new Promise((_, reject) => {\n      setTimeout(() => {\n        const error = new Error('Payment processing timed out');\n        error.name = 'TimeoutError';\n        reject(error);\n      }, timeout);\n    });\n  }\n  isRetryableError(error) {\n    // Network errors, timeouts, and 5xx server errors are retryable\n    return this.isNetworkError(error) || error.name === 'TimeoutError' || error.status >= 500 && error.status < 600;\n  }\n  isNetworkError(error) {\n    return !navigator.onLine || error.name === 'NetworkError' || error.status === 0 || error.message?.includes('network') || error.message?.includes('connection');\n  }\n  createPaymentRequest() {\n    const formValue = this.paymentForm.value;\n    // Security: Sanitize and validate input data\n    const sanitizedCardNumber = this.sanitizeCardNumber(formValue.cardNumber);\n    const sanitizedCardholderName = this.sanitizeCardholderName(formValue.cardholderName);\n    const sanitizedCVV = this.sanitizeCVV(formValue.cvv);\n    return {\n      cardNumber: sanitizedCardNumber,\n      expiryMonth: parseInt(formValue.expiryMonth),\n      expiryYear: parseInt(formValue.expiryYear),\n      cvv: sanitizedCVV,\n      cardholderName: sanitizedCardholderName,\n      amount: this.totalAmount,\n      canteenId: this.canteenId,\n      guestUserId: this.guestUserId,\n      items: this.orderItems.map(item => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name || item.itemName,\n        itemDescription: item.description || item.itemDescription\n      })),\n      orderDate: this.data.orderDate,\n      menuId: this.data.menuId,\n      menuType: this.data.menuType\n    };\n  }\n  sanitizeCardNumber(cardNumber) {\n    if (!cardNumber) return '';\n    // Remove all non-digit characters\n    return cardNumber.replace(/\\D/g, '');\n  }\n  sanitizeCardholderName(name) {\n    if (!name) return '';\n    // Remove special characters, keep only letters, spaces, hyphens, and apostrophes\n    return name.replace(/[^a-zA-Z\\s\\-']/g, '').trim();\n  }\n  sanitizeCVV(cvv) {\n    if (!cvv) return '';\n    // Remove all non-digit characters\n    return cvv.replace(/\\D/g, '');\n  }\n  handlePaymentError(errorCode, message) {\n    let errorMsg = message || 'Payment processing failed. Please try again.';\n    // Provide user-friendly error messages based on error codes\n    switch (errorCode) {\n      case 'VALIDATION_ERROR':\n        errorMsg = 'Please check your payment details and try again.';\n        break;\n      case 'INVALID_CARD':\n        errorMsg = 'Invalid card details. Please check your card information.';\n        break;\n      case 'PAYMENT_DECLINED':\n        errorMsg = 'Your payment was declined. Please try a different card.';\n        break;\n      case 'INSUFFICIENT_FUNDS':\n        errorMsg = 'Insufficient funds. Please check your card balance.';\n        break;\n      case 'PAYMENT_TIMEOUT':\n        errorMsg = 'Payment timed out. Please try again.';\n        break;\n      case 'NETWORK_ERROR':\n        errorMsg = 'Network connection issue. Please check your connection and try again.';\n        break;\n      case 'PERMISSION_DENIED':\n        errorMsg = 'You do not have permission to make this payment.';\n        break;\n      case 'CANTEEN_NOT_FOUND':\n        errorMsg = 'Unable to process payment for this location.';\n        break;\n      case 'PROCESSING_ERROR':\n      default:\n        errorMsg = message || 'An unexpected error occurred. Please try again or contact support.';\n        break;\n    }\n    this.errorMessage.set(errorMsg);\n    // Track failure with additional context\n    this.appInsightsService.TrackEvent('GuestPaymentFailure', {\n      ErrorCode: errorCode || 'UNKNOWN',\n      ErrorMessage: errorMsg,\n      OriginalMessage: message,\n      GuestUserId: this.guestUserId,\n      Amount: this.totalAmount,\n      CardType: this.cardType()\n    });\n  }\n  closeDialog() {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogClosed', {\n      GuestUserId: this.guestUserId,\n      PaymentCompleted: this.paymentSuccess()\n    });\n    this.dialogRef.close({\n      success: false,\n      message: 'Payment cancelled'\n    });\n  }\n  // Helper methods for template\n  getCardTypeIcon() {\n    switch (this.cardType()) {\n      case 'Visa':\n        return 'credit_card';\n      case 'Mastercard':\n        return 'credit_card';\n      case 'American Express':\n        return 'credit_card';\n      default:\n        return 'credit_card';\n    }\n  }\n  getCardTypeClass() {\n    return `card-type-${this.cardType().toLowerCase().replace(/\\s/g, '-')}`;\n  }\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('en-AU', {\n      style: 'currency',\n      currency: 'AUD'\n    }).format(amount);\n  }\n  static {\n    this.ɵfac = function GuestPaymentDialogComponent_Factory(t) {\n      return new (t || GuestPaymentDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.GuestPaymentService), i0.ɵɵdirectiveInject(i3.CashlessAppInsightsService), i0.ɵɵdirectiveInject(i4.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GuestPaymentDialogComponent,\n      selectors: [[\"app-guest-payment-dialog\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"guest-payment-dialog\"], [1, \"close-button-container\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [\"class\", \"success-state\", 4, \"ngIf\"], [\"class\", \"payment-form-container\", 4, \"ngIf\"], [1, \"success-state\"], [1, \"success-icon\"], [\"color\", \"primary\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"success-details\"], [1, \"payment-form-container\"], [1, \"dialog-title\"], [1, \"dialog-subtitle\"], [1, \"order-summary\"], [1, \"order-items\"], [\"class\", \"order-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-total\"], [1, \"payment-form\", 3, \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"cardNumber\", \"placeholder\", \"1234 5678 9012 3456\", \"maxlength\", \"19\", \"autocomplete\", \"cc-number\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [1, \"expiry-row\"], [\"appearance\", \"outline\", 1, \"expiry-month\"], [\"formControlName\", \"expiryMonth\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"appearance\", \"outline\", 1, \"expiry-year\"], [\"formControlName\", \"expiryYear\"], [\"class\", \"expiry-error\", 4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"cvv-field\"], [\"matInput\", \"\", \"formControlName\", \"cvv\", \"placeholder\", \"123\", \"maxlength\", \"4\", \"type\", \"password\", \"autocomplete\", \"cc-csc\"], [\"matSuffix\", \"\", \"matTooltip\", \"3-4 digit security code on the back of your card\"], [\"matInput\", \"\", \"formControlName\", \"cardholderName\", \"placeholder\", \"John Smith\", \"autocomplete\", \"cc-name\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"action-buttons\"], [\"mat-stroked-button\", \"\", 1, \"cancel-btn\", 3, \"disabled\", \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"pay-btn\", 3, \"disabled\", \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"order-item\"], [1, \"item-name\"], [1, \"item-quantity\"], [1, \"item-price\"], [3, \"value\"], [1, \"expiry-error\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n      template: function GuestPaymentDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-dialog-content\")(1, \"div\", 0)(2, \"div\", 1)(3, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function GuestPaymentDialogComponent_Template_button_click_3_listener() {\n            return ctx.closeDialog();\n          });\n          i0.ɵɵelementStart(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"close\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, GuestPaymentDialogComponent_div_6_Template, 15, 1, \"div\", 3);\n          i0.ɵɵtemplate(7, GuestPaymentDialogComponent_div_7_Template, 61, 21, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.paymentSuccess());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.paymentSuccess());\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i6.MatIconButton, i7.MatFormField, i7.MatLabel, i7.MatHint, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatSelect, i10.MatOption, i11.MatIcon, i12.MatProgressSpinner, i4.MatDialogContent, i13.MatTooltip],\n      styles: [\".guest-payment-dialog[_ngcontent-%COMP%] {\\n  max-width: 500px;\\n  min-width: 400px;\\n  padding: 0;\\n  position: relative;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  z-index: 10;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .close-button-container[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .dialog-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0 0 24px 0;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 24px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  color: #4caf50;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin: 0 0 24px 0;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .success-state[_ngcontent-%COMP%]   .success-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  padding-top: 48px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-quantity[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0 12px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-items[_ngcontent-%COMP%]   .order-item[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .order-total[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding-top: 12px;\\n  border-top: 2px solid #e0e0e0;\\n  text-align: right;\\n  font-size: 16px;\\n  color: #333;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-month[_ngcontent-%COMP%], .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-year[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .expiry-error[_ngcontent-%COMP%] {\\n  margin-top: -12px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .cvv-field[_ngcontent-%COMP%] {\\n  width: 150px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-visa[_ngcontent-%COMP%] {\\n  color: #1a1f71;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-mastercard[_ngcontent-%COMP%] {\\n  color: #eb001b;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-american-express[_ngcontent-%COMP%] {\\n  color: #006fcf;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .payment-form[_ngcontent-%COMP%]   .card-type-unknown[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  border: 1px solid #f44336;\\n  border-radius: 4px;\\n  padding: 12px;\\n  margin-bottom: 16px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   mat-error[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .security-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background-color: #e8f5e8;\\n  border-radius: 4px;\\n  margin-bottom: 24px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .security-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .security-notice[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #2e7d32;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  position: relative;\\n}\\n.guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n  .guest-payment-dialog .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  background-color: #fff;\\n}\\n  .guest-payment-dialog .mat-mdc-form-field.mat-form-field-invalid .mat-mdc-text-field-wrapper {\\n  border-color: #f44336;\\n}\\n  .guest-payment-dialog .mat-mdc-select-panel {\\n  max-height: 200px;\\n}\\n\\n@media (max-width: 480px) {\\n  .guest-payment-dialog[_ngcontent-%COMP%] {\\n    min-width: 320px;\\n    max-width: 100vw;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .payment-form-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    padding-top: 40px;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-month[_ngcontent-%COMP%], .guest-payment-dialog[_ngcontent-%COMP%]   .expiry-row[_ngcontent-%COMP%]   .expiry-year[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .cvv-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .guest-payment-dialog[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n.processing-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 100;\\n}\\n\\n.success-state[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["signal", "Validators", "MAT_DIALOG_DATA", "BaseComponent", "validateCardNumber", "validateExpiryDate", "validateCVV", "detectCardType", "formatCardNumber", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "formatCurrency", "totalAmount", "item_r16", "name", "itemName", "ɵɵtextInterpolate1", "quantity", "ctx_r2", "price", "ctx_r3", "cardType", "ɵɵtemplate", "GuestPaymentDialogComponent_div_7_mat_error_21_span_1_Template", "GuestPaymentDialogComponent_div_7_mat_error_21_span_2_Template", "ɵɵproperty", "tmp_0_0", "ctx_r4", "paymentForm", "get", "errors", "cardNumberValid", "tmp_1_0", "value", "month_r19", "label", "year_r20", "GuestPaymentDialogComponent_div_7_mat_error_42_span_1_Template", "GuestPaymentDialogComponent_div_7_mat_error_42_span_2_Template", "ctx_r10", "cvvValid", "GuestPaymentDialogComponent_div_7_mat_error_47_span_1_Template", "GuestPaymentDialogComponent_div_7_mat_error_47_span_2_Template", "ctx_r11", "<PERSON><PERSON><PERSON><PERSON>", "ctx_r12", "errorMessage", "ɵɵelement", "ctx_r14", "GuestPaymentDialogComponent_div_7_div_9_Template", "GuestPaymentDialogComponent_div_7_mat_hint_20_Template", "GuestPaymentDialogComponent_div_7_mat_error_21_Template", "GuestPaymentDialogComponent_div_7_mat_option_27_Template", "GuestPaymentDialogComponent_div_7_mat_error_28_Template", "GuestPaymentDialogComponent_div_7_mat_option_33_Template", "GuestPaymentDialogComponent_div_7_mat_error_34_Template", "GuestPaymentDialogComponent_div_7_div_35_Template", "GuestPaymentDialogComponent_div_7_mat_error_42_Template", "GuestPaymentDialogComponent_div_7_mat_error_47_Template", "GuestPaymentDialogComponent_div_7_div_48_Template", "ɵɵlistener", "GuestPaymentDialogComponent_div_7_Template_button_click_55_listener", "ɵɵrestoreView", "_r26", "ctx_r25", "ɵɵnextContext", "ɵɵresetView", "closeDialog", "GuestPaymentDialogComponent_div_7_Template_button_click_57_listener", "ctx_r27", "processPayment", "GuestPaymentDialogComponent_div_7_mat_spinner_58_Template", "GuestPaymentDialogComponent_div_7_span_59_Template", "GuestPaymentDialogComponent_div_7_span_60_Template", "ctx_r1", "orderItems", "ɵɵclassMap", "getCardTypeClass", "getCardTypeIcon", "tmp_6_0", "invalid", "touched", "expiry<PERSON><PERSON><PERSON>", "tmp_8_0", "expiryYears", "tmp_10_0", "expiry<PERSON><PERSON><PERSON>", "tmp_11_0", "tmp_12_0", "tmp_13_0", "isProcessing", "isFormValid", "GuestPaymentDialogComponent", "constructor", "fb", "guestPaymentService", "appInsightsService", "dialogRef", "data", "isValidating", "paymentSuccess", "getExpiryMonths", "getExpiryYears", "initializeFormData", "createPaymentForm", "ngOnInit", "setupFormValidation", "trackDialogOpened", "orders", "guestUserId", "selectedStudent", "UserId", "canteenId", "group", "cardNumber", "required", "<PERSON><PERSON><PERSON><PERSON>", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "cvv", "max<PERSON><PERSON><PERSON>", "cardholder<PERSON><PERSON>", "valueChanges", "subscribe", "formatted", "<PERSON><PERSON><PERSON><PERSON>", "type", "set", "setValue", "emitEvent", "validateExpiry", "trim", "length", "month", "parseInt", "year", "TrackEvent", "GuestUserId", "TotalAmount", "CanteenId", "ItemCount", "valid", "_this", "_asyncToGenerator", "processPaymentWithRetry", "_this2", "retryCount", "maxRetries", "paymentRequest", "createPaymentRequest", "Amount", "CardType", "RetryCount", "response", "Promise", "race", "processPaymentAndCreateOrder", "to<PERSON>romise", "createTimeoutPromise", "isSuccess", "OrderId", "orderId", "amountCharged", "setTimeout", "close", "success", "message", "handlePaymentError", "errorCode", "error", "isRetryableError", "delay", "Math", "pow", "isNetworkError", "handleErrorFromService", "apply", "arguments", "timeout", "_", "reject", "Error", "status", "navigator", "onLine", "includes", "formValue", "sanitizedCardNumber", "sanitizeCardNumber", "sanitizedCardholderName", "sanitizeCardholderName", "sanitizedCVV", "sanitizeCVV", "amount", "items", "map", "item", "menuItemId", "itemDescription", "description", "orderDate", "menuId", "menuType", "replace", "errorMsg", "ErrorCode", "ErrorMessage", "OriginalMessage", "PaymentCompleted", "toLowerCase", "Intl", "NumberFormat", "style", "currency", "format", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "GuestPaymentService", "i3", "CashlessAppInsightsService", "i4", "MatDialogRef", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "GuestPaymentDialogComponent_Template", "rf", "ctx", "GuestPaymentDialogComponent_Template_button_click_3_listener", "GuestPaymentDialogComponent_div_6_Template", "GuestPaymentDialogComponent_div_7_Template"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\guest-payment-dialog\\guest-payment-dialog.component.ts", "D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\pos\\components\\guest-payment-dialog\\guest-payment-dialog.component.html"], "sourcesContent": ["import { Component, Inject, OnInit, signal } from '@angular/core';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { BaseComponent } from '../../../sharedModels';\nimport { GuestPaymentService } from '../../../sharedServices/guest-payment/guest-payment.service';\nimport { CashlessAppInsightsService } from '../../../sharedServices';\nimport {\n  GuestPaymentDialogData,\n  GuestPaymentDialogResult,\n  GuestPaymentRequest,\n  validateCardNumber,\n  validateExpiryDate,\n  validateCVV,\n  detectCardType,\n  formatCardNumber\n} from '../../../sharedModels/guest-payment/guest-payment.models';\n\n@Component({\n  selector: 'app-guest-payment-dialog',\n  templateUrl: './guest-payment-dialog.component.html',\n  styleUrls: ['./guest-payment-dialog.component.scss']\n})\nexport class GuestPaymentDialogComponent extends BaseComponent implements OnInit {\n  \n  paymentForm: FormGroup;\n  isProcessing = signal<boolean>(false);\n  isValidating = signal<boolean>(false);\n  paymentSuccess = signal<boolean>(false);\n  errorMessage = signal<string | null>(null);\n  cardType = signal<string>('');\n  \n  // Form validation states\n  cardNumberValid = signal<boolean>(false);\n  expiryValid = signal<boolean>(false);\n  cvvValid = signal<boolean>(false);\n  nameValid = signal<boolean>(false);\n  \n  // Available options\n  expiryMonths = this.guestPaymentService.getExpiryMonths();\n  expiryYears = this.guestPaymentService.getExpiryYears();\n  \n  // Order details\n  totalAmount: number;\n  orderItems: any[];\n  guestUserId: number;\n  canteenId: number;\n\n  constructor(\n    private fb: FormBuilder,\n    private guestPaymentService: GuestPaymentService,\n    private appInsightsService: CashlessAppInsightsService,\n    public dialogRef: MatDialogRef<GuestPaymentDialogComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: GuestPaymentDialogData\n  ) {\n    super();\n    this.initializeFormData();\n    this.createPaymentForm();\n  }\n\n  ngOnInit(): void {\n    this.setupFormValidation();\n    this.trackDialogOpened();\n  }\n\n  private initializeFormData(): void {\n    this.totalAmount = this.data.totalAmount;\n    this.orderItems = this.data.orders;\n    this.guestUserId = this.data.selectedStudent?.UserId;\n    this.canteenId = this.data.canteenId;\n  }\n\n  private createPaymentForm(): void {\n    this.paymentForm = this.fb.group({\n      cardNumber: ['', [Validators.required, Validators.minLength(13)]],\n      expiryMonth: ['', [Validators.required]],\n      expiryYear: ['', [Validators.required]],\n      cvv: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(4)]],\n      cardholderName: ['', [Validators.required, Validators.minLength(2)]]\n    });\n  }\n\n  private setupFormValidation(): void {\n    // Card number validation and formatting\n    this.paymentForm.get('cardNumber')?.valueChanges.subscribe(value => {\n      if (value) {\n        const formatted = formatCardNumber(value);\n        const isValid = validateCardNumber(value);\n        const type = detectCardType(value);\n        \n        this.cardNumberValid.set(isValid);\n        this.cardType.set(type);\n        \n        // Update form value without triggering valueChanges again\n        if (formatted !== value) {\n          this.paymentForm.get('cardNumber')?.setValue(formatted, { emitEvent: false });\n        }\n      } else {\n        this.cardNumberValid.set(false);\n        this.cardType.set('');\n      }\n    });\n\n    // Expiry date validation\n    this.paymentForm.get('expiryMonth')?.valueChanges.subscribe(() => this.validateExpiry());\n    this.paymentForm.get('expiryYear')?.valueChanges.subscribe(() => this.validateExpiry());\n\n    // CVV validation\n    this.paymentForm.get('cvv')?.valueChanges.subscribe(value => {\n      this.cvvValid.set(value ? validateCVV(value) : false);\n    });\n\n    // Name validation\n    this.paymentForm.get('cardholderName')?.valueChanges.subscribe(value => {\n      this.nameValid.set(value ? value.trim().length >= 2 : false);\n    });\n  }\n\n  private validateExpiry(): void {\n    const month = parseInt(this.paymentForm.get('expiryMonth')?.value);\n    const year = parseInt(this.paymentForm.get('expiryYear')?.value);\n    \n    if (month && year) {\n      this.expiryValid.set(validateExpiryDate(month, year));\n    } else {\n      this.expiryValid.set(false);\n    }\n  }\n\n  private trackDialogOpened(): void {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogOpened', {\n      GuestUserId: this.guestUserId,\n      TotalAmount: this.totalAmount,\n      CanteenId: this.canteenId,\n      ItemCount: this.orderItems?.length || 0\n    });\n  }\n\n  isFormValid(): boolean {\n    return this.cardNumberValid() && \n           this.expiryValid() && \n           this.cvvValid() && \n           this.nameValid() &&\n           this.paymentForm.valid;\n  }\n\n  async processPayment(): Promise<void> {\n    if (!this.isFormValid() || this.isProcessing()) {\n      return;\n    }\n\n    await this.processPaymentWithRetry();\n  }\n\n  private async processPaymentWithRetry(retryCount: number = 0): Promise<void> {\n    const maxRetries = 2;\n\n    this.isProcessing.set(true);\n    this.errorMessage.set(null);\n\n    try {\n      // Create payment request\n      const paymentRequest: GuestPaymentRequest = this.createPaymentRequest();\n\n      // Track payment attempt\n      this.appInsightsService.TrackEvent('GuestPaymentAttempt', {\n        GuestUserId: this.guestUserId,\n        Amount: this.totalAmount,\n        CardType: this.cardType(),\n        RetryCount: retryCount\n      });\n\n      // Process payment and create order with timeout\n      const response = await Promise.race([\n        this.guestPaymentService.processPaymentAndCreateOrder(paymentRequest).toPromise(),\n        this.createTimeoutPromise(30000) // 30 second timeout\n      ]);\n\n      if (response?.isSuccess) {\n        this.paymentSuccess.set(true);\n\n        // Track success\n        this.appInsightsService.TrackEvent('GuestPaymentSuccess', {\n          OrderId: response.orderId,\n          Amount: response.amountCharged,\n          GuestUserId: this.guestUserId,\n          RetryCount: retryCount\n        });\n\n        // Close dialog with success result\n        setTimeout(() => {\n          this.dialogRef.close({\n            success: true,\n            orderId: response.orderId,\n            message: response.message\n          } as GuestPaymentDialogResult);\n        }, 2000);\n\n      } else {\n        this.handlePaymentError(response?.errorCode, response?.message);\n      }\n\n    } catch (error: any) {\n      // Check if this is a network error that can be retried\n      const isRetryableError = this.isRetryableError(error);\n\n      if (isRetryableError && retryCount < maxRetries) {\n        // Wait before retrying (exponential backoff)\n        const delay = Math.pow(2, retryCount) * 1000;\n        setTimeout(() => {\n          this.processPaymentWithRetry(retryCount + 1);\n        }, delay);\n        return;\n      }\n\n      // Handle final error\n      if (error.name === 'TimeoutError') {\n        this.handlePaymentError('PAYMENT_TIMEOUT', 'Payment processing timed out. Please try again.');\n      } else if (this.isNetworkError(error)) {\n        this.handlePaymentError('NETWORK_ERROR', 'Network connection issue. Please check your connection and try again.');\n      } else {\n        this.handlePaymentError('PROCESSING_ERROR', 'An unexpected error occurred. Please try again.');\n      }\n\n      this.handleErrorFromService(error);\n    } finally {\n      this.isProcessing.set(false);\n    }\n  }\n\n  private createTimeoutPromise(timeout: number): Promise<never> {\n    return new Promise((_, reject) => {\n      setTimeout(() => {\n        const error = new Error('Payment processing timed out');\n        error.name = 'TimeoutError';\n        reject(error);\n      }, timeout);\n    });\n  }\n\n  private isRetryableError(error: any): boolean {\n    // Network errors, timeouts, and 5xx server errors are retryable\n    return this.isNetworkError(error) ||\n           error.name === 'TimeoutError' ||\n           (error.status >= 500 && error.status < 600);\n  }\n\n  private isNetworkError(error: any): boolean {\n    return !navigator.onLine ||\n           error.name === 'NetworkError' ||\n           error.status === 0 ||\n           error.message?.includes('network') ||\n           error.message?.includes('connection');\n  }\n\n  private createPaymentRequest(): GuestPaymentRequest {\n    const formValue = this.paymentForm.value;\n\n    // Security: Sanitize and validate input data\n    const sanitizedCardNumber = this.sanitizeCardNumber(formValue.cardNumber);\n    const sanitizedCardholderName = this.sanitizeCardholderName(formValue.cardholderName);\n    const sanitizedCVV = this.sanitizeCVV(formValue.cvv);\n\n    return {\n      cardNumber: sanitizedCardNumber,\n      expiryMonth: parseInt(formValue.expiryMonth),\n      expiryYear: parseInt(formValue.expiryYear),\n      cvv: sanitizedCVV,\n      cardholderName: sanitizedCardholderName,\n      amount: this.totalAmount,\n      canteenId: this.canteenId,\n      guestUserId: this.guestUserId,\n      items: this.orderItems.map(item => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name || item.itemName,\n        itemDescription: item.description || item.itemDescription\n      })),\n      orderDate: this.data.orderDate,\n      menuId: this.data.menuId,\n      menuType: this.data.menuType\n    };\n  }\n\n  private sanitizeCardNumber(cardNumber: string): string {\n    if (!cardNumber) return '';\n    // Remove all non-digit characters\n    return cardNumber.replace(/\\D/g, '');\n  }\n\n  private sanitizeCardholderName(name: string): string {\n    if (!name) return '';\n    // Remove special characters, keep only letters, spaces, hyphens, and apostrophes\n    return name.replace(/[^a-zA-Z\\s\\-']/g, '').trim();\n  }\n\n  private sanitizeCVV(cvv: string): string {\n    if (!cvv) return '';\n    // Remove all non-digit characters\n    return cvv.replace(/\\D/g, '');\n  }\n\n  private handlePaymentError(errorCode?: string, message?: string): void {\n    let errorMsg = message || 'Payment processing failed. Please try again.';\n\n    // Provide user-friendly error messages based on error codes\n    switch (errorCode) {\n      case 'VALIDATION_ERROR':\n        errorMsg = 'Please check your payment details and try again.';\n        break;\n      case 'INVALID_CARD':\n        errorMsg = 'Invalid card details. Please check your card information.';\n        break;\n      case 'PAYMENT_DECLINED':\n        errorMsg = 'Your payment was declined. Please try a different card.';\n        break;\n      case 'INSUFFICIENT_FUNDS':\n        errorMsg = 'Insufficient funds. Please check your card balance.';\n        break;\n      case 'PAYMENT_TIMEOUT':\n        errorMsg = 'Payment timed out. Please try again.';\n        break;\n      case 'NETWORK_ERROR':\n        errorMsg = 'Network connection issue. Please check your connection and try again.';\n        break;\n      case 'PERMISSION_DENIED':\n        errorMsg = 'You do not have permission to make this payment.';\n        break;\n      case 'CANTEEN_NOT_FOUND':\n        errorMsg = 'Unable to process payment for this location.';\n        break;\n      case 'PROCESSING_ERROR':\n      default:\n        errorMsg = message || 'An unexpected error occurred. Please try again or contact support.';\n        break;\n    }\n\n    this.errorMessage.set(errorMsg);\n\n    // Track failure with additional context\n    this.appInsightsService.TrackEvent('GuestPaymentFailure', {\n      ErrorCode: errorCode || 'UNKNOWN',\n      ErrorMessage: errorMsg,\n      OriginalMessage: message,\n      GuestUserId: this.guestUserId,\n      Amount: this.totalAmount,\n      CardType: this.cardType()\n    });\n  }\n\n  closeDialog(): void {\n    this.appInsightsService.TrackEvent('GuestPaymentDialogClosed', {\n      GuestUserId: this.guestUserId,\n      PaymentCompleted: this.paymentSuccess()\n    });\n\n    this.dialogRef.close({\n      success: false,\n      message: 'Payment cancelled'\n    } as GuestPaymentDialogResult);\n  }\n\n  // Helper methods for template\n  getCardTypeIcon(): string {\n    switch (this.cardType()) {\n      case 'Visa': return 'credit_card';\n      case 'Mastercard': return 'credit_card';\n      case 'American Express': return 'credit_card';\n      default: return 'credit_card';\n    }\n  }\n\n  getCardTypeClass(): string {\n    return `card-type-${this.cardType().toLowerCase().replace(/\\s/g, '-')}`;\n  }\n\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('en-AU', {\n      style: 'currency',\n      currency: 'AUD'\n    }).format(amount);\n  }\n}\n", "<mat-dialog-content>\n  <div class=\"guest-payment-dialog\">\n    <!-- Close Button -->\n    <div class=\"close-button-container\">\n      <button mat-icon-button class=\"close-button\" (click)=\"closeDialog()\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n\n    <!-- Success State -->\n    <div *ngIf=\"paymentSuccess()\" class=\"success-state\">\n      <div class=\"success-icon\">\n        <mat-icon color=\"primary\">check_circle</mat-icon>\n      </div>\n      <h2 class=\"success-title\">Payment Successful!</h2>\n      <p class=\"success-message\">Your order has been placed successfully.</p>\n      <div class=\"success-details\">\n        <p>Amount: <strong>{{ formatCurrency(totalAmount) }}</strong></p>\n        <p>Thank you for your order!</p>\n      </div>\n    </div>\n\n    <!-- Payment Form -->\n    <div *ngIf=\"!paymentSuccess()\" class=\"payment-form-container\">\n      <!-- Header -->\n      <h2 class=\"dialog-title\">Guest Payment</h2>\n      <p class=\"dialog-subtitle\">Complete your order with card payment</p>\n\n      <!-- Order Summary -->\n      <div class=\"order-summary\">\n        <h3>Order Summary</h3>\n        <div class=\"order-items\">\n          <div *ngFor=\"let item of orderItems\" class=\"order-item\">\n            <span class=\"item-name\">{{ item.name || item.itemName }}</span>\n            <span class=\"item-quantity\">x{{ item.quantity }}</span>\n            <span class=\"item-price\">{{ formatCurrency(item.price * item.quantity) }}</span>\n          </div>\n        </div>\n        <div class=\"order-total\">\n          <strong>Total: {{ formatCurrency(totalAmount) }}</strong>\n        </div>\n      </div>\n\n      <!-- Payment Form -->\n      <form [formGroup]=\"paymentForm\" class=\"payment-form\">\n        <!-- Card Number -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Card Number</mat-label>\n          <input \n            matInput \n            formControlName=\"cardNumber\"\n            placeholder=\"1234 5678 9012 3456\"\n            maxlength=\"19\"\n            autocomplete=\"cc-number\">\n          <mat-icon matSuffix [class]=\"getCardTypeClass()\">{{ getCardTypeIcon() }}</mat-icon>\n          <mat-hint *ngIf=\"cardType()\">{{ cardType() }}</mat-hint>\n          <mat-error *ngIf=\"paymentForm.get('cardNumber')?.invalid && paymentForm.get('cardNumber')?.touched\">\n            <span *ngIf=\"paymentForm.get('cardNumber')?.errors?.['required']\">Card number is required</span>\n            <span *ngIf=\"!cardNumberValid() && paymentForm.get('cardNumber')?.value\">Invalid card number</span>\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Expiry Date -->\n        <div class=\"expiry-row\">\n          <mat-form-field appearance=\"outline\" class=\"expiry-month\">\n            <mat-label>Month</mat-label>\n            <mat-select formControlName=\"expiryMonth\">\n              <mat-option *ngFor=\"let month of expiryMonths\" [value]=\"month.value\">\n                {{ month.label }}\n              </mat-option>\n            </mat-select>\n            <mat-error *ngIf=\"paymentForm.get('expiryMonth')?.invalid && paymentForm.get('expiryMonth')?.touched\">\n              Month is required\n            </mat-error>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\" class=\"expiry-year\">\n            <mat-label>Year</mat-label>\n            <mat-select formControlName=\"expiryYear\">\n              <mat-option *ngFor=\"let year of expiryYears\" [value]=\"year.value\">\n                {{ year.label }}\n              </mat-option>\n            </mat-select>\n            <mat-error *ngIf=\"paymentForm.get('expiryYear')?.invalid && paymentForm.get('expiryYear')?.touched\">\n              Year is required\n            </mat-error>\n          </mat-form-field>\n        </div>\n\n        <div *ngIf=\"!expiryValid() && paymentForm.get('expiryMonth')?.value && paymentForm.get('expiryYear')?.value\" \n             class=\"expiry-error\">\n          <mat-error>Card has expired</mat-error>\n        </div>\n\n        <!-- CVV -->\n        <mat-form-field appearance=\"outline\" class=\"cvv-field\">\n          <mat-label>CVV</mat-label>\n          <input \n            matInput \n            formControlName=\"cvv\"\n            placeholder=\"123\"\n            maxlength=\"4\"\n            type=\"password\"\n            autocomplete=\"cc-csc\">\n          <mat-icon matSuffix matTooltip=\"3-4 digit security code on the back of your card\">help_outline</mat-icon>\n          <mat-error *ngIf=\"paymentForm.get('cvv')?.invalid && paymentForm.get('cvv')?.touched\">\n            <span *ngIf=\"paymentForm.get('cvv')?.errors?.['required']\">CVV is required</span>\n            <span *ngIf=\"!cvvValid() && paymentForm.get('cvv')?.value\">Invalid CVV</span>\n          </mat-error>\n        </mat-form-field>\n\n        <!-- Cardholder Name -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Cardholder Name</mat-label>\n          <input \n            matInput \n            formControlName=\"cardholderName\"\n            placeholder=\"John Smith\"\n            autocomplete=\"cc-name\">\n          <mat-error *ngIf=\"paymentForm.get('cardholderName')?.invalid && paymentForm.get('cardholderName')?.touched\">\n            <span *ngIf=\"paymentForm.get('cardholderName')?.errors?.['required']\">Cardholder name is required</span>\n            <span *ngIf=\"!nameValid() && paymentForm.get('cardholderName')?.value\">Name must be at least 2 characters</span>\n          </mat-error>\n        </mat-form-field>\n      </form>\n\n      <!-- Error Message -->\n      <div *ngIf=\"errorMessage()\" class=\"error-message\">\n        <mat-error>{{ errorMessage() }}</mat-error>\n      </div>\n\n      <!-- Security Notice -->\n      <div class=\"security-notice\">\n        <mat-icon>security</mat-icon>\n        <span>Your payment information is secure and encrypted</span>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"action-buttons\">\n        <button \n          mat-stroked-button \n          class=\"cancel-btn\" \n          (click)=\"closeDialog()\"\n          [disabled]=\"isProcessing()\">\n          Cancel\n        </button>\n        \n        <button \n          mat-raised-button \n          color=\"primary\"\n          class=\"pay-btn\"\n          (click)=\"processPayment()\"\n          [disabled]=\"!isFormValid() || isProcessing()\">\n          <mat-spinner *ngIf=\"isProcessing()\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!isProcessing()\">Pay {{ formatCurrency(totalAmount) }}</span>\n          <span *ngIf=\"isProcessing()\">Processing...</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</mat-dialog-content>\n"], "mappings": ";AAAA,SAAoCA,MAAM,QAAQ,eAAe;AACjE,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,eAAe,QAAsB,0BAA0B;AACxE,SAASC,aAAa,QAAQ,uBAAuB;AAGrD,SAIEC,kBAAkB,EAClBC,kBAAkB,EAClBC,WAAW,EACXC,cAAc,EACdC,gBAAgB,QACX,0DAA0D;;;;;;;;;;;;;;;;;ICL7DC,EAAA,CAAAC,cAAA,aAAoD;IAEtBD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEnDH,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,cAA6B;IACxBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADbH,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAD,MAAA,CAAAE,WAAA,EAAiC;;;;;IAelDR,EAAA,CAAAC,cAAA,cAAwD;IAC9BD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFxDH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAI,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAE,QAAA,CAAgC;IAC5BX,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,kBAAA,MAAAH,QAAA,CAAAI,QAAA,KAAoB;IACvBb,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAS,MAAA,CAAAP,cAAA,CAAAE,QAAA,CAAAM,KAAA,GAAAN,QAAA,CAAAI,QAAA,EAAgD;;;;;IAoB3Eb,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA3BH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAW,MAAA,CAAAC,QAAA,GAAgB;;;;;IAE3CjB,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChGH,EAAA,CAAAC,cAAA,WAAyE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFrGH,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAkB,UAAA,IAAAC,8DAAA,mBAAgG;IAChGnB,EAAA,CAAAkB,UAAA,IAAAE,8DAAA,mBAAmG;IACrGpB,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,WAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;IACzD1B,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAqB,UAAA,UAAAE,MAAA,CAAAI,eAAA,QAAAC,OAAA,GAAAL,MAAA,CAAAC,WAAA,CAAAC,GAAA,iCAAAG,OAAA,CAAAC,KAAA,EAAgE;;;;;IASrE7B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAqB,UAAA,UAAAS,SAAA,CAAAD,KAAA,CAAqB;IAClE7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAkB,SAAA,CAAAC,KAAA,MACF;;;;;IAEF/B,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAMVH,EAAA,CAAAC,cAAA,qBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFgCH,EAAA,CAAAqB,UAAA,UAAAW,QAAA,CAAAH,KAAA,CAAoB;IAC/D7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAoB,QAAA,CAAAD,KAAA,MACF;;;;;IAEF/B,EAAA,CAAAC,cAAA,gBAAoG;IAClGD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAIhBH,EAAA,CAAAC,cAAA,cAC0B;IACbD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAerCH,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjFH,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF/EH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAkB,UAAA,IAAAe,8DAAA,mBAAiF;IACjFjC,EAAA,CAAAkB,UAAA,IAAAgB,8DAAA,mBAA6E;IAC/ElC,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAa,OAAA,CAAAX,WAAA,CAAAC,GAAA,0BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkD;IAClD1B,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAqB,UAAA,UAAAc,OAAA,CAAAC,QAAA,QAAAR,OAAA,GAAAO,OAAA,CAAAX,WAAA,CAAAC,GAAA,0BAAAG,OAAA,CAAAC,KAAA,EAAkD;;;;;IAazD7B,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxGH,EAAA,CAAAC,cAAA,WAAuE;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFlHH,EAAA,CAAAC,cAAA,gBAA4G;IAC1GD,EAAA,CAAAkB,UAAA,IAAAmB,8DAAA,mBAAwG;IACxGrC,EAAA,CAAAkB,UAAA,IAAAoB,8DAAA,mBAAgH;IAClHtC,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAqB,UAAA,UAAAC,OAAA,GAAAiB,OAAA,CAAAf,WAAA,CAAAC,GAAA,qCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA6D;IAC7D1B,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAqB,UAAA,UAAAkB,OAAA,CAAAC,SAAA,QAAAZ,OAAA,GAAAW,OAAA,CAAAf,WAAA,CAAAC,GAAA,qCAAAG,OAAA,CAAAC,KAAA,EAA8D;;;;;IAM3E7B,EAAA,CAAAC,cAAA,cAAkD;IACrCD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhCH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAC,YAAA,GAAoB;;;;;IAyB7B1C,EAAA,CAAA2C,SAAA,sBAAgE;;;;;IAChE3C,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5CH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,kBAAA,SAAAgC,OAAA,CAAArC,cAAA,CAAAqC,OAAA,CAAApC,WAAA,MAAqC;;;;;IACnER,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IApIvDH,EAAA,CAAAC,cAAA,cAA8D;IAEnCD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGpEH,EAAA,CAAAC,cAAA,cAA2B;IACrBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAkB,UAAA,IAAA2B,gDAAA,kBAIM;IACR7C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAK7DH,EAAA,CAAAC,cAAA,gBAAqD;IAGtCD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAA2C,SAAA,iBAK2B;IAC3B3C,EAAA,CAAAC,cAAA,oBAAiD;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnFH,EAAA,CAAAkB,UAAA,KAAA4B,sDAAA,uBAAwD;IACxD9C,EAAA,CAAAkB,UAAA,KAAA6B,uDAAA,wBAGY;IACd/C,EAAA,CAAAG,YAAA,EAAiB;IAGjBH,EAAA,CAAAC,cAAA,eAAwB;IAETD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAC,cAAA,sBAA0C;IACxCD,EAAA,CAAAkB,UAAA,KAAA8B,wDAAA,yBAEa;IACfhD,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAkB,UAAA,KAAA+B,uDAAA,wBAEY;IACdjD,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAC,cAAA,0BAAyD;IAC5CD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3BH,EAAA,CAAAC,cAAA,sBAAyC;IACvCD,EAAA,CAAAkB,UAAA,KAAAgC,wDAAA,yBAEa;IACflD,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAkB,UAAA,KAAAiC,uDAAA,wBAEY;IACdnD,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAkB,UAAA,KAAAkC,iDAAA,kBAGM;IAGNpD,EAAA,CAAAC,cAAA,0BAAuD;IAC1CD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1BH,EAAA,CAAA2C,SAAA,iBAMwB;IACxB3C,EAAA,CAAAC,cAAA,oBAAkF;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzGH,EAAA,CAAAkB,UAAA,KAAAmC,uDAAA,wBAGY;IACdrD,EAAA,CAAAG,YAAA,EAAiB;IAGjBH,EAAA,CAAAC,cAAA,0BAAwD;IAC3CD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAA2C,SAAA,iBAIyB;IACzB3C,EAAA,CAAAkB,UAAA,KAAAoC,uDAAA,wBAGY;IACdtD,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAkB,UAAA,KAAAqC,iDAAA,kBAEM;IAGNvD,EAAA,CAAAC,cAAA,eAA6B;IACjBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/DH,EAAA,CAAAC,cAAA,eAA4B;IAIxBD,EAAA,CAAAwD,UAAA,mBAAAC,oEAAA;MAAAzD,EAAA,CAAA0D,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAA6D,aAAA;MAAA,OAAS7D,EAAA,CAAA8D,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEvB/D,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKgD;IAD9CD,EAAA,CAAAwD,UAAA,mBAAAQ,oEAAA;MAAAhE,EAAA,CAAA0D,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAjE,EAAA,CAAA6D,aAAA;MAAA,OAAS7D,EAAA,CAAA8D,WAAA,CAAAG,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAE1BlE,EAAA,CAAAkB,UAAA,KAAAiD,yDAAA,0BAAgE;IAChEnE,EAAA,CAAAkB,UAAA,KAAAkD,kDAAA,mBAA0E;IAC1EpE,EAAA,CAAAkB,UAAA,KAAAmD,kDAAA,mBAAiD;IACnDrE,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;IA5HeH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAqB,UAAA,YAAAiD,MAAA,CAAAC,UAAA,CAAa;IAO3BvE,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAY,kBAAA,YAAA0D,MAAA,CAAA/D,cAAA,CAAA+D,MAAA,CAAA9D,WAAA,MAAwC;IAK9CR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,cAAAiD,MAAA,CAAA9C,WAAA,CAAyB;IAUPxB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAwE,UAAA,CAAAF,MAAA,CAAAG,gBAAA,GAA4B;IAACzE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAiE,MAAA,CAAAI,eAAA,GAAuB;IAC7D1E,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAqB,UAAA,SAAAiD,MAAA,CAAArD,QAAA,GAAgB;IACfjB,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAqB,UAAA,WAAAsD,OAAA,GAAAL,MAAA,CAAA9C,WAAA,CAAAC,GAAA,iCAAAkD,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAL,MAAA,CAAA9C,WAAA,CAAAC,GAAA,iCAAAkD,OAAA,CAAAE,OAAA,EAAsF;IAWhE7E,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAqB,UAAA,YAAAiD,MAAA,CAAAQ,YAAA,CAAe;IAInC9E,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAqB,UAAA,WAAA0D,OAAA,GAAAT,MAAA,CAAA9C,WAAA,CAAAC,GAAA,kCAAAsD,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAT,MAAA,CAAA9C,WAAA,CAAAC,GAAA,kCAAAsD,OAAA,CAAAF,OAAA,EAAwF;IAQrE7E,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAqB,UAAA,YAAAiD,MAAA,CAAAU,WAAA,CAAc;IAIjChF,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAqB,UAAA,WAAA4D,QAAA,GAAAX,MAAA,CAAA9C,WAAA,CAAAC,GAAA,iCAAAwD,QAAA,CAAAL,OAAA,OAAAK,QAAA,GAAAX,MAAA,CAAA9C,WAAA,CAAAC,GAAA,iCAAAwD,QAAA,CAAAJ,OAAA,EAAsF;IAMhG7E,EAAA,CAAAI,SAAA,GAAqG;IAArGJ,EAAA,CAAAqB,UAAA,UAAAiD,MAAA,CAAAY,WAAA,QAAAC,QAAA,GAAAb,MAAA,CAAA9C,WAAA,CAAAC,GAAA,kCAAA0D,QAAA,CAAAtD,KAAA,OAAAsD,QAAA,GAAAb,MAAA,CAAA9C,WAAA,CAAAC,GAAA,iCAAA0D,QAAA,CAAAtD,KAAA,EAAqG;IAgB7F7B,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAqB,UAAA,WAAA+D,QAAA,GAAAd,MAAA,CAAA9C,WAAA,CAAAC,GAAA,0BAAA2D,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAAd,MAAA,CAAA9C,WAAA,CAAAC,GAAA,0BAAA2D,QAAA,CAAAP,OAAA,EAAwE;IAcxE7E,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAqB,UAAA,WAAAgE,QAAA,GAAAf,MAAA,CAAA9C,WAAA,CAAAC,GAAA,qCAAA4D,QAAA,CAAAT,OAAA,OAAAS,QAAA,GAAAf,MAAA,CAAA9C,WAAA,CAAAC,GAAA,qCAAA4D,QAAA,CAAAR,OAAA,EAA8F;IAQxG7E,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAiD,MAAA,CAAA5B,YAAA,GAAoB;IAgBtB1C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAqB,UAAA,aAAAiD,MAAA,CAAAgB,YAAA,GAA2B;IAS3BtF,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,UAAA,cAAAiD,MAAA,CAAAiB,WAAA,MAAAjB,MAAA,CAAAgB,YAAA,GAA6C;IAC/BtF,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAiD,MAAA,CAAAgB,YAAA,GAAoB;IAC3BtF,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAqB,UAAA,UAAAiD,MAAA,CAAAgB,YAAA,GAAqB;IACrBtF,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,UAAA,SAAAiD,MAAA,CAAAgB,YAAA,GAAoB;;;ADrIrC,OAAM,MAAOE,2BAA4B,SAAQ9F,aAAa;EAyB5D+F,YACUC,EAAe,EACfC,mBAAwC,EACxCC,kBAA8C,EAC/CC,SAAoD,EAC3BC,IAA4B;IAE5D,KAAK,EAAE;IANC,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IA3BtC,KAAAR,YAAY,GAAG/F,MAAM,CAAU,KAAK,CAAC;IACrC,KAAAwG,YAAY,GAAGxG,MAAM,CAAU,KAAK,CAAC;IACrC,KAAAyG,cAAc,GAAGzG,MAAM,CAAU,KAAK,CAAC;IACvC,KAAAmD,YAAY,GAAGnD,MAAM,CAAgB,IAAI,CAAC;IAC1C,KAAA0B,QAAQ,GAAG1B,MAAM,CAAS,EAAE,CAAC;IAE7B;IACA,KAAAoC,eAAe,GAAGpC,MAAM,CAAU,KAAK,CAAC;IACxC,KAAA2F,WAAW,GAAG3F,MAAM,CAAU,KAAK,CAAC;IACpC,KAAA6C,QAAQ,GAAG7C,MAAM,CAAU,KAAK,CAAC;IACjC,KAAAiD,SAAS,GAAGjD,MAAM,CAAU,KAAK,CAAC;IAElC;IACA,KAAAuF,YAAY,GAAG,IAAI,CAACa,mBAAmB,CAACM,eAAe,EAAE;IACzD,KAAAjB,WAAW,GAAG,IAAI,CAACW,mBAAmB,CAACO,cAAc,EAAE;IAgBrD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQJ,kBAAkBA,CAAA;IACxB,IAAI,CAAC3F,WAAW,GAAG,IAAI,CAACsF,IAAI,CAACtF,WAAW;IACxC,IAAI,CAAC+D,UAAU,GAAG,IAAI,CAACuB,IAAI,CAACU,MAAM;IAClC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACX,IAAI,CAACY,eAAe,EAAEC,MAAM;IACpD,IAAI,CAACC,SAAS,GAAG,IAAI,CAACd,IAAI,CAACc,SAAS;EACtC;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAAC5E,WAAW,GAAG,IAAI,CAACkE,EAAE,CAACmB,KAAK,CAAC;MAC/BC,UAAU,EAAE,CAAC,EAAE,EAAE,CAACtH,UAAU,CAACuH,QAAQ,EAAEvH,UAAU,CAACwH,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACjEC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACzH,UAAU,CAACuH,QAAQ,CAAC,CAAC;MACxCG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC1H,UAAU,CAACuH,QAAQ,CAAC,CAAC;MACvCI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC3H,UAAU,CAACuH,QAAQ,EAAEvH,UAAU,CAACwH,SAAS,CAAC,CAAC,CAAC,EAAExH,UAAU,CAAC4H,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC7H,UAAU,CAACuH,QAAQ,EAAEvH,UAAU,CAACwH,SAAS,CAAC,CAAC,CAAC,CAAC;KACpE,CAAC;EACJ;EAEQV,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAAC9E,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE6F,YAAY,CAACC,SAAS,CAAC1F,KAAK,IAAG;MACjE,IAAIA,KAAK,EAAE;QACT,MAAM2F,SAAS,GAAGzH,gBAAgB,CAAC8B,KAAK,CAAC;QACzC,MAAM4F,OAAO,GAAG9H,kBAAkB,CAACkC,KAAK,CAAC;QACzC,MAAM6F,IAAI,GAAG5H,cAAc,CAAC+B,KAAK,CAAC;QAElC,IAAI,CAACF,eAAe,CAACgG,GAAG,CAACF,OAAO,CAAC;QACjC,IAAI,CAACxG,QAAQ,CAAC0G,GAAG,CAACD,IAAI,CAAC;QAEvB;QACA,IAAIF,SAAS,KAAK3F,KAAK,EAAE;UACvB,IAAI,CAACL,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEmG,QAAQ,CAACJ,SAAS,EAAE;YAAEK,SAAS,EAAE;UAAK,CAAE,CAAC;;OAEhF,MAAM;QACL,IAAI,CAAClG,eAAe,CAACgG,GAAG,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC1G,QAAQ,CAAC0G,GAAG,CAAC,EAAE,CAAC;;IAEzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACnG,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAE6F,YAAY,CAACC,SAAS,CAAC,MAAM,IAAI,CAACO,cAAc,EAAE,CAAC;IACxF,IAAI,CAACtG,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE6F,YAAY,CAACC,SAAS,CAAC,MAAM,IAAI,CAACO,cAAc,EAAE,CAAC;IAEvF;IACA,IAAI,CAACtG,WAAW,CAACC,GAAG,CAAC,KAAK,CAAC,EAAE6F,YAAY,CAACC,SAAS,CAAC1F,KAAK,IAAG;MAC1D,IAAI,CAACO,QAAQ,CAACuF,GAAG,CAAC9F,KAAK,GAAGhC,WAAW,CAACgC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvD,CAAC,CAAC;IAEF;IACA,IAAI,CAACL,WAAW,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAE6F,YAAY,CAACC,SAAS,CAAC1F,KAAK,IAAG;MACrE,IAAI,CAACW,SAAS,CAACmF,GAAG,CAAC9F,KAAK,GAAGA,KAAK,CAACkG,IAAI,EAAE,CAACC,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC;IAC9D,CAAC,CAAC;EACJ;EAEQF,cAAcA,CAAA;IACpB,MAAMG,KAAK,GAAGC,QAAQ,CAAC,IAAI,CAAC1G,WAAW,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEI,KAAK,CAAC;IAClE,MAAMsG,IAAI,GAAGD,QAAQ,CAAC,IAAI,CAAC1G,WAAW,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEI,KAAK,CAAC;IAEhE,IAAIoG,KAAK,IAAIE,IAAI,EAAE;MACjB,IAAI,CAACjD,WAAW,CAACyC,GAAG,CAAC/H,kBAAkB,CAACqI,KAAK,EAAEE,IAAI,CAAC,CAAC;KACtD,MAAM;MACL,IAAI,CAACjD,WAAW,CAACyC,GAAG,CAAC,KAAK,CAAC;;EAE/B;EAEQpB,iBAAiBA,CAAA;IACvB,IAAI,CAACX,kBAAkB,CAACwC,UAAU,CAAC,0BAA0B,EAAE;MAC7DC,WAAW,EAAE,IAAI,CAAC5B,WAAW;MAC7B6B,WAAW,EAAE,IAAI,CAAC9H,WAAW;MAC7B+H,SAAS,EAAE,IAAI,CAAC3B,SAAS;MACzB4B,SAAS,EAAE,IAAI,CAACjE,UAAU,EAAEyD,MAAM,IAAI;KACvC,CAAC;EACJ;EAEAzC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC5D,eAAe,EAAE,IACtB,IAAI,CAACuD,WAAW,EAAE,IAClB,IAAI,CAAC9C,QAAQ,EAAE,IACf,IAAI,CAACI,SAAS,EAAE,IAChB,IAAI,CAAChB,WAAW,CAACiH,KAAK;EAC/B;EAEMvE,cAAcA,CAAA;IAAA,IAAAwE,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAI,CAACD,KAAI,CAACnD,WAAW,EAAE,IAAImD,KAAI,CAACpD,YAAY,EAAE,EAAE;QAC9C;;MAGF,MAAMoD,KAAI,CAACE,uBAAuB,EAAE;IAAC;EACvC;EAEcA,uBAAuBA,CAAA,EAAuB;IAAA,IAAAC,MAAA;IAAA,OAAAF,iBAAA,YAAtBG,UAAA,GAAqB,CAAC;MAC1D,MAAMC,UAAU,GAAG,CAAC;MAEpBF,MAAI,CAACvD,YAAY,CAACqC,GAAG,CAAC,IAAI,CAAC;MAC3BkB,MAAI,CAACnG,YAAY,CAACiF,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI;QACF;QACA,MAAMqB,cAAc,GAAwBH,MAAI,CAACI,oBAAoB,EAAE;QAEvE;QACAJ,MAAI,CAACjD,kBAAkB,CAACwC,UAAU,CAAC,qBAAqB,EAAE;UACxDC,WAAW,EAAEQ,MAAI,CAACpC,WAAW;UAC7ByC,MAAM,EAAEL,MAAI,CAACrI,WAAW;UACxB2I,QAAQ,EAAEN,MAAI,CAAC5H,QAAQ,EAAE;UACzBmI,UAAU,EAAEN;SACb,CAAC;QAEF;QACA,MAAMO,QAAQ,SAASC,OAAO,CAACC,IAAI,CAAC,CAClCV,MAAI,CAAClD,mBAAmB,CAAC6D,4BAA4B,CAACR,cAAc,CAAC,CAACS,SAAS,EAAE,EACjFZ,MAAI,CAACa,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAAA,CAClC,CAAC;;QAEF,IAAIL,QAAQ,EAAEM,SAAS,EAAE;UACvBd,MAAI,CAAC7C,cAAc,CAAC2B,GAAG,CAAC,IAAI,CAAC;UAE7B;UACAkB,MAAI,CAACjD,kBAAkB,CAACwC,UAAU,CAAC,qBAAqB,EAAE;YACxDwB,OAAO,EAAEP,QAAQ,CAACQ,OAAO;YACzBX,MAAM,EAAEG,QAAQ,CAACS,aAAa;YAC9BzB,WAAW,EAAEQ,MAAI,CAACpC,WAAW;YAC7B2C,UAAU,EAAEN;WACb,CAAC;UAEF;UACAiB,UAAU,CAAC,MAAK;YACdlB,MAAI,CAAChD,SAAS,CAACmE,KAAK,CAAC;cACnBC,OAAO,EAAE,IAAI;cACbJ,OAAO,EAAER,QAAQ,CAACQ,OAAO;cACzBK,OAAO,EAAEb,QAAQ,CAACa;aACS,CAAC;UAChC,CAAC,EAAE,IAAI,CAAC;SAET,MAAM;UACLrB,MAAI,CAACsB,kBAAkB,CAACd,QAAQ,EAAEe,SAAS,EAAEf,QAAQ,EAAEa,OAAO,CAAC;;OAGlE,CAAC,OAAOG,KAAU,EAAE;QACnB;QACA,MAAMC,gBAAgB,GAAGzB,MAAI,CAACyB,gBAAgB,CAACD,KAAK,CAAC;QAErD,IAAIC,gBAAgB,IAAIxB,UAAU,GAAGC,UAAU,EAAE;UAC/C;UACA,MAAMwB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3B,UAAU,CAAC,GAAG,IAAI;UAC5CiB,UAAU,CAAC,MAAK;YACdlB,MAAI,CAACD,uBAAuB,CAACE,UAAU,GAAG,CAAC,CAAC;UAC9C,CAAC,EAAEyB,KAAK,CAAC;UACT;;QAGF;QACA,IAAIF,KAAK,CAAC3J,IAAI,KAAK,cAAc,EAAE;UACjCmI,MAAI,CAACsB,kBAAkB,CAAC,iBAAiB,EAAE,iDAAiD,CAAC;SAC9F,MAAM,IAAItB,MAAI,CAAC6B,cAAc,CAACL,KAAK,CAAC,EAAE;UACrCxB,MAAI,CAACsB,kBAAkB,CAAC,eAAe,EAAE,uEAAuE,CAAC;SAClH,MAAM;UACLtB,MAAI,CAACsB,kBAAkB,CAAC,kBAAkB,EAAE,iDAAiD,CAAC;;QAGhGtB,MAAI,CAAC8B,sBAAsB,CAACN,KAAK,CAAC;OACnC,SAAS;QACRxB,MAAI,CAACvD,YAAY,CAACqC,GAAG,CAAC,KAAK,CAAC;;IAC7B,GAAAiD,KAAA,OAAAC,SAAA;EACH;EAEQnB,oBAAoBA,CAACoB,OAAe;IAC1C,OAAO,IAAIxB,OAAO,CAAC,CAACyB,CAAC,EAAEC,MAAM,KAAI;MAC/BjB,UAAU,CAAC,MAAK;QACd,MAAMM,KAAK,GAAG,IAAIY,KAAK,CAAC,8BAA8B,CAAC;QACvDZ,KAAK,CAAC3J,IAAI,GAAG,cAAc;QAC3BsK,MAAM,CAACX,KAAK,CAAC;MACf,CAAC,EAAES,OAAO,CAAC;IACb,CAAC,CAAC;EACJ;EAEQR,gBAAgBA,CAACD,KAAU;IACjC;IACA,OAAO,IAAI,CAACK,cAAc,CAACL,KAAK,CAAC,IAC1BA,KAAK,CAAC3J,IAAI,KAAK,cAAc,IAC5B2J,KAAK,CAACa,MAAM,IAAI,GAAG,IAAIb,KAAK,CAACa,MAAM,GAAG,GAAI;EACpD;EAEQR,cAAcA,CAACL,KAAU;IAC/B,OAAO,CAACc,SAAS,CAACC,MAAM,IACjBf,KAAK,CAAC3J,IAAI,KAAK,cAAc,IAC7B2J,KAAK,CAACa,MAAM,KAAK,CAAC,IAClBb,KAAK,CAACH,OAAO,EAAEmB,QAAQ,CAAC,SAAS,CAAC,IAClChB,KAAK,CAACH,OAAO,EAAEmB,QAAQ,CAAC,YAAY,CAAC;EAC9C;EAEQpC,oBAAoBA,CAAA;IAC1B,MAAMqC,SAAS,GAAG,IAAI,CAAC9J,WAAW,CAACK,KAAK;IAExC;IACA,MAAM0J,mBAAmB,GAAG,IAAI,CAACC,kBAAkB,CAACF,SAAS,CAACxE,UAAU,CAAC;IACzE,MAAM2E,uBAAuB,GAAG,IAAI,CAACC,sBAAsB,CAACJ,SAAS,CAACjE,cAAc,CAAC;IACrF,MAAMsE,YAAY,GAAG,IAAI,CAACC,WAAW,CAACN,SAAS,CAACnE,GAAG,CAAC;IAEpD,OAAO;MACLL,UAAU,EAAEyE,mBAAmB;MAC/BtE,WAAW,EAAEiB,QAAQ,CAACoD,SAAS,CAACrE,WAAW,CAAC;MAC5CC,UAAU,EAAEgB,QAAQ,CAACoD,SAAS,CAACpE,UAAU,CAAC;MAC1CC,GAAG,EAAEwE,YAAY;MACjBtE,cAAc,EAAEoE,uBAAuB;MACvCI,MAAM,EAAE,IAAI,CAACrL,WAAW;MACxBoG,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBH,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BqF,KAAK,EAAE,IAAI,CAACvH,UAAU,CAACwH,GAAG,CAACC,IAAI,KAAK;QAClCC,UAAU,EAAED,IAAI,CAACC,UAAU;QAC3BpL,QAAQ,EAAEmL,IAAI,CAACnL,QAAQ;QACvBE,KAAK,EAAEiL,IAAI,CAACjL,KAAK;QACjBJ,QAAQ,EAAEqL,IAAI,CAACtL,IAAI,IAAIsL,IAAI,CAACrL,QAAQ;QACpCuL,eAAe,EAAEF,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACE;OAC3C,CAAC,CAAC;MACHE,SAAS,EAAE,IAAI,CAACtG,IAAI,CAACsG,SAAS;MAC9BC,MAAM,EAAE,IAAI,CAACvG,IAAI,CAACuG,MAAM;MACxBC,QAAQ,EAAE,IAAI,CAACxG,IAAI,CAACwG;KACrB;EACH;EAEQd,kBAAkBA,CAAC1E,UAAkB;IAC3C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B;IACA,OAAOA,UAAU,CAACyF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACtC;EAEQb,sBAAsBA,CAAChL,IAAY;IACzC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA,OAAOA,IAAI,CAAC6L,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACxE,IAAI,EAAE;EACnD;EAEQ6D,WAAWA,CAACzE,GAAW;IAC7B,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB;IACA,OAAOA,GAAG,CAACoF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAC/B;EAEQpC,kBAAkBA,CAACC,SAAkB,EAAEF,OAAgB;IAC7D,IAAIsC,QAAQ,GAAGtC,OAAO,IAAI,8CAA8C;IAExE;IACA,QAAQE,SAAS;MACf,KAAK,kBAAkB;QACrBoC,QAAQ,GAAG,kDAAkD;QAC7D;MACF,KAAK,cAAc;QACjBA,QAAQ,GAAG,2DAA2D;QACtE;MACF,KAAK,kBAAkB;QACrBA,QAAQ,GAAG,yDAAyD;QACpE;MACF,KAAK,oBAAoB;QACvBA,QAAQ,GAAG,qDAAqD;QAChE;MACF,KAAK,iBAAiB;QACpBA,QAAQ,GAAG,sCAAsC;QACjD;MACF,KAAK,eAAe;QAClBA,QAAQ,GAAG,uEAAuE;QAClF;MACF,KAAK,mBAAmB;QACtBA,QAAQ,GAAG,kDAAkD;QAC7D;MACF,KAAK,mBAAmB;QACtBA,QAAQ,GAAG,8CAA8C;QACzD;MACF,KAAK,kBAAkB;MACvB;QACEA,QAAQ,GAAGtC,OAAO,IAAI,oEAAoE;QAC1F;;IAGJ,IAAI,CAACxH,YAAY,CAACiF,GAAG,CAAC6E,QAAQ,CAAC;IAE/B;IACA,IAAI,CAAC5G,kBAAkB,CAACwC,UAAU,CAAC,qBAAqB,EAAE;MACxDqE,SAAS,EAAErC,SAAS,IAAI,SAAS;MACjCsC,YAAY,EAAEF,QAAQ;MACtBG,eAAe,EAAEzC,OAAO;MACxB7B,WAAW,EAAE,IAAI,CAAC5B,WAAW;MAC7ByC,MAAM,EAAE,IAAI,CAAC1I,WAAW;MACxB2I,QAAQ,EAAE,IAAI,CAAClI,QAAQ;KACxB,CAAC;EACJ;EAEA8C,WAAWA,CAAA;IACT,IAAI,CAAC6B,kBAAkB,CAACwC,UAAU,CAAC,0BAA0B,EAAE;MAC7DC,WAAW,EAAE,IAAI,CAAC5B,WAAW;MAC7BmG,gBAAgB,EAAE,IAAI,CAAC5G,cAAc;KACtC,CAAC;IAEF,IAAI,CAACH,SAAS,CAACmE,KAAK,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;KACkB,CAAC;EAChC;EAEA;EACAxF,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACzD,QAAQ,EAAE;MACrB,KAAK,MAAM;QAAE,OAAO,aAAa;MACjC,KAAK,YAAY;QAAE,OAAO,aAAa;MACvC,KAAK,kBAAkB;QAAE,OAAO,aAAa;MAC7C;QAAS,OAAO,aAAa;;EAEjC;EAEAwD,gBAAgBA,CAAA;IACd,OAAO,aAAa,IAAI,CAACxD,QAAQ,EAAE,CAAC4L,WAAW,EAAE,CAACN,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;EACzE;EAEAhM,cAAcA,CAACsL,MAAc;IAC3B,OAAO,IAAIiB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACrB,MAAM,CAAC;EACnB;;;uBAvWWrG,2BAA2B,EAAAxF,EAAA,CAAAmN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAAmN,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAvN,EAAA,CAAAmN,iBAAA,CAAAK,EAAA,CAAAC,0BAAA,GAAAzN,EAAA,CAAAmN,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA3N,EAAA,CAAAmN,iBAAA,CA8B5B1N,eAAe;IAAA;EAAA;;;YA9Bd+F,2BAA2B;MAAAoI,SAAA;MAAAC,QAAA,GAAA7N,EAAA,CAAA8N,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBxCpO,EAAA,CAAAC,cAAA,yBAAoB;UAI+BD,EAAA,CAAAwD,UAAA,mBAAA8K,6DAAA;YAAA,OAASD,GAAA,CAAAtK,WAAA,EAAa;UAAA,EAAC;UAClE/D,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAK9BH,EAAA,CAAAkB,UAAA,IAAAqN,0CAAA,kBAUM;UAGNvO,EAAA,CAAAkB,UAAA,IAAAsN,0CAAA,mBAuIM;UACRxO,EAAA,CAAAG,YAAA,EAAM;;;UArJEH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAqB,UAAA,SAAAgN,GAAA,CAAArI,cAAA,GAAsB;UAatBhG,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAqB,UAAA,UAAAgN,GAAA,CAAArI,cAAA,GAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}