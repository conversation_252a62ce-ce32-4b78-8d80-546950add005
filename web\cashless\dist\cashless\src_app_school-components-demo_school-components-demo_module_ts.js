"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_school-components-demo_school-components-demo_module_ts"],{

/***/ 97293:
/*!****************************************************************************************!*\
  !*** ./src/app/school-components-demo/components/demo-button/demo-button.component.ts ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DemoButtonComponent: () => (/* binding */ DemoButtonComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _schools_button_components_dropdown_button_dropdown_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-button/components/dropdown-button/dropdown-button.component */ 17566);




class DemoButtonComponent {
  constructor() {
    this.MerchantStatus = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantStatus;
  }
  ngOnInit() {
    this.DropDownOptions = [this.MerchantStatus.Active];
  }
  static {
    this.ɵfac = function DemoButtonComponent_Factory(t) {
      return new (t || DemoButtonComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: DemoButtonComponent,
      selectors: [["demo-button"]],
      decls: 13,
      vars: 2,
      consts: [[1, "row"], [1, "col-12"], [1, "col-12", "col-sm-6", "col-lg-2"], ["text", "Action"], ["label", "Status", "waitForConfirm", "true", 1, "mr-3", 3, "values", "currentValue"]],
      template: function DemoButtonComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Schools-button");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 0)(5, "div", 2)(6, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, "basic-button");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](8, "basic-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 2)(10, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "dropdown-button");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "dropdown-button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("values", ctx.DropDownOptions)("currentValue", ctx.MerchantStatus.Active);
        }
      },
      dependencies: [_schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__.BasicButtonComponent, _schools_button_components_dropdown_button_dropdown_button_component__WEBPACK_IMPORTED_MODULE_2__.DropdownButtonComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 90357:
/*!****************************************************************************************!*\
  !*** ./src/app/school-components-demo/components/demo-common/demo-common.component.ts ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DemoCommonComponent: () => (/* binding */ DemoCommonComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-common/components/school-panel/school-panel.component */ 74153);


class DemoCommonComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function DemoCommonComponent_Factory(t) {
      return new (t || DemoCommonComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: DemoCommonComponent,
      selectors: [["demo-common"]],
      decls: 17,
      vars: 0,
      consts: [[1, "row"], [1, "col-12"], [1, "col-12", "col-sm-6", "col-lg-2"], ["title", "Title"]],
      template: function DemoCommonComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "Schools-common");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 0)(5, "div", 2)(6, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7, "school-panel");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "school-panel")(9, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "school-panel works!");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "div", 2)(12, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](13, "school-panel (with title)");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](14, "school-panel", 3)(15, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](16, "school-panel with title!");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
        }
      },
      dependencies: [_schools_common_components_school_panel_school_panel_component__WEBPACK_IMPORTED_MODULE_0__.SchoolPanelComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 12449:
/*!************************************************************************************!*\
  !*** ./src/app/school-components-demo/components/demo-form/demo-form.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DemoFormComponent: () => (/* binding */ DemoFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/schools-form/components */ 46282);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-form/components/input-text/input-text.component */ 96930);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);








function DemoFormComponent_form_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "form", 3)(1, "div", 0)(2, "div", 4)(3, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "input-text");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "input-text", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "div", 4)(7, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8, "input-date");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](9, "input-date", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 4)(11, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](12, "input-select-list");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](13, "input-select-list", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx_r0.formGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("error", ctx_r0.firstName.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("error", ctx_r0.startDate.invalid ? ctx_r0.invalidValueError : null);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("values", ctx_r0.selectValues);
  }
}
class DemoFormComponent extends src_app_schools_form_components__WEBPACK_IMPORTED_MODULE_0__.BaseFormComponent {
  constructor() {
    super();
    this.invalidValueError = 'Invalid value entered';
    this.selectValues = [];
  }
  ngOnInit() {
    this.getSelectValues();
    this.createForm();
  }
  createForm() {
    this.formGroup = new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroup({
      firstName: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl('Cashless', [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      startDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl('2022-09-20', [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      day: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl('1')
    });
  }
  get firstName() {
    return this.formGroup.get('firstName');
  }
  get startDate() {
    return this.formGroup.get('startDate');
  }
  /**
   * Get value for dropdown
   */
  getSelectValues() {
    this.selectValues.push({
      key: '1',
      value: 'Monday'
    });
    this.selectValues.push({
      key: '2',
      value: 'Tuesday'
    });
    this.selectValues.push({
      key: '3',
      value: 'Wednesday'
    });
  }
  static {
    this.ɵfac = function DemoFormComponent_Factory(t) {
      return new (t || DemoFormComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: DemoFormComponent,
      selectors: [["demo-form"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 5,
      vars: 1,
      consts: [[1, "row"], [1, "col-12"], [3, "formGroup", 4, "ngIf"], [3, "formGroup"], [1, "col-12", "col-sm-6", "col-lg-2"], ["placeholder", "First Name", "formControlName", "firstName", 3, "error"], ["placeholder", "Start Date", "formControlName", "startDate", 3, "error"], ["formControlName", "day", "placeholder", "Select value", 3, "values"]],
      template: function DemoFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "Schools-form");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, DemoFormComponent_form_4_Template, 14, 4, "form", 2);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.formGroup);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_1__.InputSelectListComponent, _schools_form_components_input_text_input_text_component__WEBPACK_IMPORTED_MODULE_2__.InputTextComponent, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_3__.InputDateComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControlName],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 17389:
/*!**************************************************************************!*\
  !*** ./src/app/school-components-demo/components/demo/demo.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DemoComponent: () => (/* binding */ DemoComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _demo_button_demo_button_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../demo-button/demo-button.component */ 97293);
/* harmony import */ var _demo_form_demo_form_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../demo-form/demo-form.component */ 12449);
/* harmony import */ var _demo_common_demo_common_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../demo-common/demo-common.component */ 90357);




class DemoComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function DemoComponent_Factory(t) {
      return new (t || DemoComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: DemoComponent,
      selectors: [["app-demo"]],
      decls: 4,
      vars: 0,
      consts: [[1, "container-fluid"]],
      template: function DemoComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "demo-button")(2, "demo-form")(3, "demo-common");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
      },
      dependencies: [_demo_button_demo_button_component__WEBPACK_IMPORTED_MODULE_0__.DemoButtonComponent, _demo_form_demo_form_component__WEBPACK_IMPORTED_MODULE_1__.DemoFormComponent, _demo_common_demo_common_component__WEBPACK_IMPORTED_MODULE_2__.DemoCommonComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 83395:
/*!************************************************************!*\
  !*** ./src/app/school-components-demo/components/index.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DemoButtonComponent: () => (/* reexport safe */ _demo_button_demo_button_component__WEBPACK_IMPORTED_MODULE_1__.DemoButtonComponent),
/* harmony export */   DemoCommonComponent: () => (/* reexport safe */ _demo_common_demo_common_component__WEBPACK_IMPORTED_MODULE_2__.DemoCommonComponent),
/* harmony export */   DemoComponent: () => (/* reexport safe */ _demo_demo_component__WEBPACK_IMPORTED_MODULE_0__.DemoComponent),
/* harmony export */   DemoFormComponent: () => (/* reexport safe */ _demo_form_demo_form_component__WEBPACK_IMPORTED_MODULE_3__.DemoFormComponent)
/* harmony export */ });
/* harmony import */ var _demo_demo_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./demo/demo.component */ 17389);
/* harmony import */ var _demo_button_demo_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./demo-button/demo-button.component */ 97293);
/* harmony import */ var _demo_common_demo_common_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./demo-common/demo-common.component */ 90357);
/* harmony import */ var _demo_form_demo_form_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./demo-form/demo-form.component */ 12449);





/***/ }),

/***/ 38758:
/*!*********************************************************************************!*\
  !*** ./src/app/school-components-demo/school-components-demo-routing.module.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolComponentsDemoRoutingModule: () => (/* binding */ SchoolComponentsDemoRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components_demo_demo_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/demo/demo.component */ 17389);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);




const routes = [{
  path: '',
  component: _components_demo_demo_component__WEBPACK_IMPORTED_MODULE_0__.DemoComponent
}];
class SchoolComponentsDemoRoutingModule {
  static {
    this.ɵfac = function SchoolComponentsDemoRoutingModule_Factory(t) {
      return new (t || SchoolComponentsDemoRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: SchoolComponentsDemoRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](SchoolComponentsDemoRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 85871:
/*!*************************************************************************!*\
  !*** ./src/app/school-components-demo/school-components-demo.module.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolComponentsDemoModule: () => (/* binding */ SchoolComponentsDemoModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _school_components_demo_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./school-components-demo-routing.module */ 38758);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components */ 83395);
/* harmony import */ var _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../schools-form/schools-form.module */ 97162);
/* harmony import */ var _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../schools-common/schools-common.module */ 53943);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);



// components

// modules



class SchoolComponentsDemoModule {
  static {
    this.ɵfac = function SchoolComponentsDemoModule_Factory(t) {
      return new (t || SchoolComponentsDemoModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineNgModule"]({
      type: SchoolComponentsDemoModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _school_components_demo_routing_module__WEBPACK_IMPORTED_MODULE_0__.SchoolComponentsDemoRoutingModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_1__.SchoolsButtonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsFormModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsCommonModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsetNgModuleScope"](SchoolComponentsDemoModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_2__.DemoComponent, _components__WEBPACK_IMPORTED_MODULE_2__.DemoButtonComponent, _components__WEBPACK_IMPORTED_MODULE_2__.DemoFormComponent, _components__WEBPACK_IMPORTED_MODULE_2__.DemoCommonComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.CommonModule, _school_components_demo_routing_module__WEBPACK_IMPORTED_MODULE_0__.SchoolComponentsDemoRoutingModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_1__.SchoolsButtonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_3__.SchoolsFormModule, _schools_common_schools_common_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsCommonModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_school-components-demo_school-components-demo_module_ts.js.map