﻿using Newtonsoft.Json;

namespace Schools.BLL.Classes;

public class PaymentHistoryDto
{
    [JsonProperty(PropertyName = "PaymentId")]
    public int PaymentId { get; set; }

    [JsonProperty(PropertyName = "UserId")]
    public int? UserId { get; set; }

    [JsonProperty(PropertyName = "TopupAmount")]
    public decimal? TopupAmount { get; set; }

    [JsonProperty(PropertyName = "UpdatedBalance")]
    public decimal? UpdatedBalance { get; set; }

    [JsonProperty(PropertyName = "DateCreatedUtc")]
    public string DateCreatedUtc { get; set; }

    [JsonProperty(PropertyName = "Description")]
    public string Description { get; set; }

    [JsonProperty(PropertyName = "OrderAmount")]
    public decimal? OrderAmount { get; set; }

    [JsonProperty(PropertyName = "PaymentType")]
    public string PaymentType { get; set; }

    [JsonProperty(PropertyName = "Declined")]
    public bool? Declined { get; set; }

    /// <summary>
    /// Activate the "Check balance" feature only if this is true (otherwise the calculation may be wrong)
    /// </summary>
    [JsonProperty(PropertyName = "CanCheckBalance")]
    public bool? CanCheckBalance { get; set; }
}

public class PaymentHistoryEntity
{
    public int PaymentId { get; set; }
    public int? UserId { get; set; }
    public string Description { get; set; }
    public decimal? Amount { get; set; }
    public decimal? TopupAmount { get; set; }
    public string ToAccount { get; set; }
    public string FromAccount { get; set; }
    public decimal? UpdatedBalance { get; set; }
    public string DateCreatedUtc { get; set; }
    public bool? Declined { get; set; }
}
