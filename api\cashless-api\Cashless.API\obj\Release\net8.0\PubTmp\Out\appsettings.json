{
  "BalanceRefundExternalId": "D96939C8-4FBE-4822-A39C-4CD1BE3F18A7",
  "DemoCanteenIds": "",
  "FlagEnforceFeeApiAmount": "false",
  "NewOrderProcessSchoolIds": "10151,10152,10033,10115,10116,10120,10117,10118,10119,20225,20236,20237",
  "SchoolsApiBaseUri": "https://spriggy-api-uat-dyhbhzhebzc3dwa3.australiasoutheast-01.azurewebsites.net/api/",
  "activeFeeUniform": "false",
  "adminCanteenAccountId": "17600",
  "apimKey": "3374e314676640e0bdbff6c8d475da3a",
  "ConnectionStrings": {
    //"appConstr": "Server=tcp:test-schools-db-instance.cnwu7csqwjp0.ap-southeast-2.rds.amazonaws.com,1433;Initial Catalog=test-schools;Persist Security Info=False;User ID=schools_api_test_schools_app;Password=**********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;",
    //"appConstrRep": "Server=tcp:test-schools-db-instance.cnwu7csqwjp0.ap-southeast-2.rds.amazonaws.com,1433;Initial Catalog=test-schools;Persist Security Info=False;User ID=schools_api_test_schools_app;Password=**********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;",
    "appConstr": "Server=tcp:clc-anthro.database.windows.net;Initial Catalog=test-schools;Persist Security Info=False;User ID=clclogin;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;",
    "appConstrRep": "Server=tcp:clc-anthro.database.windows.net;Initial Catalog=test-schools;Persist Security Info=False;User ID=clclogin;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "APPINSIGHTS_INSTRUMENTATIONKEY": "7f04c781-8ff5-4927-8238-fc712da8e6c5",
  "blockCustomMenuNamesSchoolIds": "8,",
  "cashlessApiSecret": "!Secret+Handshake!",
  "feeAccountId": "284",
  "firebaseSettings": "{\n  \"type\": \"service_account\",\n  \"project_id\": \"test-eea49\",\n  \"private_key_id\": \"ca04f9b188651e6ed47ff8fa0c71e3d0b66fcbef\",\n  \"private_key\": \"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCw+J9eI49HiBLc\\nuSbM7amGCAkSny/ttzcDCQ29yED/e1qpTce2TA85zQK61Czd5zIQxVdXu6+TIenq\\nFlekOyxKfKFojGqBBzaGg/7AHRoZvuMjPHmXokIT5RbbMcVK2HCpUhDimtmZIKAV\\n/WKlvd39nKT2flWhf30WFYY99U05ABAJ7q4ksASOr8Pj9U8YO3kvI4U1ZOPvTFas\\nD88oIvxvAaCIIsoT3UIly9LujY3pyTF99tIOeBJ77Jdac/2JVju1/Tj6tnDxhf6d\\nvQ+cXCmUA5jVquR+4e/+XMg+Cw/8olG2xszXtiM5d/iOg49+zxsr9ZQP9d0jBXmL\\nHLEckdSNAgMBAAECggEAG7r1k/V7/dAxbnM3xHxIKEepjkix5KylGt1oEREoODA+\\n0lEtBmBwMVUxxI87MZFI685UY5u2BRbXFbZnCMW56LkTPLPl+25jKZxUxos43Aho\\nhIy1G+DRvFbVKEcp/FKigrGoki2EA8XUN+Xit4hjGNqbEcHVjq8QrW11gc9miJP6\\nZBzs0GGIdsQSAc1aiPPpTBWDa1nllXxvQQViIY6agJ/Yp+/3+BnSL3zmytuV6QPQ\\nwrmzcALfGC35e8x3AzSts5LNFbamn6qIhmGlQFhbzYqB8HdyW8knoeuU3OoXZkDa\\nPbyjAvbKvQVo26LyEWSz0xEMi30yL09MNfhUeGPcwQKBgQDgH1yeqtluZwWauQWQ\\nuW83KXMYDfv9i/Au5j1UKkcdNznzSoLHWgM1m4f5vS2wPpkp6lQCYSNaA8Iq5HiH\\n0RBwOX3PTXysW2hQX+xiH+yM7k6LO9hQV0t7BE8Ak8o5/6XQQyPTWSfB04xw8zip\\nEJSFRSiyrPdsAEm/pItQqJkOTQKBgQDKJGjxNgLDpH0QZd2LHxzFi/EREGrjv6PB\\n4J4B/R+1O2xhaBAuGbCCrrJARFeTnX7/RKZqZgh6vpNLh4PE2h1szOyVoco4y+iK\\nRw86GN+2hqAxx4hIZxfu0hM+qBEgd4R+C/RflNwg2YRzwYNomaxJXQwFs2VOZ0tN\\nlBClXpF/QQKBgC1KqTD3XdFNyHgoA9PnZW5EVhn6WX43lWYk6SflE1H4IyHjYOJg\\ncl2W/LefX2hKfjFWRXvlnXtHXynD2ge4XBYRyDboddVu83mqqN57jRY94mmIwoIZ\\n4gsOJ9j6BNgLrbqUltzTG5Gz6qG4xS6FRz5XZ44UHjwZIs4ztmOqbqPRAoGAHzbQ\\nHa9BbgSQa1/PfhQn1Tlu2Du4xcJp/x44SAZWRx4N9lyjYvsEjql7TQFApMLFQZXQ\\nPM3UsV+MTzV124fy00Ixpkl9eJfEKNYgO9Brezv+sLEnEkcZIv+9iQda3bCW/P/L\\nlJbgBxKPqXBQYm6YdBuyJ480LY81yZz7lQ2tWgECgYEAuaU1o2Nqmvsh/QWbH1XY\\nxQ4rokzPv+abhxlY3lJdHnbsjJ5CCPnn3oE2WscQctuLJlYgz+42JeGy1a5U+LmB\\ndmd/TFxnXv+0eOE2idvd6+CO1ec13tVOSBFbHqHr2FhOyJPnu9/YOZfQdtognGua\\nszzciWAd4noOfxK8vOvCMxE=\\n-----END PRIVATE KEY-----\\n\",\n  \"client_email\": \"*******\",\n  \"client_id\": \"104923907787906569675\",\n  \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n  \"token_uri\": \"https://oauth2.googleapis.com/token\",\n  \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n  \"client_x509_cert_url\": \"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-97ak9%40test-eea49.iam.gserviceaccount.com\"\n}\n",
  "launchDarklyKey": "****************************************",
  "payEnv": "sandbox",
  "payMerchId": "s5n9yfg99dnktsxw",
  "payPrivKey": "369fe5de356400e4f766d669807519d0",
  "payPubKey": "5yszvbx5z9fhnzk7",
  "processOrderUrl": "https://prod-28.australiaeast.logic.azure.com:443/workflows/a2eeaebd800144ca8960dc2eab803c56/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=Natu4H0ojnucGwhnyQyNgJAd69OeQ_ZezxLYwIEGj6c",
  "reactNativeUsersFirebaseIds": "uUWXtNrRpIZeW5vMw91jeHGVut13;dPfcT72PEdbCeHPfEkqTeDXafNp1;UVq2yS5E4QcXVlsUtI6y8Is3mzZ2;DTnRaZRxLJWhLfFbfaEfu7eKtQf2;pmgYTxPEF6RYRzKDBzGwCPRTE9n1;ATjt5KEFPZfBPVesZ7fNMjYKmMx2;AfQb61kJYDRO4Cu71LUFgPgjPzF3;LpNmMYwsx6VZHhAvIpFCyokdmVE2;FRTOVHDX9JbgbBy9h7XkaiV6wiQ2",
  "sbqueue": "Endpoint=sb://ccsbuat.servicebus.windows.net/;SharedAccessKeyName=QSender;SharedAccessKey=iNpL0JOrFIqIribfEn8p0GKET1cUMsUHiKDjzrOKt/A=",
  "schoolsAuditBaseUrl": "https://sps-schools-audit-uat.azurewebsites.net",
  "schoolsGatewayKey": "0de4cd7a6a1f495980d9ea08ce55623c",
  "schoolsOrdersBaseUrl": "https://sps-aue-func-orders-uat.azurewebsites.net",
  "scoreCapacity": "20",
  "scoreConfigUri": "paymentsapp/api/configuration/cashless/471V6bFVrC",
  "scoreMedianFirstRetryDelay": "1",
  "scoreQueueLength": "30",
  "scoreRetryCount": "3",
  "scoreToken": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImF1dGgiOiJST0xFX0FETUlOLFJPTEVfVVNFUiIsImV4cCI6MTU2MzU5MTUwOH0.iNOjnaFmDt8yXyN6YSZP1E2t7WwyYFaYjDeZTC2-a4056JolG1apYr0UOF1czKH74yk1KAPTlw3JjOBxMo78gA",
  "scoreUrl": "http://spriggy-core/",
  "stripeApiKey": "sk_test_51OmnglBx28ApjzE1jspJwtWJ3YdVwmS40udludhJguQPRfRnpGFsGbH4zI4x5ysqk3F3IUQdmAttLfm6XFmFcBLD00eSCpmAgT",
  "stripeWebhookSecret": "whsec_eKXtbcP0LynnznNIOLs2JFtCt26ZxUTw",
  "intercomHMACSecret": "Y11WLeB1bMb6uBsJApLfLOdrgbNgPsgaVI9I94Yy",
  "intercomHMACSecretIOS": "LKfwEwn2Kbh7mSCTlxw6JM4tITSBiqYY3Z30E9ww",
  "intercomHMACSecretAndroid": "QacENif5doOI9V2l_xXPMam-w3LgVsRm_RNCdoJj",
  "sendgrid-key": "*********************************************************************",
  "sendgrid-template-account-activation": "d-67d09eefa47b48a28d4097f8a7e2cebd",
  "sendgrid-template-order-failed": "d-17ff4e5128fa4747b6ec9ec72fa83326",
  "sendgrid-template-order-success": "d-f00c1c7f452b4de196d9a808a730e72e",
  "sendgrid-template-order-uniform-ready": "d-56ba8c3f966b4287a8dd2e14217c00f7",
  "sendgrid-template-printed-alert": "",
  "sendgrid-template-order-cancelled": "d-c1f620182ef44d12a9316f48037cafe7",
  "sendgrid-template-event-order-changed": "d-eb12a354c00e47a48a069da9f91e8a06",
  "sendgrid-template-reset-password": "d-095e48c072014bfbabd6d4a226d12695",
  "serviceBusConn": "Endpoint=sb://schools-sb-uat.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=biMpxhOxqN4TVbr9iaW4wbww6gsZYZmMbFqHU5+P/l8=",
  "spriggyEnv": "PROD",
  "storageConstr": "DefaultEndpointsProtocol=https;AccountName=spriggyuatstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "supportedVersionsBlock": "*********",
  "supportedVersionsWarning": "*********",
  "swaggerPwd": "Rtop-90_wszVl4358",
  "swaggerUser": "swaggerSchools",
  "systemUserId": "0",
  "xeroCallbackUri": "https://uat.cashless.school/canteen/settings",
  "xeroClientId": "0E79A72BEE9847C3A7B8C3A58FD5189B",
  "brazeApiKey": "733fa756-0457-4a49-8199-577347df15ab",
  "brazeUrl": "rest.iad-03.braze.com",
  "brazePublishEventCampaignId": "6ee41e70-788c-4fea-a713-117505e6b71a",
  "IS_STRIPE_GATEWAY_ENABLED": "true"
}