using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Reflection;
using Amazon.SQS;
using Cashless.APIs.Attributes;
using Cashless.APIs.Controllers;
using Cashless.APIs.Extensions;
using Cashless.APIs.Filters;
using Cashless.APIs.HealthChecks;
using Schools.BLL.Helpers;
using Cashless.APIs.Hydrators;
using Schools.BLL.Services;
using Schools.BLL.Classes.Orders.Events;
using Schools.BLL.Extensions;
using FirebaseAdmin;
using MassTransit;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Schools.DAL.Extensions;
using Schools.Orders.Created.Consumers;
using Schools.Orders.Created.Consumers.Definitions;
using Schools.Orders.Edited.Consumers;
using Schools.Orders.Edited.Consumers.Definitions;
using Schools.DAL.Entities;
using Schools.BLL.Classes;
using Schools.BLL;
using Stripe;
using Event = Stripe.Event;
using Schools.BLL.Classes.Messaging;
using Schools.BLL.Services.Consumers.Notify;
using Schools.BLL.Services.Consumers.Notify.Definitions;

namespace Cashless.APIs;

public class Startup
{
    private readonly IConfiguration _config;
    private readonly IWebHostEnvironment _env;
    private readonly ILogger<Startup> _logger;

    /// <summary>
    /// Create a Startup class to configure the application
    /// </summary>
    public Startup(IConfiguration config, IWebHostEnvironment env, ILoggerFactory loggerFactory, ILogger<Startup> logger)
    {
        logger.LogInformation("***** Startup [ env : {EnvironmentName}] *****", env.EnvironmentName);

        _config = config;
        _env = env;
        _logger = logger;

        // Initialise the shared logger factory used for
        // static loggers in the application
        ApplicationLogging.LoggerFactory = loggerFactory;
    }

    /// <summary>
    /// This method gets called by the runtime. Use this method to add services to the container
    /// </summary>
    public void ConfigureServices(IServiceCollection services)
    {
        _logger.LogInformation("***** ConfigureServices [ env : {EnvironmentName}] *****", _env.EnvironmentName);

        services.AddApplicationInsightsTelemetry();
        services.AddSingleton<ITelemetryInitializer, TelemetryEnrichment>();

        // Configure integrations using the config loadedv59.4.0
        ConfigureIntegrations();

        // Register all API services
        services.AddApiServices();

        // Register database entity hydrators
        services.AddTransient<IHydrator<List<MenuCategoryEntity>, List<ItemCategory>>, ItemCategoryHydrator>();

        // Register services fo BLL
        services.AddBllServices();

        // Registers services for DAL
        services.AddDataAccessLayer();

        // Configure Firebase
        ConfigAuthentication(services);

        // TODO - Fix compatibility deprecation
#pragma warning disable CS0618, ASP5001
        services.AddMvc(option => option.EnableEndpointRouting = false)
            .SetCompatibilityVersion(CompatibilityVersion.Version_3_0)
            .AddNewtonsoftJson(
                options =>
                {
                    options.SerializerSettings.Converters.Add(new Newtonsoft.Json.Converters.StringEnumConverter());
                });
#pragma warning restore CS0618, ASP5001

        // Register any action filters and other MVC artefacts
        services.AddScoped<CheckApiSecretHeaderActionFilter>();
        services.AddControllers(options =>
        {
            // Apply exception handling to every request
            options.Filters.Add<HandleExceptionAttribute>();
        });

        services.AddCors(options =>
        {
            options.AddPolicy("AllowAllHeaders",
                builder =>
                {
                    builder.AllowAnyOrigin()
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                });
        });

        services.AddMassTransit(x =>
        {
            x.AddConsumer<ProcessOrdersCreatedPaymentConsumer, ProcessOrdersCreatedPaymentConsumerDefinition>();
            x.AddConsumer<ProcessOrdersEditedPaymentConsumer, ProcessOrdersEditedPaymentConsumerDefinition>();

            x.AddConsumer<ManageOrdersCreatedStockConsumer, ManageOrdersCreatedStockConsumerDefinition>();
            x.AddConsumer<ManageOrdersEditedStockConsumer, ManageOrdersEditedStockConsumerDefinition>();

            x.AddConsumer<NotifyUserOrdersCreatedConsumer, NotifyUserOrdersCreatedConsumerDefinition>();
            x.AddConsumer<NotifyUserOrdersEditedConsumer, NotifyUserOrdersEditedConsumerDefinition>();

            x.AddConsumer<EventOrderCancelledConsumer, EventOrderCancelledConsumerDefinition>();
            x.AddConsumer<NotifyEventOrderConsumer, NotifyEventOrderConsumerDefinition>();

            x.AddConsumer<StripeTopupEventConsumer, StripeTopupEventConsumerDefinition>();

            x.UsingAmazonSqs((context, cfg) =>
            {
                cfg.Host(Amazon.RegionEndpoint.APSoutheast2.SystemName, h =>
                {
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-{MessagingConstants.StripeTopupEventsQueue}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");
                    e.ConfigureConsumeTopology = false;
                    e.PublishFaults = false;

                    e.ConfigureConsumer<StripeTopupEventConsumer>(context);
                });

                cfg.Message<OrdersCreated>(x =>
                {
                    x.SetEntityName($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}");
                });
                cfg.Publish<OrdersCreated>();

                cfg.Message<OrdersEdited>(x =>
                {
                    x.SetEntityName($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}");
                });
                cfg.Publish<OrdersEdited>();

                cfg.Message<SchoolEventConsumerDto>(x =>
                {
                    x.SetEntityName($"{_config["environment"]}-schools-{MessagingConstants.EventOrderCancelledTopic}");
                });
                cfg.Publish<SchoolEventConsumerDto>();

                cfg.Message<EditSchoolEventConsumerDto>(x =>
                {
                    x.SetEntityName($"{_config["environment"]}-schools-{MessagingConstants.EventOrderEditedTopic}");
                });
                cfg.Publish<EditSchoolEventConsumerDto>();

                cfg.PublishTopology.TopicAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                cfg.PublishTopology.TopicTags.Add("environment", _config["environment"]);
                cfg.PublishTopology.TopicTags.Add("owner", MessagingConstants.OwnerTagValue);
                cfg.PublishTopology.TopicTags.Add("repository", "cashless-api");
                cfg.PublishTopology.TopicTags.Add("iac-type", "masstransit");
                cfg.PublishTopology.TopicTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}-{MessagingConstants.ProcessPaymentSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}");
                    e.ConfigureConsumer<ProcessOrdersCreatedPaymentConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}-{MessagingConstants.ProcessPaymentSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}");
                    e.Consumer<ProcessOrdersEditedPaymentConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}-{MessagingConstants.ManageStockSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}");
                    e.ConfigureConsumer<ManageOrdersCreatedStockConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}-{MessagingConstants.ManageStockSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}");
                    e.Consumer<ManageOrdersEditedStockConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}-{MessagingConstants.NotifyUserSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersCreatedTopic}");
                    e.ConfigureConsumer<NotifyUserOrdersCreatedConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}-{MessagingConstants.NotifyUserSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.OrdersEditedTopic}");
                    e.Consumer<NotifyUserOrdersEditedConsumer>(context);
                });

                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.EventOrderCancelledTopic}-{MessagingConstants.NotifyUserSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.EventOrderCancelledTopic}");
                    e.Consumer<EventOrderCancelledConsumer>(context);
                });

                // this should work
                cfg.ReceiveEndpoint($"{_config["environment"]}-schools-{MessagingConstants.EventOrderEditedTopic}-{MessagingConstants.NotifyUserSubscription}", e =>
                {
                    e.QueueAttributes.Add(QueueAttributeName.KmsMasterKeyId, _config["KMS_MASTER_KEY_ARN"]);
                    e.QueueTags.Add("environment", _config["environment"]);
                    e.QueueTags.Add("owner", MessagingConstants.OwnerTagValue);
                    e.QueueTags.Add("repository", "cashless-api");
                    e.QueueTags.Add("iac-type", "masstransit");
                    e.QueueTags.Add("aws-account", $"spriggy-{_config["environment"]}");

                    e.Subscribe($"{_config["environment"]}-schools-{MessagingConstants.EventOrderEditedTopic}");
                    e.Consumer<NotifyEventOrderConsumer>(context);
                });
            });
        });

        // Add checks on downstream systems. Failures to send emails
        // or audit logs should not trigger an overall system outage
        services.AddHealthChecks()
                .AddSqlServer(
                    name: "schools-db",
                    connectionString: _config.GetConnectionString("appConstr"))
                .AddSpriggyCore(
                    name: "spriggy-core",
                    baseUrl: _config["scoreUrl"]);

        services.AddHttpClient();
    }

    /// <summary>
    /// Configure Swagger documentation
    /// </summary>
    private void ConfigureSwagger(IServiceCollection services)
    {
        _logger.LogInformation("***** ConfigureSwagger [ env : {EnvironmentName}] *****", _env.EnvironmentName);

        // Add swagger docs in non prod environments
        services.AddSwaggerGen(options =>
        {
            // Update the version to include the release name
            options.SwaggerDoc("v1", new OpenApiInfo { Title = "Cashless API", Version = _config["AppVersion:ReleaseName"] });

            // Add bearer token to Swagger UI
            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please enter a valid token",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "Bearer"
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type=ReferenceType.SecurityScheme,
                                Id="Bearer"
                            }
                        },
                        new string[]{}
                    }
            });

            // Set the comments path for the Swagger JSON and UI
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);

            _logger.LogDebug("Using Swagger XML path: {XmlPath}", xmlPath);

            options.IncludeXmlComments(xmlPath);
        });
    }

    /// <summary>
    /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    /// </summary>
    public void Configure(IApplicationBuilder app, TelemetryConfiguration telemetryConfig)
    {
        _logger.LogInformation("***** Configure [ env : {EnvironmentName}] *****", _env.EnvironmentName);

        // Gracefully shuts down the application. 
        // Waits for incoming requests to be fully processed before shutting down. 
        // Thus does not result in HTTP 504 Gateway Timeout error when pods are shutting down.
        // https://github.com/OleConsignado/graceterm/blob/master/Source/Graceterm/GracetermMiddleware.cs
        //app.UseShutdownService();

        // Set the invariant culture for all environments. Specific cultures
        // e.g. "en-AU" may not be present in Docker containers and lightweight
        // Linux distributions
        CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
        CultureInfo.DefaultThreadCurrentUICulture = CultureInfo.InvariantCulture;

        // Remove telemetry in non prod environments if asked
        if (!_env.IsPRODAWS() && Convert.ToBoolean(_config["disableTelemetry"]))
        {
            // https://docs.microsoft.com/en-us/azure/azure-monitor/app/asp-net-core
            _logger.LogInformation("Disabling Application Insights telemetry");

            telemetryConfig.DisableTelemetry = true;
        }

        // Use strict transport in prod environments
        if (_env.IsPRODAWS())
        {
            app.UseHsts();
        }

        // Add verbose health check JSON output
        app.UseHealthChecks("/health-check", new HealthCheckOptions()
        {
            ResponseWriter = HealthCheckResponseWriter.WriteResponse,
        });

        // Register a custom exception handler to log
        // errors and send them to Application Insights
        //
        // Use exception handling filter
        // app.UseExceptionHandler("/api/Error/HandleError");

        // Set CORS policy
        app.UseCors(builder => builder.AllowAnyOrigin()
                                        .AllowAnyHeader()
                                        .AllowAnyMethod());

        // Allow middleware to read the HTTP request body multiple times
        app.Use(next => context =>
        {
            context.Request.EnableBuffering();

            return next(context);
        });

        app.UseAuthentication();
        app.UseHttpsRedirection();
        app.UseMvc(routes =>
        {
            routes.MapRoute(
                name: "default",
                template: "{controller=Home}/{action=Index}/{id?}");

            // New code to handle requests like '/Users/<USER>/BuyProduct/2'
            // routes.MapRoute(
            //     // Name of the new route, we'll need it later to generate URLs in the templates
            //     name: "twoids",
            //     // Route pattern
            //     template: "{controller}/{action}/{id}/{id2}");
        });

        if (!_env.IsPRODAWS())
        {
            _logger.LogDebug("Adding Swagger docs");

            app.UseSwaggerAuthorized();
            app.UseSwagger();

            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", $"Cashless API V1 ({_env.EnvironmentName})");
            });
        }
    }

    /// <summary>
    /// Read the Firebase settings from App Configuration and initilise the FirebaseApp instance
    /// </summary>
    private void ConfigAuthentication(IServiceCollection services)
    {
        _logger.LogInformation("***** ConfigAuthentication [ env : {EnvironmentName}] *****", _env.EnvironmentName);

        // Configure JWT authentication
        if (_env.IsPRODAWS())
        {
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = "https://securetoken.google.com/cashless-01";
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "https://securetoken.google.com/cashless-01",
                        ValidateAudience = true,
                        ValidAudience = "cashless-01",
                        ValidateLifetime = true
                    };
                    options.SaveToken = true;
                });
        }
        else if (_env.IsUAT())
        {
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = "https://securetoken.google.com/test-eea49";
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "https://securetoken.google.com/test-eea49",
                        ValidateAudience = true,
                        ValidAudience = "test-eea49",
                        ValidateLifetime = true
                    };
                    options.SaveToken = true;
                });

            ConfigureSwagger(services);
        }
        else
        {
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = "https://securetoken.google.com/test-eea49";
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "https://securetoken.google.com/test-eea49",
                        ValidateAudience = true,
                        ValidAudience = "test-eea49",
                        ValidateLifetime = true
                    };
                    options.SaveToken = true;
                });

            ConfigureSwagger(services);
        }

        // Get the settings to configure Firebase
        var credentialPath = GetFirebaseSettingsFromAppConfiguration();
        if (credentialPath == null)
        {
            credentialPath = GetFirebaseSettingsFromFileSystem();
        }

        _logger.LogDebug("Using Firebase application credentials at {CredentialPath}", credentialPath);

        // Configure Firebase according to the env.
        Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", credentialPath);
        FirebaseApp.Create();
    }

    /// <summary>
    /// Fetch the settings from App Configuration and write them to a local file
    /// </summary>
    private string GetFirebaseSettingsFromAppConfiguration()
    {
        string directory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        string filename = "firebase-settings.json";

        try
        {
            // Fetch the Firebase config from App Configuration
            string json = _config["firebaseSettings"];
            if (string.IsNullOrWhiteSpace(json))
            {
                throw new ApplicationException("Unable to load Firebase settings from App Configuration");
            }

            _logger.LogDebug("Writing {Bytes:D} bytes of Firebase settings to file: {Filename}", json.Length, filename);

            // Write the JSON loaded to a file
            return Utils.WriteTextFile(directory, filename, json);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to fetch Firebase settings / write them to local file system at: {Directory}", directory);

            return null;
        }
    }

    /// <summary>
    /// Fetch the path to the file to load from the file system
    /// </summary>
    private string GetFirebaseSettingsFromFileSystem()
    {
        string directory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

        string envName = null;
        if (_env.IsPRODAWS())
        {
            envName = "prod";
        }
        else if (_env.IsUAT())
        {
            envName = "uat";
        }
        else
        {
            envName = "dev";
        }

        return Path.Combine(directory, $"firebase-{envName}-settings.json");
    }

    /// <summary>
    /// Load configuration parameters from App Configuration in Azure and update
    /// different services ahead of using them
    ///
    /// TODO -  Load all settings into an injectable IConfigService that other
    ///         components can fetch these settings from
    /// </summary>
    private void ConfigureIntegrations()
    {
        _logger.LogInformation("***** ConfigureIntegrations [ env : {EnvironmentName}] *****", _env.EnvironmentName);

        try
        {
            // Apply config to payment gateway
            ScoreApiClient.ScoreUrl = _config["scoreUrl"];
            ScoreApiClient.ScoreToken = _config["scoreToken"];
            ScoreApiClient.ScoreConfigUri = _config["scoreConfigUri"];
            ScoreApiClient.ApimKey = _config["apimKey"];
            ScoreApiClient.Capacity = Int32.Parse(_config["scoreCapacity"]);
            ScoreApiClient.QueueLength = Int32.Parse(_config["scoreQueueLength"]);
            ScoreApiClient.MedianFirstRetryDelay = Int32.Parse(_config["scoreMedianFirstRetryDelay"]);
            ScoreApiClient.RetryCount = Int32.Parse(_config["scoreRetryCount"]);
            ScoreApiClient.CheckApiSettings();

            StripeService.IsStripeGatewayEnabled = bool.Parse(_config["IS_STRIPE_GATEWAY_ENABLED"]);
            StripeConfiguration.ApiKey = _config["stripeApiKey"];

            // Fees Api configuration
            var IsValidFeeFlag = bool.TryParse(_config["FlagEnforceFeeApiAmount"], out bool EnforceFeeApiAmount);
            if (IsValidFeeFlag)
            {
                FeeApiService.EnforceFeeApiAmount = EnforceFeeApiAmount;
            }
            else
            {
                FeeApiService.EnforceFeeApiAmount = false;
            }

            // Merchants to exclude from invoicing exports
            SchoolMerchantService.DemoCanteenIds = _config["DemoCanteenIds"];
            Schools.BLL.Services.InvoiceService.DemoCanteenIds = _config["DemoCanteenIds"];

            // Payment provider
            BraintreeConfiguration.MerchantId = _config["payMerchId"];
            BraintreeConfiguration.PrivateKey = _config["payPrivKey"];
            BraintreeConfiguration.PublicKey = _config["payPubKey"];
            BraintreeConfiguration.Environment = _config["payEnv"];
            BraintreeConfiguration.MerchantName = _config["payMerchName"];
            BraintreeConfiguration.CheckPaymentSettings();

            // Logic App configuration
            LogicAppService.ProcessOrderUrl = _config["processOrderUrl"];
            CheckApiSecretHeaderActionFilter.CashlessApiSecret = _config["cashlessApiSecret"];

            // SendGrid
            SendGridService.APIKey = _config["sendgrid-key"];
            SendGridService.PlaceOrderSuccess = _config["sendgrid-template-order-success"];
            SendGridService.PlaceOrderFailed = _config["sendgrid-template-order-failed"];
            SendGridService.UniformOrderReady = _config["sendgrid-template-order-uniform-ready"];
            SendGridService.OrdersNotPrintedAlert = _config["sendgrid-template-printed-alert"];
            SendGridService.ResetPassword = _config["sendgrid-template-reset-password"];
            SendGridService.AccountVerification = _config["sendgrid-template-account-activation"];

            // // Xero config
            // XeroService.ClientId = _config["xeroClientId"];
            // XeroService.ClientSecret = _config["xeroClientSecret"];
            // XeroService.CallbackUri = _config["xeroCallbackUri"];

            // Billing fee account
            BillingService.FeeAccountId = _config["feeAccountId"];

            // Payment refund account
            PaymentService2.BalanceRefundExternalId = _config["BalanceRefundExternalId"];

            // Admin Canteen account
            CanteenService.AdminCanteenAccountId = _config["adminCanteenAccountId"];

            // Schools approved for new order process
            OrderController.NewOrderProcessSchoolIds = _config["NewOrderProcessSchoolIds"];

            // Schools approved for custom menu names
            PrintingController.BlockCustomMenuNamesSchoolIds = _config["blockCustomMenuNamesSchoolIds"];
            NotificationService.BlockCustomMenuNamesSchoolIds = _config["blockCustomMenuNamesSchoolIds"];
        }
        catch (Exception ex)
        {
            _logger.LogError("Error encountered while loading configuration", ex);

            Trace.TraceError("Error encountered while loading configuration: " + ex.Message + "\r\n" + ex.StackTrace);

            throw;
        }
    }
}