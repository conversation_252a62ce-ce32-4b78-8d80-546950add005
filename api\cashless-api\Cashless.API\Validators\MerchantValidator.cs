using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Schools.BLL.Validators;

namespace Cashless.APIs.Validators;

public interface IMerchantValidator
{
    Task ValidateAccessToCanteen(long canteenId);
}

public class MerchantValidator : IMerchantValidator
{

    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ILogger<MerchantValidator> _logger;

    public MerchantValidator(IAuthenticationValidator authenticationValidator, ILogger<MerchantValidator> logger)
    {
        _authenticationValidator = authenticationValidator;
        _logger = logger;
    }

    public async Task ValidateAccessToCanteen(long canteenId)
    {
        await _authenticationValidator.ValidateAccessToCanteen(canteenId);
    }
}