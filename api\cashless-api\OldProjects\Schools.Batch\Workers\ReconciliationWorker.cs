using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Cashless.APIs.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Schools.Batch.Helpers;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.Batch.Workers;

/// <summary>
/// Batch service to reconcile a users account balance
/// </summary>
public class ReconciliationWorker : BackgroundService
{
    // Parameters
    private const string SettingGroup = "Reconciliation";
    private const string SettingName = "UserId";
    private const string DateFormat = "yyyy-MM-dd-HH.mm.ss";

    // Services
    private readonly IHost _host;
    private readonly IHostEnvironment _env;
    private readonly IConfigService _configService;
    private readonly IWorkerHelper _workerHelper;
    private readonly IReconciliationService _reconciliationService;
    private readonly IUserRepository _userRepository;
    private readonly ILogger<ReconciliationWorker> _logger;
    private readonly StreamWriter _writer;

    // Properties
    private int BatchSize;
    private int NumRecords;
    private string DirectoryPath;

    public ReconciliationWorker(IHost host, IHostEnvironment env, IConfigService configService, IWorkerHelper workerHelper, IReconciliationService reconciliationService,
                                IUserRepository userRepository, ILogger<ReconciliationWorker> logger)
    {
        _host = host;
        _env = env;
        _configService = configService;
        _workerHelper = workerHelper;
        _reconciliationService = reconciliationService;
        _userRepository = userRepository;
        _logger = logger;

        // Load parameters from config
        BatchSize = _configService.GetInt("Workers:Reconciliation:BatchSize");
        NumRecords = _configService.GetInt("Workers:Reconciliation:NumRecords");
        DirectoryPath = _configService.GetString("Workers:Reconciliation:DirectoryPath");
        _workerHelper.Init(SettingGroup, SettingName);

        // Create the directory if it does not exist
        if (!Directory.Exists(DirectoryPath))
        {
            Directory.CreateDirectory(DirectoryPath);
        }

        var fileName = "results-" + _env.GetEnvironmentCode() + "-" + DateTime.Now.ToString(DateFormat) + ".csv";

        // Open a file at the given path
        _writer = new StreamWriter(Path.Combine(DirectoryPath, fileName));
    }

    /// <summary>
    /// Do the work required and stop the console app once done
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await ReconcileUsers();

        await _host.StopAsync();
    }

    /// <summary>
    /// Look for all Users in the Schools DB and reconcile
    /// </summary>
    private async Task ReconcileUsers()
    {
        var stopwatch = Stopwatch.StartNew();
        var processed = 0;
        var reconciled = 0;
        var unreconciled = 0;

        // Write header line
        _writer.WriteLine("UserId, User Email, Schools Balance, Spriggy Core Balance");

        // Fetch all Users with ID > last updated User -> need to update this to only get parents
        var minUserId = await _workerHelper.GetLastUserId();
        var users = await _userRepository.GetParentUsersFromId(minUserId, NumRecords);

        _logger.LogInformation("Processing started - Users: {Users}", users.Count());

        foreach (var user in users)
        {
            processed++;

            if (await UpdateReconciliationRecord(user))
            {
                reconciled++;
            }
            else
            {
                unreconciled++;
            }

            if (processed % BatchSize == 0)
            {
                _logger.LogInformation("Processing update - Success: {Success}, Failure: {Failure}, Errors: {Error}, Total: {Processed}, Duration: {Duration:0.00}s",
                                            reconciled, unreconciled, _workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);

                await _workerHelper.SetLastUserId(user.UserId);
            }
        }

        if (users.Any())
        {
            await _workerHelper.SetLastUserId(users.Last().UserId);
        }

        await _workerHelper.ShowErrors();

        _logger.LogInformation("Processing completed - Success: {Success}, Failure: {Failure}, Errors: {Error}, Total: {Processed}, Duration: {Duration:0.00}s",
                                    reconciled, unreconciled, _workerHelper.GetErrorCount(), processed, stopwatch.ElapsedMilliseconds / 1000);

        // Close file pointer
        _writer.Close();
    }

    /// <summary>
    /// Reconcile a user account
    /// </summary>
    private async Task<bool> UpdateReconciliationRecord(User user)
    {
        try
        {
            var request = new Schools.BLL.ThirdParty.General.ReconciliationRequest
            {
                UserId = user.UserId
            };

            var result = await _reconciliationService.BeginReconcile(request);

            if (result.IsReconciled)
            {
                _logger.LogDebug("Reconciled user: {User}", user);

                return true;
            }
            else
            {
                _writer.WriteLine(user.UserId + "," + user.Email + "," + result.ReconciledAmount + "," + result.SpriggyCoreBalance);

                _logger.LogError("Failed to reconcile User: {User}", user);

                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reconcile User: {User}", user);

            _workerHelper.AddError(user, ex);

            return false;
        }
    }
}