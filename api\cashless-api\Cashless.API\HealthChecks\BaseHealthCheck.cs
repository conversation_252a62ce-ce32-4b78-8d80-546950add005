using System;
using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Cashless.API.HealthChecks;

/// <summary>
/// Class containing common functionality for health checks
/// </summary>
public abstract class BaseHealthCheck
{
    /// <summary>
    /// Outcomes of a health check
    /// </summary>
    private enum HealthCheckOutcome
    {
        Healthy,
        Degraded,
        Unhealthy
    }

    /// <summary>
    /// Combine a base URL and a URI
    /// </summary>
    public string GetUrl(string baseUrl, string uri)
    {
        return new Uri(new Uri(baseUrl), uri).ToString();
    }

    /// <summary>
    /// Return an Health / Degraded / Unhealthy response with some telemetry
    /// </summary>
    private HealthCheckResult GetResult(Stopwatch stopwatch, object response, HealthCheckOutcome outcome, object operation = null, Dictionary<string, object> data = null)
    {
        // Set defaults
        if (operation == null)
        {
            operation = this.GetType().Name;
        }

        if (data == null)
        {
            data = new Dictionary<string, object>();
        }

        // Add variables
        data.Add("executionTime", stopwatch.ElapsedMilliseconds);

        // Differentiate between exceptions and API errors
        // to avoid deserialisation errors
        if (response is Exception)
        {
            Exception exception = (Exception)response;

            data.Add("exception", new
            {
                Type = exception.GetType().ToString(),
                Message = exception.Message,
                StackTrace = exception.StackTrace
            });
        }
        else
        {
            data.Add("response", response);
        }

        // Return a result
        if (outcome == HealthCheckOutcome.Healthy)
        {
            return HealthCheckResult.Healthy(
                description: $"Call to {operation.ToString()} succeeded",
                data: data);
        }

        if (outcome == HealthCheckOutcome.Degraded)
        {
            return HealthCheckResult.Degraded(
                description: $"Call to {operation.ToString()} is degraded",
                data: data);
        }

        return HealthCheckResult.Unhealthy(
            description: $"Call to {operation.ToString()} failed",
            data: data);
    }

    /// <summary>
    /// Return a healthy result with associated telemetry
    /// </summary>
    protected HealthCheckResult GetHealthyResult(Stopwatch stopwatch, object response, object operation = null, Dictionary<string, object> data = null) => GetResult(stopwatch, response, HealthCheckOutcome.Healthy, operation, data);

    /// <summary>
    /// Return a degraded result with associated telemetry
    /// </summary>
    protected HealthCheckResult GetDegradedResult(Stopwatch stopwatch, object response, object operation = null, Dictionary<string, object> data = null) => GetResult(stopwatch, response, HealthCheckOutcome.Degraded, operation, data);

    /// <summary>
    /// Return an unhealthy result with associated telemetry
    /// </summary>
    protected HealthCheckResult GetUnhealthyResult(Stopwatch stopwatch, object response, object operation = null, Dictionary<string, object> data = null) => GetResult(stopwatch, response, HealthCheckOutcome.Unhealthy, operation, data);
}