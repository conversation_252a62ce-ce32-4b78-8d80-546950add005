﻿using System;
using Newtonsoft.Json;

namespace Schools.BLL.Classes;

public class BillingChild
{
    public BillingChild()
    {
        this.feeState = new FeeState()
        {
            NumberOrders = 0,
            TotalOrders = 0,
        };
        this.NumberFreeOrders = 3;
    }

    [JsonProperty(PropertyName = "studentId")]
    public int StudentId { get; set; }

    [JsonProperty(PropertyName = "feeState")]
    public FeeState feeState { get; set; }

    [JsonProperty(PropertyName = "numberFreeOrders")]
    public Int16 NumberFreeOrders { get; set; }
}
