using System.Collections.Generic;

namespace Schools.BLL.Classes.Payments;

public class OrdersPaymentLinkRequest
{
    public OrdersPaymentLinkRequest()
    {
        OrderIds = new List<OrderCorrelation>();
    }

    public IEnumerable<OrderCorrelation> OrderIds { get; set; }

    public long TransactionId { get; set; }

    public long PaymentId { get; set; }

    public long UserId { get; set; }
}

public class OrderCorrelation
{
    public long OrderId { get; set; }

    public string CorrelationId { get; set; }
}