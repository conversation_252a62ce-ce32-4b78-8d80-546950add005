using System;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes.Fees;

public class SchoolFeeCalculatorConfigResponse
{
    public long SchoolId { get; set; }

    public long MerchantId { get; set; }

    public long SchoolCalculatorConfigurationId { get; set; }

    public long FeeCalculatorId { get; set; }

    public string FeeCalculatorName { get; set; }

    public OrderType CalculatorOrderType { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public bool IsActive { get; set; }
}
