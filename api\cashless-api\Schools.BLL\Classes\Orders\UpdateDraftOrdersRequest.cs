using System.Collections.Generic;
using Schools.BLL.Classes.Payments;
using Schools.BLL.ThirdParty.General;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Orders;

/// <summary>
/// Request sent by the cashless-process-order Logic App
/// </summary>
public class UpdateDraftOrdersRequest
{
    [JsonProperty("Orders")]
    public List<OrderUpsertResponse> Orders { get; set; }

    [JsonProperty("PaymentRequest")]
    public PaymentRequest PaymentRequest { get; set; }

    [JsonProperty("PaymentResponse")]
    public CashlessMakePaymentResponse PaymentResponse { get; set; }
}
