using System;
using System.Collections.Generic;
using System.Linq;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;

namespace Schools.BLL.Classes;

public class OrderPlacement
{
    [Key]
    [JsonProperty(PropertyName = "OrderId")]
    public int OrderId { get; set; }

    [JsonProperty(PropertyName = "StudentId")]
    public int StudentId { get; set; }

    [JsonProperty(PropertyName = "OrderDate")]
    public DateTime OrderDate { get; set; }

    [JsonProperty(PropertyName = "MenuId")]
    public int MenuId { get; set; }

    [JsonProperty(PropertyName = "Items")]
    public List<OrderPlacementItem> Items { get; set; }

    public DateTime GetOrderDate() => OrderDate.Date;
}

public class OrderPlacementItem
{
    [JsonProperty(PropertyName = "MenuItemId")]
    public int MenuItemId { get; set; }

    [JsonProperty(PropertyName = "MenuItemOptionIds")]
    public List<int> MenuItemOptionIds { get; set; }

    [JsonProperty(PropertyName = "Quantity")]
    public int Quantity { get; set; }
}

public class OUtageFix2
{
    [JsonProperty(PropertyName = "ExternalUserId")]
    public string ExternalUserId { get; set; }
}

public class FixUserBalanceOutage
{
    [JsonProperty(PropertyName = "UserId")]
    public int UserId { get; set; }

    [JsonProperty(PropertyName = "ParentExternalUserId")]
    public string ParentExternalUserId { get; set; }

    [JsonProperty(PropertyName = "CanteenExternalUserId")]
    public string CanteenExternalUserId { get; set; }

    [JsonProperty(PropertyName = "TopupDuringIncident")]
    public decimal? TopupDuringIncident { get; set; }

    [JsonProperty(PropertyName = "LatestKnownBalance")]
    public decimal? LatestKnownBalance { get; set; }

    [JsonProperty(PropertyName = "NewPaymentSinceIncident")]
    public decimal? NewPaymentSinceIncident { get; set; }

    [JsonProperty(PropertyName = "NewTopupSinceIncident")]
    public decimal? NewTopupSinceIncident { get; set; }
}

public class OrderCutOffTimes
{
    [JsonProperty(PropertyName = "EarlyCutOffTime")]
    public string EarlyCutOffTime { get; set; }

    [JsonProperty(PropertyName = "MenuCutOffTime")]
    public string MenuCutOffTime { get; set; }

    [JsonProperty(PropertyName = "LateCutOffTime")]
    public string LateCutOffTime { get; set; }
}

public class OrderWithCutOffTimesDto
{
    [JsonProperty(PropertyName = "Order")]
    public Order Order { get; set; }

    [JsonProperty(PropertyName = "CutOffTimes")]
    public OrderCutOffTimes CutOffTimes { get; set; }
}

public class OrderWithCutOffTimes
{
    [JsonProperty(PropertyName = "Order")]
    public dynamic Order { get; set; }

    [JsonProperty(PropertyName = "CutOffTimes")]
    public OrderCutOffTimes CutOffTimes { get; set; }
}