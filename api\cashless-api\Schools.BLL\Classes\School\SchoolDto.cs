﻿using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes;

public class SchoolDto
{
    [JsonProperty(PropertyName = "SchoolId")]
    public int? SchoolId { get; set; }

    [Required]
    [StringLength(200)]
    [JsonProperty(PropertyName = "Name")]
    public string Name { get; set; }

    [Required]
    [Range(1, 8)]
    [JsonProperty(PropertyName = "StateId")]
    public Int16 StateId { get; set; }

    [JsonProperty(PropertyName = "DeactivatedFilters")]
    public string DeactivatedFilters { get; set; }

    [Required]
    [JsonProperty(PropertyName = "IsActive")]
    public bool IsActive { get; set; }

    [Required]
    [JsonProperty(PropertyName = "IsMarketingFree")]
    public bool IsMarketingFree { get; set; }

    // address
    [JsonProperty(PropertyName = "Address")]
    public AddressDto Address { get; set; }

    [JsonProperty(PropertyName = "TotalStudentEst")]
    public short? TotalStudentEst { get; set; }
}

public class CreateSchoolDto : SchoolDto
{
    [JsonProperty(PropertyName = "MerchantId")]
    public int MerchantId { get; set; }
}