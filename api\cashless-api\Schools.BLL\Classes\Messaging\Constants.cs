namespace Schools.BLL.Classes.Messaging;

public static class MessagingConstants
{
    public const string OrdersCreatedTopic = "orders-created";
    public const string OrdersEditedTopic = "orders-edited";
    public const string EventOrderCancelledTopic = "event-order-cancelled";
    public const string EventOrderEditedTopic = "event-order-date-changed";
    public const string ProcessPaymentSubscription = "process-payment";
    public const string ManageStockSubscription = "manage-stock";
    public const string NotifyUserSubscription = "notify-user";
    public const string OwnerTagValue = "spriggy-schools";
    public const string StripeTopupEventsQueue = "schools-stripe-topup-events";
}