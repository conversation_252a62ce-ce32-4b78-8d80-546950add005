using System.Collections.Generic;
using Schools.BLL.Classes.Fees;
using Schools.BLL.Exceptions;
using Schools.DAL.Extensions.Entities;

namespace Schools.BLL.Assemblers;

public static class FeeCalculatorMapper
{
    public static FeeCalculatorResponse MapToFeeCalculatorResponse(FeeCalculatorEntity entity)
    {
        return new FeeCalculatorResponse()
        {
            CalculatorOrderType = entity.CalculatorOrderType,
            ConfigurationName = entity.ConfigurationName,
            FeeCalculatorId = entity.FeeCalculatorId,
            FeeCalculatorName = entity.Name,
            IsActive = entity.IsActive,
            IsDefault = entity.IsDefault
        };
    }

    public static IEnumerable<FeeCalculatorResponse> MapListToFeeCalculatorResponse(IEnumerable<FeeCalculatorEntity> entities)
    {
        if (entities == null)
        {
            throw new FeeCalculatorConfigurationException(message: "Fee Calculator Configurations must be provided");
        }

        var response = new List<FeeCalculatorResponse>();

        foreach (var e in entities)
        {
            response.Add(MapToFeeCalculatorResponse(e));
        }

        return response;
    }
}
