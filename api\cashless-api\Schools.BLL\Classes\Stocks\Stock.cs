﻿using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes.Stocks
{
    public class UpsertStockRequest : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "Stock")]
        public Stock Stock { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public int CanteenId { get; set; }
    }

    public class RemainingStockRequest : BaseResponse.Response
    {
        [JsonProperty(PropertyName = "StockId")]
        public int? StockId { get; set; }

        [JsonProperty(PropertyName = "Date")]
        public string Date { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public int? CanteenId { get; set; }

        [JsonProperty(PropertyName = "IntegrationStockCode")]
        public string IntegrationStockCode { get; set; }
    }

    public class RemainingStockResponse
    {
        [JsonProperty(PropertyName = "RemainingQuantity")]
        public int? RemainingQuantity { get; set; }
    }

    public class AddRemoveStockRequest
    {
        [JsonProperty(PropertyName = "ItemId")]
        public int ItemId { get; set; }

        [JsonProperty(PropertyName = "StockId")]
        public int? StockId { get; set; }

        [JsonProperty(PropertyName = "CanteenId")]
        public int CanteenId { get; set; }

        [JsonProperty(PropertyName = "MenuId")]
        public int MenuId { get; set; }
    }
}
