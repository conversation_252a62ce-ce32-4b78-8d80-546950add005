{"version": 3, "file": "src_app_canteen_canteen_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACuD;AAEvD;AACyG;AAQnF;AAEtB;AAC4F;AACC;AAE7F;AAC8E;AACiB;AACX;;;AAEpF,MAAMe,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEhB,iHAAuB;EAClCiB,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRG,SAAS,EAAE,MAAM;IACjBC,UAAU,EAAE;GACb,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEf,yDAAgB;IAC3BmB,OAAO,EAAE;MAAEC,QAAQ,EAAEX,iEAAoBA;IAAA;GAC1C,EACD;IACEK,IAAI,EAAE,OAAO;IACbO,YAAY,EAAEA,CAAA,KAAM,iPAA6C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB;GAChG,EACD;IACEV,IAAI,EAAE,UAAU;IAChBO,YAAY,EAAEA,CAAA,KACZ,0SAAqD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,qBAAqB;GAC1F,EACD;IACEX,IAAI,EAAE,SAAS;IACfO,YAAY,EAAEA,CAAA,KAAM,sKAAmC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,aAAa;GAClF,EACD;IACEZ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAET,0EAAoB;IAC/Ba,OAAO,EAAE;MACPC,QAAQ,EAAEX,iEAAoBA;;GAEjC,EACD;IACEK,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAER,8EAAwB;IACnCY,OAAO,EAAE;MAAEQ,IAAI,EAAEjB,gEAAmBA;IAAA;GACrC,EACD;IACEI,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEd,4DAAmBA;GAC/B,EACD;IACEa,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEZ,6DAAoBA;GAChC,EACD;IACEW,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAEX,+DAAsBA;GAClC,EACD;IACEU,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEV,0DAAiBA;GAC7B,EACD;IACES,IAAI,EAAE,QAAQ;IACdO,YAAY,EAAEA,CAAA,KAAM,oIAA2C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB,CAAC;IAC7FC,gBAAgB,EAAE,CAACrB,qGAAsB;GAC1C,EACD;IACEM,IAAI,EAAE,QAAQ;IACdO,YAAY,EAAEA,CAAA,KAAMT,4FAAqB;IACzCO,OAAO,EAAE;MAAEW,SAAS,EAAErB,iEAAoBA;IAAA;GAC3C,EACD;IACEK,IAAI,EAAE,KAAK;IACXO,YAAY,EAAEA,CAAA,KAAM,mRAA2B,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,SAAS;GACtE;CAEJ,EACD;EAAEjB,IAAI,EAAE,6BAA6B;EAAEC,SAAS,EAAEb,mEAA0BA;AAAA,CAAE,CAC/E;AAOK,MAAO8B,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;iBAFpB,CAACxB,qGAAsB,EAAEG,uGAAuB,CAAC;MAAAsB,OAAA,GAFlDnC,yDAAY,CAACoC,QAAQ,CAACrB,MAAM,CAAC,EAC7Bf,yDAAY;IAAA;EAAA;;;sHAGXkC,oBAAoB;IAAAC,OAAA,GAAAE,yDAAA;IAAAC,OAAA,GAHrBtC,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnGiC;AACS;AACvB;AAE3C;AAC2D;AACI;AACF;AACM;AACF;AACC;AACX;AACE;AACQ;AACR;AACE;AACJ;AACE;AACI;AACN;AAES;AACN;AACH;AACiB;AACA;AACM;AAE9E;AAoBsB;;AAuDhB,MAAO4E,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAFb,CAACpC,qDAAQ,CAAC;MAAAL,OAAA,GA5BnBI,yDAAY,EACZE,+DAAmB,EACnBC,uDAAW,EACXC,sDAAc,EACdT,yEAAoB,EACpByB,kEAAa,EACbC,+DAAY,EACZC,gFAAiB,EACjBE,sFAAmB;MACnB;MACAd,6EAAkB,EAClBI,oEAAc,EACdR,0EAAiB,EACjBG,4EAAkB,EAClBM,sEAAe,EACfE,oEAAc,EACdN,kEAAa,EACbC,oEAAc,EACdP,sEAAe,EACfW,kEAAa,EACbH,4EAAkB,EAClBN,wEAAmB,EACnBC,8EAAmB,EACnBU,wEAAgB,EAChBC,kEAAa,EACbI,gFAAiB,EAEwBL,wEAAgB;IAAA;EAAA;;;sHAGhDmB,aAAa;IAAAC,YAAA,GAnDtB3E,yDAAgB,EAChBC,4DAAmB,EACnB6D,4DAAmB,EACnB5D,mEAA0B,EAC1BC,6DAAoB,EACpBC,+DAAsB,EACtBC,0DAAiB,EACjB0D,oEAA2B,EAC3BC,0EAAiC,EACjCC,wEAA+B,EAC/BC,4EAAmC,EACnCC,6DAAoB,EACpBC,8DAAqB,EACrBA,8DAAqB,EACrBC,6DAAoB,EACpBC,0EAAiC,EACjCC,+DAAsB,EACtBC,+DAAsB,EACtBC,kEAAyB;IAAAxC,OAAA,GAGzBI,yDAAY,EACZE,+DAAmB,EACnBC,uDAAW,EACXC,sDAAc,EACdT,yEAAoB,EACpByB,kEAAa,EACbC,+DAAY,EACZC,gFAAiB,EACjBE,sFAAmB;IACnB;IACAd,6EAAkB,EAClBI,oEAAc,EACdR,0EAAiB,EACjBG,4EAAkB,EAClBM,sEAAe,EACfE,oEAAc,EACdN,kEAAa,EACbC,oEAAc,EACdP,sEAAe,EACfW,kEAAa,EACbH,4EAAkB,EAClBN,wEAAmB,EACnBC,8EAAmB,EACnBU,wEAAgB,EAChBC,kEAAa,EACbI,gFAAiB;IAAAxB,OAAA,GAET6B,wEAA+B,EAAEV,wEAAgB,EAAEW,4EAAmC;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;ACrG1B;AACJ;AACK;;;;;;;;;;;ICDvEgB,4DAAA,cAAsC;IAInBA,oDAAA,eAAQ;IAAAA,0DAAA,EAAY;IAC/BA,4DAAA,oBAAuC;IACQA,oDAAA,uBAAe;IAAAA,0DAAA,EAAa;IACzEA,4DAAA,oBAA2C;IAACA,oDAAA,2BAAkB;IAAAA,0DAAA,EAAa;IAIjFA,4DAAA,cAAsC;IAEvBA,oDAAA,kBAAU;IAAAA,0DAAA,EAAY;IACjCA,4DAAA,qBAA4C;IACpBA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IAK1CA,4DAAA,cAAsC;IAEvBA,oDAAA,oBAAY;IAAAA,0DAAA,EAAY;IACnCA,4DAAA,sBAA8C;IACtBA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IACpCA,4DAAA,sBAAsB;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAa;IAI1CA,4DAAA,eAAgE;IACZA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAe;;;;IArCvEA,wDAAA,cAAAK,MAAA,CAAAC,IAAA,CAAkB;IAMfN,uDAAA,GAA+B;IAA/BA,wDAAA,UAAAK,MAAA,CAAAG,aAAA,CAAAC,OAAA,CAA+B;IAC/BT,uDAAA,GAA8B;IAA9BA,wDAAA,UAAAK,MAAA,CAAAG,aAAA,CAAAE,MAAA,CAA8B;;;ADEhD,MAAOzB,oBAAoB;EAQ/B0B,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAPxB,KAAAC,QAAQ,GAAmC,IAAInB,uDAAY,EAAE;IAC7D,KAAAoB,iBAAiB,GAA0B,IAAIpB,uDAAY,EAAE;IAC7D,KAAAqB,WAAW,GAAG,IAAIrB,uDAAY,EAAE;IAChC,KAAAsB,mBAAmB,GAA0B,IAAItB,uDAAY,EAAE;IAEzE,KAAAc,aAAa,GAAGV,iEAAe;EAEsB;EAErDmB,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAIC,QAAQ,GAAG,SAAS;IACxB,IAAI,CAACL,iBAAiB,CAACM,IAAI,CAAC,KAAK,CAAC;IAElC,IAAIC,YAAY,GAAG,IAAI,CAACT,cAAc,CAACU,kBAAkB,EAAE;IAC3D,IAAIC,iBAAiB,GAAG,IAAI,CAACX,cAAc,CAACY,wBAAwB,EAAE,KAAK,MAAM;IACjF,IAAI,CAACR,mBAAmB,CAACI,IAAI,CAACG,iBAAiB,CAAC;IAEhD,IAAIF,YAAY,IAAIA,YAAY,IAAIF,QAAQ,EAAE;MAC5CA,QAAQ,GAAGE,YAAY;MACvB,IAAI,CAACP,iBAAiB,CAACM,IAAI,CAAC,KAAK,CAAC;;IAGpC,IAAI,CAACd,IAAI,GAAG,IAAIX,qDAAS,CAAC;MACxB8B,IAAI,EAAE,IAAI7B,uDAAW,CAAC,GAAG,EAAE,CAACC,sDAAU,CAAC6B,QAAQ,CAAC,CAAC;MACjDC,MAAM,EAAE,IAAI/B,uDAAW,CAAC,GAAG,EAAE,CAACC,sDAAU,CAAC6B,QAAQ,CAAC,CAAC;MACnDP,QAAQ,EAAE,IAAIvB,uDAAW,CAACuB,QAAQ,CAAC;MACnCS,iBAAiB,EAAE,IAAIhC,uDAAW,CAAC2B,iBAAiB;KACrD,CAAC;IAEF,IAAI,CAACR,WAAW,CAACK,IAAI,EAAE;IAEvB,IAAI,CAACK,IAAI,CAACI,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACrC,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACL,MAAM,CAACE,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACvC,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACb,QAAQ,CAACU,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACzC,IAAI,CAACjB,iBAAiB,CAACM,IAAI,CAACa,OAAO,CAACF,GAAG,KAAK,QAAQ,CAAC,CAAC;MACtD,IAAI,CAACnB,cAAc,CAACsB,kBAAkB,CAACH,GAAG,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACH,iBAAiB,CAACC,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MAClD,IAAI,CAACf,mBAAmB,CAACI,IAAI,CAACW,GAAG,CAAC;MAClC,IAAI,CAACnB,cAAc,CAACuB,wBAAwB,CAACJ,GAAG,CAAC;IACnD,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,MAAMI,IAAI,GAAG,IAAIrC,kEAAgB,EAAE;IACnCqC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACa,KAAK;IAC3BF,IAAI,CAACG,MAAM,GAAG,IAAI,CAACZ,MAAM,CAACW,KAAK;IAC/B,IAAI,CAACzB,QAAQ,CAACO,IAAI,CAACgB,IAAI,CAAC;EAC1B;EAEA,IAAIX,IAAIA,CAAA;IACN,OAAO,IAAI,CAACnB,IAAI,CAACkC,GAAG,CAAC,MAAM,CAAC;EAC9B;EAEA,IAAIb,MAAMA,CAAA;IACR,OAAO,IAAI,CAACrB,IAAI,CAACkC,GAAG,CAAC,QAAQ,CAAC;EAChC;EAEA,IAAIrB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACb,IAAI,CAACkC,GAAG,CAAC,UAAU,CAAC;EAClC;EAEA,IAAIZ,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACtB,IAAI,CAACkC,GAAG,CAAC,mBAAmB,CAAC;EAC3C;;;uBA5EWvD,oBAAoB,EAAAe,+DAAA,CAAA/C,kEAAA;IAAA;EAAA;;;YAApBgC,oBAAoB;MAAA0D,SAAA;MAAAC,OAAA;QAAA/B,QAAA;QAAAC,iBAAA;QAAAC,WAAA;QAAAC,mBAAA;MAAA;MAAA6B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjClD,4DAAA,aAA6B;UAC3BA,wDAAA,IAAAqD,oCAAA,mBAwCO;UACTrD,0DAAA,EAAM;;;UAzCGA,uDAAA,GAAU;UAAVA,wDAAA,SAAAmD,GAAA,CAAA7C,IAAA,CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCiD;AAEpE;AACgF;AAOjB;;;;;;;;;;;;;;;;IC2DrDN,4DAAA,cAGC;IACKA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAK;IAC/BA,4DAAA,QAAG;IAAAA,oDAAA,GAA0C;IAAAA,0DAAA,EAAI;;;;IAA9CA,uDAAA,GAA0C;IAA1CA,+DAAA,CAAA0D,MAAA,CAAAC,cAAA,CAAAC,qBAAA,CAA0C;;;;;;;;ADzDnD,MAAO1I,sBAAuB,SAAQD,sEAAoB;EAG9D0F,YACUkD,SAAmB,EACpBC,cAA8B,EAC3BC,aAA4B,EAC5BC,WAAwB,EAC3BC,MAAiB;IAExB,KAAK,CAACF,aAAa,EAAED,cAAc,EAAEE,WAAW,EAAEC,MAAM,CAAC;IANjD,KAAAJ,SAAS,GAATA,SAAS;IACV,KAAAC,cAAc,GAAdA,cAAc;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACd,KAAAC,MAAM,GAANA,MAAM;IAPf,KAAAC,IAAI,GAAG,IAAIC,IAAI,EAAE;EAUjB;EAEAlD,QAAQA,CAAA;IACN,IAAI,CAACmD,iBAAiB,GAAGb,4DAAU,CAACc,YAAY;IAChD,IAAI,CAACC,UAAU,CAAC,IAAIhB,wDAAM,EAAE,CAAC;EAC/B;EAEAiB,WAAWA,CAAA;IACT,IAAI,CAACV,SAAS,CAACW,IAAI,EAAE;EACvB;EAEAC,YAAYA,CAACC,YAAoB;IAC/B,IAAI,CAACf,cAAc,GAAGe,YAAY;IAClC,IAAI,CAACJ,UAAU,CAACI,YAAY,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEAL,UAAUA,CAACI,YAAoB;IAC7B,MAAME,qBAAqB,GACxB,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,MAAM,CAACJ,YAAY,IAAIA,YAAY,CAACK,QAAQ,IAAI,IAAI,CAAC,IAAK,EAAE;IAClG,MAAMC,QAAQ,GAAGN,YAAY,CAACO,OAAO,GAAGzB,6EAA4B,CAACkB,YAAY,CAACO,OAAO,CAAC,GAAG,IAAI;IAEjG,IAAI,CAAC3E,IAAI,GAAG,IAAIX,qDAAS,CAAC;MACxBuF,KAAK,EAAE,IAAItF,uDAAW,CAAC8E,YAAY,CAACS,KAAK,IAAI,EAAE,EAAE,CAACtF,sDAAU,CAAC6B,QAAQ,EAAE7B,sDAAU,CAACuF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAChGC,WAAW,EAAE,IAAIzF,uDAAW,CAAC8E,YAAY,CAACY,WAAW,IAAI,EAAE,CAAC;MAC5DC,OAAO,EAAE,IAAI3F,uDAAW,CAACoF,QAAQ,CAAC;MAClCQ,QAAQ,EAAE,IAAI5F,uDAAW,CAAC,IAAI,CAAC6F,oBAAoB,CAACf,YAAY,CAAC;KAClE,CAAC;EACJ;EAEAe,oBAAoBA,CAACf,YAAoB;IACvC,MAAMgB,cAAc,GAAG,CAAChB,YAAY,CAACiB,QAAQ;IAC7C,OAAOjB,YAAY,EAAEK,QAAQ,IAAIW,cAAc;EACjD;EAEAE,SAASA,CAAA;IACP,IAAI,CAACjB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAChB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACW,UAAU,CAAC,IAAIhB,wDAAM,EAAE,CAAC;EAC/B;EAEAuC,oBAAoBA,CAAA;IAClB,OAAO,CAAC,IAAI,CAACvF,IAAI,CAACwF,KAAK;EACzB;;;uBAtDW5K,sBAAsB,EAAA8E,+DAAA,CAAA/C,qDAAA,GAAA+C,+DAAA,CAAAgG,kEAAA,GAAAhG,+DAAA,CAAAgG,iEAAA,GAAAhG,+DAAA,CAAAgG,+DAAA,GAAAhG,+DAAA,CAAAoG,gEAAA;IAAA;EAAA;;;YAAtBlL,sBAAsB;MAAAyH,SAAA;MAAA2D,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwD,gCAAAtD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBnClD,4DAAA,aAAoB;UAECA,wDAAA,qBAAA0G,mEAAA;YAAA,OAAWvD,GAAA,CAAAoB,WAAA,EAAa;UAAA,EAAC;UAAmCvE,0DAAA,EAAkB;UAC/FA,4DAAA,aAAoB;UAGhBA,wDAAA,2BAAA2G,uEAAAC,MAAA;YAAA,OAAiBzD,GAAA,CAAA0D,cAAA,CAAAD,MAAA,CAAsB;UAAA,EAAC,6BAAAE,yEAAAF,MAAA;YAAA,OACrBzD,GAAA,CAAA4D,iBAAA,CAAAH,MAAA,CAAyB;UAAA,EADJ,sBAAAI,kEAAA;YAAA,OAE5B7D,GAAA,CAAAyC,SAAA,EAAW;UAAA,EAFiB;UAGzC5F,0DAAA,EAAgB;UAIrBA,4DAAA,aAAkC;UAK5BA,wDAAA,uBAAAiH,kEAAAL,MAAA;YAAA,OAAazD,GAAA,CAAAsB,YAAA,CAAAmC,MAAA,CAAoB;UAAA,EAAC;UACnC5G,0DAAA,EAAe;UAGlBA,4DAAA,aAAwE;UAG/DA,wDAAA,mBAAAkH,oDAAA;YAAA,OAAS/D,GAAA,CAAAgE,QAAA,EAAU;UAAA,EAAC;UACrBnH,uDAAA,eAAoC;UACtCA,0DAAA,EAAI;UAGNA,4DAAA,gBAA8C;UAE/BA,oDAAA,aAAK;UAAAA,0DAAA,EAAY;UAC5BA,uDAAA,iBAME;UACJA,0DAAA,EAAiB;UAEjBA,4DAAA,0BAAqC;UACxBA,oDAAA,8BAAsB;UAAAA,0DAAA,EAAY;UAC7CA,uDAAA,wBAUY;UACZA,4DAAA,oBAAsB;UAAAA,oDAAA,IAAoC;UAAAA,0DAAA,EAAW;UAGvEA,4DAAA,0BAAqC;UACxBA,oDAAA,2BAAmB;UAAAA,0DAAA,EAAY;UAC1CA,uDAAA,iBAA4F;UAG9FA,0DAAA,EAAiB;UAEjBA,4DAAA,wBAAyC;UACdA,oDAAA,cAAM;UAAAA,0DAAA,EAAI;UAErCA,uDAAA,eAA6B;UAG7BA,wDAAA,KAAAqH,sCAAA,kBAMM;UAENrH,4DAAA,wBAMC;UALCA,wDAAA,uBAAAsH,mEAAA;YAAA,OAAanE,GAAA,CAAAoE,UAAA,EAAY;UAAA,EAAC,yBAAAC,qEAAA;YAAA,OACXrE,GAAA,CAAAgE,QAAA,EAAU;UAAA,EADC,yBAAAM,qEAAA;YAAA,OAEXtE,GAAA,CAAAuE,iBAAA,EAAmB;UAAA,EAFR;UAK3B1H,0DAAA,EAAe;;;;;UArElBA,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAmD,GAAA,CAAA0B,UAAA,CAAwB;UAMC7E,uDAAA,GAA0C;UAA1CA,wDAAA,YAAAA,6DAAA,KAAA4H,GAAA,GAAAzE,GAAA,CAAAwB,cAAA,EAA0C;UAQ7D3E,uDAAA,GAAkB;UAAlBA,wDAAA,cAAAmD,GAAA,CAAA7C,IAAA,CAAkB;UAyBEN,uDAAA,IAAoC;UAApCA,gEAAA,KAAA8H,GAAA,CAAAxF,KAAA,CAAAyF,MAAA,WAAoC;UAK1C/H,uDAAA,GAAyB;UAAzBA,wDAAA,kBAAAgI,GAAA,CAAyB,QAAA7E,GAAA,CAAAe,IAAA;UACJlE,uDAAA,GAAe;UAAfA,wDAAA,QAAAgI,GAAA,CAAe;UAWnDhI,uDAAA,GAAyE;UAAzEA,wDAAA,SAAAmD,GAAA,CAAAQ,cAAA,IAAAR,GAAA,CAAAQ,cAAA,CAAAsE,MAAA,IAAA9E,GAAA,CAAA+E,gBAAA,CAAAC,OAAA,CAAyE;UAW1EnI,uDAAA,GAA4C;UAA5CA,wDAAA,sBAAAmD,GAAA,CAAA0C,oBAAA,GAA4C,qBAAA1C,GAAA,CAAAiF,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjFA;AAExD;AACgF;;;;;;;;;;;;;;ICyDtEpI,4DAAA,cAGC;IACKA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAK;IAC/BA,4DAAA,QAAG;IAAAA,oDAAA,GAA0C;IAAAA,0DAAA,EAAI;;;;IAA9CA,uDAAA,GAA0C;IAA1CA,+DAAA,CAAAqI,MAAA,CAAA1E,cAAA,CAAAC,qBAAA,CAA0C;;;;;;;;ADnDnD,MAAOzI,iBAAkB,SAAQF,sEAAoB;EAGzD0F,YACUkD,SAAmB,EACpBC,cAA8B,EAC3BC,aAA4B,EAC5BC,WAAwB,EAC3BC,MAAiB;IAExB,KAAK,CAACF,aAAa,EAAED,cAAc,EAAEE,WAAW,EAAEC,MAAM,CAAC;IANjD,KAAAJ,SAAS,GAATA,SAAS;IACV,KAAAC,cAAc,GAAdA,cAAc;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACd,KAAAC,MAAM,GAANA,MAAM;IAPf,KAAAqE,gBAAgB,GAAa,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;EAUpE;EAEArH,QAAQA,CAAA;IACN,IAAI,CAACmD,iBAAiB,GAAGb,4DAAU,CAACgF,OAAO;IAC3C,IAAI,CAACjE,UAAU,CAAC,IAAIhB,wDAAM,EAAE,CAAC;EAC/B;EAEAiB,WAAWA,CAAA;IACT,IAAI,CAACV,SAAS,CAACW,IAAI,EAAE;EACvB;EAEAF,UAAUA,CAACkE,OAAe;IACxB,IAAI,CAAClI,IAAI,GAAG,IAAIX,qDAAS,CAAC;MACxBuF,KAAK,EAAE,IAAItF,uDAAW,CAAC4I,OAAO,CAACrD,KAAK,IAAI,EAAE,CAAC;MAC3CE,WAAW,EAAE,IAAIzF,uDAAW,CAAC4I,OAAO,CAAClD,WAAW,IAAI,EAAE,CAAC;MACvDC,OAAO,EAAE,IAAI3F,uDAAW,CAAC4I,OAAO,CAACvD,OAAO,CAAC;MACzCO,QAAQ,EAAE,IAAI5F,uDAAW,CAAC4I,OAAO,CAACzD,QAAQ,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI;KACpE,CAAC;EACJ;EAEAa,SAASA,CAAA;IACP,IAAI,CAACjB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAChB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACW,UAAU,CAAC,IAAIhB,wDAAM,EAAE,CAAC;EAC/B;EAEAuC,oBAAoBA,CAAA;IAClB,OAAO,CAAC,IAAI,CAACvF,IAAI,CAACkC,GAAG,CAAC,OAAO,CAAC,CAACF,KAAK,IAAI,CAAC,IAAI,CAAChC,IAAI,CAACkC,GAAG,CAAC,aAAa,CAAC,CAACF,KAAK;EAC7E;EAEAmC,YAAYA,CAAC+D,OAAe;IAC1B,IAAI,CAAC7E,cAAc,GAAG6E,OAAO;IAC7B,IAAI,CAAClE,UAAU,CAACkE,OAAO,CAAC;IACxB,IAAI,CAAC7D,cAAc,GAAG,IAAI;EAC5B;EAEA,IAAI8D,QAAQA,CAAA;IACV,OAAO,IAAI,CAACnI,IAAI,CAACkC,GAAG,CAAC,aAAa,CAAC,CAACF,KAAK,GAAG,IAAI,GAAG,KAAK;EAC1D;EAEAoG,gBAAgBA,CAACC,CAAC;IAChB,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;MAClBE,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,OAAO;MAC/B;;IAEFH,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM,GAAGH,CAAC,CAACC,MAAM,CAACG,YAAY,GAAG,IAAI;EACtD;;;uBAzDW5N,iBAAiB,EAAA6E,+DAAA,CAAA/C,qDAAA,GAAA+C,+DAAA,CAAAgG,kEAAA,GAAAhG,+DAAA,CAAAgG,iEAAA,GAAAhG,+DAAA,CAAAgG,+DAAA,GAAAhG,+DAAA,CAAAoG,+DAAA;IAAA;EAAA;;;YAAjBjL,iBAAiB;MAAAwH,SAAA;MAAA2D,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgG,2BAAA9F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB9BlD,4DAAA,aAAoB;UAECA,wDAAA,qBAAAiJ,8DAAA;YAAA,OAAW9F,GAAA,CAAAoB,WAAA,EAAa;UAAA,EAAC;UAAmCvE,0DAAA,EAAkB;UAE/FA,4DAAA,uBAKC;UAHCA,wDAAA,2BAAAkJ,kEAAAtC,MAAA;YAAA,OAAiBzD,GAAA,CAAA0D,cAAA,CAAAD,MAAA,CAAsB;UAAA,EAAC,6BAAAuC,oEAAAvC,MAAA;YAAA,OACrBzD,GAAA,CAAA4D,iBAAA,CAAAH,MAAA,CAAyB;UAAA,EADJ,sBAAAwC,6DAAA;YAAA,OAE5BjG,GAAA,CAAAyC,SAAA,EAAW;UAAA,EAFiB;UAGzC5F,0DAAA,EAAgB;UAGnBA,4DAAA,aAAkC;UAK5BA,wDAAA,uBAAAqJ,6DAAAzC,MAAA;YAAA,OAAazD,GAAA,CAAAsB,YAAA,CAAAmC,MAAA,CAAoB;UAAA,EAAC;UACnC5G,0DAAA,EAAe;UAGlBA,4DAAA,aAAwE;UAI7DA,wDAAA,mBAAAsJ,+CAAA;YAAA,OAASnG,GAAA,CAAAgE,QAAA,EAAU;UAAA,EAAC;UACrBnH,uDAAA,eAAoC;UACtCA,0DAAA,EAAI;UAIRA,4DAAA,gBAA8C;UAE/BA,oDAAA,aAAK;UAAAA,0DAAA,EAAY;UAC5BA,uDAAA,iBAME;UACJA,0DAAA,EAAiB;UAEjBA,4DAAA,0BAAqC;UACxBA,oDAAA,YAAI;UAAAA,0DAAA,EAAY;UAC3BA,uDAAA,wBAUY;UACZA,4DAAA,oBAAsB;UAAAA,oDAAA,IAAqC;UAAAA,0DAAA,EAAW;UAGxEA,uDAAA,eAAoC;UAGpCA,wDAAA,KAAAuJ,iCAAA,kBAMM;UAENvJ,4DAAA,wBAMC;UALCA,wDAAA,uBAAAwJ,8DAAA;YAAA,OAAarG,GAAA,CAAAoE,UAAA,EAAY;UAAA,EAAC,yBAAAkC,gEAAA;YAAA,OACXtG,GAAA,CAAAgE,QAAA,EAAU;UAAA,EADC,yBAAAuC,gEAAA;YAAA,OAEXvG,GAAA,CAAAuE,iBAAA,EAAmB;UAAA,EAFR;UAK3B1H,0DAAA,EAAe;;;;UA7DlBA,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAmD,GAAA,CAAA0B,UAAA,CAAwB;UAMC7E,uDAAA,GAA0C;UAA1CA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAzE,GAAA,CAAAwB,cAAA,EAA0C;UAU7D3E,uDAAA,GAAkB;UAAlBA,wDAAA,cAAAmD,GAAA,CAAA7C,IAAA,CAAkB;UAyBEN,uDAAA,IAAqC;UAArCA,gEAAA,KAAA8H,GAAA,CAAAxF,KAAA,CAAAyF,MAAA,YAAqC;UAO1D/H,uDAAA,GAAyE;UAAzEA,wDAAA,SAAAmD,GAAA,CAAAQ,cAAA,IAAAR,GAAA,CAAAQ,cAAA,CAAAsE,MAAA,IAAA9E,GAAA,CAAA+E,gBAAA,CAAAC,OAAA,CAAyE;UAW1EnI,uDAAA,GAA4C;UAA5CA,wDAAA,sBAAAmD,GAAA,CAAA0C,oBAAA,GAA4C,qBAAA1C,GAAA,CAAAiF,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtExD;AAC6F;;;;;;;;;;;;ICMrFpI,4DAAA,gBASC;IACCA,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;;IANPA,wDAAA,cAAAK,MAAA,CAAAyJ,UAAA,CAAwB;;;;;;IAO1B9J,4DAAA,iBAOC;IADCA,wDAAA,mBAAA+J,8DAAA;MAAA/J,2DAAA,CAAAiK,GAAA;MAAA,MAAAC,MAAA,GAAAlK,2DAAA;MAAA,OAASA,yDAAA,CAAAkK,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAE5BrK,oDAAA,cACF;IAAAA,0DAAA,EAAS;;;;IAJPA,wDAAA,cAAAqI,MAAA,CAAAyB,UAAA,CAAwB;;;;;;;;;;IAc9B9J,4DAAA,cAA2F;IAAAA,oDAAA,QAAC;IAAAA,0DAAA,EAAM;;;;IAAtCA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,EAAA0C,MAAA,CAAAC,MAAA,EAA8B;;;;;IAE1FvK,4DAAA,cAA2F;IACzFA,uDAAA,yBAAyF;IAC3FA,0DAAA,EAAM;;;;;IAFsDA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,EAAA4C,MAAA,CAAAD,MAAA,EAA8B;IACxEvK,uDAAA,GAAe;IAAfA,wDAAA,UAAAyK,QAAA,CAAe,sBAAAD,MAAA,CAAA5I,iBAAA;;;;;IALnC5B,4DAAA,cAAoF;IAElFA,wDAAA,IAAA0K,wCAAA,kBAAkG;IAElG1K,wDAAA,IAAA2K,wCAAA,kBAEM;IACR3K,0DAAA,EAAM;;;;;IAPcA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,EAAAlE,MAAA,CAAA6G,MAAA,EAA8B;IAEzBvK,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA4K,OAAA,CAAAC,UAAA,CAAkB;IAElB7K,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA4K,OAAA,CAAAd,UAAA,CAAkB;;;AD7BvC,MAAOlL,mBAAoB,SAAQgL,+DAAa;EAQpDjJ,YACUC,cAA8B,EAC9BkK,aAAoC,EACpCC,MAAc;IAEtB,KAAK,EAAE;IAJC,KAAAnK,cAAc,GAAdA,cAAc;IACd,KAAAkK,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IATR,KAAAC,WAAW,GAAc,EAAE;IACnC,KAAAC,WAAW,GAAkB,EAAE;IAC/B,KAAAV,MAAM,GAAY,IAAI;IACtB,KAAA3I,iBAAiB,GAAY,KAAK;IAClC,KAAAsJ,cAAc,GAAG,QAAQ;EAQzB;EAEAjK,QAAQA,CAAA,GAAI;EAEZkK,SAASA,CAAA;IACP,OAAO,IAAI,CAACL,aAAa,CAACM,SAAS,EAAE;EACvC;EAEAf,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACS,aAAa,CAACO,OAAO,IAAI,IAAI,CAACH,cAAc,EAAE;MACrD,IAAI,CAACtK,cAAc,CAAC0K,SAAS,CAAC,IAAI,CAACL,WAAW,CAAC;MAC/C,IAAI,CAACF,MAAM,CAACQ,QAAQ,CAAC,CAAC,qBAAqB,IAAI,CAAC3J,iBAAiB,EAAE,CAAC,CAAC;KACtE,MAAM;MACL,IAAI,CAAC4J,eAAe,EAAE;;EAE1B;EAEQA,eAAeA,CAAA;IACrB,IAAIC,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAACC,SAAS;IACtE,IAAIC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,wBAAwB,CAAC;IAClEF,QAAQ,CAACH,QAAQ,CAACK,IAAI,EAAE;IAExBF,QAAQ,CAACH,QAAQ,CAACM,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCA0HYP,aAAa;cACvC,CAAC;EACb;EAEAzJ,cAAcA,CAACb,QAA0B;IACvC,IAAI,CAAC8K,mBAAmB,CAAC9K,QAAQ,CAACkB,IAAI,EAAElB,QAAQ,CAACoB,MAAM,CAAC;EAC1D;EAEAxB,WAAWA,CAAA;IACT,IAAI,CAACmL,cAAc,EAAE;EACvB;EAEApL,iBAAiBA,CAACiB,GAAY;IAC5B,IAAI,CAACwI,MAAM,GAAGxI,GAAG;EACnB;EAEAoK,wBAAwBA,CAACpK,GAAY;IACnC,IAAI,CAACH,iBAAiB,GAAGG,GAAG;EAC9B;EAEQkK,mBAAmBA,CAACxK,IAAI,EAAEE,MAAM;IACtC,IAAIyK,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI7K,IAAI,IAAI,CAAC,EAAE;MACb4K,UAAU,GAAG5K,IAAI,GAAG,CAAC;;IAGvB,IAAIE,MAAM,IAAI,CAAC,EAAE;MACf2K,YAAY,GAAG3K,MAAM,GAAG,CAAC;;IAG3ByK,gBAAgB,GAAGC,UAAU,GAAG,CAAC,GAAGC,YAAY;IAChD,IAAI,CAACtB,WAAW,GAAG,EAAE;IAErB,KAAK,IAAIuB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,gBAAgB,EAAEG,KAAK,EAAE,EAAE;MACrD,IAAI,CAACvB,WAAW,CAACwB,IAAI,CAAC,IAAI3C,yDAAO,EAAE,CAAC;;IAGtC,IAAI,CAACqC,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,IAAI,CAACjB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,GAAG,IAAItB,6DAAW,EAAE;IACvC,IAAI,CAACsB,WAAW,CAAC,CAAC,CAAC,CAACJ,UAAU,GAAG,EAAE,GAAG,IAAI,CAACG,WAAW;IAEtD,IAAIyB,UAAU,GAAG,CAAC;IAClB,IAAIC,SAAS,GAAG,CAAC;IAEjB;IACA,OAAOD,UAAU,GAAG,IAAI,CAAC3C,UAAU,CAAC/B,MAAM,EAAE;MAC1C,IAAI,CAAC,IAAI,CAACkD,WAAW,CAACyB,SAAS,CAAC,EAAE;QAChC,IAAI,CAACzB,WAAW,CAACyB,SAAS,CAAC,GAAG,IAAI/C,6DAAW,EAAE;;MAGjD,IAAIgD,SAAS,GAAG,IAAI,CAAC1B,WAAW,CAACyB,SAAS,CAAC,CAACE,kBAAkB,EAAE;MAEhE,IAAI,CAAC3B,WAAW,CAACyB,SAAS,CAAC,CAAC5C,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC+C,KAAK,CAACJ,UAAU,EAAEA,UAAU,GAAGE,SAAS,CAAC;MAElGF,UAAU,IAAIE,SAAS;MACvBD,SAAS,EAAE;;EAEf;;;uBA9NW9N,mBAAmB,EAAAoB,+DAAA,CAAA/C,kEAAA,GAAA+C,+DAAA,CAAAgG,sEAAA,GAAAhG,+DAAA,CAAAoG,mDAAA;IAAA;EAAA;;;YAAnBxH,mBAAmB;MAAA+D,SAAA;MAAAqK,MAAA;QAAAlD,UAAA;MAAA;MAAAxD,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiK,6BAAA/J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhClD,4DAAA,aAA6B;UAEzBA,wDAAA,sBAAAkN,+DAAAtG,MAAA;YAAA,OAAYzD,GAAA,CAAAnB,cAAA,CAAA4E,MAAA,CAAsB;UAAA,EAAC,yBAAAuG,kEAAA;YAAA,OACpBhK,GAAA,CAAApC,WAAA,EAAa;UAAA,EADO,+BAAAqM,wEAAAxG,MAAA;YAAA,OAEdzD,GAAA,CAAArC,iBAAA,CAAA8F,MAAA,CAAyB;UAAA,EAFX,iCAAAyG,0EAAAzG,MAAA;YAAA,OAGZzD,GAAA,CAAAgJ,wBAAA,CAAAvF,MAAA,CAAgC;UAAA,EAHpB;UAIpC5G,0DAAA,EAAgB;UAEjBA,4DAAA,aAA6B;UAGvBA,wDAAA,IAAAsN,qCAAA,oBAWS;UACTtN,wDAAA,IAAAuN,qCAAA,oBASS;UACXvN,0DAAA,EAAM;UACNA,uDAAA,aAAyB;UAC3BA,0DAAA,EAAM;UAIVA,4DAAA,aAAwB;UACtBA,wDAAA,IAAAwN,kCAAA,iBAOM;UACRxN,0DAAA,EAAM;;;UApCKA,uDAAA,GAAiB;UAAjBA,wDAAA,SAAAmD,GAAA,CAAAgI,SAAA,GAAiB;UAYjBnL,uDAAA,GAAkB;UAAlBA,wDAAA,UAAAmD,GAAA,CAAAgI,SAAA,GAAkB;UAgByCnL,uDAAA,GAAc;UAAdA,wDAAA,YAAAmD,GAAA,CAAA8H,WAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;;ACxCqB;;;;;;;;ICSnGjL,4DAAA,aAA2D;IAIvDA,wDAAA,oBAAAyN,kFAAA7G,MAAA;MAAA5G,2DAAA,CAAA0N,GAAA;MAAA,MAAAhK,MAAA,GAAA1D,2DAAA;MAAA,OAAUA,yDAAA,CAAA0D,MAAA,CAAAiK,eAAA,CAAA/G,MAAA,CAAuB;IAAA,EAAC;IACjC5G,oDAAA,GAAsB;IAAAA,0DAAA,EACxB;;;;;IAJCA,uDAAA,GAA2B;IAA3BA,wDAAA,SAAA4N,MAAA,CAAAC,cAAA,CAA2B,YAAAxN,MAAA,CAAAyN,SAAA,CAAAF,MAAA,CAAAC,cAAA;IAG1B7N,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA4N,MAAA,CAAAG,YAAA,CAAsB;;;;;;;;ADH3B,MAAO/O,mCAAmC;EAM9C2B,YAAA;IAJS,KAAAqN,WAAW,GAAY,KAAK;IAC3B,KAAAC,kBAAkB,GAA2B,IAAIvO,uDAAY,EAAE;IACzE,KAAAwO,uBAAuB,GAAa,EAAE;EAEvB;EAEfC,WAAWA,CAACC,OAAsB;IAChC,KAAK,MAAMC,QAAQ,IAAID,OAAO,EAAE;MAC9B,QAAQC,QAAQ;QACd,KAAK,YAAY;UACf,IAAI,CAACC,YAAY,EAAE;UACnB;QAEF;UACE;;;EAGR;EAEA;;;EAGQA,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACxG,MAAM,GAAG,CAAC,EAAE;MACjD,IAAI,CAACmG,uBAAuB,GAAG,EAAE;MAEjC,IAAI,CAACK,UAAU,CAACC,OAAO,CAACC,CAAC,IAAG;QAC1B,IAAI,CAACP,uBAAuB,CAAC1B,IAAI,CAACiC,CAAC,CAACZ,cAAc,CAAC;MACrD,CAAC,CAAC;MAEF;MACA,IAAIa,UAAU,GAAaC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAC7E,IAAIJ,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAC3G,MAAM,GAAG,CAAC,EAAE;QAC/C,IAAIwE,KAAK,GAAG,IAAI,CAAC2B,uBAAuB,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC,CAAC;QAEtE,IAAInC,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,IAAI,CAAC2B,uBAAuB,GAAGQ,UAAU;;OAE5C,MAAM;QACL,IAAI,CAACO,eAAe,EAAE;;MAGxB;MACA,IAAI,CAAChB,kBAAkB,CAAC7M,IAAI,CAAC,IAAI,CAAC8M,uBAAuB,CAAC;;EAE9D;EAEA;;;;;EAKAJ,SAASA,CAACoB,UAAkB;IAC1B,OAAO,IAAI,CAAChB,uBAAuB,CAACiB,SAAS,CAACV,CAAC,IAAIA,CAAC,IAAIS,UAAU,CAAC,GAAG,CAAC,CAAC;EAC1E;EAEA;;;;EAIAvB,eAAeA,CAACyB,KAAwB;IACtC,MAAMC,QAAQ,GAAG,CAACD,KAAK,CAACE,MAAM,CAACC,IAAI;IAEnC,IAAIH,KAAK,CAACI,OAAO,EAAE;MACjB,IAAI,CAACtB,uBAAuB,CAAC1B,IAAI,CAAC6C,QAAQ,CAAC;KAC5C,MAAM;MACL,IAAI9C,KAAK,GAAG,IAAI,CAAC2B,uBAAuB,CAACiB,SAAS,CAACH,CAAC,IAAIA,CAAC,IAAIK,QAAQ,CAAC;MACtE,IAAI9C,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAC2B,uBAAuB,CAACuB,MAAM,CAAClD,KAAK,EAAE,CAAC,CAAC;;;IAIjD;IACA,IAAI,CAAC0C,eAAe,EAAE;IAEtB;IACA,IAAI,CAAChB,kBAAkB,CAAC7M,IAAI,CAAC,IAAI,CAAC8M,uBAAuB,CAAC;EAC5D;EAEA;;;EAGQe,eAAeA,CAAA;IACrBJ,YAAY,CAACa,OAAO,CAAC,gBAAgB,EAAEf,IAAI,CAACgB,SAAS,CAAC,IAAI,CAACzB,uBAAuB,CAAC,CAAC;EACtF;EAEA;;;;EAIA0B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC1B,uBAAuB,CAACnG,MAAM,IAAI,IAAI,CAACwG,UAAU,CAACxG,MAAM;EACtE;EAEA;;;;EAIA8H,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAC3B,uBAAuB,CAACnG,MAAM,IAAI,CAAC;EACjD;EAEA;;;EAGA+H,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACF,aAAa,EAAE,EAAE;MACzB,IAAI,CAAC1B,uBAAuB,GAAG,EAAE;MAEjC,IAAI,CAACK,UAAU,CAACC,OAAO,CAACC,CAAC,IAAG;QAC1B,IAAI,CAACP,uBAAuB,CAAC1B,IAAI,CAACiC,CAAC,CAACZ,cAAc,CAAC;MACrD,CAAC,CAAC;MAEF,IAAI,CAACoB,eAAe,EAAE;MAEtB;MACA,IAAI,CAAChB,kBAAkB,CAAC7M,IAAI,CAAC,IAAI,CAAC8M,uBAAuB,CAAC;;EAE9D;EAEA;;;EAGA6B,KAAKA,CAAA;IACH,IAAI,CAAC,IAAI,CAACF,uBAAuB,EAAE,EAAE;MACnC,IAAI,CAAC3B,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACD,kBAAkB,CAAC7M,IAAI,CAAC,IAAI,CAAC8M,uBAAuB,CAAC;;EAE9D;;;uBAlIWlP,mCAAmC;IAAA;EAAA;;;YAAnCA,mCAAmC;MAAA2D,SAAA;MAAAqK,MAAA;QAAAuB,UAAA;QAAAP,WAAA;MAAA;MAAApL,OAAA;QAAAqL,kBAAA;MAAA;MAAA3H,QAAA,GAAAtG,kEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiN,6CAAA/M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXhDlD,4DAAA,oBAAe;UAGSA,oDAAA,mBAAW;UAAAA,0DAAA,EAAkB;UAC/CA,4DAAA,4BAAuB;UACfA,oDAAA,GAA4D;UAAAA,0DAAA,EAAO;UAG7EA,4DAAA,aAAiB;UACfA,wDAAA,IAAAkQ,kDAAA,iBAOM;UACRlQ,0DAAA,EAAM;UACNA,4DAAA,cAAiB;UAGRA,wDAAA,mBAAAmQ,iEAAA;YAAA,OAAShN,GAAA,CAAA4M,KAAA,EAAO;UAAA,EAAC;UAAoD/P,oDAAA,aAAK;UAAAA,0DAAA,EAAI;UACjFA,4DAAA,YAAkE;UAA/DA,wDAAA,mBAAAoQ,iEAAA;YAAA,OAASjN,GAAA,CAAA2M,SAAA,EAAW;UAAA,EAAC;UAA0C9P,oDAAA,kBAAU;UAAAA,0DAAA,EAAI;;;UArBnEA,uDAAA,GAAwB;UAAxBA,wDAAA,aAAAmD,GAAA,CAAA6K,WAAA,CAAwB;UAIjChO,uDAAA,GAA4D;UAA5DA,gEAAA,KAAAmD,GAAA,CAAA+K,uBAAA,CAAAnG,MAAA,OAAA5E,GAAA,CAAAoL,UAAA,CAAAxG,MAAA,KAA4D;UAIxB/H,uDAAA,GAAa;UAAbA,wDAAA,YAAAmD,GAAA,CAAAoL,UAAA,CAAa;UAYhCvO,uDAAA,GAAkD;UAAlDA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAzE,GAAA,CAAA0M,uBAAA,IAAkD;UAC9C7P,uDAAA,GAAwC;UAAxCA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAzE,GAAA,CAAAyM,aAAA,IAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtBQ;AACf;AACnC;AAEjC;AAC4C;AAC8B;AAI1E;AAY8B;;;;;;;;;;;;;;;;;;ICd1B5P,4DAAA,aAAgE;IAC9DA,uDAAA,qBAAmE;IACrEA,0DAAA,EAAM;;;;;IACNA,4DAAA,aAAgE;IAEjDA,oDAAA,mBAAY;IAAAA,0DAAA,EAAY;IACnCA,4DAAA,qBAAsC;IACZA,oDAAA,UAAG;IAAAA,0DAAA,EAAa;IACxCA,4DAAA,qBAA4B;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAa;IACrDA,4DAAA,qBAA8B;IAAAA,oDAAA,sBAAc;IAAAA,0DAAA,EAAa;;;;;;IAmBjEA,4DAAA,aAAyC;IAInCA,wDAAA,6BAAA8Q,0GAAAlK,MAAA;MAAA5G,2DAAA,CAAA+Q,GAAA;MAAA,MAAAzG,MAAA,GAAAtK,2DAAA;MAAA,OAAmBA,yDAAA,CAAAsK,MAAA,CAAA0G,sBAAA,CAAApK,MAAA,CAA8B;IAAA,EAAC;IACnD5G,0DAAA,EAA8B;;;;IAF7BA,uDAAA,GAAmC;IAAnCA,wDAAA,YAAAkK,MAAA,CAAA+G,eAAA,CAAAC,OAAA,CAAmC;;;;;;IASrClR,4DAAA,oCAIC;IADCA,wDAAA,6BAAAmR,8HAAAvK,MAAA;MAAA5G,2DAAA,CAAAoR,GAAA;MAAA,MAAAC,MAAA,GAAArR,2DAAA;MAAA,OAAAA,yDAAA,CAAAqR,MAAA,CAAAC,UAAA,GAAA1K,MAAA;IAAA,EAA0B;IAE5B5G,0DAAA,EAA4B;;;;IAH1BA,wDAAA,iBAAAuR,MAAA,CAAAN,eAAA,CAAAO,WAAA,CAA4C,cAAAD,MAAA,CAAAD,UAAA;;;;;;IAQlDtR,4DAAA,cAAmE;IAI7DA,wDAAA,gCAAAyR,+GAAA7K,MAAA;MAAA5G,2DAAA,CAAA0R,IAAA;MAAA,MAAAC,OAAA,GAAA3R,2DAAA;MAAA,OAAsBA,yDAAA,CAAA2R,OAAA,CAAAC,0BAAA,CAAAhL,MAAA,CAAkC;IAAA,EAAC;IAC1D5G,0DAAA,EAAgC;;;;IAF/BA,uDAAA,GAA6B;IAA7BA,wDAAA,eAAA6R,MAAA,CAAAC,cAAA,CAA6B;;;;;;IA7DrC9R,4DAAA,cAAkD;IAGvBA,wDAAA,6BAAA+R,2FAAAnL,MAAA;MAAA5G,2DAAA,CAAAgS,IAAA;MAAA,MAAAC,OAAA,GAAAjS,2DAAA;MAAA,OAAmBA,yDAAA,CAAAiS,OAAA,CAAAC,cAAA,CAAAtL,MAAA,CAAsB;IAAA,EAAC;IAAC5G,0DAAA,EAAsB;IAI1FA,4DAAA,aAAiB;IACfA,wDAAA,IAAAmS,iDAAA,iBAEM;IACNnS,wDAAA,IAAAoS,iDAAA,kBASM;IACNpS,4DAAA,aAAsC;IAElCA,uDAAA,eAAmF;IACnFA,4DAAA,mBAAkE;IAA9CA,wDAAA,mBAAAqS,uEAAA;MAAArS,2DAAA,CAAAgS,IAAA;MAAA,MAAAM,OAAA,GAAAtS,2DAAA;MAAA,OAASA,yDAAA,CAAAsS,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAAqBvS,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAKzFA,4DAAA,cAAiB;IAETA,oDAAA,eAAO;IAAAA,0DAAA,EAAK;IAKpBA,wDAAA,KAAAwS,kDAAA,kBAOM;IAGNxS,4DAAA,eAAsB;IAElBA,wDAAA,KAAAyS,wEAAA,wCAK4B;IAC9BzS,0DAAA,EAAM;IAIRA,wDAAA,KAAA0S,kDAAA,kBAOM;IACR1S,0DAAA,EAAO;;;;IAlEkBA,wDAAA,cAAAK,MAAA,CAAAiR,UAAA,CAAwB;IAQvCtR,uDAAA,GAAuB;IAAvBA,wDAAA,UAAAK,MAAA,CAAAsS,gBAAA,CAAuB;IAGvB3S,uDAAA,GAAuB;IAAvBA,wDAAA,UAAAK,MAAA,CAAAsS,gBAAA,CAAuB;IAyBzB3S,uDAAA,IAAqB;IAArBA,wDAAA,SAAAK,MAAA,CAAA4Q,eAAA,CAAqB;IAapBjR,uDAAA,GAAqB;IAArBA,wDAAA,SAAAK,MAAA,CAAA4Q,eAAA,CAAqB;IAStBjR,uDAAA,GAA0C;IAA1CA,wDAAA,SAAAK,MAAA,CAAAuS,kBAAA,IAAAvS,MAAA,CAAAyR,cAAA,CAA0C;;;AD1B5C,MAAOjT,2BAA4B,SAAQ+K,+DAAa;EAe5DjJ,YACUC,cAA8B,EAC9BiS,KAAuC,EACvCC,eAAgC;IAExC,KAAK,EAAE;IAJC,KAAAlS,cAAc,GAAdA,cAAc;IACd,KAAAiS,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IAjBf,KAAAC,cAAc,GAAsC,IAAIrT,uDAAY,EAAE;IACtE,KAAAsT,eAAe,GAAsC,IAAItT,uDAAY,EAAE;IAEjF,KAAAiT,gBAAgB,GAAY,KAAK;IACjC,KAAAM,cAAc,GAAY,KAAK;IAO/B,KAAAL,kBAAkB,GAAY,KAAK;IAsMnC,KAAAM,qBAAqB,GAAG,IAAI,CAACJ,eAAe,CAACK,YAAY,CAAC,IAAI,CAACC,eAAe,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;EA7LjG;EAEAnS,QAAQA,CAAA;IACN,IAAI,CAACoS,mBAAmB,GAAG,IAAI,CAACR,KAAK,CAClCS,IAAI,CAAC/C,oDAAM,CAACC,oFAAc,CAAC,CAAC,CAC5B1O,SAAS,CAAE0O,cAAgC,IAAI;MAC9C,IAAI,CAACsB,cAAc,GAAGtB,cAAc;IACtC,CAAC,CAAC;IACJ;IACA,IAAI,CAAC+C,cAAc,GAAG,IAAI,CAAC3S,cAAc,CAAC4S,UAAU,EAAE;IAEtD;IACA,IAAI,CAACtS,WAAW,EAAE;EACpB;EAEAuS,WAAWA,CAAA;IACT,IAAI,IAAI,CAACJ,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACK,WAAW,EAAE;;EAE1C;EAEA,IAAIxP,IAAIA,CAAA;IACN,OAAO,IAAI,CAACoN,UAAU,CAAC9O,GAAG,CAAC,MAAM,CAAC;EACpC;EAEA,IAAImR,MAAMA,CAAA;IACR,OAAO,IAAI,CAACrC,UAAU,CAAC9O,GAAG,CAAC,QAAQ,CAAC;EACtC;EAEA,IAAIoR,KAAKA,CAAA;IACP,OAAO,IAAI,CAACtC,UAAU,CAAC9O,GAAG,CAAC,OAAO,CAAC;EACrC;EAEA,IAAI4M,KAAKA,CAAA;IACP,OAAO,IAAI,CAACkC,UAAU,CAAC9O,GAAG,CAAC,OAAO,CAAC;EACrC;EAEA,IAAIqR,OAAOA,CAAA;IACT,OAAO,IAAI,CAACvC,UAAU,CAAC9O,GAAG,CAAC,SAAS,CAAC;EACvC;EAEA,IAAIsR,MAAMA,CAAA;IACR,OAAO,IAAI,CAACxC,UAAU,CAAC9O,GAAG,CAAC,QAAQ,CAAC;EACtC;EAEA,IAAIuR,aAAaA,CAAA;IACf,OAAO,IAAI,CAACzC,UAAU,CAAC9O,GAAG,CAAC,eAAe,CAAC;EAC7C;EAEA,IAAIwR,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC1C,UAAU,CAAC9O,GAAG,CAAC,UAAU,CAAC;EACxC;EAEA,IAAIyR,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC3C,UAAU,CAAC9O,GAAG,CAAC,cAAc,CAAC;EAC5C;EAEA,IAAI0R,MAAMA,CAAA;IACR,OAAO,IAAI,CAAC5C,UAAU,CAAC9O,GAAG,CAAC,QAAQ,CAAC;EACtC;EAEA;;;EAGQtB,WAAWA,CAAA;IACjB,IAAI,CAACoQ,UAAU,GAAG,IAAI3R,sDAAS,CAAC;MAC9BuU,MAAM,EAAE,IAAItU,wDAAW,CAAC,EAAE,CAAC;MAC3B+T,MAAM,EAAE,IAAI/T,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACY,MAAM,CAAC;MACnDP,KAAK,EAAE,IAAIhU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACa,KAAK,CAAC;MACjDhF,KAAK,EAAE,IAAIxP,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACc,KAAK,CAAC;MACjDR,OAAO,EAAE,IAAIjU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACe,OAAO,CAAC;MACrDpQ,IAAI,EAAE,IAAItE,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACpP,IAAI,EAAE,CAACtE,uDAAU,CAAC6B,QAAQ,CAAC,CAAC;MACtEoS,MAAM,EAAE,IAAIlU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACgB,MAAM,CAAC;MACnDR,aAAa,EAAE,IAAInU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACiB,aAAa,CAAC;MACjER,QAAQ,EAAE,IAAIpU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACkB,QAAQ,CAAC;MACvDR,YAAY,EAAE,IAAIrU,wDAAW,CAAC,IAAI,CAAC2T,cAAc,CAACmB,YAAY;KAC/D,CAAC;IAEF,IAAI,CAACxQ,IAAI,CAACrC,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACrC,IAAI,CAACmR,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACS,MAAM,CAAC9R,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACvC,IAAI,CAACwR,cAAc,CAACY,MAAM,GAAGpS,GAAG;MAChC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACU,KAAK,CAAC/R,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACtC,IAAI,CAACwR,cAAc,CAACa,KAAK,GAAGrS,GAAG;MAC/B,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAAC9D,KAAK,CAACvN,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACtC,IAAI,CAACwR,cAAc,CAACc,KAAK,GAAGtS,GAAG;MAC/B,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACW,OAAO,CAAChS,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACxC,IAAI,CAACwR,cAAc,CAACe,OAAO,GAAGvS,GAAG;MACjC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACY,MAAM,CAACjS,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACvC,IAAI,CAACwR,cAAc,CAACgB,MAAM,GAAGxS,GAAG;MAChC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACa,aAAa,CAAClS,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MAC9C,IAAI,CAACwR,cAAc,CAACiB,aAAa,GAAGzS,GAAG;MACvC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACc,QAAQ,CAACnS,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MACzC,IAAI,CAACwR,cAAc,CAACkB,QAAQ,GAAG1S,GAAG;MAClC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACe,YAAY,CAACpS,YAAY,CAACC,SAAS,CAACC,GAAG,IAAG;MAC7C,IAAI,CAACwR,cAAc,CAACmB,YAAY,GAAG3S,GAAG;MACtC,IAAI,CAACnB,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;MACnD,IAAI,CAACL,qBAAqB,EAAE;IAC9B,CAAC,CAAC;EACJ;EAEA;;;;EAIAhB,cAAcA,CAAC0C,OAAgB;IAC7B,IAAI,CAAC3D,eAAe,GAAG2D,OAAO;IAC9B,IAAI,CAACjC,gBAAgB,GAAG,IAAI,CAAC1B,eAAe,CAACO,WAAW,KAAKd,kEAAgB,CAACmE,OAAO;IACrF,IAAI,CAAC5B,cAAc,GAAG,IAAI,CAAChC,eAAe,CAACO,WAAW,KAAKd,kEAAgB,CAAC2D,KAAK;IACjF,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAAC7D,eAAe,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC6D,gBAAgB,CAAC,CAAC;IAC1E,IAAI,CAACnC,kBAAkB,GAAG,IAAI,CAACkC,gBAAgB,IAAInE,sEAAoB,CAACqE,IAAI;IAE5E;IACA,MAAM7T,QAAQ,GAAwB;MACpC4T,gBAAgB,EAAE,IAAI,CAACD,gBAAgB;MACvCG,cAAc,EAAE,IAAI,CAAChE,eAAe,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC+D,cAAc;MAC9DC,gBAAgB,EAAE,IAAI,CAACvC;KACxB;IACD,IAAI,CAACK,eAAe,CAAC5R,IAAI,CAACD,QAAQ,CAAC;EACrC;EAEA;;;EAGAoR,aAAaA,CAAA;IACX,IAAI,CAACW,qBAAqB,EAAE;EAC9B;EAEA;;;;EAIAlC,sBAAsBA,CAACmE,OAAiB;IACtC,IAAI,CAACC,eAAe,GAAGD,OAAO;IAE9B,IAAI,IAAI,CAAClE,eAAe,CAACO,WAAW,IAAId,kEAAgB,CAAC2D,KAAK,EAAE;MAC9D,IAAI,CAACjF,KAAK,CAACiG,QAAQ,CAAC,IAAI,CAAC;MAEzB,IAAI,CAAC9B,cAAc,CAACc,KAAK,GAAG,IAAI;MAChC,IAAI,CAACzT,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;;IAGrD,IACE,IAAI,CAACuB,gBAAgB,IAAInE,sEAAoB,CAAC2E,KAAK,IAClD,IAAI,CAACR,gBAAgB,IAAInE,sEAAoB,CAACqE,IAAI,IAAI,IAAI,CAAClD,cAAc,CAAC/J,MAAM,GAAG,CAAE,EACtF;MACA,IAAI,CAACmL,qBAAqB,EAAE;;EAEhC;EAEA;;;;EAIAtB,0BAA0BA,CAACrD,UAAoB;IAC7C,IAAI,CAACgH,kBAAkB,GAAGhH,UAAU;IACpC,IAAI,CAAC2E,qBAAqB,EAAE;EAC9B;EAIQE,eAAeA,CAAA;IACrB;IACA,IAAIoC,OAAO,GAAwB,IAAI3E,qEAAmB,EAAE;IAE5D,IAAI,CAACuE,eAAe,CAAC5G,OAAO,CAACiH,CAAC,IAAG;MAC/B,IAAI,CAACD,OAAO,CAACE,SAAS,EAAE;QACtBF,OAAO,CAACE,SAAS,GAAGD,CAAC,CAACE,QAAQ,EAAE;OACjC,MAAM;QACLH,OAAO,CAACE,SAAS,IAAI,GAAG,GAAGD,CAAC;;IAEhC,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACF,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC/G,OAAO,CAACiH,CAAC,IAAG;QAClC,IAAI,CAACD,OAAO,CAACI,WAAW,EAAE;UACxBJ,OAAO,CAACI,WAAW,GAAGH,CAAC,CAACE,QAAQ,EAAE;SACnC,MAAM;UACLH,OAAO,CAACI,WAAW,IAAI,GAAG,GAAGH,CAAC;;MAElC,CAAC,CAAC;;IAGJD,OAAO,CAACrR,IAAI,GAAGmM,mCAAM,CAAC,IAAI,CAACpM,IAAI,CAAC5B,KAAK,CAAC,CAACuT,MAAM,CAAC,YAAY,CAAC;IAE3DL,OAAO,CAACM,aAAa,GAAG,EAAE;IAC1BN,OAAO,CAACO,SAAS,GAAG,EAAE;IACtBP,OAAO,CAACT,gBAAgB,GAAG,IAAI,CAACD,gBAAgB;IAChDU,OAAO,CAACQ,MAAM,GAAG,IAAI,CAAC9B,MAAM,CAAC5R,KAAK;IAClCkT,OAAO,CAACS,UAAU,GAAG,IAAI,CAAChF,eAAe,CAACiF,SAAS;IAEnD,IAAI,IAAI,CAACvD,gBAAgB,EAAE;MACzB6C,OAAO,CAACO,SAAS,IAAInF,8DAAY,CAACiE,OAAO,GAAG,GAAG;MAE/C,IAAI,IAAI,CAACf,MAAM,CAACxR,KAAK,EAAE;QACrBkT,OAAO,CAACM,aAAa,IAAIrF,mEAAiB,CAAC0F,GAAG,GAAG,GAAG;;MAGtD,IAAI,IAAI,CAACpC,aAAa,CAACzR,KAAK,EAAE;QAC5BkT,OAAO,CAACM,aAAa,IAAIrF,mEAAiB,CAAC2F,UAAU,GAAG,GAAG;;MAG7D,IAAI,IAAI,CAACpC,QAAQ,CAAC1R,KAAK,EAAE;QACvBkT,OAAO,CAACM,aAAa,IAAIrF,mEAAiB,CAAC4F,KAAK,GAAG,GAAG;;MAGxD,IAAI,IAAI,CAACpC,YAAY,CAAC3R,KAAK,EAAE;QAC3BkT,OAAO,CAACM,aAAa,IAAIrF,mEAAiB,CAAC6F,SAAS,GAAG,GAAG;;KAE7D,MAAM;MACL,IAAI,IAAI,CAAC3C,MAAM,CAACrR,KAAK,IAAI,CAAC,IAAI,CAAC2Q,cAAc,EAAE;QAC7CuC,OAAO,CAACO,SAAS,IAAInF,8DAAY,CAACuD,MAAM,GAAG,GAAG;;MAGhD,IAAI,IAAI,CAACP,KAAK,CAACtR,KAAK,IAAI,CAAC,IAAI,CAAC2Q,cAAc,EAAE;QAC5CuC,OAAO,CAACO,SAAS,IAAInF,8DAAY,CAACwD,KAAK,GAAG,GAAG;;MAG/C,IAAI,IAAI,CAAChF,KAAK,CAAC9M,KAAK,EAAE;QACpBkT,OAAO,CAACO,SAAS,IAAInF,8DAAY,CAACyD,KAAK,GAAG,GAAG;;MAG/C,IAAI,IAAI,CAACR,OAAO,CAACvR,KAAK,EAAE;QACtB,IAAI,IAAI,CAACuR,OAAO,CAACvR,KAAK,IAAI,KAAK,EAAE;UAC/BkT,OAAO,CAAClB,OAAO,IAAI,IAAI;SACxB,MAAM,IAAI,IAAI,CAACT,OAAO,CAACvR,KAAK,IAAI,SAAS,EAAE;UAC1CkT,OAAO,CAAClB,OAAO,GAAG,IAAI;SACvB,MAAM;UACLkB,OAAO,CAAClB,OAAO,GAAG,KAAK;;;;IAK7B;IACA,IAAI,CAACvB,cAAc,CAAC3R,IAAI,CAACoU,OAAO,CAAC;EACnC;;;uBA/RW3W,2BAA2B,EAAAmB,+DAAA,CAAA/C,kEAAA,GAAA+C,+DAAA,CAAAgG,+CAAA,GAAAhG,+DAAA,CAAA/C,mEAAA;IAAA;EAAA;;;YAA3B4B,2BAA2B;MAAA8D,SAAA;MAAAC,OAAA;QAAAmQ,cAAA;QAAAC,eAAA;MAAA;MAAA1M,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAyT,qCAAAvT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCxClD,wDAAA,IAAA0W,2CAAA,mBAkEO;;;UAlEA1W,wDAAA,SAAAmD,GAAA,CAAAmO,UAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;ACAkF;;;;;;;;ICSnGtR,4DAAA,aAA2D;IAIvDA,wDAAA,oBAAA2W,gFAAA/P,MAAA;MAAA5G,2DAAA,CAAA0N,GAAA;MAAA,MAAAhK,MAAA,GAAA1D,2DAAA;MAAA,OAAUA,yDAAA,CAAA0D,MAAA,CAAAiK,eAAA,CAAA/G,MAAA,CAAuB;IAAA,EAAC;IACjC5G,oDAAA,GAAiB;IAAAA,0DAAA,EACnB;;;;;IAJCA,uDAAA,GAAwB;IAAxBA,wDAAA,SAAA4W,SAAA,CAAAC,QAAA,CAAwB,YAAAxW,MAAA,CAAAyN,SAAA,CAAA8I,SAAA,CAAAC,QAAA;IAGvB7W,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA4W,SAAA,CAAAE,IAAA,CAAiB;;;;;;;;ADHtB,MAAOhY,iCAAiC;EAI5C6B,YAAA;IAFU,KAAAoW,eAAe,GAA2B,IAAIrX,uDAAY,EAAE;IACtE,KAAAsX,oBAAoB,GAAa,EAAE;EACpB;EAEf7I,WAAWA,CAACC,OAAsB;IAChC,KAAK,MAAMC,QAAQ,IAAID,OAAO,EAAE;MAC9B,QAAQC,QAAQ;QACd,KAAK,SAAS;UACZ,IAAI,CAACC,YAAY,EAAE;UACnB;QAEF;UACE;;;EAGR;EAEA;;;EAGQA,YAAYA,CAAA;IAClB,IAAI,IAAI,CAAC6G,OAAO,IAAI,IAAI,CAACA,OAAO,CAACpN,MAAM,GAAG,CAAC,EAAE;MAC3C;MACA,IAAI,CAACiP,oBAAoB,GAAG,EAAE;MAE9B,IAAI,CAAC7B,OAAO,CAAC3G,OAAO,CAACC,CAAC,IAAG;QACvB,IAAI,CAACuI,oBAAoB,CAACxK,IAAI,CAACiC,CAAC,CAACoI,QAAQ,CAAC;MAC5C,CAAC,CAAC;MAEF;MACA,IAAInI,UAAU,GAAaC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;MAC3E,IAAIJ,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAC3G,MAAM,GAAG,CAAC,EAAE;QAC/C,IAAIwE,KAAK,GAAG,IAAI,CAACyK,oBAAoB,CAACjI,IAAI,CAACC,CAAC,IAAIA,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC,CAAC;QAEnE,IAAInC,KAAK,GAAG,CAAC,CAAC,EAAE;UACd,IAAI,CAACyK,oBAAoB,GAAGtI,UAAU;;OAEzC,MAAM;QACL,IAAI,CAACuI,YAAY,EAAE;;MAGrB;MACA,IAAI,CAACF,eAAe,CAAC3V,IAAI,CAAC,IAAI,CAAC4V,oBAAoB,CAAC;;EAExD;EAEA;;;;;EAKAlJ,SAASA,CAACuB,QAAgB;IACxB,OAAO,IAAI,CAAC2H,oBAAoB,CAAC7H,SAAS,CAACV,CAAC,IAAIA,CAAC,IAAIY,QAAQ,CAAC,GAAG,CAAC,CAAC;EACrE;EAEA;;;;EAIA1B,eAAeA,CAACyB,KAAwB;IACtC,MAAMC,QAAQ,GAAG,CAACD,KAAK,CAACE,MAAM,CAACC,IAAI;IAEnC,IAAIH,KAAK,CAACI,OAAO,EAAE;MACjB,IAAI,CAACwH,oBAAoB,CAACxK,IAAI,CAAC6C,QAAQ,CAAC;KACzC,MAAM;MACL,IAAI9C,KAAK,GAAG,IAAI,CAACyK,oBAAoB,CAAC7H,SAAS,CAACH,CAAC,IAAIA,CAAC,IAAIK,QAAQ,CAAC;MACnE,IAAI9C,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACyK,oBAAoB,CAACvH,MAAM,CAAClD,KAAK,EAAE,CAAC,CAAC;;;IAI9C;IACA,IAAI,CAAC0K,YAAY,EAAE;IAEnB;IACA,IAAI,CAACF,eAAe,CAAC3V,IAAI,CAAC,IAAI,CAAC4V,oBAAoB,CAAC;EACtD;EAEA;;;;EAIQC,YAAYA,CAAA;IAClBpI,YAAY,CAACa,OAAO,CAAC,cAAc,EAAEf,IAAI,CAACgB,SAAS,CAAC,IAAI,CAACqH,oBAAoB,CAAC,CAAC;EACjF;EAEA;;;;EAIApH,aAAaA,CAAA;IACX,OAAO,IAAI,CAACoH,oBAAoB,CAACjP,MAAM,IAAI,IAAI,CAACoN,OAAO,CAACpN,MAAM;EAChE;EAEA;;;;EAIAmP,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACF,oBAAoB,CAACjP,MAAM,IAAI,CAAC;EAC9C;EAEA;;;EAGA+H,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACF,aAAa,EAAE,EAAE;MACzB,IAAI,CAACoH,oBAAoB,GAAG,EAAE;MAE9B,IAAI,CAAC7B,OAAO,CAAC3G,OAAO,CAACC,CAAC,IAAG;QACvB,IAAI,CAACuI,oBAAoB,CAACxK,IAAI,CAACiC,CAAC,CAACoI,QAAQ,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAI,CAACI,YAAY,EAAE;MAEnB;MACA,IAAI,CAACF,eAAe,CAAC3V,IAAI,CAAC,IAAI,CAAC4V,oBAAoB,CAAC;;EAExD;EAEA;;;EAGAjH,KAAKA,CAAA;IACH,IAAI,CAAC,IAAI,CAACmH,mBAAmB,EAAE,EAAE;MAC/B,IAAI,CAACF,oBAAoB,GAAG,EAAE;;EAElC;;;uBAjIWlY,iCAAiC;IAAA;EAAA;;;YAAjCA,iCAAiC;MAAA6D,SAAA;MAAAqK,MAAA;QAAAmI,OAAA;MAAA;MAAAvS,OAAA;QAAAmU,eAAA;MAAA;MAAAzQ,QAAA,GAAAtG,kEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmU,2CAAAjU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9ClD,4DAAA,oBAAe;UAGSA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAkB;UAC5CA,4DAAA,4BAAuB;UACfA,oDAAA,GAAsD;UAAAA,0DAAA,EAAO;UAGvEA,4DAAA,aAAiB;UACfA,wDAAA,IAAAoX,gDAAA,iBAOM;UACRpX,0DAAA,EAAM;UACNA,4DAAA,cAAiB;UAGRA,wDAAA,mBAAAqX,+DAAA;YAAA,OAASlU,GAAA,CAAA4M,KAAA,EAAO;UAAA,EAAC;UAAgD/P,oDAAA,aAAK;UAAAA,0DAAA,EAAI;UAC7EA,4DAAA,YAAkE;UAA/DA,wDAAA,mBAAAsX,+DAAA;YAAA,OAASnU,GAAA,CAAA2M,SAAA,EAAW;UAAA,EAAC;UAA0C9P,oDAAA,kBAAU;UAAAA,0DAAA,EAAI;;;UArBnEA,uDAAA,GAAkB;UAAlBA,wDAAA,mBAAkB;UAI3BA,uDAAA,GAAsD;UAAtDA,gEAAA,KAAAmD,GAAA,CAAA6T,oBAAA,CAAAjP,MAAA,OAAA5E,GAAA,CAAAgS,OAAA,CAAApN,MAAA,KAAsD;UAIf/H,uDAAA,GAAU;UAAVA,wDAAA,YAAAmD,GAAA,CAAAgS,OAAA,CAAU;UAYhCnV,uDAAA,GAA8C;UAA9CA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAzE,GAAA,CAAA+T,mBAAA,IAA8C;UAC1ClX,uDAAA,GAAwC;UAAxCA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAzE,GAAA,CAAAyM,aAAA,IAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBnB;AAExD;AAC8G;;;;;;;;;;ICKtG5P,4DAAA,aAA8D;IACrBA,oDAAA,GAA4C;;IAAAA,0DAAA,EAAe;;;;IAA3DA,uDAAA,GAA4C;IAA5CA,+DAAA,CAAAA,yDAAA,OAAAuR,MAAA,CAAAX,YAAA,CAAAuD,MAAA,EAA4C;;;;;IAErFnU,4DAAA,aAA8D;IACtBA,oDAAA,GAA2C;;IAAAA,0DAAA,EAAe;;;;IAA1DA,uDAAA,GAA2C;IAA3CA,+DAAA,CAAAA,yDAAA,OAAA6R,MAAA,CAAAjB,YAAA,CAAAwD,KAAA,EAA2C;;;;;IALrFpU,qEAAA,GAA0D;IACxDA,wDAAA,IAAAyX,mEAAA,iBAEM;IACNzX,wDAAA,IAAA0X,mEAAA,iBAEM;IACN1X,4DAAA,aAAqC;IACGA,oDAAA,YAAK;IAAAA,0DAAA,EAAe;IAE9DA,mEAAA,EAAe;;;;IATPA,uDAAA,GAAsB;IAAtBA,wDAAA,UAAAqI,MAAA,CAAAuP,eAAA,CAAsB;IAGtB5X,uDAAA,GAAsB;IAAtBA,wDAAA,UAAAqI,MAAA,CAAAuP,eAAA,CAAsB;;;;;IAWnB5X,4DAAA,WAA2C;IAAAA,oDAAA,GAA6B;IAAAA,0DAAA,EAAO;;;;IAApCA,uDAAA,GAA6B;IAA7BA,gEAAA,MAAAsK,MAAA,CAAAuN,YAAA,CAAA1B,GAAA,MAA6B;;;;;IAM7EnW,4DAAA,WACG;IAAAA,oDAAA,GAAoC;IAAAA,0DAAA,EACtC;;;;IADEA,uDAAA,GAAoC;IAApCA,gEAAA,MAAAwK,MAAA,CAAAqN,YAAA,CAAAzB,UAAA,MAAoC;;;;;IAOvCpW,4DAAA,WAA6C;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAO;;;;IAAtCA,uDAAA,GAA+B;IAA/BA,gEAAA,MAAAqR,MAAA,CAAAwG,YAAA,CAAAxB,KAAA,MAA+B;;;;;IAM5ErW,4DAAA,WACG;IAAAA,oDAAA,GAAmC;IAAAA,0DAAA,EACrC;;;;IADEA,uDAAA,GAAmC;IAAnCA,gEAAA,MAAA8X,MAAA,CAAAD,YAAA,CAAAvB,SAAA,MAAmC;;;;;IAvB1CtW,4DAAA,aAAqC;IAEhCA,oDAAA,WAAI;IAAAA,wDAAA,IAAA+X,mEAAA,mBAA+E;IAAA/X,0DAAA,EACrF;IAEHA,4DAAA,aAAqC;IAEhCA,oDAAA,kBACD;IAAAA,wDAAA,IAAAgY,mEAAA,mBAEC;IAAAhY,0DAAA,EACF;IAEHA,4DAAA,aAAqC;IAEhCA,oDAAA,cACD;IAAAA,wDAAA,KAAAiY,oEAAA,mBAAmF;IAAAjY,0DAAA,EACpF;IAEHA,4DAAA,cAAqC;IAEhCA,oDAAA,kBACD;IAAAA,wDAAA,KAAAkY,oEAAA,mBAEC;IAAAlY,0DAAA,EACF;;;;IAvBaA,uDAAA,GAAkC;IAAlCA,wDAAA,SAAAkK,MAAA,CAAA2N,YAAA,IAAA3N,MAAA,CAAA4J,MAAA,CAAAxR,KAAA,CAAkC;IAMvCtC,uDAAA,GAAyC;IAAzCA,wDAAA,SAAAkK,MAAA,CAAA2N,YAAA,IAAA3N,MAAA,CAAA6J,aAAA,CAAAzR,KAAA,CAAyC;IAQzCtC,uDAAA,GAAoC;IAApCA,wDAAA,SAAAkK,MAAA,CAAA2N,YAAA,IAAA3N,MAAA,CAAA8J,QAAA,CAAA1R,KAAA,CAAoC;IAMpCtC,uDAAA,GAAwC;IAAxCA,wDAAA,SAAAkK,MAAA,CAAA2N,YAAA,IAAA3N,MAAA,CAAA+J,YAAA,CAAA3R,KAAA,CAAwC;;;;;IApCvDtC,4DAAA,aAAyE;IACvEA,wDAAA,IAAAmY,6DAAA,0BAUe;IAEfnY,wDAAA,IAAAoY,4DAAA,iCAAApY,oEAAA,CA4Bc;IAChBA,0DAAA,EAAM;;;;;IA1C2CA,wDAAA,cAAAK,MAAA,CAAAiY,OAAA,GAAuB;IACvDtY,uDAAA,GAAsB;IAAtBA,wDAAA,UAAAK,MAAA,CAAAkY,aAAA,CAAsB,aAAAC,GAAA;;;;;;;;ADMrC,MAAOzZ,+BAA+B;EAW1C4B,YAAoB8X,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAFtC,KAAA7H,YAAY,GAAGA,8DAAY;EAEkC;EAE7D3P,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACyX,SAAS,EAAE;MACnB,IAAI,CAACxX,WAAW,EAAE;;EAEtB;EAEAiN,WAAWA,CAAA;IACT,IAAI,CAACoK,aAAa,GAAG,IAAI,CAACI,YAAY,IAAIjI,kEAAgB,CAACmE,OAAO;IAClE,IAAI,CAAC+C,eAAe,GAAG,IAAI,CAACe,YAAY,IAAIjI,kEAAgB,CAAC2D,KAAK;IAClE,IAAI,CAACuE,SAAS,GAAG,IAAI,CAACL,aAAa,GAAG,cAAc,GAAG,YAAY;EACrE;EAEAD,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACI,SAAS,EAAE;MACnB,OAAO,IAAI,CAACpH,UAAU;;IAExB,OAAO,IAAI,CAACoH,SAAS;EACvB;EAEA;;;EAGQxX,WAAWA,CAAA;IACjB,IAAI,CAACoQ,UAAU,GAAG,IAAI3R,qDAAS,CAAC;MAC9BmU,MAAM,EAAE,IAAIlU,uDAAW,CAAC,IAAI,CAAC;MAC7BmU,aAAa,EAAE,IAAInU,uDAAW,CAAC,IAAI,CAAC;MACpCoU,QAAQ,EAAE,IAAIpU,uDAAW,CAAC,IAAI,CAAC;MAC/BqU,YAAY,EAAE,IAAIrU,uDAAW,CAAC,IAAI;KACnC,CAAC;IAEF,IAAI,CAAC0R,UAAU,CAACzP,YAAY,CAACC,SAAS,CAAC+W,CAAC,IAAG;MACzC,IAAIC,qBAAqB,GAAG,EAAE;MAE9B,IAAI,IAAI,CAAC/E,aAAa,CAACzR,KAAK,EAAE;QAC5BwW,qBAAqB,CAACtM,IAAI,CAACiE,mEAAiB,CAAC2F,UAAU,CAAC;;MAE1D,IAAI,IAAI,CAACpC,QAAQ,CAAC1R,KAAK,EAAE;QACvBwW,qBAAqB,CAACtM,IAAI,CAACiE,mEAAiB,CAAC4F,KAAK,CAAC;;MAErD,IAAI,IAAI,CAACpC,YAAY,CAAC3R,KAAK,EAAE;QAC3BwW,qBAAqB,CAACtM,IAAI,CAACiE,mEAAiB,CAAC6F,SAAS,CAAC;;MAEzD,IAAI,IAAI,CAACxC,MAAM,CAACxR,KAAK,EAAE;QACrBwW,qBAAqB,CAACtM,IAAI,CAACiE,mEAAiB,CAAC0F,GAAG,CAAC;;MAGnD;MACA,IAAI,CAACsC,kBAAkB,CAACM,cAAc,CAACD,qBAAqB,CAAC;IAC/D,CAAC,CAAC;EACJ;EAEA,IAAI/E,aAAaA,CAAA;IACf,OAAO,IAAI,CAACzC,UAAU,CAAC9O,GAAG,CAAC,eAAe,CAAC;EAC7C;EAEA,IAAIwR,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC1C,UAAU,CAAC9O,GAAG,CAAC,UAAU,CAAC;EACxC;EAEA,IAAIyR,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC3C,UAAU,CAAC9O,GAAG,CAAC,cAAc,CAAC;EAC5C;EAEA,IAAIsR,MAAMA,CAAA;IACR,OAAO,IAAI,CAACxC,UAAU,CAAC9O,GAAG,CAAC,QAAQ,CAAC;EACtC;;;uBA9EWzD,+BAA+B,EAAAiB,+DAAA,CAAA/C,sEAAA;IAAA;EAAA;;;YAA/B8B,+BAA+B;MAAA4D,SAAA;MAAAqK,MAAA;QAAA2L,YAAA;QAAAD,SAAA;QAAAO,UAAA;QAAApB,YAAA;MAAA;MAAAvR,QAAA,GAAAtG,kEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkW,yCAAAhW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd5ClD,4DAAA,oBAAe;UAIPA,oDAAA,GACF;UAAAA,0DAAA,EAAkB;UAEpBA,wDAAA,IAAAmZ,8CAAA,iBA0CM;UACRnZ,0DAAA,EAAsB;;;UAjDDA,uDAAA,GAAiB;UAAjBA,wDAAA,kBAAiB,YAAAA,6DAAA,IAAA4H,GAAA,EAAAzE,GAAA,CAAA8V,UAAA;UAGhCjZ,uDAAA,GACF;UADEA,gEAAA,MAAAmD,GAAA,CAAAyV,SAAA,MACF;UAEgB5Y,uDAAA,GAA6B;UAA7BA,wDAAA,SAAAmD,GAAA,CAAAuV,SAAA,IAAAvV,GAAA,CAAAmO,UAAA,CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACJU;AAEN;AACG;AAEzB;AAgBF;;;;;;;;;;;;;;;;;ICdvBtR,4DAAA,aAAkE;IAChEA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,gBAAAK,MAAA,CAAAoZ,SAAA,CAAAC,QAAA,CAAA3R,MAAA,OACF;;;;;;IAIA/H,4DAAA,UAA8B;IAG1BA,wDAAA,+BAAA2Z,0FAAA;MAAA3Z,2DAAA,CAAA4Z,IAAA;MAAA,MAAAC,OAAA,GAAA7Z,2DAAA;MAAA,OAAqBA,yDAAA,CAAA6Z,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC,2BAAAC,sFAAA;MAAA/Z,2DAAA,CAAA4Z,IAAA;MAAA,MAAAI,OAAA,GAAAha,2DAAA;MAAA,OACxBA,yDAAA,CAAAga,OAAA,CAAAC,aAAA,EAAe;IAAA,EADS;IAE1Cja,0DAAA,EAA8B;;;;IAH7BA,uDAAA,GAAqC;IAArCA,wDAAA,mBAAAqI,MAAA,CAAAoR,SAAA,CAAAC,QAAA,CAAqC;;;;;;IA6BrC1Z,4DAAA,aAAsC;IAElCA,wDAAA,oBAAAka,+DAAAtT,MAAA;MAAA5G,2DAAA,CAAAma,IAAA;MAAA,MAAAC,OAAA,GAAApa,2DAAA;MAAA,OAAUA,yDAAA,CAAA4G,MAAA,GAASwT,OAAA,CAAAC,YAAA,EAAc,GAAG,IAAI;IAAA,EAAC;IAK3Cra,0DAAA,EAAe;;;;IAJbA,uDAAA,GAAmD;IAAnDA,wDAAA,YAAA0D,MAAA,CAAA+V,SAAA,CAAAa,QAAA,MAAA5W,MAAA,CAAA6W,aAAA,GAAmD,kBAAA7W,MAAA,CAAA+V,SAAA,CAAAa,QAAA,OAAA5W,MAAA,CAAA6W,aAAA,kBAAA7W,MAAA,CAAA8W,aAAA;;;;;;IAMvDxa,4DAAA,aAAmC;IAE/BA,wDAAA,mBAAAya,8DAAA;MAAA,MAAAC,WAAA,GAAA1a,2DAAA,CAAA2a,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9a,2DAAA;MAAA,OAASA,yDAAA,CAAA8a,OAAA,CAAArB,SAAA,CAAAsB,MAAA,CAAAH,OAAA,CAAqB;IAAA,EAAC,oBAAAI,+DAAApU,MAAA;MAAA,MAAA8T,WAAA,GAAA1a,2DAAA,CAAA2a,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAAjb,2DAAA;MAAA,OACrBA,yDAAA,CAAA4G,MAAA,GAASqU,OAAA,CAAAxB,SAAA,CAAAsB,MAAA,CAAAH,OAAA,CAAqB,GAAG,IAAI;IAAA,EADhB;IAKjC5a,0DAAA,EAAe;;;;;IAHbA,uDAAA,GAAqC;IAArCA,wDAAA,YAAAkK,MAAA,CAAAuP,SAAA,CAAAyB,UAAA,CAAAN,OAAA,EAAqC,eAAA1Q,MAAA,CAAAsQ,aAAA,CAAAI,OAAA;;;;;IAQzC5a,4DAAA,aAAmE;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAK;;;;;IAC3EA,4DAAA,aAAuC;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAK;;;;IAA1BA,uDAAA,GAAqB;IAArBA,+DAAA,CAAAmb,WAAA,CAAAC,OAAA,CAAqB;;;;;IAI5Dpb,4DAAA,aAAsC;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IACjDA,4DAAA,aAAuC;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAK;;;;IAAhCA,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAqb,WAAA,CAAAvF,aAAA,CAA2B;;;;;IAIlE9V,4DAAA,aAAmE;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAC9EA,4DAAA,aAAuC;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;;;;IAA7BA,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAsb,WAAA,CAAAC,UAAA,CAAwB;;;;;IAI/Dvb,4DAAA,aAAmE;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAK;;;;;IAClFA,4DAAA,aAAuC;IAAAA,oDAAA,GAAyC;;IAAAA,0DAAA,EAAK;;;;IAA9CA,uDAAA,GAAyC;IAAzCA,+DAAA,CAAAA,yDAAA,OAAAwb,WAAA,CAAAC,QAAA,EAAyC;;;;;IAIhFzb,4DAAA,aAAmE;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAK;;;;;IAC/EA,4DAAA,aAAuC;IAKlCA,oDAAA,GAAyB;IAAAA,0DAAA,EAC3B;;;;IAHCA,uDAAA,GAA0D;IAA1DA,oEAAA,yCAAA2b,WAAA,CAAAC,SAAA,KAA0D;IAEzD5b,uDAAA,GAAyB;IAAzBA,+DAAA,CAAA2b,WAAA,CAAAE,WAAA,CAAyB;;;;;IAM9B7b,4DAAA,aAAmE;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;;;;;IAC7EA,4DAAA,aAAuC;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAK;;;;IAA5BA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAA8b,WAAA,CAAAC,SAAA,CAAuB;;;;;IAI9D/b,4DAAA,aAAqF;IACnFA,oDAAA,mBACF;IAAAA,0DAAA,EAAK;;;;IAFiDA,wDAAA,cAAAgc,OAAA,CAAArJ,gBAAA,CAA8B;;;;;IAGpF3S,4DAAA,aAAuC;IACrCA,oDAAA,GAKF;;;IAAAA,0DAAA,EAAK;;;;;IALHA,uDAAA,GAKF;IALEA,gEAAA,MAAAic,OAAA,CAAAtJ,gBAAA,GAAA3S,yDAAA,OAAAmc,WAAA,CAAAC,SAAA,qBAAApc,yDAAA,OAAAmc,WAAA,CAAAC,SAAA,sBAKF;;;;;IAGFpc,uDAAA,aAA0E;;;;;;;;;;;IAC1EA,4DAAA,aAKC;IAFCA,wDAAA,mBAAAqc,oDAAA;MAAA,MAAA3B,WAAA,GAAA1a,2DAAA,CAAAsc,IAAA;MAAA,MAAAC,OAAA,GAAA7B,WAAA,CAAAG,SAAA;MAAA,MAAA2B,OAAA,GAAAxc,2DAAA;MAAA,OAASA,yDAAA,CAAAwc,OAAA,CAAAC,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAExBvc,0DAAA,EAAK;;;;;IADJA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,EAAA8U,OAAA,CAAAC,aAAA,CAAAJ,OAAA,GAA+C;;;;;;AD/F3D,MAAMjU,gBAAgB,GAAa,CACjC,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,WAAW,EACX,WAAW,CACZ;AACD,MAAMsU,cAAc,GAAa,CAC/B,QAAQ,EACR,SAAS,EACT,eAAe,EACf,aAAa,EACb,WAAW,EACX,WAAW,CACZ;AAOK,MAAO9hB,gBAAiB,SAAQye,iEAA6B;EAoBjE5Y,YACUoK,MAAc,EACd8R,eAAgC,EAChC/Y,cAA8B,EAC9BlD,cAA8B,EAC9Bkc,kBAAsC;IAE9C,KAAK,CAACxU,gBAAgB,CAAC;IANf,KAAAyC,MAAM,GAANA,MAAM;IACN,KAAA8R,eAAe,GAAfA,eAAe;IACf,KAAA/Y,cAAc,GAAdA,cAAc;IACd,KAAAlD,cAAc,GAAdA,cAAc;IACd,KAAAkc,kBAAkB,GAAlBA,kBAAkB;IAxB5B,KAAAC,UAAU,GAAG,IAAI3D,uEAAkB,EAAS;IAC5C,KAAA4D,UAAU,GAAY,EAAE;IACxB,KAAAC,kBAAkB,GAAgB,EAAE;IAKpC,KAAAtK,gBAAgB,GAAY,KAAK;IACjC,KAAAlC,iBAAiB,GAAGA,4DAAiB;IAIrC,KAAAyM,kBAAkB,GAAY,IAAI;IAClC,KAAAC,WAAW,GAAoB,EAAE;IAwKjC;IACA;IACA;IACA,KAAA1D,SAAS,GAAG,IAAIH,qEAAc,CAAQ,IAAI,EAAE,EAAE,CAAC;EA7J/C;EAEArY,QAAQA,CAAA;IACN;IACA,IAAI,CAACsS,cAAc,GAAG,IAAI,CAAC3S,cAAc,CAAC4S,UAAU,EAAE;IAEtD,IAAI,CAAC,IAAI,CAACD,cAAc,EAAE;MACxB,IAAI,CAACA,cAAc,GAAG,IAAIiG,yDAAc,EAAE;MAC1C,IAAI,CAACjG,cAAc,CAACpP,IAAI,GAAGmM,mCAAM,EAAE,CAAC8M,MAAM,EAAE;MAC5C,IAAI,CAAC7J,cAAc,CAACc,KAAK,GAAG,KAAK;MACjC,IAAI,CAACd,cAAc,CAACY,MAAM,GAAG,IAAI;MACjC,IAAI,CAACZ,cAAc,CAACa,KAAK,GAAG,IAAI;MAChC,IAAI,CAACb,cAAc,CAACe,OAAO,GAAG,KAAK;MACnC,IAAI,CAACf,cAAc,CAACgB,MAAM,GAAG,IAAI;MACjC,IAAI,CAAChB,cAAc,CAACiB,aAAa,GAAG,IAAI;MACxC,IAAI,CAACjB,cAAc,CAACkB,QAAQ,GAAG,IAAI;MACnC,IAAI,CAAClB,cAAc,CAACmB,YAAY,GAAG,KAAK;MAExC,IAAI,CAAC9T,cAAc,CAAC+T,UAAU,CAAC,IAAI,CAACpB,cAAc,CAAC;;EAEvD;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC4J,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAAC3J,WAAW,EAAE;;EAEnC;EAEA4J,cAAcA,CAAC9H,OAA4B;IACzC,IAAI,CAAC+H,aAAa,GAAG/H,OAAO;IAE5B,IAAI,CAACyE,aAAa,EAAE;IAEpB,IAAI,CAACH,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,CAAC+D,KAAK,EAAE;IACtB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACR,kBAAkB,GAAG,IAAI;EAChC;EAEAS,gBAAgBA,CAACC,MAAe;IAC9B,IAAI,CAACJ,aAAa,CAACK,UAAU,GAAG,IAAI,CAACC,WAAW,CAACD,UAAU;IAC3D,IAAI,CAACL,aAAa,CAACO,SAAS,GAAG,IAAI,CAACD,WAAW,CAACC,SAAS;IACzD,IAAI,CAACP,aAAa,CAACQ,MAAM,GAAG,IAAI,CAACF,WAAW,CAACE,MAAM;IACnD,IAAI,CAACR,aAAa,CAACS,aAAa,GAAG,IAAI,CAACH,WAAW,CAACG,aAAa;IACjE,IAAI,CAACT,aAAa,CAACU,MAAM,GAAGN,MAAM;IAElC,IACE,CAAC,IAAI,CAACJ,aAAa,CAACK,UAAU,IAC9B,IAAI,CAACL,aAAa,CAACK,UAAU,IAAIM,SAAS,IAC1C,IAAI,CAACX,aAAa,CAACK,UAAU,IAAI,CAAC,EAClC;MACA,IAAI,CAACL,aAAa,CAACK,UAAU,GAAG,EAAE;;EAEtC;EAEA3D,aAAaA,CAAA;IACX,IAAI,CAACyD,gBAAgB,CAAC,KAAK,CAAC;IAC5B,IAAI,CAAC5Z,cAAc,CAACqa,KAAK,EAAE;IAC3B,IAAI,CAACtB,eAAe,CAACuB,+BAA+B,CAAC,IAAI,CAACb,aAAa,CAAC,CAACzb,SAAS,CAAC;MACjFuc,IAAI,EAAGC,QAAmB,IAAI;QAC5B,IAAI,CAACC,YAAY,CAACD,QAAQ,CAACE,MAAM,CAAC;QAClC,IAAI,CAAC1a,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;QAC1B,IAAI,CAACE,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEQH,YAAYA,CAACK,MAAe;IAClC,IAAI,CAAC5B,UAAU,GAAG4B,MAAM;IACxB,IAAI,CAAC3B,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACQ,aAAa,GAAG,IAAI;IAEzB,IAAI,CAAC,IAAI,CAACT,UAAU,IAAI,IAAI,CAACA,UAAU,CAACjV,MAAM,IAAI,CAAC,EAAE;MACnD,IAAI,CAACgV,UAAU,CAAC3a,IAAI,GAAG,EAAE;MACzB,IAAI,CAACyc,SAAS,GAAG,CAAC;KACnB,MAAM;MACL,IAAI,CAAC9B,UAAU,CAAC3a,IAAI,GAAG,IAAI,CAAC4a,UAAU;MACtC,IAAI,CAAC6B,SAAS,GAAG,IAAI,CAAC7B,UAAU,CAAC,CAAC,CAAC,CAAC8B,SAAS;;EAEjD;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU,CAACxc,GAAG,CAAC,WAAW,CAAC;EACzC;EAEAyc,eAAeA,CAAC7P,KAA0B;IACxC,IAAI,CAAC8P,cAAc,GAAG9P,KAAK,CAAC6F,cAAc;IAC1C,IAAI,CAACH,gBAAgB,GAAG1F,KAAK,CAAC2F,gBAAgB;IAC9C,IAAI,CAACpC,gBAAgB,GAAGvD,KAAK,CAAC8F,gBAAgB;IAE9C,IAAI,IAAI,CAACvC,gBAAgB,EAAE;MACzB,IAAI,CAACrK,gBAAgB,GAAGsU,cAAc;KACvC,MAAM;MACL,IAAI,CAACtU,gBAAgB,GAAGA,gBAAgB;;EAE5C;EAEA6W,cAAcA,CAACC,IAAe;IAC5B,IAAIC,WAAW,GAAG,GAAG;IAErB;IACA,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACE,OAAO,CAACvX,MAAM,GAAG,CAAC,EAAE;MAC3CqX,IAAI,CAACE,OAAO,CAAC9Q,OAAO,CAAC+Q,GAAG,IAAG;QACzB;QACA,IAAIA,GAAG,CAACC,kBAAkB,IAAID,GAAG,CAACC,kBAAkB,CAACzX,MAAM,GAAG,CAAC,EAAE;UAC/DwX,GAAG,CAACC,kBAAkB,CAAChR,OAAO,CAACiR,WAAW,IAAG;YAC3CJ,WAAW,IAAII,WAAW,GAAG,IAAI;UACnC,CAAC,CAAC;;MAEN,CAAC,CAAC;;IAGJ;IACA,IAAIL,IAAI,CAACM,eAAe,IAAIN,IAAI,CAACM,eAAe,CAAC3X,MAAM,GAAG,CAAC,EAAE;MAC3DqX,IAAI,CAACM,eAAe,CAAClR,OAAO,CAAC+Q,GAAG,IAAG;QACjCF,WAAW,IAAIE,GAAG,CAACI,UAAU,GAAG,IAAI;MACtC,CAAC,CAAC;;IAGJN,WAAW,GAAGA,WAAW,CAACxS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtCwS,WAAW,IAAI,GAAG;IAClB,IAAIA,WAAW,CAACtX,MAAM,IAAI,CAAC,EAAE;MAC3BsX,WAAW,GAAG,EAAE;;IAGlB,OAAOA,WAAW;EACpB;EAEAO,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACV,cAAc,EAAE;MACxB,OAAO,IAAI;KACZ,MAAM;MACL,OAAO,CAAC,IAAI,CAACvM,gBAAgB;;EAEjC;EAEAkN,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACpC,aAAa,EAAE;MACtB,IAAI,IAAI,CAACA,aAAa,CAACqC,WAAW,EAAE;QAClC,OAAO,KAAK;OACb,MAAM;QACL,OAAO,IAAI;;KAEd,MAAM;MACL,OAAO,KAAK;;EAEhB;EAOA;EACAvF,aAAaA,CAAA;IACX,MAAMwF,WAAW,GAAG,IAAI,CAACtG,SAAS,CAACC,QAAQ,CAAC3R,MAAM;IAClD,IAAIiY,OAAO,GAAG,CAAC;IAEf,IAAI,IAAI,CAACjD,UAAU,CAAC3a,IAAI,EAAE;MACxB4d,OAAO,GAAG,IAAI,CAACjD,UAAU,CAAC3a,IAAI,CAAC2F,MAAM;;IAEvC,OAAOgY,WAAW,KAAKC,OAAO;EAChC;EAEA;EACA3F,YAAYA,CAAA;IACV,IAAI,CAACE,aAAa,EAAE,GAChB,IAAI,CAACd,SAAS,CAAC+D,KAAK,EAAE,GACtB,IAAI,CAACT,UAAU,CAAC3a,IAAI,CAACoM,OAAO,CAACyR,GAAG,IAAI,IAAI,CAACxG,SAAS,CAAClJ,MAAM,CAAC0P,GAAG,CAAC,CAAC;EACrE;EAEA;EACAzF,aAAaA,CAACyF,GAAW;IACvB,IAAI,CAACA,GAAG,EAAE;MACR,OAAO,GAAG,IAAI,CAAC1F,aAAa,EAAE,GAAG,QAAQ,GAAG,UAAU,MAAM;;IAE9D,OAAO,GAAG,IAAI,CAACd,SAAS,CAACyB,UAAU,CAAC+E,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM;EACxE;EAEAxD,QAAQA,CAACwD,GAAU;IACjB,IACE,IAAI,CAACxC,aAAa,IAClB,IAAI,CAACA,aAAa,CAACrC,OAAO,KAAK6E,GAAG,CAAC7E,OAAO,IAC1C,IAAI,CAACqC,aAAa,CAACyC,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,KAAKF,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,EAClE;MACA,IAAI,CAAC1C,aAAa,GAAG,IAAI;MACzB,IAAI,CAACR,kBAAkB,GAAG,IAAI;KAC/B,MAAM;MACL,IAAI,CAACQ,aAAa,GAAGwC,GAAG;MACxB,IAAI,CAAChD,kBAAkB,GAAGgD,GAAG,CAACC,KAAK;;IAGrC;IACA,IAAI,CAACzG,SAAS,CAACsB,MAAM,CAACkF,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACtN,gBAAgB,IAAI,IAAI,CAAC8G,SAAS,CAACC,QAAQ,CAAC3R,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAAC0V,aAAa,GAAG,IAAI,CAAChE,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACuD,kBAAkB,GAAG,IAAI,CAACxD,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACwG,KAAK;;EAE9D;EAEAE,UAAUA,CAAChR,KAAgB;IACzB,IAAI,CAAC0K,iBAAiB,EAAE;IACxB;IACA,IAAI,CAACuG,cAAc,CAACjR,KAAK,CAAC;IAC1B,IAAI,CAAC6K,aAAa,EAAE;EACtB;EAEAqG,UAAUA,CAAClR,KAAW;IACpB,IAAI,CAACyO,WAAW,CAACC,SAAS,GAAG,CAAC;IAC9B,IAAI,CAACD,WAAW,CAACE,MAAM,GAAG3O,KAAK,CAACmR,MAAM;IACtC,IAAI,CAAC1C,WAAW,CAACG,aAAa,GAAG5O,KAAK,CAACoR,SAAS;IAChD,IAAI,CAACvG,aAAa,EAAE;EACtB;EAEA0C,aAAaA,CAACsD,GAAU;IACtB,IAAI,IAAI,CAACtN,gBAAgB,EAAE;MACzB;MACA,OAAO,IAAI,CAAC8G,SAAS,CAACyB,UAAU,CAAC+E,GAAG,CAAC;KACtC,MAAM;MACL,OACE,IAAI,CAACxC,aAAa,IAClB,IAAI,CAACA,aAAa,CAACrC,OAAO,KAAK6E,GAAG,CAAC7E,OAAO,IAC1C,IAAI,CAACqC,aAAa,CAACyC,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,KAAKF,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU;;EAGxE;EAEAM,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC,IAAI,CAAC9N,gBAAgB,IAAI,IAAI,CAAC8K,aAAa,EAAE;MAChD,OAAO,IAAI;MACX;KACD,MAAM;MACL,OAAOxb,OAAO,CAAC,IAAI,CAAC0Q,gBAAgB,IAAI,IAAI,CAAC8G,SAAS,CAACC,QAAQ,CAAC3R,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC0V,aAAa,CAAC;;EAEtG;EAEA;EACA;EACA;EACAiD,cAAcA,CAAA;IACZ,IAAI,CAAChD,gBAAgB,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC5Z,cAAc,CAACqa,KAAK,EAAE;IAE3B,IAAI,IAAI,CAACrJ,gBAAgB,KAAKnE,+DAAoB,CAAC2E,KAAK,EAAE;MACxD,IAAI,CAACqL,cAAc,EAAE;MACrB;;IAEF,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC7D,kBAAkB,CAAC+D,mBAAmB,CAAC,IAAI,CAACtD,aAAa,CAAC,CAACzb,SAAS,CAAC;MACxEuc,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEwC,IAAI,EAAE;UAClB,IAAI,CAACC,mBAAmB,CAACzC,QAAQ,CAACwC,IAAI,CAAC;;QAEzC,IAAI,CAAChd,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAmC,aAAaA,CAAA;IACX,IAAI,CAAC9D,kBAAkB,CAACkE,kBAAkB,CAAC,IAAI,CAACzD,aAAa,CAAC,CAACzb,SAAS,CAAC;MACvEuc,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEwC,IAAI,EAAE;UAClB,IAAI,CAACC,mBAAmB,CAACzC,QAAQ,CAACwC,IAAI,CAAC;;QAEzC,IAAI,CAAChd,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAwC,kBAAkBA,CAAA;IAChB,IAAI,CAACnd,cAAc,CAACqa,KAAK,EAAE;IAC3B,IAAI,IAAI,CAACrJ,gBAAgB,IAAInE,+DAAoB,CAACqE,IAAI,EAAE;MACtD,IAAI,CAACkM,mBAAmB,EAAE;KAC3B,MAAM;MACL,IAAI,CAACC,oBAAoB,EAAE;;EAE/B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACrE,kBAAkB,CAACsE,2BAA2B,CAAC,IAAI,CAAC3D,aAAa,CAACrC,OAAO,CAAC,CAACtZ,SAAS,CAAC;MACxFuc,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEwC,IAAI,EAAE;UAClB,IAAI,CAACC,mBAAmB,CAACzC,QAAQ,CAACwC,IAAI,CAAC;;QAEzC,IAAI,CAAChd,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAyC,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACzD,aAAa,CAACyC,KAAK,IAAI,CAAC,IAAI,CAACzC,aAAa,CAACyC,KAAK,CAACnY,MAAM,EAAE;MACjE;;IAEF,MAAMsZ,MAAM,GAAW,IAAI,CAAC5D,aAAa,CAACyC,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU;IAE7D,IAAI,CAACrD,kBAAkB,CAACwE,yBAAyB,CAAC,IAAI,CAAC7D,aAAa,CAACrC,OAAO,EAAEiG,MAAM,CAAC,CAACvf,SAAS,CAAC;MAC9Fuc,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAEwC,IAAI,EAAE;UAClB,IAAI,CAACC,mBAAmB,CAACzC,QAAQ,CAACwC,IAAI,CAAC;;QAEzC,IAAI,CAAChd,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAsC,mBAAmBA,CAACD,IAAY;IAC9B,IAAI,IAAI,CAAC5B,cAAc,EAAE;MACvB,IAAI,CAACqC,sBAAsB,CAACT,IAAI,CAAC;KAClC,MAAM;MACL,IAAI,CAACU,iBAAiB,CAACV,IAAI,CAAC;;EAEhC;EAEQS,sBAAsBA,CAACT,IAAY;IACzC,IAAIW,cAAc,GAAQ/V,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IACnE8V,cAAc,CAACC,IAAI,GAAG,UAAU,GAAGZ,IAAI;IACvCW,cAAc,CAACE,KAAK,EAAE;EACxB;EAEQH,iBAAiBA,CAACV,IAAY;IACpC,IAAI,CAAChE,kBAAkB,CAAC8E,eAAe,CAACd,IAAI,CAAC;IAC7C,IAAI,CAAC/V,MAAM,CAACQ,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;;;uBAxXWzQ,gBAAgB,EAAAkF,+DAAA,CAAA/C,oDAAA,GAAA+C,+DAAA,CAAAgG,4DAAA,GAAAhG,+DAAA,CAAAgG,2DAAA,GAAAhG,+DAAA,CAAAgG,2DAAA,GAAAhG,+DAAA,CAAAgG,+DAAA;IAAA;EAAA;;;YAAhBlL,gBAAgB;MAAA6H,SAAA;MAAAof,SAAA,WAAAC,uBAAA9e,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;oEAkBhBmW,4DAAO;;;;;;;;;;;;;UCnEpBrZ,4DAAA,aAAkC;UACVA,wDAAA,4BAAAiiB,yEAAArb,MAAA;YAAA,OAAkBzD,GAAA,CAAAma,cAAA,CAAA1W,MAAA,CAAsB;UAAA,EAAC,6BAAAsb,0EAAAtb,MAAA;YAAA,OAAoBzD,GAAA,CAAA8b,eAAA,CAAArY,MAAA,CAAuB;UAAA,EAA3C;UAC/D5G,0DAAA,EAAuB;UAEvBA,4DAAA,aAAsB;UAINA,oDAAA,aAAM;UAAAA,0DAAA,EAAS;UAACA,4DAAA,cAA+B;UAACA,oDAAA,GAAsB;UAAAA,0DAAA,EAAO;UAEvFA,wDAAA,KAAAmiB,+BAAA,gBAEK;UACPniB,0DAAA,EAAM;UAENA,4DAAA,cAAoB;UAClBA,wDAAA,KAAAoiB,gCAAA,iBAMM;UAENpiB,4DAAA,wBAAiF;UAA/CA,wDAAA,qBAAAqiB,2DAAA;YAAA,OAAWlf,GAAA,CAAAud,cAAA,EAAgB;UAAA,EAAC;UAAmB1gB,0DAAA,EAAe;UAEhGA,uDAAA,aAAgD;UAClDA,0DAAA,EAAM;UAKVA,4DAAA,eAAsB;UAWdA,wDAAA,2BAAAsiB,0DAAA1b,MAAA;YAAA,OAAiBzD,GAAA,CAAAmd,UAAA,CAAA1Z,MAAA,CAAkB;UAAA,EAAC;UAGpC5G,qEAAA,QAAoC;UAClCA,wDAAA,KAAAuiB,+BAAA,iBAQK;UACLviB,wDAAA,KAAAwiB,+BAAA,iBAQK;UACPxiB,mEAAA,EAAe;UAEfA,qEAAA,QAAqC;UACnCA,wDAAA,KAAAyiB,+BAAA,iBAA2E;UAC3EziB,wDAAA,KAAA0iB,+BAAA,iBAAiE;UACnE1iB,mEAAA,EAAe;UAEfA,qEAAA,QAA2C;UACzCA,wDAAA,KAAA2iB,+BAAA,iBAAiD;UACjD3iB,wDAAA,KAAA4iB,+BAAA,iBAAuE;UACzE5iB,mEAAA,EAAe;UAEfA,qEAAA,QAAwC;UACtCA,wDAAA,KAAA6iB,+BAAA,iBAA8E;UAC9E7iB,wDAAA,KAAA8iB,+BAAA,iBAAoE;UACtE9iB,mEAAA,EAAe;UAEfA,qEAAA,QAAsC;UACpCA,wDAAA,KAAA+iB,+BAAA,iBAAkF;UAClF/iB,wDAAA,KAAAgjB,+BAAA,iBAAqF;UACvFhjB,mEAAA,EAAe;UAEfA,qEAAA,QAAyC;UACvCA,wDAAA,KAAAijB,+BAAA,iBAA+E;UAC/EjjB,wDAAA,KAAAkjB,+BAAA,iBAOK;UACPljB,mEAAA,EAAe;UAEfA,qEAAA,QAAuC;UACrCA,wDAAA,KAAAmjB,+BAAA,iBAA6E;UAC7EnjB,wDAAA,KAAAojB,+BAAA,iBAAmE;UACrEpjB,mEAAA,EAAe;UAEfA,qEAAA,QAAuC;UACrCA,wDAAA,KAAAqjB,+BAAA,iBAEK;UACLrjB,wDAAA,KAAAsjB,+BAAA,iBAMK;UACPtjB,mEAAA,EAAe;UAEfA,wDAAA,KAAAujB,+BAAA,iBAA0E;UAC1EvjB,wDAAA,KAAAwjB,+BAAA,iBAKM;UACRxjB,0DAAA,EAAQ;UAEVA,4DAAA,yBAMC;UADCA,wDAAA,kBAAAyjB,yDAAA7c,MAAA;YAAA,OAAQzD,GAAA,CAAAid,UAAA,CAAAxZ,MAAA,CAAkB;UAAA,EAAC;UAC5B5G,0DAAA,EAAgB;UAInBA,4DAAA,eAA2B;UAOnBA,wDAAA,wBAAA0jB,gEAAA;YAAA,OAAcvgB,GAAA,CAAA8d,kBAAA,EAAoB;UAAA,EAAC;UACpCjhB,0DAAA,EAAiB;;;UAxIsCA,uDAAA,GAAsB;UAAtBA,gEAAA,OAAAmD,GAAA,CAAA0b,SAAA,MAAsB;UAEzD7e,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAmD,GAAA,CAAAwP,gBAAA,CAAsB;UAMvC3S,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAmD,GAAA,CAAAwP,gBAAA,CAAsB;UAQmC3S,uDAAA,GAAiB;UAAjBA,wDAAA,kBAAiB;UAa9EA,uDAAA,GAAyB;UAAzBA,wDAAA,eAAAmD,GAAA,CAAA4Z,UAAA,CAAyB;UAgFL/c,uDAAA,IAAmC;UAAnCA,wDAAA,oBAAAmD,GAAA,CAAAmF,gBAAA,CAAmC;UAGhCtI,uDAAA,GAAyB;UAAzBA,wDAAA,qBAAAmD,GAAA,CAAAmF,gBAAA,CAAyB;UAOlDtI,uDAAA,GAAe;UAAfA,wDAAA,gBAAe,oBAAAA,6DAAA,KAAA4jB,GAAA,aAAAzgB,GAAA,CAAA0b,SAAA,eAAA1b,GAAA,CAAA0a,WAAA,CAAAC,SAAA;UAaX9d,uDAAA,GAAuB;UAAvBA,wDAAA,UAAAmD,GAAA,CAAAsa,aAAA,CAAuB,cAAAta,GAAA,CAAAsd,iBAAA,0BAAAtd,GAAA,CAAA8Z,kBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5IS;AACQ;AACgB;AACE;AACU;AAC5B;AACE;AACE;AACgC;AACJ;AACtC;AACU;AACA;AACF;AAC8B;AAC1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;IEdtDjd,4DAAA,aAA4D;IAAAA,oDAAA,QAAC;IAAAA,0DAAA,EAAM;;;;;IACnEA,4DAAA,aAA4D;IAC1DA,uDAAA,wBAAyF;IAC3FA,0DAAA,EAAM;;;;;IADYA,uDAAA,GAAe;IAAfA,wDAAA,UAAA6jB,QAAA,CAAe,sBAAA3Z,MAAA,CAAAtI,iBAAA;;;;;IAHnC5B,4DAAA,aAAqD;IACnDA,wDAAA,IAAA8jB,+CAAA,iBAAmE;IACnE9jB,wDAAA,IAAA+jB,+CAAA,iBAEM;IACR/jB,0DAAA,EAAM;;;;IAJmBA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAgkB,OAAA,CAAAnZ,UAAA,CAAkB;IAClB7K,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAgkB,OAAA,CAAAla,UAAA,CAAkB;;;ADWvC,MAAO9O,0BAA0B;EAKrC2F,YAAoBC,cAA8B,EAAUqjB,KAAqB;IAA7D,KAAArjB,cAAc,GAAdA,cAAc;IAA0B,KAAAqjB,KAAK,GAALA,KAAK;IAJjE,KAAAC,WAAW,GAAkB,EAAE;IAC/B,KAAA3Z,MAAM,GAAY,KAAK;IACvB,KAAA3I,iBAAiB,GAAY,KAAK;EAEkD;EAEpFX,QAAQA,CAAA;IACN,IAAI,CAACW,iBAAiB,GAAG,IAAI,CAACqiB,KAAK,CAACE,QAAQ,CAACC,MAAM,CAAC,mBAAmB,CAAC,KAAK,MAAM;IACnF,IAAI,CAACF,WAAW,GAAG,IAAI,CAACtjB,cAAc,CAACyjB,SAAS,EAAE;EACpD;;;uBAVWrpB,0BAA0B,EAAAgF,+DAAA,CAAA/C,kEAAA,GAAA+C,+DAAA,CAAAgG,2DAAA;IAAA;EAAA;;;YAA1BhL,0BAA0B;MAAA2H,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuhB,oCAAArhB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdvClD,4DAAA,aAA2D;UACzDA,wDAAA,IAAAwkB,yCAAA,iBAKM;UACRxkB,0DAAA,EAAM;;;UANiCA,uDAAA,GAAc;UAAdA,wDAAA,YAAAmD,GAAA,CAAA+gB,WAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;ACGrD;AAC8E;;;;;;;;ICK9ElkB,qEAAA,GAA0D;IACxDA,4DAAA,aAA6B;IAGnBA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;IAIrBA,uDAAA,qBAAqD;IACvDA,mEAAA,EAAe;;;;IADAA,uDAAA,GAAyB;IAAzBA,wDAAA,eAAAK,MAAA,CAAAyJ,UAAA,CAAyB;;;;;IAGxC9J,qEAAA,GAA4D;IAC1DA,4DAAA,aAA6B;IAGnBA,oDAAA,gBAAS;IAAAA,0DAAA,EAAK;IAI1BA,mEAAA,EAAe;;;ADdT,MAAOjF,mBAAoB,SAAQ6O,wDAAa;EAGpDjJ,YACUoK,MAAc,EACd+R,kBAAsC,EACtChZ,cAA8B;IAEtC,KAAK,EAAE;IAJC,KAAAiH,MAAM,GAANA,MAAM;IACN,KAAA+R,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAhZ,cAAc,GAAdA,cAAc;IALxB,KAAAgG,UAAU,GAAc,EAAE;EAQ1B;EAEA7I,QAAQA,CAAA;IACN;IACA,IAAI,CAAC6C,cAAc,CAACqa,KAAK,EAAE;IAE3B,MAAM2C,IAAI,GAAG,IAAI,CAAChE,kBAAkB,CAAC2H,eAAe,EAAE;IAEtD,IAAI,CAAC3H,kBAAkB,CAAC4H,yBAAyB,CAAC5D,IAAI,CAAC,CAAChf,SAAS,CAAC;MAChEuc,IAAI,EAAGC,QAAuB,IAAI;QAChC,IAAI,CAACxU,UAAU,GAAGwU,QAAQ,CAACqG,MAAM;QAEjC,IAAI,CAAC,IAAI,CAAC7a,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC/B,MAAM,IAAI,CAAC,EAAE;UACnD,IAAI,CAACgD,MAAM,CAACQ,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAGrC,IAAI,CAACzH,cAAc,CAAC2a,IAAI,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;QAClC,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;MAC5B;KACD,CAAC;EACJ;;;uBAhCW1jB,mBAAmB,EAAAiF,+DAAA,CAAA/C,mDAAA,GAAA+C,+DAAA,CAAAgG,+DAAA,GAAAhG,+DAAA,CAAAgG,2DAAA;IAAA;EAAA;;;YAAnBjL,mBAAmB;MAAA4H,SAAA;MAAA2D,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4hB,6BAAA1hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhClD,4DAAA,aAAiB;UACfA,uDAAA,aAMM;UACRA,0DAAA,EAAM;UAENA,wDAAA,IAAA6kB,2CAAA,0BASe;UAEf7kB,wDAAA,IAAA8kB,2CAAA,0BAQe;;;UAnBA9kB,uDAAA,GAAyC;UAAzCA,wDAAA,SAAAmD,GAAA,CAAA2G,UAAA,IAAA3G,GAAA,CAAA2G,UAAA,CAAA/B,MAAA,KAAyC;UAWzC/H,uDAAA,GAA2C;UAA3CA,wDAAA,UAAAmD,GAAA,CAAA2G,UAAA,IAAA3G,GAAA,CAAA2G,UAAA,CAAA/B,MAAA,MAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;IEnBxD/H,4DAAA,UAA+B;IACTA,oDAAA,GAAmC;IAAAA,0DAAA,EAAO;;;;IAA1CA,uDAAA,GAAmC;IAAnCA,+DAAA,CAAAK,MAAA,CAAA0kB,aAAA,UAAA1kB,MAAA,CAAA2kB,KAAA,EAAmC;;;;;IAQzDhlB,4DAAA,UAAkC;IACRA,oDAAA,GAAkD;IAAAA,0DAAA,EAAO;;;;IAAzDA,uDAAA,GAAkD;IAAlDA,gEAAA,gBAAAqI,MAAA,CAAA0c,aAAA,cAAA1c,MAAA,CAAA2c,KAAA,MAAkD;;;;;IAK1EhlB,4DAAA,YAAmD;IACjDA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAilB,OAAA,MACF;;;;;IAHFjlB,4DAAA,SAAwB;IACtBA,wDAAA,IAAAklB,4CAAA,gBAEK;IACPllB,0DAAA,EAAK;;;;IAHgCA,uDAAA,GAAc;IAAdA,wDAAA,YAAA0D,MAAA,CAAAshB,KAAA,CAAA9E,KAAA,CAAc;;;ADT/C,MAAO3gB,yBAAyB;EAIpCoB,YAAA,GAAe;EAEfM,QAAQA,CAAA,GAAU;EAElB8jB,aAAaA,CAACI,UAAkB,EAAEH,KAAc;IAC9C,IAAII,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACtW,IAAI,CAAC8J,CAAC,IAAIA,CAAC,CAAC/B,IAAI,IAAIqO,UAAU,CAAC;IAEzD,IAAIC,GAAG,EAAE;MACP,OAAOA,GAAG,CAACE,KAAK;KACjB,MAAM;MACL,OAAO,EAAE;;EAEb;EAEAC,aAAaA,CAACP,KAAc;IAC1B,IAAII,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACtW,IAAI,CAAC8J,CAAC,IAAIA,CAAC,CAAC/B,IAAI,IAAI,WAAW,CAAC;IAE1D,IAAIsO,GAAG,EAAE;MACP,OAAO,CAACnjB,OAAO,CAACmjB,GAAG,CAACE,KAAK,IAAI,IAAI,IAAIF,GAAG,CAACE,KAAK,IAAI,EAAE,CAAC;KACtD,MAAM;MACL,OAAO,KAAK;;EAEhB;;;uBA1BW/lB,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAoD,SAAA;MAAAqK,MAAA;QAAAgY,KAAA;QAAApjB,iBAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwiB,mCAAAtiB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRtClD,qEAAA,GAAc;UAEZA,wDAAA,IAAAylB,wCAAA,iBAEM;UAENzlB,4DAAA,UAAK;UACoBA,oDAAA,GAAsC;UAAAA,0DAAA,EAAO;UAItEA,wDAAA,IAAA0lB,wCAAA,iBAEM;UAGN1lB,wDAAA,IAAA2lB,uCAAA,gBAIK;UAGL3lB,4DAAA,cAAiC;UAAAA,oDAAA,GAAuB;UAAAA,0DAAA,EAAO;UAC/DA,4DAAA,cAAgC;UAAAA,oDAAA,IAAwC;UAAAA,0DAAA,EAAO;UAC/EA,4DAAA,eAAiC;UAAAA,oDAAA,IAAyC;UAAAA,0DAAA,EAAO;UACnFA,mEAAA,EAAe;;;UAxBPA,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAmD,GAAA,CAAAvB,iBAAA,CAAuB;UAKJ5B,uDAAA,GAAsC;UAAtCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,aAAA5hB,GAAA,CAAA6hB,KAAA,EAAsC;UAIzDhlB,uDAAA,GAA0B;UAA1BA,wDAAA,SAAAmD,GAAA,CAAAoiB,aAAA,CAAApiB,GAAA,CAAA6hB,KAAA,EAA0B;UAK3BhlB,uDAAA,GAAiB;UAAjBA,wDAAA,SAAAmD,GAAA,CAAA6hB,KAAA,CAAA9E,KAAA,CAAiB;UAOWlgB,uDAAA,GAAuB;UAAvBA,+DAAA,CAAAmD,GAAA,CAAA6hB,KAAA,CAAAY,WAAA,CAAuB;UACxB5lB,uDAAA,GAAwC;UAAxCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,eAAA5hB,GAAA,CAAA6hB,KAAA,EAAwC;UACvChlB,uDAAA,GAAyC;UAAzCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,gBAAA5hB,GAAA,CAAA6hB,KAAA,EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;IEvB1EhlB,4DAAA,UAA+B;IACTA,oDAAA,GAAmC;IAAAA,0DAAA,EAAO;;;;IAA1CA,uDAAA,GAAmC;IAAnCA,+DAAA,CAAAK,MAAA,CAAA0kB,aAAA,UAAA1kB,MAAA,CAAA2kB,KAAA,EAAmC;;;;;IAQzDhlB,4DAAA,UAAkC;IACRA,oDAAA,GAAkD;IAAAA,0DAAA,EAAO;;;;IAAzDA,uDAAA,GAAkD;IAAlDA,gEAAA,gBAAAqI,MAAA,CAAA0c,aAAA,cAAA1c,MAAA,CAAA2c,KAAA,MAAkD;;;;;IAK1EhlB,4DAAA,YAAmD;IACjDA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAilB,OAAA,MACF;;;;;IAHFjlB,4DAAA,SAAwB;IACtBA,wDAAA,IAAA6lB,yCAAA,gBAEK;IACP7lB,0DAAA,EAAK;;;;IAHgCA,uDAAA,GAAc;IAAdA,wDAAA,YAAA0D,MAAA,CAAAshB,KAAA,CAAA9E,KAAA,CAAc;;;ADT/C,MAAO5gB,sBAAsB;EAIjCqB,YAAA,GAAe;EAEfM,QAAQA,CAAA,GAAU;EAElB8jB,aAAaA,CAACI,UAAkB,EAAEH,KAAc;IAC9C,IAAII,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACtW,IAAI,CAAC8J,CAAC,IAAIA,CAAC,CAAC/B,IAAI,IAAIqO,UAAU,CAAC;IAEzD,IAAIC,GAAG,EAAE;MACP,OAAOA,GAAG,CAACE,KAAK;KACjB,MAAM;MACL,OAAO,EAAE;;EAEb;EAEAC,aAAaA,CAACP,KAAc;IAC1B,IAAII,GAAG,GAAGJ,KAAK,CAACK,SAAS,CAACtW,IAAI,CAAC8J,CAAC,IAAIA,CAAC,CAAC/B,IAAI,IAAI,WAAW,CAAC;IAE1D,IAAIsO,GAAG,EAAE;MACP,OAAO,CAACnjB,OAAO,CAACmjB,GAAG,CAACE,KAAK,IAAI,IAAI,IAAIF,GAAG,CAACE,KAAK,IAAI,EAAE,CAAC;KACtD,MAAM;MACL,OAAO,KAAK;;EAEhB;;;uBA1BWhmB,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAqD,SAAA;MAAAqK,MAAA;QAAAgY,KAAA;QAAApjB,iBAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8iB,gCAAA5iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRnClD,qEAAA,GAAc;UAEZA,wDAAA,IAAA+lB,qCAAA,iBAEM;UAEN/lB,4DAAA,UAAK;UACoBA,oDAAA,GAAsC;UAAAA,0DAAA,EAAO;UAItEA,wDAAA,IAAAgmB,qCAAA,iBAEM;UAGNhmB,wDAAA,IAAAimB,oCAAA,gBAIK;UAGLjmB,4DAAA,cAAiC;UAAAA,oDAAA,GAAuB;UAAAA,0DAAA,EAAO;UAC/DA,4DAAA,cAAgC;UAAAA,oDAAA,IAAwC;UAAAA,0DAAA,EAAO;UAC/EA,4DAAA,eAAiC;UAAAA,oDAAA,IAAyC;UAAAA,0DAAA,EAAO;UACnFA,mEAAA,EAAe;;;UAxBPA,uDAAA,GAAuB;UAAvBA,wDAAA,SAAAmD,GAAA,CAAAvB,iBAAA,CAAuB;UAKJ5B,uDAAA,GAAsC;UAAtCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,aAAA5hB,GAAA,CAAA6hB,KAAA,EAAsC;UAIzDhlB,uDAAA,GAA0B;UAA1BA,wDAAA,SAAAmD,GAAA,CAAAoiB,aAAA,CAAApiB,GAAA,CAAA6hB,KAAA,EAA0B;UAK3BhlB,uDAAA,GAAiB;UAAjBA,wDAAA,SAAAmD,GAAA,CAAA6hB,KAAA,CAAA9E,KAAA,CAAiB;UAOWlgB,uDAAA,GAAuB;UAAvBA,+DAAA,CAAAmD,GAAA,CAAA6hB,KAAA,CAAAY,WAAA,CAAuB;UACxB5lB,uDAAA,GAAwC;UAAxCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,eAAA5hB,GAAA,CAAA6hB,KAAA,EAAwC;UACvChlB,uDAAA,GAAyC;UAAzCA,+DAAA,CAAAmD,GAAA,CAAA4hB,aAAA,gBAAA5hB,GAAA,CAAA6hB,KAAA,EAAyC;;;;;;;;;;;;;;;;;;;;;;;;;AClBtE,MAAO/pB,oBAAoB;EAC/B0F,YAAA,GAAe;EAEfM,QAAQA,CAAA,GAAI;;;uBAHDhG,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAA0H,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkjB,8BAAAhjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPjClD,4DAAA,aAAmC;UAE/BA,uDAAA,aAA4C;UAC9CA,0DAAA,EAAe;UAEfA,4DAAA,sBAA8E;UAC5EA,uDAAA,aAAsC;UACxCA,0DAAA,EAAe;;;;;;;;;;;;;;;;;;;;;;;ACP8D;;;AAUzE,MAAOd,qBAAqB;EAMhCyB,YAAA;IAJU,KAAAwlB,aAAa,GAAyB,IAAIzmB,uDAAY,EAAE;IACxD,KAAA0mB,eAAe,GAA0B,IAAI1mB,uDAAY,EAAE;IAC3D,KAAA2mB,QAAQ,GAAG,IAAI3mB,uDAAY,EAAE;EAExB;EAEfuB,QAAQA,CAAA,GAAU;EAElB4F,cAAcA,CAACuI,KAAa;IAC1B,IAAI,CAAC+W,aAAa,CAAC/kB,IAAI,CAACgO,KAAK,CAAC;EAChC;EAEAkX,gBAAgBA,CAAClX,KAAc;IAC7B,IAAI,CAACgX,eAAe,CAAChlB,IAAI,CAACgO,KAAK,CAAC;EAClC;EAEAmX,cAAcA,CAAA;IACZ,IAAI,CAACF,QAAQ,CAACjlB,IAAI,EAAE;EACtB;;;uBApBWlC,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAyD,SAAA;MAAAqK,MAAA;QAAA9H,KAAA;MAAA;MAAAtC,OAAA;QAAAujB,aAAA;QAAAC,eAAA;QAAAC,QAAA;MAAA;MAAAxjB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwjB,+BAAAtjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVlClD,4DAAA,aAA0B;UACxBA,uDAAA,aAAuD;UACvDA,4DAAA,YAAsB;UAAAA,oDAAA,GAAY;UAAAA,0DAAA,EAAK;UAGzCA,4DAAA,gCAGC;UAFCA,wDAAA,2BAAAymB,+EAAA7f,MAAA;YAAA,OAAiBzD,GAAA,CAAA0D,cAAA,CAAAD,MAAA,CAAsB;UAAA,EAAC,6BAAA8f,iFAAA9f,MAAA;YAAA,OACrBzD,GAAA,CAAAmjB,gBAAA,CAAA1f,MAAA,CAAwB;UAAA,EADH;UAEzC5G,0DAAA,EAAyB;UAE1BA,4DAAA,aAAqC;UACNA,oDAAA,GAAgB;UAAAA,0DAAA,EAAK;UAClDA,4DAAA,WAA6D;UAA3BA,wDAAA,mBAAA2mB,kDAAA;YAAA,OAASxjB,GAAA,CAAAojB,cAAA,EAAgB;UAAA,EAAC;UAC1DvmB,4DAAA,WAAsC;UAAAA,oDAAA,IAAe;UAAAA,0DAAA,EAAI;UACzDA,uDAAA,cAA8C;UAChDA,0DAAA,EAAI;;;UAbkBA,uDAAA,GAAY;UAAZA,gEAAA,KAAAmD,GAAA,CAAA+B,KAAA,MAAY;UASLlF,uDAAA,GAAgB;UAAhBA,gEAAA,KAAAmD,GAAA,CAAA+B,KAAA,UAAgB;UAELlF,uDAAA,GAAe;UAAfA,gEAAA,SAAAmD,GAAA,CAAA+B,KAAA,KAAe;;;;;;;;;;;;;;;;;;;;;;;;;ACbsB;AAE/E;AAC4E;;;;;;ICDxElF,4DAAA,aAAoD;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;;;;;IAC9DA,4DAAA,aAA4D;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAK;;;;IAAxBA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAA4mB,WAAA,CAAAzhB,KAAA,CAAmB;;;;;IAI/EnF,4DAAA,aAA0D;IAAAA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAK;;;;;;;;;;IAC/EA,4DAAA,aAKC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IAHHA,wDAAA,YAAAA,6DAAA,IAAA4H,GAAA,GAAAif,WAAA,CAAAvhB,WAAA,CAAAyC,MAAA,EAA0D;IAE1D/H,uDAAA,GACF;IADEA,gEAAA,MAAA6mB,WAAA,CAAAvhB,WAAA,0BACF;;;;;IAIAtF,4DAAA,aAAoD;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAE7DA,uDAAA,cAA8D;;;;;IAC9DA,uDAAA,cAA4F;;;;;IAF9FA,4DAAA,aAAuC;IACrCA,wDAAA,IAAA8mB,gDAAA,kBAA8D;IAC9D9mB,wDAAA,IAAA+mB,gDAAA,kBAA4F;IAC9F/mB,0DAAA,EAAK;;;;IAFGA,uDAAA,GAAuB;IAAvBA,wDAAA,UAAAgnB,WAAA,CAAAjiB,QAAA,CAAuB;IACvB/E,uDAAA,GAAsB;IAAtBA,wDAAA,SAAAgnB,WAAA,CAAAjiB,QAAA,CAAsB;;;;;IAK9B/E,4DAAA,aAAoD;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAK;;;;;IAE7DA,4DAAA,WAAmE;IAAAA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAO;;;;;IAChGA,4DAAA,eAA4E;IAAAA,oDAAA,gBAAS;IAAAA,0DAAA,EAAO;;;;;IAC5FA,4DAAA,eAAgF;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAO;;;;;IAHjGA,4DAAA,aAAkE;IAChEA,wDAAA,IAAAinB,kDAAA,mBAAgG;IAChGjnB,wDAAA,IAAAknB,kDAAA,mBAA4F;IAC5FlnB,wDAAA,IAAAmnB,kDAAA,mBAA+F;IACjGnnB,0DAAA,EAAK;;;;;IAHIA,uDAAA,GAA0D;IAA1DA,wDAAA,SAAAonB,WAAA,CAAAnf,MAAA,IAAAoJ,MAAA,CAAAnJ,gBAAA,CAAAmf,iBAAA,CAA0D;IAC1DrnB,uDAAA,GAAgD;IAAhDA,wDAAA,SAAAonB,WAAA,CAAAnf,MAAA,IAAAoJ,MAAA,CAAAnJ,gBAAA,CAAAC,OAAA,CAAgD;IAChDnI,uDAAA,GAAkD;IAAlDA,wDAAA,SAAAonB,WAAA,CAAAnf,MAAA,IAAAoJ,MAAA,CAAAnJ,gBAAA,CAAAof,SAAA,CAAkD;;;;;IAI7DtnB,uDAAA,aAA4D;;;;;;IAC5DA,4DAAA,aAKC;IAFCA,wDAAA,mBAAAunB,gEAAA;MAAA,MAAA7M,WAAA,GAAA1a,2DAAA,CAAAwnB,IAAA;MAAA,MAAAC,OAAA,GAAA/M,WAAA,CAAAG,SAAA;MAAA,MAAA6M,OAAA,GAAA1nB,2DAAA;MAAA,OAASA,yDAAA,CAAA0nB,OAAA,CAAAC,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAE9BznB,0DAAA,EAAK;;;;;IAzCRA,4DAAA,eAAmG;IACjGA,qEAAA,MAAmC;IACjCA,wDAAA,IAAA4nB,0CAAA,gBAA8D;IAC9D5nB,wDAAA,IAAA6nB,0CAAA,gBAAoF;IACtF7nB,mEAAA,EAAe;IAEfA,qEAAA,MAAkC;IAChCA,wDAAA,IAAA8nB,0CAAA,gBAA+E;IAC/E9nB,wDAAA,IAAA+nB,0CAAA,gBAOK;IACP/nB,mEAAA,EAAe;IAEfA,qEAAA,MAAsC;IACpCA,wDAAA,IAAAgoB,0CAAA,gBAA+D;IAC/DhoB,wDAAA,IAAAioB,0CAAA,gBAGK;IACPjoB,mEAAA,EAAe;IAEfA,qEAAA,QAAoC;IAClCA,wDAAA,KAAAkoB,2CAAA,gBAA+D;IAC/DloB,wDAAA,KAAAmoB,2CAAA,iBAIK;IACPnoB,mEAAA,EAAe;IAEfA,wDAAA,KAAAooB,2CAAA,iBAA4D;IAC5DpoB,wDAAA,KAAAqoB,2CAAA,iBAKM;IACRroB,0DAAA,EAAQ;;;;IA1C2BA,wDAAA,eAAAK,MAAA,CAAAioB,SAAA,CAAwB;IAmCrCtoB,uDAAA,IAAiC;IAAjCA,wDAAA,oBAAAK,MAAA,CAAAiI,gBAAA,CAAiC;IAG9BtI,uDAAA,GAAyB;IAAzBA,wDAAA,qBAAAK,MAAA,CAAAiI,gBAAA,CAAyB;;;AD5B5C,MAAOnJ,oBAAoB;EAQ/BwB,YAAA;IALU,KAAA4nB,SAAS,GAAyB,IAAI7oB,uDAAY,EAAE;IAE9D,KAAA4I,gBAAgB,GAAa,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;IACpE,KAAAJ,gBAAgB,GAAGA,kEAAgB;EAEpB;EAEfjH,QAAQA,CAAA,GAAU;EAElB0mB,cAAcA,CAACvY,KAAa;IAC1B,IAAI,CAACmZ,SAAS,CAACnnB,IAAI,CAACgO,KAAK,CAAC;EAC5B;;;uBAdWjQ,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAwD,SAAA;MAAAqK,MAAA;QAAAsb,SAAA;QAAAE,IAAA;MAAA;MAAA5lB,OAAA;QAAA2lB,SAAA;MAAA;MAAA1lB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAylB,8BAAAvlB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjClD,wDAAA,IAAA0oB,qCAAA,oBA0CQ;;;UA1CA1oB,wDAAA,SAAAmD,GAAA,CAAAmlB,SAAA,CAAe;;;;;;;;;;;;;;;;;;;;;;;;ACAwD;;;;;;;ICG3EtoB,4DAAA,sBAKC;IAFCA,wDAAA,qBAAA2oB,+EAAA;MAAA3oB,2DAAA,CAAA0N,GAAA;MAAA,MAAAhK,MAAA,GAAA1D,2DAAA;MAAA,OAAWA,yDAAA,CAAA0D,MAAA,CAAAklB,kBAAA,EAAoB;IAAA,EAAC;IAEjC5oB,0DAAA,EAAe;;;IADdA,wDAAA,kBAAiB;;;;;IAUjBA,4DAAA,WAA8B;IAACA,oDAAA,GAAyB;IAAAA,0DAAA,EAAO;;;;IAAhCA,uDAAA,GAAyB;IAAzBA,gEAAA,SAAAuR,MAAA,CAAAsX,KAAA,CAAAC,SAAA,MAAyB;;;;;IAIxD9oB,4DAAA,SAA4C;IAC1CA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAgpB,OAAA,CAAAC,QAAA,SAAAD,OAAA,CAAAlS,IAAA,OAAAjF,MAAA,CAAAsN,cAAA,CAAA6J,OAAA,OACF;;;;;IAEFhpB,4DAAA,UAAmB;IAAAA,oDAAA,GAAoC;IAAAA,0DAAA,EAAM;;;;IAA1CA,uDAAA,GAAoC;IAApCA,gEAAA,eAAAsK,MAAA,CAAAue,KAAA,CAAAK,cAAA,KAAoC;;;;;IAdzDlpB,4DAAA,UAAuB;IAEXA,oDAAA,GAAsB;IAAAA,0DAAA,EAAS;IAEzCA,4DAAA,aAAsB;IACdA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;IAClCA,wDAAA,IAAAmpB,4CAAA,kBAA+D;IACjEnpB,0DAAA,EAAM;IACNA,4DAAA,aAAQ;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAS;IACxCA,4DAAA,UAAI;IACFA,wDAAA,KAAAopB,2CAAA,gBAEK;IACPppB,0DAAA,EAAK;IACLA,wDAAA,KAAAqpB,4CAAA,iBAA6D;IAC/DrpB,0DAAA,EAAM;;;;IAbMA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAqI,MAAA,CAAAwgB,KAAA,CAAAtN,UAAA,CAAsB;IAGxBvb,uDAAA,GAAqB;IAArBA,+DAAA,CAAAqI,MAAA,CAAAwgB,KAAA,CAAA9M,SAAA,CAAqB;IACpB/b,uDAAA,GAAqB;IAArBA,wDAAA,SAAAqI,MAAA,CAAAwgB,KAAA,CAAAC,SAAA,CAAqB;IAEtB9oB,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAqI,MAAA,CAAAwgB,KAAA,CAAAhN,WAAA,CAAuB;IAER7b,uDAAA,GAAqB;IAArBA,wDAAA,YAAAqI,MAAA,CAAA4U,kBAAA,CAAqB;IAItCjd,uDAAA,GAAW;IAAXA,wDAAA,SAAAqI,MAAA,CAAAwgB,KAAA,CAAW;;;ADjBf,MAAOxpB,sBAAsB;EAMjCsB,YAAA;IAFU,KAAA2oB,UAAU,GAAG,IAAI5pB,uDAAY,EAAE;EAE1B;EAEfuB,QAAQA,CAAA,GAAU;EAElBke,cAAcA,CAACC,IAAe;IAC5B,IAAIC,WAAW,GAAG,GAAG;IAErB;IACA,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACE,OAAO,CAACvX,MAAM,GAAG,CAAC,EAAE;MAC3CqX,IAAI,CAACE,OAAO,CAAC9Q,OAAO,CAAC+Q,GAAG,IAAG;QACzB;QACA,IAAIA,GAAG,CAACC,kBAAkB,IAAID,GAAG,CAACC,kBAAkB,CAACzX,MAAM,GAAG,CAAC,EAAE;UAC/DwX,GAAG,CAACC,kBAAkB,CAAChR,OAAO,CAACiR,WAAW,IAAG;YAC3CJ,WAAW,IAAII,WAAW,GAAG,IAAI;UACnC,CAAC,CAAC;;MAEN,CAAC,CAAC;;IAGJ;IACA,IAAIL,IAAI,CAACM,eAAe,IAAIN,IAAI,CAACM,eAAe,CAAC3X,MAAM,GAAG,CAAC,EAAE;MAC3DqX,IAAI,CAACM,eAAe,CAAClR,OAAO,CAAC+Q,GAAG,IAAG;QACjCF,WAAW,IAAIE,GAAG,CAACI,UAAU,GAAG,IAAI;MACtC,CAAC,CAAC;;IAGJN,WAAW,GAAGA,WAAW,CAACxS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtCwS,WAAW,IAAI,GAAG;IAClB,IAAIA,WAAW,CAACtX,MAAM,IAAI,CAAC,EAAE;MAC3BsX,WAAW,GAAG,EAAE;;IAGlB,OAAOA,WAAW;EACpB;EAEAuJ,kBAAkBA,CAAA;IAChB,IAAI,CAACU,UAAU,CAACloB,IAAI,EAAE;EACxB;;;uBA3CW/B,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAsD,SAAA;MAAAqK,MAAA;QAAA6b,KAAA;QAAA5L,kBAAA;QAAAsM,SAAA;MAAA;MAAA3mB,OAAA;QAAA0mB,UAAA;MAAA;MAAAzmB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwmB,gCAAAtmB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRnClD,4DAAA,aAA8C;UAEtCA,oDAAA,qBAAc;UAAAA,0DAAA,EAAK;UACvBA,wDAAA,IAAAypB,8CAAA,0BAKgB;UAClBzpB,0DAAA,EAAM;UAENA,wDAAA,IAAA0pB,qCAAA,kBAeM;UACR1pB,0DAAA,EAAM;;;UAvBCA,uDAAA,GAAe;UAAfA,wDAAA,SAAAmD,GAAA,CAAAomB,SAAA,CAAe;UAOdvpB,uDAAA,GAAe;UAAfA,wDAAA,SAAAmD,GAAA,CAAAomB,SAAA,CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXgD;AAEL;AAEgC;;;;;;;;;;;ICAhGvpB,4DAAA,gBAKC;IAFCA,wDAAA,mBAAA6pB,4EAAA;MAAA,MAAAnP,WAAA,GAAA1a,2DAAA,CAAAiK,GAAA;MAAA,MAAA6f,SAAA,GAAApP,WAAA,CAAAG,SAAA;MAAA,MAAA3Q,MAAA,GAAAlK,2DAAA;MAAA,OAASA,yDAAA,CAAAkK,MAAA,CAAA6f,UAAA,CAAAD,SAAA,CAAAxnB,KAAA,CAAwB;IAAA,EAAC;IAGlCtC,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IADPA,uDAAA,GACF;IADEA,gEAAA,MAAA8pB,SAAA,CAAAxnB,KAAA,MACF;;;ADAI,MAAOlD,iCAAkC,SAAQwK,wDAAa;EAMlEjJ,YACUmD,cAA8B,EAC9B+Y,eAAgC,EACjC5Y,MAAiB;IAExB,KAAK,EAAE;IAJC,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAA+Y,eAAe,GAAfA,eAAe;IAChB,KAAA5Y,MAAM,GAANA,MAAM;IAPL,KAAA+lB,aAAa,GAAG,IAAItqB,uDAAY,EAAE;IAClC,KAAAoa,iBAAiB,GAAG,IAAIpa,uDAAY,EAAE;IAChD,KAAA+Q,iBAAiB,GAAGA,4DAAiB;EAQrC;EAEAsZ,UAAUA,CAAChL,SAA4B;IACrC,IAAI,IAAI,CAACkL,uBAAuB,EAAE,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB;;IAEF,IAAI,CAACC,sBAAsB,CAACpL,SAAS,CAAC;EACxC;EAEAkL,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACG,cAAc,CAACC,IAAI,CAAExB,KAAY,IAAKA,KAAK,CAAC/S,aAAa,KAAKrF,4DAAiB,CAAC6F,SAAS,CAAC;EACxG;EAEA4T,iBAAiBA,CAAA;IACf,MAAM9nB,IAAI,GAAG,IAAI,CAACkoB,oBAAoB,EAAE;IAExC,IAAI,CAACrmB,MAAM,CAAC8H,IAAI,CAAC4d,4EAAqB,EAAE;MACtCY,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBpoB,IAAI,EAAEA;KACP,CAAC;EACJ;EAEA+nB,sBAAsBA,CAACpL,SAA4B;IACjD,MAAM0L,cAAc,GAClB,IAAI,CAACL,cAAc,CAACriB,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG,IAAI,CAACqiB,cAAc,CAACriB,MAAM,SAAS;IACvF,MAAM3F,IAAI,GAAG,IAAI,CAACsoB,mBAAmB,CAACD,cAAc,EAAE1L,SAAS,CAAC;IAEhE,MAAM4L,SAAS,GAAG,IAAI,CAAC1mB,MAAM,CAAC8H,IAAI,CAAC4d,4EAAqB,EAAE;MACxDY,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBpoB,IAAI,EAAEA;KACP,CAAC;IAEFuoB,SAAS,CAACC,WAAW,EAAE,CAAC9oB,SAAS,CAAC+oB,YAAY,IAAG;MAC/C,IAAI,CAACA,YAAY,EAAE;QACjB,IAAI,CAACC,iBAAiB,CAAC/L,SAAS,CAAC;;IAErC,CAAC,CAAC;EACJ;EAEA+L,iBAAiBA,CAAC/L,SAA4B;IAC5C,IAAI,CAACjb,cAAc,CAACqa,KAAK,EAAE;IAC3B,MAAM4M,gBAAgB,GAAG,IAAI,CAACX,cAAc,CAACY,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC7P,OAAO,CAAC;IAElE,IAAI,CAACyB,eAAe,CAACqO,4BAA4B,CAACH,gBAAgB,EAAEhM,SAAS,CAAC,CAACjd,SAAS,CAAC;MACvFuc,IAAI,EAAEC,QAAQ,IAAG;QACf;QACA6M,UAAU,CAAC,MAAK;UACd,IAAI,CAACnB,aAAa,CAAC5oB,IAAI,EAAE;QAC3B,CAAC,EAAE,IAAI,CAAC;QAER,IAAI,CAAC0Y,iBAAiB,CAAC1Y,IAAI,EAAE;MAC/B,CAAC;MACDsd,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAAC5a,cAAc,CAAC2a,IAAI,EAAE;QAC1B,IAAI,CAACE,sBAAsB,CAACD,KAAK,CAAC;MACpC;KACD,CAAC;EACJ;EAEA4L,oBAAoBA,CAAA;IAClB,MAAMloB,IAAI,GAAG,IAAIwnB,2DAAgB,EAAE;IACnCxnB,IAAI,CAACgpB,UAAU,GAAG,mDAAmD;IACrEhpB,IAAI,CAACipB,SAAS,GAAG,8CAA8C;IAC/DjpB,IAAI,CAACkpB,aAAa,GAAG,IAAI;IACzB,OAAOlpB,IAAI;EACb;EAEAsoB,mBAAmBA,CAACa,UAAkB,EAAExM,SAA4B;IAClE,MAAM3c,IAAI,GAAG,IAAIwnB,2DAAgB,EAAE;IACnCxnB,IAAI,CAACgpB,UAAU,GAAG,oDAAoD;IACtEhpB,IAAI,CAACipB,SAAS,GAAG,IAAI,CAACG,gBAAgB,CAACD,UAAU,EAAExM,SAAS,CAAC;IAC7D3c,IAAI,CAACqpB,YAAY,GAAG,QAAQ;IAC5BrpB,IAAI,CAACkpB,aAAa,GAAG,oBAAoB;IACzC,OAAOlpB,IAAI;EACb;EAEAopB,gBAAgBA,CAACD,UAAkB,EAAExM,SAA4B;IAC/D,OAAOA,SAAS,KAAKtO,4DAAiB,CAAC6F,SAAS,GAC5C,yCAAyC7F,4DAAiB,CAAC6F,SAAS,sCAAsC,GAC1G,GAAGiV,UAAU,wBAAwBxM,SAAS,GAAG;EACvD;;;uBA/FW3f,iCAAiC,EAAAY,+DAAA,CAAA/C,kEAAA,GAAA+C,+DAAA,CAAA/C,mEAAA,GAAA+C,+DAAA,CAAAgG,+DAAA;IAAA;EAAA;;;YAAjC5G,iCAAiC;MAAAuD,SAAA;MAAAqK,MAAA;QAAAod,cAAA;MAAA;MAAAxnB,OAAA;QAAAonB,aAAA;QAAAlQ,iBAAA;MAAA;MAAAxT,QAAA,GAAAtG,wEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0oB,2CAAAxoB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9ClD,4DAAA,gBAA0G;UACxGA,oDAAA,qBAAa;UAAAA,4DAAA,eAAU;UAAAA,oDAAA,sBAAe;UAAAA,0DAAA,EAAW;UAEnDA,4DAAA,wBAA0B;UACxBA,wDAAA,IAAA2rB,mDAAA,oBAOS;;UACX3rB,0DAAA,EAAW;;;;UAZHA,wDAAA,cAAAmD,GAAA,CAAAinB,cAAA,CAAAriB,MAAA,CAAmC,sBAAAD,GAAA;UAKpB9H,uDAAA,GAA+B;UAA/BA,wDAAA,YAAAA,yDAAA,OAAAmD,GAAA,CAAAsN,iBAAA,EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAqB;AAIrC;AACK;;;;AAGrC,MAAOnV,sBAAsB;EACjCqF,YAAoBkS,KAAuC,EAAS9H,MAAc;IAA9D,KAAA8H,KAAK,GAALA,KAAK;IAA2C,KAAA9H,MAAM,GAANA,MAAM;EAAW;EACrF+gB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACjZ,KAAK,CAACtC,MAAM,CAACU,qFAAe,CAAC,CAACqC,IAAI,CAC5CuY,yDAAS,CAAC5a,eAAe,IAAG;MAC1B,IAAI,CAACA,eAAe,CAAC8a,qBAAqB,EAAE;QAC1C,IAAI,CAAChhB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC,OAAOqgB,wCAAE,CAAC,KAAK,CAAC;OACjB,MAAM;QACL,OAAOA,wCAAE,CAAC,IAAI,CAAC;;IAEnB,CAAC,CAAC,CACH;EACH;EAEAjvB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACkW,KAAK,CAACtC,MAAM,CAACU,qFAAe,CAAC,CAACqC,IAAI,CAC5CuY,yDAAS,CAAC5a,eAAe,IAAG;MAC1B,IAAI,CAACA,eAAe,CAAC8a,qBAAqB,EAAE;QAC1C,IAAI,CAAChhB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC,OAAOqgB,wCAAE,CAAC,KAAK,CAAC;OACjB,MAAM;QACL,OAAOA,wCAAE,CAAC,IAAI,CAAC;;IAEnB,CAAC,CAAC,CACH;EACH;;;uBA1BWtwB,sBAAsB,EAAA0E,sDAAA,CAAA/C,8CAAA,GAAA+C,sDAAA,CAAAgG,mDAAA;IAAA;EAAA;;;aAAtB1K,sBAAsB;MAAA2wB,OAAA,EAAtB3wB,sBAAsB,CAAA4wB;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;ACTnC;AACsC;AACK;AAGgC;;;;AAGrE,MAAOzwB,uBAAuB;EAClCkF,YAAoBkS,KAAuC,EAAS9H,MAAc;IAA9D,KAAA8H,KAAK,GAALA,KAAK;IAA2C,KAAA9H,MAAM,GAANA,MAAM;EAAW;EACrF+gB,OAAOA,CAAA;IACL,OAAO,IAAI,CAACjZ,KAAK,CAACtC,MAAM,CAACU,qFAAe,CAAC,CAACqC,IAAI,CAC5CuY,yDAAS,CAAC5a,eAAe,IAAG;MAC1B,IAAI,CAACA,eAAe,CAACkb,sBAAsB,EAAE;QAC3C,IAAI,CAACphB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC,OAAOqgB,wCAAE,CAAC,KAAK,CAAC;OACjB,MAAM;QACL,OAAOA,wCAAE,CAAC,IAAI,CAAC;;IAEnB,CAAC,CAAC,CACH;EACH;EAEAjvB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACkW,KAAK,CAACtC,MAAM,CAACU,qFAAe,CAAC,CAACqC,IAAI,CAC5CuY,yDAAS,CAAC5a,eAAe,IAAG;MAC1B,IAAI,CAACA,eAAe,CAACkb,sBAAsB,EAAE;QAC3C,IAAI,CAACphB,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC,OAAOqgB,wCAAE,CAAC,KAAK,CAAC;OACjB,MAAM;QACL,OAAOA,wCAAE,CAAC,IAAI,CAAC;;IAEnB,CAAC,CAAC,CACH;EACH;;;uBA1BWnwB,uBAAuB,EAAAuE,sDAAA,CAAA/C,8CAAA,GAAA+C,sDAAA,CAAAgG,mDAAA;IAAA;EAAA;;;aAAvBvK,uBAAuB;MAAAwwB,OAAA,EAAvBxwB,uBAAuB,CAAAywB;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXoD;AAEV;AAG9E;AAC4C;AAGqC;AAEpB;AACF;AACJ;AACR;AACQ;;;;;;;;;;;;;ICXnDlsB,4DAAA,gBAAkF;IAC1EA,oDAAA,GAAe;IAAAA,0DAAA,EAAO;;;;IAD2BA,wDAAA,eAAAwsB,OAAA,CAAAC,IAAA,CAAwB;IACzEzsB,uDAAA,GAAe;IAAfA,+DAAA,CAAAwsB,OAAA,CAAA1V,IAAA,CAAe;;;;;IAKvB9W,4DAAA,SAAuC;IACqBA,oDAAA,GAAe;IAAAA,0DAAA,EAAI;IAC7EA,uDAAA,eAAkC;IACpCA,0DAAA,EAAK;;;;IAFAA,uDAAA,GAAwB;IAAxBA,wDAAA,eAAA0sB,OAAA,CAAAD,IAAA,CAAwB;IAA+BzsB,uDAAA,GAAe;IAAfA,+DAAA,CAAA0sB,OAAA,CAAA5V,IAAA,CAAe;;;;;IAZ/E9W,4DAAA,qBAAqG;IAEvFA,oDAAA,gBAAS;IAAAA,0DAAA,EAAW;IAEhCA,4DAAA,wBAA6B;IAC3BA,wDAAA,IAAA2sB,uDAAA,oBAES;IACX3sB,0DAAA,EAAW;IAEXA,4DAAA,YAA8B;IAC5BA,wDAAA,IAAA4sB,mDAAA,gBAGK;IACP5sB,0DAAA,EAAK;IAGLA,uDAAA,cAAkD;IACpDA,0DAAA,EAAc;;;;;IAlBYA,uDAAA,GAA6B;IAA7BA,wDAAA,sBAAAgI,GAAA,CAA6B;IAI1BhI,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAK,MAAA,CAAAwsB,aAAA,CAAgB;IAMpB7sB,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAK,MAAA,CAAAwsB,aAAA,CAAgB;;;ADenC,MAAOhyB,uBAAuB;EAsBlC8F,YACUoK,MAAc,EACd/G,WAAwB,EACxBF,cAA8B,EAC9B+O,KAAuC,EACvCia,iBAAoC,EACnC7I,KAAqB;IALtB,KAAAlZ,MAAM,GAANA,MAAM;IACN,KAAA/G,WAAW,GAAXA,WAAW;IACX,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAA+O,KAAK,GAALA,KAAK;IACL,KAAAia,iBAAiB,GAAjBA,iBAAiB;IAChB,KAAA7I,KAAK,GAALA,KAAK;IA1BhB,KAAAtR,gBAAgB,GAAY,KAAK;IACjC,KAAAoa,gBAAgB,GAAY,KAAK;IACjC,KAAAC,qBAAqB,GAAY,KAAK;IAKtC,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAJ,aAAa,GAAiB,EAAE;IAChC,KAAAK,cAAc,GAAiB,CAC7B;MAAEpW,IAAI,EAAE,QAAQ;MAAE2V,IAAI,EAAE;IAAe,CAAE,EACzC;MAAE3V,IAAI,EAAE,SAAS;MAAE2V,IAAI,EAAE;IAAkB,CAAE,EAC7C;MAAE3V,IAAI,EAAE,UAAU;MAAE2V,IAAI,EAAE;IAAmB,CAAE,EAC/C;MAAE3V,IAAI,EAAE,aAAa;MAAE2V,IAAI,EAAE;IAAiB,CAAE,EAChD;MAAE3V,IAAI,EAAE,aAAa;MAAE2V,IAAI,EAAE;IAAiB,CAAE,EAChD;MAAE3V,IAAI,EAAE,QAAQ;MAAE2V,IAAI,EAAE;IAAiB,CAAE,EAC3C;MAAE3V,IAAI,EAAE,UAAU;MAAE2V,IAAI,EAAE;IAAmB,CAAE,EAC7C;MAAE3V,IAAI,EAAE,KAAK;MAAE2V,IAAI,EAAE;IAAc,CAAE,CACxC;IAUC,IAAI,CAACU,SAAS,GAAG,IAAI,CAACpiB,MAAM,CAACqiB,GAAG;EAClC;EAEAnsB,QAAQA,CAAA;IAEV;IAEE,IAAI,CAACosB,mBAAmB,EAAE;IAGxB,IAAI,CAACvpB,cAAc,CAAC2a,IAAI,EAAE;IAC1B,IAAI,CAACza,WAAW,CAACspB,YAAY,EAAE;IAE/B,IAAI,CAACjQ,YAAY,GAAG,IAAI,CAACxK,KAAK,CAACS,IAAI,CAAC/C,mDAAM,CAAC+b,mFAAoB,CAAC,CAAC,CAACxqB,SAAS,CAAEyrB,KAAmB,IAAI;MAClG,IAAI,CAAC5a,gBAAgB,GAAG4a,KAAK,EAAE7T,QAAQ,IAAI6T,KAAK,CAAC7T,QAAQ,CAAClI,WAAW,IAAId,2DAAgB,CAACmE,OAAO;MACjG,IAAI,CAACkY,gBAAgB,GAAGQ,KAAK,EAAE7T,QAAQ,IAAI6T,KAAK,CAAC7T,QAAQ,CAACqS,qBAAqB;MAC/E,IAAI,CAACiB,qBAAqB,GAAGO,KAAK,EAAE7T,QAAQ,IAAI6T,KAAK,CAAC7T,QAAQ,CAAC8T,0BAA0B;MACzF,IAAI,CAACC,qBAAqB,EAAE;IAC9B,CAAC,CAAC;IAEF,IAAI,CAAC1iB,MAAM,CAAC2iB,MAAM,CAAC5rB,SAAS,CAACsN,KAAK,IAAG;MACnC,IAAIA,KAAK,YAAYgd,4DAAe,EAAE;QACpC,IAAI,CAACe,SAAS,GAAG/d,KAAK,CAACge,GAAG;;IAE9B,CAAC,CAAC;IAEF,IAAI,CAACN,iBAAiB,CAACa,2BAA2B,CAACtB,gDAAK,CAACuB,OAAO,CAAC;EACnE;EAMMP,mBAAmBA,CAAA;IACzB,IAAI,CAACpJ,KAAK,CAAC4J,WAAW,CAAC/rB,SAAS,CAACsiB,MAAM,IAAG;MACxC,MAAM0J,SAAS,GAAG1J,MAAM,CAAC,UAAU,CAAC;MACpC,IAAI,CAAC2J,QAAQ,GAAGD,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU;IAClE,CAAC,CAAC;EACJ;EAKEL,qBAAqBA,CAAA;IACnB,IAAI,CAACZ,aAAa,GAAG,EAAE;IACvB,IAAI,CAACK,cAAc,CAAC1e,OAAO,CAACwf,SAAS,IAAG;MACtC,IAAIA,SAAS,CAAClX,IAAI,KAAK,aAAa,IAAI,CAAC,IAAI,CAACiW,gBAAgB,EAAE;QAC9D;;MAEF,IAAIiB,SAAS,CAAClX,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,CAACkW,qBAAqB,IAAI,IAAI,CAACra,gBAAgB,CAAC,EAAE;QACzF;;MAEF,IAAIqb,SAAS,CAAClX,IAAI,KAAK,SAAS,EAAE;QAChCkX,SAAS,CAACvB,IAAI,GAAG,IAAI,CAAC9Z,gBAAgB,GAAG,+BAA+B,GAAG,kBAAkB;;MAE/F,IAAI,CAACka,aAAa,CAACrgB,IAAI,CAACwhB,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAva,WAAWA,CAAA;IACT,IAAI,CAAC4J,YAAY,EAAE3J,WAAW,EAAE;IAChC,IAAI,CAACoZ,iBAAiB,CAACmB,yBAAyB,EAAE;EACpD;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAClqB,WAAW,CAACmqB,MAAM,EAAE;EAC3B;;;uBAhGWtzB,uBAAuB,EAAAmF,+DAAA,CAAA/C,mDAAA,GAAA+C,+DAAA,CAAAgG,wDAAA,GAAAhG,+DAAA,CAAAgG,2DAAA,GAAAhG,+DAAA,CAAAoG,8CAAA,GAAApG,+DAAA,CAAAouB,wFAAA,GAAApuB,+DAAA,CAAA/C,2DAAA;IAAA;EAAA;;;YAAvBpC,uBAAuB;MAAA8H,SAAA;MAAA2rB,UAAA;MAAAhoB,QAAA,GAAAtG,iEAAA;MAAA6C,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwrB,iCAAAtrB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BpClD,wDAAA,IAAAyuB,8CAAA,0BAmBc;UACdzuB,4DAAA,UAAK;UACHA,uDAAA,oBAA+B;UACjCA,0DAAA,EAAM;;;UAtBgEA,wDAAA,SAAAmD,GAAA,CAAA4qB,QAAA,gBAA6B;;;qBDsBvFxB,uEAAgB,EAAAmC,iEAAA,EAAElxB,qEAAe,EAAAoxB,mEAAA,EAAEtwB,iEAAa,EAAAwwB,2DAAA,EAAAA,+DAAA,EAAAA,kEAAA,EAAE3xB,0DAAY,EAAA+xB,qDAAA,EAAAA,kDAAA,EAAEpxB,kEAAa,EAAAuxB,4DAAA,EAAEz0B,yDAAY,EAAAqC,yDAAA,EAAAA,uDAAA,EAAAA,6DAAA;MAAAyyB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;AEtBnE;AACiD;AAErF,MAAMM,SAAS,CAAC;EACZC,WAAW,GAAG,EAAE;EAChBC,eAAe,GAAG,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,aAAaA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACH,WAAW,GAAG,EAAE;IACrB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MACpB,IAAIA,MAAM,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;QAC5B,IAAI,CAACJ,WAAW,CAACzjB,IAAI,CAAC,CAAC6jB,GAAG,GAAG1hB,IAAI,CAACgB,SAAS,CAACygB,MAAM,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MACpF;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAQ,WAAU,IAAI,CAACP,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,CAACF,OAAO,CAAC,IAAI,EAAE,GAAG,CAAE,WAAU;EAC9E;EACA;AACJ;AACA;AACA;AACA;EACIG,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACR,eAAe;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,iBAAiBA,CAACC,OAAO,EAAE;IACvB,IAAIC,SAAS,GAAG,SAAAA,CAAUC,WAAW,EAAE;MACnC,OAAQ,gDAA+CA,WAAY,IAAG;IAC1E,CAAC;IACD,IAAIF,OAAO,CAACG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC;MACnC,IAAI,CAACf,eAAe,GAAGc,QAAQ,CAAChG,GAAG,CAACjpB,GAAG,IAAI8uB,SAAS,CAAC9uB,GAAG,CAAC,CAAC,CAAC0uB,IAAI,CAAC,EAAE,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAACP,eAAe,GAAGW,SAAS,CAACD,OAAO,CAAC;IAC7C;EACJ;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,KAAK,IAAIniB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmiB,QAAQ,CAACppB,MAAM,EAAEiH,CAAC,EAAE,EAAE;MACtC,MAAMoiB,OAAO,GAAGD,QAAQ,CAACniB,CAAC,CAAC;MAC3BoiB,OAAO,CAAC,cAAc,CAAC,GAAGA,OAAO,CAAC9uB,KAAK;MACvC,IAAI8uB,OAAO,CAAC,SAAS,CAAC,EAClBA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACF,QAAQ,EAAE;IAC3B,KAAK,IAAIniB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmiB,QAAQ,CAACppB,MAAM,EAAEiH,CAAC,EAAE,EAAE;MACtC,MAAMoiB,OAAO,GAAGD,QAAQ,CAACniB,CAAC,CAAC;MAC3B,MAAMsiB,WAAW,GAAGF,OAAO,CAACG,aAAa;MACzC,MAAMC,cAAc,GAAGJ,OAAO,CAACK,OAAO,CAACH,WAAW,CAAC;MACnDE,cAAc,CAACE,eAAe,GAAG,IAAI;IACzC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,sBAAsBA,CAACR,QAAQ,EAAE;IAC7B,KAAK,IAAIniB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmiB,QAAQ,CAACppB,MAAM,EAAEiH,CAAC,EAAE,EAAE;MACtC,MAAMoiB,OAAO,GAAGD,QAAQ,CAACniB,CAAC,CAAC;MAC3BoiB,OAAO,CAAC,cAAc,CAAC,GAAGA,OAAO,CAAC9uB,KAAK;IAC3C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIsvB,iBAAiBA,CAACR,OAAO,EAAE;IACvB,MAAMS,OAAO,GAAGT,OAAO,CAACU,SAAS,CAAC,CAAC;IACnC,OAAQ,aAAYD,OAAQ,6BAA4B;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,mBAAmBA,CAACZ,QAAQ,EAAE;IAC1B,KAAK,IAAIniB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmiB,QAAQ,CAACppB,MAAM,EAAEiH,CAAC,EAAE,EAAE;MACtC,MAAMoiB,OAAO,GAAG,IAAI,CAACQ,iBAAiB,CAACT,QAAQ,CAACniB,CAAC,CAAC,CAAC;MACnDmiB,QAAQ,CAACniB,CAAC,CAAC,CAACgjB,kBAAkB,CAAC,UAAU,EAAEZ,OAAO,CAAC;MACnDD,QAAQ,CAACniB,CAAC,CAAC,CAACijB,MAAM,CAAC,CAAC;IACxB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,cAAc,EAAE;IAC5B,MAAM1mB,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAACwmB,cAAc,CAAC;IAC7D,IAAI,CAAC1mB,aAAa,EACd,OAAO,IAAI;IACf,MAAM2mB,QAAQ,GAAG3mB,aAAa,CAAC4mB,oBAAoB,CAAC,OAAO,CAAC;IAC5D,MAAMC,SAAS,GAAG7mB,aAAa,CAAC4mB,oBAAoB,CAAC,QAAQ,CAAC;IAC9D,MAAME,WAAW,GAAG9mB,aAAa,CAAC4mB,oBAAoB,CAAC,UAAU,CAAC;IAClE,MAAMG,SAAS,GAAG/mB,aAAa,CAAC4mB,oBAAoB,CAAC,QAAQ,CAAC;IAC9D,IAAI,CAACnB,mBAAmB,CAACkB,QAAQ,CAAC;IAClC,IAAI,CAACf,oBAAoB,CAACiB,SAAS,CAAC;IACpC,IAAI,CAACX,sBAAsB,CAACY,WAAW,CAAC;IACxC,IAAI,CAACR,mBAAmB,CAACS,SAAS,CAAC;IACnC,OAAO/mB,aAAa,CAACG,SAAS;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6mB,aAAaA,CAACC,GAAG,EAAE;IACf,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMxB,QAAQ,GAAGzlB,QAAQ,CAAC2mB,oBAAoB,CAACK,GAAG,CAAC;IACnD,KAAK,IAAInmB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG4kB,QAAQ,CAACppB,MAAM,EAAEwE,KAAK,EAAE,EAAE;MAClDomB,IAAI,CAACnmB,IAAI,CAAC2kB,QAAQ,CAAC5kB,KAAK,CAAC,CAACqmB,SAAS,CAAC;IACxC;IACA,OAAOD,IAAI,CAAClC,IAAI,CAAC,MAAM,CAAC;EAC5B;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoC,KAAKA,CAACC,YAAY,EAAE;IAChB,IAAIpD,MAAM,GAAG,EAAE;MAAEqD,KAAK,GAAG,EAAE;MAAEC,MAAM,GAAG,qCAAqC;IAC3E,MAAMC,OAAO,GAAG,IAAI,CAACR,aAAa,CAAC,MAAM,CAAC;IAC1C,IAAIK,YAAY,CAACI,cAAc,EAAE;MAC7BxD,MAAM,GAAG,IAAI,CAAC+C,aAAa,CAAC,OAAO,CAAC;MACpCM,KAAK,GAAG,IAAI,CAACN,aAAa,CAAC,MAAM,CAAC;IACtC;IACA;IACA;IACA,IAAIK,YAAY,CAACK,UAAU,EAAE;MACzBH,MAAM,GAAG,EAAE;IACf;IACA,MAAMvnB,aAAa,GAAG,IAAI,CAACymB,eAAe,CAACY,YAAY,CAACX,cAAc,CAAC;IACvE,IAAI,CAAC1mB,aAAa,EAAE;MAChB;MACA2nB,OAAO,CAAC1U,KAAK,CAAE,yBAAwBoU,YAAY,CAACX,cAAe,aAAY,CAAC;MAChF;IACJ;IACA,MAAMtmB,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAEinB,MAAM,CAAC;IAClD,IAAI,CAACnnB,QAAQ,EAAE;MACX;MACAunB,OAAO,CAAC1U,KAAK,CAAC,8BAA8B,CAAC;MAC7C;IACJ;IACA7S,QAAQ,CAACH,QAAQ,CAACK,IAAI,CAAC,CAAC;IACxBF,QAAQ,CAACH,QAAQ,CAACM,KAAK,CAAE;AACjC;AACA;AACA,uBAAuB8mB,YAAY,CAACO,UAAU,GAAGP,YAAY,CAACO,UAAU,GAAG,EAAG;AAC9E,gBAAgBJ,OAAQ;AACxB,gBAAgB,IAAI,CAACzC,iBAAiB,CAAC,CAAE;AACzC,gBAAgB,IAAI,CAACE,wBAAwB,CAAC,CAAE;AAChD,gBAAgBhB,MAAO;AACvB,gBAAgBqD,KAAM;AACtB;AACA,oBAAoBD,YAAY,CAACQ,SAAS,GAAI,UAASR,YAAY,CAACQ,SAAU,GAAE,GAAG,EAAG;AACtF,gBAAgB7nB,aAAc;AAC9B;AACA;AACA;AACA,oBAAoBqnB,YAAY,CAACS,WAAW,GAAG,EAAE,GAAI;AACrD;AACA,uBAAuBT,YAAY,CAACU,UAAW,IAAI;AACnD;AACA;AACA,oBAAoBV,YAAY,CAACW,WAAW,GAAG,iBAAiB,GAAG,EAAG;AACtE;AACA;AACA;AACA;AACA,kBAAkB,CAAC;IACX5nB,QAAQ,CAACH,QAAQ,CAACgoB,KAAK,CAAC,CAAC;EAC7B;EACA,OAAOxH,IAAI,YAAAyH,kBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5D,SAAS;EAAA;EAC5G,OAAO6D,KAAK,kBAD6E7zB,gEAAE;IAAA+zB,KAAA,EACY/D,SAAS;IAAA/D,OAAA,EAAT+D,SAAS,CAAA9D,IAAA;IAAA8H,UAAA,EAAc;EAAM;AACxI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6Fj0B,+DAAE,CAGJgwB,SAAS,EAAc,CAAC;IACvGxH,IAAI,EAAEmH,qDAAU;IAChBwE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,SAASpE,SAAS,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6C,KAAKA,CAACC,YAAY,EAAE;IAChB;IACA,KAAK,CAACD,KAAK,CAACC,YAAY,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIuB,UAAUA,CAACjE,MAAM,EAAE;IACnB,KAAK,CAACD,aAAa,CAACC,MAAM,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIkE,cAAcA,CAAC1D,OAAO,EAAE;IACxB,KAAK,CAACD,iBAAiB,CAACC,OAAO,CAAC;EACpC;EACA,OAAO1E,IAAI;IAAA,IAAAqI,4BAAA;IAAA,gBAAAC,wBAAAZ,CAAA;MAAA,QAAAW,4BAAA,KAAAA,4BAAA,GAlD8Ev0B,mEAAE,CAkDQo0B,eAAe,IAAAR,CAAA,IAAfQ,eAAe;IAAA;EAAA;EAClH,OAAOP,KAAK,kBAnD6E7zB,gEAAE;IAAA+zB,KAAA,EAmDYK,eAAe;IAAAnI,OAAA,EAAfmI,eAAe,CAAAlI,IAAA;IAAA8H,UAAA,EAAc;EAAM;AAC9I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArD6Fj0B,+DAAE,CAqDJo0B,eAAe,EAAc,CAAC;IAC7G5L,IAAI,EAAEmH,qDAAU;IAChBwE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMU,YAAY,CAAC;EACfvC,cAAc,GAAG,IAAI;EACrBkB,UAAU,GAAG,IAAI;EACjBH,cAAc,GAAG,KAAK;EACtBI,SAAS,GAAG,EAAE;EACdH,UAAU,GAAG,KAAK;EAClBI,WAAW,GAAG,KAAK;EACnBE,WAAW,GAAG,IAAI;EAClBD,UAAU,GAAG,CAAC;EACd7yB,WAAWA,CAAC8wB,OAAO,EAAE;IACjB,IAAIA,OAAO,EAAE;MACTkD,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEnD,OAAO,CAAC;IAChC;EACJ;AACJ;AAEA,MAAMoD,iBAAiB,SAAS7E,SAAS,CAAC;EACtC8C,YAAY,GAAG,IAAI4B,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACI,IAAInB,WAAWA,CAACjxB,KAAK,EAAE;IACnB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAES,WAAW,EAAEjxB;IAAM,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI6vB,cAAcA,CAAC7vB,KAAK,EAAE;IACtB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEX,cAAc,EAAE7vB;IAAM,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+wB,UAAUA,CAAC/wB,KAAK,EAAE;IAClB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEO,UAAU,EAAE/wB;IAAM,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI4wB,cAAcA,CAAC5wB,KAAK,EAAE;IACtB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEI,cAAc,EAAE5wB;IAAM,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIkxB,UAAUA,CAAClxB,KAAK,EAAE;IAClB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEU,UAAU,EAAElxB;IAAM,CAAC;EACnE;EACA;AACJ;AACA;AACA;EACI,IAAImxB,WAAWA,CAACnxB,KAAK,EAAE;IACnB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEW,WAAW,EAAEnxB;IAAM,CAAC;EACpE;EACA;AACJ;AACA;AACA;EACI,IAAIgxB,SAASA,CAAChxB,KAAK,EAAE;IACjB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEQ,SAAS,EAAEhxB;IAAM,CAAC;EAClE;EACA;AACJ;AACA;AACA;EACI,IAAI6wB,UAAUA,CAAC7wB,KAAK,EAAE;IAClB,IAAI,CAACwwB,YAAY,GAAG;MAAE,GAAG,IAAI,CAACA,YAAY;MAAEK,UAAU,EAAE7wB;IAAM,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+xB,UAAUA,CAACjE,MAAM,EAAE;IACnB,KAAK,CAACD,aAAa,CAACC,MAAM,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACI,IAAIkE,cAAcA,CAAC1D,OAAO,EAAE;IACxB,KAAK,CAACD,iBAAiB,CAACC,OAAO,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIiC,KAAKA,CAAA,EAAG;IACJ,KAAK,CAACA,KAAK,CAAC,IAAI,CAACC,YAAY,CAAC;EAClC;EACA,OAAO5G,IAAI;IAAA,IAAA4I,8BAAA;IAAA,gBAAAC,0BAAAnB,CAAA;MAAA,QAAAkB,8BAAA,KAAAA,8BAAA,GAlK8E90B,mEAAE,CAkKQ60B,iBAAiB,IAAAjB,CAAA,IAAjBiB,iBAAiB;IAAA;EAAA;EACpH,OAAOG,IAAI,kBAnK8Eh1B,+DAAE;IAAAwoB,IAAA,EAmKJqM,iBAAiB;IAAAlyB,SAAA;IAAAuyB,YAAA,WAAAC,+BAAAjyB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnKflD,wDAAE,mBAAAo1B,2CAAA;UAAA,OAmKJjyB,GAAA,CAAA0vB,KAAA,CAAM,CAAC;QAAA;MAAA;IAAA;IAAA7lB,MAAA;MAAAumB,WAAA;MAAApB,cAAA;MAAAkB,UAAA;MAAAH,cAAA;MAAAM,UAAA;MAAAC,WAAA;MAAAH,SAAA;MAAAH,UAAA;MAAAkB,UAAA;MAAAC,cAAA;IAAA;IAAAhG,UAAA;IAAAhoB,QAAA,GAnKLtG,wEAAE;EAAA;AAoK/F;AACA;EAAA,QAAAi0B,SAAA,oBAAAA,SAAA,KArK6Fj0B,+DAAE,CAqKJ60B,iBAAiB,EAAc,CAAC;IAC/GrM,IAAI,EAAEoH,oDAAS;IACfuE,IAAI,EAAE,CAAC;MACCkB,QAAQ,EAAE,kBAAkB;MAC5B/G,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiF,WAAW,EAAE,CAAC;MAC5B/K,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEsC,cAAc,EAAE,CAAC;MACjB3J,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEwD,UAAU,EAAE,CAAC;MACb7K,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEqD,cAAc,EAAE,CAAC;MACjB1K,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAE2D,UAAU,EAAE,CAAC;MACbhL,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAE4D,WAAW,EAAE,CAAC;MACdjL,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEyD,SAAS,EAAE,CAAC;MACZ9K,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEsD,UAAU,EAAE,CAAC;MACb3K,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEwE,UAAU,EAAE,CAAC;MACb7L,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEyE,cAAc,EAAE,CAAC;MACjB9L,IAAI,EAAEqH,gDAAKA;IACf,CAAC,CAAC;IAAEgD,KAAK,EAAE,CAAC;MACRrK,IAAI,EAAEsH,uDAAY;MAClBqE,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM52B,cAAc,CAAC;EACjB,OAAO2uB,IAAI,YAAAoJ,uBAAA1B,CAAA;IAAA,YAAAA,CAAA,IAAwFr2B,cAAc;EAAA;EACjH,OAAOg4B,IAAI,kBAtM8Ev1B,8DAAE;IAAAwoB,IAAA,EAsMSjrB;EAAc;EAClH,OAAOk4B,IAAI,kBAvM8Ez1B,8DAAE;AAwM/F;AACA;EAAA,QAAAi0B,SAAA,oBAAAA,SAAA,KAzM6Fj0B,+DAAE,CAyMJzC,cAAc,EAAc,CAAC;IAC5GirB,IAAI,EAAEuH,mDAAQ;IACdoE,IAAI,EAAE,CAAC;MACCp3B,OAAO,EAAE,CAAC83B,iBAAiB,CAAC;MAC5B33B,OAAO,EAAE,CAAC23B,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA", "sources": ["./src/app/canteen/canteen-routing.module.ts", "./src/app/canteen/canteen.module.ts", "./src/app/canteen/components/a4-print-form/a4-print-form.component.ts", "./src/app/canteen/components/a4-print-form/a4-print-form.component.html", "./src/app/canteen/components/announcements/announcements.component.ts", "./src/app/canteen/components/announcements/announcements.component.html", "./src/app/canteen/components/articles/articles.component.ts", "./src/app/canteen/components/articles/articles.component.html", "./src/app/canteen/components/block-print/block-print.component.ts", "./src/app/canteen/components/block-print/block-print.component.html", "./src/app/canteen/components/canteen-order-category-filter/canteen-order-category-filter.component.ts", "./src/app/canteen/components/canteen-order-category-filter/canteen-order-category-filter.component.html", "./src/app/canteen/components/canteen-order-filter/canteen-order-filter.component.ts", "./src/app/canteen/components/canteen-order-filter/canteen-order-filter.component.html", "./src/app/canteen/components/canteen-order-school-filter/canteen-order-school-filter.component.ts", "./src/app/canteen/components/canteen-order-school-filter/canteen-order-school-filter.component.html", "./src/app/canteen/components/canteen-order-type-filter/canteen-order-type-filter.component.ts", "./src/app/canteen/components/canteen-order-type-filter/canteen-order-type-filter.component.html", "./src/app/canteen/components/canteen/canteen.component.ts", "./src/app/canteen/components/canteen/canteen.component.html", "./src/app/canteen/components/index.ts", "./src/app/canteen/components/ios-labels-printing/ios-labels-printing.component.ts", "./src/app/canteen/components/ios-labels-printing/ios-labels-printing.component.html", "./src/app/canteen/components/label-print/label-print.component.ts", "./src/app/canteen/components/label-print/label-print.component.html", "./src/app/canteen/components/label-template-ios/label-template-ios.component.ts", "./src/app/canteen/components/label-template-ios/label-template-ios.component.html", "./src/app/canteen/components/label-template/label-template.component.ts", "./src/app/canteen/components/label-template/label-template.component.html", "./src/app/canteen/components/notice-board/notice-board.component.ts", "./src/app/canteen/components/notice-board/notice-board.component.html", "./src/app/canteen/components/notice-header/notice-header.component.ts", "./src/app/canteen/components/notice-header/notice-header.component.html", "./src/app/canteen/components/notice-table/notice-table.component.ts", "./src/app/canteen/components/notice-table/notice-table.component.html", "./src/app/canteen/components/selected-order/selected-order.component.ts", "./src/app/canteen/components/selected-order/selected-order.component.html", "./src/app/canteen/components/uniform-order-status-picker/uniform-order-status-picker.component.ts", "./src/app/canteen/components/uniform-order-status-picker/uniform-order-status-picker.component.html", "./src/app/sharedServices/canteen/menu-editor-guard.service.ts", "./src/app/sharedServices/canteen/sales-report-guard.service.ts", "./src/app/shared/components/merchant-navbar/merchant-navbar.component.ts", "./src/app/shared/components/merchant-navbar/merchant-navbar.component.html", "./node_modules/ngx-print/fesm2022/ngx-print.mjs"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\n// components\r\nimport { MerchantNavbarComponent } from '../shared/components/merchant-navbar/merchant-navbar.component';\r\nimport {\r\n  CanteenComponent,\r\n  LabelPrintComponent,\r\n  IosLabelsPrintingComponent,\r\n  NoticeBoardComponent,\r\n  AnnouncementsComponent,\r\n  ArticlesComponent,\r\n} from './components';\r\n\r\n// Shared tools components\r\nimport { ListAccountComponent, UserDetailsPageComponent } from '../shared-tools/components';\r\nimport { MenuEditorGuardService } from '../sharedServices/canteen/menu-editor-guard.service';\r\n\r\n// Services\r\nimport { ListCanteensResolver, UserDetailsResolver } from '../sharedServices';\r\nimport { SalesReportGuardService } from '../sharedServices/canteen/sales-report-guard.service';\r\nimport { eventManagementRoutes } from '../event-management/event-management.routes';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: MerchantNavbarComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        pathMatch: 'full',\r\n        redirectTo: 'home',\r\n      },\r\n      {\r\n        path: 'home',\r\n        component: CanteenComponent,\r\n        resolve: { canteens: ListCanteensResolver },\r\n      },\r\n      {\r\n        path: 'order',\r\n        loadChildren: () => import('../manage-order/manage-order.module').then(m => m.ManageOrderModule),\r\n      },\r\n      {\r\n        path: 'settings',\r\n        loadChildren: () =>\r\n          import('../canteen-settings/canteen-settings.module').then(m => m.CanteenSettingsModule),\r\n      },\r\n      {\r\n        path: 'reports',\r\n        loadChildren: () => import('../reports/reports.module').then(m => m.ReportsModule),\r\n      },\r\n      {\r\n        path: 'students',\r\n        component: ListAccountComponent,\r\n        resolve: {\r\n          canteens: ListCanteensResolver,\r\n        },\r\n      },\r\n      {\r\n        path: 'students/details/:id',\r\n        component: UserDetailsPageComponent,\r\n        resolve: { user: UserDetailsResolver },\r\n      },\r\n      {\r\n        path: 'labels',\r\n        component: LabelPrintComponent,\r\n      },\r\n      {\r\n        path: 'notice',\r\n        component: NoticeBoardComponent,\r\n      },\r\n      {\r\n        path: 'notice/announcements',\r\n        component: AnnouncementsComponent,\r\n      },\r\n      {\r\n        path: 'notice/articles',\r\n        component: ArticlesComponent,\r\n      },\r\n      {\r\n        path: 'editor',\r\n        loadChildren: () => import('../menu-editor/menu-editor.module').then(m => m.MenuEditorModule),\r\n        canActivateChild: [MenuEditorGuardService],\r\n      },\r\n      {\r\n        path: 'events',\r\n        loadChildren: () => eventManagementRoutes,\r\n        resolve: { merchants: ListCanteensResolver },\r\n      },\r\n      {\r\n        path: 'pos',\r\n        loadChildren: () => import('../pos/pos.module').then(m => m.PosModule),\r\n      },\r\n    ],\r\n  },\r\n  { path: 'iosPrint/:displaySchoolName', component: IosLabelsPrintingComponent },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n  providers: [MenuEditorGuardService, SalesReportGuardService],\r\n})\r\nexport class CanteenRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { NgxPrintModule } from 'ngx-print';\n\n// google material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatMenuModule } from '@angular/material/menu';\n\nimport { CanteenRoutingModule } from './canteen-routing.module';\nimport { AccountModule } from '../account/account.module';\nimport { SharedModule } from '../shared/shared.module';\nimport { SharedToolsModule } from '../shared-tools/shared-tools.module';\nimport { SchoolsFormModule } from '../schools-form/schools-form.module';\nimport { SchoolsButtonModule } from '../schools-button/schools-button.module';\n\n// Components\nimport {\n  CanteenComponent,\n  LabelPrintComponent,\n  BlockPrintComponent,\n  IosLabelsPrintingComponent,\n  NoticeBoardComponent,\n  AnnouncementsComponent,\n  ArticlesComponent,\n  CanteenOrderFilterComponent,\n  CanteenOrderSchoolFilterComponent,\n  CanteenOrderTypeFilterComponent,\n  CanteenOrderCategoryFilterComponent,\n  A4PrintFormComponent,\n  NoticeHeaderComponent,\n  NoticeTableComponent,\n  UniformOrderStatusPickerComponent,\n  SelectedOrderComponent,\n  LabelTemplateComponent,\n  LabelTemplateIosComponent,\n} from './components';\n\n@NgModule({\n  declarations: [\n    CanteenComponent,\n    LabelPrintComponent,\n    BlockPrintComponent,\n    IosLabelsPrintingComponent,\n    NoticeBoardComponent,\n    AnnouncementsComponent,\n    ArticlesComponent,\n    CanteenOrderFilterComponent,\n    CanteenOrderSchoolFilterComponent,\n    CanteenOrderTypeFilterComponent,\n    CanteenOrderCategoryFilterComponent,\n    A4PrintFormComponent,\n    NoticeHeaderComponent,\n    NoticeHeaderComponent,\n    NoticeTableComponent,\n    UniformOrderStatusPickerComponent,\n    SelectedOrderComponent,\n    LabelTemplateComponent,\n    LabelTemplateIosComponent,\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    NgxPrintModule,\n    CanteenRoutingModule,\n    AccountModule,\n    SharedModule,\n    SharedToolsModule,\n    SchoolsButtonModule,\n    // material\n    MatFormFieldModule,\n    MatRadioModule,\n    MatCheckboxModule,\n    MatExpansionModule,\n    MatSelectModule,\n    MatTableModule,\n    MatIconModule,\n    MatInputModule,\n    MatButtonModule,\n    MatSortModule,\n    MatPaginatorModule,\n    MatNativeDateModule,\n    MatDatepickerModule,\n    MatTooltipModule,\n    MatMenuModule,\n    SchoolsFormModule,\n  ],\n  exports: [CanteenOrderTypeFilterComponent, MatTooltipModule, CanteenOrderCategoryFilterComponent],\n  providers: [DatePipe],\n})\nexport class CanteenModule {}\n", "import { Component, OnInit, Output, EventEmitter } from '@angular/core';\nimport { FormGroup, FormControl, Validators } from '@angular/forms';\nimport { A4PrintSettings, A4PrintPositions } from 'src/app/sharedModels';\nimport { CanteenService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'a4-print-form',\n  templateUrl: './a4-print-form.component.html',\n  styleUrls: ['./a4-print-form.component.scss'],\n})\nexport class A4PrintFormComponent implements OnInit {\n  @Output() generate: EventEmitter<A4PrintPositions> = new EventEmitter();\n  @Output() updateCustomValue: EventEmitter<boolean> = new EventEmitter();\n  @Output() sliceLabels = new EventEmitter();\n  @Output() updateDisplaySchool: EventEmitter<boolean> = new EventEmitter();\n  form: FormGroup;\n  printSettings = A4PrintSettings;\n\n  constructor(private canteenService: CanteenService) {}\n\n  ngOnInit(): void {\n    this._createForm();\n  }\n\n  private _createForm() {\n    let settings = 'default';\n    this.updateCustomValue.emit(false);\n\n    let savedSetting = this.canteenService.GetPrintingSetting();\n    let schoolNameSetting = this.canteenService.GetSchoolNamesOnA4Labels() === 'true';\n    this.updateDisplaySchool.emit(schoolNameSetting);\n\n    if (savedSetting && savedSetting != settings) {\n      settings = savedSetting;\n      this.updateCustomValue.emit(false);\n    }\n\n    this.form = new FormGroup({\n      line: new FormControl('1', [Validators.required]),\n      column: new FormControl('1', [Validators.required]),\n      settings: new FormControl(settings),\n      displaySchoolName: new FormControl(schoolNameSetting),\n    });\n\n    this.sliceLabels.emit();\n\n    this.line.valueChanges.subscribe(val => {\n      this.generateLabels();\n    });\n\n    this.column.valueChanges.subscribe(val => {\n      this.generateLabels();\n    });\n\n    this.settings.valueChanges.subscribe(val => {\n      this.updateCustomValue.emit(Boolean(val === 'custom'));\n      this.canteenService.SetPrintingSetting(val);\n    });\n\n    this.displaySchoolName.valueChanges.subscribe(val => {\n      this.updateDisplaySchool.emit(val);\n      this.canteenService.setSchoolNamesOnA4Labels(val);\n    });\n  }\n\n  generateLabels() {\n    const data = new A4PrintPositions();\n    data.Line = this.line.value;\n    data.Column = this.column.value;\n    this.generate.emit(data);\n  }\n\n  get line() {\n    return this.form.get('line');\n  }\n\n  get column() {\n    return this.form.get('column');\n  }\n\n  get settings() {\n    return this.form.get('settings');\n  }\n\n  get displaySchoolName() {\n    return this.form.get('displaySchoolName');\n  }\n}\n", "<div class=\"container-fluid\">\n  <form *ngIf=\"form\" [formGroup]=\"form\">\n    <div class=\"row\">\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <mat-form-field class=\"fullWidthInput\" appearance=\"outline\">\n          <mat-label>Settings</mat-label>\n          <mat-select formControlName=\"settings\">\n            <mat-option [value]=\"printSettings.Default\"> Chrome Default </mat-option>\n            <mat-option [value]=\"printSettings.Custom\"> Chrome No Margins </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Start line</mat-label>\n          <mat-select formControlName=\"line\" required>\n            <mat-option value=\"1\">1</mat-option>\n            <mat-option value=\"2\">2</mat-option>\n            <mat-option value=\"3\">3</mat-option>\n            <mat-option value=\"4\">4</mat-option>\n            <mat-option value=\"5\">5</mat-option>\n            <mat-option value=\"6\">6</mat-option>\n            <mat-option value=\"7\">7</mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n\n      <div class=\"col-12 col-sm-4 col-md-2\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Start column</mat-label>\n          <mat-select formControlName=\"column\" required>\n            <mat-option value=\"1\">1</mat-option>\n            <mat-option value=\"2\">2</mat-option>\n            <mat-option value=\"3\">3</mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      <div class=\"col-12 col-sm-4 col-md-2 d-flex align-items-center\">\n        <mat-checkbox formControlName=\"displaySchoolName\">Display School Name</mat-checkbox>\n      </div>\n    </div>\n  </form>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Location } from '@angular/common';\nimport { FormGroup, FormControl, Validators } from '@angular/forms';\n\n//Models\nimport { NoticeBoardComponent, Notice, NoticeType } from 'src/app/sharedModels';\n\n// Services\nimport { SpinnerService, NoticeService, UserService } from 'src/app/sharedServices';\n\n// Dialog\nimport { MatDialog } from '@angular/material/dialog';\nimport { ConvertToUniversalDateFormat } from 'src/app/utility';\n\n@Component({\n  selector: 'app-announcements',\n  templateUrl: './announcements.component.html',\n  styleUrls: ['./announcements.component.scss'],\n})\nexport class AnnouncementsComponent extends NoticeBoardComponent implements OnInit {\n  date = new Date();\n\n  constructor(\n    private _location: Location,\n    public spinnerService: SpinnerService,\n    protected noticeService: NoticeService,\n    protected userService: UserService,\n    public dialog: MatDialog\n  ) {\n    super(noticeService, spinnerService, userService, dialog);\n  }\n\n  ngOnInit() {\n    this.currentNoticeType = NoticeType.Announcement;\n    this.CreateForm(new Notice());\n  }\n\n  GoBackClick() {\n    this._location.back();\n  }\n\n  openEditForm(announcement: Notice) {\n    this.selectedNotice = announcement;\n    this.CreateForm(announcement);\n    this.showNoticeForm = true;\n  }\n\n  CreateForm(announcement: Notice) {\n    const filteredAnnouncements =\n      (this.noticeData && this.noticeData.filter(announcement => announcement.IsActive == true)) || [];\n    const formDate = announcement.EndDate ? ConvertToUniversalDateFormat(announcement.EndDate) : null;\n\n    this.form = new FormGroup({\n      title: new FormControl(announcement.Title || '', [Validators.required, Validators.minLength(3)]),\n      description: new FormControl(announcement.Description || ''),\n      endDate: new FormControl(formDate),\n      isActive: new FormControl(this.isAnnouncementActive(announcement)),\n    });\n  }\n\n  isAnnouncementActive(announcement: Notice): boolean {\n    const newAnnouncment = !announcement.NoticeId;\n    return announcement?.IsActive || newAnnouncment;\n  }\n\n  AddNotice(): void {\n    this.showNoticeForm = true;\n    this.selectedNotice = null;\n    this.CreateForm(new Notice());\n  }\n\n  IsSubmitButtonActive(): boolean {\n    return !this.form.valid;\n  }\n}\n", "<div class=\"col-12\">\n  <div class=\"col-12 col-md-8\">\n    <nav-back-button (navBack)=\"GoBackClick()\" text=\"Go Back\" class=\"backButton\"></nav-back-button>\n    <div class=\"col-12\">\n      <notice-header\n        title=\"Announcement\"\n        (schoolChanged)=\"OnSchoolSelect($event)\"\n        (merchantChanged)=\"OnMerchantChanged($event)\"\n        (openForm)=\"AddNotice()\"\n      ></notice-header>\n    </div>\n  </div>\n\n  <div class=\"col-12 row infoBlock\">\n    <div class=\"col-12 col-md-8\">\n      <notice-table\n        [tableData]=\"noticeData\"\n        type=\"NoticeType.Announcement\"\n        (selectRow)=\"openEditForm($event)\"\n      ></notice-table>\n    </div>\n\n    <div class=\"col-12 col-md-4\" [ngClass]=\"{ invisible: !showNoticeForm }\">\n      <div class=\"cardWrapper\">\n        <div class=\"crossIconWrapper\">\n          <a (click)=\"hideForm()\" class=\"closeBtn\">\n            <img src=\"assets/icons/cross.svg\" />\n          </a>\n        </div>\n\n        <form [formGroup]=\"form\" class=\"cashlessForm\">\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Title</mat-label>\n            <input\n              maxlength=\"50\"\n              matInput\n              placeholder=\"Enter title of announcement\"\n              formControlName=\"title\"\n              type=\"text\"\n            />\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Description (optional)</mat-label>\n            <textarea\n              maxlength=\"200\"\n              rows=\"1\"\n              matInput\n              placeholder=\"Add a description\"\n              formControlName=\"description\"\n              type=\"text\"\n              class=\"description\"\n              #description\n              rows=\"3\"\n            ></textarea>\n            <mat-hint align=\"end\">{{ description.value.length }} / 200</mat-hint>\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\">\n            <mat-label>End date (optional)</mat-label>\n            <input matInput [matDatepicker]=\"picker1\" formControlName=\"endDate\" [min]=\"date\" readonly />\n            <mat-datepicker-toggle matIconSuffix [for]=\"picker1\"></mat-datepicker-toggle>\n            <mat-datepicker #picker1></mat-datepicker>\n          </mat-form-field>\n\n          <mat-checkbox formControlName=\"isActive\">\n            <p class=\"checkboxLabel\">Active</p>\n          </mat-checkbox>\n          <div class=\"separator\"></div>\n\n          <!-- validation explain -->\n          <div\n            *ngIf=\"selectedNotice && selectedNotice.Status == NoticeStatusEnum.Refused\"\n            class=\"validationExplanation\"\n          >\n            <h4>Validation explanation</h4>\n            <p>{{ selectedNotice.ValidationDescription }}</p>\n          </div>\n\n          <form-buttons\n            (saveEvent)=\"saveNotice()\"\n            (cancelEvent)=\"hideForm()\"\n            (deleteEvent)=\"deleteNoticeCheck()\"\n            [disableSaveButton]=\"IsSubmitButtonActive()\"\n            [showDeleteButton]=\"showDeleteButton()\"\n          ></form-buttons>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Location } from '@angular/common';\nimport { FormGroup, FormControl } from '@angular/forms';\n\n// Models\nimport { NoticeBoardComponent, Notice, NoticeType } from 'src/app/sharedModels';\n\n// Services\nimport { SpinnerService, NoticeService, UserService } from 'src/app/sharedServices';\nimport { MatDialog } from '@angular/material/dialog';\n\n@Component({\n  selector: 'app-articles',\n  templateUrl: './articles.component.html',\n  styleUrls: ['./articles.component.scss'],\n})\nexport class ArticlesComponent extends NoticeBoardComponent implements OnInit {\n  displayedColumns: string[] = ['title', 'body', 'IsActive', 'Status'];\n\n  constructor(\n    private _location: Location,\n    public spinnerService: SpinnerService,\n    protected noticeService: NoticeService,\n    protected userService: UserService,\n    public dialog: MatDialog\n  ) {\n    super(noticeService, spinnerService, userService, dialog);\n  }\n\n  ngOnInit() {\n    this.currentNoticeType = NoticeType.Article;\n    this.CreateForm(new Notice());\n  }\n\n  GoBackClick() {\n    this._location.back();\n  }\n\n  CreateForm(article: Notice) {\n    this.form = new FormGroup({\n      title: new FormControl(article.Title || ''),\n      description: new FormControl(article.Description || ''),\n      endDate: new FormControl(article.EndDate),\n      isActive: new FormControl(article.IsActive === false ? false : true),\n    });\n  }\n\n  AddNotice() {\n    this.showNoticeForm = true;\n    this.selectedNotice = null;\n    this.CreateForm(new Notice());\n  }\n\n  IsSubmitButtonActive() {\n    return !this.form.get('title').value || !this.form.get('description').value;\n  }\n\n  openEditForm(article: Notice) {\n    this.selectedNotice = article;\n    this.CreateForm(article);\n    this.showNoticeForm = true;\n  }\n\n  get bodyFill() {\n    return this.form.get('description').value ? true : false;\n  }\n\n  autoGrowTextZone(e) {\n    if (!this.bodyFill) {\n      e.target.style.height = '300px';\n      return;\n    }\n    e.target.style.height = e.target.scrollHeight + 'px';\n  }\n}\n", "<div class=\"col-12\">\n  <div class=\"col-12 col-md-8\">\n    <nav-back-button (navBack)=\"GoBackClick()\" text=\"Go Back\" class=\"backButton\"></nav-back-button>\n\n    <notice-header\n      title=\"Article\"\n      (schoolChanged)=\"OnSchoolSelect($event)\"\n      (merchantChanged)=\"OnMerchantChanged($event)\"\n      (openForm)=\"AddNotice()\"\n    ></notice-header>\n  </div>\n\n  <div class=\"col-12 row infoBlock\">\n    <div class=\"col-12 col-md-8\">\n      <notice-table\n        [tableData]=\"noticeData\"\n        type=\"NoticeType.Article\"\n        (selectRow)=\"openEditForm($event)\"\n      ></notice-table>\n    </div>\n\n    <div class=\"col-12 col-md-4\" [ngClass]=\"{ invisible: !showNoticeForm }\">\n      <div class=\"cardWrapper\">\n        <div class=\"cardWrapperPadding\">\n          <div class=\"crossIconWrapper\">\n            <a (click)=\"hideForm()\" class=\"closeBtn\">\n              <img src=\"assets/icons/cross.svg\" />\n            </a>\n          </div>\n        </div>\n\n        <form [formGroup]=\"form\" class=\"cashlessForm\">\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Title</mat-label>\n            <input\n              maxlength=\"50\"\n              matInput\n              placeholder=\"Enter title of article\"\n              formControlName=\"title\"\n              type=\"text\"\n            />\n          </mat-form-field>\n\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Body</mat-label>\n            <textarea\n              maxlength=\"1000\"\n              rows=\"1\"\n              matInput\n              placeholder=\"Add article content\"\n              formControlName=\"description\"\n              type=\"text\"\n              class=\"description\"\n              #description\n              rows=\"5\"\n            ></textarea>\n            <mat-hint align=\"end\">{{ description.value.length }} / 1000</mat-hint>\n          </mat-form-field>\n\n          <div class=\"separator bottom\"></div>\n\n          <!-- validation explain -->\n          <div\n            *ngIf=\"selectedNotice && selectedNotice.Status == NoticeStatusEnum.Refused\"\n            class=\"validationExplanation\"\n          >\n            <h4>Validation explanation</h4>\n            <p>{{ selectedNotice.ValidationDescription }}</p>\n          </div>\n\n          <form-buttons\n            (saveEvent)=\"saveNotice()\"\n            (cancelEvent)=\"hideForm()\"\n            (deleteEvent)=\"deleteNoticeCheck()\"\n            [disableSaveButton]=\"IsSubmitButtonActive()\"\n            [showDeleteButton]=\"showDeleteButton()\"\n          ></form-buttons>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit, Input } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { DeviceDetectorService } from 'ngx-device-detector';\n\n// Models\nimport { A4PageLabel, A4PrintPositions, BaseComponent, LabelV2 } from 'src/app/sharedModels';\n\n// Services\nimport { CanteenService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'block-print',\n  templateUrl: './block-print.component.html',\n  styleUrls: ['./block-print.component.scss'],\n})\nexport class BlockPrintComponent extends BaseComponent implements OnInit {\n  @Input() listLabels: LabelV2[];\n  private _fakeLabels: LabelV2[] = [];\n  a4PageLabel: A4PageLabel[] = [];\n  custom: boolean = true;\n  displaySchoolName: boolean = false;\n  SAFARI_BROWSER = 'Safari';\n\n  constructor(\n    private canteenService: CanteenService,\n    private deviceService: DeviceDetectorService,\n    private router: Router\n  ) {\n    super();\n  }\n\n  ngOnInit() {}\n\n  IsDesktop(): boolean {\n    return this.deviceService.isDesktop();\n  }\n\n  ClickTabletPrint() {\n    if (this.deviceService.browser == this.SAFARI_BROWSER) {\n      this.canteenService.SetLabels(this.a4PageLabel);\n      this.router.navigate([`/canteen/iosPrint/${this.displaySchoolName}`]);\n    } else {\n      this.AndroidPrinting();\n    }\n  }\n\n  private AndroidPrinting() {\n    let printContents = document.getElementById('labelsToPrint').innerHTML;\n    let popupWin = window.open('', '_blank', 'height=100%,width=auto');\n    popupWin.document.open();\n\n    popupWin.document.write(`\n      <html>\n        <head>\n          <title>Print tab</title>\n        \n          <style>\n          @page{\n            margin-left: 4mm;\n            margin-right: 1mm;\n            margin-top: 7mm;\n            margin-bottom: 1mm;\n            }\n          .pageA4{\n            width: 295mm;\n            padding-top:8mm;\n            margin-bottom: 6mm;\n        }\n    \n    .labelsContainer{\n        margin: 30px;\n    }\n    \n    \n    .colLabel{\n        padding-top: 2mm;\n        padding-left: 6mm;\n        padding-right: 2mm;\n        padding-bottom: 2mm;\n        width: 87mm;//86mm;\n        height: 51mm;\n        margin-bottom:2mm;\n        margin-right:2mm;\n        display: inline-flex;\n    \n        // border-width: 1px;\n        // border-color: green;\n        // border-style: solid;\n        // border-radius: 8px;\n    \n    \n        font-family: 'bariol_regular';\n        font-size: 16px;\n    \n        &.custom{\n            width: 61mm;\n            height: 35mm;\n            margin-left: 3MM;\n            font-size: 13px;\n    \n            & li{\n                font-size: 15px;\n            }\n        }\n    }\n    \n    .importantText{\n        //font-size: 18px;\n        font-weight: bold;\n    }\n    \n    .className{\n        //font-size: 18px;\n        font-family: 'bariol_regular';\n        margin-top:4mm;\n        margin-left:2mm;\n    }\n    \n    .classNamePage1{\n        //font-size: 18px;\n        font-family: 'bariol_regular';\n        margin-top:2mm;\n        margin-left:2mm;\n    }\n    \n    .subTitle{\n        font-family: 'bariol_regular';\n        margin-top:0px;\n        margin-left:2mm;\n        font-size: 15px;\n    }\n    \n    .classNameBold{\n        //font-size: 18px;\n        font-family: 'bariol_bold';\n        margin-top:2mm;\n    \n        &.totalItems{\n          display: inline-block;\n          margin-top: 2px;\n        }\n    }\n    .orderNo{\n        //font-size: 18px;\n        font-family: 'bariol_regular';\n    }\n    \n    ul{\n        font-family: 'bariol_regular';\n        margin-top: 2px;\n        margin-bottom: 2px;\n        padding-left: 20px;\n    \n        & li{\n            font-size: 19px;\n        }\n    }\n    \n    h4{\n        width: 100%;\n        text-align: right;\n    }\n    mat-form-field{\n        width: 70px;\n    }\n    \n    \n    .fullWidthInput{\n        width: 100%;\n    }\n\n          </style>\n        </head>\n        <body onload=\"window.print()\">${printContents}</body>\n      </html>`);\n  }\n\n  generateLabels(settings: A4PrintPositions) {\n    this._generateFakeLabels(settings.Line, settings.Column);\n  }\n\n  sliceLabels() {\n    this._SliceLabelsA4();\n  }\n\n  updateCustomValue(val: boolean) {\n    this.custom = val;\n  }\n\n  updateDisplaySchoolValue(val: boolean) {\n    this.displaySchoolName = val;\n  }\n\n  private _generateFakeLabels(line, column) {\n    let numberFakeLabels = 0; //(this.line.value * 3) + this.column.value;\n    let numberLine = 0;\n    let numberColumn = 0;\n\n    if (line != 1) {\n      numberLine = line - 1;\n    }\n\n    if (column != 1) {\n      numberColumn = column - 1;\n    }\n\n    numberFakeLabels = numberLine * 3 + numberColumn;\n    this._fakeLabels = [];\n\n    for (let index = 0; index < numberFakeLabels; index++) {\n      this._fakeLabels.push(new LabelV2());\n    }\n\n    this._SliceLabelsA4();\n  }\n\n  private _SliceLabelsA4() {\n    this.a4PageLabel = [];\n    this.a4PageLabel[0] = new A4PageLabel();\n    this.a4PageLabel[0].fakeLabels = [] = this._fakeLabels;\n\n    let sliceStart = 0;\n    let indexPage = 0;\n\n    // sort listLabels into local blocks\n    while (sliceStart < this.listLabels.length) {\n      if (!this.a4PageLabel[indexPage]) {\n        this.a4PageLabel[indexPage] = new A4PageLabel();\n      }\n\n      let labelPage = this.a4PageLabel[indexPage].GetRemainingLabels();\n\n      this.a4PageLabel[indexPage].listLabels = this.listLabels.slice(sliceStart, sliceStart + labelPage);\n\n      sliceStart += labelPage;\n      indexPage++;\n    }\n  }\n}\n", "<div class=\"container-fluid\">\n  <a4-print-form\n    (generate)=\"generateLabels($event)\"\n    (sliceLabels)=\"sliceLabels()\"\n    (updateCustomValue)=\"updateCustomValue($event)\"\n    (updateDisplaySchool)=\"updateDisplaySchoolValue($event)\"\n  ></a4-print-form>\n\n  <div class=\"labelsContainer\">\n    <div class=\"row\">\n      <div class=\"col-6\">\n        <button\n          *ngIf=\"IsDesktop()\"\n          mat-flat-button\n          color=\"primary\"\n          type=\"button\"\n          [disabled]=\"!listLabels\"\n          printSectionId=\"labelsToPrint\"\n          [useExistingCss]=\"true\"\n          ngxPrint\n        >\n          Print\n        </button>\n        <button\n          *ngIf=\"!IsDesktop()\"\n          mat-flat-button\n          color=\"primary\"\n          type=\"button\"\n          [disabled]=\"!listLabels\"\n          (click)=\"ClickTabletPrint()\"\n        >\n          Print\n        </button>\n      </div>\n      <div class=\"col-6\"></div>\n    </div>\n  </div>\n</div>\n\n<div id=\"labelsToPrint\">\n  <div class=\"pageA4\" [ngClass]=\"{ custom: custom }\" *ngFor=\"let page of a4PageLabel\">\n    <!-- fake labels are the empty ones starts from beginning -->\n    <div *ngFor=\"let label of page.fakeLabels\" class=\"colLabel\" [ngClass]=\"{ custom: custom }\">.</div>\n\n    <div *ngFor=\"let label of page.listLabels\" class=\"colLabel\" [ngClass]=\"{ custom: custom }\">\n      <label-template [label]=\"label\" [displaySchoolName]=\"displaySchoolName\"></label-template>\n    </div>\n  </div>\n</div>\n", "import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';\nimport { MatCheckboxChange } from '@angular/material/checkbox';\n\n// models\nimport { CategoryEditor } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'canteen-order-category-filter',\n  templateUrl: './canteen-order-category-filter.component.html',\n  styleUrls: ['../canteen-order-school-filter/canteen-order-school-filter.component.scss'],\n})\nexport class CanteenOrderCategoryFilterComponent implements OnChanges {\n  @Input() categories: CategoryEditor[];\n  @Input() rowExpanded: boolean = false;\n  @Output() categoriesSelected: EventEmitter<number[]> = new EventEmitter();\n  selectedValueCategories: number[] = [];\n\n  constructor() {}\n\n  ngOnChanges(changes: SimpleChanges) {\n    for (const propName in changes) {\n      switch (propName) {\n        case 'categories':\n          this._prepareList();\n          break;\n\n        default:\n          break;\n      }\n    }\n  }\n\n  /**\n   * Prepare the school list to display\n   */\n  private _prepareList() {\n    if (this.categories && this.categories.length > 0) {\n      this.selectedValueCategories = [];\n\n      this.categories.forEach(s => {\n        this.selectedValueCategories.push(s.MenuCategoryId);\n      });\n\n      // if there is a preferred school, use it.\n      let savedValue: number[] = JSON.parse(localStorage.getItem('prefCategoryId'));\n      if (savedValue != null && savedValue.length > 0) {\n        let index = this.selectedValueCategories.find(i => i == savedValue[0]);\n\n        if (index > -1) {\n          this.selectedValueCategories = savedValue;\n        }\n      } else {\n        this._SaveCategories();\n      }\n\n      // trigger selected categories\n      this.categoriesSelected.emit(this.selectedValueCategories);\n    }\n  }\n\n  /**\n   * Check if the given categoryId is selected\n   * @param categoryId\n   * @returns\n   */\n  IsChecked(categoryId: number): boolean {\n    return this.selectedValueCategories.findIndex(s => s == categoryId) > -1;\n  }\n\n  /**\n   * Checkbox value changed\n   * @param event Checkbox event\n   */\n  CheckboxChanged(event: MatCheckboxChange) {\n    const schoolId = +event.source.name;\n\n    if (event.checked) {\n      this.selectedValueCategories.push(schoolId);\n    } else {\n      let index = this.selectedValueCategories.findIndex(i => i == schoolId);\n      if (index > -1) {\n        this.selectedValueCategories.splice(index, 1);\n      }\n    }\n\n    // save categories selection\n    this._SaveCategories();\n\n    // trigger selected categories\n    this.categoriesSelected.emit(this.selectedValueCategories);\n  }\n\n  /**\n   * Save the categories list in localStorage\n   */\n  private _SaveCategories() {\n    localStorage.setItem('prefCategoryId', JSON.stringify(this.selectedValueCategories));\n  }\n\n  /**\n   * Check if all categories are selected\n   * @returns\n   */\n  IsAllSelected(): boolean {\n    return this.selectedValueCategories.length == this.categories.length;\n  }\n\n  /**\n   * Check if the selected categories list is empty\n   * @returns\n   */\n  IsNoCategoriessSelected(): boolean {\n    return this.selectedValueCategories.length == 0;\n  }\n\n  /**\n   * Select all the categories available\n   */\n  SelectAll() {\n    if (!this.IsAllSelected()) {\n      this.selectedValueCategories = [];\n\n      this.categories.forEach(s => {\n        this.selectedValueCategories.push(s.MenuCategoryId);\n      });\n\n      this._SaveCategories();\n\n      // trigger selected categories\n      this.categoriesSelected.emit(this.selectedValueCategories);\n    }\n  }\n\n  /**\n   * Clear selected list\n   */\n  Clear() {\n    if (!this.IsNoCategoriessSelected()) {\n      this.selectedValueCategories = [];\n      this.categoriesSelected.emit(this.selectedValueCategories);\n    }\n  }\n}\n", "<mat-accordion>\n  <mat-expansion-panel [expanded]=\"rowExpanded\">\n    <mat-expansion-panel-header>\n      <mat-panel-title> Categories </mat-panel-title>\n      <mat-panel-description>\n        <span>{{ selectedValueCategories.length }}/{{ categories.length }}</span>\n      </mat-panel-description>\n    </mat-expansion-panel-header>\n    <div class=\"row\">\n      <div class=\"col-6 col-md-3\" *ngFor=\"let cat of categories\">\n        <mat-checkbox\n          [name]=\"cat.MenuCategoryId\"\n          [checked]=\"IsChecked(cat.MenuCategoryId)\"\n          (change)=\"CheckboxChanged($event)\"\n          >{{ cat.CategoryName }}</mat-checkbox\n        >\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col-12 col-md-6\">\n        <div class=\"blockAction\">\n          <a (click)=\"Clear()\" [ngClass]=\"{ active: !IsNoCategoriessSelected() }\">Clear</a>\n          <a (click)=\"SelectAll()\" [ngClass]=\"{ active: !IsAllSelected() }\">Select All</a>\n        </div>\n      </div>\n    </div>\n  </mat-expansion-panel>\n</mat-accordion>\n", "import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output } from '@angular/core';\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as moment from 'moment';\n\n//ngrx\nimport { select, Store } from '@ngrx/store';\nimport { menuCategories } from 'src/app/states/canteen/canteen.selectors';\nimport { Subscription } from 'rxjs';\nimport { CanteenState } from 'src/app/states';\n\n// models\nimport {\n  BaseComponent,\n  Canteen,\n  CanteenFilters,\n  CanteenStatusEnum,\n  MerchantTypeEnum,\n  CategoryEditor,\n  LabelPrintChoiceEnum,\n  MenuTypeEnum,\n  CanteenOrderRequest,\n  SettingChangedEvent,\n} from 'src/app/sharedModels';\n\n// services\nimport { CanteenService, DebounceService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'canteen-order-filter',\n  templateUrl: './canteen-order-filter.component.html',\n  styleUrls: ['./canteen-order-filter.component.scss'],\n})\nexport class CanteenOrderFilterComponent extends BaseComponent implements OnInit, OnDestroy {\n  @Output() filtersChanged: EventEmitter<CanteenOrderRequest> = new EventEmitter();\n  @Output() settingsChanged: EventEmitter<SettingChangedEvent> = new EventEmitter();\n  selectedCanteen: Canteen;\n  isUniformCanteen: boolean = false;\n  isEventCanteen: boolean = false;\n  filterForm: FormGroup;\n  canteenFilters: CanteenFilters;\n  labelPrintChoice: string;\n  listCategories: CategoryEditor[];\n  selectedSchools: number[];\n  selectedCategories: number[];\n  showCategoriesList: boolean = false;\n  canteenSubscription: Subscription;\n\n  constructor(\n    private canteenService: CanteenService,\n    private store: Store<{ canteen: CanteenState }>,\n    private debounceService: DebounceService\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    this.canteenSubscription = this.store\n      .pipe(select(menuCategories))\n      .subscribe((menuCategories: CategoryEditor[]) => {\n        this.listCategories = menuCategories;\n      });\n    // get saved filters\n    this.canteenFilters = this.canteenService.GetFilters();\n\n    // create form\n    this._createForm();\n  }\n\n  ngOnDestroy() {\n    if (this.canteenSubscription) {\n      this.canteenSubscription.unsubscribe();\n    }\n  }\n\n  get date() {\n    return this.filterForm.get('date');\n  }\n\n  get recess() {\n    return this.filterForm.get('recess');\n  }\n\n  get lunch() {\n    return this.filterForm.get('lunch');\n  }\n\n  get event() {\n    return this.filterForm.get('event');\n  }\n\n  get printed() {\n    return this.filterForm.get('printed');\n  }\n\n  get uniNew() {\n    return this.filterForm.get('uniNew');\n  }\n\n  get uniProcessing() {\n    return this.filterForm.get('uniProcessing');\n  }\n\n  get uniReady() {\n    return this.filterForm.get('uniReady');\n  }\n\n  get uniCompleted() {\n    return this.filterForm.get('uniCompleted');\n  }\n\n  get search() {\n    return this.filterForm.get('search');\n  }\n\n  /**\n   * Setup the filters form and listen to events\n   */\n  private _createForm() {\n    this.filterForm = new FormGroup({\n      search: new FormControl(''),\n      recess: new FormControl(this.canteenFilters.Recess),\n      lunch: new FormControl(this.canteenFilters.Lunch),\n      event: new FormControl(this.canteenFilters.Event),\n      printed: new FormControl(this.canteenFilters.Printed),\n      date: new FormControl(this.canteenFilters.Date, [Validators.required]),\n      uniNew: new FormControl(this.canteenFilters.UniNew),\n      uniProcessing: new FormControl(this.canteenFilters.UniProcessing),\n      uniReady: new FormControl(this.canteenFilters.UniReady),\n      uniCompleted: new FormControl(this.canteenFilters.UniCompleted),\n    });\n\n    this.date.valueChanges.subscribe(val => {\n      this.triggerFiltersChanged();\n    });\n\n    this.recess.valueChanges.subscribe(val => {\n      this.canteenFilters.Recess = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.lunch.valueChanges.subscribe(val => {\n      this.canteenFilters.Lunch = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.event.valueChanges.subscribe(val => {\n      this.canteenFilters.Event = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.printed.valueChanges.subscribe(val => {\n      this.canteenFilters.Printed = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.uniNew.valueChanges.subscribe(val => {\n      this.canteenFilters.UniNew = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.uniProcessing.valueChanges.subscribe(val => {\n      this.canteenFilters.UniProcessing = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.uniReady.valueChanges.subscribe(val => {\n      this.canteenFilters.UniReady = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n\n    this.uniCompleted.valueChanges.subscribe(val => {\n      this.canteenFilters.UniCompleted = val;\n      this.canteenService.SetFilters(this.canteenFilters);\n      this.triggerFiltersChanged();\n    });\n  }\n\n  /**\n   * Manage the change of the selected canteen\n   * @param canteen selected canteen\n   */\n  CanteenChanged(canteen: Canteen) {\n    this.selectedCanteen = canteen;\n    this.isUniformCanteen = this.selectedCanteen.CanteenType === MerchantTypeEnum.Uniform;\n    this.isEventCanteen = this.selectedCanteen.CanteenType === MerchantTypeEnum.Event;\n    this.labelPrintChoice = this.selectedCanteen.Schools[0].LabelPrintChoice; // TODO => change the logic to take the First selected school\n    this.showCategoriesList = this.labelPrintChoice == LabelPrintChoiceEnum.Item;\n\n    // export settings to the main screen\n    const settings: SettingChangedEvent = {\n      LabelPrintChoice: this.labelPrintChoice,\n      UsePrintingApp: this.selectedCanteen.Schools[0].UsePrintingApp,\n      IsUniformCanteen: this.isUniformCanteen,\n    };\n    this.settingsChanged.emit(settings);\n  }\n\n  /**\n   * Search value\n   */\n  SearchClicked() {\n    this.triggerFiltersChanged();\n  }\n\n  /**\n   * Handled event when schools selection change\n   * @param schools list of schools\n   */\n  SchoolSelectionChanged(schools: number[]) {\n    this.selectedSchools = schools;\n\n    if (this.selectedCanteen.CanteenType == MerchantTypeEnum.Event) {\n      this.event.setValue(true);\n\n      this.canteenFilters.Event = true;\n      this.canteenService.SetFilters(this.canteenFilters);\n    }\n\n    if (\n      this.labelPrintChoice == LabelPrintChoiceEnum.Order ||\n      (this.labelPrintChoice == LabelPrintChoiceEnum.Item && this.listCategories.length > 0)\n    ) {\n      this.triggerFiltersChanged();\n    }\n  }\n\n  /**\n   * Handled event when categories selection change\n   * @param categories list of categories\n   */\n  CategoriesSelectionChanged(categories: number[]) {\n    this.selectedCategories = categories;\n    this.triggerFiltersChanged();\n  }\n\n  triggerFiltersChanged = this.debounceService.callDebounce(this._prepareRequest, 100, false, true);\n\n  private _prepareRequest() {\n    // prepare request\n    let request: CanteenOrderRequest = new CanteenOrderRequest();\n\n    this.selectedSchools.forEach(n => {\n      if (!request.SchoolIds) {\n        request.SchoolIds = n.toString();\n      } else {\n        request.SchoolIds += ',' + n;\n      }\n    });\n\n    // filter per selected categories (print 1 item per label only)\n    if (this.selectedCategories) {\n      this.selectedCategories.forEach(n => {\n        if (!request.CategoryIds) {\n          request.CategoryIds = n.toString();\n        } else {\n          request.CategoryIds += ',' + n;\n        }\n      });\n    }\n\n    request.Date = moment(this.date.value).format('YYYY-MM-DD');\n\n    request.CanteenStatus = '';\n    request.OrderType = '';\n    request.LabelPrintChoice = this.labelPrintChoice;\n    request.Filter = this.search.value;\n    request.MerchantId = this.selectedCanteen.CanteenId;\n\n    if (this.isUniformCanteen) {\n      request.OrderType += MenuTypeEnum.Uniform + ',';\n\n      if (this.uniNew.value) {\n        request.CanteenStatus += CanteenStatusEnum.New + ',';\n      }\n\n      if (this.uniProcessing.value) {\n        request.CanteenStatus += CanteenStatusEnum.Processing + ',';\n      }\n\n      if (this.uniReady.value) {\n        request.CanteenStatus += CanteenStatusEnum.Ready + ',';\n      }\n\n      if (this.uniCompleted.value) {\n        request.CanteenStatus += CanteenStatusEnum.Completed + ',';\n      }\n    } else {\n      if (this.recess.value && !this.isEventCanteen) {\n        request.OrderType += MenuTypeEnum.Recess + ',';\n      }\n\n      if (this.lunch.value && !this.isEventCanteen) {\n        request.OrderType += MenuTypeEnum.Lunch + ',';\n      }\n\n      if (this.event.value) {\n        request.OrderType += MenuTypeEnum.Event + ',';\n      }\n\n      if (this.printed.value) {\n        if (this.printed.value == 'all') {\n          request.Printed == null;\n        } else if (this.printed.value == 'printed') {\n          request.Printed = true;\n        } else {\n          request.Printed = false;\n        }\n      }\n    }\n\n    // trigger event\n    this.filtersChanged.emit(request);\n  }\n}\n", "<form *ngIf=\"filterForm\" [formGroup]=\"filterForm\">\n  <div class=\"row\">\n    <div class=\"col-12 col-md-3 col-lg-2\">\n      <canteen-select-list (selectedChanged)=\"CanteenChanged($event)\"></canteen-select-list>\n    </div>\n  </div>\n\n  <div class=\"row\">\n    <div *ngIf=\"!isUniformCanteen\" class=\"col-12 col-md-3 col-lg-2\">\n      <input-date placeholder=\"Date\" formControlName=\"date\"></input-date>\n    </div>\n    <div *ngIf=\"!isUniformCanteen\" class=\"col-12 col-md-3 col-lg-2\">\n      <mat-form-field appearance=\"outline\">\n        <mat-label>Print Status</mat-label>\n        <mat-select formControlName=\"printed\">\n          <mat-option value=\"all\">All</mat-option>\n          <mat-option value=\"printed\">Printed Only</mat-option>\n          <mat-option value=\"unprinted\">Unprinted Only</mat-option>\n        </mat-select>\n      </mat-form-field>\n    </div>\n    <div class=\"col-12 col-md-3 col-lg-2\">\n      <mat-form-field appearance=\"outline\" class=\"searchBox\">\n        <input matInput formControlName=\"search\" placeholder=\"Search\" autocomplete=\"off\" />\n        <mat-icon matSuffix (click)=\"SearchClicked()\" aria-hidden=\"false\">search</mat-icon>\n      </mat-form-field>\n    </div>\n  </div>\n\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <h4>Filters</h4>\n    </div>\n  </div>\n\n  <!-- Schools filters -->\n  <div *ngIf=\"selectedCanteen\" class=\"row\">\n    <div class=\"col-12\">\n      <canteen-order-school-filter\n        [schools]=\"selectedCanteen.Schools\"\n        (schoolsSelected)=\"SchoolSelectionChanged($event)\"\n      ></canteen-order-school-filter>\n    </div>\n  </div>\n\n  <!-- Order type Filters-->\n  <div class=\"row mt-3\">\n    <div class=\"col-12\">\n      <canteen-order-type-filter\n        *ngIf=\"selectedCanteen\"\n        [merchantType]=\"selectedCanteen.CanteenType\"\n        [(formGroup)]=\"filterForm\"\n      >\n      </canteen-order-type-filter>\n    </div>\n  </div>\n\n  <!-- Item categories Filters-->\n  <div *ngIf=\"showCategoriesList && listCategories\" class=\"row mt-3\">\n    <div class=\"col-12\">\n      <canteen-order-category-filter\n        [categories]=\"listCategories\"\n        (categoriesSelected)=\"CategoriesSelectionChanged($event)\"\n      ></canteen-order-category-filter>\n    </div>\n  </div>\n</form>\n", "import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';\nimport { MatCheckboxChange } from '@angular/material/checkbox';\n\n// models\nimport { CanteenSchool } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'canteen-order-school-filter',\n  templateUrl: './canteen-order-school-filter.component.html',\n  styleUrls: ['./canteen-order-school-filter.component.scss'],\n})\nexport class CanteenOrderSchoolFilterComponent implements OnChanges {\n  @Input() schools: CanteenSchool[];\n  @Output() schoolsSelected: EventEmitter<number[]> = new EventEmitter();\n  selectedValueSchools: number[] = [];\n  constructor() {}\n\n  ngOnChanges(changes: SimpleChanges) {\n    for (const propName in changes) {\n      switch (propName) {\n        case 'schools':\n          this._prepareList();\n          break;\n\n        default:\n          break;\n      }\n    }\n  }\n\n  /**\n   * Prepare the school list to display\n   */\n  private _prepareList() {\n    if (this.schools && this.schools.length > 0) {\n      //this.usePrintingApp = this.listSchools[0].UsePrintingApp;\n      this.selectedValueSchools = [];\n\n      this.schools.forEach(s => {\n        this.selectedValueSchools.push(s.SchoolId);\n      });\n\n      // if there is a preferred school, use it.\n      let savedValue: number[] = JSON.parse(localStorage.getItem('prefSchoolId'));\n      if (savedValue != null && savedValue.length > 0) {\n        let index = this.selectedValueSchools.find(i => i == savedValue[0]);\n\n        if (index > -1) {\n          this.selectedValueSchools = savedValue;\n        }\n      } else {\n        this._SaveSchools();\n      }\n\n      // trigger selected schools\n      this.schoolsSelected.emit(this.selectedValueSchools);\n    }\n  }\n\n  /**\n   * Check if the given schoolId is selected\n   * @param schoolId\n   * @returns\n   */\n  IsChecked(schoolId: number): boolean {\n    return this.selectedValueSchools.findIndex(s => s == schoolId) > -1;\n  }\n\n  /**\n   * Checkbox value changed\n   * @param event Checkbox event\n   */\n  CheckboxChanged(event: MatCheckboxChange) {\n    const schoolId = +event.source.name;\n\n    if (event.checked) {\n      this.selectedValueSchools.push(schoolId);\n    } else {\n      let index = this.selectedValueSchools.findIndex(i => i == schoolId);\n      if (index > -1) {\n        this.selectedValueSchools.splice(index, 1);\n      }\n    }\n\n    // save school selection\n    this._SaveSchools();\n\n    // trigger selected schools\n    this.schoolsSelected.emit(this.selectedValueSchools);\n  }\n\n  /**\n   * Save the schools list in localStorage\n   * @param schools\n   */\n  private _SaveSchools() {\n    localStorage.setItem('prefSchoolId', JSON.stringify(this.selectedValueSchools));\n  }\n\n  /**\n   * Check if all schools are selected\n   * @returns\n   */\n  IsAllSelected(): boolean {\n    return this.selectedValueSchools.length == this.schools.length;\n  }\n\n  /**\n   * Check if the selected schools list is empty\n   * @returns\n   */\n  IsNoSchoolsSelected(): boolean {\n    return this.selectedValueSchools.length == 0;\n  }\n\n  /**\n   * Select all the schools available\n   */\n  SelectAll() {\n    if (!this.IsAllSelected()) {\n      this.selectedValueSchools = [];\n\n      this.schools.forEach(s => {\n        this.selectedValueSchools.push(s.SchoolId);\n      });\n\n      this._SaveSchools();\n\n      // trigger selected schools\n      this.schoolsSelected.emit(this.selectedValueSchools);\n    }\n  }\n\n  /**\n   * Clear selected list\n   */\n  Clear() {\n    if (!this.IsNoSchoolsSelected()) {\n      this.selectedValueSchools = [];\n    }\n  }\n}\n", "<mat-accordion>\n  <mat-expansion-panel [expanded]=\"false\">\n    <mat-expansion-panel-header>\n      <mat-panel-title> Schools </mat-panel-title>\n      <mat-panel-description>\n        <span>{{ selectedValueSchools.length }}/{{ schools.length }}</span>\n      </mat-panel-description>\n    </mat-expansion-panel-header>\n    <div class=\"row\">\n      <div class=\"col-6 col-md-3\" *ngFor=\"let school of schools\">\n        <mat-checkbox\n          [name]=\"school.SchoolId\"\n          [checked]=\"IsChecked(school.SchoolId)\"\n          (change)=\"CheckboxChanged($event)\"\n          >{{ school.Name }}</mat-checkbox\n        >\n      </div>\n    </div>\n    <div class=\"row\">\n      <div class=\"col-12 col-md-6\">\n        <div class=\"blockAction\">\n          <a (click)=\"Clear()\" [ngClass]=\"{ active: !IsNoSchoolsSelected() }\">Clear</a>\n          <a (click)=\"SelectAll()\" [ngClass]=\"{ active: !IsAllSelected() }\">Select All</a>\n        </div>\n      </div>\n    </div>\n  </mat-expansion-panel>\n</mat-accordion>\n", "import { Component, Input, OnInit, Output } from '@angular/core';\nimport { FormGroup, FormControl } from '@angular/forms';\n\n// models\nimport { CanteenStatusTotals, CanteenStatusEnum, MerchantTypeEnum, MenuTypeEnum } from 'src/app/sharedModels';\n\n// services\nimport { OrderStatusService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'canteen-order-type-filter',\n  templateUrl: './canteen-order-type-filter.component.html',\n  styleUrls: ['./canteen-order-type-filter.component.scss'],\n})\nexport class CanteenOrderTypeFilterComponent implements OnInit {\n  @Input() merchantType: MerchantTypeEnum;\n  @Input() formGroup: FormGroup;\n  @Input() forReports: boolean;\n  @Input() statusTotals: CanteenStatusTotals;\n  filterForm: FormGroup;\n  formTitle: string;\n  isUniformShop: boolean;\n  isEventMerchant: boolean;\n  MenuTypeEnum = MenuTypeEnum;\n\n  constructor(private orderStatusService: OrderStatusService) {}\n\n  ngOnInit() {\n    if (!this.formGroup) {\n      this._createForm();\n    }\n  }\n\n  ngOnChanges() {\n    this.isUniformShop = this.merchantType == MerchantTypeEnum.Uniform;\n    this.isEventMerchant = this.merchantType == MerchantTypeEnum.Event;\n    this.formTitle = this.isUniformShop ? 'Order Status' : 'Order Type';\n  }\n\n  getForm() {\n    if (!this.formGroup) {\n      return this.filterForm;\n    }\n    return this.formGroup;\n  }\n\n  /**\n   * Setup the filters form and listen to events\n   */\n  private _createForm() {\n    this.filterForm = new FormGroup({\n      uniNew: new FormControl(true),\n      uniProcessing: new FormControl(true),\n      uniReady: new FormControl(true),\n      uniCompleted: new FormControl(true),\n    });\n\n    this.filterForm.valueChanges.subscribe(x => {\n      let activeCanteenStatuses = [];\n\n      if (this.uniProcessing.value) {\n        activeCanteenStatuses.push(CanteenStatusEnum.Processing);\n      }\n      if (this.uniReady.value) {\n        activeCanteenStatuses.push(CanteenStatusEnum.Ready);\n      }\n      if (this.uniCompleted.value) {\n        activeCanteenStatuses.push(CanteenStatusEnum.Completed);\n      }\n      if (this.uniNew.value) {\n        activeCanteenStatuses.push(CanteenStatusEnum.New);\n      }\n\n      //save orderStatus values to subscription\n      this.orderStatusService.setOrderStatus(activeCanteenStatuses);\n    });\n  }\n\n  get uniProcessing() {\n    return this.filterForm.get('uniProcessing');\n  }\n\n  get uniReady() {\n    return this.filterForm.get('uniReady');\n  }\n\n  get uniCompleted() {\n    return this.filterForm.get('uniCompleted');\n  }\n\n  get uniNew() {\n    return this.filterForm.get('uniNew');\n  }\n}\n", "<mat-accordion>\n  <mat-expansion-panel [expanded]=\"true\" [ngClass]=\"{ 'mat-elevation-z0': forReports }\">\n    <mat-expansion-panel-header>\n      <mat-panel-title>\n        {{ formTitle }}\n      </mat-panel-title>\n    </mat-expansion-panel-header>\n    <div class=\"row\" *ngIf=\"formGroup || filterForm\" [formGroup]=\"getForm()\">\n      <ng-container *ngIf=\"!isUniformShop; else uniformFilters\">\n        <div *ngIf=\"!isEventMerchant\" class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"recess\">{{ MenuTypeEnum.Recess | merchantMenuName }}</mat-checkbox>\n        </div>\n        <div *ngIf=\"!isEventMerchant\" class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"lunch\">{{ MenuTypeEnum.Lunch | merchantMenuName }}</mat-checkbox>\n        </div>\n        <div class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"event\">Event</mat-checkbox>\n        </div>\n      </ng-container>\n\n      <ng-template #uniformFilters>\n        <div class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"uniNew\"\n            >New <span *ngIf=\"statusTotals && uniNew.value\">({{ this.statusTotals.New }})</span></mat-checkbox\n          >\n        </div>\n        <div class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"uniProcessing\"\n            >Processing\n            <span *ngIf=\"statusTotals && uniProcessing.value\"\n              >({{ this.statusTotals.Processing }})</span\n            ></mat-checkbox\n          >\n        </div>\n        <div class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"uniReady\"\n            >Ready\n            <span *ngIf=\"statusTotals && uniReady.value\">({{ this.statusTotals.Ready }})</span></mat-checkbox\n          >\n        </div>\n        <div class=\"col-6 col-md-2 col-lg-1\">\n          <mat-checkbox formControlName=\"uniCompleted\"\n            >Completed\n            <span *ngIf=\"statusTotals && uniCompleted.value\"\n              >({{ this.statusTotals.Completed }})</span\n            ></mat-checkbox\n          >\n        </div>\n      </ng-template>\n    </div>\n  </mat-expansion-panel>\n</mat-accordion>\n", "import { Component, OnInit, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { FormGroup } from '@angular/forms';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { PageEvent } from '@angular/material/paginator';\nimport { MatSort, Sort } from '@angular/material/sort';\nimport { SelectionModel } from '@angular/cdk/collections';\n\nimport * as moment from 'moment';\n\n// ngrx\nimport { Subscription } from 'rxjs';\n\nimport {\n  CanteenSchool,\n  BasePaginatorComponent,\n  Order,\n  ListOrder,\n  CanteenOrderRequest,\n  CanteenFilters,\n  CanteenStatusEnum,\n  OrderItem,\n  SettingChangedEvent,\n  LabelPrintChoiceEnum,\n} from '../../../sharedModels';\nimport { SpinnerService, OrderApiService, CanteenService, PrintingApiService } from '../../../sharedServices';\n\nconst displayedColumns: string[] = [\n  'OrderId',\n  'SchoolName',\n  'MenuType',\n  'StudentName',\n  'ClassName',\n  'OrderDate',\n];\nconst uniformColumns: string[] = [\n  'select',\n  'OrderId',\n  'CanteenStatus',\n  'StudentName',\n  'ClassName',\n  'OrderDate',\n];\n\n@Component({\n  selector: 'app-canteen',\n  templateUrl: './canteen.component.html',\n  styleUrls: ['./canteen.component.scss'],\n})\nexport class CanteenComponent extends BasePaginatorComponent<Order> implements OnInit {\n  dataSource = new MatTableDataSource<Order>();\n  listOrders: Order[] = [];\n  itemsSelectedOrder: OrderItem[] = [];\n  selectedOrder: Order;\n  oldStatus: string;\n  form: FormGroup;\n  statusForm: FormGroup;\n  isUniformCanteen: boolean = false;\n  CanteenStatusEnum = CanteenStatusEnum;\n  usePrintingApp: boolean;\n  labelPrintChoice: string;\n  private canteenFilters: CanteenFilters;\n  canteenListVisible: boolean = true;\n  listSchools: CanteenSchool[] = [];\n  private subscription: Subscription;\n  private ordersRequest: CanteenOrderRequest;\n\n  @ViewChild(MatSort) sort: MatSort;\n\n  constructor(\n    private router: Router,\n    private orderAPIService: OrderApiService,\n    private spinnerService: SpinnerService,\n    private canteenService: CanteenService,\n    private printingApiService: PrintingApiService\n  ) {\n    super(displayedColumns);\n  }\n\n  ngOnInit(): void {\n    // get saved filters\n    this.canteenFilters = this.canteenService.GetFilters();\n\n    if (!this.canteenFilters) {\n      this.canteenFilters = new CanteenFilters();\n      this.canteenFilters.Date = moment().toDate();\n      this.canteenFilters.Event = false;\n      this.canteenFilters.Recess = true;\n      this.canteenFilters.Lunch = true;\n      this.canteenFilters.Printed = 'all';\n      this.canteenFilters.UniNew = true;\n      this.canteenFilters.UniProcessing = true;\n      this.canteenFilters.UniReady = true;\n      this.canteenFilters.UniCompleted = false;\n\n      this.canteenService.SetFilters(this.canteenFilters);\n    }\n  }\n\n  ngOnDestroy(): void {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n  FiltersChanged(request: CanteenOrderRequest): void {\n    this.ordersRequest = request;\n\n    this.LoadTableData();\n\n    this.clearRowSelection();\n  }\n\n  clearRowSelection(): void {\n    this.selection.clear();\n    this.selectedOrder = null;\n    this.itemsSelectedOrder = null;\n  }\n\n  getOrdersRequest(getAll: boolean): void {\n    this.ordersRequest.NumberRows = this.listfilters.NumberRows;\n    this.ordersRequest.PageIndex = this.listfilters.PageIndex;\n    this.ordersRequest.SortBy = this.listfilters.SortBy;\n    this.ordersRequest.SortDirection = this.listfilters.SortDirection;\n    this.ordersRequest.GetAll = getAll;\n\n    if (\n      !this.ordersRequest.NumberRows ||\n      this.ordersRequest.NumberRows == undefined ||\n      this.ordersRequest.NumberRows == 0\n    ) {\n      this.ordersRequest.NumberRows = 25;\n    }\n  }\n\n  LoadTableData(): void {\n    this.getOrdersRequest(false);\n    this.spinnerService.start();\n    this.orderAPIService.GetOrdersBySchoolAndMerchantAPI(this.ordersRequest).subscribe({\n      next: (response: ListOrder) => {\n        this.RefreshTable(response.Orders);\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.spinnerService.stop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  private RefreshTable(orders: Order[]): void {\n    this.listOrders = orders;\n    this.itemsSelectedOrder = [];\n    this.selectedOrder = null;\n\n    if (!this.listOrders || this.listOrders.length == 0) {\n      this.dataSource.data = [];\n      this.totalRows = 0;\n    } else {\n      this.dataSource.data = this.listOrders;\n      this.totalRows = this.listOrders[0].TotalRows;\n    }\n  }\n\n  get newStatus() {\n    return this.statusForm.get('newStatus');\n  }\n\n  SettingsChanged(event: SettingChangedEvent): void {\n    this.usePrintingApp = event.UsePrintingApp;\n    this.labelPrintChoice = event.LabelPrintChoice;\n    this.isUniformCanteen = event.IsUniformCanteen;\n\n    if (this.isUniformCanteen) {\n      this.displayedColumns = uniformColumns;\n    } else {\n      this.displayedColumns = displayedColumns;\n    }\n  }\n\n  GetItemOptions(item: OrderItem): string {\n    let optionsText = '(';\n\n    //item stores modifers in Options\n    if (item.Options && item.Options.length > 0) {\n      item.Options.forEach(opt => {\n        // item has selected options\n        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {\n          opt.SelectedOptionList.forEach(selectedOpt => {\n            optionsText += selectedOpt + ', ';\n          });\n        }\n      });\n    }\n\n    //item stores modifiers in SelectedOptions\n    if (item.SelectedOptions && item.SelectedOptions.length > 0) {\n      item.SelectedOptions.forEach(opt => {\n        optionsText += opt.OptionName + ', ';\n      });\n    }\n\n    optionsText = optionsText.slice(0, -2);\n    optionsText += ')';\n    if (optionsText.length <= 1) {\n      optionsText = '';\n    }\n\n    return optionsText;\n  }\n\n  ShowButtonPrintLabels(): boolean {\n    if (!this.usePrintingApp) {\n      return true;\n    } else {\n      return !this.isUniformCanteen;\n    }\n  }\n\n  EnableMarkAsFulfilledButton(): boolean {\n    if (this.selectedOrder) {\n      if (this.selectedOrder.IsFulFilled) {\n        return false;\n      } else {\n        return true;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /////////////////////////////////////////////////////////\n  // Table functions\n  //////////////////////////////////////////////////////////\n  selection = new SelectionModel<Order>(true, []);\n\n  /** Whether the number of selected elements matches the total number of rows. */\n  isAllSelected(): boolean {\n    const numSelected = this.selection.selected.length;\n    let numRows = 0;\n\n    if (this.dataSource.data) {\n      numRows = this.dataSource.data.length;\n    }\n    return numSelected === numRows;\n  }\n\n  /** Selects all rows if they are not all selected; otherwise clear selection. */\n  masterToggle(): void {\n    this.isAllSelected()\n      ? this.selection.clear()\n      : this.dataSource.data.forEach(row => this.selection.select(row));\n  }\n\n  /** The label for the checkbox on the passed row */\n  checkboxLabel(row?: Order): string {\n    if (!row) {\n      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;\n    }\n    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row`;\n  }\n\n  RowClick(row: Order): void {\n    if (\n      this.selectedOrder &&\n      this.selectedOrder.OrderId === row.OrderId &&\n      this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId\n    ) {\n      this.selectedOrder = null;\n      this.itemsSelectedOrder = null;\n    } else {\n      this.selectedOrder = row;\n      this.itemsSelectedOrder = row.Items;\n    }\n\n    //row selection for uniforms\n    this.selection.toggle(row);\n    if (this.isUniformCanteen && this.selection.selected.length === 1) {\n      this.selectedOrder = this.selection.selected[0];\n      this.itemsSelectedOrder = this.selection.selected[0].Items;\n    }\n  }\n\n  PageChange(event: PageEvent): void {\n    this.clearRowSelection();\n    // Update filter\n    this.basePageChange(event);\n    this.LoadTableData();\n  }\n\n  SortChange(event: Sort) {\n    this.listfilters.PageIndex = 0;\n    this.listfilters.SortBy = event.active;\n    this.listfilters.SortDirection = event.direction;\n    this.LoadTableData();\n  }\n\n  isRowSelected(row: Order): boolean {\n    if (this.isUniformCanteen) {\n      //multi selected rows\n      return this.selection.isSelected(row);\n    } else {\n      return (\n        this.selectedOrder &&\n        this.selectedOrder.OrderId === row.OrderId &&\n        this.selectedOrder.Items[0].MenuItemId === row.Items[0].MenuItemId\n      );\n    }\n  }\n\n  showSelectedOrder(): boolean {\n    //show selected order canteen\n    if (!this.isUniformCanteen && this.selectedOrder) {\n      return true;\n      //show selected order uniform\n    } else {\n      return Boolean(this.isUniformCanteen && this.selection.selected.length == 1 && this.selectedOrder);\n    }\n  }\n\n  /////////////////////////////////////////////////////////\n  // Printing functions\n  //////////////////////////////////////////////////////////\n  PrintAllLabels(): void {\n    this.getOrdersRequest(true);\n    this.spinnerService.start();\n\n    if (this.labelPrintChoice === LabelPrintChoiceEnum.Order) {\n      this.printAllOrders();\n      return;\n    }\n    this.printAllItems();\n  }\n\n  printAllOrders(): void {\n    this.printingApiService.SetOrdersToPrintAPI(this.ordersRequest).subscribe({\n      next: (response: any) => {\n        if (response?.guid) {\n          this.handlePrintResponse(response.guid);\n        }\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n\n  printAllItems(): void {\n    this.printingApiService.SetItemsToPrintAPI(this.ordersRequest).subscribe({\n      next: (response: any) => {\n        if (response?.guid) {\n          this.handlePrintResponse(response.guid);\n        }\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n\n  PrintSelectedOrder(): void {\n    this.spinnerService.start();\n    if (this.labelPrintChoice == LabelPrintChoiceEnum.Item) {\n      this.printOneOrderByItem();\n    } else {\n      this.printOneOrderByOrder();\n    }\n  }\n\n  printOneOrderByOrder(): void {\n    this.printingApiService.SetOrderToPrintByOrderIdAPI(this.selectedOrder.OrderId).subscribe({\n      next: (response: any) => {\n        if (response?.guid) {\n          this.handlePrintResponse(response.guid);\n        }\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n\n  printOneOrderByItem(): void {\n    if (!this.selectedOrder.Items || !this.selectedOrder.Items.length) {\n      return;\n    }\n    const itemId: number = this.selectedOrder.Items[0].MenuItemId;\n\n    this.printingApiService.SetItemToPrintByItemIdAPI(this.selectedOrder.OrderId, itemId).subscribe({\n      next: (response: any) => {\n        if (response?.guid) {\n          this.handlePrintResponse(response.guid);\n        }\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n\n  handlePrintResponse(guid: string): void {\n    if (this.usePrintingApp) {\n      this._triggerThermalPrinter(guid);\n    } else {\n      this._triggerA4Printer(guid);\n    }\n  }\n\n  private _triggerThermalPrinter(guid: string): void {\n    let canteenPrinter: any = document.getElementById('canteenPrinter');\n    canteenPrinter.href = 'print://' + guid;\n    canteenPrinter.click();\n  }\n\n  private _triggerA4Printer(guid: string): void {\n    this.printingApiService.SetPrintingGuid(guid);\n    this.router.navigate(['canteen/labels']);\n  }\n}\n", "<div class=\"container-fluid pt-4\">\n  <canteen-order-filter (filtersChanged)=\"FiltersChanged($event)\" (settingsChanged)=\"SettingsChanged($event)\">\n  </canteen-order-filter>\n\n  <div class=\"row mt-4\">\n    <div class=\"col-sm-7 d-flex justify-content-between align-items-end\">\n      <div class=\"d-flex justify-content-between align-items-end\">\n        <h3 style=\"margin: 0\">\n          <strong>Labels</strong> <span style=\"font-weight: 400\"> ({{ this.totalRows }})</span>\n        </h3>\n        <h4 style=\"margin: 0\" *ngIf=\"isUniformCanteen\" class=\"labelCount\">\n          Selected ({{ selection.selected.length }})\n        </h4>\n      </div>\n\n      <div class=\"d-flex\">\n        <div *ngIf=\"isUniformCanteen\">\n          <uniform-order-status-picker\n            [selectedOrders]=\"selection.selected\"\n            (clearRowSelection)=\"clearRowSelection()\"\n            (loadTableData)=\"LoadTableData()\"\n          ></uniform-order-status-picker>\n        </div>\n\n        <basic-button text=\"Print Labels\" (onPress)=\"PrintAllLabels()\" [buttonStyle]=\"1\"></basic-button>\n\n        <a id=\"canteenPrinter\" style=\"color: white\"></a>\n      </div>\n    </div>\n  </div>\n\n  <!-- table orders column -->\n  <div class=\"row mt-2\">\n    <div class=\"col-sm-7 pb-3\">\n      <div class=\"table-container mat-elevation-z6\">\n        <table\n          mat-table\n          [dataSource]=\"dataSource\"\n          class=\"mat-elevation-z8 tableau\"\n          matSort\n          matSortActive=\"ClassName\"\n          matSortDisableClear\n          matSortDirection=\"desc\"\n          (matSortChange)=\"SortChange($event)\"\n        >\n          <!-- Checkbox Column -->\n          <ng-container matColumnDef=\"select\">\n            <th mat-header-cell *matHeaderCellDef>\n              <mat-checkbox\n                (change)=\"$event ? masterToggle() : null\"\n                [checked]=\"selection.hasValue() && isAllSelected()\"\n                [indeterminate]=\"selection.hasValue() && !isAllSelected()\"\n                [aria-label]=\"checkboxLabel()\"\n              >\n              </mat-checkbox>\n            </th>\n            <td mat-cell *matCellDef=\"let row\">\n              <mat-checkbox\n                (click)=\"selection.toggle(row)\"\n                (change)=\"$event ? selection.toggle(row) : null\"\n                [checked]=\"selection.isSelected(row)\"\n                [aria-label]=\"checkboxLabel(row)\"\n              >\n              </mat-checkbox>\n            </td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"OrderId\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>No.</th>\n            <td mat-cell *matCellDef=\"let element\">{{ element.OrderId }}</td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"CanteenStatus\">\n            <th mat-header-cell *matHeaderCellDef>Status</th>\n            <td mat-cell *matCellDef=\"let element\">{{ element.CanteenStatus }}</td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"SchoolName\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>School</th>\n            <td mat-cell *matCellDef=\"let element\">{{ element.SchoolName }}</td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"MenuType\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Order Type</th>\n            <td mat-cell *matCellDef=\"let element\">{{ element.MenuType | merchantMenuName }}</td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"StudentName\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Student</th>\n            <td mat-cell *matCellDef=\"let element\">\n              <a\n                matTooltip=\"View Child\"\n                routerLink=\"./../students/details/{{ element.StudentId }}\"\n                class=\"student-link\"\n                >{{ element.StudentName }}</a\n              >\n            </td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"ClassName\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header disableClear>Class</th>\n            <td mat-cell *matCellDef=\"let element\">{{ element.ClassName }}</td>\n          </ng-container>\n\n          <ng-container matColumnDef=\"OrderDate\">\n            <th mat-header-cell *matHeaderCellDef mat-sort-header [disabled]=\"!isUniformCanteen\">\n              Order Date\n            </th>\n            <td mat-cell *matCellDef=\"let element\">\n              {{\n                isUniformCanteen\n                  ? (element.OrderDate | date : 'EEEE d MMMM y')\n                  : (element.OrderDate | date : 'EEEE d MMMM')\n              }}\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"displayedColumns; sticky: true\"></tr>\n          <tr\n            mat-row\n            *matRowDef=\"let row; columns: displayedColumns\"\n            (click)=\"RowClick(row)\"\n            [ngClass]=\"{ selectedRow: isRowSelected(row) }\"\n          ></tr>\n        </table>\n      </div>\n      <mat-paginator\n        [pageSize]=\"25\"\n        [pageSizeOptions]=\"[25, 50, 100]\"\n        [length]=\"totalRows\"\n        [pageIndex]=\"listfilters.PageIndex\"\n        (page)=\"PageChange($event)\"\n      ></mat-paginator>\n    </div>\n\n    <!-- selected order column -->\n    <div class=\"col-sm-5 pb-3\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <selected-order\n            [order]=\"selectedOrder\"\n            [showOrder]=\"showSelectedOrder()\"\n            [itemsSelectedOrder]=\"itemsSelectedOrder\"\n            (printOrder)=\"PrintSelectedOrder()\"\n          ></selected-order>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "export * from './canteen/canteen.component';\nexport * from './label-print/label-print.component';\nexport * from './ios-labels-printing/ios-labels-printing.component';\nexport * from './canteen-order-filter/canteen-order-filter.component';\nexport * from './canteen-order-type-filter/canteen-order-type-filter.component';\nexport * from './block-print/block-print.component';\nexport * from './notice-board/notice-board.component';\nexport * from './announcements/announcements.component';\nexport * from './canteen-order-category-filter/canteen-order-category-filter.component';\nexport * from './canteen-order-school-filter/canteen-order-school-filter.component';\nexport * from './articles/articles.component';\nexport * from './a4-print-form/a4-print-form.component';\nexport * from './notice-header/notice-header.component';\nexport * from './notice-table/notice-table.component';\nexport * from './uniform-order-status-picker/uniform-order-status-picker.component';\nexport * from './selected-order/selected-order.component';\nexport * from './label-template/label-template.component';\nexport * from './label-template-ios/label-template-ios.component';\n", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\n\n// Models\nimport { A4PageLabel } from 'src/app/sharedModels';\n\n// Services\nimport { CanteenService } from 'src/app/sharedServices';\n\n@Component({\n  selector: 'app-ios-labels-printing',\n  templateUrl: './ios-labels-printing.component.html',\n  styleUrls: ['./ios-labels-printing.component.scss'],\n})\nexport class IosLabelsPrintingComponent implements OnInit {\n  pagesLabels: A4PageLabel[] = [];\n  custom: boolean = false;\n  displaySchoolName: boolean = false;\n\n  constructor(private canteenService: CanteenService, private route: ActivatedRoute) {}\n\n  ngOnInit() {\n    this.displaySchoolName = this.route.snapshot.params['displaySchoolName'] === 'true';\n    this.pagesLabels = this.canteenService.GetLabels();\n  }\n}\n", "<div id=\"htmlData\" class=\"background\" style=\"width: 215mm\">\n  <div class=\"pageA4\" *ngFor=\"let page of pagesLabels\">\n    <div *ngFor=\"let label of page.fakeLabels\" class=\"colLabel\">.</div>\n    <div *ngFor=\"let label of page.listLabels\" class=\"colLabel\">\n      <label-template [label]=\"label\" [displaySchoolName]=\"displaySchoolName\"></label-template>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as _ from 'lodash';\n\n// Models\nimport { BaseComponent, LabelFormatV2, LabelV2 } from '../../../sharedModels';\n\n// Services\nimport { PrintingApiService, SpinnerService } from '../../../sharedServices';\n\n@Component({\n  selector: 'app-label-print',\n  templateUrl: './label-print.component.html',\n  styleUrls: ['./label-print.component.scss'],\n})\nexport class LabelPrintComponent extends BaseComponent implements OnInit {\n  listLabels: LabelV2[] = [];\n\n  constructor(\n    private router: Router,\n    private printingApiService: PrintingApiService,\n    private spinnerService: SpinnerService\n  ) {\n    super();\n  }\n\n  ngOnInit() {\n    // get the orders to print\n    this.spinnerService.start();\n\n    const guid = this.printingApiService.GetPrintingGuid();\n\n    this.printingApiService.GetOrdersToPrintByGuidAPI(guid).subscribe({\n      next: (response: LabelFormatV2) => {\n        this.listLabels = response.Labels;\n\n        if (!this.listLabels || this.listLabels.length == 0) {\n          this.router.navigate(['./canteen']);\n        }\n\n        this.spinnerService.stop();\n      },\n      error: error => {\n        this.handleErrorFromService(error);\n        this.spinnerService.stop();\n      },\n    });\n  }\n}\n", "<div class=\"row\">\n  <div class=\"col-12\">\n    <!-- <ul class=\"tabLabels\">\n        <li [ngClass]=\"{'active': ShowTab(menuType.Recess)}\" (click)=\"ChangeTab(menuType.Recess)\">Recess</li>\n        <li [ngClass]=\"{'active': ShowTab(menuType.Lunch)}\" (click)=\"ChangeTab(menuType.Lunch)\">Lunch</li>\n        <li [ngClass]=\"{'active': ShowTab(menuType.Event)}\" (click)=\"ChangeTab(menuType.Event)\">Event</li>\n      </ul> -->\n  </div>\n</div>\n\n<ng-container *ngIf=\"listLabels && listLabels.length > 0\">\n  <div class=\"container-fluid\">\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <h3>Labels</h3>\n      </div>\n    </div>\n  </div>\n  <block-print [listLabels]=\"listLabels\"></block-print>\n</ng-container>\n\n<ng-container *ngIf=\"!listLabels || listLabels.length == 0\">\n  <div class=\"container-fluid\">\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <h3>No Labels</h3>\n      </div>\n    </div>\n  </div>\n</ng-container>\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { LabelV2 } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'label-template-ios',\n  templateUrl: './label-template-ios.component.html',\n  styleUrls: ['./label-template-ios.component.scss'],\n})\nexport class LabelTemplateIosComponent implements OnInit {\n  @Input() label: LabelV2;\n  @Input() displaySchoolName: boolean;\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  GetPrintValue(printField: string, label: LabelV2): string {\n    var pos = label.Positions.find(x => x.Name == printField);\n\n    if (pos) {\n      return pos.Value;\n    } else {\n      return '';\n    }\n  }\n\n  ShowAllergies(label: LabelV2): boolean {\n    var pos = label.Positions.find(x => x.Name == 'Allergies');\n\n    if (pos) {\n      return !Boolean(pos.Value == null || pos.Value == '');\n    } else {\n      return false;\n    }\n  }\n}\n", "<ng-container>\n  <!-- First line: School Name -->\n  <div *ngIf=\"displaySchoolName\">\n    <span class=\"title\">{{ GetPrintValue('Title', label) }}</span>\n  </div>\n  <!-- Second line: Name - SchoolClass -->\n  <div>\n    <span class=\"subTitle\">{{ GetPrintValue('SubTitle', label) }}</span>\n  </div>\n\n  <!-- Allergies -->\n  <div *ngIf=\"ShowAllergies(label)\">\n    <span class=\"allergies\">Allergies: {{ GetPrintValue('Allergies', label) }}</span>\n  </div>\n\n  <!-- Items -->\n  <ul *ngIf=\"label.Items\">\n    <li class=\"items\" *ngFor=\"let item of label.Items\">\n      {{ item }}\n    </li>\n  </ul>\n\n  <!-- Bottom line: Label number, menu type, date -->\n  <span class=\"footer labelNumber\">{{ label.LabelNumber }}</span>\n  <span class=\"footer footerLeft\">{{ GetPrintValue('FooterLeft', label) }}</span>\n  <span class=\"footer footerRight\">{{ GetPrintValue('FooterRight', label) }}</span>\n</ng-container>\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { LabelV2 } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'label-template',\n  templateUrl: './label-template.component.html',\n  styleUrls: ['./label-template.component.scss'],\n})\nexport class LabelTemplateComponent implements OnInit {\n  @Input() label: LabelV2;\n  @Input() displaySchoolName: boolean;\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  GetPrintValue(printField: string, label: LabelV2): string {\n    var pos = label.Positions.find(x => x.Name == printField);\n\n    if (pos) {\n      return pos.Value;\n    } else {\n      return '';\n    }\n  }\n\n  ShowAllergies(label: LabelV2): boolean {\n    var pos = label.Positions.find(x => x.Name == 'Allergies');\n\n    if (pos) {\n      return !Boolean(pos.Value == null || pos.Value == '');\n    } else {\n      return false;\n    }\n  }\n}\n", "<ng-container>\n  <!-- First line: School Name -->\n  <div *ngIf=\"displaySchoolName\">\n    <span class=\"title\">{{ GetPrintValue('Title', label) }}</span>\n  </div>\n  <!-- Second line: Name - SchoolClass -->\n  <div>\n    <span class=\"subTitle\">{{ GetPrintValue('SubTitle', label) }}</span>\n  </div>\n\n  <!-- Allergies -->\n  <div *ngIf=\"ShowAllergies(label)\">\n    <span class=\"allergies\">Allergies: {{ GetPrintValue('Allergies', label) }}</span>\n  </div>\n\n  <!-- Items -->\n  <ul *ngIf=\"label.Items\">\n    <li class=\"items\" *ngFor=\"let item of label.Items\">\n      {{ item }}\n    </li>\n  </ul>\n\n  <!-- Bottom line: Label number, menu type, date -->\n  <span class=\"footer labelNumber\">{{ label.LabelNumber }}</span>\n  <span class=\"footer footerLeft\">{{ GetPrintValue('FooterLeft', label) }}</span>\n  <span class=\"footer footerRight\">{{ GetPrintValue('FooterRight', label) }}</span>\n</ng-container>\n", "import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-notice-board',\n  templateUrl: './notice-board.component.html',\n  styleUrls: ['./notice-board.component.scss'],\n})\nexport class NoticeBoardComponent implements OnInit {\n  constructor() {}\n\n  ngOnInit() {}\n}\n", "<div class=\"col-8 settingsWrapper\">\n  <settings-row text=\"Announcements\" route=\"/canteen/notice/announcements\">\n    <img src=\"assets/icons/announcements.svg\" />\n  </settings-row>\n\n  <settings-row text=\"Articles\" route=\"/canteen/notice/articles\" lastRow=\"true\">\n    <img src=\"assets/icons/article.svg\" />\n  </settings-row>\n</div>\n", "import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';\n\n//Models\nimport { Canteen } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'notice-header',\n  templateUrl: './notice-header.component.html',\n  styleUrls: ['./notice-header.component.scss'],\n})\nexport class NoticeHeaderComponent implements OnInit {\n  @Input() title: string;\n  @Output() schoolChanged: EventEmitter<number> = new EventEmitter();\n  @Output() merchantChanged: EventEmitter<Canteen> = new EventEmitter();\n  @Output() openForm = new EventEmitter();\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  OnSchoolSelect(event: number) {\n    this.schoolChanged.emit(event);\n  }\n\n  OnMerchantChange(event: Canteen) {\n    this.merchantChanged.emit(event);\n  }\n\n  openNoticeForm() {\n    this.openForm.emit();\n  }\n}\n", "<div class=\"titleWrapper\">\n  <img sizes=\"24\" src=\"assets/icons/announcements.svg\" />\n  <h1 class=\"titleMain\">{{ title }}s</h1>\n</div>\n\n<merchant-school-picker\n  (schoolChanged)=\"OnSchoolSelect($event)\"\n  (merchantChanged)=\"OnMerchantChange($event)\"\n></merchant-school-picker>\n\n<div class=\"titleDescriptionWrapper\">\n  <h3 class=\"titleDescription\">{{ title }} List</h3>\n  <a class=\"titleDescriptionButton\" (click)=\"openNoticeForm()\">\n    <p class=\"titleDescriptionButtonText\">New {{ title }}</p>\n    <img sizes=\"24\" src=\"assets/icons/plus.svg\" />\n  </a>\n</div>\n", "import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\n\n//Models\nimport { Notice, NoticeStatusEnum, NoticeType } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'notice-table',\n  templateUrl: './notice-table.component.html',\n  styleUrls: ['./notice-table.component.scss'],\n})\nexport class NoticeTableComponent implements OnInit {\n  @Input() tableData: Notice[];\n  @Input() type: NoticeType;\n  @Output() selectRow: EventEmitter<Notice> = new EventEmitter();\n\n  displayedColumns: string[] = ['title', 'body', 'IsActive', 'Status'];\n  NoticeStatusEnum = NoticeStatusEnum;\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  noticeRowClick(event: Notice) {\n    this.selectRow.emit(event);\n  }\n}\n", "<table *ngIf=\"tableData\" mat-table [dataSource]=\"tableData\" class=\"mat-elevation-z8 tableau table\">\n  <ng-container matColumnDef=\"title\">\n    <th mat-header-cell *matHeaderCellDef class=\"title\">Title</th>\n    <td mat-cell *matCellDef=\"let element\" class=\"tableElement\">{{ element.Title }}</td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"body\">\n    <th mat-header-cell *matHeaderCellDef class=\"titleCenter\">Body/Description</th>\n    <td\n      mat-cell\n      *matCellDef=\"let element\"\n      class=\"tableElement\"\n      [ngClass]=\"{ noDescription: !element.Description.length }\"\n    >\n      {{ element.Description || 'No description' }}\n    </td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"IsActive\">\n    <th mat-header-cell *matHeaderCellDef class=\"title\">Active</th>\n    <td mat-cell *matCellDef=\"let element\">\n      <div *ngIf=\"!element.IsActive\" class=\"inActiveCheckbox\"></div>\n      <img *ngIf=\"element.IsActive\" sizes=\"24\" src=\"assets/icons/checkBox.svg\" class=\"checkBox\" />\n    </td>\n  </ng-container>\n\n  <ng-container matColumnDef=\"Status\">\n    <th mat-header-cell *matHeaderCellDef class=\"title\">Status</th>\n    <td mat-cell *matCellDef=\"let element\" style=\"text-align: center\">\n      <span *ngIf=\"element.Status == NoticeStatusEnum.WaitingValidation\">Waiting for validation</span>\n      <span *ngIf=\"element.Status == NoticeStatusEnum.Refused\" style=\"color: red\">Not valid</span>\n      <span *ngIf=\"element.Status == NoticeStatusEnum.Validated\" style=\"color: green\">Approved</span>\n    </td>\n  </ng-container>\n\n  <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n  <tr\n    mat-row\n    *matRowDef=\"let row; columns: displayedColumns\"\n    (click)=\"noticeRowClick(row)\"\n    class=\"rowElement\"\n  ></tr>\n</table>\n", "import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { Order, OrderItem } from 'src/app/sharedModels';\n\n@Component({\n  selector: 'selected-order',\n  templateUrl: './selected-order.component.html',\n  styleUrls: ['./selected-order.component.scss'],\n})\nexport class SelectedOrderComponent implements OnInit {\n  @Input() order: Order;\n  @Input() itemsSelectedOrder: OrderItem[];\n  @Input() showOrder: boolean;\n  @Output() printOrder = new EventEmitter();\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  GetItemOptions(item: OrderItem): string {\n    let optionsText = '(';\n\n    //item stores modifiers in Options\n    if (item.Options && item.Options.length > 0) {\n      item.Options.forEach(opt => {\n        // item has selected options\n        if (opt.SelectedOptionList && opt.SelectedOptionList.length > 0) {\n          opt.SelectedOptionList.forEach(selectedOpt => {\n            optionsText += selectedOpt + ', ';\n          });\n        }\n      });\n    }\n\n    //item stores modifiers in SelectedOptions\n    if (item.SelectedOptions && item.SelectedOptions.length > 0) {\n      item.SelectedOptions.forEach(opt => {\n        optionsText += opt.OptionName + ', ';\n      });\n    }\n\n    optionsText = optionsText.slice(0, -2);\n    optionsText += ')';\n    if (optionsText.length <= 1) {\n      optionsText = '';\n    }\n\n    return optionsText;\n  }\n\n  printButtonPressed() {\n    this.printOrder.emit();\n  }\n}\n", "<div class=\"divOrderDetails mat-elevation-z2\">\n  <div class=\"header\">\n    <h3>Selected Order</h3>\n    <basic-button\n      *ngIf=\"showOrder\"\n      text=\"Print this order\"\n      (onPress)=\"printButtonPressed()\"\n      [buttonStyle]=\"1\"\n    ></basic-button>\n  </div>\n\n  <div *ngIf=\"showOrder\">\n    <div class=\"dataline\">\n      <strong>{{ order.SchoolName }}</strong>\n    </div>\n    <div class=\"dataline\">\n      <span>{{ order.ClassName }}</span>\n      <span *ngIf=\"order.Allergies\"> - ({{ order.Allergies }})</span>\n    </div>\n    <strong>{{ order.StudentName }}</strong>\n    <ul>\n      <li *ngFor=\"let item of itemsSelectedOrder\">\n        {{ item.Quantity }} x {{ item.Name }} {{ GetItemOptions(item) }}\n      </li>\n    </ul>\n    <div *ngIf=\"order\">Order ID: {{ order.LocalRunNumber }}</div>\n  </div>\n</div>\n", "import { Component, Input, EventEmitter, Output } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { DialogResultComponent } from 'src/app/shared/components';\nimport { OrderApiService, SpinnerService } from 'src/app/sharedServices';\nimport { BaseComponent, CanteenStatusEnum, Order, ResultDialogData } from '../../../sharedModels';\n\n@Component({\n  selector: 'uniform-order-status-picker',\n  templateUrl: './uniform-order-status-picker.component.html',\n  styleUrls: ['./uniform-order-status-picker.component.scss'],\n})\nexport class UniformOrderStatusPickerComponent extends BaseComponent {\n  @Input() selectedOrders: Order[];\n  @Output() loadTableData = new EventEmitter();\n  @Output() clearRowSelection = new EventEmitter();\n  CanteenStatusEnum = CanteenStatusEnum;\n\n  constructor(\n    private spinnerService: SpinnerService,\n    private orderAPIService: OrderApiService,\n    public dialog: MatDialog\n  ) {\n    super();\n  }\n\n  showDialog(newStatus: CanteenStatusEnum): void {\n    if (this.completedOrdersSelected()) {\n      this.showWarningDialog();\n      return;\n    }\n    this.showUpdateStatusDialog(newStatus);\n  }\n\n  completedOrdersSelected(): boolean {\n    return this.selectedOrders.some((order: Order) => order.CanteenStatus === CanteenStatusEnum.Completed);\n  }\n\n  showWarningDialog(): void {\n    const data = this.getWarningDialogData();\n\n    this.dialog.open(DialogResultComponent, {\n      width: '400px',\n      disableClose: true,\n      data: data,\n    });\n  }\n\n  showUpdateStatusDialog(newStatus: CanteenStatusEnum): void {\n    const orderCountText =\n      this.selectedOrders.length === 1 ? '1 order' : `${this.selectedOrders.length} orders`;\n    const data = this.getUpdateDialogData(orderCountText, newStatus);\n\n    const dialogRef = this.dialog.open(DialogResultComponent, {\n      width: '400px',\n      disableClose: true,\n      data: data,\n    });\n\n    dialogRef.afterClosed().subscribe(cancelResult => {\n      if (!cancelResult) {\n        this.updateOrderStatus(newStatus);\n      }\n    });\n  }\n\n  updateOrderStatus(newStatus: CanteenStatusEnum): void {\n    this.spinnerService.start();\n    const selectedOrderIds = this.selectedOrders.map(el => el.OrderId);\n\n    this.orderAPIService.UpdateListCanteenStatusOrder(selectedOrderIds, newStatus).subscribe({\n      next: response => {\n        // Give time for orders table in the replica db to update before refreshing orders\n        setTimeout(() => {\n          this.loadTableData.emit();\n        }, 1000);\n\n        this.clearRowSelection.emit();\n      },\n      error: error => {\n        this.spinnerService.stop();\n        this.handleErrorFromService(error);\n      },\n    });\n  }\n\n  getWarningDialogData(): ResultDialogData {\n    const data = new ResultDialogData();\n    data.TitleLine1 = 'Completed orders cannot have their status updated';\n    data.TextLine2 = 'Please unselect completed orders to continue';\n    data.ConfirmButton = 'Ok';\n    return data;\n  }\n\n  getUpdateDialogData(orderCount: string, newStatus: CanteenStatusEnum): ResultDialogData {\n    const data = new ResultDialogData();\n    data.TitleLine1 = 'Are you sure you want to update the order status? ';\n    data.TextLine2 = this.getDialogMessage(orderCount, newStatus);\n    data.CancelButton = 'Cancel';\n    data.ConfirmButton = 'Yes, change status';\n    return data;\n  }\n\n  getDialogMessage(orderCount: string, newStatus: CanteenStatusEnum): string {\n    return newStatus === CanteenStatusEnum.Completed\n      ? `Once an order's status is updated to \"${CanteenStatusEnum.Completed}\" the status cannot be changed again`\n      : `${orderCount} will be updated to “${newStatus}”`;\n  }\n}\n", "<button [disabled]=\"!selectedOrders.length\" mat-button [matMenuTriggerFor]=\"menu\" class=\"status-menu-btn\">\n  Order Status <mat-icon>arrow_drop_down</mat-icon>\n</button>\n<mat-menu #menu=\"matMenu\">\n  <button\n    *ngFor=\"let status of CanteenStatusEnum | keyvalue\"\n    mat-menu-item\n    (click)=\"showDialog(status.value)\"\n    class=\"menu-status-picker\"\n  >\n    {{ status.value }}\n  </button>\n</mat-menu>\n", "import { Injectable } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { UserCashless, Canteen } from '../../sharedModels';\nimport { Store, select } from '@ngrx/store';\nimport { CanteenState } from 'src/app/states';\nimport { selectedCanteen } from 'src/app/states/canteen/canteen.selectors';\n\n// Services\nimport { UserService } from '../authentication';\nimport { Observable, of } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\n\n@Injectable()\nexport class MenuEditorGuardService  {\n  constructor(private store: Store<{ canteen: CanteenState }>, public router: Router) {}\n  canLoad(): Observable<boolean> | boolean {\n    return this.store.select(selectedCanteen).pipe(\n      switchMap(selectedCanteen => {\n        if (!selectedCanteen.IsMenuEditorAvailable) {\n          this.router.navigate(['/canteen']);\n          return of(false);\n        } else {\n          return of(true);\n        }\n      })\n    );\n  }\n\n  canActivateChild(): Observable<boolean> | boolean {\n    return this.store.select(selectedCanteen).pipe(\n      switchMap(selectedCanteen => {\n        if (!selectedCanteen.IsMenuEditorAvailable) {\n          this.router.navigate(['/canteen']);\n          return of(false);\n        } else {\n          return of(true);\n        }\n      })\n    );\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { UserCashless } from '../../sharedModels';\n\n// Services\nimport { Observable, of } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\nimport { Store, select } from '@ngrx/store';\nimport { CanteenState } from 'src/app/states';\nimport { selectedCanteen } from 'src/app/states/canteen/canteen.selectors';\n\n@Injectable()\nexport class SalesReportGuardService  {\n  constructor(private store: Store<{ canteen: CanteenState }>, public router: Router) {}\n  canLoad(): Observable<boolean> | boolean {\n    return this.store.select(selectedCanteen).pipe(\n      switchMap(selectedCanteen => {\n        if (!selectedCanteen.IsSaleReportsAvailable) {\n          this.router.navigate(['/canteen']);\n          return of(false);\n        } else {\n          return of(true);\n        }\n      })\n    );\n  }\n\n  canActivateChild(): Observable<boolean> | boolean {\n    return this.store.select(selectedCanteen).pipe(\n      switchMap(selectedCanteen => {\n        if (!selectedCanteen.IsSaleReportsAvailable) {\n          this.router.navigate(['/canteen']);\n          return of(false);\n        } else {\n          return of(true);\n        }\n      })\n    );\n  }\n}\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { Router, NavigationStart, RouterModule, ActivatedRoute } from '@angular/router';\r\n\r\nimport { UserCashless, MerchantTypeEnum, Roles } from '../../../sharedModels';\r\nimport { UserService, SpinnerService } from '../../../sharedServices';\r\n\r\n// ngrx\r\nimport { Store, select } from '@ngrx/store';\r\nimport { Observable, Subscription, timer } from 'rxjs';\r\nimport { CanteenState } from '../../../states';\r\nimport { canteenStateSelector } from '../../../states/canteen/canteen.selectors';\r\nimport { NavBarData } from 'src/app/sharedModels/navBar/navBar';\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { BrazeTimerService } from 'src/app/sharedServices/brazeTimer.service';\r\n\r\n@Component({\r\n  selector: 'merchant-navbar',\r\n  standalone: true,\r\n  imports: [MatToolbarModule, MatButtonModule, MatMenuModule, CommonModule, MatIconModule, RouterModule],\r\n  templateUrl: './merchant-navbar.component.html',\r\n  styleUrls: ['./merchant-navbar.component.scss'],\r\n})\r\nexport class MerchantNavbarComponent implements OnInit, OnDestroy {\r\n  user: UserCashless;\r\n  isUniformCanteen: boolean = false;\r\n  isMenuEditorRole: boolean = false;\r\n  isEventManegementRole: boolean = false;\r\n  routeName: string;\r\n  private subscription: Subscription;\r\n  timerSubscription: Observable<number>;\r\n  brazeTimerSubscription: Subscription;\r\n  refreshCount: number = 0;\r\n  navDataToShow: NavBarData[] = [];\r\n  navDataOptions: NavBarData[] = [\r\n    { Name: 'Orders', Link: '/canteen/home' },\r\n    { Name: 'Reports', Link: '/canteen/reports' },\r\n    { Name: 'Students', Link: '/canteen/students' },\r\n    { Name: 'Menu Editor', Link: '/canteen/editor' },\r\n    { Name: 'Noticeboard', Link: '/canteen/notice' },\r\n    { Name: 'Events', Link: '/canteen/events' },\r\n    { Name: 'Settings', Link: '/canteen/settings' },\r\n      { Name: 'POS', Link: '/canteen/pos' }\r\n  ];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private spinnerService: SpinnerService,\r\n    private store: Store<{ canteen: CanteenState }>,\r\n    private brazeTimerService: BrazeTimerService,\r\n     private route: ActivatedRoute,\r\n  ) {\r\n    this.routeName = this.router.url;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n\r\n// get view type paramter from query params\r\n\r\n  this.initializeFromRoute();\r\n\r\n\r\n    this.spinnerService.stop();\r\n    this.userService.IdentifyUser();\r\n\r\n    this.subscription = this.store.pipe(select(canteenStateSelector)).subscribe((state: CanteenState) => {\r\n      this.isUniformCanteen = state?.selected && state.selected.CanteenType == MerchantTypeEnum.Uniform;\r\n      this.isMenuEditorRole = state?.selected && state.selected.IsMenuEditorAvailable;\r\n      this.isEventManegementRole = state?.selected && state.selected.IsEventManagementAvailable;\r\n      this.generateNavDataToShow();\r\n    });\r\n\r\n    this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationStart) {\r\n        this.routeName = event.url;\r\n      }\r\n    });\r\n\r\n    this.brazeTimerService.setUpBrazeInAppMessageTimer(Roles.Canteen);\r\n  }\r\n\r\n\r\n \r\nviewType: 'merchant' | 'student';\r\n\r\nprivate initializeFromRoute(): void {\r\n  this.route.queryParams.subscribe(params => {\r\n    const viewParam = params['viewType'];\r\n    this.viewType = viewParam === 'student' ? 'student' : 'merchant';\r\n  });\r\n}\r\n\r\n\r\n \r\n\r\n  generateNavDataToShow(): void {\r\n    this.navDataToShow = [];\r\n    this.navDataOptions.forEach(navOption => {\r\n      if (navOption.Name === 'Menu Editor' && !this.isMenuEditorRole) {\r\n        return;\r\n      }\r\n      if (navOption.Name === 'Events' && (!this.isEventManegementRole || this.isUniformCanteen)) {\r\n        return;\r\n      }\r\n      if (navOption.Name === 'Reports') {\r\n        navOption.Link = this.isUniformCanteen ? '/canteen/reports/uniformitems' : '/canteen/reports';\r\n      }\r\n      this.navDataToShow.push(navOption);\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscription?.unsubscribe();\r\n    this.brazeTimerService.unsubscribeFromBrazeTimer();\r\n  }\r\n\r\n  SignOut(): void {\r\n    this.userService.logout();\r\n  }\r\n}\r\n", "<mat-toolbar color=\"primary\" role=\"heading\" class=\"cashless-toolbar\" *ngIf=\"viewType === 'merchant'\">\n  <button mat-icon-button [matMenuTriggerFor]=\"menuNav\" class=\"d-block d-lg-none\">\n    <mat-icon>more_vert</mat-icon>\n  </button>\n  <mat-menu #menuNav=\"matMenu\">\n    <button *ngFor=\"let data of navDataToShow\" mat-menu-item [routerLink]=\"data.Link\">\n      <span>{{ data.Name }}</span>\n    </button>\n  </mat-menu>\n\n  <ul class=\"d-none d-lg-block\">\n    <li *ngFor=\"let data of navDataToShow\">\n      <a [routerLink]=\"data.Link\" routerLinkActive=\"activeLink\">{{ data.Name }}</a>\n      <span class=\"tab-selected\"></span>\n    </li>\n  </ul>\n\n  <!-- This fills the remaining space of the current row -->\n  <span class=\"example-fill-remaining-space\"></span>\n</mat-toolbar>\n<div>\n  <router-outlet></router-outlet>\n</div>\n", "import * as i0 from '@angular/core';\nimport { Injectable, Directive, Input, HostListener, NgModule } from '@angular/core';\n\nclass PrintBase {\n    _printStyle = [];\n    _styleSheetFile = '';\n    //#region Getters and Setters\n    /**\n     * Sets the print styles based on the provided values.\n     *\n     * @param {Object} values - Key-value pairs representing print styles.\n     * @protected\n     */\n    setPrintStyle(values) {\n        this._printStyle = [];\n        for (let key in values) {\n            if (values.hasOwnProperty(key)) {\n                this._printStyle.push((key + JSON.stringify(values[key])).replace(/['\"]+/g, ''));\n            }\n        }\n    }\n    /**\n     *\n     *\n     * @returns the string that create the stylesheet which will be injected\n     * later within <style></style> tag.\n     *\n     * -join/replace to transform an array objects to css-styled string\n     */\n    returnStyleValues() {\n        return `<style> ${this._printStyle.join(' ').replace(/,/g, ';')} </style>`;\n    }\n    /**\n   * @returns string which contains the link tags containing the css which will\n   * be injected later within <head></head> tag.\n   *\n   */\n    returnStyleSheetLinkTags() {\n        return this._styleSheetFile;\n    }\n    /**\n     * Sets the style sheet file based on the provided CSS list.\n     *\n     * @param {string} cssList - CSS file or list of CSS files.\n     * @protected\n     */\n    setStyleSheetFile(cssList) {\n        let linkTagFn = function (cssFileName) {\n            return `<link rel=\"stylesheet\" type=\"text/css\" href=\"${cssFileName}\">`;\n        };\n        if (cssList.indexOf(',') !== -1) {\n            const valueArr = cssList.split(',');\n            this._styleSheetFile = valueArr.map(val => linkTagFn(val)).join('');\n        }\n        else {\n            this._styleSheetFile = linkTagFn(cssList);\n        }\n    }\n    //#endregion\n    //#region Private methods used by PrintBase\n    /**\n     * Updates the default values for input elements.\n     *\n     * @param {HTMLCollectionOf<HTMLInputElement>} elements - Collection of input elements.\n     * @private\n     */\n    updateInputDefaults(elements) {\n        for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            element['defaultValue'] = element.value;\n            if (element['checked'])\n                element['defaultChecked'] = true;\n        }\n    }\n    /**\n     * Updates the default values for select elements.\n     *\n     * @param {HTMLCollectionOf<HTMLSelectElement>} elements - Collection of select elements.\n     * @private\n     */\n    updateSelectDefaults(elements) {\n        for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            const selectedIdx = element.selectedIndex;\n            const selectedOption = element.options[selectedIdx];\n            selectedOption.defaultSelected = true;\n        }\n    }\n    /**\n     * Updates the default values for textarea elements.\n     *\n     * @param {HTMLCollectionOf<HTMLTextAreaElement>} elements - Collection of textarea elements.\n     * @private\n     */\n    updateTextAreaDefaults(elements) {\n        for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            element['defaultValue'] = element.value;\n        }\n    }\n    /**\n     * Converts a canvas element to an image and returns its HTML string.\n     *\n     * @param {HTMLCanvasElement} element - The canvas element to convert.\n     * @returns {string} - HTML string of the image.\n     * @private\n     */\n    canvasToImageHtml(element) {\n        const dataUrl = element.toDataURL();\n        return `<img src=\"${dataUrl}\" style=\"max-width: 100%;\">`;\n    }\n    /**\n     * Includes canvas contents in the print section via img tags.\n     *\n     * @param {HTMLCollectionOf<HTMLCanvasElement>} elements - Collection of canvas elements.\n     * @private\n     */\n    updateCanvasToImage(elements) {\n        for (let i = 0; i < elements.length; i++) {\n            const element = this.canvasToImageHtml(elements[i]);\n            elements[i].insertAdjacentHTML('afterend', element);\n            elements[i].remove();\n        }\n    }\n    /**\n     * Retrieves the HTML content of a specified printing section.\n     *\n     * @param {string} printSectionId - Id of the printing section.\n     * @returns {string | null} - HTML content of the printing section, or null if not found.\n     * @private\n     */\n    getHtmlContents(printSectionId) {\n        const printContents = document.getElementById(printSectionId);\n        if (!printContents)\n            return null;\n        const inputEls = printContents.getElementsByTagName('input');\n        const selectEls = printContents.getElementsByTagName('select');\n        const textAreaEls = printContents.getElementsByTagName('textarea');\n        const canvasEls = printContents.getElementsByTagName('canvas');\n        this.updateInputDefaults(inputEls);\n        this.updateSelectDefaults(selectEls);\n        this.updateTextAreaDefaults(textAreaEls);\n        this.updateCanvasToImage(canvasEls);\n        return printContents.innerHTML;\n    }\n    /**\n     * Retrieves the HTML content of elements with the specified tag.\n     *\n     * @param {keyof HTMLElementTagNameMap} tag - HTML tag name.\n     * @returns {string} - Concatenated outerHTML of elements with the specified tag.\n     * @private\n     */\n    getElementTag(tag) {\n        const html = [];\n        const elements = document.getElementsByTagName(tag);\n        for (let index = 0; index < elements.length; index++) {\n            html.push(elements[index].outerHTML);\n        }\n        return html.join('\\r\\n');\n    }\n    //#endregion\n    /**\n     * Prints the specified content using the provided print options.\n     *\n     * @param {PrintOptions} printOptions - Options for printing.\n     * @public\n     */\n    print(printOptions) {\n        let styles = '', links = '', popOut = 'top=0,left=0,height=auto,width=auto';\n        const baseTag = this.getElementTag('base');\n        if (printOptions.useExistingCss) {\n            styles = this.getElementTag('style');\n            links = this.getElementTag('link');\n        }\n        // If the openNewTab option is set to true, then set the popOut option to an empty string. \n        // This will cause the print dialog to open in a new tab.\n        if (printOptions.openNewTab) {\n            popOut = '';\n        }\n        const printContents = this.getHtmlContents(printOptions.printSectionId);\n        if (!printContents) {\n            // Handle the case where the specified print section is not found.\n            console.error(`Print section with id ${printOptions.printSectionId} not found.`);\n            return;\n        }\n        const popupWin = window.open(\"\", \"_blank\", popOut);\n        if (!popupWin) {\n            // the popup window could not be opened.\n            console.error('Could not open print window.');\n            return;\n        }\n        popupWin.document.open();\n        popupWin.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${printOptions.printTitle ? printOptions.printTitle : \"\"}</title>\r\n              ${baseTag}\r\n              ${this.returnStyleValues()}\r\n              ${this.returnStyleSheetLinkTags()}\r\n              ${styles}\r\n              ${links}\r\n            </head>\r\n            <body ${printOptions.bodyClass ? `class=\"${printOptions.bodyClass}\"` : ''}>\r\n              ${printContents}\r\n              <script defer>\r\n                function triggerPrint(event) {\r\n                  window.removeEventListener('load', triggerPrint, false);\r\n                  ${printOptions.previewOnly ? '' : `setTimeout(function() {\r\n                    closeWindow(window.print());\r\n                  }, ${printOptions.printDelay});`}\r\n                }\r\n                function closeWindow(){\r\n                  ${printOptions.closeWindow ? 'window.close();' : ''}\r\n                }\r\n                window.addEventListener('load', triggerPrint, false);\r\n              </script>\r\n            </body>\r\n          </html>`);\n        popupWin.document.close();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: PrintBase, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: PrintBase, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: PrintBase, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\n/**\n * Service for handling printing functionality in Angular applications.\n * Extends the base printing class (PrintBase).\n *\n * @export\n * @class NgxPrintService\n * @extends {PrintBase}\n */\nclass NgxPrintService extends PrintBase {\n    /**\n     * Initiates the printing process using the provided print options.\n     *\n     * @param {PrintOptions} printOptions - Options for configuring the printing process.\n     * @memberof NgxPrintService\n     * @returns {void}\n     */\n    print(printOptions) {\n        // Call the print method in the parent class\n        super.print(printOptions);\n    }\n    /**\n     * Sets the print style for the printing process.\n     *\n     * @param {{ [key: string]: { [key: string]: string } }} values - A dictionary representing the print styles.\n     * @memberof NgxPrintService\n     * @setter\n     */\n    set printStyle(values) {\n        super.setPrintStyle(values);\n    }\n    /**\n     * Sets the stylesheet file for the printing process.\n     *\n     * @param {string} cssList - A string representing the path to the stylesheet file.\n     * @memberof NgxPrintService\n     * @setter\n     */\n    set styleSheetFile(cssList) {\n        super.setStyleSheetFile(cssList);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintService, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintService, providedIn: \"root\" });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: \"root\",\n                }]\n        }] });\n\nclass PrintOptions {\n    printSectionId = null;\n    printTitle = null;\n    useExistingCss = false;\n    bodyClass = '';\n    openNewTab = false;\n    previewOnly = false;\n    closeWindow = true;\n    printDelay = 0;\n    constructor(options) {\n        if (options) {\n            Object.assign(this, options);\n        }\n    }\n}\n\nclass NgxPrintDirective extends PrintBase {\n    printOptions = new PrintOptions();\n    /**\n     * Prevents the print dialog from opening on the window\n     *\n     * @memberof NgxPrintDirective\n     */\n    set previewOnly(value) {\n        this.printOptions = { ...this.printOptions, previewOnly: value };\n    }\n    /**\n     *\n     *\n     * @memberof NgxPrintDirective\n     */\n    set printSectionId(value) {\n        this.printOptions = { ...this.printOptions, printSectionId: value };\n    }\n    /**\n     *\n     *\n     * @memberof NgxPrintDirective\n     */\n    set printTitle(value) {\n        this.printOptions = { ...this.printOptions, printTitle: value };\n    }\n    /**\n     *\n     *\n     * @memberof NgxPrintDirective\n     */\n    set useExistingCss(value) {\n        this.printOptions = { ...this.printOptions, useExistingCss: value };\n    }\n    /**\n     * A delay in milliseconds to force the print dialog to wait before opened. Default: 0\n     *\n     * @memberof NgxPrintDirective\n     */\n    set printDelay(value) {\n        this.printOptions = { ...this.printOptions, printDelay: value };\n    }\n    /**\n     * Whether to close the window after print() returns.\n     *\n     */\n    set closeWindow(value) {\n        this.printOptions = { ...this.printOptions, closeWindow: value };\n    }\n    /**\n     * Class attribute to apply to the body element.\n     *\n     */\n    set bodyClass(value) {\n        this.printOptions = { ...this.printOptions, bodyClass: value };\n    }\n    /**\n     * Whether to open a new window or default to new window.\n     *\n     */\n    set openNewTab(value) {\n        this.printOptions = { ...this.printOptions, openNewTab: value };\n    }\n    /**\n     *\n     *\n     * @memberof NgxPrintDirective\n     */\n    set printStyle(values) {\n        super.setPrintStyle(values);\n    }\n    /**\n     * @memberof NgxPrintDirective\n     * @param cssList\n     */\n    set styleSheetFile(cssList) {\n        super.setStyleSheetFile(cssList);\n    }\n    /**\n     *\n     *\n     * @memberof NgxPrintDirective\n     */\n    print() {\n        super.print(this.printOptions);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.4\", type: NgxPrintDirective, isStandalone: true, selector: \"button[ngxPrint]\", inputs: { previewOnly: \"previewOnly\", printSectionId: \"printSectionId\", printTitle: \"printTitle\", useExistingCss: \"useExistingCss\", printDelay: \"printDelay\", closeWindow: \"closeWindow\", bodyClass: \"bodyClass\", openNewTab: \"openNewTab\", printStyle: \"printStyle\", styleSheetFile: \"styleSheetFile\" }, host: { listeners: { \"click\": \"print()\" } }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: \"button[ngxPrint]\",\n                    standalone: true\n                }]\n        }], propDecorators: { previewOnly: [{\n                type: Input\n            }], printSectionId: [{\n                type: Input\n            }], printTitle: [{\n                type: Input\n            }], useExistingCss: [{\n                type: Input\n            }], printDelay: [{\n                type: Input\n            }], closeWindow: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }], openNewTab: [{\n                type: Input\n            }], printStyle: [{\n                type: Input\n            }], styleSheetFile: [{\n                type: Input\n            }], print: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n\nclass NgxPrintModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintModule, imports: [NgxPrintDirective], exports: [NgxPrintDirective] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.4\", ngImport: i0, type: NgxPrintModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NgxPrintDirective],\n                    exports: [NgxPrintDirective]\n                }]\n        }] });\n\n/*\n * Public API Surface of ngx-print\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgxPrintDirective, NgxPrintModule, NgxPrintService, PrintOptions };\n"], "names": ["RouterModule", "MerchantNavbarComponent", "CanteenComponent", "LabelPrintComponent", "IosLabelsPrintingComponent", "NoticeBoardComponent", "AnnouncementsComponent", "ArticlesComponent", "ListAccountComponent", "UserDetailsPageComponent", "MenuEditorGuardService", "ListCanteensResolver", "UserDetailsResolver", "SalesReportGuardService", "eventManagementRoutes", "routes", "path", "component", "children", "pathMatch", "redirectTo", "resolve", "canteens", "loadChildren", "then", "m", "ManageOrderModule", "CanteenSettingsModule", "ReportsModule", "user", "MenuEditorModule", "canActivateChild", "merchants", "PosModule", "CanteenRoutingModule", "imports", "<PERSON><PERSON><PERSON><PERSON>", "i1", "exports", "CommonModule", "DatePipe", "ReactiveFormsModule", "FormsModule", "NgxPrintModule", "MatButtonModule", "MatCheckboxModule", "MatNativeDateModule", "MatDatepickerModule", "MatExpansionModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatPaginatorModule", "MatRadioModule", "MatSelectModule", "MatSortModule", "MatTableModule", "MatTooltipModule", "MatMenuModule", "AccountModule", "SharedModule", "SharedToolsModule", "SchoolsFormModule", "SchoolsButtonModule", "BlockPrintComponent", "CanteenOrderFilterComponent", "CanteenOrderSchoolFilterComponent", "CanteenOrderTypeFilterComponent", "CanteenOrderCategoryFilterComponent", "A4PrintFormComponent", "NoticeHeaderComponent", "NoticeTableComponent", "UniformOrderStatusPickerComponent", "SelectedOrderComponent", "LabelTemplateComponent", "LabelTemplateIosComponent", "CanteenModule", "declarations", "EventEmitter", "FormGroup", "FormControl", "Validators", "A4PrintSettings", "A4PrintPositions", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "form", "ɵɵadvance", "printSettings", "<PERSON><PERSON><PERSON>", "Custom", "constructor", "canteenService", "generate", "updateCustomValue", "sliceLabels", "updateDisplaySchool", "ngOnInit", "_createForm", "settings", "emit", "savedSetting", "GetPrintingSetting", "schoolNameSetting", "GetSchoolNamesOnA4Labels", "line", "required", "column", "displaySchoolName", "valueChanges", "subscribe", "val", "generateLabels", "Boolean", "SetPrintingSetting", "setSchoolNamesOnA4Labels", "data", "Line", "value", "Column", "get", "ɵɵdirectiveInject", "CanteenService", "selectors", "outputs", "decls", "vars", "consts", "template", "A4PrintFormComponent_Template", "rf", "ctx", "ɵɵtemplate", "A4PrintFormComponent_form_1_Template", "Notice", "NoticeType", "ConvertToUniversalDateFormat", "ɵɵtextInterpolate", "ctx_r2", "selectedNotice", "ValidationDescription", "_location", "spinnerService", "noticeService", "userService", "dialog", "date", "Date", "currentNoticeType", "Announcement", "CreateForm", "GoBackClick", "back", "openEditForm", "announcement", "showNoticeForm", "filteredAnnouncements", "noticeData", "filter", "IsActive", "formDate", "EndDate", "title", "Title", "<PERSON><PERSON><PERSON><PERSON>", "description", "Description", "endDate", "isActive", "isAnnouncementActive", "newAnnouncment", "NoticeId", "AddNotice", "IsSubmitButtonActive", "valid", "Location", "i2", "SpinnerService", "NoticeService", "UserService", "i3", "MatDialog", "features", "ɵɵInheritDefinitionFeature", "AnnouncementsComponent_Template", "ɵɵlistener", "AnnouncementsComponent_Template_nav_back_button_navBack_2_listener", "AnnouncementsComponent_Template_notice_header_schoolChanged_4_listener", "$event", "OnSchoolSelect", "AnnouncementsComponent_Template_notice_header_merchantChanged_4_listener", "OnMerchantChanged", "AnnouncementsComponent_Template_notice_header_openForm_4_listener", "AnnouncementsComponent_Template_notice_table_selectRow_7_listener", "AnnouncementsComponent_Template_a_click_11_listener", "hideForm", "ɵɵelement", "AnnouncementsComponent_div_36_Template", "AnnouncementsComponent_Template_form_buttons_saveEvent_37_listener", "saveNotice", "AnnouncementsComponent_Template_form_buttons_cancelEvent_37_listener", "AnnouncementsComponent_Template_form_buttons_deleteEvent_37_listener", "deleteNoticeCheck", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "_r0", "length", "_r1", "Status", "NoticeStatusEnum", "Refused", "showDeleteButton", "ctx_r1", "displayedColumns", "Article", "article", "bodyFill", "autoGrowTextZone", "e", "target", "style", "height", "scrollHeight", "ArticlesComponent_Template", "ArticlesComponent_Template_nav_back_button_navBack_2_listener", "ArticlesComponent_Template_notice_header_schoolChanged_3_listener", "ArticlesComponent_Template_notice_header_merchantChanged_3_listener", "ArticlesComponent_Template_notice_header_openForm_3_listener", "ArticlesComponent_Template_notice_table_selectRow_6_listener", "ArticlesComponent_Template_a_click_11_listener", "ArticlesComponent_div_26_Template", "ArticlesComponent_Template_form_buttons_saveEvent_27_listener", "ArticlesComponent_Template_form_buttons_cancelEvent_27_listener", "ArticlesComponent_Template_form_buttons_deleteEvent_27_listener", "A4PageLabel", "BaseComponent", "LabelV2", "listLabels", "BlockPrintComponent_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "ClickTabletPrint", "ctx_r6", "custom", "ctx_r7", "label_r9", "BlockPrintComponent_div_9_div_1_Template", "BlockPrintComponent_div_9_div_2_Template", "page_r5", "<PERSON><PERSON><PERSON><PERSON>", "deviceService", "router", "_fake<PERSON><PERSON><PERSON>", "a4PageLabel", "SAFARI_BROWSER", "IsDesktop", "isDesktop", "browser", "<PERSON><PERSON><PERSON><PERSON>", "navigate", "AndroidPrinting", "printContents", "document", "getElementById", "innerHTML", "popupWin", "window", "open", "write", "_generateFakeLabels", "_SliceLabelsA4", "updateDisplaySchoolValue", "numberFakeLabels", "numberLine", "numberColumn", "index", "push", "sliceStart", "indexPage", "labelPage", "GetRemainingLabels", "slice", "DeviceDetectorService", "Router", "inputs", "BlockPrintComponent_Template", "BlockPrintComponent_Template_a4_print_form_generate_1_listener", "BlockPrintComponent_Template_a4_print_form_sliceLabels_1_listener", "BlockPrintComponent_Template_a4_print_form_updateCustomValue_1_listener", "BlockPrintComponent_Template_a4_print_form_updateDisplaySchool_1_listener", "BlockPrintComponent_button_5_Template", "BlockPrintComponent_button_6_Template", "BlockPrintComponent_div_9_Template", "CanteenOrderCategoryFilterComponent_div_9_Template_mat_checkbox_change_1_listener", "_r3", "CheckboxChanged", "cat_r1", "MenuCategoryId", "IsChecked", "CategoryName", "rowExpanded", "categoriesSelected", "selectedValueCategories", "ngOnChanges", "changes", "propName", "_prepareList", "categories", "for<PERSON>ach", "s", "savedValue", "JSON", "parse", "localStorage", "getItem", "find", "i", "_SaveCategories", "categoryId", "findIndex", "event", "schoolId", "source", "name", "checked", "splice", "setItem", "stringify", "IsAllSelected", "IsNoCategoriessSelected", "SelectAll", "Clear", "ɵɵNgOnChangesFeature", "CanteenOrderCategoryFilterComponent_Template", "CanteenOrderCategoryFilterComponent_div_9_Template", "CanteenOrderCategoryFilterComponent_Template_a_click_13_listener", "CanteenOrderCategoryFilterComponent_Template_a_click_15_listener", "ɵɵtextInterpolate2", "moment", "select", "menuCategories", "CanteenStatusEnum", "MerchantTypeEnum", "LabelPrintChoiceEnum", "MenuTypeEnum", "CanteenOrderRequest", "CanteenOrderFilterComponent_form_0_div_16_Template_canteen_order_school_filter_schoolsSelected_2_listener", "_r7", "SchoolSelectionChanged", "selectedCanteen", "Schools", "CanteenOrderFilterComponent_form_0_canteen_order_type_filter_19_Template_canteen_order_type_filter_formGroupChange_0_listener", "_r9", "ctx_r8", "filterForm", "ctx_r4", "CanteenType", "CanteenOrderFilterComponent_form_0_div_20_Template_canteen_order_category_filter_categoriesSelected_2_listener", "_r11", "ctx_r10", "CategoriesSelectionChanged", "ctx_r5", "listCategories", "CanteenOrderFilterComponent_form_0_Template_canteen_select_list_selectedChanged_3_listener", "_r13", "ctx_r12", "CanteenChanged", "CanteenOrderFilterComponent_form_0_div_5_Template", "CanteenOrderFilterComponent_form_0_div_6_Template", "CanteenOrderFilterComponent_form_0_Template_mat_icon_click_10_listener", "ctx_r14", "SearchClicked", "CanteenOrderFilterComponent_form_0_div_16_Template", "CanteenOrderFilterComponent_form_0_canteen_order_type_filter_19_Template", "CanteenOrderFilterComponent_form_0_div_20_Template", "isUniformCanteen", "showCategoriesList", "store", "debounceService", "filtersChanged", "settingsChanged", "isEventCanteen", "triggerFiltersChanged", "callDebounce", "_prepareRequest", "canteenSubscription", "pipe", "canteenFilters", "GetFilters", "ngOnDestroy", "unsubscribe", "recess", "lunch", "printed", "uniNew", "uniProcessing", "uniReady", "uniCompleted", "search", "Recess", "Lunch", "Event", "Printed", "UniNew", "UniProcessing", "UniReady", "UniCompleted", "SetFilters", "canteen", "Uniform", "labelPrintChoice", "LabelPrintChoice", "<PERSON><PERSON>", "UsePrintingApp", "IsUniformCanteen", "schools", "selectedSchools", "setValue", "Order", "selectedCategories", "request", "n", "SchoolIds", "toString", "CategoryIds", "format", "CanteenStatus", "OrderType", "Filter", "MerchantId", "CanteenId", "New", "Processing", "Ready", "Completed", "Store", "DebounceService", "CanteenOrderFilterComponent_Template", "CanteenOrderFilterComponent_form_0_Template", "CanteenOrderSchoolFilterComponent_div_9_Template_mat_checkbox_change_1_listener", "school_r1", "SchoolId", "Name", "schoolsSelected", "selectedValueSchools", "_SaveSchools", "IsNoSchoolsSelected", "CanteenOrderSchoolFilterComponent_Template", "CanteenOrderSchoolFilterComponent_div_9_Template", "CanteenOrderSchoolFilterComponent_Template_a_click_13_listener", "CanteenOrderSchoolFilterComponent_Template_a_click_15_listener", "ɵɵpipeBind1", "ɵɵelementContainerStart", "CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_1_Template", "CanteenOrderTypeFilterComponent_div_5_ng_container_1_div_2_Template", "ɵɵelementContainerEnd", "isEventMerchant", "statusTotals", "ctx_r9", "CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_3_Template", "CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_7_Template", "CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_11_Template", "CanteenOrderTypeFilterComponent_div_5_ng_template_2_span_15_Template", "CanteenOrderTypeFilterComponent_div_5_ng_container_1_Template", "CanteenOrderTypeFilterComponent_div_5_ng_template_2_Template", "ɵɵtemplateRefExtractor", "getForm", "isUniformShop", "_r2", "orderStatusService", "formGroup", "merchantType", "formTitle", "x", "activeCanteenStatuses", "setOrderStatus", "OrderStatusService", "forReports", "CanteenOrderTypeFilterComponent_Template", "CanteenOrderTypeFilterComponent_div_5_Template", "MatTableDataSource", "MatSort", "SelectionModel", "BasePaginatorComponent", "CanteenFilters", "selection", "selected", "CanteenComponent_div_12_Template_uniform_order_status_picker_clearRowSelection_1_listener", "_r21", "ctx_r20", "clearRowSelection", "CanteenComponent_div_12_Template_uniform_order_status_picker_loadTableData_1_listener", "ctx_r22", "LoadTableData", "CanteenComponent_th_20_Template_mat_checkbox_change_1_listener", "_r24", "ctx_r23", "masterToggle", "hasValue", "isAllSelected", "checkboxLabel", "CanteenComponent_td_21_Template_mat_checkbox_click_1_listener", "restoredCtx", "_r27", "row_r25", "$implicit", "ctx_r26", "toggle", "CanteenComponent_td_21_Template_mat_checkbox_change_1_listener", "ctx_r28", "isSelected", "element_r29", "OrderId", "element_r30", "element_r31", "SchoolName", "element_r32", "MenuType", "ɵɵpropertyInterpolate1", "element_r33", "StudentId", "StudentName", "element_r34", "ClassName", "ctx_r16", "ctx_r17", "ɵɵpipeBind2", "element_r35", "OrderDate", "CanteenComponent_tr_44_Template_tr_click_0_listener", "_r38", "row_r36", "ctx_r37", "RowClick", "ctx_r19", "isRowSelected", "uniformColumns", "orderAPIService", "printingApiService", "dataSource", "listOrders", "itemsSelectedOrder", "canteenListVisible", "listSchools", "toDate", "subscription", "FiltersChanged", "ordersRequest", "clear", "<PERSON><PERSON><PERSON><PERSON>", "getOrdersRequest", "getAll", "NumberRows", "listfilters", "PageIndex", "SortBy", "SortDirection", "GetAll", "undefined", "start", "GetOrdersBySchoolAndMerchantAPI", "next", "response", "RefreshTable", "Orders", "stop", "error", "handleErrorFromService", "orders", "totalRows", "TotalRows", "newStatus", "statusForm", "SettingsChanged", "usePrintingApp", "GetItemOptions", "item", "optionsText", "Options", "opt", "SelectedOptionList", "selectedOpt", "SelectedOptions", "OptionName", "ShowButtonPrintLabels", "EnableMarkAsFulfilledButton", "IsFulFilled", "numSelected", "numRows", "row", "Items", "MenuItemId", "PageChange", "basePageChange", "SortChange", "active", "direction", "showSelectedOrder", "PrintAllLabels", "printAllOrders", "printAllItems", "SetOrdersToPrintAPI", "guid", "handlePrintResponse", "SetItemsToPrintAPI", "PrintSelectedOrder", "printOneOrderByItem", "printOneOrderByOrder", "SetOrderToPrintByOrderIdAPI", "itemId", "SetItemToPrintByItemIdAPI", "_triggerThermalPrinter", "_triggerA4Printer", "canteenPrinter", "href", "click", "SetPrintingGuid", "OrderApiService", "PrintingApiService", "viewQuery", "CanteenComponent_Query", "CanteenComponent_Template_canteen_order_filter_filtersChanged_1_listener", "CanteenComponent_Template_canteen_order_filter_settingsChanged_1_listener", "CanteenComponent_h4_10_Template", "CanteenComponent_div_12_Template", "CanteenComponent_Template_basic_button_onPress_13_listener", "CanteenComponent_Template_table_matSortChange_18_listener", "CanteenComponent_th_20_Template", "CanteenComponent_td_21_Template", "CanteenComponent_th_23_Template", "CanteenComponent_td_24_Template", "CanteenComponent_th_26_Template", "CanteenComponent_td_27_Template", "CanteenComponent_th_29_Template", "CanteenComponent_td_30_Template", "CanteenComponent_th_32_Template", "CanteenComponent_td_33_Template", "CanteenComponent_th_35_Template", "CanteenComponent_td_36_Template", "CanteenComponent_th_38_Template", "CanteenComponent_td_39_Template", "CanteenComponent_th_41_Template", "CanteenComponent_td_42_Template", "CanteenComponent_tr_43_Template", "CanteenComponent_tr_44_Template", "CanteenComponent_Template_mat_paginator_page_45_listener", "CanteenComponent_Template_selected_order_printOrder_49_listener", "ɵɵpureFunction0", "_c1", "label_r5", "IosLabelsPrintingComponent_div_1_div_1_Template", "IosLabelsPrintingComponent_div_1_div_2_Template", "page_r1", "route", "pagesLabels", "snapshot", "params", "GetLabels", "ActivatedRoute", "IosLabelsPrintingComponent_Template", "IosLabelsPrintingComponent_div_1_Template", "GetPrintingGuid", "GetOrdersToPrintByGuidAPI", "Labels", "LabelPrintComponent_Template", "LabelPrintComponent_ng_container_2_Template", "LabelPrintComponent_ng_container_3_Template", "GetPrintValue", "label", "item_r4", "LabelTemplateIosComponent_ul_6_li_1_Template", "printField", "pos", "Positions", "Value", "ShowAllergies", "LabelTemplateIosComponent_Template", "LabelTemplateIosComponent_div_1_Template", "LabelTemplateIosComponent_div_5_Template", "LabelTemplateIosComponent_ul_6_Template", "LabelNumber", "LabelTemplateComponent_ul_6_li_1_Template", "LabelTemplateComponent_Template", "LabelTemplateComponent_div_1_Template", "LabelTemplateComponent_div_5_Template", "LabelTemplateComponent_ul_6_Template", "NoticeBoardComponent_Template", "schoolChanged", "merchantChanged", "openForm", "OnMerchantChange", "openNoticeForm", "NoticeHeaderComponent_Template", "NoticeHeaderComponent_Template_merchant_school_picker_schoolChanged_4_listener", "NoticeHeaderComponent_Template_merchant_school_picker_merchantChanged_4_listener", "NoticeHeaderComponent_Template_a_click_8_listener", "element_r11", "element_r12", "NoticeTableComponent_table_0_td_9_div_1_Template", "NoticeTableComponent_table_0_td_9_img_2_Template", "element_r13", "NoticeTableComponent_table_0_td_12_span_1_Template", "NoticeTableComponent_table_0_td_12_span_2_Template", "NoticeTableComponent_table_0_td_12_span_3_Template", "element_r16", "WaitingValidation", "Validated", "NoticeTableComponent_table_0_tr_14_Template_tr_click_0_listener", "_r22", "row_r20", "ctx_r21", "noticeRowClick", "NoticeTableComponent_table_0_th_2_Template", "NoticeTableComponent_table_0_td_3_Template", "NoticeTableComponent_table_0_th_5_Template", "NoticeTableComponent_table_0_td_6_Template", "NoticeTableComponent_table_0_th_8_Template", "NoticeTableComponent_table_0_td_9_Template", "NoticeTableComponent_table_0_th_11_Template", "NoticeTableComponent_table_0_td_12_Template", "NoticeTableComponent_table_0_tr_13_Template", "NoticeTableComponent_table_0_tr_14_Template", "tableData", "selectRow", "type", "NoticeTableComponent_Template", "NoticeTableComponent_table_0_Template", "SelectedOrderComponent_basic_button_4_Template_basic_button_onPress_0_listener", "printButtonPressed", "order", "Allergies", "ɵɵtextInterpolate3", "item_r7", "Quantity", "LocalRunNumber", "SelectedOrderComponent_div_5_span_7_Template", "SelectedOrderComponent_div_5_li_11_Template", "SelectedOrderComponent_div_5_div_12_Template", "printOrder", "showOrder", "SelectedOrderComponent_Template", "SelectedOrderComponent_basic_button_4_Template", "SelectedOrderComponent_div_5_Template", "DialogResultComponent", "ResultDialogData", "UniformOrderStatusPickerComponent_button_6_Template_button_click_0_listener", "status_r2", "showDialog", "loadTableData", "completedOrdersSelected", "showWarningDialog", "showUpdateStatusDialog", "selectedOrders", "some", "getWarningDialogData", "width", "disableClose", "orderCountText", "getUpdateDialogData", "dialogRef", "afterClosed", "cancelResult", "updateOrderStatus", "selectedOrderIds", "map", "el", "UpdateListCanteenStatusOrder", "setTimeout", "TitleLine1", "TextLine2", "ConfirmButton", "orderCount", "getDialogMessage", "CancelButton", "UniformOrderStatusPickerComponent_Template", "UniformOrderStatusPickerComponent_button_6_Template", "of", "switchMap", "canLoad", "IsMenuEditorAvailable", "ɵɵinject", "factory", "ɵfac", "IsSaleReportsAvailable", "NavigationStart", "Roles", "canteenStateSelector", "MatToolbarModule", "data_r4", "Link", "data_r5", "MerchantNavbarComponent_mat_toolbar_0_button_6_Template", "MerchantNavbarComponent_mat_toolbar_0_li_8_Template", "navDataToShow", "brazeTimerService", "isMenuEditorRole", "isEventManegementRole", "refreshCount", "navDataOptions", "routeName", "url", "initializeFromRoute", "IdentifyUser", "state", "IsEventManagementAvailable", "generateNavDataToShow", "events", "setUpBrazeInAppMessageTimer", "Canteen", "queryParams", "viewParam", "viewType", "navOption", "unsubscribeFromBrazeTimer", "SignOut", "logout", "i4", "BrazeTimerService", "standalone", "ɵɵStandaloneFeature", "MerchantNavbarComponent_Template", "MerchantNavbarComponent_mat_toolbar_0_Template", "i5", "MatToolbar", "i6", "MatIconButton", "i7", "MatMenu", "MatMenuItem", "MatMenuTrigger", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "MatIcon", "RouterOutlet", "RouterLink", "RouterLinkActive", "styles", "Injectable", "Directive", "Input", "HostListener", "NgModule", "PrintBase", "_printStyle", "_styleSheetFile", "setPrintStyle", "values", "key", "hasOwnProperty", "replace", "returnStyleV<PERSON>ues", "join", "returnStyleSheetLinkTags", "setStyleSheetFile", "cssList", "linkTagFn", "cssFileName", "indexOf", "valueArr", "split", "updateInputDefaults", "elements", "element", "updateSelectDefaults", "selectedIdx", "selectedIndex", "selectedOption", "options", "defaultSelected", "updateTextAreaDefaults", "canvasToImageHtml", "dataUrl", "toDataURL", "updateCanvasToImage", "insertAdjacentHTML", "remove", "getHtmlContents", "printSectionId", "inputEls", "getElementsByTagName", "selectEls", "textAreaEls", "canvasEls", "getElementTag", "tag", "html", "outerHTML", "print", "printOptions", "links", "popOut", "baseTag", "useExistingCss", "openNewTab", "console", "printTitle", "bodyClass", "previewOnly", "printDelay", "closeWindow", "close", "PrintBase_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "NgxPrintService", "printStyle", "styleSheetFile", "ɵNgxPrintService_BaseFactory", "NgxPrintService_Factory", "ɵɵgetInheritedFactory", "PrintOptions", "Object", "assign", "NgxPrintDirective", "ɵNgxPrintDirective_BaseFactory", "NgxPrintDirective_Factory", "ɵdir", "ɵɵdefineDirective", "hostBindings", "NgxPrintDirective_HostBindings", "NgxPrintDirective_click_HostBindingHandler", "selector", "NgxPrintModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sourceRoot": "webpack:///", "x_google_ignoreList": [43]}