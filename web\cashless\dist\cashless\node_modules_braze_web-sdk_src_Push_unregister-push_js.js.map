{"version": 3, "file": "node_modules_braze_web-sdk_src_Push_unregister-push_js.js", "mappings": ";;;;;;;;;;;;;;;AAA8C;AACH;AACpC,SAASE,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAIJ,mEAAC,CAACK,EAAE,CAAC,CAAC,EAAE,OAAOJ,gEAAE,CAACK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACJ,CAAC,EAAEC,CAAC,CAAC;AAC7C", "sources": ["./node_modules/@braze/web-sdk/src/Push/unregister-push.js"], "sourcesContent": ["import e from \"../managers/braze-instance.js\";\nimport na from \"./push-manager-factory.js\";\nexport function unregisterPush(r, n) {\n  if (e.rr()) return na.m().unsubscribe(r, n);\n}\n"], "names": ["e", "na", "unregisterPush", "r", "n", "rr", "m", "unsubscribe"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}