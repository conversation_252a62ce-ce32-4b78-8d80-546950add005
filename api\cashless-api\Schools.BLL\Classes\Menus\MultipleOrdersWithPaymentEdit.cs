﻿using System.Collections.Generic;
using Schools.BLL.Classes.Payments;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes
{
    public class MultipleOrdersWithPaymentEdit
    {
        [JsonProperty(PropertyName = "Orders")]
        public MultipleOrders Orders { get; set; }

        [JsonProperty(PropertyName = "ExistingOrders")]
        public Dictionary<long, Order> ExistingOrders { get; set; }

        [JsonProperty(PropertyName = "Payment")]
        public PaymentRequest Payment { get; set; }
    }
}
