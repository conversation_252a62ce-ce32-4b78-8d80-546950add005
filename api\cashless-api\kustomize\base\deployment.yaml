apiVersion: apps/v1
kind: Deployment
metadata:
  name: schools-api
  labels:
    tags.datadoghq.com/service: schools-api
spec:
  replicas: 1
  revisionHistoryLimit: 5
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 1%
    type: RollingUpdate 
  template:
    metadata:
      labels:
        tags.datadoghq.com/service: schools-api
    spec:
      terminationGracePeriodSeconds: 117  # pre stop (30) + graceful timeout (85) + buffer
      # At present we only use one node type, this is to ensure where possible that schools-api is spread evenly across nodes and does not result in build up on one particular node. ScheduleAnyway is so that if it can't be even, it will still deploy.
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: schools-api
      serviceAccountName: schools-api
      containers:
        - name: schools-api
          lifecycle:
            preStop:
              exec:
                command:
                  - sleep
                  - "30"  # The target type is IP in prod right now. The deregistration delay on the ALB is 45s.
          image: "************.dkr.ecr.ap-southeast-2.amazonaws.com/schools/schools-api"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          startupProbe:
            httpGet:
              path: /health-check
              port: http
            failureThreshold: 60
            periodSeconds: 2
          livenessProbe:
            httpGet:
              path: /health-check
              port: http
            periodSeconds: 10
            timeoutSeconds: 2
          readinessProbe:
            httpGet:
              path: /health-check
              port: http
            periodSeconds: 2
            timeoutSeconds: 2
          resources:
            limits:
              memory: 768Mi
            requests:
              cpu: 0.15
              memory: 128Mi
          envFrom:
            - configMapRef:
                name: schools-api-config