﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Filters;
using Schools.BLL.Classes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationController : Controller
    {
        private readonly INotificationService notificationService;

        public NotificationController(INotificationService notificationService)
        {
            this.notificationService = notificationService;
        }

        /// <summary>
        /// This is used by the cashless-process-order logic app. 
        /// 
        /// Needs anonymous access, but we will check if the request
        /// contains the required cashless-api-secret header.
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("PlaceOrderNotification/{orderStatus}")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [Obsolete]
        public async Task<IActionResult> PlaceOrderNotification([FromBody] List<OrderUpsertResponse> notificationRequest, int orderStatus)
        {
            await this.notificationService.SendNotification(notificationRequest, orderStatus);

            return new OkResult();
        }
    }
}
