﻿using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes;
using Schools.DAL.Entities;
using Schools.DAL.QueryResults;

namespace Schools.BLL.Assemblers;

public class MerchantAssembler
{
    public MerchantAssembler()
    {
    }

    public MerchantDto Convert_CanteenEntity_To_MerchantDto(CanteenEntity request)
    {
        MerchantDto dto = new()
        {
            MerchantId = request.CanteenId,
            MerchantName = request.CanteenName,
            FriendlyName = request.FriendlyName,
            MerchantType = request.CanteenType,
            ExternalUserId = request.ExternalUserId,
            SchoolId = request.SchoolId,
            MerchantFee = request.MerchantFee,
            MerchantFeeStartDate = request.MerchantFeeStartDate,
            ParentFeeCoveredByMerchants = request.ParentFeeCoveredByMerchants,
            IsActive = request.IsActive
        };

        return dto;
    }

    public List<MerchantDto> Convert_ListCanteenEntity_To_ListMerchantDto(IEnumerable<CanteenEntity> request)
    {
        List<MerchantDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(this.Convert_CanteenEntity_To_MerchantDto(e));
            }
        }

        return dto;
    }

    public List<CanteenWithRightsResponce> Convert_List_CanteenPermissionsResult_To_List_CanteenWithRightsResponce(List<CanteenPermissionsResult> queryResult)
    {
        List<CanteenWithRightsResponce> dto = new();

        foreach (var res in queryResult)
        {
            var merchant = dto.Find(m => m.CanteenId == res.CanteenId);

            if (merchant == null)
            {
                CanteenWithRightsResponce newMerchant = new()
                {
                    CanteenId = res.CanteenId,
                    CanteenName = res.CanteenName,
                    CanteenType = res.CanteenType,
                    FriendlyName = res.FriendlyName,
                    IsAdmin = res.IsAdmin,
                    IsMenuEditorAvailable = res.IsMenuEditorAvailable,
                    IsOrdersNotPrintedReportsAvailable = res.IsOrdersNotPrintedReportsAvailable,
                    IsSaleReportsAvailable = res.IsSaleReportsAvailable,
                    IsEventManagementAvailable = res.IsEventManagementAvailable,
                    IsEditEventManagementAvailable = res.IsEditEventManagementAvailable,
                    Schools =
                    [
                        this.NewCanteenSchool(res)
                    ]
                };

                dto.Add(newMerchant);
            }
            else
            {
                merchant.Schools.Add(this.NewCanteenSchool(res));
            }
        }

        return dto;
    }

    private CanteenSchool NewCanteenSchool(CanteenPermissionsResult res)
    {
        return new CanteenSchool()
        {
            Name = res.Name,
            LabelFormat = res.LabelFormat,
            LabelPrintChoice = res.LabelPrintChoice,
            LabelTypeId = res.LabelTypeId,
            SchoolId = res.SchoolId,
            StateId = res.StateId,
            UsePrintingApp = res.UsePrintingApp
        };
    }
}
