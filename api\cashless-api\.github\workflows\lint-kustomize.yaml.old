# Dosen't work with CRDS, so breaks with isto VS/gw

name: <PERSON><PERSON>

on:
  push:
    branches:
      - '**'
    paths:
      - 'kustomize/**'
jobs:
  lint:
    runs-on: prod-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: yokawasa/action-setup-kube-tools@v0.9.3
        with:
          setup-tools: |
            kubeconform
            kustomize
          kubeconform: '0.6.2'
          kustomize: '5.0.2'

      - name: Lint Projects
        run: |
          set -e
          cd kustomize
          kustomize build environments/test -o ./temp.yaml
          kubeconform -verbose -strict ./temp.yaml
          kustomize build environments/prod  -o ./temp.yaml
          kubeconform -verbose -strict ./temp.yaml
