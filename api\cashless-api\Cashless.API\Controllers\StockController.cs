﻿using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Filters;
using Schools.BLL.Classes.Stocks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Enums;


namespace Cashless.APIs.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockController : ControllerBase
    {
        private readonly ITelemetryService telemetryService;
        private readonly IUnitOfWork unitOfWork;
        private readonly IOrderService orderService;
        private readonly IStocksService stocksService;
        private readonly IXeroService xeroService;
        private readonly ILogger<StockController> logger;

        public StockController(ITelemetryService telemetryService, IUnitOfWork unitOfWork, IOrderService orderService, IStocksService stocksService,
                                IXeroService xeroService, ILogger<StockController> logger)
        {
            this.telemetryService = telemetryService;
            this.unitOfWork = unitOfWork;
            this.orderService = orderService;
            this.stocksService = stocksService;
            this.xeroService = xeroService;
            this.logger = logger;
        }

        /// <summary>
        /// This is used by the cashless-process-order logic app. 
        /// 
        /// Needs anonymous access, but we will check if the request
        /// contains the required cashless-api-secret header.
        /// </summary>
        [AllowAnonymous]
        [TypeFilter(typeof(CheckApiSecretHeaderActionFilter))]
        [Route("OrderUpdateStocks/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> OrderUpdateStocks(int orderId)
        {
            Order order = await this.orderService.GetOrderById(orderId);

            await this.stocksService.OrderUpdateStocks(order);

            return new OkResult();
        }

        [Route("GetRemainingQuantityStock")]
        [Route("GetRemainingQuantityStockMobile")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
        public async Task<IActionResult> GetRemainingQuantityStock([FromBody] RemainingStockRequest request)
        {
            // to do: verify access to stock?
            RemainingStockResponse response = new()
            {
                RemainingQuantity = await this.stocksService.RemainingStockAvailable(request)
            };

            return new OkObjectResult(response);
        }

        [Route("{stockId}")]
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> ArchiveStock(long stockId)
        {
            await this.stocksService.ArchiveStock(stockId);

            return new OkResult();
        }
    }
}
