"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["node_modules_braze_web-sdk_src_Core_log-purchase_js"],{

/***/ 35607:
/*!**************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Core/log-purchase.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logPurchase: () => (/* binding */ logPurchase)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/constants.js */ 12238);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _triggers_models_trigger_events_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../triggers/models/trigger-events.js */ 50646);
/* harmony import */ var _triggers_triggers_provider_factory_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../triggers/triggers-provider-factory.js */ 79001);
/* harmony import */ var _util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/validation-utils.js */ 27607);
/* harmony import */ var _common_event_logger_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../common/event-logger.js */ 86860);







function logPurchase(o, i, n, t, D) {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return !1;
  if (null == n && (n = "USD"), null == t && (t = 1), null == o || o.length <= 0) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logPurchase requires a non-empty productId, got "${o}", ignoring.`), !1;
  if (!(0,_util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__.validateCustomString)(o, "log purchase", "the purchase name")) return !1;
  if (null == i || isNaN(parseFloat(i.toString()))) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logPurchase requires a numeric price, got ${i}, ignoring.`), !1;
  const a = parseFloat(i.toString()).toFixed(2);
  if (null == t || isNaN(parseInt(t.toString()))) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logPurchase requires an integer quantity, got ${t}, ignoring.`), !1;
  const u = parseInt(t.toString());
  if (u < 1 || u > _common_constants_js__WEBPACK_IMPORTED_MODULE_3__.MAX_PURCHASE_QUANTITY) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logPurchase requires a quantity >1 and <${_common_constants_js__WEBPACK_IMPORTED_MODULE_3__.MAX_PURCHASE_QUANTITY}, got ${u}, ignoring.`), !1;
  n = null != n ? n.toUpperCase() : n;
  if (-1 === ["AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AUD", "AWG", "AZN", "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BRL", "BSD", "BTC", "BTN", "BWP", "BYR", "BZD", "CAD", "CDF", "CHF", "CLF", "CLP", "CNY", "COP", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF", "DKK", "DOP", "DZD", "EEK", "EGP", "ERN", "ETB", "EUR", "FJD", "FKP", "GBP", "GEL", "GGP", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG", "HUF", "IDR", "ILS", "IMP", "INR", "IQD", "IRR", "ISK", "JEP", "JMD", "JOD", "JPY", "KES", "KGS", "KHR", "KMF", "KPW", "KRW", "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LTL", "LVL", "LYD", "MAD", "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRO", "MTL", "MUR", "MVR", "MWK", "MXN", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR", "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SEK", "SGD", "SHP", "SLL", "SOS", "SRD", "STD", "SVC", "SYP", "SZL", "THB", "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX", "USD", "UYU", "UZS", "VEF", "VND", "VUV", "WST", "XAF", "XAG", "XAU", "XCD", "XDR", "XOF", "XPD", "XPF", "XPT", "YER", "ZAR", "ZMK", "ZMW", "ZWL"].indexOf(n)) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logPurchase requires a valid currencyCode, got ${n}, ignoring.`), !1;
  const [g, P] = (0,_util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__.validateCustomProperties)(D, "logPurchase", "purchaseProperties", `log purchase "${o}"`, "purchase");
  if (!g) return !1;
  const R = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].tr();
  if (R && R.Dr(o)) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Purchase "${o}" is blocklisted, ignoring.`), !1;
  const c = _common_event_logger_js__WEBPACK_IMPORTED_MODULE_4__["default"].N(_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.Pr, {
    pid: o,
    c: n,
    p: a,
    q: u,
    pr: P
  });
  if (c.O) {
    _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Logged ${u} purchase${u > 1 ? "s" : ""} of "${o}" for ${n} ${a}.`);
    for (const r of c.ve) _triggers_triggers_provider_factory_js__WEBPACK_IMPORTED_MODULE_5__.TriggersProviderFactory.er().be(_triggers_models_trigger_events_js__WEBPACK_IMPORTED_MODULE_6__["default"].Rr, [o, D], r);
  }
  return c.O;
}

/***/ })

}]);
//# sourceMappingURL=node_modules_braze_web-sdk_src_Core_log-purchase_js.js.map