{"ast": null, "code": "import { BaseService } from '../base.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class GuestPaymentService extends BaseService {\n  constructor(http) {\n    super();\n    this.http = http;\n    this.SetController('GuestPayment');\n  }\n  /**\r\n   * Process a guest payment with card details\r\n   */\n  processGuestPayment(request) {\n    this.SetAction('ProcessPayment');\n    return this.http.post(this.serviceUrl, request);\n  }\n  /**\r\n   * Process guest payment and create order in one step\r\n   */\n  processPaymentAndCreateOrder(request) {\n    this.SetAction('ProcessPaymentAndCreateOrder');\n    return this.http.post(this.serviceUrl, request);\n  }\n  /**\r\n   * Validate guest card details\r\n   */\n  validateGuestCard(request) {\n    this.SetAction('ValidateCard');\n    return this.http.post(this.serviceUrl, request);\n  }\n  /**\r\n   * Get payment status for a guest order\r\n   */\n  getPaymentStatus(orderId) {\n    this.SetAction(`PaymentStatus/${orderId}`);\n    return this.http.get(this.serviceUrl);\n  }\n  /**\r\n   * Validate guest user permissions for payment\r\n   */\n  validateGuestPermissions(guestUserId, canteenId) {\n    this.SetAction(`ValidatePermissions/${guestUserId}/${canteenId}`);\n    return this.http.get(this.serviceUrl);\n  }\n  /**\r\n   * Create guest payment request from order data\r\n   */\n  createGuestPaymentRequest(cardDetails, orderData, guestUserId, canteenId) {\n    return {\n      cardNumber: cardDetails.cardNumber.replace(/\\s/g, ''),\n      expiryMonth: parseInt(cardDetails.expiryMonth),\n      expiryYear: parseInt(cardDetails.expiryYear),\n      cvv: cardDetails.cvv,\n      cardholderName: cardDetails.cardholderName,\n      amount: orderData.totalAmount,\n      canteenId: canteenId,\n      guestUserId: guestUserId,\n      items: orderData.items.map(item => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name,\n        itemDescription: item.description\n      })),\n      orderDate: orderData.orderDate,\n      menuId: orderData.menuId,\n      menuType: orderData.menuType\n    };\n  }\n  /**\r\n   * Format card number for display\r\n   */\n  formatCardNumber(cardNumber) {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    const groups = cleanNumber.match(/.{1,4}/g);\n    return groups ? groups.join(' ') : cleanNumber;\n  }\n  /**\r\n   * Detect card type from card number\r\n   */\n  detectCardType(cardNumber) {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    if (/^4/.test(cleanNumber)) {\n      return 'Visa';\n    }\n    if (/^5[1-5]/.test(cleanNumber)) {\n      return 'Mastercard';\n    }\n    if (/^3[47]/.test(cleanNumber)) {\n      return 'American Express';\n    }\n    return 'Unknown';\n  }\n  /**\r\n   * Validate card number using Luhn algorithm\r\n   */\n  validateCardNumber(cardNumber) {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    // Check if all digits\n    if (!/^\\d+$/.test(cleanNumber)) {\n      return false;\n    }\n    // Luhn algorithm\n    let sum = 0;\n    let alternate = false;\n    for (let i = cleanNumber.length - 1; i >= 0; i--) {\n      let digit = parseInt(cleanNumber[i]);\n      if (alternate) {\n        digit *= 2;\n        if (digit > 9) {\n          digit = digit % 10 + 1;\n        }\n      }\n      sum += digit;\n      alternate = !alternate;\n    }\n    return sum % 10 === 0;\n  }\n  /**\r\n   * Validate expiry date\r\n   */\n  validateExpiryDate(month, year) {\n    const now = new Date();\n    const expiry = new Date(year, month - 1);\n    return expiry >= now;\n  }\n  /**\r\n   * Validate CVV\r\n   */\n  validateCVV(cvv) {\n    return /^[0-9]{3,4}$/.test(cvv);\n  }\n  /**\r\n   * Get available expiry months\r\n   */\n  getExpiryMonths() {\n    const months = [];\n    for (let i = 1; i <= 12; i++) {\n      const value = i.toString().padStart(2, '0');\n      const label = `${value}`;\n      months.push({\n        value,\n        label\n      });\n    }\n    return months;\n  }\n  /**\r\n   * Get available expiry years\r\n   */\n  getExpiryYears() {\n    const currentYear = new Date().getFullYear();\n    const years = [];\n    for (let i = 0; i <= 10; i++) {\n      const year = currentYear + i;\n      years.push({\n        value: year.toString(),\n        label: year.toString()\n      });\n    }\n    return years;\n  }\n  static {\n    this.ɵfac = function GuestPaymentService_Factory(t) {\n      return new (t || GuestPaymentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GuestPaymentService,\n      factory: GuestPaymentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseService", "GuestPaymentService", "constructor", "http", "SetController", "processGuestPayment", "request", "SetAction", "post", "serviceUrl", "processPaymentAndCreateOrder", "validateGuestCard", "getPaymentStatus", "orderId", "get", "validateGuestPermissions", "guestUserId", "canteenId", "createGuestPaymentRequest", "cardDetails", "orderData", "cardNumber", "replace", "expiry<PERSON><PERSON><PERSON>", "parseInt", "expiryYear", "cvv", "cardholder<PERSON><PERSON>", "amount", "totalAmount", "items", "map", "item", "menuItemId", "quantity", "price", "itemName", "name", "itemDescription", "description", "orderDate", "menuId", "menuType", "formatCardNumber", "cleanNumber", "groups", "match", "join", "detectCardType", "test", "validateCardNumber", "sum", "alternate", "i", "length", "digit", "validateExpiryDate", "month", "year", "now", "Date", "expiry", "validateCVV", "getExpiryMonths", "months", "value", "toString", "padStart", "label", "push", "getExpiryYears", "currentYear", "getFullYear", "years", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\projects\\spriggy\\git-spriggy-latest\\web\\cashless\\src\\app\\sharedServices\\guest-payment\\guest-payment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { BaseService } from '../base.service';\nimport {\n  GuestPaymentRequest,\n  GuestPaymentResponse,\n  GuestCardValidationRequest,\n  GuestCardValidationResponse\n} from '../../sharedModels/guest-payment/guest-payment.models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GuestPaymentService extends BaseService {\n\n  constructor(private http: HttpClient) {\n    super();\n    this.SetController('GuestPayment');\n  }\n\n  /**\n   * Process a guest payment with card details\n   */\n  processGuestPayment(request: GuestPaymentRequest): Observable<GuestPaymentResponse> {\n    this.SetAction('ProcessPayment');\n    return this.http.post<GuestPaymentResponse>(this.serviceUrl, request);\n  }\n\n  /**\n   * Process guest payment and create order in one step\n   */\n  processPaymentAndCreateOrder(request: GuestPaymentRequest): Observable<GuestPaymentResponse> {\n    this.SetAction('ProcessPaymentAndCreateOrder');\n    return this.http.post<GuestPaymentResponse>(this.serviceUrl, request);\n  }\n\n  /**\n   * Validate guest card details\n   */\n  validateGuestCard(request: GuestCardValidationRequest): Observable<GuestCardValidationResponse> {\n    this.SetAction('ValidateCard');\n    return this.http.post<GuestCardValidationResponse>(this.serviceUrl, request);\n  }\n\n  /**\n   * Get payment status for a guest order\n   */\n  getPaymentStatus(orderId: string): Observable<any> {\n    this.SetAction(`PaymentStatus/${orderId}`);\n    return this.http.get<any>(this.serviceUrl);\n  }\n\n  /**\n   * Validate guest user permissions for payment\n   */\n  validateGuestPermissions(guestUserId: number, canteenId: number): Observable<{ hasPermission: boolean }> {\n    this.SetAction(`ValidatePermissions/${guestUserId}/${canteenId}`);\n    return this.http.get<{ hasPermission: boolean }>(this.serviceUrl);\n  }\n\n  /**\n   * Create guest payment request from order data\n   */\n  createGuestPaymentRequest(\n    cardDetails: any,\n    orderData: any,\n    guestUserId: number,\n    canteenId: number\n  ): GuestPaymentRequest {\n    return {\n      cardNumber: cardDetails.cardNumber.replace(/\\s/g, ''), // Remove spaces\n      expiryMonth: parseInt(cardDetails.expiryMonth),\n      expiryYear: parseInt(cardDetails.expiryYear),\n      cvv: cardDetails.cvv,\n      cardholderName: cardDetails.cardholderName,\n      amount: orderData.totalAmount,\n      canteenId: canteenId,\n      guestUserId: guestUserId,\n      items: orderData.items.map((item: any) => ({\n        menuItemId: item.menuItemId,\n        quantity: item.quantity,\n        price: item.price,\n        itemName: item.name,\n        itemDescription: item.description\n      })),\n      orderDate: orderData.orderDate,\n      menuId: orderData.menuId,\n      menuType: orderData.menuType\n    };\n  }\n\n  /**\n   * Format card number for display\n   */\n  formatCardNumber(cardNumber: string): string {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    const groups = cleanNumber.match(/.{1,4}/g);\n    return groups ? groups.join(' ') : cleanNumber;\n  }\n\n  /**\n   * Detect card type from card number\n   */\n  detectCardType(cardNumber: string): string {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    \n    if (/^4/.test(cleanNumber)) {\n      return 'Visa';\n    }\n    if (/^5[1-5]/.test(cleanNumber)) {\n      return 'Mastercard';\n    }\n    if (/^3[47]/.test(cleanNumber)) {\n      return 'American Express';\n    }\n    \n    return 'Unknown';\n  }\n\n  /**\n   * Validate card number using Luhn algorithm\n   */\n  validateCardNumber(cardNumber: string): boolean {\n    const cleanNumber = cardNumber.replace(/\\s/g, '');\n    \n    // Check if all digits\n    if (!/^\\d+$/.test(cleanNumber)) {\n      return false;\n    }\n    \n    // Luhn algorithm\n    let sum = 0;\n    let alternate = false;\n    \n    for (let i = cleanNumber.length - 1; i >= 0; i--) {\n      let digit = parseInt(cleanNumber[i]);\n      \n      if (alternate) {\n        digit *= 2;\n        if (digit > 9) {\n          digit = (digit % 10) + 1;\n        }\n      }\n      \n      sum += digit;\n      alternate = !alternate;\n    }\n    \n    return sum % 10 === 0;\n  }\n\n  /**\n   * Validate expiry date\n   */\n  validateExpiryDate(month: number, year: number): boolean {\n    const now = new Date();\n    const expiry = new Date(year, month - 1);\n    return expiry >= now;\n  }\n\n  /**\n   * Validate CVV\n   */\n  validateCVV(cvv: string): boolean {\n    return /^[0-9]{3,4}$/.test(cvv);\n  }\n\n  /**\n   * Get available expiry months\n   */\n  getExpiryMonths(): { value: string; label: string }[] {\n    const months = [];\n    for (let i = 1; i <= 12; i++) {\n      const value = i.toString().padStart(2, '0');\n      const label = `${value}`;\n      months.push({ value, label });\n    }\n    return months;\n  }\n\n  /**\n   * Get available expiry years\n   */\n  getExpiryYears(): { value: string; label: string }[] {\n    const currentYear = new Date().getFullYear();\n    const years = [];\n    for (let i = 0; i <= 10; i++) {\n      const year = currentYear + i;\n      years.push({ value: year.toString(), label: year.toString() });\n    }\n    return years;\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,iBAAiB;;;AAW7C,OAAM,MAAOC,mBAAoB,SAAQD,WAAW;EAElDE,YAAoBC,IAAgB;IAClC,KAAK,EAAE;IADW,KAAAA,IAAI,GAAJA,IAAI;IAEtB,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC;EACpC;EAEA;;;EAGAC,mBAAmBA,CAACC,OAA4B;IAC9C,IAAI,CAACC,SAAS,CAAC,gBAAgB,CAAC;IAChC,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAuB,IAAI,CAACC,UAAU,EAAEH,OAAO,CAAC;EACvE;EAEA;;;EAGAI,4BAA4BA,CAACJ,OAA4B;IACvD,IAAI,CAACC,SAAS,CAAC,8BAA8B,CAAC;IAC9C,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAuB,IAAI,CAACC,UAAU,EAAEH,OAAO,CAAC;EACvE;EAEA;;;EAGAK,iBAAiBA,CAACL,OAAmC;IACnD,IAAI,CAACC,SAAS,CAAC,cAAc,CAAC;IAC9B,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAA8B,IAAI,CAACC,UAAU,EAAEH,OAAO,CAAC;EAC9E;EAEA;;;EAGAM,gBAAgBA,CAACC,OAAe;IAC9B,IAAI,CAACN,SAAS,CAAC,iBAAiBM,OAAO,EAAE,CAAC;IAC1C,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAM,IAAI,CAACL,UAAU,CAAC;EAC5C;EAEA;;;EAGAM,wBAAwBA,CAACC,WAAmB,EAAEC,SAAiB;IAC7D,IAAI,CAACV,SAAS,CAAC,uBAAuBS,WAAW,IAAIC,SAAS,EAAE,CAAC;IACjE,OAAO,IAAI,CAACd,IAAI,CAACW,GAAG,CAA6B,IAAI,CAACL,UAAU,CAAC;EACnE;EAEA;;;EAGAS,yBAAyBA,CACvBC,WAAgB,EAChBC,SAAc,EACdJ,WAAmB,EACnBC,SAAiB;IAEjB,OAAO;MACLI,UAAU,EAAEF,WAAW,CAACE,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACrDC,WAAW,EAAEC,QAAQ,CAACL,WAAW,CAACI,WAAW,CAAC;MAC9CE,UAAU,EAAED,QAAQ,CAACL,WAAW,CAACM,UAAU,CAAC;MAC5CC,GAAG,EAAEP,WAAW,CAACO,GAAG;MACpBC,cAAc,EAAER,WAAW,CAACQ,cAAc;MAC1CC,MAAM,EAAER,SAAS,CAACS,WAAW;MAC7BZ,SAAS,EAAEA,SAAS;MACpBD,WAAW,EAAEA,WAAW;MACxBc,KAAK,EAAEV,SAAS,CAACU,KAAK,CAACC,GAAG,CAAEC,IAAS,KAAM;QACzCC,UAAU,EAAED,IAAI,CAACC,UAAU;QAC3BC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;QACvBC,KAAK,EAAEH,IAAI,CAACG,KAAK;QACjBC,QAAQ,EAAEJ,IAAI,CAACK,IAAI;QACnBC,eAAe,EAAEN,IAAI,CAACO;OACvB,CAAC,CAAC;MACHC,SAAS,EAAEpB,SAAS,CAACoB,SAAS;MAC9BC,MAAM,EAAErB,SAAS,CAACqB,MAAM;MACxBC,QAAQ,EAAEtB,SAAS,CAACsB;KACrB;EACH;EAEA;;;EAGAC,gBAAgBA,CAACtB,UAAkB;IACjC,MAAMuB,WAAW,GAAGvB,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACjD,MAAMuB,MAAM,GAAGD,WAAW,CAACE,KAAK,CAAC,SAAS,CAAC;IAC3C,OAAOD,MAAM,GAAGA,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGH,WAAW;EAChD;EAEA;;;EAGAI,cAAcA,CAAC3B,UAAkB;IAC/B,MAAMuB,WAAW,GAAGvB,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAEjD,IAAI,IAAI,CAAC2B,IAAI,CAACL,WAAW,CAAC,EAAE;MAC1B,OAAO,MAAM;;IAEf,IAAI,SAAS,CAACK,IAAI,CAACL,WAAW,CAAC,EAAE;MAC/B,OAAO,YAAY;;IAErB,IAAI,QAAQ,CAACK,IAAI,CAACL,WAAW,CAAC,EAAE;MAC9B,OAAO,kBAAkB;;IAG3B,OAAO,SAAS;EAClB;EAEA;;;EAGAM,kBAAkBA,CAAC7B,UAAkB;IACnC,MAAMuB,WAAW,GAAGvB,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAEjD;IACA,IAAI,CAAC,OAAO,CAAC2B,IAAI,CAACL,WAAW,CAAC,EAAE;MAC9B,OAAO,KAAK;;IAGd;IACA,IAAIO,GAAG,GAAG,CAAC;IACX,IAAIC,SAAS,GAAG,KAAK;IAErB,KAAK,IAAIC,CAAC,GAAGT,WAAW,CAACU,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChD,IAAIE,KAAK,GAAG/B,QAAQ,CAACoB,WAAW,CAACS,CAAC,CAAC,CAAC;MAEpC,IAAID,SAAS,EAAE;QACbG,KAAK,IAAI,CAAC;QACV,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbA,KAAK,GAAIA,KAAK,GAAG,EAAE,GAAI,CAAC;;;MAI5BJ,GAAG,IAAII,KAAK;MACZH,SAAS,GAAG,CAACA,SAAS;;IAGxB,OAAOD,GAAG,GAAG,EAAE,KAAK,CAAC;EACvB;EAEA;;;EAGAK,kBAAkBA,CAACC,KAAa,EAAEC,IAAY;IAC5C,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,MAAM,GAAG,IAAID,IAAI,CAACF,IAAI,EAAED,KAAK,GAAG,CAAC,CAAC;IACxC,OAAOI,MAAM,IAAIF,GAAG;EACtB;EAEA;;;EAGAG,WAAWA,CAACpC,GAAW;IACrB,OAAO,cAAc,CAACuB,IAAI,CAACvB,GAAG,CAAC;EACjC;EAEA;;;EAGAqC,eAAeA,CAAA;IACb,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMY,KAAK,GAAGZ,CAAC,CAACa,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC3C,MAAMC,KAAK,GAAG,GAAGH,KAAK,EAAE;MACxBD,MAAM,CAACK,IAAI,CAAC;QAAEJ,KAAK;QAAEG;MAAK,CAAE,CAAC;;IAE/B,OAAOJ,MAAM;EACf;EAEA;;;EAGAM,cAAcA,CAAA;IACZ,MAAMC,WAAW,GAAG,IAAIX,IAAI,EAAE,CAACY,WAAW,EAAE;IAC5C,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMK,IAAI,GAAGa,WAAW,GAAGlB,CAAC;MAC5BoB,KAAK,CAACJ,IAAI,CAAC;QAAEJ,KAAK,EAAEP,IAAI,CAACQ,QAAQ,EAAE;QAAEE,KAAK,EAAEV,IAAI,CAACQ,QAAQ;MAAE,CAAE,CAAC;;IAEhE,OAAOO,KAAK;EACd;;;uBAlLWxE,mBAAmB,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB5E,mBAAmB;MAAA6E,OAAA,EAAnB7E,mBAAmB,CAAA8E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}