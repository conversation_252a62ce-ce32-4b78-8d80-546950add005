# Development Environment Setup

Install [Azure Functions Core tools](https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local?tabs=v4%2Cmacos%2Ccsharp%2Cportal%2Cbash#install-the-azure-functions-core-tools) version 4.x using Homebrew.

```sh
brew tap azure/functions
brew install azure-functions-core-tools@4

# if upgrading on a machine that has 2.x or 3.x installed:
brew link --overwrite azure-functions-core-tools@4
```

To check you are running version 4.x, run the following command.

```sh
func --version
```

# Create Functions Poject

Run the following command from the root directory to create a new functions project named **Schools.Orders** in line with current naming conventions. Note we are adding Docker support for now in case we need it.

```sh
func init Schools.Orders --worker-runtime dotnetIsolated --target-framework "net8.0" --docker
```

Add the new project to the overall solution.

```sh
dotnet sln add Schools.Orders/Schools.Orders.csproj
```

## Running Azure Functions In An Isolated Mode

We chose to run the functions project in an [isolated worked process run-time](https://learn.microsoft.com/en-us/azure/azure-functions/dotnet-isolated-process-guide). The difference between the in-process vs. isolated options are:

- Under the in-process options `--worker-runtime dotnet` there is tight coupling between the host process and the .NET Function because the .NET class libraries and assemblies share binding APIs & types
- Under the isolated worker option `--worker-runtime dotnetIsolated` the functions app is deployed as a console app. This gives us access to the star-up of the function app and decouples the libraries and assemblies used in our code from the assemblies used to run the Functions framework

## Departures From Template Project

Rename the **csproj** file to **Schools.Orders.csproj** in line with our file naming conventions and remove the directives that allow for:

- Implicit usings
- Implicit nullables

Implicit anything is never a good idea!

Update the generated **Program.cs** in line with the above settings. We can add a **Startup** class at some point similar to our batch or API projects.

```c#
using Microsoft.Extensions.Hosting;

namespace Schools.Orders;

public class Program
{
    /// <summary>
    /// Build and run the functions app
    /// </summary>
    public static void Main(string[] args)
    {
        var host = new HostBuilder()
                        .ConfigureFunctionsWorkerDefaults()
                        .Build();

        host.Run();
    }
}
```

## Adding Configuration

Configuration parameters are stored in **local.settings.json**. A sample file is below:

```json
{
  "IsEncrypted": false,
  "Values": {
    "FUNCTIONS_WORKER_RUNTIME": "dotnetIsolated",
    "FUNCTIONS_EXTENSION_VERSION": "~4",
    "APPINSIGHTS_INSTRUMENTATIONKEY": "<Use the Application Insights Key for the dev environment>",
    "ASPNETCORE_ENVIRONMENT": "Development",
    "AzureWebJobsStorage": "<Point this at Azure Storage endpoint in the dev environment, or at Azurite on localhost, or UseDevelopmentStorage=true>"
  },
  "ConnectionStrings": {
    "QueueConnectionString": "<Point this at Azure Service Bus in dev>",
    "AppConfig": "<Point this at SQL Server database cc_appdb in the dev environment>"
  }
}
```

## Azure Web Jobs Storage

There are a number of options when it comes to setting up a development environment.

1. Use the Azure Storage created for the Functions App in the `dev` environment. This can be fetched from the configuration settings of the [sps-schools-audit-dev](https://portal.azure.com/#@spriggy.com.au/resource/subscriptions/4c6e50a9-2c26-4dc5-b783-d59043344fa5/resourceGroups/spriggy-schools-dev/providers/Microsoft.Web/sites/sps-schools-audit-dev/configuration) functions app
2. Use the [endpoint used in Azurite](#using-docker-compose) to emulate Azure Web Storage in the section below
3. Use the `"AzureWebJobsStorage": "UseDevelopmentStorage=true"` setting which is enabled out of the box

### Add Azurite For Local Storage

Azure Functions typically need an Azure Storage account to use for cache / blob storage.

We can use the (Azurite Docker image)[https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=docker-hub] to emulate blob storage used for Azure Functions running locally.

After starting Docker Desktop, run the following command to download the Azurite Docker image.

```sh
docker pull mcr.microsoft.com/azure-storage/azurite
```

Once the image is downloaded, you can run it on port **10000**, **10001** and **10002** by running the following command.

```sh
docker run -p 10000:10000 -p 10001:10001 -p 10002:10002 mcr.microsoft.com/azure-storage/azurite &
```

### Using Docker Compose

Alternatively, we can add Azurite to the [docker-compose.yml](/docker-compose.yml) file as another service to start when `docker-compose up -d` is executed. This also contains configuration to create a storage account which we can use to connect to Azure Storage when the functions are running locally. This sets up Azure Storage with endpoint settings `"DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"`

## Adding Libraries

Using dotnet isolated worker processes essentially allows us to create standalone batch programs that are started and destroyed by Azure Functions at runtime. This allows us greater flexibility in the code we can write.

For this, we will add libraries to allow us to use [dependency injection](https://learn.microsoft.com/en-us/azure/azure-functions/dotnet-isolated-process-guide).

```sh
dotnet add package Microsoft.Azure.Functions.Worker
dotnet add package Microsoft.Azure.Functions.Worker.Sdk
dotnet add package Microsoft.Extensions.DependencyInjection
```

We need to add different [Azure Functions Extension packages](https://www.nuget.org/packages?q=Microsoft.Azure.Functions.Worker.Extensions) to interact with the Functions framework. These include:

- [Microsoft.Azure.Functions.Worker.Extensions.Abstractions](https://www.nuget.org/packages/Microsoft.Azure.Functions.Worker.Extensions.Abstractions) - Needed for logging and other utilities
- [Microsoft.Azure.Functions.Worker.Extensions.Http](https://www.nuget.org/packages/Microsoft.Azure.Functions.Worker.Extensions.Http) - Needed for HTTP Triggered functions in the [Schools.Orders.Example namespace](/Schools.Orders/Example/)
- [Microsoft.Azure.Functions.Worker.Extensions.ServiceBus](https://www.nuget.org/packages/Microsoft.Azure.Functions.Worker.Extensions.ServiceBus) - Needed to connect to Azure Service Bus in order to consume or publish messages
- [Microsoft.Azure.WebJobs.Extensions.Storage](https://www.nuget.org/packages/Microsoft.Azure.WebJobs.Extensions.Storage) - Needed for storage queues. Maybe?

## Adding Services

The Functions classes themselves must be kept as simple as possible. They are to interface with Azure at runtime and no more.

All functionality must be contained in a service layer to enable us to unit test any domain or business logic.

All services will be registered with the Dependecy Injection container which in turn is registered in the [Program.cs](/Schools.Orders/Program.cs).

Check the configuration of the [Schools.Batch project](/Schools.Batch/Schools.Batch.csproj) to see the packages imported and the associated [Program.cs](/Schools.Batch/Program.cs) for the configuration of:

- Dependency Injection
- Azure App Configuration
- Azure Application Insights

# Deploymemt

The current proof of concept in this project includes functions that respond to:

- HTTP requests
- Azure Service Bus messages

Azure Service Bus queues needed for this PoC are:

- **orders-process** - This is the initial input queue
- **order-success** - This is where messages go following successful processing
- **order-error** - This is where messages are placed on error

Please note, the above queues need to be present and accessible for the PoC to work.

## Outstanding

The following are items that need investigation or need adding.

### General

- Add error handling middleware to log all exceptions as Failures in Application Insights
  - For message or event triggered functions, include the trigger message or event
  - For HTTP triggered functions, include the HTTP request - **DONE**

### [Message Processing](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-service-bus?pivots=programming-language-csharp&tabs=isolated-process%2Cfunctionsv2%2Cextensionv3)

- Add functions that trigger off Azure Service Bus messages - **DONE**
- Bind to multiple output channels. E.g. queues, HTTP, event hub etc - **DONE**
- Check behaviour when exceptions are thrown. Expected behaviour is the trigger message is placed back on the queue - **DONE**
- Check DLQ behaviour and any message storage restrictions
- Investigate retry behaviour using retry policies
  - Control over number of rertries. This appears to be a setting on the queue
  - Control exponential backoff or similar
  - We may have to [build a custom retry handler](https://blog.kloud.com.au/2017/05/22/message-retry-patterns-in-azure-functions/) that decides based on the exception thrown
- Investigate how to peek at queues
  - Fetch message content for display in Admin Portal
- Provide ability to move messages between queues
  - Transactions (ACID) are needed for this
  - Add this API for Admin Portal to use

### [Event Processing](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-event-grid?pivots=programming-language-csharp&tabs=isolated-process%2Cextensionv2)

- Add functions that trigger off Azure Event Grid events

### [HTTP Processing](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook-trigger?pivots=programming-language-csharp&tabs=python-v2%2Cisolated-process%2Cfunctionsv2#working-with-client-identities)

- Add functions that trigger off HTTP requests - **DONE**
- Add JWT authentication
  - Authorisation via role checks
- Add common secret authentication middleware using `cashless-api-secret` headers
  - Fetch value from App Configuration
  - Refactor the core of `CheckApiSecretHeaderActionFilter` into a service in `Cashless.Common`

# Running Functions Locally

## Command Line

To build the project, run the following command

```sh
cd Schools.Orders
dotnet clean; dotnet restore; dotnet build
```

To run run the functions app, run the following command from the `Schools.Orders` directory. This requires all queues that are named in the input and output bindings of the various function classes to be configured and ready in the [`schools-sb-dev` Service Bus instance in `dev`](https://portal.azure.com/#@spriggy.com.au/resource/subscriptions/4c6e50a9-2c26-4dc5-b783-d59043344fa5/resourceGroups/spriggy-schools-dev/providers/Microsoft.ServiceBus/namespaces/schools-sb-dev/overview).

```sh
func host start
```

You should see console output that looks like this when the sample HTTP functions are included:

```sh
Azure Functions Core Tools
Core Tools Version:       4.0.5095 Commit hash: N/A  (64-bit)
Function Runtime Version: 4.16.5.20396

[2023-05-01T01:47:47.969Z] Found /Users/<USER>/Development/cashless-API/Schools.Orders/Schools.Orders.csproj. Using for user secrets file configuration.

Functions:

        Echo: [GET,POST] http://localhost:7071/api/http/echo

        Hello: [GET,POST] http://localhost:7071/api/http/hello

For detailed output, run func with --verbose flag.
[2023-05-01T01:47:48.976Z] Worker process started and initialized.
[2023-05-01T01:47:54.056Z] Host lock lease acquired by instance ID '000000000000000000000000A9560F23'.
```

### Visual Studio Code

A new launch configuration called **"Attach to .NET Functions"** has been added. This will launch the Functions app and attach the debugger.

Please note, when you stop the debugger, the Functions app will still be running. You need to terminate that separately by pressing **CTRL-C**.

### Visual Studio

**TODO**
