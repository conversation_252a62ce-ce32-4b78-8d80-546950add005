using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Assemblers;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Schools.DAL.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;
[Authorize]
[Route("api/[controller]")]
[ApiController]
public class SchoolClassController : ControllerBase
{

    private readonly ISchoolClassService schoolClassService;
    private readonly IUserService userService;
    private readonly ISchoolClassValidator schoolClassValidator;

    public SchoolClassController(ISchoolClassService schoolClassService,
    IUserService userService, ISchoolClassValidator schoolClassValidator)
    {
        this.schoolClassService = schoolClassService;
        this.userService = userService;
        this.schoolClassValidator = schoolClassValidator;
    }

    /// <summary>
    /// Get class list for a school
    /// </summary>
    [Route("GetClassList")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    [Obsolete]
    public async Task<IActionResult> GetClassList([FromBody] ClassRequest request)
    {
        return await GetClassListCore(request.SchoolId, request.ActiveOnly);
    }

    /// <summary>
    /// Get class list for a school
    /// </summary>
    [Route("{schoolId}/classes")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetClassList(int schoolId)
    {
        var activeOnly = HttpUtility.ParseQueryString(HttpContext.Request.QueryString.Value).Get("activeOnly") == true.ToString().ToLower();
        return await GetClassListCore(schoolId, activeOnly);
    }

    private async Task<IActionResult> GetClassListCore(int schoolId, bool activeOnly)
    {
        var user = await userService.GetCurrentUser();
        if (user.IsMerchant)
        {
            await this.schoolClassValidator.ValidateUserAccessToSchool(schoolId);
        }
        activeOnly = user.IsParent || activeOnly;
        ListSchoolClassResponse listSchoolClassResponse = new();

        // To Do: restrict class list to information acceptable for anyone to view

        // get classes
        List<SchoolClassEntity> classes = await this.schoolClassService.GetClassListBySchool(schoolId, activeOnly);
        listSchoolClassResponse.Classes = SchoolClassAssembler.Convert_ListClassEntity_To_ListClassDto(classes);

        return new OkObjectResult(listSchoolClassResponse);
    }

    /// <summary>
    /// Update school Class
    /// </summary>
    [Route("UpdateClass")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    [Obsolete]
    public async Task<IActionResult> UpdateClass([FromBody] SchoolClassUpdateRequest request)
    {
        return await UpdateClassCore(request.ClassId, request);
    }

    /// <summary>
    /// Update school Class
    /// </summary>
    [Route("{classId}")]
    [HttpPut]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> UpdateClass(int classId, [FromBody] SchoolClassUpdateRequest request)
    {
        return await UpdateClassCore(classId, request);
    }

    private async Task<IActionResult> UpdateClassCore(int classId, SchoolClassUpdateRequest request)
    {
        this.schoolClassValidator.ValidateClassUpdateRequest(request);

        var classToUpdate = await this.schoolClassService.GetSchoolClassById(classId);

        if (classToUpdate == null)
        {
            throw new SchoolClassNotFoundException($"Class ({classId}) not found");
        }

        await this.schoolClassValidator.ValidateUserAccessToSchool(classToUpdate.SchoolId);

        var response = await this.schoolClassService.UpdateSchoolClass(request, classToUpdate);

        return new OkObjectResult(response);
    }

    /// <summary>
    /// Create school class
    /// </summary>
    [Route("createClass")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> CreateClass([FromBody] SchoolClassCreateRequest schoolClass)
    {
        this.schoolClassValidator.ValidateClassCreateRequest(schoolClass);

        await this.schoolClassValidator.ValidateUserAccessToSchool(schoolClass.SchoolId);

        var response = await this.schoolClassService.CreateSchoolClass(schoolClass);

        return new OkObjectResult(response);
    }

    /// <summary>
    /// archive school class 
    /// </summary>
    [Route("ArchiveClass")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    [Obsolete]
    public async Task<IActionResult> ArchiveClass([FromBody] SchoolClassUpdateRequest request)
    {
        this.schoolClassValidator.ValidateClassUpdateRequest(request);

        return await ArchiveClassCore(request.ClassId);
    }

    /// <summary>
    /// archive school class 
    /// </summary>
    [Route("{classId}")]
    [HttpDelete]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    public async Task<IActionResult> ArchiveClass(int classId)
    {
        return await ArchiveClassCore(classId);
    }

    private async Task<IActionResult> ArchiveClassCore(int classId)
    {
        var classToArchive = await this.schoolClassService.GetSchoolClassById(classId);

        if (classToArchive == null)
        {
            throw new SchoolClassNotFoundException($"Class ({classId}) not found");
        }

        await this.schoolClassValidator.ValidateUserAccessToSchool(classToArchive.SchoolId);

        var response = await this.schoolClassService.ArchiveSchoolClass(classToArchive);

        //notify students that their class has been deleted 
        await this.userService.SetStudentsNeedToUpdateClass(classToArchive.ClassId);

        return new OkResult();
    }

    [Route("GetSchoolClassById")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
    [Obsolete]
    public Task<IActionResult> GetSchoolClassById([FromBody] ClassRequest request)
    {
        throw new DeprecatedApiException();
    }
}
