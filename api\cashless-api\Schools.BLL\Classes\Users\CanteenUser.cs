﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.BLL.Classes;

public class CanteenUserWithPermission : CanteenUser
{
    [JsonProperty(PropertyName = "Schools")]
    public List<SchoolIdPermission> Schools { get; set; }
}

public class SchoolIdPermission
{
    [JsonProperty(PropertyName = "SchoolId")]
    public int SchoolId { get; set; }
}
