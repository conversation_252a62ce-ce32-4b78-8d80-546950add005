"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["src_app_canteen-settings_canteen-settings_module_ts"],{

/***/ 58816:
/*!*********************************************************************!*\
  !*** ./src/app/canteen-settings/canteen-settings-routing.module.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenSettingsRoutingModule: () => (/* binding */ CanteenSettingsRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sharedServices */ 2902);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components */ 31096);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);

// Services

// Components



const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.AccountSettingsComponent
}, {
  path: 'school',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.SchoolSettingsComponent
}, {
  path: 'admin',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsComponent
}, {
  path: 'menu',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.MenuSettingsComponent
}, {
  path: 'menu/categories',
  component: _components__WEBPACK_IMPORTED_MODULE_1__.ManageCategoriesComponent,
  resolve: {
    categories: _sharedServices__WEBPACK_IMPORTED_MODULE_0__.CategoriesResolver
  }
}];
class CanteenSettingsRoutingModule {
  static {
    this.ɵfac = function CanteenSettingsRoutingModule_Factory(t) {
      return new (t || CanteenSettingsRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: CanteenSettingsRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](CanteenSettingsRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 11953:
/*!*************************************************************!*\
  !*** ./src/app/canteen-settings/canteen-settings.module.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CanteenSettingsModule: () => (/* binding */ CanteenSettingsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var ngx_print__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ngx-print */ 33133);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/material/core */ 74646);
/* harmony import */ var _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @angular/material/datepicker */ 61977);
/* harmony import */ var _angular_material_expansion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/material/expansion */ 19322);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @angular/material/paginator */ 24624);
/* harmony import */ var _angular_material_radio__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/radio */ 53804);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _angular_material_menu__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @angular/material/menu */ 31034);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _canteen_settings_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./canteen-settings-routing.module */ 58816);
/* harmony import */ var _account_account_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../account/account.module */ 90359);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-tools/shared-tools.module */ 23879);
/* harmony import */ var _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../schools-events/schools-events.module */ 42199);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../schools-form/schools-form.module */ 97162);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components */ 31096);
/* harmony import */ var _manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../manage-order/components/category-icon/category-icon.component */ 70900);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 37580);



// google material
















//Modules







// Components



class CanteenSettingsModule {
  static {
    this.ɵfac = function CanteenSettingsModule_Factory(t) {
      return new (t || CanteenSettingsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineNgModule"]({
      type: CanteenSettingsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineInjector"]({
      providers: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.DatePipe],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsFormModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, ngx_print__WEBPACK_IMPORTED_MODULE_12__.NgxPrintModule, _canteen_settings_routing_module__WEBPACK_IMPORTED_MODULE_0__.CanteenSettingsRoutingModule,
      //CashlessCoreModule,
      _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsEventsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsButtonModule,
      // material
      _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatFormFieldModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_14__.MatRadioModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__.MatCheckboxModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_16__.MatExpansionModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_17__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_18__.MatTableModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_19__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_20__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_21__.MatButtonModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_22__.MatSortModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__.MatPaginatorModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_24__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_25__.MatDatepickerModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_26__.MatTooltipModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_27__.MatMenuModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_28__.MatDialogModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_26__.MatTooltipModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsetNgModuleScope"](CanteenSettingsModule, {
    declarations: [_components__WEBPACK_IMPORTED_MODULE_7__.AccountSettingsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.SchoolSettingsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MenuSettingsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.AdminSettingsComponent, _components__WEBPACK_IMPORTED_MODULE_7__.ClassesListComponent, _components__WEBPACK_IMPORTED_MODULE_7__.PopupCategoryFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.ManageCategoriesComponent, _components__WEBPACK_IMPORTED_MODULE_7__.AddSchoolClassComponent, _components__WEBPACK_IMPORTED_MODULE_7__.OpeningDaysFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.DietaryLabelsFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.MenuCategoriesFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.OrderAdvanceFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.AllergyAlertFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.PrinterOptionsFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.SchoolClosingDateFormComponent, _components__WEBPACK_IMPORTED_MODULE_7__.SchoolCloseDateTableComponent, _components__WEBPACK_IMPORTED_MODULE_7__.SettingsCheckboxListComponent, _components__WEBPACK_IMPORTED_MODULE_7__.FoodBreakSettingsFormComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _schools_form_schools_form_module__WEBPACK_IMPORTED_MODULE_6__.SchoolsFormModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, ngx_print__WEBPACK_IMPORTED_MODULE_12__.NgxPrintModule, _canteen_settings_routing_module__WEBPACK_IMPORTED_MODULE_0__.CanteenSettingsRoutingModule,
    //CashlessCoreModule,
    _account_account_module__WEBPACK_IMPORTED_MODULE_1__.AccountModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule, _shared_tools_shared_tools_module__WEBPACK_IMPORTED_MODULE_3__.SharedToolsModule, _schools_events_schools_events_module__WEBPACK_IMPORTED_MODULE_4__.SchoolsEventsModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_5__.SchoolsButtonModule,
    // material
    _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatFormFieldModule, _angular_material_radio__WEBPACK_IMPORTED_MODULE_14__.MatRadioModule, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_15__.MatCheckboxModule, _angular_material_expansion__WEBPACK_IMPORTED_MODULE_16__.MatExpansionModule, _angular_material_select__WEBPACK_IMPORTED_MODULE_17__.MatSelectModule, _angular_material_table__WEBPACK_IMPORTED_MODULE_18__.MatTableModule, _angular_material_icon__WEBPACK_IMPORTED_MODULE_19__.MatIconModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_20__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_21__.MatButtonModule, _angular_material_sort__WEBPACK_IMPORTED_MODULE_22__.MatSortModule, _angular_material_paginator__WEBPACK_IMPORTED_MODULE_23__.MatPaginatorModule, _angular_material_core__WEBPACK_IMPORTED_MODULE_24__.MatNativeDateModule, _angular_material_datepicker__WEBPACK_IMPORTED_MODULE_25__.MatDatepickerModule, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_26__.MatTooltipModule, _angular_material_menu__WEBPACK_IMPORTED_MODULE_27__.MatMenuModule, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_28__.MatDialogModule, _manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_8__.CategoryIconComponent],
    exports: [_angular_material_tooltip__WEBPACK_IMPORTED_MODULE_26__.MatTooltipModule]
  });
})();

/***/ }),

/***/ 6162:
/*!********************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/account-settings/account-settings.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountSettingsComponent: () => (/* binding */ AccountSettingsComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared-tools/components/settings-row/settings-row.component */ 7429);






function AccountSettingsComponent_settings_row_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "settings-row", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "img", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
class AccountSettingsComponent {
  constructor(store) {
    this.store = store;
  }
  ngOnInit() {
    this.subscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.select)(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.selectedCanteen)).subscribe(state => {
      this.isAdmin = Boolean(state.IsAdmin);
    });
  }
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
  static {
    this.ɵfac = function AccountSettingsComponent_Factory(t) {
      return new (t || AccountSettingsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: AccountSettingsComponent,
      selectors: [["app-account-settings"]],
      decls: 8,
      vars: 1,
      consts: [[1, "col-8", "settingsWrapper"], ["text", "Admin Settings", "route", "/canteen/settings/admin", 4, "ngIf"], ["text", "School Settings", "route", "/canteen/settings/school"], ["src", "assets/icons/settings.svg"], ["text", "Canteen Settings", "route", "/canteen/settings/menu"], ["src", "assets/icons/bread.svg"], ["text", "Sign out", "signOutMode", "true", "lastRow", "true"], ["src", "assets/icons/sign-out.svg"], ["text", "Admin Settings", "route", "/canteen/settings/admin"], ["src", "assets/icons/admin-settings.svg"]],
      template: function AccountSettingsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, AccountSettingsComponent_settings_row_1_Template, 2, 0, "settings-row", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "settings-row", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](3, "img", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "settings-row", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](5, "img", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "settings-row", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](7, "img", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isAdmin);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _shared_tools_components_settings_row_settings_row_component__WEBPACK_IMPORTED_MODULE_1__.SettingsRowComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.settingsWrapper[_ngcontent-%COMP%] {\n  margin: 16px 20px 0 20px;\n  border-radius: 12px;\n  overflow: hidden;\n  padding: 0;\n}\n\nimg[_ngcontent-%COMP%] {\n  width: 22px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi1zZXR0aW5ncy9jb21wb25lbnRzL2FjY291bnQtc2V0dGluZ3MvYWNjb3VudC1zZXR0aW5ncy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFkQTtFQUNFLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLFVBQUE7QUFpQkY7O0FBZEE7RUFDRSxXQUFBO0FBaUJGIiwic291cmNlc0NvbnRlbnQiOlsiJGJyZWFrcG9pbnQtc206IDU3NnB4O1xuJGJyZWFrcG9pbnQtbWQ6IDc2N3B4O1xuJGJyZWFrcG9pbnQtbGc6IDk5MnB4O1xuJGJyZWFrcG9pbnQteGw6IDEyMDBweDtcblxuLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWF4LXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4vLyBOT1RFIEN1cnJlbnRseSB0YWJsZXQgYW5kIG1vYmlsZSBpcyB0aGUgc2FtZS4gQ2hhbmdlIHRvICRicmVha3BvaW50LWxnIGxhdGVyIGlmIHdlIGdldCBhIHByb3BlciB0YWJsZXQgZGVzaWduLlxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuIiwiQGltcG9ydCAnLi4vLi4vLi4vLi4vc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MnO1xuXG4uc2V0dGluZ3NXcmFwcGVyIHtcbiAgbWFyZ2luOiAxNnB4IDIwcHggMCAyMHB4O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBwYWRkaW5nOiAwO1xufVxuXG5pbWcge1xuICB3aWR0aDogMjJweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 85398:
/*!********************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/add-school-class/add-school-class.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AddSchoolClassComponent: () => (/* binding */ AddSchoolClassComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components/dialog-confirm/dialog-confirm.component */ 26645);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_basic_form_buttons_basic_form_buttons_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../schools-form/components/basic-form-buttons/basic-form-buttons.component */ 4612);
/* harmony import */ var _shared_tools_components_modal_header_modal_header_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared-tools/components/modal-header/modal-header.component */ 20951);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/input */ 95541);














function AddSchoolClassComponent_mat_error_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx_r0.getErrorMessageName());
  }
}
class AddSchoolClassComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(schoolClassService, spinnerService, dialogRef, initSchoolData, dialog) {
    super();
    this.schoolClassService = schoolClassService;
    this.spinnerService = spinnerService;
    this.dialogRef = dialogRef;
    this.initSchoolData = initSchoolData;
    this.dialog = dialog;
    this.loading = false;
  }
  ngOnInit() {
    this.CreateForm(this.initSchoolData);
  }
  ////////////////////////////////////////
  // Form
  ////////////////////////////////////////
  get name() {
    return this.form.get('name');
  }
  getErrorMessageName() {
    return this.form.get('name').hasError('required') ? 'You must enter a value' : '';
  }
  CreateForm(schoolClass) {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroup({
      id: new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControl(schoolClass.ClassId),
      schoolId: new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControl(schoolClass.SchoolId),
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControl(schoolClass.Name, [_angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required]),
      teacher: new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControl(schoolClass.Teacher || ''),
      sortOrder: new _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControl(schoolClass.SortOrder)
    });
  }
  ////////////////////////////////////////
  // UPSERT class
  ////////////////////////////////////////
  onSubmit() {
    this.loading = true;
    this.initSchoolData.Name = this.form.get('name').value;
    this.initSchoolData.Teacher = this.form.get('teacher').value;
    this.initSchoolData.SortOrder = this.form.get('sortOrder').value;
    if (this.initSchoolData.ClassId) {
      this.updateClass(this.initSchoolData);
    } else {
      this.addClass(this.initSchoolData);
    }
  }
  addClass(data) {
    this.schoolClassService.CreateClassApi(data).subscribe({
      next: response => {
        this.apiCallSuccess(response);
      },
      error: error => {
        this.apiCallError(false, error);
      }
    });
  }
  updateClass(data) {
    this.schoolClassService.UpdateClassApi(data).subscribe({
      next: response => {
        this.apiCallSuccess(response);
      },
      error: error => {
        this.apiCallError(true, error);
      }
    });
  }
  apiCallSuccess(response) {
    this.dialogRef.close(response);
    this.spinnerService.stop();
    this.loading = false;
  }
  apiCallError(editClass, error) {
    this.showErrorDialog(editClass);
    this.spinnerService.stop();
    this.loading = false;
    this.handleErrorFromService(error);
  }
  showErrorDialog(editClass) {
    const keyWord = editClass ? 'Editing' : 'Creating';
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `${keyWord} a class was unsuccessful. Please try again.`;
    data.ConfirmButton = 'Ok';
    this.dialog.open(src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_1__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
  }
  GetTitle() {
    return this.initSchoolData.ClassId > 0 ? 'Edit Class' : 'Add Class';
  }
  closeModal() {
    this.dialogRef.close();
  }
  static {
    this.ɵfac = function AddSchoolClassComponent_Factory(t) {
      return new (t || AddSchoolClassComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolClassesService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MatDialogRef), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MAT_DIALOG_DATA), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: AddSchoolClassComponent,
      selectors: [["canteen-add-school-class"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 18,
      vars: 5,
      consts: [[3, "title", "close"], [1, "cashlessForm", 3, "formGroup"], ["appearance", "outline"], ["matInput", "", "placeholder", "Enter class Name", "formControlName", "name", "type", "text"], [4, "ngIf"], ["matInput", "", "placeholder", "Enter teacher name", "formControlName", "teacher", "type", "text"], ["matInput", "", "placeholder", "Display Order", "formControlName", "sortOrder", "type", "number", "step", "1"], [1, "buttonsWrapper"], [3, "disableSaveButton", "loading", "saveEvent", "cancelEvent"]],
      template: function AddSchoolClassComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "mat-dialog-content")(1, "modal-header", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("close", function AddSchoolClassComponent_Template_modal_header_close_1_listener() {
            return ctx.closeModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "form", 1)(3, "mat-form-field", 2)(4, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5, "Class Name");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](6, "input", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](7, AddSchoolClassComponent_mat_error_7_Template, 2, 1, "mat-error", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "mat-form-field", 2)(9, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10, "Teacher Name");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](11, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](12, "mat-form-field", 2)(13, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](14, "Display Order");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](15, "input", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "div", 7)(17, "basic-form-buttons", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("saveEvent", function AddSchoolClassComponent_Template_basic_form_buttons_saveEvent_17_listener() {
            return ctx.onSubmit();
          })("cancelEvent", function AddSchoolClassComponent_Template_basic_form_buttons_cancelEvent_17_listener() {
            return ctx.closeModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("title", ctx.GetTitle());
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.name.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("disableSaveButton", !ctx.form.valid)("loading", ctx.loading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _schools_form_components_basic_form_buttons_basic_form_buttons_component__WEBPACK_IMPORTED_MODULE_3__.BasicFormButtonsComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _shared_tools_components_modal_header_modal_header_component__WEBPACK_IMPORTED_MODULE_4__.ModalHeaderComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatError, _angular_material_input__WEBPACK_IMPORTED_MODULE_10__.MatInput, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_7__.MatDialogContent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.buttonsWrapper[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-top: 20px;\n  padding-right: 10px;\n  text-align: right;\n}\n\n.modalTitle[_ngcontent-%COMP%] {\n  font-size: 30px;\n  font-weight: bold;\n  margin-top: 0;\n  margin-bottom: 15px;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  color: #ffffff;\n  font-style: normal;\n  font-weight: bold;\n  font-size: 16px;\n  line-height: 18px;\n  text-align: center;\n  border-radius: 10px;\n  padding-right: 22px;\n  padding-left: 22px;\n  height: 34px;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  background: #e0e0e0;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 16px;\n  line-height: 18px;\n  text-align: center;\n  border: 1px solid #ff7a00;\n  border-radius: 10px;\n  height: 34px;\n  margin-right: 10px;\n  color: #333b44;\n}\n\ninput[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\ninput[_ngcontent-%COMP%]::placeholder {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #dadada;\n}\n\n.image[_ngcontent-%COMP%] {\n  width: 28px;\n}\n\n.emptyBlock[_ngcontent-%COMP%] {\n  height: 64px;\n}\n\n.archiveDiv[_ngcontent-%COMP%] {\n  text-align: right;\n}\n\n.archiveButton[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 16px;\n  padding-right: 22px;\n  padding-left: 22px;\n  width: 90px;\n  height: 44px;\n  text-align: center;\n  background-color: #e0e0e0;\n  color: red;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi1zZXR0aW5ncy9jb21wb25lbnRzL2FkZC1zY2hvb2wtY2xhc3MvYWRkLXNjaG9vbC1jbGFzcy5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtFQUNFLGFBQUE7QUNKRjtBREtFO0VBRkY7SUFHSSxjQUFBO0VDRkY7QUFDRjs7QURLQTtFQUNFLGFBQUE7QUNGRjtBREdFO0VBRkY7SUFHSSxjQUFBO0VDQUY7QUFDRjs7QUFkQTtFQUNFLFNBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7QUFpQkY7O0FBZEE7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUFpQkY7O0FBZEE7RUFDRSxtQkNRUztFRFBULGNBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0FBaUJGOztBQWRBO0VBQ0UsbUJBQUE7QUFpQkY7O0FBZEE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQWlCRjs7QUFkQTtFQUNFLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFpQkY7O0FBZEE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQWlCRjs7QUFkQTtFQUNFLFdBQUE7QUFpQkY7O0FBZEE7RUFDRSxZQUFBO0FBaUJGOztBQWRBO0VBQ0UsaUJBQUE7QUFpQkY7O0FBZEE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxVQUFBO0FBaUJGIiwic291cmNlc0NvbnRlbnQiOlsiJGJyZWFrcG9pbnQtc206IDU3NnB4O1xuJGJyZWFrcG9pbnQtbWQ6IDc2N3B4O1xuJGJyZWFrcG9pbnQtbGc6IDk5MnB4O1xuJGJyZWFrcG9pbnQteGw6IDEyMDBweDtcblxuLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWF4LXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4vLyBOT1RFIEN1cnJlbnRseSB0YWJsZXQgYW5kIG1vYmlsZSBpcyB0aGUgc2FtZS4gQ2hhbmdlIHRvICRicmVha3BvaW50LWxnIGxhdGVyIGlmIHdlIGdldCBhIHByb3BlciB0YWJsZXQgZGVzaWduLlxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuIiwiQGltcG9ydCAnLi4vLi4vLi4vLi4vc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MnO1xuXG4uYnV0dG9uc1dyYXBwZXIge1xuICBtYXJnaW46IDA7XG4gIHBhZGRpbmctdG9wOiAyMHB4O1xuICBwYWRkaW5nLXJpZ2h0OiAxMHB4O1xuICB0ZXh0LWFsaWduOiByaWdodDtcbn1cblxuLm1vZGFsVGl0bGUge1xuICBmb250LXNpemU6IDMwcHg7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBtYXJnaW4tdG9wOiAwO1xuICBtYXJnaW4tYm90dG9tOiAxNXB4O1xufVxuXG4uYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogJG9yYW5nZS0zO1xuICBjb2xvcjogI2ZmZmZmZjtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBsaW5lLWhlaWdodDogMThweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBwYWRkaW5nLXJpZ2h0OiAyMnB4O1xuICBwYWRkaW5nLWxlZnQ6IDIycHg7XG4gIGhlaWdodDogMzRweDtcbn1cblxuLmJ1dHRvbjpkaXNhYmxlZCB7XG4gIGJhY2tncm91bmQ6ICNlMGUwZTA7XG59XG5cbi5jYW5jZWxCdXR0b24ge1xuICBmb250LXN0eWxlOiBub3JtYWw7XG4gIGZvbnQtd2VpZ2h0OiBub3JtYWw7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgbGluZS1oZWlnaHQ6IDE4cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgYm9yZGVyOiAxcHggc29saWQkb3JhbmdlLTM7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGhlaWdodDogMzRweDtcbiAgbWFyZ2luLXJpZ2h0OiAxMHB4O1xuICBjb2xvcjogIzMzM2I0NDtcbn1cblxuaW5wdXQge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBmb250LXN0eWxlOiBub3JtYWw7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAyMHB4O1xuICBjb2xvcjogIzAwMDAwMDtcbn1cblxuaW5wdXQ6OnBsYWNlaG9sZGVyIHtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogbm9ybWFsO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAyMHB4O1xuICBjb2xvcjogI2RhZGFkYTtcbn1cblxuLmltYWdlIHtcbiAgd2lkdGg6IDI4cHg7XG59XG5cbi5lbXB0eUJsb2NrIHtcbiAgaGVpZ2h0OiA2NHB4O1xufVxuXG4uYXJjaGl2ZURpdiB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xufVxuXG4uYXJjaGl2ZUJ1dHRvbiB7XG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcbiAgZm9udC13ZWlnaHQ6IG5vcm1hbDtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBwYWRkaW5nLXJpZ2h0OiAyMnB4O1xuICBwYWRkaW5nLWxlZnQ6IDIycHg7XG4gIHdpZHRoOiA5MHB4O1xuICBoZWlnaHQ6IDQ0cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2UwZTBlMDtcbiAgY29sb3I6IHJlZDtcbn1cbiIsIkBpbXBvcnQgJ2Nhc2hsZXNzLWJyZWFrcG9pbnRzJztcblxuLy8gUHJpbWFyeSBjb2xvdXJzXG5cbiRibHVlLTE6ICMxZWEzY2U7XG4kYmx1ZS0yOiAjNDc1NGIwO1xuJGJsdWUtMzogIzE0NGNkYztcblxuJHJlZC0xOiAjZjE0NzYyO1xuJHJlZC0yOiAjZmZlYmViO1xuJHJlZC0zOiAjYzA0NTQ1O1xuJHJlZC00OiAjZmZjZmNjO1xuXG4kcHVycGxlLTE6ICM3ZjNkYjM7XG4kbmF2eS0xOiAjMWM0MjcwO1xuJGNoYXJjb2FsLTE6ICMzMzNiNDQ7XG5cbiRncmVlbi0xOiAjMDBiYTZiO1xuJGdyZWVuLTI6ICNkOWY1ZTk7XG4kZ3JlZW4tMzogIzAwNmY0OTtcbiRncmVlbi00OiAjZTNmNWVmO1xuJGdyZWVuLTU6ICNkZmZmZjA7XG5cbi8vIE9yYW5nZVxuJG9yYW5nZS0xOiAjZmY5ZTAwO1xuJG9yYW5nZS0zOiAjZmY3YTAwO1xuJG9yYW5nZS0yOiAjZmY0YjE3O1xuJG9yYW5nZS00OiAjZmZlMmM3O1xuJG9yYW5nZS01OiAjZmZmMmU2O1xuJG9yYW5nZS02OiAjZmY4MDAwO1xuJG9yYW5nZS03OiAjZmZlYWQ2O1xuJG9yYW5nZS04OiAjZmVmMGUwO1xuJG9yYW5nZS05OiAjZmZmMGUwO1xuJG9yYW5nZS0xMDogI2YzNjYwMDtcbiRvcmFuZ2UtMTE6ICNmZmUzYmI7XG4kbW9iaWxlLWRhcmstb3JhbmdlOiAjRDk1QjAwO1xuJG1vYmlsZS1saWdodC1vcmFuZ2U6ICNGRkVBRDY7XG5cbi8vIGdyZXlcbiRncmV5LTE6ICM4ODk0OWY7XG4kZ3JleS0yOiAjZTBlMWUyO1xuJGdyZXktMzogI2RkZGRkZDtcbiRncmV5LTQ6ICNmMmYyZjI7XG4kZ3JleS01OiAjMzMzYjQ0O1xuJGdyZXktNjogI2U1ZTVlNTtcbiRncmV5LTc6ICNiOWI5Yzg7XG4kZ3JleS04OiAjODc4Nzg3O1xuJGdyZXktOTogI2UwZTBlMDtcbiRncmV5LTEwOiAjYmRiZGJkO1xuJGdyZXktMTE6ICM4MjgyODI7XG4kZ3JleS0xMjogIzFiMWYzOTtcbiRncmV5LTEzOiAjYjhiOGI4O1xuJGdyZXktMTQ6ICMyNzJjNTA7XG4kZ3JleS0xNTogI2Y2ZjVmMztcbiRncmV5LTE2OiAjZmFmOWY4O1xuJGdyZXktMTc6ICM2YjZjODk7XG5cbi8vIFNlY29uZGFyeSBjb2xvdXJzXG4kYmx1ZS1zZWNvbmRhcnktMTogcmdiYSgyNTUsIDI0MywgMjE5LCAxKTtcbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 70014:
/*!****************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/admin-settings/admin-settings.component.ts ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminSettingsComponent: () => (/* binding */ AdminSettingsComponent)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/states/user/user.selectors */ 92290);
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _shared_tools_components_canteen_select_list_canteen_select_list_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared-tools/components/canteen-select-list/canteen-select-list.component */ 4007);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/table */ 77697);











function AdminSettingsComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 10)(1, "canteen-select-list", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("isVisible", function AdminSettingsComponent_div_7_Template_canteen_select_list_isVisible_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r2.CanteenListVisibleChanged($event));
    })("selectedChanged", function AdminSettingsComponent_div_7_Template_canteen_select_list_selectedChanged_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r3);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r4.onCanteenSelect($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
}
function AdminSettingsComponent_table_12_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "p", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate2"](" ", user_r21.FirstName, " ", user_r21.Lastname, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r21.IsAdmin ? "(Admin)" : "");
  }
}
function AdminSettingsComponent_table_12_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Email");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r22 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](user_r22.Email);
  }
}
function AdminSettingsComponent_table_12_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "View Menu Editor");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_9_div_2_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 36);
  }
}
function AdminSettingsComponent_table_12_td_9_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_9_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_9_div_2_img_1_Template, 1, 0, "img", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_9_div_2_div_2_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r23.IsMenuEditorAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r23.IsMenuEditorAvailable);
  }
}
function AdminSettingsComponent_table_12_td_9_ng_template_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_9_ng_template_3_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 40);
  }
}
function AdminSettingsComponent_table_12_td_9_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r34 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function AdminSettingsComponent_table_12_td_9_ng_template_3_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r34);
      const user_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r32.onMarkClick(user_r23, "menuEditor"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_9_ng_template_3_div_1_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_9_ng_template_3_img_2_Template, 1, 0, "img", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r23.IsMenuEditorAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r23.IsMenuEditorAvailable);
  }
}
function AdminSettingsComponent_table_12_td_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27)(1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_9_div_2_Template, 3, 2, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_9_ng_template_3_Template, 3, 2, "ng-template", null, 33, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r23 = ctx.$implicit;
    const _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](4);
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r23.UserId == ctx_r10.currentUser.UserId)("ngIfElse", _r25);
  }
}
function AdminSettingsComponent_table_12_th_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "View Sales Reports");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_12_div_2_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 36);
  }
}
function AdminSettingsComponent_table_12_td_12_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_12_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_12_div_2_img_1_Template, 1, 0, "img", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_12_div_2_div_2_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r36.IsSaleReportsAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r36.IsSaleReportsAvailable);
  }
}
function AdminSettingsComponent_table_12_td_12_ng_template_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_12_ng_template_3_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 40);
  }
}
function AdminSettingsComponent_table_12_td_12_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r47 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function AdminSettingsComponent_table_12_td_12_ng_template_3_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r47);
      const user_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r45 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r45.onMarkClick(user_r36, "salesReport"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_12_ng_template_3_div_1_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_12_ng_template_3_img_2_Template, 1, 0, "img", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r36.IsSaleReportsAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r36.IsSaleReportsAvailable);
  }
}
function AdminSettingsComponent_table_12_td_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27)(1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_12_div_2_Template, 3, 2, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_12_ng_template_3_Template, 3, 2, "ng-template", null, 41, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r36 = ctx.$implicit;
    const _r38 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](4);
    const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r36.UserId == ctx_r12.currentUser.UserId)("ngIfElse", _r38);
  }
}
function AdminSettingsComponent_table_12_th_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "View Event management");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_15_div_2_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 36);
  }
}
function AdminSettingsComponent_table_12_td_15_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_15_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_15_div_2_img_1_Template, 1, 0, "img", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_15_div_2_div_2_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r49.IsEventManagementAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r49.IsEventManagementAvailable);
  }
}
function AdminSettingsComponent_table_12_td_15_ng_template_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_15_ng_template_3_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 40);
  }
}
function AdminSettingsComponent_table_12_td_15_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r60 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function AdminSettingsComponent_table_12_td_15_ng_template_3_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r60);
      const user_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r58.onMarkClick(user_r49, "viewEvent"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_15_ng_template_3_div_1_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_15_ng_template_3_img_2_Template, 1, 0, "img", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r49.IsEventManagementAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r49.IsEventManagementAvailable);
  }
}
function AdminSettingsComponent_table_12_td_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27)(1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_15_div_2_Template, 3, 2, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_15_ng_template_3_Template, 3, 2, "ng-template", null, 42, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r49 = ctx.$implicit;
    const _r51 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](4);
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r49.UserId == ctx_r14.currentUser.UserId)("ngIfElse", _r51);
  }
}
function AdminSettingsComponent_table_12_th_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Allow Unprinted Orders");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_18_div_2_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 36);
  }
}
function AdminSettingsComponent_table_12_td_18_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_18_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_18_div_2_img_1_Template, 1, 0, "img", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_18_div_2_div_2_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r62 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r62.IsOrdersNotPrintedReportsAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r62.IsOrdersNotPrintedReportsAvailable);
  }
}
function AdminSettingsComponent_table_12_td_18_ng_template_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_18_ng_template_3_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 40);
  }
}
function AdminSettingsComponent_table_12_td_18_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r73 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function AdminSettingsComponent_table_12_td_18_ng_template_3_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r73);
      const user_r62 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r71 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r71.onMarkClick(user_r62, "notPrintedReport"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_18_ng_template_3_div_1_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_18_ng_template_3_img_2_Template, 1, 0, "img", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r62 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r62.IsOrdersNotPrintedReportsAvailable);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r62.IsOrdersNotPrintedReportsAvailable);
  }
}
function AdminSettingsComponent_table_12_td_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27)(1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_18_div_2_Template, 3, 2, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_18_ng_template_3_Template, 3, 2, "ng-template", null, 43, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r62 = ctx.$implicit;
    const _r64 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](4);
    const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r62.UserId == ctx_r16.currentUser.UserId)("ngIfElse", _r64);
  }
}
function AdminSettingsComponent_table_12_th_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "th", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Email Unprinted Orders");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function AdminSettingsComponent_table_12_td_21_div_2_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 36);
  }
}
function AdminSettingsComponent_table_12_td_21_div_2_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_21_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_21_div_2_img_1_Template, 1, 0, "img", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_21_div_2_div_2_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r75 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r75.NotifyOrdersNotPrinted);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r75.NotifyOrdersNotPrinted);
  }
}
function AdminSettingsComponent_table_12_td_21_ng_template_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "div", 37);
  }
}
function AdminSettingsComponent_table_12_td_21_ng_template_3_img_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 40);
  }
}
function AdminSettingsComponent_table_12_td_21_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r86 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function AdminSettingsComponent_table_12_td_21_ng_template_3_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r86);
      const user_r75 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r84 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r84.onMarkClick(user_r75, "notPrinted"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, AdminSettingsComponent_table_12_td_21_ng_template_3_div_1_Template, 1, 0, "div", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_21_ng_template_3_img_2_Template, 1, 0, "img", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r75 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !user_r75.NotifyOrdersNotPrinted);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r75.NotifyOrdersNotPrinted);
  }
}
function AdminSettingsComponent_table_12_td_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "td", 27)(1, "div", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_td_21_div_2_Template, 3, 2, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_21_ng_template_3_Template, 3, 2, "ng-template", null, 43, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r75 = ctx.$implicit;
    const _r77 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](4);
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", user_r75.UserId == ctx_r18.currentUser.UserId)("ngIfElse", _r77);
  }
}
function AdminSettingsComponent_table_12_tr_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "tr", 44);
  }
}
function AdminSettingsComponent_table_12_tr_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "tr", 45);
  }
}
function AdminSettingsComponent_table_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "table", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](1, 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, AdminSettingsComponent_table_12_th_2_Template, 2, 0, "th", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, AdminSettingsComponent_table_12_td_3_Template, 4, 3, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](4, 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](5, AdminSettingsComponent_table_12_th_5_Template, 2, 0, "th", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](6, AdminSettingsComponent_table_12_td_6_Template, 2, 1, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](7, 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, AdminSettingsComponent_table_12_th_8_Template, 2, 0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](9, AdminSettingsComponent_table_12_td_9_Template, 5, 2, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](10, 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](11, AdminSettingsComponent_table_12_th_11_Template, 2, 0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](12, AdminSettingsComponent_table_12_td_12_Template, 5, 2, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](13, 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](14, AdminSettingsComponent_table_12_th_14_Template, 2, 0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, AdminSettingsComponent_table_12_td_15_Template, 5, 2, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](16, 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](17, AdminSettingsComponent_table_12_th_17_Template, 2, 0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](18, AdminSettingsComponent_table_12_td_18_Template, 5, 2, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](19, 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](20, AdminSettingsComponent_table_12_th_20_Template, 2, 0, "th", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](21, AdminSettingsComponent_table_12_td_21_Template, 5, 2, "td", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](22, AdminSettingsComponent_table_12_tr_22_Template, 1, 0, "tr", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](23, AdminSettingsComponent_table_12_tr_23_Template, 1, 0, "tr", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("dataSource", ctx_r1.users);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](22);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("matHeaderRowDef", ctx_r1.displayedColumns);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("matRowDefColumns", ctx_r1.displayedColumns);
  }
}
const _canteenAdminColumns = ['name', 'email', 'menuEditor', 'salesReport', 'notPrintedReport', 'notPrintedAlert'];
const _canteenAdminWithEventColumns = ['name', 'email', 'menuEditor', 'salesReport', 'viewEvent', 'notPrintedReport', 'notPrintedAlert'];
class AdminSettingsComponent {
  constructor(_location, store, spinnerService, userService, featureFlagService) {
    this._location = _location;
    this.store = store;
    this.spinnerService = spinnerService;
    this.userService = userService;
    this.featureFlagService = featureFlagService;
    this.displayedColumns = [];
    this.canteenListVisible = true;
    this.eventFeatureFlag = false;
  }
  ngOnInit() {
    this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_7__.select)(src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_1__.userState)).subscribe(state => {
      this.currentUser = state.connectedUser;
    });
    // get flag
    this.featureFlagService.getFlag(src_constants__WEBPACK_IMPORTED_MODULE_2__.FeatureFlags.viewEventManagement, false).then(res => {
      this.eventFeatureFlag = res;
      this.displayColumns();
    });
  }
  ngDestroy() {
    this.subscription.unsubscribe();
  }
  GoBackClick() {
    this._location.back();
  }
  LoadUsers(canteenId) {
    this.userService.GetUsersByCanteenAPI(canteenId).subscribe({
      next: users => {
        if (users.Users) {
          const selectedUser = users.Users.find(serverUser => serverUser.UserId === this.currentUser.UserId);
          const restUsers = users.Users.filter(serverUser => serverUser.UserId !== this.currentUser.UserId && serverUser.Email != '<EMAIL>') || [];
          this.users = [selectedUser, ...restUsers];
        }
        this.isListLoaded = true;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
      }
    });
  }
  onCanteenSelect(event) {
    this.spinnerService.start();
    this.selectedCanteen = event;
    this.displayColumns();
    this.LoadUsers(event.CanteenId);
  }
  displayColumns() {
    if (this.eventFeatureFlag && this.selectedCanteen.CanteenType != src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Uniform) {
      this.displayedColumns = _canteenAdminWithEventColumns;
    } else {
      this.displayedColumns = _canteenAdminColumns;
    }
  }
  onMarkClick(user, type) {
    this.spinnerService.start();
    // const { CanteenId, IsSaleReportsAvailable, UserId, IsMenuEditorAvailable } = user
    // const commonData = { UserId, CanteenId, IsSaleReportsAvailable, IsMenuEditorAvailable, ResponseDescription: 'OK' }
    // const menuEditorData = { ...commonData, IsMenuEditorAvailable: !IsMenuEditorAvailable }
    // const salesReportData = { ...commonData, IsSaleReportsAvailable: !IsSaleReportsAvailable }
    if (type == 'menuEditor') {
      user.IsMenuEditorAvailable = !user.IsMenuEditorAvailable;
    } else if (type == 'salesReport') {
      user.IsSaleReportsAvailable = !user.IsSaleReportsAvailable;
    } else if (type == 'notPrintedReport') {
      user.IsOrdersNotPrintedReportsAvailable = !user.IsOrdersNotPrintedReportsAvailable;
    } else if (type == 'viewEvent') {
      user.IsEventManagementAvailable = !user.IsEventManagementAvailable;
    } else {
      user.NotifyOrdersNotPrinted = !user.NotifyOrdersNotPrinted;
    }
    this.userService.UpdateCanteenUserSettingsAPI(user).subscribe({
      next: res => {
        this.LoadUsers(this.selectedCanteen.CanteenId);
      },
      error: err => {
        this.spinnerService.stop();
      }
    });
  }
  CanteenListVisibleChanged(isVisible) {
    this.canteenListVisible = isVisible;
  }
  static {
    this.ɵfac = function AdminSettingsComponent_Factory(t) {
      return new (t || AdminSettingsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_7__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.FeatureFlagService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: AdminSettingsComponent,
      selectors: [["admin-settings"]],
      decls: 13,
      vars: 2,
      consts: [[1, "col-12"], [1, "col-12", "col-md-8", "col-lg-4"], ["text", "Go Back", 1, "backButton", 3, "navBack"], [1, "titleWrapper"], ["sizes", "24", "src", "assets/icons/admin-settings.svg"], [1, "title"], ["class", "schoolSelection", 4, "ngIf"], [1, "titleDescriptionWrapper"], [1, "tableTitle"], ["mat-table", "", "class", "mat-elevation-z8 tableau usersTable", 3, "dataSource", 4, "ngIf"], [1, "schoolSelection"], ["title", "Select Canteen", 3, "isVisible", "selectedChanged"], ["mat-table", "", 1, "mat-elevation-z8", "tableau", "usersTable", 3, "dataSource"], ["matColumnDef", "name"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "email"], ["mat-header-cell", "", "class", "emailHeader", 4, "matHeaderCellDef"], ["matColumnDef", "menuEditor"], ["mat-header-cell", "", "class", "centerAlignment", 4, "matHeaderCellDef"], ["matColumnDef", "salesReport"], ["matColumnDef", "viewEvent"], ["matColumnDef", "notPrintedReport"], ["matColumnDef", "notPrintedAlert"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "adminLabel"], ["mat-header-cell", "", 1, "emailHeader"], ["mat-header-cell", "", 1, "centerAlignment"], [1, "checkboxWrapper"], [4, "ngIf", "ngIfElse"], ["notAdminMenu", ""], ["sizes", "24", "src", "assets/icons/checkBox-disable.svg", "class", "checkBox", 4, "ngIf"], ["class", "inActiveCheckbox", 4, "ngIf"], ["sizes", "24", "src", "assets/icons/checkBox-disable.svg", 1, "checkBox"], [1, "inActiveCheckbox"], [3, "click"], ["sizes", "24", "src", "assets/icons/checkBox.svg", "class", "checkBox", 4, "ngIf"], ["sizes", "24", "src", "assets/icons/checkBox.svg", 1, "checkBox"], ["notAdminSales", ""], ["notViewEvent", ""], ["notAdminAlert", ""], ["mat-header-row", ""], ["mat-row", ""]],
      template: function AdminSettingsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "nav-back-button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("navBack", function AdminSettingsComponent_Template_nav_back_button_navBack_2_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "img", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "h1", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, "Admin Settings");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](7, AdminSettingsComponent_div_7_Template, 2, 0, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 7)(9, "p", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](10, "Members");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](12, AdminSettingsComponent_table_12_Template, 24, 3, "table", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.canteenListVisible);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isListLoaded);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_4__.NavBackButtonComponent, _shared_tools_components_canteen_select_list_canteen_select_list_component__WEBPACK_IMPORTED_MODULE_5__.CanteenSelectListComponent, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_9__.MatRow],
      styles: [".backButton[_ngcontent-%COMP%] {\n  color: orange;\n}\n\n.titleWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 13px;\n  align-items: center;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  margin: 0;\n  margin-left: 8px;\n}\n\n.schoolSelection[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding-top: 10px;\n  padding-left: 20px;\n  padding-right: 20px;\n  margin-bottom: 16px;\n}\n\n.tableTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  color: #000000;\n  margin: 0;\n}\n\n.usersTable[_ngcontent-%COMP%] {\n  width: 100%;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.mat-mdc-cell[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 16px;\n  line-height: 20px;\n  color: #000000;\n  border-bottom: 1px solid #dadada;\n  padding-right: 5px;\n  padding-left: 5px;\n}\n\n.centerAlignment[_ngcontent-%COMP%] {\n  text-align: center;\n}\n\n.mat-elevation-z8[_ngcontent-%COMP%] {\n  box-shadow: 0 0 #000000;\n}\n\n.inActiveCheckbox[_ngcontent-%COMP%] {\n  border: 1px solid #333b44;\n  width: 16px;\n  height: 16px;\n  border-radius: 2px;\n}\n\n.checkboxWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n}\n\n.emailHeader[_ngcontent-%COMP%] {\n  padding-left: 5px;\n}\n\n.adminLabel[_ngcontent-%COMP%] {\n  display: inline;\n  color: #6d7681;\n}\n\n.titleDescriptionWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 20px 18px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 49848:
/*!************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/allergy-alert-form/allergy-alert-form.component.ts ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AllergyAlertFormComponent: () => (/* binding */ AllergyAlertFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../settings-checkbox-list/settings-checkbox-list.component */ 76504);

//Models





class AllergyAlertFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(spinnerService, schoolService) {
    super();
    this.spinnerService = spinnerService;
    this.schoolService = schoolService;
    this.checkBoxValue = [];
  }
  ngOnInit() {
    this.checkBoxValue.push({
      key: 'allergyAlert',
      value: 'Allow'
    });
  }
  ngOnChanges(changes) {
    if (changes?.schoolId?.currentValue) {
      this._createForm();
    }
  }
  _createForm() {
    this.currentAllergyFeature = this.getAllergyFeature();
    let hasAllergyAlert = Boolean(this.currentAllergyFeature?.IsActive);
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({
      allergyAlert: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(hasAllergyAlert, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required)
    });
  }
  submitForm() {
    this.spinnerService.start();
    let request = this.getAllergyUpdateRequest();
    this.schoolService.UpsertSchoolOptionsApi(request).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  getAllergyFeature() {
    if (!this.schoolFeatures) {
      return null;
    }
    let index = this.schoolFeatures.findIndex(x => x.OptionName === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureEnum.AllergyAlert);
    return index >= 0 ? this.schoolFeatures[index] : null;
  }
  getAllergyUpdateRequest() {
    let request;
    //update existing allergy feature
    if (this.currentAllergyFeature) {
      this.currentAllergyFeature.IsActive = this.allergyAlert.value;
      request = this.currentAllergyFeature;
    } else {
      //create new allergy feature
      request = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.OptionSchoolResponse();
      request.SchoolId = this.schoolId;
      request.OptionName = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureEnum.AllergyAlert;
      request.OptionDescription = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolFeatureEnum.AllergyAlert;
      request.IsActive = this.allergyAlert.value;
    }
    return request;
  }
  get allergyAlert() {
    return this.form.get('allergyAlert');
  }
  static {
    this.ɵfac = function AllergyAlertFormComponent_Factory(t) {
      return new (t || AllergyAlertFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SchoolService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AllergyAlertFormComponent,
      selectors: [["allergy-alert-form"]],
      inputs: {
        schoolId: "schoolId",
        schoolFeatures: "schoolFeatures"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵNgOnChangesFeature"]],
      decls: 1,
      vars: 3,
      consts: [["title", "Allergy Alerts", "description", "Allow parents to specify an 'allergy alert' on their child\u2019s order labels to highlight particular issues.\n  Allergies are selected from a predetermined list.", 3, "schoolId", "formGroup", "values", "submit"]],
      template: function AllergyAlertFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "settings-checkbox-list", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("submit", function AllergyAlertFormComponent_Template_settings_checkbox_list_submit_0_listener() {
            return ctx.submitForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("schoolId", ctx.schoolId)("formGroup", ctx.form)("values", ctx.checkBoxValue);
        }
      },
      dependencies: [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__.SettingsCheckboxListComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 70846:
/*!************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/classes-list/classes-list.component.ts ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClassesListComponent: () => (/* binding */ ClassesListComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_sort__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/sort */ 22047);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);











function ClassesListComponent_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Class Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function ClassesListComponent_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r8.Name);
  }
}
function ClassesListComponent_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "th", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Teacher");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function ClassesListComponent_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](element_r9.Teacher);
  }
}
function ClassesListComponent_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "th", 9);
  }
}
function ClassesListComponent_td_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "td", 11)(1, "mat-icon", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ClassesListComponent_td_9_Template_mat_icon_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r12);
      const element_r10 = restoredCtx.$implicit;
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r11.ArchiveClicked(element_r10));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "delete_outline");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "mat-icon", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ClassesListComponent_td_9_Template_mat_icon_click_3_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r12);
      const element_r10 = restoredCtx.$implicit;
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r13.onEdit(element_r10));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "mode_edit");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function ClassesListComponent_tr_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 14);
  }
}
function ClassesListComponent_tr_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "tr", 15);
  }
}
const compare = (a, b, isAsc) => {
  if (a < b) {
    return isAsc ? -1 : 1;
  }
  if (a === null) {
    return 1;
  }
  if (b === null) {
    return -1;
  }
  if (a > b) {
    return isAsc ? 1 : -1;
  }
  return 0;
};
class ClassesListComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(dialog, spinnerService, schoolClassService) {
    super();
    this.dialog = dialog;
    this.spinnerService = spinnerService;
    this.schoolClassService = schoolClassService;
    this.editClicked = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.displayedColumns = ['name', 'teacher', 'Actions'];
    this.dataSource = new _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTableDataSource();
  }
  ngOnInit() {}
  ngOnChanges() {
    this.RefreshTable(this.listClasses);
  }
  sortData(sort) {
    const data = this.listClasses ? [...this.listClasses] : [];
    if (!sort.active || sort.direction === '') {
      this.dataSource.data = this.listClasses;
      return;
    }
    const newData = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'name':
          return compare(a.Name.toLocaleLowerCase(), b.Name.toLocaleLowerCase(), isAsc);
        case 'teacher':
          return compare(a.Teacher ? a.Teacher.toLocaleLowerCase() : null, b.Teacher ? b.Teacher.toLocaleLowerCase() : null, isAsc);
        case 'IsActive':
          return compare(!a.IsActive, !b.IsActive, isAsc);
        default:
          return 0;
      }
    });
    this.dataSource.data = newData;
  }
  onEdit(classe) {
    this.editClicked.emit(classe);
  }
  RefreshTable(listClasses) {
    this.dataSource.data = listClasses;
  }
  ArchiveClicked(classe) {
    let data = new _sharedModels__WEBPACK_IMPORTED_MODULE_0__.ConfirmModal();
    data.Title = 'Archive Class';
    data.Text = "Archiving this class will inactivate the class permanently and can not be undone. Parents will be instructed to change their child's class. Proceed?";
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_1__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.ArchiveClickConfirmed(classe);
      }
    });
  }
  ArchiveClickConfirmed(classe) {
    this.spinnerService.start();
    classe.IsArchived = true;
    this.schoolClassService.ArchiveClassApi(classe).subscribe({
      next: response => {
        let index = this.listClasses.findIndex(i => i.ClassId == classe.ClassId);
        if (index > -1) {
          this.listClasses.splice(index, 1);
          this.RefreshTable(this.listClasses);
        }
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  static {
    this.ɵfac = function ClassesListComponent_Factory(t) {
      return new (t || ClassesListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_5__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolClassesService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: ClassesListComponent,
      selectors: [["classes-list"]],
      inputs: {
        listClasses: "listClasses"
      },
      outputs: {
        editClicked: "editClicked"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵNgOnChangesFeature"]],
      decls: 12,
      vars: 3,
      consts: [["mat-table", "", "matSort", "", 1, "mat-elevation-z8", "tableau", "classesTable", 3, "dataSource", "matSortChange"], ["matColumnDef", "name"], ["mat-sort-header", "", "mat-header-cell", "", "class", "title", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "teacher"], ["matColumnDef", "Actions"], ["mat-cell", "", "class", "actions", 4, "matCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", "class", "rowElement", 4, "matRowDef", "matRowDefColumns"], ["mat-sort-header", "", "mat-header-cell", "", 1, "title"], ["mat-cell", ""], ["mat-cell", "", 1, "actions"], ["matTooltip", "Delete", 3, "click"], ["matTooltip", "Edit", 3, "click"], ["mat-header-row", ""], ["mat-row", "", 1, "rowElement"]],
      template: function ClassesListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "table", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("matSortChange", function ClassesListComponent_Template_table_matSortChange_0_listener($event) {
            return ctx.sortData($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](1, 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, ClassesListComponent_th_2_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, ClassesListComponent_td_3_Template, 2, 1, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](4, 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, ClassesListComponent_th_5_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, ClassesListComponent_td_6_Template, 2, 1, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](7, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, ClassesListComponent_th_8_Template, 1, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, ClassesListComponent_td_9_Template, 5, 0, "td", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, ClassesListComponent_tr_10_Template, 1, 0, "tr", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, ClassesListComponent_tr_11_Template, 1, 0, "tr", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("dataSource", ctx.dataSource);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matHeaderRowDef", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
        }
      },
      dependencies: [_angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_4__.MatRow, _angular_material_icon__WEBPACK_IMPORTED_MODULE_6__.MatIcon, _angular_material_sort__WEBPACK_IMPORTED_MODULE_7__.MatSort, _angular_material_sort__WEBPACK_IMPORTED_MODULE_7__.MatSortHeader, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_8__.MatTooltip],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.classesTable[_ngcontent-%COMP%] {\n  width: 100%;\n  border-radius: 12px;\n  overflow: hidden;\n  margin-bottom: 30px;\n}\n\n.actionTableau[_ngcontent-%COMP%] {\n  margin-left: 5px;\n  margin-right: 5px;\n  cursor: pointer;\n  background-color: green;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-style: normal;\n  font-weight: normal;\n  font-size: 14px;\n  line-height: 15px;\n  color: #828282;\n}\n\n.checkBox[_ngcontent-%COMP%] {\n  color: #ff7a00;\n}\n\n.rowElement[_ngcontent-%COMP%] {\n  height: 61px;\n}\n\n.rowElement[_ngcontent-%COMP%]:hover {\n  background-color: #fbfaf9;\n}\n\n.actions[_ngcontent-%COMP%] {\n  text-align: right;\n}\n.actions[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 30px;\n  cursor: pointer;\n}\n\n.mat-mdc-cell[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 16px;\n  line-height: 20px;\n  color: #000000;\n  border-bottom: 1px solid #dadada;\n}\n\n.mat-elevation-z8[_ngcontent-%COMP%] {\n  box-shadow: 0 0 #000000;\n}\n\n.inActiveCheckbox[_ngcontent-%COMP%] {\n  transition: border-color 90ms cubic-bezier(0, 0, 0.2, 0.1);\n  border-width: 2px;\n  border-style: solid;\n  border-color: #ff7a00;\n  width: 16px;\n  height: 16px;\n  border-radius: 2px;\n}\n\n.checkBox[_ngcontent-%COMP%] {\n  margin-top: 4px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 62070:
/*!**************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/dietary-labels-form/dietary-labels-form.component.ts ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DietaryLabelsFormComponent: () => (/* binding */ DietaryLabelsFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedModels_base_KeyValueConversion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels/base/KeyValueConversion */ 26562);
/* harmony import */ var _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../settings-checkbox-list/settings-checkbox-list.component */ 76504);


//Models





class DietaryLabelsFormComponent {
  constructor() {
    this.updateSchool = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.dietaryFilters = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersLongForm;
    this.checkBoxValues = [];
  }
  ngOnInit() {
    this.checkBoxValues = (0,src_app_sharedModels_base_KeyValueConversion__WEBPACK_IMPORTED_MODULE_1__.getDietaryKeyValue)();
  }
  ngOnChanges(changes) {
    if (changes?.school?.currentValue) {
      if (this.school.Name) {
        this._createForm();
      }
    }
  }
  getLabelValue(dietaryLabel) {
    if (!this.school.DeactivatedFilters) {
      return true;
    }
    return !this.school?.DeactivatedFilters.includes(dietaryLabel);
  }
  _createForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      Vegetarian: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(`${src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['Vegetarian']},`)),
      Vegan: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['Vegan'])),
      GlutenFree: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['GlutenFree'])),
      Halal: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['Halal'])),
      LactoseFree: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['LactoseFree'])),
      NutsFree: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['NutsFree'])),
      FastingFriendly: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['FastingFriendly'])),
      DairyFree: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.getLabelValue(src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm['DairyFree']))
    });
  }
  submitForm() {
    let deactivatedFilters = '';
    Object.keys(this.form.controls).forEach(key => {
      if (!this.form.get(key).value) {
        deactivatedFilters += `${src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.DietaryFiltersShortForm[key]},`;
      }
    });
    const data = {
      ...this.school,
      DeactivatedFilters: deactivatedFilters
    };
    this.updateSchool.emit(data);
  }
  static {
    this.ɵfac = function DietaryLabelsFormComponent_Factory(t) {
      return new (t || DietaryLabelsFormComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: DietaryLabelsFormComponent,
      selectors: [["dietary-labels-form"]],
      inputs: {
        school: "school"
      },
      outputs: {
        updateSchool: "updateSchool"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵNgOnChangesFeature"]],
      decls: 1,
      vars: 3,
      consts: [["title", "Dietary Labels Display Options", 3, "schoolId", "formGroup", "values", "submit"]],
      template: function DietaryLabelsFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "settings-checkbox-list", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("submit", function DietaryLabelsFormComponent_Template_settings_checkbox_list_submit_0_listener() {
            return ctx.submitForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("schoolId", ctx.school.SchoolId)("formGroup", ctx.form)("values", ctx.checkBoxValues);
        }
      },
      dependencies: [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__.SettingsCheckboxListComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 81014:
/*!************************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/food-break-settings-form/food-break-settings-form.component.ts ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FoodBreakSettingsFormComponent: () => (/* binding */ FoodBreakSettingsFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices/menu/menu-custom-name */ 76573);
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/input-select-list/input-select-list.component */ 87892);
/* harmony import */ var _shared_tools_components_menu_list_menu_list_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared-tools/components/menu-list/menu-list.component */ 69451);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/checkbox */ 97024);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/input */ 95541);














function FoodBreakSettingsFormComponent_div_4_div_3_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "input-select-list", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("values", ctx_r2.customMenuNameList);
  }
}
function FoodBreakSettingsFormComponent_div_4_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div")(1, "form", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](2, FoodBreakSettingsFormComponent_div_4_div_3_div_2_Template, 2, 1, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "div", 7)(4, "div", 8)(5, "h3", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, "Cut off time");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](7, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](8, "Set the cut-off time that applies to each food break.");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](9, "mat-form-field", 10)(10, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](11, "Cut off time");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](12, "input", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](13, "div", 7)(14, "div", 8)(15, "h3", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](16, "Break time");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](17, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](18, "Set the time that each food break starts.");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](19, "mat-form-field", 10)(20, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](21, "Break time");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](22, "input", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](23, "div", 7)(24, "div", 8)(25, "mat-checkbox", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](26, "Disable");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](27, "div", 7)(28, "div", 8)(29, "basic-button", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("onPress", function FoodBreakSettingsFormComponent_div_4_div_3_Template_basic_button_onPress_29_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r3.saveFoodBreakSettings());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx_r1.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r1.showCustomMenuPicker);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](23);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("checked", !ctx_r1.selectedMenu.IsActive);
  }
}
function FoodBreakSettingsFormComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div")(1, "div", 3)(2, "menu-list", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("menuSelectionChanged", function FoodBreakSettingsFormComponent_div_4_Template_menu_list_menuSelectionChanged_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r6);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r5.setSelectedMenu($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](3, FoodBreakSettingsFormComponent_div_4_div_3_Template, 30, 3, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("menuList", ctx_r0.menuList);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx_r0.selectedMenu);
  }
}
class FoodBreakSettingsFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(spinnerService, menuService, featureFlagService) {
    super();
    this.spinnerService = spinnerService;
    this.menuService = menuService;
    this.featureFlagService = featureFlagService;
    this.menuList = [];
    this.selectedMenu = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Menu();
    this.customMenuNameList = [];
    this.customMenuNames = src_app_sharedServices_menu_menu_custom_name__WEBPACK_IMPORTED_MODULE_1__.CUSTOM_MENU_NAME_OPTIONS;
  }
  ngOnInit() {
    this.featureFlagService.getFlag(src_constants__WEBPACK_IMPORTED_MODULE_2__.FeatureFlags.showCustomMenuName, false).then(res => {
      this.showCustomMenuPicker = res;
    });
    this.customMenuNames.forEach((menuName, i) => this.customMenuNameList.push({
      key: i.toString(),
      value: menuName
    }));
    this._createForm();
  }
  ngOnChanges(changes) {
    if (changes.schoolId?.currentValue) {
      this.getMenus(this.schoolId);
    }
  }
  _createForm() {
    const menuIsDisabled = !this.selectedMenu?.IsActive;
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
      menuIsDisabled: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(menuIsDisabled),
      customMenuName: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null),
      cutOffTime: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null),
      breakTime: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null) // will be set via api result
    });
  }

  saveFoodBreakSettings() {
    const request = this.getFoodBreakSettingsRequest();
    this.selectedMenu.IsActive = !this.menuIsDisabled.value;
    this.spinnerService.start();
    this.menuService.UpdateFoodBreakSettingsAPI(request).subscribe({
      next: () => {
        this.selectedMenu.IsActive = request.IsActive;
        this.selectedMenu.FriendlyName = request.FriendlyName;
        this.selectedMenu.CutOffTime = new Date();
        this.selectedMenu.CutOffTime.setHours(parseInt(this.cutOffTime.value.split(':')[0]));
        this.selectedMenu.CutOffTime.setMinutes(parseInt(this.cutOffTime.value.split(':')[1]));
        this.selectedMenu.BreakTime = request.BreakTime;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  getFoodBreakSettingsRequest() {
    const customMenuName = this.findMenuNameKeyValueByKey(this.customMenuName.value);
    const request = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.FoodBreakSettings();
    request.MenuId = this.selectedMenu.MenuId;
    request.CutOffTime = this.cutOffTime.value;
    request.BreakTime = this.breakTime.value;
    request.IsActive = !this.menuIsDisabled.value;
    request.FriendlyName = customMenuName.value;
    return request;
  }
  get menuIsDisabled() {
    return this.form.get('menuIsDisabled');
  }
  get customMenuName() {
    return this.form.get('customMenuName');
  }
  get cutOffTime() {
    return this.form.get('cutOffTime');
  }
  get breakTime() {
    return this.form.get('breakTime');
  }
  isStringValue(obj) {
    return typeof obj === 'string';
  }
  setSelectedMenu(selectedMenu) {
    this.selectedMenu = selectedMenu;
    const canteenCutOffTime = this.isStringValue(this.selectedMenu.CutOffTime) ? this.selectedMenu.CutOffTime.toString() : this.calculateCutOffTime(this.selectedMenu.CutOffTime);
    const breakTime = this.selectedMenu.BreakTime;
    this.breakTime.setValue(breakTime);
    this.cutOffTime.setValue(canteenCutOffTime);
    const friendlyMenuName = this.findMenuNameKeyValueByName(this.selectedMenu.FriendlyName);
    this.customMenuName.setValue(friendlyMenuName.key);
    this.menuIsDisabled.setValue(!this.selectedMenu.IsActive);
  }
  findMenuNameKeyValueByName(name) {
    return this.customMenuNameList.find(x => {
      return x.value === name;
    });
  }
  findMenuNameKeyValueByKey(key) {
    return this.customMenuNameList.find(x => {
      return x.key === key;
    });
  }
  calculateCutOffTime(cutOffTime) {
    const hours = cutOffTime.getHours().toString().padStart(2, '0');
    const minutes = cutOffTime.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
  //triggered by new school
  getMenus(schoolId) {
    this.menuService.GetConfiguredMenuBySchoolAPI(this.merchantId, schoolId).subscribe({
      next: menuListItems => {
        this.clearMenuList();
        this.menuList = menuListItems.Menus;
      },
      error: error => {
        this.handleErrorFromService(error);
      }
    });
  }
  clearMenuList() {
    this.menuList = [];
  }
  hasMenu() {
    return this.menuList?.length > 0;
  }
  static {
    this.ɵfac = function FoodBreakSettingsFormComponent_Factory(t) {
      return new (t || FoodBreakSettingsFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.MenuService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.FeatureFlagService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: FoodBreakSettingsFormComponent,
      selectors: [["food-break-settings-form"]],
      inputs: {
        schoolId: "schoolId",
        merchantId: "merchantId"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵNgOnChangesFeature"]],
      decls: 5,
      vars: 1,
      consts: [[1, "col-12"], [1, "optionTitle"], [4, "ngIf"], [1, "picker-container"], [3, "menuList", "menuSelectionChanged"], [3, "formGroup"], ["class", "picker-container", 4, "ngIf"], [1, "row", "mt-3"], [1, "col-4"], [1, "mt-2", "mb-2"], ["appearance", "outline"], ["matInput", "", "maxlength", "40", "formControlName", "cutOffTime", "type", "time"], ["matInput", "", "maxlength", "40", "formControlName", "breakTime", "type", "time"], ["formControlName", "menuIsDisabled", 1, "checkbox", 3, "checked"], ["text", "Save", 3, "onPress"], ["formControlName", "customMenuName", "placeholder", "Select menu name", 3, "values"]],
      template: function FoodBreakSettingsFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0)(1, "h3", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "Food Break Settings");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](3, "br");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, FoodBreakSettingsFormComponent_div_4_Template, 4, 2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.hasMenu());
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _schools_form_components_input_select_list_input_select_list_component__WEBPACK_IMPORTED_MODULE_4__.InputSelectListComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.MaxLengthValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControlName, _shared_tools_components_menu_list_menu_list_component__WEBPACK_IMPORTED_MODULE_5__.MenuListComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__.BasicButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatLabel, _angular_material_checkbox__WEBPACK_IMPORTED_MODULE_11__.MatCheckbox, _angular_material_input__WEBPACK_IMPORTED_MODULE_12__.MatInput],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n\n.picker-container[_ngcontent-%COMP%] {\n  width: 300px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 31096:
/*!******************************************************!*\
  !*** ./src/app/canteen-settings/components/index.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountSettingsComponent: () => (/* reexport safe */ _account_settings_account_settings_component__WEBPACK_IMPORTED_MODULE_1__.AccountSettingsComponent),
/* harmony export */   AddSchoolClassComponent: () => (/* reexport safe */ _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_7__.AddSchoolClassComponent),
/* harmony export */   AdminSettingsComponent: () => (/* reexport safe */ _admin_settings_admin_settings_component__WEBPACK_IMPORTED_MODULE_2__.AdminSettingsComponent),
/* harmony export */   AllergyAlertFormComponent: () => (/* reexport safe */ _allergy_alert_form_allergy_alert_form_component__WEBPACK_IMPORTED_MODULE_12__.AllergyAlertFormComponent),
/* harmony export */   ClassesListComponent: () => (/* reexport safe */ _classes_list_classes_list_component__WEBPACK_IMPORTED_MODULE_5__.ClassesListComponent),
/* harmony export */   DietaryLabelsFormComponent: () => (/* reexport safe */ _dietary_labels_form_dietary_labels_form_component__WEBPACK_IMPORTED_MODULE_9__.DietaryLabelsFormComponent),
/* harmony export */   FoodBreakSettingsFormComponent: () => (/* reexport safe */ _food_break_settings_form_food_break_settings_form_component__WEBPACK_IMPORTED_MODULE_17__.FoodBreakSettingsFormComponent),
/* harmony export */   ManageCategoriesComponent: () => (/* reexport safe */ _manage_categories_manage_categories_component__WEBPACK_IMPORTED_MODULE_4__.ManageCategoriesComponent),
/* harmony export */   MenuCategoriesFormComponent: () => (/* reexport safe */ _menu_categories_form_menu_categories_form_component__WEBPACK_IMPORTED_MODULE_10__.MenuCategoriesFormComponent),
/* harmony export */   MenuSettingsComponent: () => (/* reexport safe */ _menu_settings_menu_settings_component__WEBPACK_IMPORTED_MODULE_3__.MenuSettingsComponent),
/* harmony export */   OpeningDaysFormComponent: () => (/* reexport safe */ _opening_days_form_opening_days_form_component__WEBPACK_IMPORTED_MODULE_8__.OpeningDaysFormComponent),
/* harmony export */   OrderAdvanceFormComponent: () => (/* reexport safe */ _order_advance_form_order_advance_form_component__WEBPACK_IMPORTED_MODULE_11__.OrderAdvanceFormComponent),
/* harmony export */   PopupCategoryFormComponent: () => (/* reexport safe */ _manage_categories_popup_category_form_component__WEBPACK_IMPORTED_MODULE_6__.PopupCategoryFormComponent),
/* harmony export */   PrinterOptionsFormComponent: () => (/* reexport safe */ _printer_options_form_printer_options_form_component__WEBPACK_IMPORTED_MODULE_13__.PrinterOptionsFormComponent),
/* harmony export */   SchoolCloseDateTableComponent: () => (/* reexport safe */ _school_close_date_table_school_close_date_table_component__WEBPACK_IMPORTED_MODULE_14__.SchoolCloseDateTableComponent),
/* harmony export */   SchoolClosingDateFormComponent: () => (/* reexport safe */ _school_closing_date_form_school_closing_date_form_component__WEBPACK_IMPORTED_MODULE_15__.SchoolClosingDateFormComponent),
/* harmony export */   SchoolSettingsComponent: () => (/* reexport safe */ _school_settings_school_settings_component__WEBPACK_IMPORTED_MODULE_0__.SchoolSettingsComponent),
/* harmony export */   SettingsCheckboxListComponent: () => (/* reexport safe */ _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_16__.SettingsCheckboxListComponent)
/* harmony export */ });
/* harmony import */ var _school_settings_school_settings_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./school-settings/school-settings.component */ 33626);
/* harmony import */ var _account_settings_account_settings_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./account-settings/account-settings.component */ 6162);
/* harmony import */ var _admin_settings_admin_settings_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./admin-settings/admin-settings.component */ 70014);
/* harmony import */ var _menu_settings_menu_settings_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./menu-settings/menu-settings.component */ 87070);
/* harmony import */ var _manage_categories_manage_categories_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manage-categories/manage-categories.component */ 91254);
/* harmony import */ var _classes_list_classes_list_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./classes-list/classes-list.component */ 70846);
/* harmony import */ var _manage_categories_popup_category_form_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./manage-categories/popup-category-form.component */ 71634);
/* harmony import */ var _add_school_class_add_school_class_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./add-school-class/add-school-class.component */ 85398);
/* harmony import */ var _opening_days_form_opening_days_form_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./opening-days-form/opening-days-form.component */ 22958);
/* harmony import */ var _dietary_labels_form_dietary_labels_form_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./dietary-labels-form/dietary-labels-form.component */ 62070);
/* harmony import */ var _menu_categories_form_menu_categories_form_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./menu-categories-form/menu-categories-form.component */ 32006);
/* harmony import */ var _order_advance_form_order_advance_form_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./order-advance-form/order-advance-form.component */ 71072);
/* harmony import */ var _allergy_alert_form_allergy_alert_form_component__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./allergy-alert-form/allergy-alert-form.component */ 49848);
/* harmony import */ var _printer_options_form_printer_options_form_component__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./printer-options-form/printer-options-form.component */ 80556);
/* harmony import */ var _school_close_date_table_school_close_date_table_component__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./school-close-date-table/school-close-date-table.component */ 68070);
/* harmony import */ var _school_closing_date_form_school_closing_date_form_component__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./school-closing-date-form/school-closing-date-form.component */ 83276);
/* harmony import */ var _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./settings-checkbox-list/settings-checkbox-list.component */ 76504);
/* harmony import */ var _food_break_settings_form_food_break_settings_form_component__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./food-break-settings-form/food-break-settings-form.component */ 81014);



















/***/ }),

/***/ 91254:
/*!**********************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/manage-categories/manage-categories.component.ts ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ManageCategoriesComponent: () => (/* binding */ ManageCategoriesComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var _popup_category_form_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./popup-category-form.component */ 71634);
/* harmony import */ var src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/components/dialog-confirm/dialog-confirm.component */ 26645);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../manage-order/components/category-icon/category-icon.component */ 70900);
// Models



// component













function ManageCategoriesComponent_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "tr")(1, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](3, "td", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](4, "category-icon", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](5, "td", 13)(6, "mat-icon", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function ManageCategoriesComponent_tr_18_Template_mat_icon_click_6_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r3);
      const cat_r1 = restoredCtx.$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r2.ArchiveClicked(cat_r1));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](7, "delete");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](8, "mat-icon", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function ManageCategoriesComponent_tr_18_Template_mat_icon_click_8_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r3);
      const cat_r1 = restoredCtx.$implicit;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r4.OpenDialog(cat_r1));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](9, "edit");
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const cat_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtextInterpolate"](cat_r1.CategoryName);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("iconName", cat_r1.CategoryUrl);
  }
}
class ManageCategoriesComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_location, route, dialog, menueditorService, spinnerService, store) {
    super();
    this._location = _location;
    this.route = route;
    this.dialog = dialog;
    this.menueditorService = menueditorService;
    this.spinnerService = spinnerService;
    this.store = store;
    this.listCategories = [];
    this.listItems = [];
  }
  ngOnInit() {
    this.route.data.subscribe(data => {
      this.listCategories = data['categories'];
    });
    this.subscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_9__.select)(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_1__.selectedCanteen)).subscribe(selectedCanteen => {
      this.canteen = selectedCanteen;
    });
  }
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
  GoBackClick() {
    this._location.back();
  }
  NewCategoryClick() {
    let request = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ItemCategory();
    request.SortOrder = 1;
    this.OpenDialog(request);
  }
  ArchiveClicked(cat) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ConfirmModal();
    // Check here it items exist in category before allowing delete action.
    this.menueditorService.GetItemsByCategoryIdAPI(cat.MenuCategoryId).subscribe({
      next: response => {
        this.listItems = response;
        if (this.listItems.length > 0) {
          data.Title = 'Delete Category';
          data.Text = 'Deleting this category is not possible because there are still items in this category. Please remove all items and try again.';
          data.ConfirmButton = 'Ok';
        } else {
          data.Title = 'Delete Category';
          data.Text = 'Deleting this category will inactivate the category permanently and can not be undone. Proceed?';
          data.CancelButton = 'No';
          data.ConfirmButton = 'Yes';
        }
        const dialogRef = this.dialog.open(src_app_shared_components_dialog_confirm_dialog_confirm_component__WEBPACK_IMPORTED_MODULE_3__.DialogConfirmComponent, {
          width: '500px',
          disableClose: true,
          data: data
        });
        dialogRef.afterClosed().subscribe(result => {
          if (result && this.listItems.length == 0) {
            this.ArchiveClickConfirmed(cat);
          }
        });
      },
      error: error => {
        this.handleErrorFromService(error);
      }
    });
  }
  ArchiveClickConfirmed(cat) {
    this.spinnerService.start();
    this.menueditorService.ArchiveCategoryAPI(cat.MenuCategoryId).subscribe({
      next: response => {
        this.RefreshCategories();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  OpenDialog(category) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.PopupCategoryModel();
    data.Canteen = this.canteen;
    data.SelectedCategory = category;
    // open dialog
    let dialogRef;
    dialogRef = this.dialog.open(_popup_category_form_component__WEBPACK_IMPORTED_MODULE_2__.PopupCategoryFormComponent, {
      width: '1000px',
      maxHeight: '95vh',
      disableClose: false,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // refresh categories
        this.RefreshCategories();
      }
    });
  }
  RefreshCategories() {
    this.spinnerService.start();
    this.menueditorService.GetCategoriesForEditorByCanteenIdAPI(this.canteen.CanteenId).subscribe({
      next: response => {
        this.listCategories = response;
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  static {
    this.ɵfac = function ManageCategoriesComponent_Factory(t) {
      return new (t || ManageCategoriesComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_10__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_11__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_12__.MatDialog), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_4__.MenuEditorApiService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_4__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_9__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineComponent"]({
      type: ManageCategoriesComponent,
      selectors: [["app-manage-categories"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵInheritDefinitionFeature"]],
      decls: 19,
      vars: 1,
      consts: [[1, "col-12"], ["text", "Go Back", 1, "backButton", 3, "navBack"], [1, "schoolSelection", "printerSection"], [1, "row"], [1, "col-8"], [1, "optionTitle"], [1, "col-4"], [1, "headerAddCategory"], ["text", "Add New Category", 3, "onPress"], [1, "tableCat"], [4, "ngFor", "ngForOf"], [1, "catLine"], [3, "iconName"], [1, "catLine", 2, "text-align", "right"], ["matTooltip", "Delete", 1, "iconTable", 3, "click"], ["matTooltip", "Edit", 1, "iconTable", 3, "click"]],
      template: function ManageCategoriesComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div", 0)(1, "nav-back-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("navBack", function ManageCategoriesComponent_Template_nav_back_button_navBack_1_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "h3", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](6, "Menu Categories");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](7, "div", 6)(8, "div", 7)(9, "basic-button", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("onPress", function ManageCategoriesComponent_Template_basic_button_onPress_9_listener() {
            return ctx.NewCategoryClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](10, "table", 9)(11, "thead")(12, "tr")(13, "th");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](14, "Category");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](15, "th");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](16, "Image");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](17, "th");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](18, ManageCategoriesComponent_tr_18_Template, 10, 2, "tr", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngForOf", ctx.listCategories);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.NgForOf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_5__.NavBackButtonComponent, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_6__.BasicButtonComponent, _angular_material_icon__WEBPACK_IMPORTED_MODULE_13__.MatIcon, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_14__.MatTooltip, _manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_7__.CategoryIconComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: orange;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.tableCat[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-top: 10px;\n  padding-bottom: 80px;\n  background-color: #ffffff;\n}\n.tableCat[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  color: #88949f;\n  text-align: left;\n  padding-left: 20px;\n  padding-top: 10px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #dadada;\n}\n\n.catLine[_ngcontent-%COMP%] {\n  padding: 16px 0px 16px 20px;\n  border-bottom: 1px solid #dadada;\n}\n\n.iconTable[_ngcontent-%COMP%] {\n  margin-right: 20px;\n  cursor: pointer;\n}\n\n.closePopupIcon[_ngcontent-%COMP%] {\n  text-align: center;\n  padding-top: 20px;\n}\n\n.imageBlock[_ngcontent-%COMP%] {\n  padding: 4px;\n  margin: 4px;\n  overflow: hidden;\n  border-radius: 12px;\n  border: 2px solid transparent;\n  cursor: pointer;\n}\n.imageBlock.selected[_ngcontent-%COMP%] {\n  border-color: #ff9e00;\n}\n.imageBlock[_ngcontent-%COMP%]   .imageWrapper[_ngcontent-%COMP%] {\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n}\n.imageBlock[_ngcontent-%COMP%]   .imageWrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: auto;\n  cursor: pointer;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  border: 1px solid #ff9e00;\n  color: #ff9e00;\n  margin-right: 15px;\n  font-size: 16px;\n  border-radius: 14px;\n}\n\n.headerAddCategory[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 71634:
/*!************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/manage-categories/popup-category-form.component.ts ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PopupCategoryFormComponent: () => (/* binding */ PopupCategoryFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_states_canteen_canteen_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/states/canteen/canteen.actions */ 41219);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var src_app_manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../manage-order/components/category-icon/category-icon.component */ 70900);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_basic_form_buttons_basic_form_buttons_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-form/components/basic-form-buttons/basic-form-buttons.component */ 4612);
/* harmony import */ var _shared_tools_components_modal_header_modal_header_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../shared-tools/components/modal-header/modal-header.component */ 20951);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/material/input */ 95541);

// Models


















const _c0 = function (a0) {
  return {
    selected: a0
  };
};
function PopupCategoryFormComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("click", function PopupCategoryFormComponent_div_14_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵrestoreView"](_r3);
      const name_r1 = restoredCtx.$implicit;
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵresetView"](ctx_r2.imageSelection(name_r1));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](1, "category-icon", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const name_r1 = ctx.$implicit;
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵpureFunction1"](2, _c0, ctx_r0.isSelected(name_r1)));
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("iconName", name_r1);
  }
}
class PopupCategoryFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(dialogRef, data, menuEditorService, store, dialog) {
    super();
    this.dialogRef = dialogRef;
    this.data = data;
    this.menuEditorService = menuEditorService;
    this.store = store;
    this.dialog = dialog;
    this.listImages = [];
    this.selectedImage = null;
    this.title = 'New Category';
    this.buttonLoading = false;
    this.categoryIcon = new src_app_manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_4__.CategoryIconComponent();
  }
  ngOnInit() {
    //create deep copy of selected category to stop data being updated in parent component
    this.selectedCategory = {
      ...this.data.SelectedCategory
    };
    this.listImages = this.getCategoryIconsForMerchantType();
    if (this.selectedCategory?.MenuCategoryId > 0) {
      this.title = 'Edit Category';
      this.imageSelection(this.selectedCategory.CategoryUrl);
    }
    this.createForm();
  }
  getCategoryIconsForMerchantType() {
    const isUniformMerchant = this.data.Canteen.CanteenType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Uniform;
    return isUniformMerchant ? src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.UNIFORM_CATEGORY_ICON_ARRAY : src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CANTEEN_CATEGORY_ICON_ARRAY;
  }
  getUrlCategory(imageName) {
    return src_environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.blobStorage + src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ImageUrlEnum.MenusSM + imageName;
  }
  closeDialog(reload) {
    this.dialogRef.close(reload);
  }
  disableSave() {
    return this.form.invalid || !Boolean(this.selectedImage);
  }
  createForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormGroup({
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this.selectedCategory.CategoryName, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.required),
      sortOrder: new _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControl(this.selectedCategory.SortOrder, [_angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.Validators.min(1)])
    });
  }
  get name() {
    return this.form.get('name');
  }
  get sortOrder() {
    return this.form.get('sortOrder');
  }
  isSelected(image) {
    return image == this.selectedImage;
  }
  imageSelection(image) {
    this.selectedImage = image ? this.categoryIcon.getImageNameFromFileName(image) : null;
  }
  onSubmit() {
    this.buttonLoading = true;
    if (this.selectedCategory.MenuCategoryId > 0) {
      this.updateCategory();
      return;
    }
    this.insertCategory();
  }
  getImageFileName() {
    return `${this.selectedImage}.jpg`;
  }
  updateCategory() {
    const request = {
      CategoryName: this.name.value,
      SortOrder: this.sortOrder.value,
      CategoryUrl: this.getImageFileName(),
      CategoryId: this.selectedCategory.MenuCategoryId
    };
    this.menuEditorService.UpdateCategoryAPI(request).subscribe({
      next: response => {
        this.apiSuccessAction();
      },
      error: error => {
        this.apiErrorAction(error, 'Editing');
      }
    });
  }
  insertCategory() {
    const request = {
      CategoryName: this.name.value,
      SortOrder: this.sortOrder.value,
      CategoryUrl: this.getImageFileName(),
      CanteenId: this.data.Canteen.CanteenId
    };
    this.menuEditorService.InsertCategoryAPI(request).subscribe({
      next: response => {
        this.apiSuccessAction();
      },
      error: error => {
        this.apiErrorAction(error, 'Creating');
      }
    });
  }
  apiSuccessAction() {
    this.buttonLoading = false;
    this.closeDialog(true);
    this.store.dispatch((0,src_app_states_canteen_canteen_actions__WEBPACK_IMPORTED_MODULE_2__.LoadMenuCategories)());
  }
  apiErrorAction(error, keyword) {
    this.buttonLoading = false;
    this.showErrorDialog(keyword);
    this.handleErrorFromService(error);
  }
  showErrorDialog(keyWord) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ConfirmModal();
    data.Title = `Something went wrong`;
    data.Text = `${keyWord} this category was unsuccessful. Please try again.`;
    data.ConfirmButton = 'Ok';
    this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_3__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
  }
  static {
    this.ɵfac = function PopupCategoryFormComponent_Factory(t) {
      return new (t || PopupCategoryFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MatDialogRef), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MAT_DIALOG_DATA), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_5__.MenuEditorApiService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_11__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineComponent"]({
      type: PopupCategoryFormComponent,
      selectors: [["popup-category-form"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵInheritDefinitionFeature"]],
      decls: 18,
      vars: 5,
      consts: [[3, "title", "close"], [1, "row"], [1, "col-6"], [1, "cashlessForm", 3, "formGroup"], ["appearance", "outline"], ["matInput", "", "placeholder", "Enter category name", "formControlName", "name", "type", "text"], ["matInput", "", "formControlName", "sortOrder", "type", "number", "min", "1"], ["class", "imageBlock", 3, "ngClass", "click", 4, "ngFor", "ngForOf"], [1, "row", "pt-4"], [1, "col-12"], [3, "disableSaveButton", "loading", "saveEvent", "cancelEvent"], [1, "imageBlock", 3, "ngClass", "click"], [3, "iconName"]],
      template: function PopupCategoryFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](0, "mat-dialog-content")(1, "modal-header", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("close", function PopupCategoryFormComponent_Template_modal_header_close_1_listener() {
            return ctx.closeDialog(false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](2, "div", 1)(3, "div", 2)(4, "form", 3)(5, "mat-form-field", 4)(6, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](7, "Category");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](8, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](9, "mat-form-field", 4)(10, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtext"](11, "Display Order");
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelement"](12, "input", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](13, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵtemplate"](14, PopupCategoryFormComponent_div_14_Template, 2, 4, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementStart"](15, "div", 8)(16, "div", 9)(17, "basic-form-buttons", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵlistener"]("saveEvent", function PopupCategoryFormComponent_Template_basic_form_buttons_saveEvent_17_listener() {
            return ctx.onSubmit();
          })("cancelEvent", function PopupCategoryFormComponent_Template_basic_form_buttons_cancelEvent_17_listener() {
            return ctx.closeDialog(false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("title", ctx.title);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("ngForOf", ctx.listImages);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵproperty"]("disableSaveButton", ctx.disableSave())("loading", ctx.buttonLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_12__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_12__.NgForOf, _schools_form_components_basic_form_buttons_basic_form_buttons_component__WEBPACK_IMPORTED_MODULE_6__.BasicFormButtonsComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_9__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NumberValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.MinValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormControlName, _shared_tools_components_modal_header_modal_header_component__WEBPACK_IMPORTED_MODULE_7__.ModalHeaderComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.MatLabel, _angular_material_input__WEBPACK_IMPORTED_MODULE_14__.MatInput, _angular_material_dialog__WEBPACK_IMPORTED_MODULE_10__.MatDialogContent, src_app_manage_order_components_category_icon_category_icon_component__WEBPACK_IMPORTED_MODULE_4__.CategoryIconComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: orange;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.tableCat[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-top: 10px;\n  padding-bottom: 80px;\n  background-color: #ffffff;\n}\n.tableCat[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  color: #88949f;\n  text-align: left;\n  padding-left: 20px;\n  padding-top: 10px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #dadada;\n}\n\n.catLine[_ngcontent-%COMP%] {\n  padding: 16px 0px 16px 20px;\n  border-bottom: 1px solid #dadada;\n}\n\n.iconTable[_ngcontent-%COMP%] {\n  margin-right: 20px;\n  cursor: pointer;\n}\n\n.closePopupIcon[_ngcontent-%COMP%] {\n  text-align: center;\n  padding-top: 20px;\n}\n\n.imageBlock[_ngcontent-%COMP%] {\n  padding: 4px;\n  margin: 4px;\n  overflow: hidden;\n  border-radius: 12px;\n  border: 2px solid transparent;\n  cursor: pointer;\n}\n.imageBlock.selected[_ngcontent-%COMP%] {\n  border-color: #ff9e00;\n}\n.imageBlock[_ngcontent-%COMP%]   .imageWrapper[_ngcontent-%COMP%] {\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n}\n.imageBlock[_ngcontent-%COMP%]   .imageWrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: auto;\n  cursor: pointer;\n}\n\n.cancelButton[_ngcontent-%COMP%] {\n  border: 1px solid #ff9e00;\n  color: #ff9e00;\n  margin-right: 15px;\n  font-size: 16px;\n  border-radius: 14px;\n}\n\n.headerAddCategory[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      changeDetection: 0
    });
  }
}

/***/ }),

/***/ 32006:
/*!****************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/menu-categories-form/menu-categories-form.component.ts ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MenuCategoriesFormComponent: () => (/* binding */ MenuCategoriesFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/states/canteen/canteen.selectors */ 80974);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);

//ngrx







function MenuCategoriesFormComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 6)(1, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const cat_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](cat_r1.CategoryName);
  }
}
class MenuCategoriesFormComponent {
  constructor(store, router) {
    this.store = store;
    this.router = router;
    this.updateSchool = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
    this.listCategories = [];
  }
  ngOnInit() {
    this.canteenSubscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.select)(src_app_states_canteen_canteen_selectors__WEBPACK_IMPORTED_MODULE_0__.canteenStateSelector)).subscribe(state => {
      if (state.selected) {
        this.listCategories = state.menuCategories;
      }
    });
  }
  ngOnDestroy() {
    this.canteenSubscription.unsubscribe();
  }
  editCategories() {
    this.router.navigate(['canteen/settings/menu/categories']);
  }
  static {
    this.ɵfac = function MenuCategoriesFormComponent_Factory(t) {
      return new (t || MenuCategoriesFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_3__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: MenuCategoriesFormComponent,
      selectors: [["menu-categories-form"]],
      outputs: {
        updateSchool: "updateSchool"
      },
      decls: 7,
      vars: 1,
      consts: [[1, "col-12"], [1, "optionTitle"], [1, "row"], ["class", "col-4", 4, "ngFor", "ngForOf"], [1, "buttonWrapper", "pt-2"], ["text", "Edit", 3, "onPress"], [1, "col-4"]],
      template: function MenuCategoriesFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "h3", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2, "Menu Categories");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](4, MenuCategoriesFormComponent_div_4_Template, 3, 1, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 4)(6, "basic-button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("onPress", function MenuCategoriesFormComponent_Template_basic_button_onPress_6_listener() {
            return ctx.editCategories();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.listCategories);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__.BasicButtonComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2FudGVlbi1zZXR0aW5ncy9jb21wb25lbnRzL21lbnUtY2F0ZWdvcmllcy1mb3JtL21lbnUtY2F0ZWdvcmllcy1mb3JtLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vc3JjL2FwcC9jYW50ZWVuLXNldHRpbmdzL2NvbXBvbmVudHMvY2FudGVlbi1zZXR0aW5ncy5zY3NzIiwid2VicGFjazovLy4vc3JjL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUtBO0VBQ0UsYUFBQTtBQ0pGO0FES0U7RUFGRjtJQUdJLGNBQUE7RUNGRjtBQUNGOztBREtBO0VBQ0UsYUFBQTtBQ0ZGO0FER0U7RUFGRjtJQUdJLGNBQUE7RUNBRjtBQUNGOztBQ2RBO0VBQ0UseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0FEaUJGOztBQ2RBO0VBQ0Usa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxTQUFBO0FEaUJGOztBQ2RBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0FEaUJGOztBQ2RBO0VBQ0Usb0JBQUE7RUFDQSxnQkFBQTtBRGlCRjs7QUNkQTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FEaUJGOztBQ2RBO0VBQ0UsY0FBQTtBRGlCRjs7QUNkQTtFQUNFLG1CQ2hCUztFRGlCVCxpQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNEJBQUE7RUFDQSxjQUFBO0FEaUJGOztBQ2RBO0VBQ0UsY0FBQTtFQUNBLG1CQ0xPO0FGc0JUIiwic291cmNlc0NvbnRlbnQiOlsiJGJyZWFrcG9pbnQtc206IDU3NnB4O1xuJGJyZWFrcG9pbnQtbWQ6IDc2N3B4O1xuJGJyZWFrcG9pbnQtbGc6IDk5MnB4O1xuJGJyZWFrcG9pbnQteGw6IDEyMDBweDtcblxuLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWF4LXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4vLyBOT1RFIEN1cnJlbnRseSB0YWJsZXQgYW5kIG1vYmlsZSBpcyB0aGUgc2FtZS4gQ2hhbmdlIHRvICRicmVha3BvaW50LWxnIGxhdGVyIGlmIHdlIGdldCBhIHByb3BlciB0YWJsZXQgZGVzaWduLlxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuIiwiLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG59XG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLm1vYmlsZSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cblxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDc2N3B4KSB7XG4gIC5kZXNrdG9wIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuXG4ub3B0aW9uc1dyYXBwZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBwYWRkaW5nOiAyMHB4O1xuICBtYXJnaW46IDIwcHggMDtcbn1cblxuLm9wdGlvblRpdGxlIHtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgZm9udC1zaXplOiAxOHB4O1xuICBsaW5lLWhlaWdodDogMjBweDtcbiAgY29sb3I6ICMwMDAwMDA7XG4gIG1hcmdpbjogMDtcbn1cblxuLm9wdGlvbiB7XG4gIG1hcmdpbi10b3A6IDA7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7XG59XG5cbi5vcHRpb25zV3JhcHBlckxpc3Qge1xuICBwYWRkaW5nLWJvdHRvbTogMTVweDtcbiAgbWFyZ2luLXRvcDogMTVweDtcbn1cblxuLmNoZWNrYm94IHtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogbm9ybWFsO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAyMHB4O1xuICBjb2xvcjogIzAwMDAwMDtcbn1cblxuLmJ1dHRvbldyYXBwZXIge1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLmJ1dHRvbiB7XG4gIGJhY2tncm91bmQ6ICNmZjdhMDA7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBmb250LXNpemU6IDIwcHg7XG4gIGxpbmUtaGVpZ2h0OiAyMnB4O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDExcHggMjNweCAxNHB4IDIzcHg7XG4gIGNvbG9yOiAjZmZmZmZmO1xufVxuXG4uYnV0dG9uOmRpc2FibGVkIHtcbiAgY29sb3I6ICNmZmZmZmY7XG4gIGJhY2tncm91bmQ6ICNlMGUwZTA7XG59IiwiQGltcG9ydCAnLi4vLi4vLi4vc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MnO1xuXG4ub3B0aW9uc1dyYXBwZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBwYWRkaW5nOiAyMHB4O1xuICBtYXJnaW46IDIwcHggMDtcbn1cblxuLm9wdGlvblRpdGxlIHtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgZm9udC1zaXplOiAxOHB4O1xuICBsaW5lLWhlaWdodDogMjBweDtcbiAgY29sb3I6ICMwMDAwMDA7XG4gIG1hcmdpbjogMDtcbn1cblxuLm9wdGlvbiB7XG4gIG1hcmdpbi10b3A6IDA7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7XG59XG5cbi5vcHRpb25zV3JhcHBlckxpc3Qge1xuICBwYWRkaW5nLWJvdHRvbTogMTVweDtcbiAgbWFyZ2luLXRvcDogMTVweDtcbn1cblxuLmNoZWNrYm94IHtcbiAgZm9udC1zdHlsZTogbm9ybWFsO1xuICBmb250LXdlaWdodDogbm9ybWFsO1xuICBmb250LXNpemU6IDE4cHg7XG4gIGxpbmUtaGVpZ2h0OiAyMHB4O1xuICBjb2xvcjogIzAwMDAwMDtcbn1cblxuLmJ1dHRvbldyYXBwZXIge1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLmJ1dHRvbiB7XG4gIGJhY2tncm91bmQ6ICRvcmFuZ2UtMztcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIGZvbnQtc2l6ZTogMjBweDtcbiAgbGluZS1oZWlnaHQ6IDIycHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZzogMTFweCAyM3B4IDE0cHggMjNweDtcbiAgY29sb3I6ICNmZmZmZmY7XG59XG5cbi5idXR0b246ZGlzYWJsZWQge1xuICBjb2xvcjogI2ZmZmZmZjtcbiAgYmFja2dyb3VuZDogJGdyZXktOTtcbn1cbiIsIkBpbXBvcnQgJ2Nhc2hsZXNzLWJyZWFrcG9pbnRzJztcblxuLy8gUHJpbWFyeSBjb2xvdXJzXG5cbiRibHVlLTE6ICMxZWEzY2U7XG4kYmx1ZS0yOiAjNDc1NGIwO1xuJGJsdWUtMzogIzE0NGNkYztcblxuJHJlZC0xOiAjZjE0NzYyO1xuJHJlZC0yOiAjZmZlYmViO1xuJHJlZC0zOiAjYzA0NTQ1O1xuJHJlZC00OiAjZmZjZmNjO1xuXG4kcHVycGxlLTE6ICM3ZjNkYjM7XG4kbmF2eS0xOiAjMWM0MjcwO1xuJGNoYXJjb2FsLTE6ICMzMzNiNDQ7XG5cbiRncmVlbi0xOiAjMDBiYTZiO1xuJGdyZWVuLTI6ICNkOWY1ZTk7XG4kZ3JlZW4tMzogIzAwNmY0OTtcbiRncmVlbi00OiAjZTNmNWVmO1xuJGdyZWVuLTU6ICNkZmZmZjA7XG5cbi8vIE9yYW5nZVxuJG9yYW5nZS0xOiAjZmY5ZTAwO1xuJG9yYW5nZS0zOiAjZmY3YTAwO1xuJG9yYW5nZS0yOiAjZmY0YjE3O1xuJG9yYW5nZS00OiAjZmZlMmM3O1xuJG9yYW5nZS01OiAjZmZmMmU2O1xuJG9yYW5nZS02OiAjZmY4MDAwO1xuJG9yYW5nZS03OiAjZmZlYWQ2O1xuJG9yYW5nZS04OiAjZmVmMGUwO1xuJG9yYW5nZS05OiAjZmZmMGUwO1xuJG9yYW5nZS0xMDogI2YzNjYwMDtcbiRvcmFuZ2UtMTE6ICNmZmUzYmI7XG4kbW9iaWxlLWRhcmstb3JhbmdlOiAjRDk1QjAwO1xuJG1vYmlsZS1saWdodC1vcmFuZ2U6ICNGRkVBRDY7XG5cbi8vIGdyZXlcbiRncmV5LTE6ICM4ODk0OWY7XG4kZ3JleS0yOiAjZTBlMWUyO1xuJGdyZXktMzogI2RkZGRkZDtcbiRncmV5LTQ6ICNmMmYyZjI7XG4kZ3JleS01OiAjMzMzYjQ0O1xuJGdyZXktNjogI2U1ZTVlNTtcbiRncmV5LTc6ICNiOWI5Yzg7XG4kZ3JleS04OiAjODc4Nzg3O1xuJGdyZXktOTogI2UwZTBlMDtcbiRncmV5LTEwOiAjYmRiZGJkO1xuJGdyZXktMTE6ICM4MjgyODI7XG4kZ3JleS0xMjogIzFiMWYzOTtcbiRncmV5LTEzOiAjYjhiOGI4O1xuJGdyZXktMTQ6ICMyNzJjNTA7XG4kZ3JleS0xNTogI2Y2ZjVmMztcbiRncmV5LTE2OiAjZmFmOWY4O1xuJGdyZXktMTc6ICM2YjZjODk7XG5cbi8vIFNlY29uZGFyeSBjb2xvdXJzXG4kYmx1ZS1zZWNvbmRhcnktMTogcmdiYSgyNTUsIDI0MywgMjE5LCAxKTtcbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 87070:
/*!**************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/menu-settings/menu-settings.component.ts ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MenuSettingsComponent: () => (/* binding */ MenuSettingsComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared-tools/components/merchant-school-picker/merchant-school-picker.component */ 38237);
/* harmony import */ var _opening_days_form_opening_days_form_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../opening-days-form/opening-days-form.component */ 22958);
/* harmony import */ var _dietary_labels_form_dietary_labels_form_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dietary-labels-form/dietary-labels-form.component */ 62070);
/* harmony import */ var _menu_categories_form_menu_categories_form_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../menu-categories-form/menu-categories-form.component */ 32006);
/* harmony import */ var _order_advance_form_order_advance_form_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../order-advance-form/order-advance-form.component */ 71072);
/* harmony import */ var _allergy_alert_form_allergy_alert_form_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../allergy-alert-form/allergy-alert-form.component */ 49848);
/* harmony import */ var _printer_options_form_printer_options_form_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../printer-options-form/printer-options-form.component */ 80556);
/* harmony import */ var _school_closing_date_form_school_closing_date_form_component__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../school-closing-date-form/school-closing-date-form.component */ 83276);
/* harmony import */ var _food_break_settings_form_food_break_settings_form_component__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../food-break-settings-form/food-break-settings-form.component */ 81014);
//Models














function MenuSettingsComponent_div_8_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](1, "div", 8)(2, "opening-days-form", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("updateSchool", function MenuSettingsComponent_div_8_ng_container_1_Template_opening_days_form_updateSchool_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r3.updateSchool($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](3, "div", 8)(4, "dietary-labels-form", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("updateSchool", function MenuSettingsComponent_div_8_ng_container_1_Template_dietary_labels_form_updateSchool_4_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r4);
      const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r5.updateSchool($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("school", ctx_r1.selectedSchool);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("school", ctx_r1.selectedSchool);
  }
}
function MenuSettingsComponent_div_8_ng_container_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementContainerStart"](0)(1);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](2, "div", 8)(3, "order-advance-form", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("updateValue", function MenuSettingsComponent_div_8_ng_container_4_Template_order_advance_form_updateValue_3_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵresetView"](ctx_r6.updateWeeksPreOrder($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](4, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](5, "allergy-alert-form", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](6, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](7, "school-closing-date-form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](8, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](9, "food-break-settings-form", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](10, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](11, "printer-options-form", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("weekValue", ctx_r2.selectedSchool.WeeksPreOrder);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("schoolFeatures", ctx_r2.selectedSchool.SchoolFeatures)("schoolId", ctx_r2.selectedSchool.SchoolId);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("schoolId", ctx_r2.selectedSchool.SchoolId);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("merchantId", ctx_r2.selectedMerchant.CanteenId)("schoolId", ctx_r2.selectedSchool.SchoolId);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("printSettings", ctx_r2.printSettings);
  }
}
function MenuSettingsComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtemplate"](1, MenuSettingsComponent_div_8_ng_container_1_Template, 5, 2, "ng-container", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](2, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](3, "menu-categories-form");
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtemplate"](4, MenuSettingsComponent_div_8_ng_container_4_Template, 12, 7, "ng-container", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("ngIf", !ctx_r0.isEventMerchant);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("ngIf", !ctx_r0.isEventMerchant);
  }
}
class MenuSettingsComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_location, spinnerService, schoolService) {
    super();
    this._location = _location;
    this.spinnerService = spinnerService;
    this.schoolService = schoolService;
    this.listCanteens = [];
    this.selectedSchool = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.School();
    this.selectedMerchant = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Canteen();
    this.canteenListVisible = true;
  }
  GoBackClick() {
    this._location.back();
  }
  onMerchantSelect(merchant) {
    this.selectedMerchant = merchant;
    this.isEventMerchant = this.selectedMerchant.CanteenType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Event;
    //this.isUniformMerchant = this.selectedMerchant.CanteenType === MerchantTypeEnum.Uniform;
    this.isCanteenMerchant = this.selectedMerchant.CanteenType === src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.MerchantTypeEnum.Canteen;
  }
  onSchoolSelect(schoolId) {
    this.spinnerService.start();
    this.schoolService.GetSchoolByIdAPI(schoolId).subscribe(school => {
      this.selectedSchool = school;
      this.getPrintSettings(school);
      this.spinnerService.stop();
    });
  }
  getPrintSettings(school) {
    this.printSettings = {
      SchoolId: school.SchoolId,
      LabelPrintChoice: school.LabelPrintChoice,
      LabelTypeId: school.LabelTypeId,
      UsePrintingApp: school.UsePrintingApp
    };
  }
  updateWeeksPreOrder(weeksPreOrder) {
    this.selectedSchool.WeeksPreOrder = weeksPreOrder;
    this.updateSchool(this.selectedSchool);
  }
  updateSchool(data) {
    this.spinnerService.start();
    this.schoolService.UpsertSchoolApi(data).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  static {
    this.ɵfac = function MenuSettingsComponent_Factory(t) {
      return new (t || MenuSettingsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_13__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SchoolService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵdefineComponent"]({
      type: MenuSettingsComponent,
      selectors: [["menu-settings"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵInheritDefinitionFeature"]],
      decls: 9,
      vars: 1,
      consts: [[1, "col-12"], [1, "col-md-8", "col-sm-12"], ["text", "Go Back", 1, "backButton", 3, "navBack"], [1, "titleWrapper"], ["sizes", "24", "src", "assets/icons/bread.svg"], [1, "title"], [3, "merchantChanged", "schoolChanged"], [4, "ngIf"], [1, "optionsWrapper"], [3, "school", "updateSchool"], [3, "weekValue", "updateValue"], [3, "schoolFeatures", "schoolId"], [3, "schoolId"], [3, "merchantId", "schoolId"], [1, "schoolSelection", "printerSection"], [3, "printSettings"]],
      template: function MenuSettingsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "nav-back-button", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("navBack", function MenuSettingsComponent_Template_nav_back_button_navBack_2_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelement"](4, "img", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](5, "h1", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtext"](6, "Canteen Settings");
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementStart"](7, "merchant-school-picker", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵlistener"]("merchantChanged", function MenuSettingsComponent_Template_merchant_school_picker_merchantChanged_7_listener($event) {
            return ctx.onMerchantSelect($event);
          })("schoolChanged", function MenuSettingsComponent_Template_merchant_school_picker_schoolChanged_7_listener($event) {
            return ctx.onSchoolSelect($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵtemplate"](8, MenuSettingsComponent_div_8_Template, 5, 2, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_12__["ɵɵproperty"]("ngIf", ctx.selectedSchool.SchoolId);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__.NavBackButtonComponent, _shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_3__.MerchantSchoolPickerComponent, _opening_days_form_opening_days_form_component__WEBPACK_IMPORTED_MODULE_4__.OpeningDaysFormComponent, _dietary_labels_form_dietary_labels_form_component__WEBPACK_IMPORTED_MODULE_5__.DietaryLabelsFormComponent, _menu_categories_form_menu_categories_form_component__WEBPACK_IMPORTED_MODULE_6__.MenuCategoriesFormComponent, _order_advance_form_order_advance_form_component__WEBPACK_IMPORTED_MODULE_7__.OrderAdvanceFormComponent, _allergy_alert_form_allergy_alert_form_component__WEBPACK_IMPORTED_MODULE_8__.AllergyAlertFormComponent, _printer_options_form_printer_options_form_component__WEBPACK_IMPORTED_MODULE_9__.PrinterOptionsFormComponent, _school_closing_date_form_school_closing_date_form_component__WEBPACK_IMPORTED_MODULE_10__.SchoolClosingDateFormComponent, _food_break_settings_form_food_break_settings_form_component__WEBPACK_IMPORTED_MODULE_11__.FoodBreakSettingsFormComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: orange;\n}\n\n.titleWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 13px;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  margin: 0;\n  margin-left: 8px;\n}\n\n.schoolSelection[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding-top: 10px;\n  padding-left: 20px;\n  padding-right: 20px;\n  margin-bottom: 16px;\n}\n\n.labelSelectionWrapper[_ngcontent-%COMP%] {\n  margin-top: 10px;\n}\n\n.printerSection[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  padding-bottom: 20px;\n  padding-top: 20px;\n}\n\ntable[_ngcontent-%COMP%] {\n  width: 200%;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 22958:
/*!**********************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/opening-days-form/opening-days-form.component.ts ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OpeningDaysFormComponent: () => (/* binding */ OpeningDaysFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_sharedModels_base_KeyValueConversion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels/base/KeyValueConversion */ 26562);
/* harmony import */ var _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../settings-checkbox-list/settings-checkbox-list.component */ 76504);


//Models





class OpeningDaysFormComponent {
  constructor() {
    this.updateSchool = new _angular_core__WEBPACK_IMPORTED_MODULE_3__.EventEmitter();
    this.checkBoxValues = [];
  }
  ngOnInit() {
    this.checkBoxValues = (0,src_app_sharedModels_base_KeyValueConversion__WEBPACK_IMPORTED_MODULE_1__.getWeekDayKeyValue)();
  }
  ngOnChanges(changes) {
    if (changes?.school?.currentValue) {
      if (this.school.Name) {
        this._createForm();
      }
    }
  }
  _createForm() {
    this.daysHelper = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.Days(this.school.OpeningDays);
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      Monday: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.daysHelper.IsAvailableMonday()),
      Tuesday: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.daysHelper.IsAvailableTuesday()),
      Wednesday: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.daysHelper.IsAvailableWednesday()),
      Thursday: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.daysHelper.IsAvailableThursday()),
      Friday: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.daysHelper.IsAvailableFriday())
    });
  }
  submitForm() {
    let dayString = (0,src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.getSelectedWeekdaysFromForm)(this.form);
    this.school.OpeningDays = dayString.length > 0 ? dayString.substring(0, dayString.length - 1) : '';
    this.updateSchool.emit(this.school);
  }
  static {
    this.ɵfac = function OpeningDaysFormComponent_Factory(t) {
      return new (t || OpeningDaysFormComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: OpeningDaysFormComponent,
      selectors: [["opening-days-form"]],
      inputs: {
        school: "school"
      },
      outputs: {
        updateSchool: "updateSchool"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵNgOnChangesFeature"]],
      decls: 1,
      vars: 3,
      consts: [["title", "Opening Days", 3, "schoolId", "formGroup", "values", "submit"]],
      template: function OpeningDaysFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "settings-checkbox-list", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("submit", function OpeningDaysFormComponent_Template_settings_checkbox_list_submit_0_listener() {
            return ctx.submitForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("schoolId", ctx.school.SchoolId)("formGroup", ctx.form)("values", ctx.checkBoxValues);
        }
      },
      dependencies: [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _settings_checkbox_list_settings_checkbox_list_component__WEBPACK_IMPORTED_MODULE_2__.SettingsCheckboxListComponent],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 71072:
/*!************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/order-advance-form/order-advance-form.component.ts ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderAdvanceFormComponent: () => (/* binding */ OrderAdvanceFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/core */ 74646);









function OrderAdvanceFormComponent_mat_option_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "mat-option", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r1 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("value", option_r1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", option_r1, " ");
  }
}
class OrderAdvanceFormComponent {
  constructor() {
    this.updateValue = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this.weeksPreOrderOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  }
  ngOnInit() {}
  ngOnChanges(changes) {
    if (changes?.weekValue?.currentValue || changes?.weekValue?.currentValue === 0) {
      this._createForm();
    }
  }
  _createForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroup({
      weeksAdvanceValue: new _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControl(this.weekValue, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required)
    });
  }
  submitForm() {
    this.updateValue.emit(this.weeksAdvanceValue.value);
  }
  get weeksAdvanceValue() {
    return this.form.get('weeksAdvanceValue');
  }
  static {
    this.ɵfac = function OrderAdvanceFormComponent_Factory(t) {
      return new (t || OrderAdvanceFormComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: OrderAdvanceFormComponent,
      selectors: [["order-advance-form"]],
      inputs: {
        weekValue: "weekValue"
      },
      outputs: {
        updateValue: "updateValue"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵNgOnChangesFeature"]],
      decls: 11,
      vars: 2,
      consts: [[1, "col-12"], [1, "optionTitle", "pb-4"], [1, "labelSelectionWrapper", 3, "formGroup"], ["appearance", "outline"], ["formControlName", "weeksAdvanceValue"], [3, "value", 4, "ngFor", "ngForOf"], [1, "buttonWrapper"], ["text", "Save", 3, "onPress"], [3, "value"]],
      template: function OrderAdvanceFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "h3", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Allow Ordering In Advance By");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "form", 2)(4, "mat-form-field", 3)(5, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Weeks");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "mat-select", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](8, OrderAdvanceFormComponent_mat_option_8_Template, 2, 2, "mat-option", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "div", 6)(10, "basic-button", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("onPress", function OrderAdvanceFormComponent_Template_basic_button_onPress_10_listener() {
            return ctx.submitForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.weeksPreOrderOptions);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgForOf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_0__.BasicButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_4__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_4__.MatLabel, _angular_material_select__WEBPACK_IMPORTED_MODULE_5__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_6__.MatOption],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 80556:
/*!****************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/printer-options-form/printer-options-form.component.ts ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PrinterOptionsFormComponent: () => (/* binding */ PrinterOptionsFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/material/select */ 25175);
/* harmony import */ var _angular_material_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/core */ 74646);

//Models









function PrinterOptionsFormComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div")(1, "h3", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Printer Options");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 3)(4, "mat-form-field", 4)(5, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Printer Type Selection");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "mat-select", 5)(8, "mat-option", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, "A4 Printer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "mat-option", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "Thermal Printer");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "mat-option", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13, "Thermal Printer (2023)");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "div", 3)(15, "mat-form-field", 4)(16, "mat-label");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](17, "Label print choice");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "mat-select", 7)(19, "mat-option", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](20, "1 order per label");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](21, "mat-option", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](22, "1 item per label");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](23, "basic-button", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("onPress", function PrinterOptionsFormComponent_div_1_Template_basic_button_onPress_23_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r1.submitForm());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx_r0.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", ctx_r0.A4_PRINTER_ID);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", ctx_r0.THERMAL_PRINTER_ID);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", ctx_r0.THERMAL_2023_PRINTER_ID);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx_r0.form);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", ctx_r0.LabelPrintChoiceEnum.Order);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", ctx_r0.LabelPrintChoiceEnum.Item);
  }
}
class PrinterOptionsFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(printingService, spinnerService) {
    super();
    this.printingService = printingService;
    this.spinnerService = spinnerService;
    this.LabelPrintChoiceEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.LabelPrintChoiceEnum;
    this.thermalPrinterOptionEnum = src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.ThermalPrinterOptionEnum;
    this.A4_PRINTER_ID = 5;
    this.THERMAL_PRINTER_ID = 2;
    this.THERMAL_2023_PRINTER_ID = 4;
  }
  ngOnInit() {}
  ngOnChanges(changes) {
    if (changes?.printSettings?.currentValue) {
      this._createForm();
    }
  }
  _createForm() {
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroup({
      labelType: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.printSettings.LabelTypeId),
      labelPrintChoice: new _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControl(this.printSettings.LabelPrintChoice || this.LabelPrintChoiceEnum.Order)
    });
  }
  submitForm() {
    let data = {
      SchoolId: this.printSettings.SchoolId,
      LabelTypeId: this.labelType.value,
      LabelPrintChoice: this.labelPrintChoice.value
    };
    this.spinnerService.start();
    this.printingService.UpdateSchoolPrintOptionsAPI(data).subscribe({
      next: () => {
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this._createForm();
        this.handleErrorFromService(error);
      }
    });
  }
  get labelType() {
    return this.form.get('labelType');
  }
  get labelPrintChoice() {
    return this.form.get('labelPrintChoice');
  }
  get printerOption() {
    return this.form.get('printerOption');
  }
  static {
    this.ɵfac = function PrinterOptionsFormComponent_Factory(t) {
      return new (t || PrinterOptionsFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.PrintingApiService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.SpinnerService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: PrinterOptionsFormComponent,
      selectors: [["printer-options-form"]],
      inputs: {
        printSettings: "printSettings"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵNgOnChangesFeature"]],
      decls: 2,
      vars: 1,
      consts: [[1, "col-12"], [4, "ngIf"], [1, "optionTitle", "pb-4"], [1, "labelSelectionWrapper", 3, "formGroup"], ["appearance", "outline"], ["formControlName", "labelType"], [3, "value"], ["formControlName", "labelPrintChoice"], ["text", "Save", 3, "onPress"]],
      template: function PrinterOptionsFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, PrinterOptionsFormComponent_div_1_Template, 24, 7, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.form);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_2__.BasicButtonComponent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_6__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_6__.MatLabel, _angular_material_select__WEBPACK_IMPORTED_MODULE_7__.MatSelect, _angular_material_core__WEBPACK_IMPORTED_MODULE_8__.MatOption],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 68070:
/*!**********************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/school-close-date-table/school-close-date-table.component.ts ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolCloseDateTableComponent: () => (/* binding */ SchoolCloseDateTableComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_material_table__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/material/table */ 77697);
/* harmony import */ var _angular_material_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/material/icon */ 93840);
/* harmony import */ var _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/material/tooltip */ 80640);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);






function SchoolCloseDateTableComponent_th_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "th", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, "Start Date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
}
function SchoolCloseDateTableComponent_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "td", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](2, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](2, 1, element_r8.StartDate, "mediumDate"));
  }
}
function SchoolCloseDateTableComponent_th_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "th", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, "End Date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
}
function SchoolCloseDateTableComponent_td_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "td", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipe"](2, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const element_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵpipeBind2"](2, 1, element_r9.EndDate, "mediumDate"));
  }
}
function SchoolCloseDateTableComponent_th_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "th", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, "Action");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
  }
}
function SchoolCloseDateTableComponent_td_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "td", 9)(1, "mat-icon", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵlistener"]("click", function SchoolCloseDateTableComponent_td_9_Template_mat_icon_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵrestoreView"](_r12);
      const element_r10 = restoredCtx.$implicit;
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵresetView"](ctx_r11.archiveClicked(element_r10.SchoolDateId));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2, "delete_outline");
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
  }
}
function SchoolCloseDateTableComponent_tr_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "tr", 11);
  }
}
function SchoolCloseDateTableComponent_tr_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "tr", 12);
  }
}
class SchoolCloseDateTableComponent {
  constructor() {
    this.archive = new _angular_core__WEBPACK_IMPORTED_MODULE_0__.EventEmitter();
    this.displayedColumns = ['StartDate', 'EndDate', 'Actions'];
  }
  ngOnInit() {}
  archiveClicked(dateId) {
    this.archive.emit(dateId);
  }
  static {
    this.ɵfac = function SchoolCloseDateTableComponent_Factory(t) {
      return new (t || SchoolCloseDateTableComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: SchoolCloseDateTableComponent,
      selectors: [["school-close-date-table"]],
      inputs: {
        dates: "dates"
      },
      outputs: {
        archive: "archive"
      },
      decls: 12,
      vars: 3,
      consts: [["mat-table", "", 1, "col-12", 3, "dataSource"], ["matColumnDef", "StartDate"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "EndDate"], ["matColumnDef", "Actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], ["matTooltip", "Archive", 2, "cursor", "pointer", 3, "click"], ["mat-header-row", ""], ["mat-row", ""]],
      template: function SchoolCloseDateTableComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "table", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerStart"](1, 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](2, SchoolCloseDateTableComponent_th_2_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](3, SchoolCloseDateTableComponent_td_3_Template, 3, 4, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerStart"](4, 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](5, SchoolCloseDateTableComponent_th_5_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](6, SchoolCloseDateTableComponent_td_6_Template, 3, 4, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerStart"](7, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](8, SchoolCloseDateTableComponent_th_8_Template, 2, 0, "th", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](9, SchoolCloseDateTableComponent_td_9_Template, 3, 0, "td", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementContainerEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](10, SchoolCloseDateTableComponent_tr_10_Template, 1, 0, "tr", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtemplate"](11, SchoolCloseDateTableComponent_tr_11_Template, 1, 0, "tr", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("dataSource", ctx.dates);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("matHeaderRowDef", ctx.displayedColumns);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵproperty"]("matRowDefColumns", ctx.displayedColumns);
        }
      },
      dependencies: [_angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatTable, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatHeaderCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatHeaderRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatColumnDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatCellDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatRowDef, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatHeaderCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatCell, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatHeaderRow, _angular_material_table__WEBPACK_IMPORTED_MODULE_1__.MatRow, _angular_material_icon__WEBPACK_IMPORTED_MODULE_2__.MatIcon, _angular_material_tooltip__WEBPACK_IMPORTED_MODULE_3__.MatTooltip, _angular_common__WEBPACK_IMPORTED_MODULE_4__.DatePipe],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 83276:
/*!************************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/school-closing-date-form/school-closing-date-form.component.ts ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolClosingDateFormComponent: () => (/* binding */ SchoolClosingDateFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_shared_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/shared/components */ 2691);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../schools-form/components/input-date/input-date.component */ 81392);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);
/* harmony import */ var _school_close_date_table_school_close_date_table_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../school-close-date-table/school-close-date-table.component */ 68070);


//Models

//Dialog









function SchoolClosingDateFormComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 5)(1, "h3", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, "School Closing Dates");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](4, "input-date", 8)(5, "input-date", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("formGroup", ctx_r0.form);
  }
}
function SchoolClosingDateFormComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div")(1, "school-close-date-table", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("archive", function SchoolClosingDateFormComponent_div_3_Template_school_close_date_table_archive_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r5);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r4.archiveClicked($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("dates", ctx_r1.schoolClosingDates);
  }
}
function SchoolClosingDateFormComponent_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](1, "Your school currently doesn't have any closing dates");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
class SchoolClosingDateFormComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(spinnerService, schoolService, dialog) {
    super();
    this.spinnerService = spinnerService;
    this.schoolService = schoolService;
    this.dialog = dialog;
    this.schoolClosingDates = [];
  }
  ngOnInit() {}
  ngOnChanges(changes) {
    if (changes?.schoolId?.currentValue) {
      this.getCloseDates(this.schoolId);
      this._createForm();
    }
  }
  _createForm() {
    if (this.form) {
      return;
    }
    this.form = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
      closeStartDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required),
      closeEndDate: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.Validators.required)
    });
  }
  submitForm() {
    if (this.form.invalid) {
      return;
    }
    this.spinnerService.start();
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.DateSchool();
    data.StartDate = this.formatDate(this.closeStartDate.value);
    data.EndDate = this.formatDate(this.closeEndDate.value);
    data.SchoolId = this.schoolId;
    this.schoolService.UpsertSchoolCloseDatesByIdApi(data).subscribe({
      next: res => {
        this.getCloseDates(this.schoolId);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  formatDate(date) {
    return new Date(moment__WEBPACK_IMPORTED_MODULE_0___default()(date).format('YYYY-MM-DD'));
  }
  getCloseDates(schoolId) {
    this.schoolService.GetSchoolCloseDatesByIdApi(schoolId).subscribe(CloseDates => {
      this.schoolClosingDates = CloseDates;
    });
  }
  archiveClicked(dateId) {
    let data = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_1__.ConfirmModal();
    data.Title = 'Delete School Closing Date';
    data.Text = 'Deleting this record cannot be undone, are you sure you want to delete it?';
    data.CancelButton = 'No';
    data.ConfirmButton = 'Yes';
    const dialogRef = this.dialog.open(src_app_shared_components__WEBPACK_IMPORTED_MODULE_2__.DialogConfirmComponent, {
      width: '500px',
      disableClose: true,
      data: data
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteSchoolDate(dateId);
      }
    });
  }
  deleteSchoolDate(dateId) {
    this.spinnerService.start();
    this.schoolService.DeleteSchoolCloseDatesByDateIdApi(dateId).subscribe({
      next: res => {
        this.getCloseDates(this.schoolId);
        this.spinnerService.stop();
      },
      error: error => {
        this.spinnerService.stop();
        this.handleErrorFromService(error);
      }
    });
  }
  get closeStartDate() {
    return this.form.get('closeStartDate');
  }
  get closeEndDate() {
    return this.form.get('closeEndDate');
  }
  static {
    this.ɵfac = function SchoolClosingDateFormComponent_Factory(t) {
      return new (t || SchoolClosingDateFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_3__.SchoolService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: SchoolClosingDateFormComponent,
      selectors: [["school-closing-date-form"]],
      inputs: {
        schoolId: "schoolId"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵNgOnChangesFeature"]],
      decls: 6,
      vars: 3,
      consts: [["class", "col-md-6 col-lg-4", 4, "ngIf"], ["text", "Save", 3, "onPress"], [1, "col-12"], [4, "ngIf", "ngIfElse"], ["noDateMessage", ""], [1, "col-md-6", "col-lg-4"], [1, "optionTitle"], [1, "pt-3", "pb-3", 3, "formGroup"], ["placeholder", "Start Date", "formControlName", "closeStartDate"], ["placeholder", "End Date", "formControlName", "closeEndDate"], [3, "dates", "archive"]],
      template: function SchoolClosingDateFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](0, SchoolClosingDateFormComponent_div_0_Template, 6, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "basic-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("onPress", function SchoolClosingDateFormComponent_Template_basic_button_onPress_1_listener() {
            return ctx.submitForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](3, SchoolClosingDateFormComponent_div_3_Template, 2, 1, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, SchoolClosingDateFormComponent_ng_template_4_Template, 2, 0, "ng-template", null, 4, _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵreference"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.schoolClosingDates == null ? null : ctx.schoolClosingDates.length)("ngIfElse", _r2);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _schools_form_components_input_date_input_date_component__WEBPACK_IMPORTED_MODULE_4__.InputDateComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControlName, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_5__.BasicButtonComponent, _school_close_date_table_school_close_date_table_component__WEBPACK_IMPORTED_MODULE_6__.SchoolCloseDateTableComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 33626:
/*!******************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/school-settings/school-settings.component.ts ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SchoolSettingsComponent: () => (/* binding */ SchoolSettingsComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var src_app_canteen_settings_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/canteen-settings/components */ 31096);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/dialog */ 12587);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared-tools/components/merchant-school-picker/merchant-school-picker.component */ 38237);
/* harmony import */ var _classes_list_classes_list_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../classes-list/classes-list.component */ 70846);










function SchoolSettingsComponent_classes_list_17_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "classes-list", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("editClicked", function SchoolSettingsComponent_classes_list_17_Template_classes_list_editClicked_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r1.OpenClassModal($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("listClasses", ctx_r0.listClasses);
  }
}
class SchoolSettingsComponent {
  constructor(_location, store, classesService, spinnerService, dialog) {
    this._location = _location;
    this.store = store;
    this.classesService = classesService;
    this.spinnerService = spinnerService;
    this.dialog = dialog;
    this.listCanteens = [];
    this.listClasses = [];
    this.classAdded = false;
    this.canteenListVisible = true;
  }
  ngOnInit() {}
  ngOnDestroy() {}
  GoBackClick() {
    this._location.back();
  }
  onSchoolSelect(event) {
    this.selectedSchoolId = event;
    this.LoadClass(event);
  }
  LoadClass(schoolId) {
    if (schoolId) {
      this.spinnerService.start();
      this.classesService.GetClassesBySchoolAPI(schoolId, true).subscribe({
        next: response => {
          this.listClasses = response.Classes;
          this.isListLoaded = true;
          this.spinnerService.stop();
        },
        error: error => {
          this.spinnerService.stop();
        }
      });
    }
  }
  CanteenListVisibleChanged(isVisible) {
    this.canteenListVisible = isVisible;
  }
  OpenClassModal(schoolClass) {
    if (!schoolClass) {
      schoolClass = new src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.SchoolClass();
      schoolClass.SchoolId = this.selectedSchoolId;
      schoolClass.IsActive = true;
    }
    const dialogRef = this.dialog.open(src_app_canteen_settings_components__WEBPACK_IMPORTED_MODULE_1__.AddSchoolClassComponent, {
      width: '500px',
      disableClose: true,
      data: schoolClass
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.LoadClass(result.schoolId);
      }
    });
  }
  static {
    this.ɵfac = function SchoolSettingsComponent_Factory(t) {
      return new (t || SchoolSettingsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_7__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_8__.Store), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SchoolClassesService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_9__.MatDialog));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: SchoolSettingsComponent,
      selectors: [["app-school-settings"]],
      decls: 18,
      vars: 1,
      consts: [[1, "col-12"], ["text", "Go Back", 1, "backButton", 3, "navBack"], [1, "titleWrapper"], ["sizes", "24", "src", "assets/icons/settings.svg"], [1, "title"], [3, "schoolChanged"], [1, "row"], [1, "titleDescriptionWrapper"], [1, "titleDescription"], [1, "titleDescriptionButton", 3, "click"], [1, "titleDescriptionButtonText"], ["sizes", "24", "src", "assets/icons/plus.svg"], [3, "listClasses", "editClicked", 4, "ngIf"], [3, "listClasses", "editClicked"]],
      template: function SchoolSettingsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "nav-back-button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("navBack", function SchoolSettingsComponent_Template_nav_back_button_navBack_1_listener() {
            return ctx.GoBackClick();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "img", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "h1", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](5, "School Settings");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "merchant-school-picker", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("schoolChanged", function SchoolSettingsComponent_Template_merchant_school_picker_schoolChanged_6_listener($event) {
            return ctx.onSchoolSelect($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "div", 6)(8, "div", 0)(9, "div", 7)(10, "h3", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](11, "Class List");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "a", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function SchoolSettingsComponent_Template_a_click_12_listener() {
            return ctx.OpenClassModal(null);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "p", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14, "Add Class");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](15, "img", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](17, SchoolSettingsComponent_classes_list_17_Template, 1, 1, "classes-list", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isListLoaded);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _shared_tools_components_merchant_school_picker_merchant_school_picker_component__WEBPACK_IMPORTED_MODULE_4__.MerchantSchoolPickerComponent, _classes_list_classes_list_component__WEBPACK_IMPORTED_MODULE_5__.ClassesListComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.backButton[_ngcontent-%COMP%] {\n  color: orange;\n  font-size: 14px;\n}\n\n.titleWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 13px;\n  align-items: center;\n}\n\n.title[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  margin: 0;\n  margin-left: 8px;\n}\n\n.schoolSelection[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding-top: 10px;\n  padding-left: 20px;\n  padding-right: 20px;\n}\n\n.titleDescription[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 20px;\n}\n\n.titleDescriptionWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 18px 20px;\n}\n\n.titleDescriptionButton[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n}\n\n.titleDescriptionButtonText[_ngcontent-%COMP%] {\n  color: #ff7a00;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  text-align: right;\n  margin: 0;\n  margin-right: 9px;\n}\n\n.cardWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding-top: 20px;\n  padding-bottom: 23px;\n  padding-right: 15px;\n  padding-left: 20px;\n}\n\n.crossIconWrapper[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-right: 2px;\n}\n\n.invisible[_ngcontent-%COMP%] {\n  display: none;\n}\n\n\n\n.mat-checkbox-background[_ngcontent-%COMP%] {\n  background-color: yellowgreen !important;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 76504:
/*!********************************************************************************************************!*\
  !*** ./src/app/canteen-settings/components/settings-checkbox-list/settings-checkbox-list.component.ts ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsCheckboxListComponent: () => (/* binding */ SettingsCheckboxListComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _schools_form_components_input_checkbox_list_input_checkbox_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../schools-form/components/input-checkbox-list/input-checkbox-list.component */ 25852);
/* harmony import */ var _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../schools-button/components/basic-button/basic-button.component */ 10786);






function SettingsCheckboxListComponent_div_0_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r1.description, " ");
  }
}
function SettingsCheckboxListComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div")(1, "h3", 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](3, SettingsCheckboxListComponent_div_0_div_3_Template, 2, 1, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "input-checkbox-list", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 4)(6, "basic-button", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("onPress", function SettingsCheckboxListComponent_div_0_Template_basic_button_onPress_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r3);
      const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r2.submitForm());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx_r0.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r0.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx_r0.form)("values", ctx_r0.values)("valueChangeTrigger", ctx_r0.schoolId);
  }
}
class SettingsCheckboxListComponent {
  constructor(controlContainer) {
    this.controlContainer = controlContainer;
    this.values = [];
    this.submit = new _angular_core__WEBPACK_IMPORTED_MODULE_2__.EventEmitter();
  }
  ngOnInit() {}
  ngOnChanges(changes) {
    if (changes?.schoolId?.currentValue) {
      this.form = this.controlContainer.control;
    }
  }
  submitForm() {
    this.submit.emit();
  }
  static {
    this.ɵfac = function SettingsCheckboxListComponent_Factory(t) {
      return new (t || SettingsCheckboxListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_3__.ControlContainer));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: SettingsCheckboxListComponent,
      selectors: [["settings-checkbox-list"]],
      inputs: {
        schoolId: "schoolId",
        title: "title",
        description: "description",
        values: "values"
      },
      outputs: {
        submit: "submit"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵNgOnChangesFeature"]],
      decls: 1,
      vars: 1,
      consts: [[4, "ngIf"], [1, "optionTitle", "pb-3"], ["class", "pb-3", 4, "ngIf"], [3, "formGroup", "values", "valueChangeTrigger"], [1, "pt-3"], ["text", "Save", 3, "onPress"], [1, "pb-3"]],
      template: function SettingsCheckboxListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](0, SettingsCheckboxListComponent_div_0_Template, 7, 5, "div", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.form);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _schools_form_components_input_checkbox_list_input_checkbox_list_component__WEBPACK_IMPORTED_MODULE_0__.InputCheckboxListComponent, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _schools_button_components_basic_button_basic_button_component__WEBPACK_IMPORTED_MODULE_1__.BasicButtonComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.optionsWrapper[_ngcontent-%COMP%] {\n  background-color: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.optionTitle[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: bold;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n  margin: 0;\n}\n\n.option[_ngcontent-%COMP%] {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.optionsWrapperList[_ngcontent-%COMP%] {\n  padding-bottom: 15px;\n  margin-top: 15px;\n}\n\n.checkbox[_ngcontent-%COMP%] {\n  font-style: normal;\n  font-weight: normal;\n  font-size: 18px;\n  line-height: 20px;\n  color: #000000;\n}\n\n.buttonWrapper[_ngcontent-%COMP%] {\n  display: block;\n}\n\n.button[_ngcontent-%COMP%] {\n  background: #ff7a00;\n  font-weight: bold;\n  font-size: 20px;\n  line-height: 22px;\n  text-align: center;\n  padding: 11px 23px 14px 23px;\n  color: #ffffff;\n}\n\n.button[_ngcontent-%COMP%]:disabled {\n  color: #ffffff;\n  background: #e0e0e0;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 70900:
/*!**********************************************************************************!*\
  !*** ./src/app/manage-order/components/category-icon/category-icon.component.ts ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CategoryIconComponent: () => (/* binding */ CategoryIconComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);


class CategoryIconComponent {
  constructor() {
    this.ICON_PATH = 'assets/menuIcons';
    this.DEFAULT_ICON = 'default.svg';
    this.iconSource = `${this.ICON_PATH}/${this.DEFAULT_ICON}`;
  }
  ngOnChanges(simpleChanges) {
    const iconFileName = this.getIconName(simpleChanges.iconName.currentValue);
    this.iconSource = `${this.ICON_PATH}/${iconFileName}`;
  }
  getIconName(imageName) {
    const iconFileName = this.getImageNameFromFileName(imageName);
    return this.validCategoryName(iconFileName) ? `${iconFileName}.svg` : this.DEFAULT_ICON;
  }
  validCategoryName(imageName) {
    const allCategories = [...src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.CANTEEN_CATEGORY_ICON_ARRAY, ...src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.UNIFORM_CATEGORY_ICON_ARRAY];
    return allCategories.includes(imageName);
  }
  getImageNameFromFileName(imageName) {
    return imageName ? imageName.replace('.jpg', '') : null;
  }
  static {
    this.ɵfac = function CategoryIconComponent_Factory(t) {
      return new (t || CategoryIconComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: CategoryIconComponent,
      selectors: [["category-icon"]],
      inputs: {
        iconName: "iconName"
      },
      standalone: true,
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵNgOnChangesFeature"], _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵStandaloneFeature"]],
      decls: 1,
      vars: 1,
      consts: [["width", "50", 3, "src"]],
      template: function CategoryIconComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "img", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("src", ctx.iconSource, _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsanitizeUrl"]);
        }
      },
      styles: ["img[_ngcontent-%COMP%] {\n  pointer-events: none;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbWFuYWdlLW9yZGVyL2NvbXBvbmVudHMvY2F0ZWdvcnktaWNvbi9jYXRlZ29yeS1pY29uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usb0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbImltZyB7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"],
      changeDetection: 0
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_canteen-settings_canteen-settings_module_ts.js.map