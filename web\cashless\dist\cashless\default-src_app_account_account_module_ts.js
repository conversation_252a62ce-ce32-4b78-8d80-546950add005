"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["default-src_app_account_account_module_ts"],{

/***/ 25198:
/*!***************************************************!*\
  !*** ./src/app/account/account-routing.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountRoutingModule: () => (/* binding */ AccountRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components */ 27403);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);

// // Components



// Services
const routes = [{
  path: '',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AccountComponent
}, {
  path: 'account-topup',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AccountTopupComponent
}, {
  path: 'billing-history',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.BillingHistoryComponent
}, {
  path: 'account-help',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AccountHelpComponent
}, {
  path: 'account-help/account-close',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AccountCloseComponent
}, {
  path: 'profile',
  component: _components__WEBPACK_IMPORTED_MODULE_0__.AccountEditComponent
}];
class AccountRoutingModule {
  static {
    this.ɵfac = function AccountRoutingModule_Factory(t) {
      return new (t || AccountRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: AccountRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](AccountRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 90359:
/*!*******************************************!*\
  !*** ./src/app/account/account.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountModule: () => (/* binding */ AccountModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _components_account_account_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/account/account.component */ 12765);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/material/button */ 84175);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _account_routing_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./account-routing.module */ 25198);
/* harmony import */ var _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../schools-button/schools-button.module */ 33373);
/* harmony import */ var _payment_payment_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../payment/payment.module */ 42047);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components */ 27403);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 37580);



// google material










class AccountModule {
  static {
    this.ɵfac = function AccountModule_Factory(t) {
      return new (t || AccountModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: AccountModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.ReactiveFormsModule, _account_routing_module__WEBPACK_IMPORTED_MODULE_1__.AccountRoutingModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_3__.PaymentModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_4__.SharedModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_2__.SchoolsButtonModule,
      // material
      _angular_material_card__WEBPACK_IMPORTED_MODULE_9__.MatCardModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_11__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_12__.MatButtonModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](AccountModule, {
    declarations: [_components_account_account_component__WEBPACK_IMPORTED_MODULE_0__.AccountComponent, _components__WEBPACK_IMPORTED_MODULE_5__.AccountEditComponent, _components__WEBPACK_IMPORTED_MODULE_5__.AccountTopupComponent, _components__WEBPACK_IMPORTED_MODULE_5__.AccountAboutComponent, _components__WEBPACK_IMPORTED_MODULE_5__.BillingHistoryComponent, _components__WEBPACK_IMPORTED_MODULE_5__.AccountCloseComponent, _components__WEBPACK_IMPORTED_MODULE_5__.AccountHelpComponent, _components__WEBPACK_IMPORTED_MODULE_5__.CloseAccountRowComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.ReactiveFormsModule, _account_routing_module__WEBPACK_IMPORTED_MODULE_1__.AccountRoutingModule, _payment_payment_module__WEBPACK_IMPORTED_MODULE_3__.PaymentModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_4__.SharedModule, _schools_button_schools_button_module__WEBPACK_IMPORTED_MODULE_2__.SchoolsButtonModule,
    // material
    _angular_material_card__WEBPACK_IMPORTED_MODULE_9__.MatCardModule, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_10__.MatFormFieldModule, _angular_material_input__WEBPACK_IMPORTED_MODULE_11__.MatInputModule, _angular_material_button__WEBPACK_IMPORTED_MODULE_12__.MatButtonModule],
    exports: [_components_account_account_component__WEBPACK_IMPORTED_MODULE_0__.AccountComponent]
  });
})();

/***/ }),

/***/ 34637:
/*!*****************************************************************************!*\
  !*** ./src/app/account/components/account-about/account-about.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountAboutComponent: () => (/* binding */ AccountAboutComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);

class AccountAboutComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function AccountAboutComponent_Factory(t) {
      return new (t || AccountAboutComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: AccountAboutComponent,
      selectors: [["app-account-about"]],
      decls: 2,
      vars: 0,
      template: function AccountAboutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](1, "account-about works!");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
        }
      },
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 38753:
/*!*****************************************************************************!*\
  !*** ./src/app/account/components/account-close/account-close.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountCloseComponent: () => (/* binding */ AccountCloseComponent)
/* harmony export */ });
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _close_account_row_close_account_row_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../close-account-row/close-account-row.component */ 2293);

// Models





class AccountCloseComponent {
  constructor(userService) {
    this.userService = userService;
    this.termsLink = src_constants__WEBPACK_IMPORTED_MODULE_0__.SPRIGGY_SCHOOLS_TERMS_LINK;
  }
  ngOnInit() {
    this.user = this.userService.GetUserConnected();
    if (!this.user) {
      this.user = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.UserCashless();
    }
    this.userName = this.user.FirstName;
  }
  triggerIntercom() {
    this.userService.openIntercom();
  }
  static {
    this.ɵfac = function AccountCloseComponent_Factory(t) {
      return new (t || AccountCloseComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.UserService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AccountCloseComponent,
      selectors: [["app-account-close"]],
      decls: 23,
      vars: 8,
      consts: [[1, "container-fluid"], [1, "wrapperContainer"], [1, "row"], [1, "col-12"], [1, "page-title", 2, "text-align", "center"], [1, "row", "justify-content-center"], [1, "col-12", "col-md-8", "col-lg-6", "align-self-center"], ["appearance", "outlined", 1, "cardForm"], [1, "col-12", "col-md-8"], [2, "font-size", "24px", "line-height", "32px", "font-weight", "700"], [3, "text", "rowNumber"], ["target", "_blank", 1, "terms-link", 3, "href"], ["type", "button", 1, "PrimaryButton", "saveButton", 2, "margin-top", "15px", "font-size", "18px", 3, "click"]],
      template: function AccountCloseComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, "Close my Account");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "div", 5)(7, "div", 6)(8, "mat-card", 7)(9, "mat-card-content")(10, "div", 5)(11, "div", 8)(12, "h3", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "To close your Spriggy Schools account just follow these steps.");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](16, "close-account-row", 10)(17, "close-account-row", 10)(18, "close-account-row", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "a", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](20, "Privacy policy & terms");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AccountCloseComponent_Template_button_click_21_listener() {
            return ctx.triggerIntercom();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](22, " Close my account ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" We\u2019ll miss you ", ctx.userName, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("text", "Tap the \u2018Close Account\u2019 button")("rowNumber", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("text", "Start a conversation with our member help representative")("rowNumber", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("text", "Type and send \u2018Close Account\u2019 and follow the prompts")("rowNumber", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("href", ctx.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
        }
      },
      dependencies: [_angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_5__.MatCardContent, _close_account_row_close_account_row_component__WEBPACK_IMPORTED_MODULE_3__.CloseAccountRowComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.page-title[_ngcontent-%COMP%] {\n  font-size: 18px;\n  line-height: 24px;\n  font-weight: 500;\n  color: #272c50;\n}\n\n.cardForm[_ngcontent-%COMP%] {\n  font-size: 16px;\n  line-height: 24px;\n  font-weight: 400;\n  color: #272c50;\n}\n\n.terms-link[_ngcontent-%COMP%] {\n  font-size: 18px;\n  line-height: 24px;\n  font-weight: 700;\n  color: #ff8000;\n  display: block;\n  text-align: center;\n  padding: 10px;\n  text-decoration: none;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 4463:
/*!***************************************************************************!*\
  !*** ./src/app/account/components/account-edit/account-edit.component.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountEditComponent: () => (/* binding */ AccountEditComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ 46227);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
/* harmony import */ var _angular_material_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/material/card */ 53777);
/* harmony import */ var _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/material/form-field */ 24950);
/* harmony import */ var _angular_material_input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/material/input */ 95541);
/* harmony import */ var _angular_material_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/material/button */ 84175);


// Models











function AccountEditComponent_mat_error_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r0.getErrorMessageName());
  }
}
function AccountEditComponent_mat_error_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r1.getErrorMessageLastname());
  }
}
function AccountEditComponent_mat_error_29_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r2.getErrorMessageMobile());
  }
}
function AccountEditComponent_mat_error_30_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "mat-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r3.WriteError());
  }
}
const _c0 = function () {
  return ["../"];
};
class AccountEditComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(userService, spinnerService, routerService, phoneNumberService, _location) {
    super();
    this.userService = userService;
    this.spinnerService = spinnerService;
    this.routerService = routerService;
    this.phoneNumberService = phoneNumberService;
    this._location = _location;
  }
  ngOnInit() {
    this.user = this.userService.GetUserConnected();
    if (!this.user) {
      this.user = new _sharedModels__WEBPACK_IMPORTED_MODULE_1__.UserCashless();
    }
    const formattedMobile = this.phoneNumberService.formatToPhone(this.user.Mobile);
    this.accountForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroup({
      name: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.user.FirstName, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      lastname: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.user.Lastname, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      email: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(this.user.Email, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required]),
      mobile: new _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControl(formattedMobile, [_angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.Validators.minLength(12)])
    });
    this.accountForm.get('email').disable();
  }
  backClicked() {
    this._location.back();
  }
  get name() {
    return this.accountForm.get('name');
  }
  get lastname() {
    return this.accountForm.get('lastname');
  }
  get mobile() {
    return this.accountForm.get('mobile');
  }
  getErrorMessageName() {
    return this.accountForm.get('name').hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageLastname() {
    return this.accountForm.get('lastname').hasError('required') ? 'You must enter a value' : '';
  }
  getErrorMessageMobile() {
    return this.accountForm.get('mobile').hasError('required') || this.accountForm.get('mobile').invalid ? 'You must enter a valid mobile number' : '';
  }
  ConvertUser() {
    const userCopy = lodash__WEBPACK_IMPORTED_MODULE_0__.cloneDeep(this.user);
    userCopy.FirstName = this.name.value;
    userCopy.Lastname = this.lastname.value;
    userCopy.Mobile = this.phoneNumberService.serverMobileNumber(this.mobile.value);
    return userCopy;
  }
  ClickSubmit() {
    if (this.accountForm.invalid) {
      this.accountForm.markAllAsTouched();
      return;
    }
    if (this.accountForm.valid) {
      this.spinnerService.start();
      let user = this.ConvertUser();
      this.userService.UpsertUser(user).subscribe({
        next: userResponse => {
          this.userService.SetUserConnected(userResponse);
          this.spinnerService.stop();
          this.routerService.navigate(['/family/account']);
        },
        error: error => {
          this.spinnerService.stop();
          this.handleErrorFromService(error);
        }
      });
    }
  }
  formatMobileInput() {
    let res = this.phoneNumberService.aussieMobileBranded(this.mobile.value);
    this.mobile.setValue(res);
  }
  static {
    this.ɵfac = function AccountEditComponent_Factory(t) {
      return new (t || AccountEditComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.SpinnerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_6__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_2__.PhoneNumberService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_7__.Location));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AccountEditComponent,
      selectors: [["app-account-edit"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵInheritDefinitionFeature"]],
      decls: 38,
      vars: 7,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-12"], ["text", "Profile", 3, "navBack"], [1, "row", "justify-content-center"], [1, "col-12", "col-md-8", "col-lg-6", "align-self-center"], ["appearance", "outlined", 1, "cardForm"], [1, "col-12", "col-md-8"], [1, "cashlessForm", 3, "formGroup"], ["appearance", "outline"], ["matInput", "", "placeholder", "Name", "formControlName", "name", "type", "text", "required", ""], [4, "ngIf"], ["matInput", "", "placeholder", "Lastname", "formControlName", "lastname", "type", "text", "required", ""], ["matInput", "", "placeholder", "Email", "formControlName", "email", "type", "text"], ["matInput", "", "placeholder", "Mobile number", "formControlName", "mobile", "type", "tel", "required", "", 3, "keyup"], [1, "row", "justify-content-center", "rowButton"], ["type", "button", 1, "PrimaryButton", 3, "click"], ["mat-flat-button", "", "type", "button", 1, "SecondaryButton", 3, "routerLink"]],
      template: function AccountEditComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "nav-back-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("navBack", function AccountEditComponent_Template_nav_back_button_navBack_3_listener() {
            return ctx.backClicked();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 4)(5, "div", 5)(6, "mat-card", 6)(7, "mat-card-content")(8, "div", 4)(9, "div", 7)(10, "form", 8)(11, "mat-form-field", 9)(12, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13, "First name");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "input", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, AccountEditComponent_mat_error_15_Template, 2, 1, "mat-error", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "mat-form-field", 9)(17, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18, "Last name");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](19, "input", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, AccountEditComponent_mat_error_20_Template, 2, 1, "mat-error", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "mat-form-field", 9)(22, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](23, "Email");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](24, "input", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "mat-form-field", 9)(26, "mat-label");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27, "Mobile number");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "input", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("keyup", function AccountEditComponent_Template_input_keyup_28_listener() {
            return ctx.formatMobileInput();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](29, AccountEditComponent_mat_error_29_Template, 2, 1, "mat-error", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](30, AccountEditComponent_mat_error_30_Template, 2, 1, "mat-error", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "div", 15)(32, "div", 7)(33, "button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AccountEditComponent_Template_button_click_33_listener() {
            return ctx.ClickSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](34, "Save");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](35, "div", 7)(36, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](37, " Cancel ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("formGroup", ctx.accountForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.name.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.lastname.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.mobile.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.errorAPI);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction0"](6, _c0));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_5__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_5__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormControlName, _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterLink, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_3__.NavBackButtonComponent, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCard, _angular_material_card__WEBPACK_IMPORTED_MODULE_8__.MatCardContent, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatFormField, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatLabel, _angular_material_form_field__WEBPACK_IMPORTED_MODULE_9__.MatError, _angular_material_input__WEBPACK_IMPORTED_MODULE_10__.MatInput, _angular_material_button__WEBPACK_IMPORTED_MODULE_11__.MatButton],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.cardForm[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\n\n.buttonsRow[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\n\n.saveButton[_ngcontent-%COMP%] {\n  margin-top: 10px;\n}\n@media (min-width: 767px) {\n  .saveButton[_ngcontent-%COMP%] {\n    margin-top: 0;\n    height: 37px;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjb3VudC9jb21wb25lbnRzL2FjY291bnQtZWRpdC9hY2NvdW50LWVkaXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS0E7RUFDRSxhQUFBO0FDSkY7QURLRTtFQUZGO0lBR0ksY0FBQTtFQ0ZGO0FBQ0Y7O0FES0E7RUFDRSxhQUFBO0FDRkY7QURHRTtFQUZGO0lBR0ksY0FBQTtFQ0FGO0FBQ0Y7O0FBZEE7RUFDRSxnQkFBQTtBQWlCRjs7QUFkQTtFQUNFLGdCQUFBO0FBaUJGOztBQWRBO0VBQ0UsZ0JBQUE7QUFpQkY7QUFoQkU7RUFGRjtJQUdJLGFBQUE7SUFDQSxZQUFBO0VBbUJGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIkYnJlYWtwb2ludC1zbTogNTc2cHg7XG4kYnJlYWtwb2ludC1tZDogNzY3cHg7XG4kYnJlYWtwb2ludC1sZzogOTkycHg7XG4kYnJlYWtwb2ludC14bDogMTIwMHB4O1xuXG4ubW9iaWxlIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtYXgtd2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbi8vIE5PVEUgQ3VycmVudGx5IHRhYmxldCBhbmQgbW9iaWxlIGlzIHRoZSBzYW1lLiBDaGFuZ2UgdG8gJGJyZWFrcG9pbnQtbGcgbGF0ZXIgaWYgd2UgZ2V0IGEgcHJvcGVyIHRhYmxldCBkZXNpZ24uXG4uZGVza3RvcCB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWluLXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4iLCJAaW1wb3J0ICcuLi8uLi8uLi8uLi9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2Nzcyc7XG5cbi5jYXJkRm9ybSB7XG4gIG1hcmdpbi10b3A6IDIwcHg7XG59XG5cbi5idXR0b25zUm93IHtcbiAgbWFyZ2luLXRvcDogMjBweDtcbn1cblxuLnNhdmVCdXR0b24ge1xuICBtYXJnaW4tdG9wOiAxMHB4O1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBtYXJnaW4tdG9wOiAwO1xuICAgIGhlaWdodDogMzdweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 90121:
/*!***************************************************************************!*\
  !*** ./src/app/account/components/account-help/account-help.component.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountHelpComponent: () => (/* binding */ AccountHelpComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);





const _c0 = function () {
  return ["account-close"];
};
class AccountHelpComponent {
  constructor(userService, _location) {
    this.userService = userService;
    this._location = _location;
  }
  triggerIntercom() {
    this.userService.openIntercom();
  }
  backClicked() {
    this._location.back();
  }
  static {
    this.ɵfac = function AccountHelpComponent_Factory(t) {
      return new (t || AccountHelpComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_0__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_3__.Location));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: AccountHelpComponent,
      selectors: [["app-account-help"]],
      decls: 13,
      vars: 2,
      consts: [[1, "container-fluid"], [1, "wrapperContainer"], [1, "row"], [1, "col-12"], ["text", "Help", 3, "navBack"], [1, "row", "rowHistory", 3, "click"], [1, "help-text"], [1, "row", "rowHistory", 3, "routerLink"]],
      template: function AccountHelpComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "nav-back-button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("navBack", function AccountHelpComponent_Template_nav_back_button_navBack_4_listener() {
            return ctx.backClicked();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function AccountHelpComponent_Template_div_click_5_listener() {
            return ctx.triggerIntercom();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "div", 3)(7, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8, "Contact Member Help");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "div", 7)(10, "div", 3)(11, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12, "Close my Account");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpureFunction0"](1, _c0));
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_4__.RouterLink, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_1__.NavBackButtonComponent],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.rowHistory[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #dddddd;\n  background-color: white;\n  cursor: pointer;\n}\n\n.help-text[_ngcontent-%COMP%] {\n  font-size: 18px;\n  line-height: 24px;\n  font-weight: 500;\n  color: #272c50;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 5086:
/*!*****************************************************************************!*\
  !*** ./src/app/account/components/account-topup/account-topup.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountTopupComponent: () => (/* binding */ AccountTopupComponent)
/* harmony export */ });
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _payment_components_top_up_form_top_up_form_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../payment/components/top-up-form/top-up-form.component */ 53890);
/* harmony import */ var _payment_components_top_up_choices_top_up_choices_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../payment/components/top-up-choices/top-up-choices.component */ 42836);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);







function AccountTopupComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 11)(1, "div", 8)(2, "top-up-form", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("PaymentSucceed", function AccountTopupComponent_div_15_Template_top_up_form_PaymentSucceed_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r1.backClicked());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("topUpAmount", ctx_r0.topUpAmount)("userBalance", ctx_r0.accountBalance);
  }
}
class AccountTopupComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(payService, _location) {
    super();
    this.payService = payService;
    this._location = _location;
  }
  ngOnInit() {
    this.subscriptionBalance = this.payService.SubscribeBalanceUpdate().subscribe(newBalance => this.accountBalance = newBalance);
    this.payService.UpdateBalance();
  }
  ngOnDestroy() {
    this.subscriptionBalance?.unsubscribe();
  }
  backClicked() {
    this._location.back();
  }
  TopUpAmountChanged(amount) {
    this.topUpAmount = amount;
  }
  static {
    this.ɵfac = function AccountTopupComponent_Factory(t) {
      return new (t || AccountTopupComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_1__.PayService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_6__.Location));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: AccountTopupComponent,
      selectors: [["app-account-topup"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵInheritDefinitionFeature"]],
      decls: 16,
      vars: 6,
      consts: [[1, "container-fluid"], [1, "row"], [1, "col-12"], ["text", "Account", 3, "navBack"], [1, "accountTitle"], [1, "row", "cardDefaultParent", "cardBalance"], [1, "col-12", "col-md-4", "col-lg-6"], [1, "balanceAmount", "align-items-center"], [1, "col-12", "col-md-8", "col-lg-6"], [3, "isNestedTopUp", "choiceChanged"], ["class", "row cardDefaultParent justify-content-center cardTopUp", 4, "ngIf"], [1, "row", "cardDefaultParent", "justify-content-center", "cardTopUp"], [3, "topUpAmount", "userBalance", "PaymentSucceed"]],
      template: function AccountTopupComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "nav-back-button", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("navBack", function AccountTopupComponent_Template_nav_back_button_navBack_3_listener() {
            return ctx.backClicked();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 1)(5, "div", 2)(6, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7, "Balance");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "div", 5)(9, "div", 6)(10, "p", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](12, "number");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "div", 8)(14, "payment-top-up-choices", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("choiceChanged", function AccountTopupComponent_Template_payment_top_up_choices_choiceChanged_14_listener($event) {
            return ctx.TopUpAmountChanged($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](15, AccountTopupComponent_div_15_Template, 3, 2, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"]("$", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](12, 3, ctx.accountBalance, "1.2-2"), "");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("isNestedTopUp", false);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.topUpAmount);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _payment_components_top_up_form_top_up_form_component__WEBPACK_IMPORTED_MODULE_2__.TopUpFormComponent, _payment_components_top_up_choices_top_up_choices_component__WEBPACK_IMPORTED_MODULE_3__.TopUpChoicesComponent, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_4__.NavBackButtonComponent, _angular_common__WEBPACK_IMPORTED_MODULE_6__.DecimalPipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.wrapperContainer[_ngcontent-%COMP%] {\n  margin-left: 0;\n  margin-right: 0;\n}\n@media (min-width: 767px) {\n  .wrapperContainer[_ngcontent-%COMP%] {\n    margin-left: 42px;\n    margin-right: 32px;\n  }\n}\n\n.accountTitle[_ngcontent-%COMP%] {\n  font-size: 22px;\n  font-family: \"bariol_bold\";\n  margin-left: 8px;\n  margin-top: 20px;\n  color: #333b44;\n}\n@media (min-width: 767px) {\n  .accountTitle[_ngcontent-%COMP%] {\n    margin-left: 0;\n    margin-top: 40px;\n  }\n}\n\n.balanceAmount[_ngcontent-%COMP%] {\n  font-style: \"bariol_regular\";\n  font-size: 44px;\n  border-radius: 12px;\n  color: #333b44;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n\n.cardBalance[_ngcontent-%COMP%] {\n  padding: 28px;\n}\n@media (min-width: 767px) {\n  .cardBalance[_ngcontent-%COMP%] {\n    margin-left: 0;\n    margin-right: 0;\n  }\n}\n\n.cardTopUp[_ngcontent-%COMP%] {\n  padding: 28px;\n}\n@media (max-width: 767px) {\n  .cardTopUp[_ngcontent-%COMP%] {\n    margin-left: -15px;\n    margin-right: -15px;\n    padding: 0;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 12765:
/*!*****************************************************************!*\
  !*** ./src/app/account/components/account/account.component.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountComponent: () => (/* binding */ AccountComponent)
/* harmony export */ });
/* harmony import */ var src_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/constants */ 36680);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../sharedModels */ 8872);
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/animations */ 47172);
/* harmony import */ var ng_animate__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ng-animate */ 77975);
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ngrx/store */ 81383);
/* harmony import */ var src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/states/user/user.selectors */ 92290);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _sharedServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../sharedServices */ 2902);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _payment_components_list_payment_methods_list_payment_methods_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../payment/components/list-payment-methods/list-payment-methods.component */ 58500);
/* harmony import */ var _shared_components_warning_warning_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../shared/components/warning/warning.component */ 42689);
/* harmony import */ var _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../schools-button/components/primary-button/primary-button.component */ 58666);














function AccountComponent_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](1, "div", 4)(2, "p", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, "Balance");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](4, "div", 20)(5, "p", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipe"](7, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](8, "div", 22)(9, "primary-button", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("onPress", function AccountComponent_ng_container_2_Template_primary_button_onPress_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r4);
      const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r3.TopUp());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](10, "div", 4)(11, "div", 5)(12, "p", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](13, "Payment Method");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](14, "div", 7)(15, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](16, "payment-list-payment-methods", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate1"](" $", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipeBind2"](7, 3, ctx_r0.accountBalance, "1.2-2"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("showDelete", true)("canSelect", false);
  }
}
function AccountComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 4)(1, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "app-warning", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function AccountComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 16)(1, "p", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2, " Check out our mobile apps! ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "a", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4, "iOS");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "a", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, "Android");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
  }
}
const _c0 = function () {
  return ["profile"];
};
const _c1 = function () {
  return ["billing-history"];
};
const _c2 = function () {
  return ["account-help"];
};
class AccountComponent extends _sharedModels__WEBPACK_IMPORTED_MODULE_1__.BaseComponent {
  constructor(userService, router, payService, store) {
    super();
    this.userService = userService;
    this.router = router;
    this.payService = payService;
    this.store = store;
    this.deactivatedUser = false;
    this.isDesktop = false;
    this.termsLink = src_constants__WEBPACK_IMPORTED_MODULE_0__.SPRIGGY_SCHOOLS_TERMS_LINK;
  }
  ngOnInit() {
    this.subscriptionBalance = this.payService.SubscribeBalanceUpdate().subscribe(newBalance => this.accountBalance = newBalance);
    this.connectedUserSubscription = this.store.pipe((0,_ngrx_store__WEBPACK_IMPORTED_MODULE_8__.select)(src_app_states_user_user_selectors__WEBPACK_IMPORTED_MODULE_2__.connectedUser)).subscribe(user => {
      this.deactivatedUser = !user.IsActive;
    });
    this.payService.UpdateBalance();
  }
  ngOnDestroy() {
    if (this.subscriptionBalance) {
      this.subscriptionBalance.unsubscribe();
    }
    if (this.connectedUserSubscription) {
      this.connectedUserSubscription.unsubscribe();
    }
  }
  SignOut() {
    this.userService.logout();
  }
  TopUp() {
    this.router.navigate(['family/account/account-topup']);
  }
  static {
    this.ɵfac = function AccountComponent_Factory(t) {
      return new (t || AccountComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_sharedServices__WEBPACK_IMPORTED_MODULE_3__.PayService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_ngrx_store__WEBPACK_IMPORTED_MODULE_8__.Store));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: AccountComponent,
      selectors: [["app-account"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵInheritDefinitionFeature"]],
      decls: 35,
      vars: 11,
      consts: [[1, "container-fluid"], [1, "wrapperContainer"], [4, "ngIf"], ["class", "row", 4, "ngIf"], [1, "row"], [1, "col-12"], [1, "accountTitle"], [1, "row", "cardDefaultParent"], [1, "col-12", "listItem", "top", 3, "routerLink"], ["src", "assets/icons/parentconfig.svg", 1, "listItemImage", "text-center"], [1, "listItemText", "text-center"], [1, "col-12", "listItem", "single", 3, "routerLink"], ["height", "30", "src", "assets/icons/attach_money.svg", 1, "listItemImage", "billing", "text-center"], ["height", "30", "src", "assets/icons/help-icon.svg", 1, "listItemImage", "text-center"], [1, "col-12", "listItem", "single", 3, "click"], ["src", "assets/icons/logout.svg", 1, "listItemImage", "text-center"], [1, "align-items-center"], ["target", "_blank", 3, "href"], ["target", "_blank", "href", "https://spriggyprodstorage.blob.core.windows.net/docs/cashlesslicence.html"], ["class", "align-items-center", 4, "ngIf"], [1, "row", "cardDefaultParent", "cardBalance"], [1, "col-12", "col-sm-9", "balanceAmount", "align-items-center"], [1, "col-12", "col-sm-3"], ["text", "Top up", "id", "top-up-flow-button", 3, "onPress"], [3, "showDelete", "canSelect"], ["title", "Your account is deactivated", "description", "Please get in touch with us if you would like to reopen your account."], [1, "appLinks"], ["target", "_blank", "href", "https://apps.apple.com/us/app/cashless/id1474589312?ls=1"], ["target", "_blank", "href", "https://play.google.com/store/apps/details?id=com.cashless.cashlessapp"]],
      template: function AccountComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](2, AccountComponent_ng_container_2_Template, 17, 6, "ng-container", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](3, AccountComponent_div_3_Template, 3, 0, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](4, "div", 4)(5, "div", 5)(6, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](7, "Settings");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](8, "div", 7)(9, "button", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](10, "img", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](11, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](12, "Profile");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](13, "button", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](14, "img", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](15, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](16, "Billing History");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](17, "button", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](18, "img", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](19, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](20, "Help");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](21, "button", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function AccountComponent_Template_button_click_21_listener() {
            return ctx.SignOut();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](22, "img", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](23, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](24, "Log Out");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](25, "div", 16)(26, "a", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](27, "Terms of Service");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](28, " - ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](29, "a", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](30, "Privacy Policy");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](31, " - ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](32, "a", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](33, "Software Licences");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](34, AccountComponent_div_34_Template, 7, 0, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx.deactivatedUser);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.deactivatedUser);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpureFunction0"](8, _c0));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpureFunction0"](9, _c1));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpureFunction0"](10, _c2));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("href", ctx.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsanitizeUrl"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("href", ctx.termsLink, _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsanitizeUrl"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.isDesktop);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterLink, _payment_components_list_payment_methods_list_payment_methods_component__WEBPACK_IMPORTED_MODULE_4__.ListPaymentMethodsComponent, _shared_components_warning_warning_component__WEBPACK_IMPORTED_MODULE_5__.WarningComponent, _schools_button_components_primary_button_primary_button_component__WEBPACK_IMPORTED_MODULE_6__.PrimaryButtonComponent, _angular_common__WEBPACK_IMPORTED_MODULE_10__.DecimalPipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n@font-face {\n  font-family: \"bariol_regular\";\n  font-display: swap;\n  src: url('bariol_regular-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_bold\";\n  font-display: swap;\n  src: url('bariol_bold-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_light\";\n  font-display: swap;\n  src: url('bariol_light-webfont.woff') format(\"woff\");\n}\n@font-face {\n  font-family: \"bariol_thin\";\n  font-display: swap;\n  src: url('bariol_thin-webfont.woff') format(\"woff\");\n}\n.mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.wrapperContainer[_ngcontent-%COMP%] {\n  margin-left: 0;\n  margin-right: 0;\n}\n@media (min-width: 767px) {\n  .wrapperContainer[_ngcontent-%COMP%] {\n    margin-left: 42px;\n    margin-right: 32px;\n  }\n}\n\n.accountTitle[_ngcontent-%COMP%] {\n  font-size: 22px;\n  font-family: \"bariol_bold\";\n  margin-left: 8px;\n  margin-top: 42px;\n  color: #333b44;\n}\n@media (min-width: 767px) {\n  .accountTitle[_ngcontent-%COMP%] {\n    margin-left: 0;\n  }\n}\n\n.balance[_ngcontent-%COMP%] {\n  background-color: white;\n  border-radius: 12px;\n}\n\n.balanceAmount[_ngcontent-%COMP%] {\n  font-style: \"bariol_regular\";\n  font-size: 44px;\n  border-radius: 12px;\n  color: #333b44;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n\n.cardBalance[_ngcontent-%COMP%] {\n  padding: 28px;\n}\n\n.PrimaryButton[_ngcontent-%COMP%] {\n  max-width: 100%;\n  margin-top: 24px;\n}\n@media (min-width: 767px) {\n  .PrimaryButton[_ngcontent-%COMP%] {\n    max-width: 159px;\n    margin-top: auto;\n  }\n}\n\n.PrimaryButtonTest[_ngcontent-%COMP%] {\n  max-width: 100%;\n  margin-top: 24px;\n}\n@media (min-width: 767px) {\n  .PrimaryButtonTest[_ngcontent-%COMP%] {\n    max-width: 300px;\n    margin-top: auto;\n  }\n}\n\n.listItem[_ngcontent-%COMP%] {\n  height: 72px;\n  background: white;\n  border-style: none;\n  color: #333b44;\n  cursor: pointer;\n  font-family: \"bariol_regular\";\n  font-size: 20px;\n  margin-top: auto;\n  margin-bottom: auto;\n  height: 72px;\n  width: 100%;\n  text-align: start;\n  border-width: 1px;\n  border-color: #dddddd;\n  display: flex;\n  outline: none;\n  align-items: center;\n}\n\n.listItemImage[_ngcontent-%COMP%] {\n  margin-left: 16px;\n  margin-right: 21px;\n}\n.listItemImage.billing[_ngcontent-%COMP%] {\n  margin-left: 12px;\n}\n\n.listItemText[_ngcontent-%COMP%] {\n  margin-top: auto;\n  margin-bottom: auto;\n  vertical-align: middle;\n}\n\n.top[_ngcontent-%COMP%] {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n  border-bottom-style: solid;\n}\n\n.bottom[_ngcontent-%COMP%] {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\n\n.single[_ngcontent-%COMP%] {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n  border-bottom-style: solid;\n}\n\n.hidden[_ngcontent-%COMP%] {\n  display: none;\n}\n\n.appLinks[_ngcontent-%COMP%] {\n  margin-top: 40px;\n}\n.appLinks[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  margin-left: 10px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_11__.trigger)('bounce', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_11__.transition)('* => *', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_11__.useAnimation)(ng_animate__WEBPACK_IMPORTED_MODULE_12__.fadeIn, {
          // Set the duration to 5seconds and delay to 2seconds
          params: {
            timing: 0.5,
            delay: 0
          }
        }))])]
      }
    });
  }
}

/***/ }),

/***/ 50177:
/*!*********************************************************************************!*\
  !*** ./src/app/account/components/billing-history/billing-history.component.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BillingHistoryComponent: () => (/* binding */ BillingHistoryComponent)
/* harmony export */ });
/* harmony import */ var src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/sharedServices */ 2902);
/* harmony import */ var _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../shared/components/nav-back-button/nav-back-button.component */ 11717);
// models





function BillingHistoryComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9)(1, "div", 3)(2, "p")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](5, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " Top Up ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "strong", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](11, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const topup_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](5, 2, topup_r6.Date, "EEEE d MMMM y"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" $", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](11, 5, topup_r6.TopupAmount, "1.2-2"), " ");
  }
}
function BillingHistoryComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9)(1, "div", 3)(2, "p")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "No Top Up History");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
}
function BillingHistoryComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9)(1, "div", 3)(2, "p")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](5, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " Account Credit ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "strong", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](11, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const topup_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](5, 2, topup_r7.Date, "EEEE d MMMM y"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" $", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](11, 5, topup_r7.TopupAmount, "1.2-2"), " ");
  }
}
function BillingHistoryComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9)(1, "div", 3)(2, "p")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "No Credit History");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
}
function BillingHistoryComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 1)(1, "div", 2)(2, "div", 3)(3, "p", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Billing History");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
}
function BillingHistoryComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9)(1, "div", 3)(2, "p")(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](5, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "br");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "strong", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](11, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "strong", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](14, ".");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](16, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const billing_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](5, 6, billing_r8.OrderDate, "EEEE d MMMM y"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Account Fee for ", billing_r8.StudentName, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate3"](" ", billing_r8.TermName, " (", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](11, 9, billing_r8.StartDate, "dd MMM"), " / ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](12, 12, billing_r8.EndDate, "dd MMM"), ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" $", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](16, 15, billing_r8.OrderAmount, "1.2-2"), " ");
  }
}
class BillingHistoryComponent extends src_app_sharedModels__WEBPACK_IMPORTED_MODULE_0__.BaseComponent {
  constructor(_location, userService, billingAPIService) {
    super();
    this._location = _location;
    this.userService = userService;
    this.billingAPIService = billingAPIService;
    this.listBilling = [];
    this.listTopUp = [];
    this.listCredit = [];
    this.hasTopUp = false;
    this.hasBilling = false;
    this.hasCredit = false;
  }
  ngOnInit() {
    let user = this.userService.GetUserConnected();
    if (user) {
      // top up history
      this.billingAPIService.GetTopUpHistory(user.UserId).subscribe({
        next: res => {
          this.listTopUp = res;
          if (res && res.length > 0) {
            this.hasTopUp = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        }
      });
      // credit history
      this.billingAPIService.GetCreditHistory(user.UserId).subscribe({
        next: res => {
          this.listCredit = res;
          if (res && res.length > 0) {
            this.hasCredit = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        }
      });
      // billing history
      this.billingAPIService.GetBillingHistory(user.UserId).subscribe({
        next: res => {
          this.listBilling = res;
          if (res && res.length > 0) {
            this.hasBilling = true;
          }
        },
        error: error => {
          this.handleErrorFromService(error);
        }
      });
    }
  }
  backClicked() {
    this._location.back();
  }
  static {
    this.ɵfac = function BillingHistoryComponent_Factory(t) {
      return new (t || BillingHistoryComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_4__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_sharedServices__WEBPACK_IMPORTED_MODULE_1__.BillingApiService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: BillingHistoryComponent,
      selectors: [["app-billing-history"]],
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵInheritDefinitionFeature"]],
      decls: 20,
      vars: 6,
      consts: [[1, "container-fluid"], [1, "wrapperContainer"], [1, "row"], [1, "col-12"], ["text", "Account", 3, "navBack"], [1, "accountTitle"], ["class", "row rowHistory", 4, "ngFor", "ngForOf"], ["class", "row rowHistory", 4, "ngIf"], ["class", "wrapperContainer", 4, "ngIf"], [1, "row", "rowHistory"], [1, "spacerDescription"]],
      template: function BillingHistoryComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "nav-back-button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("navBack", function BillingHistoryComponent_Template_nav_back_button_navBack_4_listener() {
            return ctx.backClicked();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 2)(6, "div", 3)(7, "p", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, "Top Up History");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, BillingHistoryComponent_div_9_Template, 12, 8, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, BillingHistoryComponent_div_10_Template, 5, 0, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](11, "div", 1)(12, "div", 2)(13, "div", 3)(14, "p", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15, "Credit History");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](16, BillingHistoryComponent_div_16_Template, 12, 8, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](17, BillingHistoryComponent_div_17_Template, 5, 0, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](18, BillingHistoryComponent_div_18_Template, 5, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](19, BillingHistoryComponent_div_19_Template, 17, 18, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.listTopUp);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.hasTopUp);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.listCredit);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.hasCredit);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.hasBilling);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.listBilling);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _shared_components_nav_back_button_nav_back_button_component__WEBPACK_IMPORTED_MODULE_2__.NavBackButtonComponent, _angular_common__WEBPACK_IMPORTED_MODULE_4__.DecimalPipe, _angular_common__WEBPACK_IMPORTED_MODULE_4__.DatePipe],
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.wrapperContainer[_ngcontent-%COMP%] {\n  margin-left: 0;\n  margin-right: 0;\n}\n@media (min-width: 767px) {\n  .wrapperContainer[_ngcontent-%COMP%] {\n    margin-left: 42px;\n    margin-right: 32px;\n  }\n}\n\n.accountTitle[_ngcontent-%COMP%] {\n  font-size: 22px;\n  font-family: \"bariol_bold\";\n  margin-left: 8px;\n  margin-top: 20px;\n  color: #333b44;\n}\n@media (min-width: 767px) {\n  .accountTitle[_ngcontent-%COMP%] {\n    margin-left: 0;\n    margin-top: 40px;\n  }\n}\n\n.balanceAmount[_ngcontent-%COMP%] {\n  font-style: \"bariol_regular\";\n  font-size: 44px;\n  border-radius: 12px;\n  color: #333b44;\n  margin-top: auto;\n  margin-bottom: auto;\n}\n\n.cardBalance[_ngcontent-%COMP%] {\n  padding: 28px;\n}\n@media (min-width: 767px) {\n  .cardBalance[_ngcontent-%COMP%] {\n    margin-left: 0;\n    margin-right: 0;\n  }\n}\n\n.cardTopUp[_ngcontent-%COMP%] {\n  padding: 28px;\n}\n@media (max-width: 767px) {\n  .cardTopUp[_ngcontent-%COMP%] {\n    margin-left: -15px;\n    margin-right: -15px;\n    padding: 0;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */", ".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.rowHistory[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #dddddd;\n  background-color: white;\n}\n.rowHistory[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  font-family: \"bariol_bold\";\n}\n\n.spacerDescription[_ngcontent-%COMP%] {\n  font-size: 30px;\n  line-height: 16px;\n  font-weight: bold;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjb3VudC9jb21wb25lbnRzL2JpbGxpbmctaGlzdG9yeS9iaWxsaW5nLWhpc3RvcnkuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS0E7RUFDRSxhQUFBO0FDSkY7QURLRTtFQUZGO0lBR0ksY0FBQTtFQ0ZGO0FBQ0Y7O0FES0E7RUFDRSxhQUFBO0FDRkY7QURHRTtFQUZGO0lBR0ksY0FBQTtFQ0FGO0FBQ0Y7O0FBZEE7RUFDRSxnQ0FBQTtFQUNBLHVCQUFBO0FBaUJGO0FBZkU7RUFDRSwwQkFBQTtBQWlCSjs7QUFSQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0FBV0YiLCJzb3VyY2VzQ29udGVudCI6WyIkYnJlYWtwb2ludC1zbTogNTc2cHg7XG4kYnJlYWtwb2ludC1tZDogNzY3cHg7XG4kYnJlYWtwb2ludC1sZzogOTkycHg7XG4kYnJlYWtwb2ludC14bDogMTIwMHB4O1xuXG4ubW9iaWxlIHtcbiAgZGlzcGxheTogbm9uZTtcbiAgQG1lZGlhIChtYXgtd2lkdGg6ICRicmVha3BvaW50LW1kKSB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cbi8vIE5PVEUgQ3VycmVudGx5IHRhYmxldCBhbmQgbW9iaWxlIGlzIHRoZSBzYW1lLiBDaGFuZ2UgdG8gJGJyZWFrcG9pbnQtbGcgbGF0ZXIgaWYgd2UgZ2V0IGEgcHJvcGVyIHRhYmxldCBkZXNpZ24uXG4uZGVza3RvcCB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWluLXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4iLCJAaW1wb3J0ICcuLi8uLi8uLi8uLi9zdHlsZXMvY2FzaGxlc3MtdGhlbWUuc2Nzcyc7XG5cbi5yb3dIaXN0b3J5IHtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICRncmV5LTM7XG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xuXG4gICYgc3Ryb25nIHtcbiAgICBmb250LWZhbWlseTogJ2JhcmlvbF9ib2xkJztcbiAgfVxufVxuXG4vLyAuZGF0ZUhpc3Rvcnl7XG4vLyAgICAgZm9udC1mYW1pbHk6IFwiYmFyaW9sX2JvbGRcIjtcbi8vICAgICBmb250LXNpemU6IDE2cHg7XG4vLyB9XG5cbi5zcGFjZXJEZXNjcmlwdGlvbiB7XG4gIGZvbnQtc2l6ZTogMzBweDtcbiAgbGluZS1oZWlnaHQ6IDE2cHg7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 2293:
/*!*************************************************************************************!*\
  !*** ./src/app/account/components/close-account-row/close-account-row.component.ts ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CloseAccountRowComponent: () => (/* binding */ CloseAccountRowComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 37580);

class CloseAccountRowComponent {
  constructor() {}
  ngOnInit() {}
  static {
    this.ɵfac = function CloseAccountRowComponent_Factory(t) {
      return new (t || CloseAccountRowComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: CloseAccountRowComponent,
      selectors: [["close-account-row"]],
      inputs: {
        text: "text",
        rowNumber: "rowNumber"
      },
      decls: 5,
      vars: 2,
      consts: [[1, "d-flex", "align-items-center", "list-spacing"], [1, "list-number"]],
      template: function CloseAccountRowComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementStart"](3, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.rowNumber);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵtextInterpolate"](ctx.text);
        }
      },
      styles: [".mobile[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (max-width: 767px) {\n  .mobile[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.desktop[_ngcontent-%COMP%] {\n  display: none;\n}\n@media (min-width: 767px) {\n  .desktop[_ngcontent-%COMP%] {\n    display: block;\n  }\n}\n\n.list-spacing[_ngcontent-%COMP%] {\n  gap: 15px;\n  padding: 8px 0;\n}\n\n.list-number[_ngcontent-%COMP%] {\n  border: 1px solid #f36600;\n  color: #f36600;\n  border-radius: 16px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 20px;\n  font-weight: 700;\n  width: 32px;\n  height: 32px;\n  min-width: 32px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zdHlsZXMvY2FzaGxlc3MtYnJlYWtwb2ludHMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjb3VudC9jb21wb25lbnRzL2Nsb3NlLWFjY291bnQtcm93L2Nsb3NlLWFjY291bnQtcm93LmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vc3JjL3N0eWxlcy9jYXNobGVzcy10aGVtZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUtBO0VBQ0UsYUFBQTtBQ0pGO0FES0U7RUFGRjtJQUdJLGNBQUE7RUNGRjtBQUNGOztBREtBO0VBQ0UsYUFBQTtBQ0ZGO0FER0U7RUFGRjtJQUdJLGNBQUE7RUNBRjtBQUNGOztBQWZBO0VBQ0UsU0FBQTtFQUNBLGNBQUE7QUFrQkY7O0FBaEJBO0VBQ0UseUJBQUE7RUFDQSxjQzBCVTtFRHpCVixtQkFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBbUJGIiwic291cmNlc0NvbnRlbnQiOlsiJGJyZWFrcG9pbnQtc206IDU3NnB4O1xuJGJyZWFrcG9pbnQtbWQ6IDc2N3B4O1xuJGJyZWFrcG9pbnQtbGc6IDk5MnB4O1xuJGJyZWFrcG9pbnQteGw6IDEyMDBweDtcblxuLm1vYmlsZSB7XG4gIGRpc3BsYXk6IG5vbmU7XG4gIEBtZWRpYSAobWF4LXdpZHRoOiAkYnJlYWtwb2ludC1tZCkge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG4vLyBOT1RFIEN1cnJlbnRseSB0YWJsZXQgYW5kIG1vYmlsZSBpcyB0aGUgc2FtZS4gQ2hhbmdlIHRvICRicmVha3BvaW50LWxnIGxhdGVyIGlmIHdlIGdldCBhIHByb3BlciB0YWJsZXQgZGVzaWduLlxuLmRlc2t0b3Age1xuICBkaXNwbGF5OiBub25lO1xuICBAbWVkaWEgKG1pbi13aWR0aDogJGJyZWFrcG9pbnQtbWQpIHtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuIiwiQGltcG9ydCAnLi4vLi4vLi4vLi4vc3R5bGVzL2Nhc2hsZXNzLXRoZW1lLnNjc3MnO1xuLmxpc3Qtc3BhY2luZyB7XG4gIGdhcDogMTVweDtcbiAgcGFkZGluZzogOHB4IDA7XG59XG4ubGlzdC1udW1iZXIge1xuICBib3JkZXI6IDFweCBzb2xpZCAkb3JhbmdlLTEwO1xuICBjb2xvcjogJG9yYW5nZS0xMDtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogMjBweDtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgd2lkdGg6IDMycHg7XG4gIGhlaWdodDogMzJweDtcbiAgbWluLXdpZHRoOiAzMnB4O1xufVxuIiwiQGltcG9ydCAnY2FzaGxlc3MtYnJlYWtwb2ludHMnO1xuXG4vLyBQcmltYXJ5IGNvbG91cnNcblxuJGJsdWUtMTogIzFlYTNjZTtcbiRibHVlLTI6ICM0NzU0YjA7XG4kYmx1ZS0zOiAjMTQ0Y2RjO1xuXG4kcmVkLTE6ICNmMTQ3NjI7XG4kcmVkLTI6ICNmZmViZWI7XG4kcmVkLTM6ICNjMDQ1NDU7XG4kcmVkLTQ6ICNmZmNmY2M7XG5cbiRwdXJwbGUtMTogIzdmM2RiMztcbiRuYXZ5LTE6ICMxYzQyNzA7XG4kY2hhcmNvYWwtMTogIzMzM2I0NDtcblxuJGdyZWVuLTE6ICMwMGJhNmI7XG4kZ3JlZW4tMjogI2Q5ZjVlOTtcbiRncmVlbi0zOiAjMDA2ZjQ5O1xuJGdyZWVuLTQ6ICNlM2Y1ZWY7XG4kZ3JlZW4tNTogI2RmZmZmMDtcblxuLy8gT3JhbmdlXG4kb3JhbmdlLTE6ICNmZjllMDA7XG4kb3JhbmdlLTM6ICNmZjdhMDA7XG4kb3JhbmdlLTI6ICNmZjRiMTc7XG4kb3JhbmdlLTQ6ICNmZmUyYzc7XG4kb3JhbmdlLTU6ICNmZmYyZTY7XG4kb3JhbmdlLTY6ICNmZjgwMDA7XG4kb3JhbmdlLTc6ICNmZmVhZDY7XG4kb3JhbmdlLTg6ICNmZWYwZTA7XG4kb3JhbmdlLTk6ICNmZmYwZTA7XG4kb3JhbmdlLTEwOiAjZjM2NjAwO1xuJG9yYW5nZS0xMTogI2ZmZTNiYjtcbiRtb2JpbGUtZGFyay1vcmFuZ2U6ICNEOTVCMDA7XG4kbW9iaWxlLWxpZ2h0LW9yYW5nZTogI0ZGRUFENjtcblxuLy8gZ3JleVxuJGdyZXktMTogIzg4OTQ5ZjtcbiRncmV5LTI6ICNlMGUxZTI7XG4kZ3JleS0zOiAjZGRkZGRkO1xuJGdyZXktNDogI2YyZjJmMjtcbiRncmV5LTU6ICMzMzNiNDQ7XG4kZ3JleS02OiAjZTVlNWU1O1xuJGdyZXktNzogI2I5YjljODtcbiRncmV5LTg6ICM4Nzg3ODc7XG4kZ3JleS05OiAjZTBlMGUwO1xuJGdyZXktMTA6ICNiZGJkYmQ7XG4kZ3JleS0xMTogIzgyODI4MjtcbiRncmV5LTEyOiAjMWIxZjM5O1xuJGdyZXktMTM6ICNiOGI4Yjg7XG4kZ3JleS0xNDogIzI3MmM1MDtcbiRncmV5LTE1OiAjZjZmNWYzO1xuJGdyZXktMTY6ICNmYWY5Zjg7XG4kZ3JleS0xNzogIzZiNmM4OTtcblxuLy8gU2Vjb25kYXJ5IGNvbG91cnNcbiRibHVlLXNlY29uZGFyeS0xOiByZ2JhKDI1NSwgMjQzLCAyMTksIDEpO1xuIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 27403:
/*!*********************************************!*\
  !*** ./src/app/account/components/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccountAboutComponent: () => (/* reexport safe */ _account_about_account_about_component__WEBPACK_IMPORTED_MODULE_1__.AccountAboutComponent),
/* harmony export */   AccountCloseComponent: () => (/* reexport safe */ _account_close_account_close_component__WEBPACK_IMPORTED_MODULE_6__.AccountCloseComponent),
/* harmony export */   AccountComponent: () => (/* reexport safe */ _account_account_component__WEBPACK_IMPORTED_MODULE_0__.AccountComponent),
/* harmony export */   AccountEditComponent: () => (/* reexport safe */ _account_edit_account_edit_component__WEBPACK_IMPORTED_MODULE_2__.AccountEditComponent),
/* harmony export */   AccountHelpComponent: () => (/* reexport safe */ _account_help_account_help_component__WEBPACK_IMPORTED_MODULE_5__.AccountHelpComponent),
/* harmony export */   AccountTopupComponent: () => (/* reexport safe */ _account_topup_account_topup_component__WEBPACK_IMPORTED_MODULE_3__.AccountTopupComponent),
/* harmony export */   BillingHistoryComponent: () => (/* reexport safe */ _billing_history_billing_history_component__WEBPACK_IMPORTED_MODULE_4__.BillingHistoryComponent),
/* harmony export */   CloseAccountRowComponent: () => (/* reexport safe */ _close_account_row_close_account_row_component__WEBPACK_IMPORTED_MODULE_7__.CloseAccountRowComponent)
/* harmony export */ });
/* harmony import */ var _account_account_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./account/account.component */ 12765);
/* harmony import */ var _account_about_account_about_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./account-about/account-about.component */ 34637);
/* harmony import */ var _account_edit_account_edit_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./account-edit/account-edit.component */ 4463);
/* harmony import */ var _account_topup_account_topup_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./account-topup/account-topup.component */ 5086);
/* harmony import */ var _billing_history_billing_history_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./billing-history/billing-history.component */ 50177);
/* harmony import */ var _account_help_account_help_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./account-help/account-help.component */ 90121);
/* harmony import */ var _account_close_account_close_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./account-close/account-close.component */ 38753);
/* harmony import */ var _close_account_row_close_account_row_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./close-account-row/close-account-row.component */ 2293);









/***/ })

}]);
//# sourceMappingURL=default-src_app_account_account_module_ts.js.map