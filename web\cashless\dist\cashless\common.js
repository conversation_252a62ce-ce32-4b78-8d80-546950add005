"use strict";
(self["webpackChunkcashless"] = self["webpackChunkcashless"] || []).push([["common"],{

/***/ 17730:
/*!******************************************************!*\
  !*** ./src/app/sharedServices/brazeTimer.service.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BrazeTimerService: () => (/* binding */ BrazeTimerService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 14876);
/* harmony import */ var _braze_web_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @braze/web-sdk */ 93698);
/* harmony import */ var _sharedModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../sharedModels */ 8872);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);




class BrazeTimerService {
  constructor() {
    this.INTERVAL_TIME = 300000; // 5 minutes
    this.PARENT_EVENT_KEY = 'WebPortalParentRefresh';
    this.MERCHANT_EVENT_KEY = 'WebPortalMerchantRefresh';
    this.refreshCount = 0;
  }
  setUpBrazeInAppMessageTimer(role) {
    this.brazeKey = role === _sharedModels__WEBPACK_IMPORTED_MODULE_0__.Roles.Parent ? this.PARENT_EVENT_KEY : this.MERCHANT_EVENT_KEY;
    if (this.timerSubscription) {
      return;
    }
    this.timerSubscription = (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.timer)(0, this.INTERVAL_TIME);
    this.brazeTimerSubscription = this.timerSubscription.subscribe(res => {
      if (this.refreshCount > 20) {
        return;
      }
      _braze_web_sdk__WEBPACK_IMPORTED_MODULE_2__.logCustomEvent(this.brazeKey);
      this.refreshCount++;
    });
  }
  unsubscribeFromBrazeTimer() {
    this.timerSubscription = null;
    if (this.brazeTimerSubscription) {
      this.brazeTimerSubscription.unsubscribe();
    }
  }
  static {
    this.ɵfac = function BrazeTimerService_Factory(t) {
      return new (t || BrazeTimerService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: BrazeTimerService,
      factory: BrazeTimerService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 61548:
/*!***************************************************!*\
  !*** ./src/app/states/family/family.selectors.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MenuNameSelect: () => (/* binding */ MenuNameSelect),
/* harmony export */   MenuPickerSelect: () => (/* binding */ MenuPickerSelect),
/* harmony export */   appState: () => (/* binding */ appState),
/* harmony export */   datePickerSelect: () => (/* binding */ datePickerSelect),
/* harmony export */   dayDetail: () => (/* binding */ dayDetail),
/* harmony export */   familyState: () => (/* binding */ familyState),
/* harmony export */   selectedWeek: () => (/* binding */ selectedWeek)
/* harmony export */ });
/* harmony import */ var _ngrx_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ngrx/store */ 81383);

const appState = state => state;
const familyState = state => state.family;
const dayDetail = state => state.family?.dayDetail || null;
const selectedWeek = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(familyState, state => state.selectedWeek);
const MenuPickerSelect = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(familyState, state => state.dayDetail?.MenuType);
const MenuNameSelect = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(familyState, state => state.dayDetail?.MenuName);
const datePickerSelect = (0,_ngrx_store__WEBPACK_IMPORTED_MODULE_0__.createSelector)(familyState, state => state.dayDetail?.Date);

/***/ }),

/***/ 92471:
/*!********************************************!*\
  !*** ./src/app/utility/timezone-helper.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   convertSchoolDateTimeToLocalDateTime: () => (/* binding */ convertSchoolDateTimeToLocalDateTime),
/* harmony export */   convertSchoolTimeToLocalTime: () => (/* binding */ convertSchoolTimeToLocalTime)
/* harmony export */ });
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ 39545);
/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);

const convertSchoolTimeToLocalTime = (time, utcOffSet) => {
  const momentTime = moment__WEBPACK_IMPORTED_MODULE_0___default()(time, 'HH:mm');
  const offSetTime = addUtcOffSetToMomentValue(momentTime, utcOffSet);
  //convert iso format date back to local time on users device
  return moment__WEBPACK_IMPORTED_MODULE_0___default()(offSetTime).local().format('HH:mm A');
};
const convertSchoolDateTimeToLocalDateTime = (dateTime, utcOffSet) => {
  const momentDateTime = moment__WEBPACK_IMPORTED_MODULE_0___default()(dateTime);
  const offSetDateTime = addUtcOffSetToMomentValue(momentDateTime, utcOffSet);
  //convert iso format date back to local time on users device in NSW
  return moment__WEBPACK_IMPORTED_MODULE_0___default()(offSetDateTime).local().format('YYYY-MM-DD HH:mm');
};
const addUtcOffSetToMomentValue = (moment, utcOffSet) => {
  // set the time utc offset as the schools local utc offset (transforms date to iso format)
  return moment.utcOffset(utcOffSet * 60, true);
};

/***/ }),

/***/ 93698:
/*!******************************************************************!*\
  !*** ./node_modules/@braze/web-sdk/src/Core/log-custom-event.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logCustomEvent: () => (/* binding */ logCustomEvent)
/* harmony export */ });
/* harmony import */ var _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../managers/braze-instance.js */ 30186);
/* harmony import */ var _common_event_logger_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../common/event-logger.js */ 86860);
/* harmony import */ var _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared-lib/braze-shared-lib.js */ 37366);
/* harmony import */ var _triggers_models_trigger_events_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../triggers/models/trigger-events.js */ 50646);
/* harmony import */ var _triggers_triggers_provider_factory_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../triggers/triggers-provider-factory.js */ 79001);
/* harmony import */ var _util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/validation-utils.js */ 27607);
/* harmony import */ var _common_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../common/constants.js */ 12238);







function logCustomEvent(t, o) {
  if (!_managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].rr()) return !1;
  if (null == t || t.length <= 0) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.error(`logCustomEvent requires a non-empty eventName, got "${t}". Ignoring event.`), !1;
  if (!(0,_util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__.validateCustomString)(t, "log custom event", "the event name")) return !1;
  const [n, i] = (0,_util_validation_utils_js__WEBPACK_IMPORTED_MODULE_2__.validateCustomProperties)(o, _common_constants_js__WEBPACK_IMPORTED_MODULE_3__.LOG_CUSTOM_EVENT_STRING, "eventProperties", `log custom event "${t}"`, "event");
  if (!n) return !1;
  const m = _managers_braze_instance_js__WEBPACK_IMPORTED_MODULE_0__["default"].tr();
  if (m && m.ge(t)) return _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Custom Event "${t}" is blocklisted, ignoring.`), !1;
  const g = _common_event_logger_js__WEBPACK_IMPORTED_MODULE_4__["default"].N(_shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].q.CustomEvent, {
    n: t,
    p: i
  });
  if (g.O) {
    _shared_lib_braze_shared_lib_js__WEBPACK_IMPORTED_MODULE_1__["default"].j.info(`Logged custom event "${t}".`);
    for (const e of g.ve) _triggers_triggers_provider_factory_js__WEBPACK_IMPORTED_MODULE_5__.TriggersProviderFactory.er().be(_triggers_models_trigger_events_js__WEBPACK_IMPORTED_MODULE_6__["default"].$e, [t, o], e);
  }
  return g.O;
}

/***/ })

}]);
//# sourceMappingURL=common.js.map