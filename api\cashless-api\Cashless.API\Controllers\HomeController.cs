﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Cashless.APIs.Attributes;
using Schools.BLL.Classes.Screens;
using Schools.BLL.Assemblers.Users;
using Schools.BLL.Helpers;
using Schools.BLL.Services.Interfaces;
using Schools.BLL.Validators;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class HomeController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IAuthenticationValidator _authenticationValidator;
    private readonly ISchoolEventService _schoolEventService;
    private readonly IMenuService _menuService;
    private readonly IOrderService _orderService;
    private readonly ISchoolService _schoolService;
    private readonly INoticeService _noticeService;
    private readonly ISchoolFeatureService _schoolFeatureService;
    private readonly ISupportedVersionsService _supportedVersionsService;

    public HomeController(IUserService userService, ISupportedVersionsService supportedVersionsService,
        IAuthenticationValidator authenticationValidator, ISchoolEventService schoolEventService,
        IMenuService menuService, IOrderService orderService, ISchoolService schoolService,
        ISchoolFeatureService schoolFeatureService, INoticeService noticeService)
    {
        _userService = userService;
        _supportedVersionsService = supportedVersionsService;
        _authenticationValidator = authenticationValidator;
        _schoolEventService = schoolEventService;
        _menuService = menuService;
        _orderService = orderService;
        _schoolService = schoolService;
        _noticeService = noticeService;
        _schoolFeatureService = schoolFeatureService;
    }

    [Route("LoginSuccess")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Parent)]
    public async Task<IActionResult> LoginSuccess()
    {
        var user = await _userService.GetCurrentUserWithHMAC(true);

        GetUserWithStudentsDto response = new()
        {
            User = UserAssembler.ConvertUserEntityToUserDto(user),
            Students = await _userService.GetStudentsByParentIdV2(user.UserId),
            SupportedVersions = _supportedVersionsService.GetSupportedVersions()
        };

        return new OkObjectResult(response);
    }

    [Route("")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Parent)]
    public async Task<IActionResult> Home([FromBody] HomeScreenRefreshRequest request)
    {
        var student = await _authenticationValidator.ValidateParentAccessToStudent(request.StudentId);

        HomeScreenDto response = new()
        {
            Events = await _schoolEventService.GetEventByStudent(student),
            Menus = await _menuService.GetMenusWithUtcCutOffTimesByStudent(student),
            Orders = await _orderService.GetOrdersByStudentWithFormat(request),
            SchoolDates = await _schoolService.GetSchoolDatesBySchoolId(Utils.NullableIntToLong(student.SchoolId), DateTime.Parse(request.StartOrderDate), DateTime.Parse(request.EndOrderDate)),
            Notices = await _noticeService.GetActiveSchoolNotices(Convert.ToInt64(student.SchoolId)),
            HasUniformMenu = await _schoolFeatureService.HasActiveUniformShop(Convert.ToInt32(student.SchoolId)),
            SupportedAppVersions = _supportedVersionsService.GetSupportedVersions()
        };

        return new OkObjectResult(response);
    }

    [Route("HomeRefresh")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Parent)]
    public async Task<IActionResult> HomeChangeWeek([FromBody] HomeScreenRefreshRequest request)
    {
        var student = await _authenticationValidator.ValidateParentAccessToStudent(request.StudentId);

        HomeScreenRefreshDto response = new()
        {
            Orders = await _orderService.GetOrdersByStudentWithFormat(request),
            SchoolDates = await _schoolService.GetSchoolDatesBySchoolId(Utils.NullableIntToLong(student.SchoolId), DateTime.Parse(request.StartOrderDate), DateTime.Parse(request.EndOrderDate))
        };

        // events optional
        if (request.LoadEvents == true)
        {
            response.Events = await _schoolEventService.GetEventByStudent(student);
        }

        return new OkObjectResult(response);
    }
}

