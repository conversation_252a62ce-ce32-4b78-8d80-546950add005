﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes.SchoolFeature;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;

namespace Cashless.APIs.Controllers;

[Authorize]
[Route("api/[controller]")]
[ApiController]
public class SchoolFeatureController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly ISchoolFeatureService _schoolFeatureService;
    private readonly ISchoolValidator _schoolValidation;

    public SchoolFeatureController(IUserService userService, ISchoolFeatureService schoolFeatureService, ISchoolValidator schoolValidation)
    {
        _userService = userService;
        _schoolFeatureService = schoolFeatureService;
        _schoolValidation = schoolValidation;
    }

    /// <summary>
    /// Get all active features for the given school
    /// </summary>
    [Route("{schoolId}")]
    [HttpGet]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin, UserRole.Merchant, UserRole.Parent)]
    public async Task<IActionResult> GetFeaturesBySchool(int schoolId)
    {
        var user = await _userService.GetCurrentUser();

        // Parents use this endpoint to check features at a School
        // before adding Children. This validation will fail if we
        // check BEFORE a Child is added 
        if (!user.IsParent)
        {
            await _schoolValidation.ValidateAccessToSchool(schoolId);
        }

        // Get active features only
        AdminSchoolFeaturesDto res = await _schoolFeatureService.GetActiveSchoolFeatures(schoolId);

        return new OkObjectResult(res);
    }

    /// <summary>
    /// update feature
    /// </summary>
    [Route("{schoolId}")]
    [HttpPost]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    [CheckUserRole(UserRole.Admin)]
    public async Task<IActionResult> CreateFeature(int schoolId, [FromBody] List<SchoolFeatureDto> request)
    {
        await _schoolFeatureService.UpdateListSchoolFeatures(schoolId, request);

        return new OkResult();
    }
}

