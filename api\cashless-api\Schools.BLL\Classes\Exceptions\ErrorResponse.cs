using System;
using System.Collections.Generic;
using System.Net;
using Schools.BLL.Exceptions;

namespace Schools.BLL.Classes;

/// <summary>
/// Convert an exception into a response similar to the response
/// returned by the .NET MVC framework on error. For example, when a
/// ValdationException is encountered, the following JSON is returned:
/// {
///     "errors": {
///         "StartDate": [
///             "The StartDate field is required."
///         ]
///     },
///     "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
///     "title": "One or more validation errors occurred.",
///     "status": 400
/// }
/// </summary>
public class ErrorResponse
{
    public IDictionary<string, string[]> Errors { get; set; }
    public string[] Error { get; set; }
    public string Type { get; set; }
    public string Title { get; set; }
    public int Status { get; set; }

    public ErrorResponse()
    {
        Errors = new Dictionary<string, string[]>();
    }

    /// <summary>
    /// Convert the given exception into a generic error response
    /// similar to the response returned by the MVC core framework
    /// </summary>
    public ErrorResponse(Exception ex) : this()
    {
        if (ex is DeprecatedApiException)
        {
            // Send back a 299 response
            DeprecatedApiException dae = (DeprecatedApiException)ex;

            string[] errorMessages = { dae.Message };

            Errors.Add(ex.GetType().Name, errorMessages);
            Error = [dae.Message];
            Type = "https://www.rfc-editor.org/rfc/rfc7234#section-5.5";
            Title = "One or more deprecation errors occurred.";
            Status = 299;
        }
        else if (ex is UnauthorizedAccessException)
        {
            // Send back a 401 response
            UnauthorizedAccessException uae = (UnauthorizedAccessException)ex;

            string[] errorMessages = { uae.Message };

            Errors.Add(ex.GetType().Name, errorMessages);
            Error = [uae.Message];
            Type = "https://www.rfc-editor.org/rfc/rfc7235#section-3.1";
            Title = "One or more security errors occurred.";
            Status = (int)HttpStatusCode.Unauthorized;
        }
        else if (ex is ValidationException)
        {
            // Send back a 400 response
            ValidationException ve = (ValidationException)ex;

            string[] errorMessages = { ve.Message };

            Errors.Add(ve.FieldName, errorMessages);
            Error = [ve.Message];
            Type = "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.1";
            Title = "One or more validation errors occurred.";
            Status = (int)HttpStatusCode.BadRequest;
        }
        else if (ex is NotFoundException)
        {
            // Send back a 404 response
            ValidationException ve = (ValidationException)ex;

            string[] errorMessages = { ve.Message };

            Errors.Add(ve.FieldName, errorMessages);
            Error = [ve.Message];
            Type = "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.4";
            Title = "Target not found";
            Status = (int)HttpStatusCode.NotFound;
        }
        else if (ex is TopUpException)
        {
            // Send back a 402 response
            TopUpException tope = (TopUpException)ex;

            string[] errorMessages = { tope.Message };

            Errors.Add(ex.GetType().Name, errorMessages);
            Error = [tope.Message];
            Type = "https://www.rfc-editor.org/rfc/rfc7231#section-6.5.2";
            Title = "Payment Failed";
            Status = (int)HttpStatusCode.PaymentRequired;
        }
        else
        {
            // Send back a generic 500 response
            // Errors.Add(ex.GetType().Name, errorMessages); => we don't want to send back error details to the client side
            Type = "https://www.rfc-editor.org/rfc/rfc7231#section-6.6.1";
            Title = "One or more server errors occurred.";
            Status = (int)HttpStatusCode.InternalServerError;
        }
    }
}
