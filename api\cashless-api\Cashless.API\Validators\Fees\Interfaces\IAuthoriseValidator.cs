using System.Collections.Generic;
using System.Threading.Tasks;
using Schools.BLL.Classes.Fees.Requests;
using Schools.DAL.Extensions.Entities;

namespace Cashless.APIs.Validators.Fees;

public interface IAuthoriseValidator
{
    Task ValidateUserAccess(FeeEntity fee);
    Task ValidateUserAccess(IEnumerable<FeeEntity> fees);
    Task ValidateUserAccess(long parentId, long childId, long schoolId);
    Task ValidateUserAccess(IEnumerable<FeesRequest> request);
}