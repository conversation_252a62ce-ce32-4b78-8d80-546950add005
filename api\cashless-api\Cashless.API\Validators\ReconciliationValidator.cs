using System;
using Schools.BLL.Exceptions;
using Schools.BLL.ThirdParty.General;
using Microsoft.Extensions.Logging;

namespace Cashless.APIs.Validators;

public interface IReconciliationValidator
{
    void ValidateReconciliationBetweenDatesRequest(ReconciliationRequest request);
}

public class ReconciliationValidator : IReconciliationValidator
{
    private readonly ILogger<PrintingValidator> _logger;
    public ReconciliationValidator(ILogger<PrintingValidator> logger)
    {
        _logger = logger;
    }

    public void ValidateReconciliationBetweenDatesRequest(ReconciliationRequest request)
    {
        if (request == null) throw new ValidationException("Request must be provided");
        if (request.UserId <= 0) throw new ValidationException("Invalid User id");

        if (request.StartDate == null ^ request.EndDate == null) throw new ValidationException("Both start and end date must be provided");

        if (request.StartDate != null)
        {
            if (DateTime.Parse(request.EndDate) < DateTime.Parse(request.StartDate)) throw new ValidationException("End date cannot be before the start date");
        }
    }
}