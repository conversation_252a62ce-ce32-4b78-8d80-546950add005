﻿using Newtonsoft.Json;

namespace Schools.BLL.Classes
{
    public class NutritionalValue
    {
        /*
        [JsonProperty(PropertyName = "NutritionalId")]
        public int? NutritionalId { get; set; }

        [JsonProperty(PropertyName = "StateId")]
        public int? StateId { get; set; }
        */

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        [JsonProperty(PropertyName = "Color")]
        public string Color { get; set; }
    }
}