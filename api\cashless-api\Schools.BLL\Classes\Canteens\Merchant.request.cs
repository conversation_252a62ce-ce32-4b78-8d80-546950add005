﻿using System;
using System.Collections.Generic;

namespace Schools.BLL.Classes.Canteens;

public class UpdateMerchantFeeRequest
{
    public string MerchantFeeStartDate { get; set; }
    public Decimal? MerchantFee { get; set; }
    public Decimal? ParentFeeCoveredByMerchants { get; set; }
}

public class UpsertMerchantUserRequest
{
    public int UserId { get; set; }
    public List<int> Schools { get; set; }
    public MerchantPermission Permissions { get; set; }
}

public class CreateMerchantRequest
{
    public UpdateMerchantOwnerRequest Owner { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
}

public class UpdateMerchantOwnerRequest
{
    public int OwnerId { get; set; }
    public string OwnerFirstName { get; set; }
    public string OwnerLastName { get; set; }
    public string OwnerMobile { get; set; }
}

public class UpdateMerchantDetailsRequest
{
    public string Name { get; set; }
    public string FriendlyName { get; set; }
    public string Phone { get; set; }
    public string Abn { get; set; }
    public string Status { get; set; }
    public string StatusDate { get; set; }
}
