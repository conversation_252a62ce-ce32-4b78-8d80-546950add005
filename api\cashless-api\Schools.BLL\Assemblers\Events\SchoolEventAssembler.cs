﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Constants;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.QueryResults;

namespace Schools.BLL.Assemblers;

public class SchoolEventAssembler
{
    public StudentEventDto ConvertStudentEventDatabaseResponseDtoToStudentEventDto(StudentEventDatabaseResponseDto entity)
    {
        StudentEventDto dto = new()
        {
            SchoolEventId = entity.SchoolEventId,
            SchoolId = entity.SchoolId,
            MerchantId = Convert.ToInt32(entity.MerchantId),
            MenuId = Convert.ToInt32(entity.MenuId),
            Name = entity.Name,
            Description = entity.Description,
            CutOffDate = entity.CutOffDate,
            EventDate = entity.EventDate,
            WeeksPreOrder = entity.WeeksPreOrder > 0 ? Convert.ToInt32(entity.WeeksPreOrder) : SchoolConstants.DefaultWeeksPreOrder,
            OrderId = entity.OrderId,
            OrderStatusId = entity.OrderStatusId,
            ImageUrl = entity.ImageUrl
        };

        return dto;
    }

    public List<StudentEventDto> ConvertListStudentEventDatabaseResponseDtoToListStudentEventDto(IEnumerable<StudentEventDatabaseResponseDto> request)
    {
        List<StudentEventDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(ConvertStudentEventDatabaseResponseDtoToStudentEventDto(e));
            }
        }

        return dto;
    }

    public List<EventItemsViewDto> Convert_List_EventItemsDetailsResult_To_List_EventItemsViewDto(IEnumerable<EventItemsDetailsResult> results)
    {
        List<EventItemsViewDto> dto = new();

        if (results != null && results.Any())
        {
            foreach (var e in results)
            {
                var item = dto.Find(i => i.MenuItemId == e.MenuItemId);

                if (item == null)
                {
                    // item doesn't exist in Dto
                    EventItemsViewDto newItem = new()
                    {
                        MenuItemId = e.MenuItemId,
                        Name = e.Name,
                        ImageUrl = e.ImageUrl,
                        Price = e.Price,
                        Options = new()
                    };

                    if (e.DailyStock != null || e.StockQuantity != null)
                    {
                        newItem.Stock = e.DailyStock != null && e.DailyStock >= 0 ? e.DailyStock : e.StockQuantity;
                    }

                    if (!String.IsNullOrEmpty(e.OptionType))
                    {
                        // Add new option to the item
                        newItem.Options.Add(this.GetOptionDtoFrom(e));
                    }

                    dto.Add(newItem);
                }
                else
                {
                    // item already exist in DTO
                    if (!String.IsNullOrEmpty(e.OptionType))
                    {
                        // search option
                        var option = item.Options.Find(o => o.Name == e.OptionType);

                        if (option != null)
                        {
                            // add choice to the current option
                            option.Choices.Add(this.GetChoiceDtoFrom(e));
                        }
                        else
                        {
                            // Add new option to the item
                            item.Options.Add(this.GetOptionDtoFrom(e));
                        }
                    }
                }
            }
        }

        return dto;
    }

    private EventItemsOPtionChoiceViewDto GetChoiceDtoFrom(EventItemsDetailsResult e)
    {
        return new EventItemsOPtionChoiceViewDto()
        {
            Name = e.OptionName,
            Cost = e.OptionCost
        };
    }

    private EventItemsOptionViewDto GetOptionDtoFrom(EventItemsDetailsResult e)
    {
        return new EventItemsOptionViewDto()
        {
            OptionId = e.MenuItemOptionsCategoryId,
            Name = e.OptionType,
            Choices = [
                this.GetChoiceDtoFrom(e)
            ]
        };
    }
}