using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Schools.BLL.Classes;

public class SalesReportBySchoolIdItem
{
    [JsonProperty(PropertyName = "Quantity")]
    public uint Quantity { get; }
    [JsonProperty(PropertyName = "CategoryName")]
    public string CategoryName { get; }
    [JsonProperty(PropertyName = "ItemName")]
    public string ItemName { get; }
    [JsonProperty(PropertyName = "Options")]
    public string Options { get; }
    [JsonProperty(PropertyName = "SortOrder")]
    public uint SortOrder { get; }
    [JsonProperty(PropertyName = "Price")]
    public decimal Price { get; }
    [JsonProperty(PropertyName = "GstValue")]
    public decimal GstValue { get; }
    [JsonProperty(PropertyName = "PriceExclGst")]
    public decimal PriceExclGst { get; }
    [JsonProperty(PropertyName = "MenuItemId")]
    public uint MenuItemId { get; }
    [JsonProperty(PropertyName = "CanteenStatus")]
    public uint CanteenStatus { get; }
}
