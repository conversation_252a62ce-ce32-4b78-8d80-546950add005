using System.Collections.Generic;
using Newtonsoft.Json;
using Schools.BLL.Classes;
using Schools.DAL.QueryResults;

namespace Schools.BLL.Assemblers.Events;

public class EventTemplateAssembler
{
    public EventTemplateDto Convert_EventTemplateResult_To_EventTemplateDto(EventTemplateResult result)
    {
        EventTemplateDto dto = new()
        {
            EventTemplateId = result.EventTemplateId,
            Title = result.Title,
            Description = result.Description,
            Items = JsonConvert.DeserializeObject<List<EventTemplateItemDto>>(result.AddItemDescription),
        };

        return dto;
    }

    public List<EventTypeDto> Convert_ListEventTemplateResult_To_ListEventTypeDto(IEnumerable<EventTemplateResult> result)
    {
        List<EventTypeDto> dtos = new();

        foreach (EventTemplateResult item in result)
        {
            var eventType = dtos.Find(d => d.EventTypeId == item.EventTypeId);

            if (eventType == null)
            {
                EventTypeDto newType = new()
                {
                    EventTypeId = item.EventTypeId,
                    Type = item.Type,
                    Templates =
                        [
                            this.Convert_EventTemplateResult_To_EventTemplateDto(item)
                        ]
                };
                dtos.Add(newType);
            }
            else
            {
                eventType.Templates.Add(this.Convert_EventTemplateResult_To_EventTemplateDto(item));
            }
        }

        return dtos;
    }
}
