﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Orders.Reports;

public class SaleReportDto
{
    [JsonProperty(PropertyName = "Categories")]
    public List<CategorySaleReportDto> Categories { get; set; }

    [JsonProperty(PropertyName = "TotalGST")]
    public Decimal TotalGST { get; set; }

    [JsonProperty(PropertyName = "TotalPriceExclGST")]
    public Decimal TotalPriceExclGST { get; set; }

    [JsonProperty(PropertyName = "TotalPriceIncGST")]
    public Decimal TotalPriceIncGST { get; set; }
}

public class CategorySaleReportDto
{
    [JsonProperty(PropertyName = "CatName")]
    public string CatName { get; set; }

    [JsonProperty(PropertyName = "Sales")]
    public List<SalesReportItemDto> Sales { get; set; }

    [JsonProperty(PropertyName = "TotalGST")]
    public Decimal TotalGST { get; set; }

    [JsonProperty(PropertyName = "TotalPriceExclGST")]
    public Decimal TotalPriceExclGST { get; set; }

    [JsonProperty(PropertyName = "TotalPriceIncGST")]
    public Decimal TotalPriceIncGST { get; set; }
}

public class SalesReportItemDto
{
    [JsonProperty(PropertyName = "Name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "Quantity")]
    public int? Quantity { get; set; }

    [JsonProperty(PropertyName = "SelectedOptions")]
    public string SelectedOptions { get; set; }

    [JsonProperty(PropertyName = "Price")]
    public Decimal Price { get; set; }

    [JsonProperty(PropertyName = "GstValue")]
    public Decimal GstValue { get; set; }

    [JsonProperty(PropertyName = "PriceExclGst")]
    public Decimal PriceExclGst { get; set; }

    [JsonProperty(PropertyName = "CanteenStatus")]
    public string CanteenStatus { get; set; }
}