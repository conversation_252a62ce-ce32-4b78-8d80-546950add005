﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cashless.APIs.Attributes;
using Cashless.APIs.Validators;
using Schools.BLL.Classes;
using Schools.BLL.Exceptions;
using Schools.BLL.Helpers;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Schools.DAL.Interfaces;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.Enums;
using Schools.DAL.DtosToMoveToBLL;
using static Schools.BLL.Classes.Label;

namespace Cashless.APIs.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PrintingController : Controller
    {
        private readonly ITelemetryService telemetryService;
        private readonly IDBHelper dbHelper;
        private readonly IOrderService orderService;
        private readonly ISchoolService schoolService;
        private readonly IPrintingService printingService;
        private readonly IPrintingValidator printingValidator;

        public static string BlockCustomMenuNamesSchoolIds { get; set; }
        private static HashSet<string> CustomMenuNameSchoolIds { get; set; }

        public PrintingController(ITelemetryService telemetryService, IDBHelper dbHelper, IOrderService orderService,
                                    ISchoolService schoolService, IPrintingService printingService, IPrintingValidator printingValidator)
        {
            this.telemetryService = telemetryService;
            this.dbHelper = dbHelper;
            this.orderService = orderService;
            this.schoolService = schoolService;
            this.printingService = printingService;
            this.printingValidator = printingValidator;

            CustomMenuNameSchoolIds = CustomMenuNameHelper.GetCustomMenuNameSchoolIds(CustomMenuNameSchoolIds, BlockCustomMenuNamesSchoolIds);
        }

        /// <summary>
        /// Save the orders to print
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("SetOrdersToPrint")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> SetOrdersToPrint([FromBody] OrderRequest request)
        {
            this.printingValidator.ValidateOrderRequest(request);
            await this.printingValidator.ValidateUserAccessToSchools(request.SchoolIds);

            // guid to insert on the orders/items to print
            var guid = Guid.NewGuid().ToString();

            // update print settings on the schools
            await this.schoolService.UpdatePrintingGuid(request.SchoolIds, guid, request.SortBy, request.SortDirection);

            // update orders
            await this.orderService.SetOrdersToPrint(request, guid);
            PrintGuid response = new()
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        /// <summary>
        /// Set items to print
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Route("SetItemsToPrint")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> SetItemsToPrint([FromBody] OrderRequest request)
        {
            this.printingValidator.ValidateOrderRequest(request);
            await this.printingValidator.ValidateUserAccessToSchools(request.SchoolIds);

            // guid to insert on the orders/items to print
            var guid = Guid.NewGuid().ToString();

            // update print settings on the schools
            await this.schoolService.UpdatePrintingGuid(request.SchoolIds, guid, request.SortBy, request.SortDirection);

            // update items
            await this.orderService.SetItemsToPrint(request, guid);
            PrintGuid response = new()
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        [Route("SetOrderToPrintByOrderId/{orderId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> SetOrderToPrintByOrderId(int orderId)
        {
            // get the order
            Order order = await this.orderService.GetOrderById(orderId);

            await this.printingValidator.ValidateAccessToOrder(order);

            // create guid
            var guid = Guid.NewGuid().ToString();

            // update school printing guid
            await this.schoolService.UpdatePrintingGuid(order.SchoolId + "", guid, "", "");

            await this.orderService.SetOrderToPrintByOrderId(orderId, guid);
            PrintGuid response = new()
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        [Route("SetItemToPrintByItemId/{orderId}/{itemId}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> SetItemToPrintByItemId(int orderId, int itemId)
        {
            // get the order
            Order order = await this.orderService.GetOrderById(orderId);

            await this.printingValidator.ValidateAccessToOrder(order);

            // get orders
            var guid = Guid.NewGuid().ToString();

            // update school printing guid
            await this.schoolService.UpdatePrintingGuid(order.SchoolId + "", guid, "", "");

            await this.orderService.SetItemToPrintByItemId(orderId, itemId, guid);

            PrintGuid response = new()
            {
                Guid = guid
            };

            return new OkObjectResult(response);
        }

        [Route("GetOrdersToPrintByGuid/{guid}")]
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        // To Do: Add user to printing app - then add validation to API call
        public async Task<IActionResult> GetOrdersToPrintByGuid(string guid)
        {
            if (String.IsNullOrEmpty(guid)) throw new ValidationException("guid", "Invalid request");

            // get schools by printing guid
            var school = await this.schoolService.GetSchoolByPrintingGuid(guid);

            if (school == null)
            {
                this.telemetryService.TrackTrace("printing_school_not_found",
                                    new Dictionary<string, string>() {
                                            {"guid", guid}
                                    });

                throw new Exception("No school found for the given guid");
            }

            // get orders
            List<OrderPrintingBLL> orders = null;

            if (school.LabelPrintChoice == SchoolPrintChoiceEnum.Item)
            {
                orders = await this.orderService.GetOrdersToPrintByItemGuid(guid, school.PrintSortBy, school.PrintSortDirection);
            }
            else
            {
                orders = await this.orderService.GetOrdersToPrintByGuidByClassName(guid, school.PrintSortBy, school.PrintSortDirection);
            }

            LabelFormatV2 res = new("");

            // assign label values
            if (orders != null && orders.Count > 0)
            {
                res = new LabelFormatV2(school.Size);

                // get positions settings
                List<LabelPosition> positions = await this.printingService.GetSchoolLabelPositions(Convert.ToInt32(school.SchoolId), school.UsePrintingApp);

                // default items per label
                var itemsperLabel = 3;

                // check if the school as a custom number of items per label
                var itemsPosition = positions.Find(p => p.Name == "Items");

                if (itemsPosition != null && itemsPosition.MaxItems > 0)
                {
                    itemsperLabel = Convert.ToInt32(itemsPosition.MaxItems);
                }

                // generate labels
                orders.ForEach(o =>
                {
                    try
                    {
                        // items number in the order
                        var totalItems = o.TotalItems;

                        if (school.LabelPrintChoice == SchoolPrintChoiceEnum.Item)
                        {
                            o.Items.ForEach(i =>
                            {
                                //if (i.PrintGuid == guid && i.OrderItemId == o.OrderItemId)
                                //{
                                var label = new LabelV2(o, i.MenuItemId, positions, CustomMenuNameSchoolIds)
                                {
                                    LabelNumber = i.ItemNumber.ToString() + "/" + totalItems.ToString()
                                };

                                string item = i.Quantity + " x " + i.Name + ItemOptionHelper.GetItemOptions(i);
                                label.Items.Add(item);

                                res.Labels.Add(label);
                                //}
                            });
                        }
                        else
                        {
                            // Print the complete order on 1 label
                            // The place for the items are limited on each labels, so 1 order may need more than 1 label
                            var totalCounter = o.Items.Count;
                            var nextItemToPrint = 0;
                            List<LabelV2> labels = new();

                            // until all the items are added to a label, continue to create labels
                            while (totalCounter > 0)
                            {
                                var itemLabelCounter = 0;

                                LabelV2 label = new(o, null, positions, CustomMenuNameSchoolIds);

                                // add the items
                                for (int i = nextItemToPrint; i < o.Items.Count; i++)
                                {
                                    // if we are under the item limit per label
                                    if (itemLabelCounter < itemsperLabel)
                                    {
                                        var loopItem = o.Items[i];
                                        string item = loopItem.Quantity + " x " + loopItem.Name + ItemOptionHelper.GetItemOptions(loopItem);
                                        label.Items.Add(item);

                                        totalCounter--;
                                        itemLabelCounter++;
                                    }
                                    else
                                    {
                                        // save the position to start printing the right item on the next label
                                        nextItemToPrint = i;
                                        break;
                                    }
                                }

                                labels.Add(label);
                            }

                            // Setup the labelNumber and add the labels to the result
                            for (int i = 0; i < labels.Count; i++)
                            {
                                labels[i].LabelNumber = i + 1 + "/" + labels.Count;
                                res.Labels.Add(labels[i]);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // we handle the error in the orders loop to avoid failing all the labels if only 1 of them fail
                        // log error in Azure
                        GenerateLabelException telemetryException = new(ex.Message, ex);

                        this.telemetryService.TrackException(telemetryException,
                                        new Dictionary<string, string>() {
                                                { "Order", JsonConvert.SerializeObject(o)},
                                                { "Positions", JsonConvert.SerializeObject(positions)},
                                        });
                    }
                });
            }

            return new OkObjectResult(res);
        }

        /// <summary>
        /// Get all orders for a given school.
        /// </summary>
        /// <returns></returns>
        [Route("SetOrdersHasPrinted")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        // To Do: Add user to printing app - then add validation to API call
        public async Task<IActionResult> SetOrdersHasPrinted([FromBody] SetPrintedRequest request)
        {
            if (String.IsNullOrEmpty(request.Orders)) throw new ValidationException("Orders", "Invalid request");

            //To Do: get user  canteensand pass canteen ids into stored proc

            await this.orderService.SetOrdersToPrinted(request.Orders.Split(',').Select(o => long.Parse(o)).ToList());

            return new OkResult();
        }

        [Route("SetOrdersHasPrintedWithRunNumber")]
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        [Obsolete]
        public Task<IActionResult> SetOrdersHasPrintedWithRunNumber([FromBody] SetPrintedRequest request)
        {
            throw new DeprecatedApiException();
        }

        [HttpPut("UpdateSchoolPrintOptions")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> UpdateSchoolPrintOptions([FromBody] SchoolPrintOptionsRequest request)
        {
            if (request.SchoolId == null) throw new ValidationException(fieldName: "schoolId", "Invalid request");
            if (request.LabelTypeId == null) throw new ValidationException("LabelTypeId", "Invalid request");

            await this.printingValidator.ValidateAccessToSchool((long)request.SchoolId);

            await this.dbHelper.ExecSprocByParams("sp_School_Update_labelType",
                                new Dictionary<string, string>() {
                                    { "schoolId", $"{request.SchoolId}" },
                                    { "typeId", $"{request.LabelTypeId}" },
                                    { "printChoice", $"{request.LabelPrintChoice}" }
                                });

            return new OkResult();
        }

        [HttpGet("LabelTypes")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [CheckUserRole(UserRole.Admin, UserRole.Merchant)]
        public async Task<IActionResult> LabelTypes()
        {
            var res = await this.dbHelper.ExecSprocByParams<List<LabelType>>("sp_LabelType_Get_All", new Dictionary<string, string>() { });

            return new OkObjectResult(res);
        }
    }
}
