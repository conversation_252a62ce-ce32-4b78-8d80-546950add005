# Guest User Payment Feature Implementation

## Overview

This document describes the implementation of guest user payment functionality in the POS module, allowing guest users to place orders using card payments instead of being blocked from the system.

## Architecture Overview

The implementation follows a clean separation of concerns with dedicated components for guest payments that do not interfere with existing authenticated user payment flows.

### Key Components

1. **API Layer**: New `GuestPaymentController` with dedicated endpoints
2. **Business Logic**: `GuestPaymentService` for payment processing and validation
3. **Frontend**: `GuestPaymentDialogComponent` for secure card input
4. **Integration**: Modified POS components to support guest payment flow

## API Implementation

### New Controller: `GuestPaymentController`

**Location**: `api/cashless-api/Cashless.API/Controllers/GuestPaymentController.cs`

**Endpoints**:
- `POST /api/GuestPayment/ProcessPayment` - Process guest card payment
- `POST /api/GuestPayment/ProcessPaymentAndCreateOrder` - Combined payment and order creation
- `POST /api/GuestPayment/ValidateCard` - Validate card details
- `GET /api/GuestPayment/PaymentStatus/{orderId}` - Check payment status
- `GET /api/GuestPayment/ValidatePermissions/{guestUserId}/{canteenId}` - Validate permissions

**Security Features**:
- Rate limiting (5 attempts/minute, 20 attempts/hour per user)
- IP-based rate limiting
- Input sanitization and validation
- Secure logging (sensitive data masked)
- Authentication required (`[CheckUserRole]` attributes)

### Business Logic: `GuestPaymentService`

**Location**: `api/cashless-api/Schools.BLL/Services/GuestPaymentService.cs`

**Key Features**:
- Card validation using Luhn algorithm
- Expiry date validation
- CVV validation
- Guest user permission validation
- Stripe integration for payment processing
- Comprehensive error handling
- Input sanitization

**Models**: `api/cashless-api/Schools.BLL/Classes/Payments/GuestPaymentRequest.cs`

## Frontend Implementation

### Guest Payment Dialog Component

**Location**: `web/cashless/src/app/pos/components/guest-payment-dialog/`

**Features**:
- Secure card input form with real-time validation
- Card type detection (Visa, Mastercard, American Express)
- Expiry date validation
- CVV validation with help tooltip
- Cardholder name validation
- Loading states and progress indicators
- Success/failure messaging
- Retry logic with exponential backoff
- Network error handling

**Security Measures**:
- Input sanitization
- No card data stored in local storage
- Secure form validation
- Rate limiting awareness
- Timeout handling (30-second timeout)

### Modified POS Components

**Updated Files**:
- `web/cashless/src/app/pos/components/pos-tab/pos-tab.component.ts`
- `web/cashless/src/app/pos/components/pos-tab/pos-tab.component.html`
- `web/cashless/src/app/pos/components/pos-place-order-dialog/pos-place-order-dialog.component.ts`

**Changes**:
- Removed guest user blocking
- Added "Pay with Card" button for guest users
- Updated mobile FAB to handle guest payments
- Modified place order dialog to redirect guests to card payment

## Integration Flow

### Guest Payment Process

1. **Guest User Selection**: Merchant selects a guest user in POS
2. **Add Items**: Items are added to cart normally
3. **Place Order**: Guest user clicks "Pay with Card" (not "Pay with Spriggy")
4. **Payment Dialog**: Guest payment dialog opens with card input form
5. **Card Validation**: Real-time validation of card details
6. **Payment Processing**: Secure payment processing via API
7. **Order Creation**: Order is created upon successful payment
8. **Confirmation**: Success message and order confirmation

### Error Handling

**Frontend Error Types**:
- `VALIDATION_ERROR`: Form validation issues
- `INVALID_CARD`: Card validation failures
- `PAYMENT_DECLINED`: Payment processor declined
- `INSUFFICIENT_FUNDS`: Card has insufficient funds
- `PAYMENT_TIMEOUT`: Processing timeout
- `NETWORK_ERROR`: Connection issues
- `RATE_LIMIT_EXCEEDED`: Too many attempts

**Backend Error Types**:
- `PERMISSION_DENIED`: Guest user lacks permissions
- `CANTEEN_NOT_FOUND`: Invalid canteen
- `PROCESSING_ERROR`: Unexpected errors
- `UNAUTHORIZED`: Authentication issues
- `INVALID_OPERATION`: Invalid state

## Security Implementation

### Rate Limiting

**Per User Limits**:
- Payment attempts: 5/minute, 20/hour
- Card validation: 10/minute, 60/hour

**Per IP Limits**:
- Payment attempts: 10/minute, 60/hour
- Card validation: 20/minute, 120/hour

### Data Protection

**Sensitive Data Handling**:
- Card numbers masked in logs (`**** **** **** 1234`)
- CVV never logged
- Cardholder names partially masked (`J***h`)
- No card data stored in browser storage
- Secure transmission over HTTPS

**Input Sanitization**:
- Card numbers: digits only
- CVV: digits only
- Names: letters, spaces, hyphens, apostrophes only
- Amounts: validated ranges

## Testing

### Unit Tests

**Frontend Tests**: `web/cashless/src/app/pos/components/guest-payment-dialog/guest-payment-dialog.component.spec.ts`
- Form validation tests
- Card validation tests
- Payment processing tests
- Error handling tests

**Backend Tests**: `api/cashless-api/Test/Cashless.API.Test/Services/GuestPaymentServiceTest.cs`
- Card validation tests
- Permission validation tests
- Payment processing tests
- Error scenario tests

### Test Coverage

- Card validation (Luhn algorithm)
- Expiry date validation
- CVV validation
- Permission checks
- Rate limiting
- Error handling
- Network failure scenarios

## Configuration

### Required Dependencies

**Frontend**:
- Angular Material components
- Reactive Forms
- HTTP Client
- Memory caching for rate limiting

**Backend**:
- Stripe SDK (existing)
- Memory caching
- Logging framework
- Telemetry service

### Environment Setup

1. **API Registration**: Service registered in `DependencyInjection.cs`
2. **Frontend Module**: Component added to POS module
3. **Routing**: No new routes required (dialog-based)
4. **Permissions**: Uses existing `[CheckUserRole]` attributes

## Deployment Considerations

### Database Changes
- No database schema changes required
- Uses existing user and order tables
- Guest users identified by `IsGuest` property

### API Versioning
- New controller doesn't affect existing endpoints
- Backward compatible implementation
- No breaking changes to existing flows

### Monitoring
- Comprehensive logging for payment attempts
- Telemetry tracking for success/failure rates
- Rate limiting metrics
- Security event logging

## Future Enhancements

### Potential Improvements
1. **Payment Methods**: Support for additional payment processors
2. **Saved Cards**: Temporary card tokenization for repeat guests
3. **Receipt Generation**: Email receipts for guest orders
4. **Analytics**: Enhanced reporting for guest payment patterns
5. **Mobile Optimization**: Native mobile app integration

### Scalability Considerations
1. **Rate Limiting**: Move to distributed cache (Redis) for multi-instance deployments
2. **Payment Processing**: Implement circuit breaker pattern
3. **Audit Trail**: Enhanced audit logging for compliance
4. **Performance**: Optimize payment processing pipeline

## Maintenance

### Regular Tasks
1. **Security Updates**: Keep payment processor SDKs updated
2. **Rate Limit Tuning**: Monitor and adjust rate limits based on usage
3. **Error Monitoring**: Review error logs for patterns
4. **Performance Monitoring**: Track payment processing times

### Troubleshooting
1. **Payment Failures**: Check logs for error codes and patterns
2. **Rate Limiting**: Review cache entries and adjust limits
3. **Network Issues**: Verify timeout settings and retry logic
4. **Validation Issues**: Check card validation logic and test data

## Code Review Checklist

### Security
- [ ] No sensitive data in logs
- [ ] Rate limiting implemented
- [ ] Input validation and sanitization
- [ ] Secure error messages
- [ ] Authentication and authorization

### Functionality
- [ ] Guest payment flow works end-to-end
- [ ] Error handling covers all scenarios
- [ ] Validation logic is comprehensive
- [ ] Integration with existing POS flow
- [ ] Mobile responsiveness

### Code Quality
- [ ] Unit tests with good coverage
- [ ] Clean separation of concerns
- [ ] Consistent error handling
- [ ] Proper logging and telemetry
- [ ] Documentation is complete

### Performance
- [ ] Efficient validation algorithms
- [ ] Appropriate timeout settings
- [ ] Memory cache usage optimized
- [ ] Network requests minimized
- [ ] UI responsiveness maintained
