{"format": 1, "restore": {"D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\OldProjects\\Schools.Orders\\Schools.Orders.csproj": {}}, "projects": {"D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\OldProjects\\Schools.Orders\\Schools.Orders.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\OldProjects\\Schools.Orders\\Schools.Orders.csproj", "projectName": "Schools.Orders", "projectPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\OldProjects\\Schools.Orders\\Schools.Orders.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\OldProjects\\Schools.Orders\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/healthteams/_packaging/healthteams/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\Schools.BLL.csproj": {"projectPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\Schools.BLL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Messaging.EventHubs": {"target": "Package", "version": "[5.9.1, )"}, "Azure.Messaging.ServiceBus": {"target": "Package", "version": "[7.14.0, )"}, "MassTransit": {"target": "Package", "version": "[8.0.15, )"}, "MassTransit.WebJobs.ServiceBus": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.Azure.AppConfiguration.AspNetCore": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Azure.Functions.Extensions": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[1.22.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": {"target": "Package", "version": "[1.3.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.ServiceBus": {"target": "Package", "version": "[5.7.0, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[1.17.2, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\Schools.BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\Schools.BLL.csproj", "projectName": "Schools.BLL", "projectPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\Schools.BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/healthteams/_packaging/healthteams/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\Schools.DAL.csproj": {"projectPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\Schools.DAL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.307.24, )"}, "Azure.Messaging.ServiceBus": {"target": "Package", "version": "[7.14.0, )"}, "Braintree": {"target": "Package", "version": "[5.12.0, )"}, "CsvHelper": {"target": "Package", "version": "[28.0.1, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[3.3.1, )"}, "FirebaseAdmin": {"target": "Package", "version": "[2.3.0, )"}, "LaunchDarkly.ServerSdk": {"target": "Package", "version": "[7.0.3, )"}, "MassTransit": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.ApplicationInsights": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.ApplicationInsights.AspNetCore": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Sendgrid": {"target": "Package", "version": "[9.29.3, )"}, "Stripe.net": {"target": "Package", "version": "[45.4.0, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}, "Xero.NetStandard.OAuth2": {"target": "Package", "version": "[3.25.0, )"}, "Xero.NetStandard.OAuth2Client": {"target": "Package", "version": "[1.6.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\Schools.DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\Schools.DAL.csproj", "projectName": "Schools.DAL", "projectPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\Schools.DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\projects\\spriggy\\git-spriggy-latest\\api\\cashless-api\\Schools.DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/healthteams/_packaging/healthteams/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.0.123, )"}, "Dapper.Contrib": {"target": "Package", "version": "[2.0.78, )"}, "Datadog.Trace.Bundle": {"target": "Package", "version": "[3.3.1, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.5, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[6.0.0, )"}, "StyleCop.Analyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.1.118, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}