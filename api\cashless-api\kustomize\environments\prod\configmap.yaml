apiVersion: v1
data:
  ASPNETCORE_ENVIRONMENT: prod
  KMS_MASTER_KEY_ARN: arn:aws:kms:ap-southeast-2:359697289456:key/02bb3f53-9e5d-4155-a34a-476024b40de1
  AWS_S3_ASSETS_BUCKET_NAME: prod-schools-assets
  AWS_ASSETS_READ_URL: https://assets.spriggyschools.com.au/
  # Any mobile app on a version less than the speicfied MOBILE_APP_WARNING_VERSION or MOBILE_APP_BLOCK_VERSION will see the associated popup
  MOBILE_APP_WARNING_VERSION: "7.3.3.318"
  MOBILE_APP_BLOCK_VERSION: "7.3.3.318"
  # Datadog variables https://docs.datadoghq.com/tracing/trace_collection/automatic_instrumentation/dd_libraries/dotnet-core/?tab=linux
  DD_LOGS_INJECTION: 'true'
  DD_PROFILING_ENABLED: 'true'
  DD_SERVICE: 'schools-api'
  DD_DBM_PROPAGATION_MODE: full
  IS_STRIPE_GATEWAY_ENABLED : 'true'
  STRIPE_VERSION: '2024-06-20'
  BALANCE_REFRESH_INTERVALS_IN_MILLIS: '1000,1000,2000,2000'
  FREQUENTLY_ORDERED_ITEMS_PAST_MONTHS: '12'
  FREQUENTLY_ORDERED_ITEMS_FREQUENCY: '3'
  FREQUENTLY_ORDERED_ITEMS_NUMBER_TO_SHOW: '5'
  FREQUENTLY_ORDERED_ITEMS_SCHOOLIDS: '-1'
kind: ConfigMap
metadata:
  name: schools-api-config
  namespace: default