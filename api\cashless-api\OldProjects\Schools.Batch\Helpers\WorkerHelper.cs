using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;
using Schools.DAL.DtosToMoveToBLL;

namespace Schools.Batch.Helpers;

public interface IWorkerHelper
{
    void Init(string settingGroup, string settingName);
    Task<long> GetLastUserId();
    Task SetLastUserId(long userId);
    int GetErrorCount();
    void AddError(User user, Exception ex);
    Task ShowErrors();
}

/// <summary>
/// Helper class containing common functions for all Workers
/// </summary>
public class WorkerHelper : IWorkerHelper
{
    /// <summary>
    /// User and error
    /// </summary>
    public record UserError(User user, string error);

    // Services injected
    private readonly ISettingsService _settingsService;
    private readonly ILogger<WorkerHelper> _logger;

    // Properties
    private string SettingGroup;
    private string SettingName;
    private IDictionary<string, UserError> Errors;

    public WorkerHelper(ISettingsService settingsService, ILogger<WorkerHelper> logger)
    {
        _settingsService = settingsService;
        _logger = logger;
    }

    /// <summary>
    /// Set the name of the setting to update and init other properties
    /// </summary>
    public void Init(string settingGroup, string settingName)
    {
        SettingGroup = settingGroup;
        SettingName = settingName;
        Errors = new Dictionary<string, UserError>();
    }

    /// <summary>
    /// Fetch the UserId of the last User updated
    /// </summary>
    public async Task<long> GetLastUserId()
    {
        var setting = await _settingsService.GetSetting(SettingGroup, SettingName);
        if (setting == null)
        {
            setting = await _settingsService.CreateSetting(SettingGroup, SettingName, 0);

            _logger.LogDebug("Created new Setting: {Setting} for last updated UserId", setting);
        }

        _logger.LogDebug("Fetched Setting: {Setting} for last updated UserId", setting);

        return setting.LongValue;
    }

    /// <summary>
    /// Set the ID of the last User reconciled
    /// </summary>
    public async Task SetLastUserId(long userId)
    {
        await _settingsService.UpdateSetting(SettingGroup, SettingName, userId);
    }

    /// <summary>
    /// Get the number of errors
    /// </summary>
    public int GetErrorCount()
    {
        return Errors.Count;
    }

    /// <summary>
    /// Add a new error for a User
    /// </summary>
    public void AddError(User user, Exception ex)
    {
        if (Errors.Keys.Contains(user.Email))
        {
            _logger.LogError(ex, "User: {User} is duplicated. Another user with same email already exists!", user);

            // Make a unique key!
            Errors.Add(user.Email + "-" + user.UserId, new UserError(user, ex.Message));
        }
        else
        {
            Errors.Add(user.Email, new UserError(user, ex.Message));
        }
    }

    /// <summary>
    /// Log the errors caught
    /// </summary>
    public async Task ShowErrors()
    {
        if (Errors.Any())
        {
            _logger.LogWarning("Errors encountered: {Count}", Errors.Count);

            foreach (var email in Errors.Keys)
            {
                _logger.LogWarning("  {Email}: {UserError}", email, Errors[email]);
            }
        }

        var lastUserId = await GetLastUserId();

        _logger.LogInformation("Last user updated: {UserId}", lastUserId);
    }
}