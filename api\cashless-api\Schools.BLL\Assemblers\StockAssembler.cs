﻿using System;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.Entities;

namespace Schools.BLL.Assemblers;

public static class StockAssembler
{
    public static Stock Convert_StockEntity_To_Stock(StockEntity entity)
    {
        Stock dto = new()
        {
            StockId = Convert.ToInt32(entity.StockId),
            DailyStock = entity.DailyStock,
            IsActive = entity.IsActive,
            StockName = entity.StockName,
            StockQuantity = entity.StockQuantity,
        };

        return dto;
    }
}
