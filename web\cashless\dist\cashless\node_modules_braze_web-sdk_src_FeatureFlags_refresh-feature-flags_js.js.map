{"version": 3, "file": "node_modules_braze_web-sdk_src_FeatureFlags_refresh-feature-flags_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAqD;AACT;AACrC,SAASE,sBAAsBA,CAACC,CAAC,EAAE;EACxC,IAAIA,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACC,EAAE,CAAC,IAAI,SAAS,IAAI,OAAOF,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACE,EAAE,CAAC,EAClE,OAAO,IAAIL,wDAAW,CACpBE,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACC,EAAE,CAAC,EACpBF,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACE,EAAE,CAAC,EACpBH,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACG,EAAE,CAAC,EACpBJ,CAAC,CAACF,wDAAW,CAACG,EAAE,CAACI,EAAE,CACrB,CAAC;EACHR,uEAAC,CAACS,CAAC,CAACC,IAAI,CAAE,sCAAqCC,IAAI,CAACC,SAAS,CAACT,CAAC,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;AAC9E;AACO,SAASU,iCAAiCA,CAACV,CAAC,EAAE;EACnD,IAAIA,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACT,EAAE,CAAC,IAAI,SAAS,IAAI,OAAOF,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACR,EAAE,CAAC,EAClE,OAAO,IAAIL,wDAAW,CACpBE,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACT,EAAE,CAAC,EACpBF,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACR,EAAE,CAAC,EACpBH,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACP,EAAE,CAAC,EACpBJ,CAAC,CAACF,wDAAW,CAACa,EAAE,CAACN,EAAE,CACrB,CAAC;EACHR,uEAAC,CAACS,CAAC,CAACC,IAAI,CACL,2CAA0CC,IAAI,CAACC,SAAS,CAACT,CAAC,EAAE,IAAI,EAAE,CAAC,CAAE,EACxE,CAAC;AACH;;;;;;;;;;;;;;;ACvBqD;AACtC,MAAMF,WAAW,CAAC;EAC/Bc,WAAWA,CAACC,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,EAAEc,CAAC,EAAE;IAC/B,IAAI,CAACC,EAAE,GAAGF,CAAC,EACT,IAAI,CAACG,OAAO,GAAGnB,CAAC,EAChB,IAAI,CAACoB,UAAU,GAAGjB,CAAC,EACnB,IAAI,CAACkB,cAAc,GAAGJ,CAAC,EACvB,IAAI,CAACC,EAAE,GAAGF,CAAC,EACX,IAAI,CAACG,OAAO,GAAGnB,CAAC,EAChB,IAAI,CAACoB,UAAU,GAAGjB,CAAC,EACnB,IAAI,CAACkB,cAAc,GAAGJ,CAAE;EAC7B;EACAK,iBAAiBA,CAACN,CAAC,EAAE;IACnB,MAAMhB,CAAC,GAAG,IAAI,CAACoB,UAAU,CAACJ,CAAC,CAAC;IAC5B,OAAO,IAAI,IAAIhB,CAAC,IACX,IAAI,CAACuB,EAAE,CAACP,CAAC,CAAC,EAAE,IAAI,IACjB,IAAI,CAACQ,EAAE,CAACxB,CAAC,CAAC,GACVA,CAAC,CAACyB,KAAK,IACN,IAAI,CAACC,EAAE,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;EAC/B;EACAC,iBAAiBA,CAACX,CAAC,EAAE;IACnB,MAAMhB,CAAC,GAAG,IAAI,CAACoB,UAAU,CAACJ,CAAC,CAAC;IAC5B,OAAO,IAAI,IAAIhB,CAAC,IACX,IAAI,CAACuB,EAAE,CAACP,CAAC,CAAC,EAAE,IAAI,IACjB,IAAI,CAACY,EAAE,CAAC5B,CAAC,CAAC,GACVA,CAAC,CAACyB,KAAK,IACN,IAAI,CAACC,EAAE,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;EAC/B;EACAG,kBAAkBA,CAACb,CAAC,EAAE;IACpB,MAAMhB,CAAC,GAAG,IAAI,CAACoB,UAAU,CAACJ,CAAC,CAAC;IAC5B,OAAO,IAAI,IAAIhB,CAAC,IACX,IAAI,CAACuB,EAAE,CAACP,CAAC,CAAC,EAAE,IAAI,IACjB,IAAI,CAACc,EAAE,CAAC9B,CAAC,CAAC,GACVA,CAAC,CAACyB,KAAK,IACN,IAAI,CAACC,EAAE,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC;EAChC;EACAK,EAAEA,CAAA,EAAG;IACH,MAAMf,CAAC,GAAG,CAAC,CAAC;IACZ,OACGA,CAAC,CAACf,WAAW,CAACa,EAAE,CAACT,EAAE,CAAC,GAAG,IAAI,CAACa,EAAE,EAC9BF,CAAC,CAACf,WAAW,CAACa,EAAE,CAACR,EAAE,CAAC,GAAG,IAAI,CAACa,OAAO,EACnCH,CAAC,CAACf,WAAW,CAACa,EAAE,CAACP,EAAE,CAAC,GAAG,IAAI,CAACa,UAAU,EACtCJ,CAAC,CAACf,WAAW,CAACa,EAAE,CAACN,EAAE,CAAC,GAAG,IAAI,CAACa,cAAc,EAC3CL,CAAC;EAEL;EACAU,EAAEA,CAACV,CAAC,EAAE;IACJhB,uEAAC,CAACS,CAAC,CAACC,IAAI,CAAE,2BAA0BM,CAAE,GAAE,CAAC;EAC3C;EACAO,EAAEA,CAACP,CAAC,EAAE;IACJhB,uEAAC,CAACS,CAAC,CAACC,IAAI,CAAE,GAAEM,CAAE,wCAAuC,CAAC;EACxD;EACAQ,EAAEA,CAACR,CAAC,EAAE;IACJ,OAAO,QAAQ,KAAKA,CAAC,CAACgB,IAAI,IAAI,QAAQ,IAAI,OAAOhB,CAAC,CAACS,KAAK;EAC1D;EACAG,EAAEA,CAACZ,CAAC,EAAE;IACJ,OAAO,QAAQ,KAAKA,CAAC,CAACgB,IAAI,IAAI,QAAQ,IAAI,OAAOhB,CAAC,CAACS,KAAK;EAC1D;EACAK,EAAEA,CAACd,CAAC,EAAE;IACJ,OAAO,SAAS,KAAKA,CAAC,CAACgB,IAAI,IAAI,SAAS,IAAI,OAAOhB,CAAC,CAACS,KAAK;EAC5D;AACF;AACCxB,WAAW,CAACa,EAAE,GAAG;EAAET,EAAE,EAAE,IAAI;EAAEC,EAAE,EAAE,GAAG;EAAEC,EAAE,EAAE,IAAI;EAAEC,EAAE,EAAE;AAAM,CAAC,EACzDP,WAAW,CAACG,EAAE,GAAG;EAAEC,EAAE,EAAE,IAAI;EAAEC,EAAE,EAAE,SAAS;EAAEC,EAAE,EAAE,YAAY;EAAEC,EAAE,EAAE;AAAM,CAAE;;;;;;;;;;;;;;;;AC/D/B;AACD;AAC7C,MAAM0B,EAAE,GAAG;EACTlB,CAAC,EAAE,CAAC,CAAC;EACLmB,QAAQ,EAAE,IAAI;EACdF,EAAE,EAAEA,CAAA,MACFC,EAAE,CAACE,CAAC,CAAC,CAAC,EACNF,EAAE,CAACC,QAAQ,KACPD,EAAE,CAACC,QAAQ,GAAG,IAAIF,kEAAE,CAAC9B,mEAAC,CAACkC,EAAE,CAAC,CAAC,EAAElC,mEAAC,CAACmC,EAAE,CAAC,CAAC,EAAEnC,mEAAC,CAACoC,CAAC,CAAC,CAAC,CAAC,EAAGpC,mEAAC,CAACqC,EAAE,CAACN,EAAE,CAACC,QAAQ,CAAC,CAAC,EACpED,EAAE,CAACC,QAAQ,CACZ;EACDC,CAAC,EAAEA,CAAA,KAAM;IACPF,EAAE,CAAClB,CAAC,KAAKb,mEAAC,CAACsC,CAAC,CAACP,EAAE,CAAC,EAAGA,EAAE,CAAClB,CAAC,GAAG,CAAC,CAAE,CAAC;EAChC,CAAC;EACD0B,OAAO,EAAEA,CAAA,KAAM;IACZR,EAAE,CAACC,QAAQ,GAAG,IAAI,EAAID,EAAE,CAAClB,CAAC,GAAG,CAAC,CAAE;EACnC;AACF,CAAC;AACD,iEAAekB,EAAE;;;;;;;;;;;;;;;;;;;;;;;AClBoC;AACV;AACG;AACqB;AACf;AACG;AACxB;AAII;AACa;AACjC,MAAMD,EAAE,SAASU,gEAAC,CAAC;EAChC5B,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE4B,CAAC,EAAE;IACnB,KAAK,CAAC,CAAC,EACJ,IAAI,CAACQ,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,CAAC,GAAGV,CAAC,EACV,IAAI,CAACW,EAAE,GAAG,EAAE,EACZ,IAAI,CAACC,EAAE,GAAG,CAAC,EACX,IAAI,CAACJ,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,EAAE,GAAGrC,CAAC,EACX,IAAI,CAACsC,CAAC,GAAGV,CAAC,EACV,IAAI,CAACa,EAAE,GAAG,IAAI,EACd,IAAI,CAACC,EAAE,GAAG,IAAIb,yEAAC,CAAC,CAAC,EACjB,IAAI,CAACc,EAAE,GAAG,EAAE,EACZ,IAAI,CAACC,EAAE,GAAG,IAAI,EACd,IAAI,CAACC,EAAE,GAAG,IAAI,EACf3D,mEAAC,CAAC4D,EAAE,CAAC,IAAI,CAACJ,EAAE,CAAC;EACjB;EACAK,EAAEA,CAAChD,CAAC,EAAE;IACJ,IAAI,CAAC,CAAC,IAAI,CAACqC,EAAE,IAAI,IAAI,CAACA,EAAE,CAACY,EAAE,CAAC,CAAC,KAAK,IAAI,IAAIjD,CAAC,IAAIA,CAAC,CAACkD,aAAa,EAAE;MAC9D,IAAI,CAACV,EAAE,GAAG,EAAE;MACZ,KAAK,MAAMvC,CAAC,IAAID,CAAC,CAACkD,aAAa,EAAE;QAC/B,MAAMlD,CAAC,GAAGkC,gFAAE,CAACjC,CAAC,CAAC;QACfD,CAAC,IAAI,IAAI,CAACwC,EAAE,CAACW,IAAI,CAACnD,CAAC,CAAC;MACtB;MACC,IAAI,CAACyC,EAAE,GAAG,IAAIW,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAG,IAAI,CAACC,EAAE,CAAC,CAAC,EAAE,IAAI,CAACX,EAAE,CAACY,EAAE,CAAC,IAAI,CAACf,EAAE,CAAC;IAClE;EACF;EACAgB,EAAEA,CAAA,EAAG;IACH,IAAIxD,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAACuC,CAAC,KAAKvC,CAAC,GAAG,IAAI,CAACuC,CAAC,CAACkB,CAAC,CAAC5B,sEAAC,CAAC6B,CAAC,CAACC,EAAE,CAAC,CAAC;IAChC,MAAM1D,CAAC,GAAG,CAAC,CAAC;IACZ,KAAK,MAAM4B,CAAC,IAAI7B,CAAC,EAAE;MACjB,MAAMb,CAAC,GAAGgD,2FAAE,CAACnC,CAAC,CAAC6B,CAAC,CAAC,CAAC;MAClB1C,CAAC,KAAKc,CAAC,CAACd,CAAC,CAACe,EAAE,CAAC,GAAGf,CAAC,CAAC;IACpB;IACA,OAAOc,CAAC;EACV;EACA2D,EAAEA,CAAA,EAAG;IACH,IAAI5D,CAAC;IACL,OAAO,CAAC,IAAI,MAAMA,CAAC,GAAG,IAAI,CAACuC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAKvC,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACyD,CAAC,CAAC5B,sEAAC,CAAC6B,CAAC,CAACG,EAAE,CAAC,KAAK,CAAC,CAAC;EAC7E;EACAC,EAAEA,CAAC9D,CAAC,EAAE;IACJ,IAAI,CAACuC,CAAC,IAAI,IAAI,CAACA,CAAC,CAACP,CAAC,CAACH,sEAAC,CAAC6B,CAAC,CAACG,EAAE,EAAE7D,CAAC,CAAC;EAC/B;EACA+D,EAAEA,CAAC/D,CAAC,EAAE;IACJ,OAAO,IAAI,CAAC2C,EAAE,CAACqB,EAAE,CAAChE,CAAC,CAAC;EACtB;EACAiE,mBAAmBA,CAACjE,CAAC,EAAEC,CAAC,EAAE4B,CAAC,GAAG,CAAC,CAAC,EAAE1C,CAAC,GAAG,CAAC,CAAC,EAAE;IACxC,IAAI,CAAC,IAAI,CAAC+E,EAAE,CAACrC,CAAC,CAAC,EACb,OACE,CAAC,IAAI,CAACa,EAAE,IACN,IAAI,CAACL,EAAE,KACN,IAAI,CAACK,EAAE,GAAG,IAAI,CAACL,EAAE,CAAC8B,EAAE,CAAC,MAAM;MAC1B,IAAI,CAACF,mBAAmB,CAACjE,CAAC,EAAEC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,EACL,MAAM,UAAU,IAAI,OAAOA,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;IAExC,IAAKd,CAAC,IAAI,IAAI,CAACiF,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC9B,EAAE,EAAG,OAAO,MAAM,UAAU,IAAI,OAAOrC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;IAC3E,MAAMjB,CAAC,GAAG,IAAI,CAACsD,EAAE,CAAC+B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1BC,CAAC,GAAG,IAAI,CAAChC,EAAE,CAACiC,EAAE,CAACvF,CAAC,EAAEoD,qEAAC,CAACoC,EAAE,CAACC,EAAE,CAAC;IAC5B,IAAIrD,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAACkB,EAAE,CAACoC,EAAE,CAAC1F,CAAC,EAAE,MAAM;MAClB,IAAI,CAACsD,EAAE,IACFF,qEAAC,CAACuC,EAAE,CAAC,IAAI,CAACpC,CAAC,EAAEH,qEAAC,CAACoC,EAAE,CAACC,EAAE,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACwB,OAAO,CAAC,CAAC,CAAC,EAC5C3C,oDAAC,CAAC4C,EAAE,CAAC;QACHC,GAAG,EAAG,GAAE,IAAI,CAACxC,EAAE,CAACyC,EAAE,CAAC,CAAE,qBAAoB;QACzCC,OAAO,EAAEV,CAAC;QACVW,IAAI,EAAEjG,CAAC;QACPkG,CAAC,EAAGrD,CAAC,IAAK;UACR,IAAI,CAAC,IAAI,CAACS,EAAE,CAAC6C,EAAE,CAACnG,CAAC,EAAE6C,CAAC,EAAEyC,CAAC,CAAC,EACtB,OAAQlD,CAAC,GAAG,CAAC,CAAC,EAAG,MAAM,UAAU,IAAI,OAAOnB,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;UACvD,IAAI,CAACqC,EAAE,CAAC8C,EAAE,CAAC,CAAC,EACV,IAAI,CAACpC,EAAE,CAACnB,CAAC,CAAC,EACTT,CAAC,GAAG,CAAC,CAAC,EACPgB,qEAAC,CAACiD,EAAE,CAAC,IAAI,CAAC9C,CAAC,EAAEH,qEAAC,CAACoC,EAAE,CAACC,EAAE,EAAE,CAAC,CAAC,EACxB,UAAU,IAAI,OAAOzE,CAAC,IAAIA,CAAC,CAAC,CAAC;QACjC,CAAC;QACDsF,KAAK,EAAGtF,CAAC,IAAK;UACZ,IAAI,CAACsC,EAAE,CAACiD,EAAE,CAACvF,CAAC,EAAE,0BAA0B,CAAC,EACtCoB,CAAC,GAAG,CAAC,CAAC,EACP,UAAU,IAAI,OAAOnB,CAAC,IAAIA,CAAC,CAAC,CAAC;QACjC,CAAC;QACDuF,EAAE,EAAEA,CAAA,KAAM;UACR,IAAIrG,CAAC,IAAIiC,CAAC,IAAI,CAAC,IAAI,CAAC0B,EAAE,EAAE;YACtBV,qEAAC,CAACqD,EAAE,CAAC,IAAI,CAAClD,CAAC,EAAEH,qEAAC,CAACoC,EAAE,CAACC,EAAE,CAAC;YACrB,IAAItF,CAAC,GAAG,IAAI,CAAC0D,EAAE;YACf,CAAC,IAAI,IAAI1D,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,IAAI,CAACyD,EAAE,MAAMzD,CAAC,GAAG,GAAG,GAAG,IAAI,CAACyD,EAAE,CAAC,EACrD,IAAI,CAAC8C,EAAE,CAACC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE5D,8DAAC,CAAC,GAAG,GAAG,IAAI,CAACY,EAAE,EAAE,CAAC,GAAGzD,CAAC,CAAC,CAAC,EAAEa,CAAC,EAAEC,CAAC,EAAE4B,CAAC,CAAC;UAC5D;QACF;MACF,CAAC,CAAC,IACF,UAAU,IAAI,OAAO5B,CAAC,IAAIA,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC;EACJ;EACAmE,EAAEA,CAAA,EAAG;IACH,IAAI,IAAI,IAAI,CAACtB,EAAE,KAAK+C,YAAY,CAAC,IAAI,CAAC/C,EAAE,CAAC,EAAG,IAAI,CAACA,EAAE,GAAG,IAAK,CAAC;EAC9D;EACA4C,EAAEA,CAAC1F,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC4C,EAAE,EAAE3C,CAAC,EAAE4B,CAAC,EAAE1C,CAAC,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI,CAACiF,EAAE,CAAC,CAAC,EACN,IAAI,CAACtB,EAAE,GAAGgD,MAAM,CAACC,UAAU,CAAC,MAAM;MACjC,IAAI,CAAC9B,mBAAmB,CAAChE,CAAC,EAAE4B,CAAC,EAAE1C,CAAC,CAAC;IACnC,CAAC,EAAEa,CAAC,CAAC,EACJ,IAAI,CAAC6C,EAAE,GAAG7C,CAAE;EACjB;EACAkE,EAAEA,CAAClE,CAAC,EAAE;IACJ,IAAI,CAAC,IAAI,CAACqC,EAAE,EAAE,OAAO,CAAC,CAAC;IACvB,IAAI,CAACrC,CAAC,EAAE;MACN,MAAMA,CAAC,GAAG,IAAI,CAACqC,EAAE,CAAC2D,EAAE,CAAC,CAAC;MACtB,IAAI,IAAI,IAAIhG,CAAC,EAAE,OAAO,CAAC,CAAC;MACxB,IAAIC,CAAC,GAAG,CAAC,CAAC;MACV,IAAI,CAACgG,KAAK,CAACjG,CAAC,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,KAAKA,CAAC,EAAE,OAAOhB,uEAAC,CAACS,CAAC,CAACC,IAAI,CAAC,oCAAoC,CAAC,EAAE,CAAC,CAAC;QACvEO,CAAC,GAAG,IAAImD,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAACZ,EAAE,IAAI,CAAC,IAAI,GAAG,GAAGzC,CAAC;MACtD;MACA,IAAI,CAACC,CAAC,EACJ,OACEjB,uEAAC,CAACS,CAAC,CAACC,IAAI,CAAE,+CAA8CM,CAAE,UAAS,CAAC,EACpE,CAAC,CAAC;IAER;IACA,OAAO,IAAI,CAACqC,EAAE,CAACY,EAAE,CAAC,CAAC;EACrB;EACAK,EAAEA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACf,CAAC,EAAE;IACb,MAAMvC,CAAC,GAAG,CAAC,CAAC;IACZ,KAAK,MAAMC,CAAC,IAAI,IAAI,CAACuC,EAAE,EAAE;MACvB,MAAMX,CAAC,GAAG5B,CAAC,CAACc,EAAE,CAAC,CAAC;MAChBf,CAAC,CAACC,CAAC,CAACC,EAAE,CAAC,GAAG2B,CAAC;IACb;IACA,IAAI,CAACU,CAAC,CAACP,CAAC,CAACH,sEAAC,CAAC6B,CAAC,CAACC,EAAE,EAAE3D,CAAC,CAAC,EAAE,IAAI,CAACuC,CAAC,CAACP,CAAC,CAACH,sEAAC,CAAC6B,CAAC,CAACwC,EAAE,EAAE,IAAI,CAACzD,EAAE,CAAC;EAChD;AACF;;;;;;;;;;;;;;;;;AChJ8C;AACO;AACrD,SAASpB,EAAEA,CAACrC,CAAC,EAAEgB,CAAC,EAAEmG,CAAC,GAAG,CAAC,CAAC,EAAE;EACxB,IAAIhH,mEAAC,CAACiH,EAAE,CAAC,CAAC,EAAE,OAAOlF,0EAAE,CAACD,EAAE,CAAC,CAAC,CAACgD,mBAAmB,CAACjF,CAAC,EAAEgB,CAAC,EAAEmG,CAAC,CAAC;AACzD;AACO,SAASlC,mBAAmBA,CAACjF,CAAC,EAAEG,CAAC,EAAE;EACxCkC,EAAE,CAACrC,CAAC,EAAEG,CAAC,CAAC;AACV;AACA,iEAAekC,EAAE", "sources": ["./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flag-factory.js", "./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flag.js", "./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flags-provider-factory.js", "./node_modules/@braze/web-sdk/src/FeatureFlags/feature-flags-provider.js", "./node_modules/@braze/web-sdk/src/FeatureFlags/refresh-feature-flags.js"], "sourcesContent": ["import r from \"../../shared-lib/braze-shared-lib.js\";\nimport FeatureFlag from \"./feature-flag.js\";\nexport function newFeatureFlagFromJson(e) {\n  if (e[FeatureFlag.Tt.ns] && \"boolean\" == typeof e[FeatureFlag.Tt.Fe])\n    return new FeatureFlag(\n      e[FeatureFlag.Tt.ns],\n      e[FeatureFlag.Tt.Fe],\n      e[FeatureFlag.Tt.we],\n      e[FeatureFlag.Tt.ze],\n    );\n  r.j.info(`Unable to create feature flag from ${JSON.stringify(e, null, 2)}`);\n}\nexport function newFeatureFlagFromSerializedValue(e) {\n  if (e[FeatureFlag.hs.ns] && \"boolean\" == typeof e[FeatureFlag.hs.Fe])\n    return new FeatureFlag(\n      e[FeatureFlag.hs.ns],\n      e[FeatureFlag.hs.Fe],\n      e[FeatureFlag.hs.we],\n      e[FeatureFlag.hs.ze],\n    );\n  r.j.info(\n    `Unable to deserialize feature flag from ${JSON.stringify(e, null, 2)}`,\n  );\n}\n", "import r from \"../../shared-lib/braze-shared-lib.js\";\nexport default class FeatureFlag {\n  constructor(t, r = !1, e = {}, s) {\n    (this.id = t),\n      (this.enabled = r),\n      (this.properties = e),\n      (this.trackingString = s),\n      (this.id = t),\n      (this.enabled = r),\n      (this.properties = e),\n      (this.trackingString = s);\n  }\n  getStringProperty(t) {\n    const r = this.properties[t];\n    return null == r\n      ? (this.Er(t), null)\n      : this.Ir(r)\n      ? r.value\n      : (this.Nr(\"string\"), null);\n  }\n  getNumberProperty(t) {\n    const r = this.properties[t];\n    return null == r\n      ? (this.Er(t), null)\n      : this.Tr(r)\n      ? r.value\n      : (this.Nr(\"number\"), null);\n  }\n  getBooleanProperty(t) {\n    const r = this.properties[t];\n    return null == r\n      ? (this.Er(t), null)\n      : this.Ar(r)\n      ? r.value\n      : (this.Nr(\"boolean\"), null);\n  }\n  ss() {\n    const t = {};\n    return (\n      (t[FeatureFlag.hs.ns] = this.id),\n      (t[FeatureFlag.hs.Fe] = this.enabled),\n      (t[FeatureFlag.hs.we] = this.properties),\n      (t[FeatureFlag.hs.ze] = this.trackingString),\n      t\n    );\n  }\n  Nr(t) {\n    r.j.info(`Property is not of type ${t}.`);\n  }\n  Er(t) {\n    r.j.info(`${t} not found in feature flag properties.`);\n  }\n  Ir(t) {\n    return \"string\" === t.type && \"string\" == typeof t.value;\n  }\n  Tr(t) {\n    return \"number\" === t.type && \"number\" == typeof t.value;\n  }\n  Ar(t) {\n    return \"boolean\" === t.type && \"boolean\" == typeof t.value;\n  }\n}\n(FeatureFlag.hs = { ns: \"id\", Fe: \"e\", we: \"pr\", ze: \"fts\" }),\n  (FeatureFlag.Tt = { ns: \"id\", Fe: \"enabled\", we: \"properties\", ze: \"fts\" });\n", "import e from \"../managers/braze-instance.js\";\nimport er from \"./feature-flags-provider.js\";\nconst ir = {\n  t: !1,\n  provider: null,\n  er: () => (\n    ir.o(),\n    ir.provider ||\n      ((ir.provider = new er(e.tr(), e.ar(), e.l())), e.dr(ir.provider)),\n    ir.provider\n  ),\n  o: () => {\n    ir.t || (e.g(ir), (ir.t = !0));\n  },\n  destroy: () => {\n    (ir.provider = null), (ir.t = !1);\n  },\n};\nexport default ir;\n", "import r from \"../../shared-lib/braze-shared-lib.js\";\nimport y from \"../common/base-provider.js\";\nimport e from \"../managers/braze-instance.js\";\nimport { STORAGE_KEYS as i } from \"../managers/storage-manager.js\";\nimport E from \"../managers/subscription-manager.js\";\nimport { randomInclusive as D } from \"../util/math.js\";\nimport C from \"../util/net.js\";\nimport {\n  newFeatureFlagFromJson as st,\n  newFeatureFlagFromSerializedValue as it,\n} from \"./feature-flag-factory.js\";\nimport T from \"../util/request-header-utils.js\";\nexport default class er extends y {\n  constructor(t, s, i) {\n    super(),\n      (this.wt = t),\n      (this.gt = s),\n      (this.u = i),\n      (this.pi = []),\n      (this.gi = 0),\n      (this.wt = t),\n      (this.gt = s),\n      (this.u = i),\n      (this.Fi = null),\n      (this.wi = new E()),\n      (this.yi = 10),\n      (this.ji = null),\n      (this.bi = null),\n      e.jt(this.wi);\n  }\n  Ts(t) {\n    if ((!this.wt || this.wt.vi()) && null != t && t.feature_flags) {\n      this.pi = [];\n      for (const s of t.feature_flags) {\n        const t = st(s);\n        t && this.pi.push(t);\n      }\n      (this.gi = new Date().getTime()), this.Ti(), this.wi.Et(this.pi);\n    }\n  }\n  Di() {\n    let t = {};\n    this.u && (t = this.u.v(i.k.Ri));\n    const s = {};\n    for (const i in t) {\n      const e = it(t[i]);\n      e && (s[e.id] = e);\n    }\n    return s;\n  }\n  Ni() {\n    var t;\n    return (null === (t = this.u) || void 0 === t ? void 0 : t.v(i.k.qi)) || {};\n  }\n  xi(t) {\n    this.u && this.u.D(i.k.qi, t);\n  }\n  ri(t) {\n    return this.wi.lt(t);\n  }\n  refreshFeatureFlags(t, s, i = !1, e = !0) {\n    if (!this.zi(i))\n      return (\n        !this.Fi &&\n          this.wt &&\n          (this.Fi = this.wt.Ci(() => {\n            this.refreshFeatureFlags(t, s);\n          })),\n        void (\"function\" == typeof s && s())\n      );\n    if ((e && this.Ii(), !this.gt)) return void (\"function\" == typeof s && s());\n    const r = this.gt.Bs({}, !0),\n      h = this.gt.Hs(r, T.Os.Si);\n    let o = !1;\n    this.gt.Qs(r, () => {\n      this.gt\n        ? (T.Ws(this.u, T.Os.Si, new Date().valueOf()),\n          C.Xs({\n            url: `${this.gt.Ys()}/feature_flags/sync`,\n            headers: h,\n            data: r,\n            O: (i) => {\n              if (!this.gt.Zs(r, i, h))\n                return (o = !0), void (\"function\" == typeof s && s());\n              this.gt.ti(),\n                this.Ts(i),\n                (o = !1),\n                T.si(this.u, T.Os.Si, 1),\n                \"function\" == typeof t && t();\n            },\n            error: (t) => {\n              this.gt.ii(t, \"retrieving feature flags\"),\n                (o = !0),\n                \"function\" == typeof s && s();\n            },\n            ei: () => {\n              if (e && o && !this.bi) {\n                T.hi(this.u, T.Os.Si);\n                let e = this.ji;\n                (null == e || e < 1e3 * this.yi) && (e = 1e3 * this.yi),\n                  this.$i(Math.min(3e5, D(1e3 * this.yi, 3 * e)), t, s, i);\n              }\n            },\n          }))\n        : \"function\" == typeof s && s();\n    });\n  }\n  Ii() {\n    null != this.bi && (clearTimeout(this.bi), (this.bi = null));\n  }\n  $i(t = 1e3 * this.yi, s, i, e = !1) {\n    this.Ii(),\n      (this.bi = window.setTimeout(() => {\n        this.refreshFeatureFlags(s, i, e);\n      }, t)),\n      (this.ji = t);\n  }\n  zi(t) {\n    if (!this.wt) return !1;\n    if (!t) {\n      const t = this.wt.Mi();\n      if (null == t) return !1;\n      let s = !1;\n      if (!isNaN(t)) {\n        if (-1 === t) return r.j.info(\"Feature flag refreshes not allowed\"), !1;\n        s = new Date().getTime() >= (this.gi || 0) + 1e3 * t;\n      }\n      if (!s)\n        return (\n          r.j.info(`Feature flag refreshes were rate limited to ${t} seconds`),\n          !1\n        );\n    }\n    return this.wt.vi();\n  }\n  Ti() {\n    if (!this.u) return;\n    const t = {};\n    for (const s of this.pi) {\n      const i = s.ss();\n      t[s.id] = i;\n    }\n    this.u.D(i.k.Ri, t), this.u.D(i.k.Ui, this.gi);\n  }\n}\n", "import e from \"../managers/braze-instance.js\";\nimport ir from \"./feature-flags-provider-factory.js\";\nfunction tr(r, t, a = !1) {\n  if (e.rr()) return ir.er().refreshFeatureFlags(r, t, a);\n}\nexport function refreshFeatureFlags(r, e) {\n  tr(r, e);\n}\nexport default tr;\n"], "names": ["r", "FeatureFlag", "newFeatureFlagFromJson", "e", "Tt", "ns", "Fe", "we", "ze", "j", "info", "JSON", "stringify", "newFeatureFlagFromSerializedValue", "hs", "constructor", "t", "s", "id", "enabled", "properties", "trackingString", "getStringProperty", "Er", "<PERSON>r", "value", "Nr", "getNumberProperty", "Tr", "getBooleanProperty", "Ar", "ss", "type", "er", "ir", "provider", "o", "tr", "ar", "l", "dr", "g", "destroy", "y", "STORAGE_KEYS", "i", "E", "randomInclusive", "D", "C", "st", "it", "T", "wt", "gt", "u", "pi", "gi", "Fi", "wi", "yi", "ji", "bi", "jt", "Ts", "vi", "feature_flags", "push", "Date", "getTime", "Ti", "Et", "Di", "v", "k", "Ri", "<PERSON>", "qi", "xi", "ri", "lt", "refreshFeatureFlags", "zi", "Ci", "Ii", "Bs", "h", "Hs", "<PERSON><PERSON>", "Si", "Qs", "Ws", "valueOf", "Xs", "url", "Ys", "headers", "data", "O", "Zs", "ti", "si", "error", "ii", "ei", "hi", "$i", "Math", "min", "clearTimeout", "window", "setTimeout", "<PERSON>", "isNaN", "Ui", "a", "rr"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3, 4]}