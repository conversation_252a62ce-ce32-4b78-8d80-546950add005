using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;
using Schools.BLL.Classes.FrequentlyOrdered;
using Schools.DAL.Entities;

namespace Schools.BLL.Classes;

public class AllMenusResult
{
    [JsonProperty(PropertyName = "MenuId")]
    public int? MenuId { get; set; }

    [JsonProperty(PropertyName = "SchoolId")]
    public int? SchoolId { get; set; }
}

public class MenuWithFrequentlyOrderedItems
{
    [JsonProperty(PropertyName = "Menus")]
    public List<Menu> Menus { get; set; }

    [JsonProperty(PropertyName = "FrequentItems")]
    public List<FrequentlyOrderedDto> FrequentItems { get; set; }
}
