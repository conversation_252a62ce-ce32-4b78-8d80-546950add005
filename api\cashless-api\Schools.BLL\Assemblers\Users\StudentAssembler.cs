﻿using System;
using System.Collections.Generic;
using System.Linq;
using Schools.BLL.Classes;
using Schools.BLL.Classes.Users;
using Schools.BLL.Constants;
using Schools.BLL.Helpers;
using Schools.DAL.DtosToMoveToBLL;
using Schools.DAL.QueryResults;

namespace Schools.BLL.Assemblers.Users;

public static class StudentAssembler
{
    public static StudentDto ConvertUserEntityToStudentDto(User request)
    {
        StudentDto dto = new()
        {
            UserId = request.UserId,
            FirstName = request.FirstName,
            Lastname = request.Lastname,
            ClassId = Convert.ToInt32(request.ClassId),
            NeedToUpdateClass = Convert.ToBoolean(request.NeedToUpdateClass),
            SchoolId = Convert.ToInt32(request.SchoolId),
            SchoolName = request.SchoolName,
            SchoolDeactivatedFilters = String.IsNullOrEmpty(request.SchoolDeactivatedFilters) ? String.Empty : request.SchoolDeactivatedFilters,
            SchoolWeeksPreOrder = request.SchoolWeeksPreOrder > 0 ? Convert.ToInt32(request.SchoolWeeksPreOrder) : SchoolConstants.DefaultWeeksPreOrder,
            SchoolOpeningDays = String.IsNullOrEmpty(request.SchoolOpeningDays) ? String.Empty : request.SchoolOpeningDays,
            SchoolIsMarketingFree = request.SchoolIsMarketingFree,
        };

        return dto;
    }

    public static List<StudentDto> ConvertListUserEntityToListStudentDto(IEnumerable<User> request)
    {
        List<StudentDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(ConvertUserEntityToStudentDto(e));
            }
        }

        return dto;
    }

    public static StudentDto Convert_StudentByParentResultV2_To_StudentDto(StudentByParentResultV2 request)
    {
        StudentDto dto = new()
        {
            UserId = request.UserId,
            FirstName = request.FirstName,
            Lastname = request.Lastname,
            ClassId = Convert.ToInt32(request.ClassId),

            NeedToUpdateClass = Convert.ToBoolean(request.NeedToUpdateClass),
            SchoolId = Convert.ToInt32(request.SchoolId),
            SchoolName = request.SchoolName,
            SchoolDeactivatedFilters = String.IsNullOrEmpty(request.SchoolDeactivatedFilters) ? String.Empty : request.SchoolDeactivatedFilters,
            SchoolWeeksPreOrder = request.SchoolWeeksPreOrder > 0 ? Convert.ToInt32(request.SchoolWeeksPreOrder) : SchoolConstants.DefaultWeeksPreOrder,
            SchoolOpeningDays = String.IsNullOrEmpty(request.SchoolOpeningDays) ? String.Empty : request.SchoolOpeningDays,
            SchoolIsMarketingFree = request.SchoolIsMarketingFree,
            SchoolTimeZoneOffSetHours = OrderHelper.GetUtcOffsetFromTimeZone(request.TimeZone)
        };

        return dto;
    }

    public static List<StudentDto> Convert_List_StudentByParentResultV2_To_List_StudentDto(IEnumerable<StudentByParentResultV2> request)
    {
        List<StudentDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(Convert_StudentByParentResultV2_To_StudentDto(e));
            }
        }

        return dto;
    }

    public static EditStudentDto Convert_StudentByParentResult_To_EditStudentDto(StudentByParentResult request)
    {
        EditStudentDto dto = new()
        {
            UserId = request.UserId,
            FirstName = request.FirstName,
            Lastname = request.Lastname,
            ClassId = Convert.ToInt32(request.ClassId),
            ClassName = request.ClassName,
            NeedToUpdateClass = Convert.ToBoolean(request.NeedToUpdateClass),
            SchoolId = Convert.ToInt32(request.SchoolId),
            SchoolName = request.SchoolName,
            SchoolDeactivatedFilters = String.IsNullOrEmpty(request.SchoolDeactivatedFilters) ? String.Empty : request.SchoolDeactivatedFilters,
            SchoolWeeksPreOrder = request.SchoolWeeksPreOrder > 0 ? Convert.ToInt32(request.SchoolWeeksPreOrder) : SchoolConstants.DefaultWeeksPreOrder,
            SchoolOpeningDays = String.IsNullOrEmpty(request.SchoolOpeningDays) ? String.Empty : request.SchoolOpeningDays,
            SchoolIsMarketingFree = request.SchoolIsMarketingFree,
            SchoolTimeZoneOffSetHours = OrderHelper.GetUtcOffsetFromTimeZone(request.TimeZone),
            Allergies = request.Allergies,
            AllowCanteenToOrder = request.AllowCanteenToOrder,
            DateOfBirth = request.DateOfBirth,
            Email = request.Email,
            FavouriteColour = request.FavouriteColour,
            IsActive = request.IsActive,
            Mobile = request.Mobile,
            ParentId = request.ParentId,
            Role = request.Role,
            SchoolCutOffTime = request.SchoolCutOffTime,
        };

        return dto;
    }

    public static List<EditStudentDto> Convert_List_StudentByParentResult_To_List_EditStudentDto(IEnumerable<StudentByParentResult> request)
    {
        List<EditStudentDto> dto = new();

        if (request != null && request.Any())
        {
            foreach (var e in request)
            {
                dto.Add(Convert_StudentByParentResult_To_EditStudentDto(e));
            }
        }

        return dto;
    }
}