using System.Collections.Generic;
using Schools.BLL.ThirdParty.General;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Orders;

public class FailedOrdersRequest
{
    public FailedOrdersRequest()
    {
        OrderIds = new List<int>();
    }

    [JsonProperty("OrderIds")]
    public IEnumerable<int> OrderIds { get; set; }

    [JsonProperty("PaymentResponse")]
    public CashlessMakePaymentResponse PaymentResponse { get; set; }
}