/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[2]!./node_modules/@angular/material/prebuilt-themes/indigo-pink.css ***!
  \********************************************************************************************************************************************************************************************************************************************************/
.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0)}.cdk-high-contrast-active .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-ripple-element{background-color:rgba(0,0,0,.1)}html{--mat-option-selected-state-label-text-color:#3f51b5;--mat-option-label-text-color:rgba(0, 0, 0, 0.87);--mat-option-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-option-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-option-selected-state-layer-color:rgba(0, 0, 0, 0.04)}.mat-accent{--mat-option-selected-state-label-text-color:#ff4081}.mat-warn{--mat-option-selected-state-label-text-color:#f44336}html{--mat-optgroup-label-text-color:rgba(0, 0, 0, 0.87)}.mat-pseudo-checkbox-full{color:rgba(0,0,0,.54)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{color:#b0b0b0}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#3f51b5}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#3f51b5}.mat-primary .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-primary .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#ff4081}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#ff4081}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#ff4081}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#ff4081}.mat-accent .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-accent .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#f44336}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#f44336}.mat-warn .mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full::after,.mat-warn .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full::after{color:#fafafa}.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-minimal::after,.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-minimal::after{color:#b0b0b0}.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-full,.mat-pseudo-checkbox-disabled.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-full{background:#b0b0b0}.mat-app-background{background-color:#fafafa;color:rgba(0,0,0,.87)}.mat-elevation-z0,.mat-mdc-elevation-specific.mat-elevation-z0{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z1,.mat-mdc-elevation-specific.mat-elevation-z1{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z2,.mat-mdc-elevation-specific.mat-elevation-z2{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z3,.mat-mdc-elevation-specific.mat-elevation-z3{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z4,.mat-mdc-elevation-specific.mat-elevation-z4{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z5,.mat-mdc-elevation-specific.mat-elevation-z5{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z6,.mat-mdc-elevation-specific.mat-elevation-z6{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z7,.mat-mdc-elevation-specific.mat-elevation-z7{box-shadow:0px 4px 5px -2px rgba(0, 0, 0, 0.2), 0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12)}.mat-elevation-z8,.mat-mdc-elevation-specific.mat-elevation-z8{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z9,.mat-mdc-elevation-specific.mat-elevation-z9{box-shadow:0px 5px 6px -3px rgba(0, 0, 0, 0.2), 0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z10,.mat-mdc-elevation-specific.mat-elevation-z10{box-shadow:0px 6px 6px -3px rgba(0, 0, 0, 0.2), 0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z11,.mat-mdc-elevation-specific.mat-elevation-z11{box-shadow:0px 6px 7px -4px rgba(0, 0, 0, 0.2), 0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z12,.mat-mdc-elevation-specific.mat-elevation-z12{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z13,.mat-mdc-elevation-specific.mat-elevation-z13{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z14,.mat-mdc-elevation-specific.mat-elevation-z14{box-shadow:0px 7px 9px -4px rgba(0, 0, 0, 0.2), 0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z15,.mat-mdc-elevation-specific.mat-elevation-z15{box-shadow:0px 8px 9px -5px rgba(0, 0, 0, 0.2), 0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z16,.mat-mdc-elevation-specific.mat-elevation-z16{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z17,.mat-mdc-elevation-specific.mat-elevation-z17{box-shadow:0px 8px 11px -5px rgba(0, 0, 0, 0.2), 0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z18,.mat-mdc-elevation-specific.mat-elevation-z18{box-shadow:0px 9px 11px -5px rgba(0, 0, 0, 0.2), 0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z19,.mat-mdc-elevation-specific.mat-elevation-z19{box-shadow:0px 9px 12px -6px rgba(0, 0, 0, 0.2), 0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z20,.mat-mdc-elevation-specific.mat-elevation-z20{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z21,.mat-mdc-elevation-specific.mat-elevation-z21{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z22,.mat-mdc-elevation-specific.mat-elevation-z22{box-shadow:0px 10px 14px -6px rgba(0, 0, 0, 0.2), 0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z23,.mat-mdc-elevation-specific.mat-elevation-z23{box-shadow:0px 11px 14px -7px rgba(0, 0, 0, 0.2), 0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12)}.mat-elevation-z24,.mat-mdc-elevation-specific.mat-elevation-z24{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-theme-loaded-marker{display:none}html{--mat-option-label-text-font:Roboto, sans-serif;--mat-option-label-text-line-height:24px;--mat-option-label-text-size:16px;--mat-option-label-text-tracking:0.03125em;--mat-option-label-text-weight:400}html{--mat-optgroup-label-text-font:Roboto, sans-serif;--mat-optgroup-label-text-line-height:24px;--mat-optgroup-label-text-size:16px;--mat-optgroup-label-text-tracking:0.03125em;--mat-optgroup-label-text-weight:400}.mat-mdc-card{--mdc-elevated-card-container-color:white;--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-color:white;--mdc-outlined-card-outline-color:rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-card-subtitle-text-color:rgba(0, 0, 0, 0.54)}.mat-mdc-card{--mat-card-title-text-font:Roboto, sans-serif;--mat-card-title-text-line-height:32px;--mat-card-title-text-size:20px;--mat-card-title-text-tracking:0.0125em;--mat-card-title-text-weight:500;--mat-card-subtitle-text-font:Roboto, sans-serif;--mat-card-subtitle-text-line-height:22px;--mat-card-subtitle-text-size:14px;--mat-card-subtitle-text-tracking:0.0071428571em;--mat-card-subtitle-text-weight:500}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-color:#3f51b5;--mdc-linear-progress-track-color:rgba(63, 81, 181, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:rgba(63, 81, 181, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(63, 81, 181, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(63, 81, 181, 0.25)'/%3E%3C/svg%3E")}}.mat-mdc-progress-bar .mdc-linear-progress__buffer-bar{background-color:rgba(63, 81, 181, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(63, 81, 181, 0.25))}.mat-mdc-progress-bar.mat-accent{--mdc-linear-progress-active-indicator-color:#ff4081;--mdc-linear-progress-track-color:rgba(255, 64, 129, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:rgba(255, 64, 129, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(255, 64, 129, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(255, 64, 129, 0.25)'/%3E%3C/svg%3E")}}.mat-mdc-progress-bar.mat-accent .mdc-linear-progress__buffer-bar{background-color:rgba(255, 64, 129, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(255, 64, 129, 0.25))}.mat-mdc-progress-bar.mat-warn{--mdc-linear-progress-active-indicator-color:#f44336;--mdc-linear-progress-track-color:rgba(244, 67, 54, 0.25)}@keyframes mdc-linear-progress-buffering{}.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:rgba(244, 67, 54, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(244, 67, 54, 0.25))}@media(forced-colors: active){.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E")}}.mat-mdc-progress-bar.mat-warn .mdc-linear-progress__buffer-bar{background-color:rgba(244, 67, 54, 0.25);background-color:var(--mdc-linear-progress-track-color, rgba(244, 67, 54, 0.25))}.mat-mdc-tooltip{--mdc-plain-tooltip-container-color:#616161;--mdc-plain-tooltip-supporting-text-color:#fff}.mat-mdc-tooltip{--mdc-plain-tooltip-supporting-text-font:Roboto, sans-serif;--mdc-plain-tooltip-supporting-text-size:12px;--mdc-plain-tooltip-supporting-text-weight:400;--mdc-plain-tooltip-supporting-text-tracking:0.0333333333em}html{--mdc-filled-text-field-caret-color:#3f51b5;--mdc-filled-text-field-focus-active-indicator-color:#3f51b5;--mdc-filled-text-field-focus-label-text-color:rgba(63, 81, 181, 0.87);--mdc-filled-text-field-container-color:whitesmoke;--mdc-filled-text-field-disabled-container-color:#fafafa;--mdc-filled-text-field-label-text-color:rgba(0, 0, 0, 0.6);--mdc-filled-text-field-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-text-field-input-text-color:rgba(0, 0, 0, 0.87);--mdc-filled-text-field-disabled-input-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-text-field-input-text-placeholder-color:rgba(0, 0, 0, 0.6);--mdc-filled-text-field-error-focus-label-text-color:#f44336;--mdc-filled-text-field-error-label-text-color:#f44336;--mdc-filled-text-field-error-caret-color:#f44336;--mdc-filled-text-field-active-indicator-color:rgba(0, 0, 0, 0.42);--mdc-filled-text-field-disabled-active-indicator-color:rgba(0, 0, 0, 0.06);--mdc-filled-text-field-hover-active-indicator-color:rgba(0, 0, 0, 0.87);--mdc-filled-text-field-error-active-indicator-color:#f44336;--mdc-filled-text-field-error-focus-active-indicator-color:#f44336;--mdc-filled-text-field-error-hover-active-indicator-color:#f44336;--mdc-outlined-text-field-caret-color:#3f51b5;--mdc-outlined-text-field-focus-outline-color:#3f51b5;--mdc-outlined-text-field-focus-label-text-color:rgba(63, 81, 181, 0.87);--mdc-outlined-text-field-label-text-color:rgba(0, 0, 0, 0.6);--mdc-outlined-text-field-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-input-text-color:rgba(0, 0, 0, 0.87);--mdc-outlined-text-field-disabled-input-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-input-text-placeholder-color:rgba(0, 0, 0, 0.6);--mdc-outlined-text-field-error-caret-color:#f44336;--mdc-outlined-text-field-error-focus-label-text-color:#f44336;--mdc-outlined-text-field-error-label-text-color:#f44336;--mdc-outlined-text-field-outline-color:rgba(0, 0, 0, 0.38);--mdc-outlined-text-field-disabled-outline-color:rgba(0, 0, 0, 0.06);--mdc-outlined-text-field-hover-outline-color:rgba(0, 0, 0, 0.87);--mdc-outlined-text-field-error-focus-outline-color:#f44336;--mdc-outlined-text-field-error-hover-outline-color:#f44336;--mdc-outlined-text-field-error-outline-color:#f44336;--mat-form-field-disabled-input-text-placeholder-color:rgba(0, 0, 0, 0.38)}.mat-mdc-form-field-error{color:var(--mdc-theme-error, #f44336)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{background-color:rgba(0,0,0,.87)}.mat-mdc-form-field:hover .mat-mdc-form-field-focus-overlay{opacity:.04}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:.12}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{color:rgba(0,0,0,.54)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-primary .mat-mdc-form-field-infix::after{color:rgba(63,81,181,.87)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-accent .mat-mdc-form-field-infix::after{color:rgba(255,64,129,.87)}.mat-mdc-form-field-type-mat-native-select.mat-focused.mat-warn .mat-mdc-form-field-infix::after{color:rgba(244,67,54,.87)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:rgba(0,0,0,.38)}.mat-mdc-form-field.mat-accent{--mdc-filled-text-field-caret-color:#ff4081;--mdc-filled-text-field-focus-active-indicator-color:#ff4081;--mdc-filled-text-field-focus-label-text-color:rgba(255, 64, 129, 0.87);--mdc-outlined-text-field-caret-color:#ff4081;--mdc-outlined-text-field-focus-outline-color:#ff4081;--mdc-outlined-text-field-focus-label-text-color:rgba(255, 64, 129, 0.87)}.mat-mdc-form-field.mat-warn{--mdc-filled-text-field-caret-color:#f44336;--mdc-filled-text-field-focus-active-indicator-color:#f44336;--mdc-filled-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87);--mdc-outlined-text-field-caret-color:#f44336;--mdc-outlined-text-field-focus-outline-color:#f44336;--mdc-outlined-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87)}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:56px}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:28px}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY( -34.75px) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{padding-top:16px;padding-bottom:16px}.mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-form-field-infix{padding-top:24px;padding-bottom:8px}.mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix{padding-top:16px;padding-bottom:16px}html{--mdc-filled-text-field-label-text-font:Roboto, sans-serif;--mdc-filled-text-field-label-text-size:16px;--mdc-filled-text-field-label-text-tracking:0.03125em;--mdc-filled-text-field-label-text-weight:400;--mdc-outlined-text-field-label-text-font:Roboto, sans-serif;--mdc-outlined-text-field-label-text-size:16px;--mdc-outlined-text-field-label-text-tracking:0.03125em;--mdc-outlined-text-field-label-text-weight:400;--mat-form-field-container-text-font:Roboto, sans-serif;--mat-form-field-container-text-line-height:24px;--mat-form-field-container-text-size:16px;--mat-form-field-container-text-tracking:0.03125em;--mat-form-field-container-text-weight:400;--mat-form-field-outlined-label-text-populated-size:16px;--mat-form-field-subscript-text-font:Roboto, sans-serif;--mat-form-field-subscript-text-line-height:20px;--mat-form-field-subscript-text-size:12px;--mat-form-field-subscript-text-tracking:0.0333333333em;--mat-form-field-subscript-text-weight:400}html{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(63, 81, 181, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-accent{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(255, 64, 129, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-warn{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, 0.87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, 0.38);--mat-select-placeholder-text-color:rgba(0, 0, 0, 0.6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, 0.54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, 0.38);--mat-select-focused-arrow-color:rgba(244, 67, 54, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html{--mat-select-trigger-text-font:Roboto, sans-serif;--mat-select-trigger-text-line-height:24px;--mat-select-trigger-text-size:16px;--mat-select-trigger-text-tracking:0.03125em;--mat-select-trigger-text-weight:400}html{--mat-autocomplete-background-color:white}.mat-mdc-dialog-container{--mdc-dialog-container-color:white;--mdc-dialog-subhead-color:rgba(0, 0, 0, 0.87);--mdc-dialog-supporting-text-color:rgba(0, 0, 0, 0.6)}.mat-mdc-dialog-container{--mdc-dialog-subhead-font:Roboto, sans-serif;--mdc-dialog-subhead-line-height:32px;--mdc-dialog-subhead-size:20px;--mdc-dialog-subhead-weight:500;--mdc-dialog-subhead-tracking:0.0125em;--mdc-dialog-supporting-text-font:Roboto, sans-serif;--mdc-dialog-supporting-text-line-height:24px;--mdc-dialog-supporting-text-size:16px;--mdc-dialog-supporting-text-weight:400;--mdc-dialog-supporting-text-tracking:0.03125em}.mat-mdc-standard-chip{--mdc-chip-disabled-label-text-color:#212121;--mdc-chip-elevated-container-color:#e0e0e0;--mdc-chip-elevated-disabled-container-color:#e0e0e0;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:#212121;--mdc-chip-with-icon-icon-color:#212121;--mdc-chip-with-icon-disabled-icon-color:#212121;--mdc-chip-with-icon-selected-icon-color:#212121;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:#212121;--mdc-chip-with-trailing-icon-trailing-icon-color:#212121}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#3f51b5;--mdc-chip-elevated-disabled-container-color:#3f51b5;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-accent,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-accent{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#ff4081;--mdc-chip-elevated-disabled-container-color:#ff4081;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-warn,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-warn{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#f44336;--mdc-chip-elevated-disabled-container-color:#f44336;--mdc-chip-focus-state-layer-color:black;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:white;--mdc-chip-with-icon-icon-color:white;--mdc-chip-with-icon-disabled-icon-color:white;--mdc-chip-with-icon-selected-icon-color:white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:white;--mdc-chip-with-trailing-icon-trailing-icon-color:white}.mat-mdc-chip.mat-mdc-standard-chip{--mdc-chip-container-height:32px}.mat-mdc-standard-chip{--mdc-chip-label-text-font:Roboto, sans-serif;--mdc-chip-label-text-line-height:20px;--mdc-chip-label-text-size:14px;--mdc-chip-label-text-tracking:0.0178571429em;--mdc-chip-label-text-weight:400}.mat-mdc-slide-toggle{--mdc-switch-selected-focus-state-layer-color:#3949ab;--mdc-switch-selected-handle-color:#3949ab;--mdc-switch-selected-hover-state-layer-color:#3949ab;--mdc-switch-selected-pressed-state-layer-color:#3949ab;--mdc-switch-selected-focus-handle-color:#1a237e;--mdc-switch-selected-hover-handle-color:#1a237e;--mdc-switch-selected-pressed-handle-color:#1a237e;--mdc-switch-selected-focus-track-color:#7986cb;--mdc-switch-selected-hover-track-color:#7986cb;--mdc-switch-selected-pressed-track-color:#7986cb;--mdc-switch-selected-track-color:#7986cb;--mdc-switch-disabled-selected-handle-color:#424242;--mdc-switch-disabled-selected-icon-color:#fff;--mdc-switch-disabled-selected-track-color:#424242;--mdc-switch-disabled-unselected-handle-color:#424242;--mdc-switch-disabled-unselected-icon-color:#fff;--mdc-switch-disabled-unselected-track-color:#424242;--mdc-switch-handle-surface-color:var(--mdc-theme-surface, #fff);--mdc-switch-handle-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-switch-handle-shadow-color:black;--mdc-switch-disabled-handle-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mdc-switch-selected-icon-color:#fff;--mdc-switch-unselected-focus-handle-color:#212121;--mdc-switch-unselected-focus-state-layer-color:#424242;--mdc-switch-unselected-focus-track-color:#e0e0e0;--mdc-switch-unselected-handle-color:#616161;--mdc-switch-unselected-hover-handle-color:#212121;--mdc-switch-unselected-hover-state-layer-color:#424242;--mdc-switch-unselected-hover-track-color:#e0e0e0;--mdc-switch-unselected-icon-color:#fff;--mdc-switch-unselected-pressed-handle-color:#212121;--mdc-switch-unselected-pressed-state-layer-color:#424242;--mdc-switch-unselected-pressed-track-color:#e0e0e0;--mdc-switch-unselected-track-color:#e0e0e0}.mat-mdc-slide-toggle .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:rgba(0,0,0,.38)}.mat-mdc-slide-toggle.mat-accent{--mdc-switch-selected-focus-state-layer-color:#d81b60;--mdc-switch-selected-handle-color:#d81b60;--mdc-switch-selected-hover-state-layer-color:#d81b60;--mdc-switch-selected-pressed-state-layer-color:#d81b60;--mdc-switch-selected-focus-handle-color:#880e4f;--mdc-switch-selected-hover-handle-color:#880e4f;--mdc-switch-selected-pressed-handle-color:#880e4f;--mdc-switch-selected-focus-track-color:#f06292;--mdc-switch-selected-hover-track-color:#f06292;--mdc-switch-selected-pressed-track-color:#f06292;--mdc-switch-selected-track-color:#f06292}.mat-mdc-slide-toggle.mat-warn{--mdc-switch-selected-focus-state-layer-color:#e53935;--mdc-switch-selected-handle-color:#e53935;--mdc-switch-selected-hover-state-layer-color:#e53935;--mdc-switch-selected-pressed-state-layer-color:#e53935;--mdc-switch-selected-focus-handle-color:#b71c1c;--mdc-switch-selected-hover-handle-color:#b71c1c;--mdc-switch-selected-pressed-handle-color:#b71c1c;--mdc-switch-selected-focus-track-color:#e57373;--mdc-switch-selected-hover-track-color:#e57373;--mdc-switch-selected-pressed-track-color:#e57373;--mdc-switch-selected-track-color:#e57373}.mat-mdc-slide-toggle{--mdc-switch-state-layer-size:48px}.mat-mdc-slide-toggle{--mat-slide-toggle-label-text-font:Roboto, sans-serif;--mat-slide-toggle-label-text-size:14px;--mat-slide-toggle-label-text-tracking:0.0178571429em;--mat-slide-toggle-label-text-line-height:20px;--mat-slide-toggle-label-text-weight:400}.mat-mdc-slide-toggle .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;-webkit-text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit)}.mat-mdc-radio-button .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-radio-button.mat-primary{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#3f51b5;--mdc-radio-selected-hover-icon-color:#3f51b5;--mdc-radio-selected-icon-color:#3f51b5;--mdc-radio-selected-pressed-icon-color:#3f51b5;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#3f51b5;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button.mat-accent{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#ff4081;--mdc-radio-selected-hover-icon-color:#ff4081;--mdc-radio-selected-icon-color:#ff4081;--mdc-radio-selected-pressed-icon-color:#ff4081;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#ff4081;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button.mat-warn{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#f44336;--mdc-radio-selected-hover-icon-color:#f44336;--mdc-radio-selected-icon-color:#f44336;--mdc-radio-selected-pressed-icon-color:#f44336;--mat-radio-ripple-color:#000;--mat-radio-checked-ripple-color:#f44336;--mat-radio-disabled-label-color:rgba(0, 0, 0, 0.38)}.mat-mdc-radio-button .mdc-radio{--mdc-radio-state-layer-size:40px}.mat-mdc-radio-button .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-body2-font-size, 14px);line-height:var(--mdc-typography-body2-line-height, 20px);font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);-webkit-text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:var(--mdc-typography-body2-text-transform, none)}.mat-mdc-slider{--mdc-slider-label-container-color:black;--mdc-slider-label-label-text-color:white;--mdc-slider-disabled-handle-color:#000;--mdc-slider-disabled-active-track-color:#000;--mdc-slider-disabled-inactive-track-color:#000;--mdc-slider-with-tick-marks-disabled-container-color:#000;--mat-mdc-slider-value-indicator-opacity: 0.6}.mat-mdc-slider.mat-primary{--mdc-slider-handle-color:#3f51b5;--mdc-slider-focus-handle-color:#3f51b5;--mdc-slider-hover-handle-color:#3f51b5;--mdc-slider-active-track-color:#3f51b5;--mdc-slider-inactive-track-color:#3f51b5;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#3f51b5;--mat-mdc-slider-ripple-color: #3f51b5;--mat-mdc-slider-hover-ripple-color: rgba(63, 81, 181, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(63, 81, 181, 0.2)}.mat-mdc-slider.mat-accent{--mdc-slider-handle-color:#ff4081;--mdc-slider-focus-handle-color:#ff4081;--mdc-slider-hover-handle-color:#ff4081;--mdc-slider-active-track-color:#ff4081;--mdc-slider-inactive-track-color:#ff4081;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#ff4081;--mat-mdc-slider-ripple-color: #ff4081;--mat-mdc-slider-hover-ripple-color: rgba(255, 64, 129, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(255, 64, 129, 0.2)}.mat-mdc-slider.mat-warn{--mdc-slider-handle-color:#f44336;--mdc-slider-focus-handle-color:#f44336;--mdc-slider-hover-handle-color:#f44336;--mdc-slider-active-track-color:#f44336;--mdc-slider-inactive-track-color:#f44336;--mdc-slider-with-tick-marks-active-container-color:#fff;--mdc-slider-with-tick-marks-inactive-container-color:#f44336;--mat-mdc-slider-ripple-color: #f44336;--mat-mdc-slider-hover-ripple-color: rgba(244, 67, 54, 0.05);--mat-mdc-slider-focus-ripple-color: rgba(244, 67, 54, 0.2)}.mat-mdc-slider{--mdc-slider-label-label-text-font:Roboto, sans-serif;--mdc-slider-label-label-text-size:14px;--mdc-slider-label-label-text-line-height:22px;--mdc-slider-label-label-text-tracking:0.0071428571em;--mdc-slider-label-label-text-weight:500}html{--mat-menu-item-label-text-color:rgba(0, 0, 0, 0.87);--mat-menu-item-icon-color:rgba(0, 0, 0, 0.87);--mat-menu-item-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-menu-item-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-menu-container-color:white}html{--mat-menu-item-label-text-font:Roboto, sans-serif;--mat-menu-item-label-text-size:16px;--mat-menu-item-label-text-tracking:0.03125em;--mat-menu-item-label-text-line-height:24px;--mat-menu-item-label-text-weight:400}.mat-mdc-list-base{--mdc-list-list-item-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-supporting-text-color:rgba(0, 0, 0, 0.54);--mdc-list-list-item-leading-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-trailing-supporting-text-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-selected-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-disabled-label-text-color:black;--mdc-list-list-item-disabled-leading-icon-color:black;--mdc-list-list-item-disabled-trailing-icon-color:black;--mdc-list-list-item-hover-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-hover-leading-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-hover-trailing-icon-color:rgba(0, 0, 0, 0.38);--mdc-list-list-item-focus-label-text-color:rgba(0, 0, 0, 0.87);--mdc-list-list-item-hover-state-layer-color:black;--mdc-list-list-item-hover-state-layer-opacity:0.04;--mdc-list-list-item-focus-state-layer-color:black;--mdc-list-list-item-focus-state-layer-opacity:0.12}.mdc-list-item__start,.mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#3f51b5;--mdc-radio-selected-hover-icon-color:#3f51b5;--mdc-radio-selected-icon-color:#3f51b5;--mdc-radio-selected-pressed-icon-color:#3f51b5}.mat-accent .mdc-list-item__start,.mat-accent .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#ff4081;--mdc-radio-selected-hover-icon-color:#ff4081;--mdc-radio-selected-icon-color:#ff4081;--mdc-radio-selected-pressed-icon-color:#ff4081}.mat-warn .mdc-list-item__start,.mat-warn .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color:#000;--mdc-radio-disabled-unselected-icon-color:#000;--mdc-radio-unselected-hover-icon-color:#212121;--mdc-radio-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-radio-selected-focus-icon-color:#f44336;--mdc-radio-selected-hover-icon-color:#f44336;--mdc-radio-selected-icon-color:#f44336;--mdc-radio-selected-pressed-icon-color:#f44336}.mat-mdc-list-option{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#3f51b5;--mdc-checkbox-selected-hover-icon-color:#3f51b5;--mdc-checkbox-selected-icon-color:#3f51b5;--mdc-checkbox-selected-pressed-icon-color:#3f51b5;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#3f51b5;--mdc-checkbox-selected-hover-state-layer-color:#3f51b5;--mdc-checkbox-selected-pressed-state-layer-color:#3f51b5;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-option.mat-accent{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#ff4081;--mdc-checkbox-selected-hover-icon-color:#ff4081;--mdc-checkbox-selected-icon-color:#ff4081;--mdc-checkbox-selected-pressed-icon-color:#ff4081;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#ff4081;--mdc-checkbox-selected-hover-state-layer-color:#ff4081;--mdc-checkbox-selected-pressed-state-layer-color:#ff4081;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-option.mat-warn{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#f44336;--mdc-checkbox-selected-hover-icon-color:#f44336;--mdc-checkbox-selected-icon-color:#f44336;--mdc-checkbox-selected-pressed-icon-color:#f44336;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#f44336;--mdc-checkbox-selected-hover-state-layer-color:#f44336;--mdc-checkbox-selected-pressed-state-layer-color:#f44336;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__primary-text{color:#3f51b5}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected.mdc-list-item--with-leading-icon .mdc-list-item__start,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated.mdc-list-item--with-leading-icon .mdc-list-item__start{color:#3f51b5}.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__content,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mat-mdc-list-base{--mdc-list-list-item-one-line-container-height:48px;--mdc-list-list-item-two-line-container-height:64px;--mdc-list-list-item-three-line-container-height:88px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line{height:56px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines{height:72px}.mat-mdc-list-base{--mdc-list-list-item-label-text-font:Roboto, sans-serif;--mdc-list-list-item-label-text-line-height:24px;--mdc-list-list-item-label-text-size:16px;--mdc-list-list-item-label-text-tracking:0.03125em;--mdc-list-list-item-label-text-weight:400;--mdc-list-list-item-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-supporting-text-line-height:20px;--mdc-list-list-item-supporting-text-size:14px;--mdc-list-list-item-supporting-text-tracking:0.0178571429em;--mdc-list-list-item-supporting-text-weight:400;--mdc-list-list-item-trailing-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-trailing-supporting-text-line-height:20px;--mdc-list-list-item-trailing-supporting-text-size:12px;--mdc-list-list-item-trailing-supporting-text-tracking:0.0333333333em;--mdc-list-list-item-trailing-supporting-text-weight:400}.mdc-list-group__subheader{font-size:16px;font-weight:400;line-height:28px;font-family:Roboto, sans-serif;letter-spacing:.009375em}html{--mat-paginator-container-text-color:rgba(0, 0, 0, 0.87);--mat-paginator-container-background-color:white;--mat-paginator-enabled-icon-color:rgba(0, 0, 0, 0.54);--mat-paginator-disabled-icon-color:rgba(0, 0, 0, 0.12)}html{--mat-paginator-container-size:56px}.mat-mdc-paginator .mat-mdc-form-field-infix{min-height:40px}.mat-mdc-paginator .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:20px}.mat-mdc-paginator .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY( -26.75px) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-paginator .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix{padding-top:8px;padding-bottom:8px}.mat-mdc-paginator .mat-mdc-text-field-wrapper:not(.mdc-text-field--outlined) .mat-mdc-floating-label{display:none}html{--mat-paginator-container-text-font:Roboto, sans-serif;--mat-paginator-container-text-line-height:20px;--mat-paginator-container-text-size:12px;--mat-paginator-container-text-tracking:0.0333333333em;--mat-paginator-container-text-weight:400;--mat-paginator-select-trigger-text-size:12px}.mat-mdc-tab-group,.mat-mdc-tab-nav-bar{--mdc-tab-indicator-active-indicator-color:#3f51b5;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#3f51b5;--mat-tab-header-active-ripple-color:#3f51b5;--mat-tab-header-inactive-ripple-color:#3f51b5;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#3f51b5;--mat-tab-header-active-hover-label-text-color:#3f51b5;--mat-tab-header-active-focus-indicator-color:#3f51b5;--mat-tab-header-active-hover-indicator-color:#3f51b5}.mat-mdc-tab-group.mat-accent,.mat-mdc-tab-nav-bar.mat-accent{--mdc-tab-indicator-active-indicator-color:#ff4081;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#ff4081;--mat-tab-header-active-ripple-color:#ff4081;--mat-tab-header-inactive-ripple-color:#ff4081;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#ff4081;--mat-tab-header-active-hover-label-text-color:#ff4081;--mat-tab-header-active-focus-indicator-color:#ff4081;--mat-tab-header-active-hover-indicator-color:#ff4081}.mat-mdc-tab-group.mat-warn,.mat-mdc-tab-nav-bar.mat-warn{--mdc-tab-indicator-active-indicator-color:#f44336;--mat-tab-header-disabled-ripple-color:rgba(0, 0, 0, 0.38);--mat-tab-header-pagination-icon-color:#000;--mat-tab-header-inactive-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-label-text-color:#f44336;--mat-tab-header-active-ripple-color:#f44336;--mat-tab-header-inactive-ripple-color:#f44336;--mat-tab-header-inactive-focus-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-inactive-hover-label-text-color:rgba(0, 0, 0, 0.6);--mat-tab-header-active-focus-label-text-color:#f44336;--mat-tab-header-active-hover-label-text-color:#f44336;--mat-tab-header-active-focus-indicator-color:#f44336;--mat-tab-header-active-hover-indicator-color:#f44336}.mat-mdc-tab-group.mat-background-primary,.mat-mdc-tab-nav-bar.mat-background-primary{--mat-tab-header-with-background-background-color:#3f51b5;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-group.mat-background-accent,.mat-mdc-tab-nav-bar.mat-background-accent{--mat-tab-header-with-background-background-color:#ff4081;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-group.mat-background-warn,.mat-mdc-tab-nav-bar.mat-background-warn{--mat-tab-header-with-background-background-color:#f44336;--mat-tab-header-with-background-foreground-color:white}.mat-mdc-tab-header{--mdc-secondary-navigation-tab-container-height:48px}.mat-mdc-tab-header{--mat-tab-header-label-text-font:Roboto, sans-serif;--mat-tab-header-label-text-size:14px;--mat-tab-header-label-text-tracking:0.0892857143em;--mat-tab-header-label-text-line-height:36px;--mat-tab-header-label-text-weight:500}html{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#ff4081;--mdc-checkbox-selected-hover-icon-color:#ff4081;--mdc-checkbox-selected-icon-color:#ff4081;--mdc-checkbox-selected-pressed-icon-color:#ff4081;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#ff4081;--mdc-checkbox-selected-hover-state-layer-color:#ff4081;--mdc-checkbox-selected-pressed-state-layer-color:#ff4081;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox.mat-primary{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#3f51b5;--mdc-checkbox-selected-hover-icon-color:#3f51b5;--mdc-checkbox-selected-icon-color:#3f51b5;--mdc-checkbox-selected-pressed-icon-color:#3f51b5;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#3f51b5;--mdc-checkbox-selected-hover-state-layer-color:#3f51b5;--mdc-checkbox-selected-pressed-state-layer-color:#3f51b5;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox.mat-warn{--mdc-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, 0.38);--mdc-checkbox-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-icon-color:#f44336;--mdc-checkbox-selected-hover-icon-color:#f44336;--mdc-checkbox-selected-icon-color:#f44336;--mdc-checkbox-selected-pressed-icon-color:#f44336;--mdc-checkbox-unselected-focus-icon-color:#212121;--mdc-checkbox-unselected-hover-icon-color:#212121;--mdc-checkbox-unselected-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-unselected-pressed-icon-color:rgba(0, 0, 0, 0.54);--mdc-checkbox-selected-focus-state-layer-color:#f44336;--mdc-checkbox-selected-hover-state-layer-color:#f44336;--mdc-checkbox-selected-pressed-state-layer-color:#f44336;--mdc-checkbox-unselected-focus-state-layer-color:black;--mdc-checkbox-unselected-hover-state-layer-color:black;--mdc-checkbox-unselected-pressed-state-layer-color:black}.mat-mdc-checkbox .mdc-form-field{color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{color:rgba(0,0,0,.38)}html{--mdc-checkbox-state-layer-size:40px}.mat-mdc-checkbox .mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-body2-font-size, 14px);line-height:var(--mdc-typography-body2-line-height, 20px);font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);-webkit-text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:var(--mdc-typography-body2-text-transform, none)}.mat-mdc-button.mat-unthemed{--mdc-text-button-label-text-color:#000}.mat-mdc-button.mat-primary{--mdc-text-button-label-text-color:#3f51b5}.mat-mdc-button.mat-accent{--mdc-text-button-label-text-color:#ff4081}.mat-mdc-button.mat-warn{--mdc-text-button-label-text-color:#f44336}.mat-mdc-button[disabled][disabled]{--mdc-text-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-text-button-label-text-color:rgba(0, 0, 0, 0.38)}.mat-mdc-unelevated-button.mat-unthemed{--mdc-filled-button-container-color:#fff;--mdc-filled-button-label-text-color:#000}.mat-mdc-unelevated-button.mat-primary{--mdc-filled-button-container-color:#3f51b5;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button.mat-accent{--mdc-filled-button-container-color:#ff4081;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button.mat-warn{--mdc-filled-button-container-color:#f44336;--mdc-filled-button-label-text-color:#fff}.mat-mdc-unelevated-button[disabled][disabled]{--mdc-filled-button-disabled-container-color:rgba(0, 0, 0, 0.12);--mdc-filled-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-filled-button-container-color:rgba(0, 0, 0, 0.12);--mdc-filled-button-label-text-color:rgba(0, 0, 0, 0.38)}.mat-mdc-raised-button.mat-unthemed{--mdc-protected-button-container-color:#fff;--mdc-protected-button-label-text-color:#000}.mat-mdc-raised-button.mat-primary{--mdc-protected-button-container-color:#3f51b5;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button.mat-accent{--mdc-protected-button-container-color:#ff4081;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button.mat-warn{--mdc-protected-button-container-color:#f44336;--mdc-protected-button-label-text-color:#fff}.mat-mdc-raised-button[disabled][disabled]{--mdc-protected-button-disabled-container-color:rgba(0, 0, 0, 0.12);--mdc-protected-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-protected-button-container-color:rgba(0, 0, 0, 0.12);--mdc-protected-button-label-text-color:rgba(0, 0, 0, 0.38);--mdc-protected-button-container-elevation:0}.mat-mdc-outlined-button{--mdc-outlined-button-outline-color:rgba(0, 0, 0, 0.12)}.mat-mdc-outlined-button.mat-unthemed{--mdc-outlined-button-label-text-color:#000}.mat-mdc-outlined-button.mat-primary{--mdc-outlined-button-label-text-color:#3f51b5}.mat-mdc-outlined-button.mat-accent{--mdc-outlined-button-label-text-color:#ff4081}.mat-mdc-outlined-button.mat-warn{--mdc-outlined-button-label-text-color:#f44336}.mat-mdc-outlined-button[disabled][disabled]{--mdc-outlined-button-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-button-disabled-label-text-color:rgba(0, 0, 0, 0.38);--mdc-outlined-button-outline-color:rgba(0, 0, 0, 0.12);--mdc-outlined-button-disabled-outline-color:rgba(0, 0, 0, 0.12)}.mat-mdc-button,.mat-mdc-outlined-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-button.mat-primary,.mat-mdc-outlined-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #3f51b5;--mat-mdc-button-ripple-color: rgba(63, 81, 181, 0.1)}.mat-mdc-button.mat-accent,.mat-mdc-outlined-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #ff4081;--mat-mdc-button-ripple-color: rgba(255, 64, 129, 0.1)}.mat-mdc-button.mat-warn,.mat-mdc-outlined-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #f44336;--mat-mdc-button-ripple-color: rgba(244, 67, 54, 0.1)}.mat-mdc-raised-button,.mat-mdc-unelevated-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-raised-button.mat-primary,.mat-mdc-unelevated-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-raised-button.mat-accent,.mat-mdc-unelevated-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-raised-button.mat-warn,.mat-mdc-unelevated-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-button.mat-mdc-button-base,.mat-mdc-raised-button.mat-mdc-button-base,.mat-mdc-unelevated-button.mat-mdc-button-base,.mat-mdc-outlined-button.mat-mdc-button-base{height:36px}.mdc-button{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-button-font-size, 14px);line-height:var(--mdc-typography-button-line-height, 36px);font-weight:var(--mdc-typography-button-font-weight, 500);letter-spacing:var(--mdc-typography-button-letter-spacing, 0.0892857143em);-webkit-text-decoration:var(--mdc-typography-button-text-decoration, none);text-decoration:var(--mdc-typography-button-text-decoration, none);text-transform:var(--mdc-typography-button-text-transform, none)}.mat-mdc-icon-button{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1);--mdc-icon-button-icon-color:inherit;--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-icon-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-icon-button:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-icon-button.mat-primary{--mat-mdc-button-persistent-ripple-color: #6200ee;--mat-mdc-button-ripple-color: rgba(98, 0, 238, 0.1)}.mat-mdc-icon-button.mat-accent{--mat-mdc-button-persistent-ripple-color: #018786;--mat-mdc-button-ripple-color: rgba(1, 135, 134, 0.1)}.mat-mdc-icon-button.mat-warn{--mat-mdc-button-persistent-ripple-color: #b00020;--mat-mdc-button-ripple-color: rgba(176, 0, 32, 0.1)}.mat-mdc-icon-button.mat-primary{--mdc-icon-button-icon-color:#3f51b5;--mat-mdc-button-persistent-ripple-color: #3f51b5;--mat-mdc-button-ripple-color: rgba(63, 81, 181, 0.1)}.mat-mdc-icon-button.mat-accent{--mdc-icon-button-icon-color:#ff4081;--mat-mdc-button-persistent-ripple-color: #ff4081;--mat-mdc-button-ripple-color: rgba(255, 64, 129, 0.1)}.mat-mdc-icon-button.mat-warn{--mdc-icon-button-icon-color:#f44336;--mat-mdc-button-persistent-ripple-color: #f44336;--mat-mdc-button-ripple-color: rgba(244, 67, 54, 0.1)}.mat-mdc-icon-button[disabled][disabled]{--mdc-icon-button-icon-color:rgba(0, 0, 0, 0.38);--mdc-icon-button-disabled-icon-color:rgba(0, 0, 0, 0.38)}.mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size:48px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:12px}.mat-mdc-fab,.mat-mdc-mini-fab{--mat-mdc-button-persistent-ripple-color: #000;--mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1)}.mat-mdc-fab:hover .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab:hover .mat-mdc-button-persistent-ripple::before{opacity:.04}.mat-mdc-fab.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-fab:active .mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab:active .mat-mdc-button-persistent-ripple::before{opacity:.12}.mat-mdc-fab.mat-primary,.mat-mdc-mini-fab.mat-primary{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab.mat-accent,.mat-mdc-mini-fab.mat-accent{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab.mat-warn,.mat-mdc-mini-fab.mat-warn{--mat-mdc-button-persistent-ripple-color: #fff;--mat-mdc-button-ripple-color: rgba(255, 255, 255, 0.1)}.mat-mdc-fab[disabled][disabled],.mat-mdc-mini-fab[disabled][disabled]{--mdc-fab-container-color:rgba(0, 0, 0, 0.12);--mdc-fab-icon-color:rgba(0, 0, 0, 0.38);--mat-mdc-fab-color: rgba(0, 0, 0, 0.38)}.mat-mdc-fab.mat-unthemed,.mat-mdc-mini-fab.mat-unthemed{--mdc-fab-container-color:white;--mdc-fab-icon-color:black;--mat-mdc-fab-color: #000}.mat-mdc-fab.mat-primary,.mat-mdc-mini-fab.mat-primary{--mdc-fab-container-color:#3f51b5;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mat-mdc-fab.mat-accent,.mat-mdc-mini-fab.mat-accent{--mdc-fab-container-color:#ff4081;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mat-mdc-fab.mat-warn,.mat-mdc-mini-fab.mat-warn{--mdc-fab-container-color:#f44336;--mdc-fab-icon-color:white;--mat-mdc-fab-color: #fff}.mdc-fab--extended{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:var(--mdc-typography-button-font-size, 14px);line-height:var(--mdc-typography-button-line-height, 36px);font-weight:var(--mdc-typography-button-font-weight, 500);letter-spacing:var(--mdc-typography-button-letter-spacing, 0.0892857143em);-webkit-text-decoration:var(--mdc-typography-button-text-decoration, none);text-decoration:var(--mdc-typography-button-text-decoration, none);text-transform:var(--mdc-typography-button-text-transform, none)}.mat-mdc-extended-fab{--mdc-extended-fab-label-text-font:Roboto, sans-serif;--mdc-extended-fab-label-text-size:14px;--mdc-extended-fab-label-text-tracking:0.0892857143em;--mdc-extended-fab-label-text-weight:500}.mat-mdc-snack-bar-container{--mdc-snackbar-container-color:#333333;--mdc-snackbar-supporting-text-color:rgba(255, 255, 255, 0.87);--mat-snack-bar-button-color:#ff4081}.mat-mdc-snack-bar-container{--mdc-snackbar-supporting-text-font:Roboto, sans-serif;--mdc-snackbar-supporting-text-line-height:20px;--mdc-snackbar-supporting-text-size:14px;--mdc-snackbar-supporting-text-weight:400}html{--mat-table-background-color:white;--mat-table-header-headline-color:rgba(0, 0, 0, 0.87);--mat-table-row-item-label-text-color:rgba(0, 0, 0, 0.87);--mat-table-row-item-outline-color:rgba(0, 0, 0, 0.12)}html{--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px}html{--mat-table-header-headline-font:Roboto, sans-serif;--mat-table-header-headline-line-height:22px;--mat-table-header-headline-size:14px;--mat-table-header-headline-weight:500;--mat-table-header-headline-tracking:0.0071428571em;--mat-table-row-item-label-text-font:Roboto, sans-serif;--mat-table-row-item-label-text-line-height:20px;--mat-table-row-item-label-text-size:14px;--mat-table-row-item-label-text-weight:400;--mat-table-row-item-label-text-tracking:0.0178571429em;--mat-table-footer-supporting-text-font:Roboto, sans-serif;--mat-table-footer-supporting-text-line-height:20px;--mat-table-footer-supporting-text-size:14px;--mat-table-footer-supporting-text-weight:400;--mat-table-footer-supporting-text-tracking:0.0178571429em}.mat-mdc-progress-spinner{--mdc-circular-progress-active-indicator-color:#3f51b5}.mat-mdc-progress-spinner.mat-accent{--mdc-circular-progress-active-indicator-color:#ff4081}.mat-mdc-progress-spinner.mat-warn{--mdc-circular-progress-active-indicator-color:#f44336}.mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;border-radius:50%;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:Roboto, sans-serif;font-family:var(--mat-badge-text-font, Roboto, sans-serif);font-size:12px;font-size:var(--mat-badge-text-size, 12px);font-weight:600;font-weight:var(--mat-badge-text-weight, 600)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:16px;height:16px;line-height:16px;font-size:9px;font-size:var(--mat-badge-small-size-text-size, 9px)}.mat-badge-small.mat-badge-above .mat-badge-content{top:-8px}.mat-badge-small.mat-badge-below .mat-badge-content{bottom:-8px}.mat-badge-small.mat-badge-before .mat-badge-content{left:-16px}[dir=rtl] .mat-badge-small.mat-badge-before .mat-badge-content{left:auto;right:-16px}.mat-badge-small.mat-badge-after .mat-badge-content{right:-16px}[dir=rtl] .mat-badge-small.mat-badge-after .mat-badge-content{right:auto;left:-16px}.mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-8px}.mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-8px}[dir=rtl] .mat-badge-small.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-8px}.mat-badge-medium .mat-badge-content{width:22px;height:22px;line-height:22px}.mat-badge-medium.mat-badge-above .mat-badge-content{top:-11px}.mat-badge-medium.mat-badge-below .mat-badge-content{bottom:-11px}.mat-badge-medium.mat-badge-before .mat-badge-content{left:-22px}[dir=rtl] .mat-badge-medium.mat-badge-before .mat-badge-content{left:auto;right:-22px}.mat-badge-medium.mat-badge-after .mat-badge-content{right:-22px}[dir=rtl] .mat-badge-medium.mat-badge-after .mat-badge-content{right:auto;left:-22px}.mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-11px}.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-11px}[dir=rtl] .mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-11px}.mat-badge-large .mat-badge-content{width:28px;height:28px;line-height:28px;font-size:24px;font-size:var(--mat-badge-large-size-text-size, 24px)}.mat-badge-large.mat-badge-above .mat-badge-content{top:-14px}.mat-badge-large.mat-badge-below .mat-badge-content{bottom:-14px}.mat-badge-large.mat-badge-before .mat-badge-content{left:-28px}[dir=rtl] .mat-badge-large.mat-badge-before .mat-badge-content{left:auto;right:-28px}.mat-badge-large.mat-badge-after .mat-badge-content{right:-28px}[dir=rtl] .mat-badge-large.mat-badge-after .mat-badge-content{right:auto;left:-28px}.mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-before .mat-badge-content{left:auto;right:-14px}.mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:-14px}[dir=rtl] .mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content{right:auto;left:-14px}html{--mat-badge-background-color:#3f51b5;--mat-badge-text-color:white;--mat-badge-disabled-state-background-color:#b9b9b9;--mat-badge-disabled-state-text-color:rgba(0, 0, 0, 0.38)}.mat-badge-accent{--mat-badge-background-color:#ff4081;--mat-badge-text-color:white}.mat-badge-warn{--mat-badge-background-color:#f44336;--mat-badge-text-color:white}html{--mat-badge-text-font:Roboto, sans-serif;--mat-badge-text-size:12px;--mat-badge-text-weight:600;--mat-badge-small-size-text-size:9px;--mat-badge-large-size-text-size:24px}html{--mat-bottom-sheet-container-text-color:rgba(0, 0, 0, 0.87);--mat-bottom-sheet-container-background-color:white}html{--mat-bottom-sheet-container-text-font:Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height:20px;--mat-bottom-sheet-container-text-size:14px;--mat-bottom-sheet-container-text-tracking:0.0178571429em;--mat-bottom-sheet-container-text-weight:400}html{--mat-legacy-button-toggle-text-color:rgba(0, 0, 0, 0.38);--mat-legacy-button-toggle-state-layer-color:rgba(0, 0, 0, 0.12);--mat-legacy-button-toggle-selected-state-text-color:rgba(0, 0, 0, 0.54);--mat-legacy-button-toggle-selected-state-background-color:#e0e0e0;--mat-legacy-button-toggle-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-legacy-button-toggle-disabled-state-background-color:#eeeeee;--mat-legacy-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-standard-button-toggle-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-background-color:white;--mat-standard-button-toggle-state-layer-color:black;--mat-standard-button-toggle-selected-state-background-color:#e0e0e0;--mat-standard-button-toggle-selected-state-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-standard-button-toggle-disabled-state-background-color:white;--mat-standard-button-toggle-disabled-selected-state-text-color:rgba(0, 0, 0, 0.87);--mat-standard-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-standard-button-toggle-divider-color:#e0e0e0}html{--mat-standard-button-toggle-height:48px}html{--mat-legacy-button-toggle-text-font:Roboto, sans-serif;--mat-standard-button-toggle-text-font:Roboto, sans-serif}html{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#3f51b5;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(63, 81, 181, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(63, 81, 181, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(63, 81, 181, 0.3);--mat-datepicker-toggle-active-state-icon-color:#3f51b5;--mat-datepicker-calendar-date-in-range-state-background-color:rgba(63, 81, 181, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e;--mat-datepicker-toggle-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-body-label-text-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-period-button-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-navigation-button-icon-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-header-divider-color:rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-text-color:rgba(0, 0, 0, 0.54);--mat-datepicker-calendar-date-today-outline-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-date-today-disabled-state-outline-color:rgba(0, 0, 0, 0.18);--mat-datepicker-calendar-date-text-color:rgba(0, 0, 0, 0.87);--mat-datepicker-calendar-date-outline-color:transparent;--mat-datepicker-calendar-date-disabled-state-text-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-date-preview-state-outline-color:rgba(0, 0, 0, 0.24);--mat-datepicker-range-input-separator-color:rgba(0, 0, 0, 0.87);--mat-datepicker-range-input-disabled-state-separator-color:rgba(0, 0, 0, 0.38);--mat-datepicker-range-input-disabled-state-text-color:rgba(0, 0, 0, 0.38);--mat-datepicker-calendar-container-background-color:white;--mat-datepicker-calendar-container-text-color:rgba(0, 0, 0, 0.87)}.mat-datepicker-content.mat-accent{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#ff4081;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(255, 64, 129, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(255, 64, 129, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(255, 64, 129, 0.3);--mat-datepicker-calendar-date-in-range-state-background-color:rgba(255, 64, 129, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e}.mat-datepicker-content.mat-warn{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#f44336;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(244, 67, 54, 0.4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(244, 67, 54, 0.3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(244, 67, 54, 0.3);--mat-datepicker-calendar-date-in-range-state-background-color:rgba(244, 67, 54, 0.2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, 0.2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#46a35e}.mat-datepicker-toggle-active.mat-accent{--mat-datepicker-toggle-active-state-icon-color:#ff4081}.mat-datepicker-toggle-active.mat-warn{--mat-datepicker-toggle-active-state-icon-color:#f44336}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size:40px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:8px}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base .mat-mdc-button-touch-target{display:none}html{--mat-datepicker-calendar-text-font:Roboto, sans-serif;--mat-datepicker-calendar-text-size:13px;--mat-datepicker-calendar-body-label-text-size:14px;--mat-datepicker-calendar-body-label-text-weight:500;--mat-datepicker-calendar-period-button-text-size:14px;--mat-datepicker-calendar-period-button-text-weight:500;--mat-datepicker-calendar-header-text-size:11px;--mat-datepicker-calendar-header-text-weight:400}html{--mat-divider-color:rgba(0, 0, 0, 0.12)}html{--mat-expansion-container-background-color:white;--mat-expansion-container-text-color:rgba(0, 0, 0, 0.87);--mat-expansion-actions-divider-color:rgba(0, 0, 0, 0.12);--mat-expansion-header-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-expansion-header-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-expansion-header-disabled-state-text-color:rgba(0, 0, 0, 0.26);--mat-expansion-header-text-color:rgba(0, 0, 0, 0.87);--mat-expansion-header-description-color:rgba(0, 0, 0, 0.54);--mat-expansion-header-indicator-color:rgba(0, 0, 0, 0.54)}html{--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px}html{--mat-expansion-header-text-font:Roboto, sans-serif;--mat-expansion-header-text-size:14px;--mat-expansion-header-text-weight:500;--mat-expansion-header-text-line-height:inherit;--mat-expansion-header-text-tracking:inherit;--mat-expansion-container-text-font:Roboto, sans-serif;--mat-expansion-container-text-line-height:20px;--mat-expansion-container-text-size:14px;--mat-expansion-container-text-tracking:0.0178571429em;--mat-expansion-container-text-weight:400}html{--mat-grid-list-tile-header-primary-text-size:14px;--mat-grid-list-tile-header-secondary-text-size:12px;--mat-grid-list-tile-footer-primary-text-size:14px;--mat-grid-list-tile-footer-secondary-text-size:12px}html{--mat-icon-color:inherit}.mat-icon.mat-primary{--mat-icon-color:#3f51b5}.mat-icon.mat-accent{--mat-icon-color:#ff4081}.mat-icon.mat-warn{--mat-icon-color:#f44336}html{--mat-sidenav-container-divider-color:rgba(0, 0, 0, 0.12);--mat-sidenav-container-background-color:white;--mat-sidenav-container-text-color:rgba(0, 0, 0, 0.87);--mat-sidenav-content-background-color:#fafafa;--mat-sidenav-content-text-color:rgba(0, 0, 0, 0.87);--mat-sidenav-scrim-color:rgba(0, 0, 0, 0.6)}html{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#3f51b5;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#3f51b5;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#3f51b5;--mat-stepper-header-edit-state-icon-foreground-color:white;--mat-stepper-container-color:white;--mat-stepper-line-color:rgba(0, 0, 0, 0.12);--mat-stepper-header-hover-state-layer-color:rgba(0, 0, 0, 0.04);--mat-stepper-header-focus-state-layer-color:rgba(0, 0, 0, 0.04);--mat-stepper-header-label-text-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-optional-label-text-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-selected-state-label-text-color:rgba(0, 0, 0, 0.87);--mat-stepper-header-error-state-label-text-color:#f44336;--mat-stepper-header-icon-background-color:rgba(0, 0, 0, 0.54);--mat-stepper-header-error-state-icon-foreground-color:#f44336;--mat-stepper-header-error-state-icon-background-color:transparent}html .mat-step-header.mat-accent{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#ff4081;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#ff4081;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#ff4081;--mat-stepper-header-edit-state-icon-foreground-color:white}html .mat-step-header.mat-warn{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#f44336;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#f44336;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#f44336;--mat-stepper-header-edit-state-icon-foreground-color:white}html{--mat-stepper-header-height:72px}html{--mat-stepper-container-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-size:14px;--mat-stepper-header-label-text-weight:400;--mat-stepper-header-error-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-weight:400}.mat-sort-header-arrow{color:#757575}html{--mat-toolbar-container-background-color:whitesmoke;--mat-toolbar-container-text-color:rgba(0, 0, 0, 0.87)}.mat-toolbar.mat-primary{--mat-toolbar-container-background-color:#3f51b5;--mat-toolbar-container-text-color:white}.mat-toolbar.mat-accent{--mat-toolbar-container-background-color:#ff4081;--mat-toolbar-container-text-color:white}.mat-toolbar.mat-warn{--mat-toolbar-container-background-color:#f44336;--mat-toolbar-container-text-color:white}html{--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px}html{--mat-toolbar-title-text-font:Roboto, sans-serif;--mat-toolbar-title-text-line-height:32px;--mat-toolbar-title-text-size:20px;--mat-toolbar-title-text-tracking:0.0125em;--mat-toolbar-title-text-weight:500}.mat-tree{background:#fff}.mat-tree-node,.mat-nested-tree-node{color:rgba(0,0,0,.87)}.mat-tree-node{min-height:48px}.mat-tree{font-family:Roboto, sans-serif}.mat-tree-node,.mat-nested-tree-node{font-weight:400;font-size:14px}.mat-h1,.mat-headline-5,.mat-typography .mat-h1,.mat-typography .mat-headline-5,.mat-typography h1{font-size:24px;font-weight:400;line-height:32px;font-family:Roboto, sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2,.mat-headline-6,.mat-typography .mat-h2,.mat-typography .mat-headline-6,.mat-typography h2{font-size:20px;font-weight:500;line-height:32px;font-family:Roboto, sans-serif;letter-spacing:.0125em;margin:0 0 16px}.mat-h3,.mat-subtitle-1,.mat-typography .mat-h3,.mat-typography .mat-subtitle-1,.mat-typography h3{font-size:16px;font-weight:400;line-height:28px;font-family:Roboto, sans-serif;letter-spacing:.009375em;margin:0 0 16px}.mat-h4,.mat-body-1,.mat-typography .mat-h4,.mat-typography .mat-body-1,.mat-typography h4{font-size:16px;font-weight:400;line-height:24px;font-family:Roboto, sans-serif;letter-spacing:.03125em;margin:0 0 16px}.mat-h5,.mat-typography .mat-h5,.mat-typography h5{font:400 calc(14px * 0.83)/20px Roboto, sans-serif;margin:0 0 12px}.mat-h6,.mat-typography .mat-h6,.mat-typography h6{font:400 calc(14px * 0.67)/20px Roboto, sans-serif;margin:0 0 12px}.mat-body-strong,.mat-subtitle-2,.mat-typography .mat-body-strong,.mat-typography .mat-subtitle-2{font-size:14px;font-weight:500;line-height:22px;font-family:Roboto, sans-serif;letter-spacing:.0071428571em}.mat-body,.mat-body-2,.mat-typography .mat-body,.mat-typography .mat-body-2,.mat-typography{font-size:14px;font-weight:400;line-height:20px;font-family:Roboto, sans-serif;letter-spacing:.0178571429em}.mat-body p,.mat-body-2 p,.mat-typography .mat-body p,.mat-typography .mat-body-2 p,.mat-typography p{margin:0 0 12px}.mat-small,.mat-caption,.mat-typography .mat-small,.mat-typography .mat-caption{font-size:12px;font-weight:400;line-height:20px;font-family:Roboto, sans-serif;letter-spacing:.0333333333em}.mat-headline-1,.mat-typography .mat-headline-1{font-size:96px;font-weight:300;line-height:96px;font-family:Roboto, sans-serif;letter-spacing:-0.015625em;margin:0 0 56px}.mat-headline-2,.mat-typography .mat-headline-2{font-size:60px;font-weight:300;line-height:60px;font-family:Roboto, sans-serif;letter-spacing:-.0083333333em;margin:0 0 64px}.mat-headline-3,.mat-typography .mat-headline-3{font-size:48px;font-weight:400;line-height:50px;font-family:Roboto, sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-headline-4,.mat-typography .mat-headline-4{font-size:34px;font-weight:400;line-height:40px;font-family:Roboto, sans-serif;letter-spacing:.0073529412em;margin:0 0 64px}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[6].rules[0].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js??ruleSet[1].rules[6].rules[1].use[0]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[6].rules[1].use[1]!./src/styles.scss?ngGlobalStyle ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* You can add global styles to this file, and also import other style files */
/* Font optimization for better loading performance */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  font-display: swap; /* Ensures text remains visible during font load */
  src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format("woff2");
}
/* Ensure Material Icons load properly with fallback */
.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-display: swap;
}

/* Angular Material icon compatibility */
.mat-icon {
  font-display: swap;
}

body {
  padding: 0px;
  margin: 0px;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/*!
 * Bootstrap Grid v4.3.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  background-color: #f2f2f2;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.col-xl,
.col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg,
.col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md,
.col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm,
.col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col,
.col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  flex: 0 0 8.3333333333%;
  max-width: 8.3333333333%;
}

.col-2 {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.3333333333%;
  max-width: 33.3333333333%;
}

.col-5 {
  flex: 0 0 41.6666666667%;
  max-width: 41.6666666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.3333333333%;
  max-width: 58.3333333333%;
}

.col-8 {
  flex: 0 0 66.6666666667%;
  max-width: 66.6666666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.3333333333%;
  max-width: 83.3333333333%;
}

.col-11 {
  flex: 0 0 91.6666666667%;
  max-width: 91.6666666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.3333333333%;
}

.offset-2 {
  margin-left: 16.6666666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.3333333333%;
}

.offset-5 {
  margin-left: 41.6666666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.3333333333%;
}

.offset-8 {
  margin-left: 66.6666666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.3333333333%;
}

.offset-11 {
  margin-left: 91.6666666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-sm-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.3333333333%;
  }
  .offset-sm-2 {
    margin-left: 16.6666666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.3333333333%;
  }
  .offset-sm-5 {
    margin-left: 41.6666666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.3333333333%;
  }
  .offset-sm-8 {
    margin-left: 66.6666666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.3333333333%;
  }
  .offset-sm-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-md-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-md-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-md-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-md-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-md-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.3333333333%;
  }
  .offset-md-2 {
    margin-left: 16.6666666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.3333333333%;
  }
  .offset-md-5 {
    margin-left: 41.6666666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.3333333333%;
  }
  .offset-md-8 {
    margin-left: 66.6666666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.3333333333%;
  }
  .offset-md-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-lg-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.3333333333%;
  }
  .offset-lg-2 {
    margin-left: 16.6666666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.3333333333%;
  }
  .offset-lg-5 {
    margin-left: 41.6666666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.3333333333%;
  }
  .offset-lg-8 {
    margin-left: 66.6666666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.3333333333%;
  }
  .offset-lg-11 {
    margin-left: 91.6666666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xl-1 {
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.3333333333%;
  }
  .offset-xl-2 {
    margin-left: 16.6666666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.3333333333%;
  }
  .offset-xl-5 {
    margin-left: 41.6666666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.3333333333%;
  }
  .offset-xl-8 {
    margin-left: 66.6666666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.3333333333%;
  }
  .offset-xl-11 {
    margin-left: 91.6666666667%;
  }
}
.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}
.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}

.ml-n2,
.mx-n2 {
  margin-left: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}

.ml-n3,
.mx-n3 {
  margin-left: -1rem !important;
}

.m-n4 {
  margin: -1.5rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}

.ml-n4,
.mx-n4 {
  margin-left: -1.5rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}

.ml-n5,
.mx-n5 {
  margin-left: -3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important;
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important;
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important;
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important;
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important;
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important;
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important;
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-sm-n1,
  .mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-sm-n2,
  .mx-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .m-sm-n3 {
    margin: -1rem !important;
  }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -1rem !important;
  }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-sm-n3,
  .mx-sm-n3 {
    margin-left: -1rem !important;
  }
  .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-sm-n4,
  .mx-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .m-sm-n5 {
    margin: -3rem !important;
  }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -3rem !important;
  }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-sm-n5,
  .mx-sm-n5 {
    margin-left: -3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important;
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important;
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important;
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important;
  }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important;
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important;
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important;
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important;
  }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important;
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important;
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-2,
  .px-md-2 {
    padding-left: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important;
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important;
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important;
  }
  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important;
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important;
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important;
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important;
  }
  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-md-n1,
  .mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.5rem !important;
  }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-md-n2,
  .mx-md-n2 {
    margin-left: -0.5rem !important;
  }
  .m-md-n3 {
    margin: -1rem !important;
  }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -1rem !important;
  }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -1rem !important;
  }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-md-n3,
  .mx-md-n3 {
    margin-left: -1rem !important;
  }
  .m-md-n4 {
    margin: -1.5rem !important;
  }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-md-n4,
  .mx-md-n4 {
    margin-left: -1.5rem !important;
  }
  .m-md-n5 {
    margin: -3rem !important;
  }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -3rem !important;
  }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -3rem !important;
  }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-md-n5,
  .mx-md-n5 {
    margin-left: -3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important;
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important;
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important;
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important;
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important;
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important;
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important;
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-lg-n1,
  .mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-lg-n2,
  .mx-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .m-lg-n3 {
    margin: -1rem !important;
  }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -1rem !important;
  }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-lg-n3,
  .mx-lg-n3 {
    margin-left: -1rem !important;
  }
  .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-lg-n4,
  .mx-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .m-lg-n5 {
    margin: -3rem !important;
  }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -3rem !important;
  }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-lg-n5,
  .mx-lg-n5 {
    margin-left: -3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }
}
@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important;
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important;
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important;
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important;
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important;
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important;
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important;
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xl-n1,
  .mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xl-n2,
  .mx-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .m-xl-n3 {
    margin: -1rem !important;
  }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -1rem !important;
  }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-xl-n3,
  .mx-xl-n3 {
    margin-left: -1rem !important;
  }
  .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-xl-n4,
  .mx-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .m-xl-n5 {
    margin: -3rem !important;
  }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -3rem !important;
  }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-xl-n5,
  .mx-xl-n5 {
    margin-left: -3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }
}
.mobile {
  display: none;
}
@media (max-width: 767px) {
  .mobile {
    display: block;
  }
}

.desktop {
  display: none;
}
@media (min-width: 767px) {
  .desktop {
    display: block;
  }
}

@font-face {
  font-family: "bariol_regular";
  font-display: swap;
  src: url('bariol_regular-webfont.woff') format("woff");
}
@font-face {
  font-family: "bariol_bold";
  font-display: swap;
  src: url('bariol_bold-webfont.woff') format("woff");
}
@font-face {
  font-family: "bariol_light";
  font-display: swap;
  src: url('bariol_light-webfont.woff') format("woff");
}
@font-face {
  font-family: "bariol_thin";
  font-display: swap;
  src: url('bariol_thin-webfont.woff') format("woff");
}
.mobile {
  display: none;
}
@media (max-width: 767px) {
  .mobile {
    display: block;
  }
}

.desktop {
  display: none;
}
@media (min-width: 767px) {
  .desktop {
    display: block;
  }
}

.loading {
  position: fixed;
  z-index: 999;
  height: 64px;
  width: 64px;
  overflow: visible;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* Transparent Overlay */
.loading:before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(243, 243, 243, 0.7);
}

.splashSpinner {
  width: 100px;
  margin: auto;
  margin-top: 300px;
}

/*!
 * Load Awesome v1.1.0 (http://github.danielcardoso.net/load-awesome/)
 * Copyright 2015 Daniel Cardoso <@DanielCardoso>
 * Licensed under MIT
 */
.la-ball-clip-rotate,
.la-ball-clip-rotate > div {
  position: relative;
  box-sizing: border-box;
}

.la-ball-clip-rotate {
  display: block;
  font-size: 0;
  color: #3f51b5;
}

.la-ball-clip-rotate.la-dark {
  color: #333;
}

.la-ball-clip-rotate > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.la-ball-clip-rotate {
  width: 32px;
  height: 32px;
}

.la-ball-clip-rotate > div {
  width: 32px;
  height: 32px;
  background: transparent;
  border-width: 2px;
  border-bottom-color: transparent;
  border-radius: 100%;
  animation: ball-clip-rotate 0.75s linear infinite;
}

.la-ball-clip-rotate.la-sm {
  width: 16px;
  height: 16px;
}

.la-ball-clip-rotate.la-sm > div {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

.la-ball-clip-rotate.la-2x {
  width: 64px;
  height: 64px;
}

.la-ball-clip-rotate.la-2x > div {
  width: 64px;
  height: 64px;
  border-width: 4px;
}

.la-ball-clip-rotate.la-3x {
  width: 96px;
  height: 96px;
}

.la-ball-clip-rotate.la-3x > div {
  width: 96px;
  height: 96px;
  border-width: 6px;
}

/*
  * Animation
  */
@keyframes ball-clip-rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
input[type=color],
input[type=date],
input[type=datetime],
input[type=datetime-local],
input[type=email],
input[type=month],
input[type=number],
input[type=password],
input[type=search],
input[type=tel],
input[type=text],
input[type=time],
input[type=url],
input[type=week],
select,
option,
textarea {
  font-size: 16px;
}

select option {
  font-size: 16px;
}

.cashlessForm > * {
  width: 100%;
}

.cashlessLink a {
  text-decoration: none;
  cursor: pointer;
}

/* default font */
* {
  font-family: "bariol_regular", Arial, Helvetica, sans-serif;
}

.noPadding {
  padding: 0;
}

.mat-drawer-content {
  background-color: #f2f2f2;
}

.cardDefaultParent {
  background-color: white;
  padding: 0px;
  margin-bottom: 20px;
  border: 1px solid #dddddd;
  border-radius: 0;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  height: auto;
}
@media (min-width: 767px) {
  .cardDefaultParent {
    border-radius: 12px;
  }
}

.cardDefaultCanteen {
  background-color: white;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #dddddd;
  border-radius: 0;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  height: auto;
}
@media (min-width: 767px) {
  .cardDefaultCanteen {
    border-radius: 12px;
  }
}

.PrimaryButton {
  background: linear-gradient(96.62deg, #ff9e00 0%, #ff4b17 100%);
  border-radius: 8px;
  border-style: none;
  color: white;
  cursor: pointer;
  font-family: "bariol_bold";
  font-size: 22px;
  margin-top: auto;
  margin-bottom: auto;
  height: 56px;
  width: 100%;
  outline: none;
}
.PrimaryButton.smaller {
  height: 48px;
  border-radius: 10px;
}
.PrimaryButton.action {
  width: 60px;
  height: 35px;
  font-size: 16px;
  padding: 10px;
}

.PrimaryButton:disabled {
  color: rgba(43, 45, 37, 0.5);
  font-family: "bariol_regular";
}

.SecondaryButton {
  width: 100%;
  background-color: #e0e1e2;
  color: black;
  margin-top: 10px;
  border-radius: 8px;
  cursor: pointer;
}

.WarnLink {
  width: 100%;
  font-weight: bold;
  font-size: 18px;
}

.titleBlock {
  color: #333b44;
  font-family: "bariol_bold";
  font-size: 18px;
  margin-bottom: 6px;
  margin-top: 32px;
  padding-top: 0px;
}
@media (min-width: 767px) {
  .titleBlock {
    margin-top: 16px;
  }
}

.scrolling-horizontal-wrapper {
  overflow: hidden;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}
.scrolling-horizontal-wrapper::-webkit-scrollbar {
  height: 4px;
}
.scrolling-horizontal-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
}
@media (min-width: 767px) {
  .scrolling-horizontal-wrapper::-webkit-scrollbar-thumb {
    background: #e0e1e2;
  }
}
.scrolling-horizontal-wrapper::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
  background: #bdbdbd;
}

.spinnerBlock {
  padding-top: 10px;
  padding-bottom: 10px;
}

.modalClose {
  height: 56px;
  width: 56px;
  background-color: white;
  border: 1px solid #e0e1e2;
  border-radius: 50%;
  padding: 7px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
}
.modalClose.noBackground {
  box-shadow: none;
  border: none;
}
.modalClose mat-icon {
  font-size: 40px;
  color: #ff7a00;
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.spinnerDialog {
  width: 100px;
  height: 300px;
  margin: auto;
  padding-top: 100px;
}

.paddingLine {
  padding: 5px;
}

.headerContainer {
  height: 60px;
}

.titleDialog {
  text-align: center;
  font-size: 24px;
  margin-bottom: 100px;
}

.feeInformationLink {
  margin-bottom: 0;
  margin-top: 30px;
  color: #333b44;
  font-size: 14px;
}
.feeInformationLink a {
  cursor: pointer;
  color: #ff7a00;
  text-decoration: underline;
}

.section {
  background-color: white;
  border-radius: 12px;
  margin-left: 12px;
  margin-right: 12px;
  margin-top: 12px;
  margin-bottom: 12px;
  padding-top: 12px;
}
.section.noTopMargin {
  margin-top: 0;
}
.section.noSideMargin {
  margin-left: 0;
  margin-right: 0;
}

/*# sourceMappingURL=styles.css.map*/