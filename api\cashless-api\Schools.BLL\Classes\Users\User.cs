using System;
using System.Collections.Generic;
using System.Text;
using Schools.DAL.DtosToMoveToBLL;
using Newtonsoft.Json;

namespace Schools.BLL.Classes;

/// <summary>
/// Used to create new Users in Firebase
/// </summary>
public class CreateUserRequest : User
{
    [JsonProperty(PropertyName = "Password")]
    public string Password { get; set; }
}

public class UserDto
{
    [JsonProperty(PropertyName = "UserId")]
    public int UserId { get; set; }

    [JsonProperty(PropertyName = "FirstName")]
    public string FirstName { get; set; }

    [JsonProperty(PropertyName = "Lastname")]
    public string Lastname { get; set; }

    [JsonProperty(PropertyName = "Email")]
    public string Email { get; set; }

    [JsonProperty(PropertyName = "Mobile")]
    public string Mobile { get; set; }

    [JsonProperty(PropertyName = "SpriggyBalance")]
    public decimal SpriggyBalance { get; set; }

    [JsonProperty(PropertyName = "ExternalUserId")]
    public string ExternalUserId { get; set; }

    [JsonProperty(PropertyName = "FirebaseUserId")]
    public string FirebaseUserId { get; set; }

    [JsonProperty(PropertyName = "Role")]
    public string Role { get; set; }

    [JsonProperty(PropertyName = "DateCreated")]
    public string DateCreated { get; set; }

    // not used anymore => keep it to avoid App crash
    [JsonProperty(PropertyName = "IntercomUserHMAC")]
    public string IntercomUserHMAC { get; set; }

    [JsonProperty(PropertyName = "IntercomHMAC")]
    public IntercomHMAC IntercomHMAC { get; set; }
}