using Azure.Identity;
using System;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Formatting.Json;

namespace Cashless.APIs;

public class Program
{
    /// <summary>
    /// Build and run the API server
    /// </summary>
    public static void Main(string[] args)
    {
        CreateWebHostBuilder(args).Build().Run();
    }

    /// <summary>
    /// Build an API server and push the configuration of services
    /// into the Startup class
    /// </summary>
    public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
        WebHost.CreateDefaultBuilder(args)
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                //logging.AddSerilog(dispose: true);
                //logging.AddSerilog(dispose: true);
            })
            .UseStartup<Startup>();
}
