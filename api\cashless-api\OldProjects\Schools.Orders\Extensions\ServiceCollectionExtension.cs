using Schools.BLL.Services;
using Microsoft.Extensions.DependencyInjection;
using Schools.BLL.Services.Interfaces;

namespace Schools.Orders.Extensions;

/// <summary>
/// Register various services in the DI container
/// </summary>
public static class ServiceCollectionExtension
{
    public static IServiceCollection AddExternalServices(this IServiceCollection services)
    {
        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService();
        services.AddSingleton<IConfigService, ConfigService>();
        services.AddScoped<ITelemetryService, TelemetryService>();
        services.AddScoped<IHealthCheckService, HealthCheckService>();
        services.AddScoped<IAppVersionService, AppVersionService>();
        services.AddHttpClient();

        return services;
    }

    public static IServiceCollection AddServiceLayerServices(this IServiceCollection services)
    {
        services.AddTransient<IStocksService, StocksService>();
        services.AddTransient<IOrderService, OrderService>();
        services.AddTransient<IOrderStatusService, OrderStatusService>();

        return services;
    }
}