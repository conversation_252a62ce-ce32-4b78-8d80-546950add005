﻿using System.Collections.Generic;
using Schools.BLL.Classes.Users;
using Newtonsoft.Json;

namespace Schools.BLL.Classes.Screens;

public class GetUserWithStudentsDto
{
    [JsonProperty(PropertyName = "User")]
    public UserDto User { get; set; }

    [JsonProperty(PropertyName = "Students")]
    public List<StudentDto> Students { get; set; }

    [JsonProperty(PropertyName = "SupportedVersions")]
    public SupportedVersionsResponse SupportedVersions { get; set; }
}