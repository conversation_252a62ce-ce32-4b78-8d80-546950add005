{"version": 3, "file": "common.js", "mappings": ";;;;;;;;;;;;;;;;;AAAuD;AACf;AACA;;AAMlC,MAAOG,iBAAiB;EAC5BC,YAAA;IACA,KAAAC,aAAa,GAAW,MAAM,CAAC,CAAC;IAChC,KAAAC,gBAAgB,GAAG,wBAAwB;IAC3C,KAAAC,kBAAkB,GAAG,0BAA0B;IAG/C,KAAAC,YAAY,GAAW,CAAC;EANT;EAUfC,2BAA2BA,CAACC,IAAW;IACrC,IAAI,CAACC,QAAQ,GAAGD,IAAI,KAAKR,gDAAK,CAACU,MAAM,GAAG,IAAI,CAACN,gBAAgB,GAAG,IAAI,CAACC,kBAAkB;IACvF,IAAI,IAAI,CAACM,iBAAiB,EAAE;MAC1B;;IAEF,IAAI,CAACA,iBAAiB,GAAGb,2CAAK,CAAC,CAAC,EAAE,IAAI,CAACK,aAAa,CAAC;IACrD,IAAI,CAACS,sBAAsB,GAAG,IAAI,CAACD,iBAAiB,CAACE,SAAS,CAACC,GAAG,IAAG;MACnE,IAAI,IAAI,CAACR,YAAY,GAAG,EAAE,EAAE;QAC1B;;MAEFP,0DAAoB,CAAC,IAAI,CAACU,QAAQ,CAAC;MACnC,IAAI,CAACH,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAU,yBAAyBA,CAAA;IACvB,IAAI,CAACL,iBAAiB,GAAG,IAAI;IAE7B,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACK,WAAW,EAAE;;EAE7C;;;uBAhCWhB,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAiB,OAAA,EAAjBjB,iBAAiB,CAAAkB,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;ACNyB;AAKtC,MAAME,QAAQ,GAAIC,KAAe,IAAKA,KAAK;AAE3C,MAAMC,WAAW,GAAID,KAAe,IAAKA,KAAK,CAACE,MAAM;AAErD,MAAMC,SAAS,GAAIH,KAAe,IAAKA,KAAK,CAACE,MAAM,EAAEC,SAAS,IAAI,IAAI;AAEtE,MAAMC,YAAY,GAAGN,2DAAc,CAACG,WAAW,EAAGD,KAAkB,IAAKA,KAAK,CAACI,YAAY,CAAC;AAE5F,MAAMC,gBAAgB,GAAGP,2DAAc,CAC5CG,WAAW,EACVD,KAAkB,IAAKA,KAAK,CAACG,SAAS,EAAEG,QAAQ,CAClD;AAEM,MAAMC,cAAc,GAAGT,2DAAc,CAACG,WAAW,EAAGD,KAAkB,IAAKA,KAAK,CAACG,SAAS,EAAEK,QAAQ,CAAC;AAErG,MAAMC,gBAAgB,GAAGX,2DAAc,CAACG,WAAW,EAAGD,KAAkB,IAAKA,KAAK,CAACG,SAAS,EAAEO,IAAI,CAAC;;;;;;;;;;;;;;;;;ACpBlE;AAEjC,MAAME,4BAA4B,GAAGA,CAACC,IAAY,EAAEC,SAAiB,KAAI;EAC9E,MAAMC,UAAU,GAAWJ,6CAAM,CAACE,IAAI,EAAE,OAAO,CAAC;EAChD,MAAMG,UAAU,GAAWC,yBAAyB,CAACF,UAAU,EAAED,SAAS,CAAC;EAE3E;EACA,OAAOH,6CAAM,CAACK,UAAU,CAAC,CAACE,KAAK,EAAE,CAACC,MAAM,CAAC,SAAS,CAAC;AACrD,CAAC;AAEM,MAAMC,oCAAoC,GAAGA,CAACC,QAAuB,EAAEP,SAAiB,KAAI;EACjG,MAAMQ,cAAc,GAAWX,6CAAM,CAACU,QAAQ,CAAC;EAC/C,MAAME,cAAc,GAAWN,yBAAyB,CAACK,cAAc,EAAER,SAAS,CAAC;EAEnF;EACA,OAAOH,6CAAM,CAACY,cAAc,CAAC,CAACL,KAAK,EAAE,CAACC,MAAM,CAAC,kBAAkB,CAAC;AAClE,CAAC;AAED,MAAMF,yBAAyB,GAAGA,CAACN,MAAc,EAAEG,SAAiB,KAAY;EAC9E;EACA,OAAOH,MAAM,CAACa,SAAS,CAACV,SAAS,GAAG,EAAE,EAAE,IAAI,CAAC;AAC/C,CAAC;;;;;;;;;;;;;;;;;;;;;ACrB6C;AACJ;AACW;AACC;AACmC;AAIpD;AACkC;AAChE,SAAStB,cAAcA,CAAC6C,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAI,CAACb,mEAAC,CAACc,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACtB,IAAI,IAAI,IAAIF,CAAC,IAAIA,CAAC,CAACG,MAAM,IAAI,CAAC,EAC5B,OACEb,uEAAC,CAACc,CAAC,CAACC,KAAK,CACN,uDAAsDL,CAAE,oBAC3D,CAAC,EACD,CAAC,CAAC;EAEN,IAAI,CAACH,+EAAE,CAACG,CAAC,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3D,MAAM,CAACM,CAAC,EAAEC,CAAC,CAAC,GAAGZ,mFAAE,CACfM,CAAC,EACDF,yEAAE,EACF,iBAAiB,EAChB,qBAAoBC,CAAE,GAAE,EACzB,OACF,CAAC;EACD,IAAI,CAACM,CAAC,EAAE,OAAO,CAAC,CAAC;EACjB,MAAME,CAAC,GAAGpB,mEAAC,CAACqB,EAAE,CAAC,CAAC;EAChB,IAAID,CAAC,IAAIA,CAAC,CAACE,EAAE,CAACV,CAAC,CAAC,EACd,OAAOV,uEAAC,CAACc,CAAC,CAACO,IAAI,CAAE,iBAAgBX,CAAE,6BAA4B,CAAC,EAAE,CAAC,CAAC;EACtE,MAAMY,CAAC,GAAGvB,+DAAC,CAACwB,CAAC,CAACvB,uEAAC,CAACwB,CAAC,CAACC,WAAW,EAAE;IAAET,CAAC,EAAEN,CAAC;IAAEgB,CAAC,EAAET;EAAE,CAAC,CAAC;EAC9C,IAAIK,CAAC,CAACK,CAAC,EAAE;IACP3B,uEAAC,CAACc,CAAC,CAACO,IAAI,CAAE,wBAAuBX,CAAE,IAAG,CAAC;IACvC,KAAK,MAAMZ,CAAC,IAAIwB,CAAC,CAACM,EAAE,EAAEzB,2FAAE,CAAC0B,EAAE,CAAC,CAAC,CAACC,EAAE,CAAC7B,0EAAE,CAAC8B,EAAE,EAAE,CAACrB,CAAC,EAAEC,CAAC,CAAC,EAAEb,CAAC,CAAC;EACpD;EACA,OAAOwB,CAAC,CAACK,CAAC;AACZ", "sources": ["./src/app/sharedServices/brazeTimer.service.ts", "./src/app/states/family/family.selectors.ts", "./src/app/utility/timezone-helper.ts", "./node_modules/@braze/web-sdk/src/Core/log-custom-event.js"], "sourcesContent": ["import { Observable, Subscription, timer } from 'rxjs';\nimport * as braze from '@braze/web-sdk';\nimport { Roles } from '../sharedModels';\nimport { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class BrazeTimerService {\n  constructor() {}\n  INTERVAL_TIME: number = 300000; // 5 minutes\n  PARENT_EVENT_KEY = 'WebPortalParentRefresh';\n  MERCHANT_EVENT_KEY = 'WebPortalMerchantRefresh';\n  brazeTimerSubscription: Subscription;\n  timerSubscription: Observable<number>;\n  refreshCount: number = 0;\n\n  brazeKey: string;\n\n  setUpBrazeInAppMessageTimer(role: Roles): void {\n    this.brazeKey = role === Roles.Parent ? this.PARENT_EVENT_KEY : this.MERCHANT_EVENT_KEY;\n    if (this.timerSubscription) {\n      return;\n    }\n    this.timerSubscription = timer(0, this.INTERVAL_TIME);\n    this.brazeTimerSubscription = this.timerSubscription.subscribe(res => {\n      if (this.refreshCount > 20) {\n        return;\n      }\n      braze.logCustomEvent(this.brazeKey);\n      this.refreshCount++;\n    });\n  }\n\n  unsubscribeFromBrazeTimer(): void {\n    this.timerSubscription = null;\n\n    if (this.brazeTimerSubscription) {\n      this.brazeTimerSubscription.unsubscribe();\n    }\n  }\n}\n", "import { createSelector } from '@ngrx/store';\n\n// states\nimport { AppState, FamilyState } from '..';\n\nexport const appState = (state: AppState) => state;\n\nexport const familyState = (state: AppState) => state.family;\n\nexport const dayDetail = (state: AppState) => state.family?.dayDetail || null;\n\nexport const selectedWeek = createSelector(familyState, (state: FamilyState) => state.selectedWeek);\n\nexport const MenuPickerSelect = createSelector(\n  familyState,\n  (state: FamilyState) => state.dayDetail?.MenuType\n);\n\nexport const MenuNameSelect = createSelector(familyState, (state: FamilyState) => state.dayDetail?.MenuName);\n\nexport const datePickerSelect = createSelector(familyState, (state: FamilyState) => state.dayDetail?.Date);\n", "import moment, { Moment } from 'moment';\n\nexport const convertSchoolTimeToLocalTime = (time: string, utcOffSet: number) => {\n  const momentTime: Moment = moment(time, 'HH:mm');\n  const offSetTime: Moment = addUtcOffSetToMomentValue(momentTime, utcOffSet);\n\n  //convert iso format date back to local time on users device\n  return moment(offSetTime).local().format('HH:mm A');\n};\n\nexport const convertSchoolDateTimeToLocalDateTime = (dateTime: string | Date, utcOffSet: number) => {\n  const momentDateTime: Moment = moment(dateTime);\n  const offSetDateTime: Moment = addUtcOffSetToMomentValue(momentDateTime, utcOffSet);\n\n  //convert iso format date back to local time on users device in NSW\n  return moment(offSetDateTime).local().format('YYYY-MM-DD HH:mm');\n};\n\nconst addUtcOffSetToMomentValue = (moment: Moment, utcOffSet: number): Moment => {\n  // set the time utc offset as the schools local utc offset (transforms date to iso format)\n  return moment.utcOffset(utcOffSet * 60, true);\n};\n", "import e from \"../managers/braze-instance.js\";\nimport s from \"../common/event-logger.js\";\nimport r from \"../../shared-lib/braze-shared-lib.js\";\nimport tt from \"../triggers/models/trigger-events.js\";\nimport { TriggersProviderFactory as et } from \"../triggers/triggers-provider-factory.js\";\nimport {\n  validateCustomProperties as rt,\n  validateCustomString as ot,\n} from \"../util/validation-utils.js\";\nimport { LOG_CUSTOM_EVENT_STRING as nt } from \"../common/constants.js\";\nexport function logCustomEvent(t, o) {\n  if (!e.rr()) return !1;\n  if (null == t || t.length <= 0)\n    return (\n      r.j.error(\n        `logCustomEvent requires a non-empty eventName, got \"${t}\". Ignoring event.`,\n      ),\n      !1\n    );\n  if (!ot(t, \"log custom event\", \"the event name\")) return !1;\n  const [n, i] = rt(\n    o,\n    nt,\n    \"eventProperties\",\n    `log custom event \"${t}\"`,\n    \"event\",\n  );\n  if (!n) return !1;\n  const m = e.tr();\n  if (m && m.ge(t))\n    return r.j.info(`Custom Event \"${t}\" is blocklisted, ignoring.`), !1;\n  const g = s.N(r.q.CustomEvent, { n: t, p: i });\n  if (g.O) {\n    r.j.info(`Logged custom event \"${t}\".`);\n    for (const e of g.ve) et.er().be(tt.$e, [t, o], e);\n  }\n  return g.O;\n}\n"], "names": ["timer", "braze", "Roles", "BrazeTimerService", "constructor", "INTERVAL_TIME", "PARENT_EVENT_KEY", "MERCHANT_EVENT_KEY", "refreshCount", "setUpBrazeInAppMessageTimer", "role", "braze<PERSON>ey", "Parent", "timerSubscription", "brazeTimerSubscription", "subscribe", "res", "logCustomEvent", "unsubscribeFromBrazeTimer", "unsubscribe", "factory", "ɵfac", "providedIn", "createSelector", "appState", "state", "familyState", "family", "dayDetail", "selectedWeek", "MenuPickerSelect", "MenuType", "MenuNameSelect", "MenuName", "datePickerSelect", "Date", "moment", "convertSchoolTimeToLocalTime", "time", "utcOffSet", "momentTime", "offSetTime", "addUtcOffSetToMomentValue", "local", "format", "convertSchoolDateTimeToLocalDateTime", "dateTime", "momentDateTime", "offSetDateTime", "utcOffset", "e", "s", "r", "tt", "TriggersProviderFactory", "et", "validateCustomProperties", "rt", "validateCustomString", "ot", "LOG_CUSTOM_EVENT_STRING", "nt", "t", "o", "rr", "length", "j", "error", "n", "i", "m", "tr", "ge", "info", "g", "N", "q", "CustomEvent", "p", "O", "ve", "er", "be", "$e"], "sourceRoot": "webpack:///", "x_google_ignoreList": [3]}