using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using Schools.Orders.Extensions;

namespace Schools.Orders.Common;

/// <summary>
/// Message context coming from Azure Event Grid
/// </summary>
public class MessageEnvelope
{
    public string messageId { get; set; }
    public string requestId { get; set; }
    public string correlationId { get; set; }
    public string conversationId { get; set; }
    public string sentTime { get; set; }
    public IEnumerable<string> messageType { get; set; }

    /// <summary>
    /// JSON payload will need decoding
    /// </summary>
    public JsonNode message { get; set; }

    #region Helpers

    /// <summary>
    /// For debugging purposes
    /// </summary>
    public override string ToString()
    {
        var sb = new StringBuilder();

        sb.Append("<ServiceBusMessageContext id: \"" + messageId + "\"")
            .AddField("data", value: message?.ToString()?.Substring(0, 10) + "..." ?? null)
            .AddField("requestId", requestId)
            .AddField("correlationId", correlationId)
            .AddField("conversationId", conversationId)
            .AddField("messageType", messageType?.FirstOrDefault())
            .Append("/>");
        return sb.ToString();
    }
    #endregion
}