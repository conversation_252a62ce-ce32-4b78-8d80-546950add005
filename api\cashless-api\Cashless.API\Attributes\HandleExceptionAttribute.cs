using System;
using System.Threading.Tasks;
using Schools.BLL.Classes;
using Schools.BLL.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Schools.BLL.Services.Interfaces;

namespace Cashless.APIs.Attributes;

/// <summary>
/// Custom middleware to handle exceptions. This is implmented as an exception 
/// filter attribute. It needs to be added to the startup class as per:
/// https://docs.microsoft.com/en-us/aspnet/core/web-api/handle-errors?view=aspnetcore-6.0#use-exceptions-to-modify-the-response
/// 
/// Return a response similar to the response returned by the .NET MVC framework. 
/// For example, when validation errors are encountered, the following JSON is 
/// returned:
/// {
///     "errors": {
///         "StartDate": [
///             "The StartDate field is required."
///         ]
///     },
///     "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
///     "title": "One or more validation errors occurred.",
///     "status": 400
/// }
/// </summary>
public class HandleExceptionAttribute : ExceptionFilterAttribute
{
    private readonly ILogger<HandleExceptionAttribute> _logger;
    private readonly IExceptionHandlerService _exceptionHandlerService;

    public HandleExceptionAttribute(IExceptionHandlerService exceptionHandlerService, ILogger<HandleExceptionAttribute> logger)
    {
        _exceptionHandlerService = exceptionHandlerService;
        _logger = logger;
    }

    /// <summary>
    /// Handle an exception in the request chain, and return a HTTP 500 response
    /// </summary>
    public override async Task OnExceptionAsync(ExceptionContext context)
    {
        base.OnException(context);
        await _exceptionHandlerService.OnException(context.HttpContext, context.Exception);
        context.Result = GetObjectResult(context.Exception);
    }

    /// <summary>
    /// Convert the exception into a response object depending
    /// on the type of exception
    /// </summary>
    private ObjectResult GetObjectResult(Exception exception)
    {
        ErrorResponse response = new(exception);

        return new ObjectResult(response)
        {
            StatusCode = response.Status
        };
    }
}